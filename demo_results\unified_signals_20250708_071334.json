{
  "EURUSD": {
    "symbol": "EURUSD",
    "timeframe": "H1",
    "timestamp": "2025-07-08T07:13:33.179288",
    "final_action": "hold",
    "final_confidence": 0.5,
    "rl_component": {
      "action": "hold",
      "confidence": 0.5,
      "model_used": "none"
    },
    "plutus_component": {
      "trend": "neutral",
      "confidence": 0.5,
      "prediction": {
        "symbol": "EURUSD",
        "timeframe": "H1",
        "timestamp": "2025-07-08T07:13:33.670997",
        "individual_signals": {
          "chronos": {
            "trend": "bullish",
            "confidence": 0.9,
            "strength": 0.03423440212917474
          },
          "fingpt": {
            "trend": "neutral",
            "confidence": 0.5,
            "reasoning": "Based on technical analysis of EURUSD: MA trend is bullish, RSI is 67.2 (neutral), Volume is below average. Price is at 100.0% of recent range.",
            "technical_indicators": {
              "rsi": 67.15116279069817,
              "ma_short": 1.135312,
              "ma_long": 1.132808,
              "volume_ratio": 0.5573363977258982,
              "price_position": 1.0
            }
          }
        },
        "combined_signal": {
          "trend": "bullish",
          "confidence": 0.888,
          "agreement": true,
          "individual_trends": [
            "bullish"
          ],
          "adaptive_weights": {
            "chronos_weight": 0.6,
            "fingpt_weight": 0.4,
            "combined_threshold": 0.65,
            "confidence_multiplier": 1.0,
            "last_updated": 