#!/usr/bin/env python3
"""
🧪 Simple AI Model Test
تست ساده مدل‌های هوش مصنوعی
"""

import os
import sys
from pathlib import Path

# Set proxy if available
if os.path.exists("PROXY.json"):
    os.environ["HTTP_PROXY"] = "http://127.0.0.1:10809"
    os.environ["HTTPS_PROXY"] = "http://127.0.0.1:10809"

def test_model_download(model_name, model_path):
    """تست دانلود یک مدل"""
    print(f"\n🔽 Testing: {model_name}")
    print(f"   Path: {model_path}")
    
    try:
        from transformers import AutoTokenizer, AutoModel
        
        print("   📥 Downloading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        print("   📥 Downloading model...")
        model = AutoModel.from_pretrained(model_path)
        
        print("   🧪 Testing basic functionality...")
        test_text = "Bitcoin price is rising"
        inputs = tokenizer(test_text, return_tensors="pt")
        outputs = model(**inputs)
        
        print(f"   ✅ Success! Output shape: {outputs.last_hidden_state.shape}")
        return True
        
    except Exception as e:
        print(f"   ❌ Failed: {e}")
        return False

def main():
    """تابع اصلی"""
    print("🧪 Simple AI Model Test")
    print("=" * 40)
    
    # Test a few key models
    models_to_test = [
        ("DistilBERT", "distilbert-base-uncased"),
        ("FinBERT", "ProsusAI/finbert"),
        ("BERT Tiny", "prajjwal1/bert-tiny"),
    ]
    
    successful = 0
    total = len(models_to_test)
    
    for model_name, model_path in models_to_test:
        if test_model_download(model_name, model_path):
            successful += 1
    
    print(f"\n📊 Results: {successful}/{total} models downloaded successfully")
    
    if successful > 0:
        print("✅ Some models are working! Your setup is ready.")
    else:
        print("❌ No models could be downloaded. Check your internet connection.")

if __name__ == "__main__":
    main() 