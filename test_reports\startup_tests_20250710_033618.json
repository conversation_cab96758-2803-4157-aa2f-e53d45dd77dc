{"type": "startup_tests", "timestamp": "2025-07-10T03:36:18.034531", "summary": {"total_tests": 4, "passed": 0, "failed": 4, "success_rate": 0.0, "total_duration": 172.1560652256012}, "results": [{"name": "test_advanced_risk_manager.py", "passed": false, "duration": 61.94366240501404, "output": "", "error": "list index out of range", "timestamp": "2025-07-10T03:34:27.817120"}, {"name": "test_smart_portfolio_manager.py", "passed": false, "duration": 38.678778409957886, "output": "", "error": "list index out of range", "timestamp": "2025-07-10T03:35:06.496907"}, {"name": "test_integrated_system.py", "passed": false, "duration": 36.13128113746643, "output": "", "error": "list index out of range", "timestamp": "2025-07-10T03:35:42.629189"}, {"name": "test_integration_*.py", "passed": false, "duration": 35.40234327316284, "output": "", "error": "list index out of range", "timestamp": "2025-07-10T03:36:18.033531"}]}