#!/usr/bin/env python3
"""
🧹 Clear Python cache and test the fixed system
"""

import sys
import os
import importlib
import pandas as pd
import numpy as np

# Clear Python cache
def clear_python_cache():
    """Clear Python import cache"""
    print("🧹 Clearing Python cache...")
    
    # Remove from sys.modules if exists
    modules_to_remove = []
    for module_name in sys.modules:
        if 'fixed_ultimate_main' in module_name:
            modules_to_remove.append(module_name)
    
    for module_name in modules_to_remove:
        print(f"   🗑️ Removing {module_name} from cache")
        del sys.modules[module_name]
    
    # Clear __pycache__ directories
    import shutil
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                cache_path = os.path.join(root, dir_name)
                try:
                    shutil.rmtree(cache_path)
                    print(f"   🗑️ Removed cache directory: {cache_path}")
                except:
                    pass
    
    print("✅ Cache cleared!")

def test_fresh_import():
    """Test with fresh import"""
    print("\n🔄 Testing with fresh import...")
    
    # Add current directory to path
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # Fresh import
        import fixed_ultimate_main
        importlib.reload(fixed_ultimate_main)
        
        from fixed_ultimate_main import MultiBrainSystem
        print("✅ Fresh import successful")
        
        # Create test data
        data = pd.DataFrame({
            'close': np.random.uniform(1.1000, 1.1100, 50),
            'volume': np.random.randint(1000, 10000, 50),
            'rsi': np.random.uniform(20, 80, 50),
            'macd': np.random.uniform(-0.01, 0.01, 50),
            'sma_20': np.random.uniform(1.1000, 1.1100, 50),
            'ema_12': np.random.uniform(1.1000, 1.1100, 50),
            'bb_upper': np.random.uniform(1.1080, 1.1120, 50),
            'bb_lower': np.random.uniform(1.0980, 1.1020, 50),
            'stoch_k': np.random.uniform(20, 80, 50),
            'stoch_d': np.random.uniform(20, 80, 50)
        })
        
        print(f"📊 Test data: {len(data)} rows")
        
        # Initialize system
        multi_brain = MultiBrainSystem()
        print("✅ MultiBrainSystem initialized")
        
        # Test analysis
        print("\n🔍 Testing analysis with fresh cache...")
        analysis = multi_brain.analyze_training_situation(data, "LSTM", "EURUSD")
        
        print(f"✅ Analysis completed: {type(analysis)}")
        print(f"   Keys: {list(analysis.keys())}")
        
        # Check for the problematic key
        if 'hyperparameter_suggestions' in analysis:
            hps = analysis['hyperparameter_suggestions']
            print(f"   ✅ hyperparameter_suggestions: {type(hps)}")
            if isinstance(hps, dict):
                print(f"      Content: {list(hps.keys())}")
                return True
            else:
                print(f"      ⚠️ Wrong type: {type(hps)}")
                return False
        else:
            print(f"   ❌ hyperparameter_suggestions: MISSING")
            return False
            
    except Exception as e:
        print(f"❌ Fresh import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_function():
    """Test the specific function that was causing issues"""
    print("\n🎯 Testing specific function...")
    
    try:
        from fixed_ultimate_main import MultiBrainSystem
        
        # Create minimal test
        multi_brain = MultiBrainSystem()
        
        # Create mock brain results like what would happen
        mock_brain_results = {
            'optuna': {'learning_rate': 0.001, 'batch_size': 32},
            'h2o': {'data_quality': {'completeness': 0.95}},
            'autogluon': {'model_type': 'LSTM'},
            'ray': {'best_config': {'learning_rate': 0.001}},
            'hyperparameter_suggestions': {'learning_rate': 0.001, 'batch_size': 32},
            'model_recommendations': {},
            'distributed_config': {},
            'data_insights': {}
        }
        
        # Test the fallback function directly
        result = multi_brain._fallback_supervised_analysis(mock_brain_results, "LSTM", "EURUSD")
        
        print(f"✅ Fallback analysis: {type(result)}")
        print(f"   Keys: {list(result.keys())}")
        
        if 'hyperparameter_suggestions' in result:
            print(f"   ✅ hyperparameter_suggestions: Present")
            return True
        else:
            print(f"   ❌ hyperparameter_suggestions: Missing")
            return False
            
    except Exception as e:
        print(f"❌ Direct function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧹 CACHE CLEAR AND TEST")
    print("=" * 60)
    
    # Step 1: Clear cache
    clear_python_cache()
    
    # Step 2: Test with fresh import
    test1 = test_fresh_import()
    
    # Step 3: Test specific function
    test2 = test_direct_function()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 CACHE TEST SUMMARY")
    print("=" * 60)
    print(f"🔄 Fresh Import Test: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"🎯 Direct Function Test: {'✅ PASSED' if test2 else '❌ FAILED'}")
    
    overall = test1 and test2
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if overall else '❌ SOME TESTS FAILED'}")
    
    if overall:
        print("\n🎉 Cache cleared and fix is working!")
        print("💡 The hyperparameter_suggestions error should now be resolved.")
    else:
        print("\n⚠️ Cache issue or other problems remain.")
        print("💡 Try restarting Python completely or check the specific error above.")
