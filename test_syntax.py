#!/usr/bin/env python3
"""Test syntax of fixed_ultimate_main.py"""

try:
    print("🔍 Testing syntax...")
    with open('/content/fixed_ultimate_main.py', 'r') as f:
        code = f.read()
    
    # Compile to check syntax
    compile(code, '/content/fixed_ultimate_main.py', 'exec')
    print("✅ Syntax check passed!")
    
except SyntaxError as e:
    print(f"❌ Syntax Error: {e}")
    print(f"   Line {e.lineno}: {e.text}")
    print(f"   Error: {e.msg}")
    
except Exception as e:
    print(f"❌ Other Error: {e}")
