# 🤖 AI Model Usage Examples
# مثال‌های استفاده از مدل‌های هوش مصنوعی

# Distilbert Example

# DistilBERT Usage Example
from transformers import AutoTokenizer, AutoModel
import torch

tokenizer = AutoTokenizer.from_pretrained('distilbert-base-uncased')
model = AutoModel.from_pretrained('distilbert-base-uncased')

text = "Bitcoin price analysis shows bullish trend"
inputs = tokenizer(text, return_tensors='pt')
outputs = model(**inputs)
embeddings = outputs.last_hidden_state.mean(dim=1)
print(f"Embedding shape: {embeddings.shape}")


# Finbert Example

# FinBERT Financial Sentiment Analysis
from transformers import pipeline

classifier = pipeline("sentiment-analysis", model="ProsusAI/finbert")

texts = [
    "The company's quarterly earnings exceeded expectations",
    "Market volatility increases due to economic uncertainty",
    "Stable performance with consistent growth"
]

for text in texts:
    result = classifier(text)[0]
    print(f"Text: {text}")
    print(f"Sentiment: {result['label']} (confidence: {result['score']:.2f})")
    print("-" * 50)


# Bert Tiny Example

# BERT Tiny for Fast Processing
from transformers import AutoTokenizer, AutoModel
import torch

tokenizer = AutoTokenizer.from_pretrained('prajjwal1/bert-tiny')
model = AutoModel.from_pretrained('prajjwal1/bert-tiny')

# Fast text processing
texts = ["Quick analysis", "Fast inference", "Lightweight model"]

for text in texts:
    inputs = tokenizer(text, return_tensors='pt')
    with torch.no_grad():
        outputs = model(**inputs)
    print(f"'{text}' -> {outputs.last_hidden_state.shape}")


