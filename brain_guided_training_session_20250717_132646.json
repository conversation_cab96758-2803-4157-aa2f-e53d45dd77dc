{"session_info": {"start_time": "2025-07-17T13:25:17.391608", "end_time": "2025-07-17T13:26:46.173262", "duration_seconds": 88.778245, "config": {"use_memory_optimization": true, "use_enhanced_replay": true, "use_genetic_optimization": true, "use_continual_learning": true, "use_advanced_backtesting": true, "memory_optimization_level": "aggressive", "replay_buffer_size": 10000, "prioritized_replay": true, "genetic_population_size": 20, "genetic_generations": 10, "ewc_lambda": 0.4, "use_experience_replay": true, "auto_backtest_after_training": true, "backtest_validation_split": 0.2}}, "training_results": {"completed": [{"success": true, "performance": 0.87, "metrics": {"accuracy": 0.87, "f1_score": 0.85, "precision": 0.88, "recall": 0.83}, "epochs_completed": 10, "best_epoch": 8, "advanced_metrics": {"memory_efficiency": 0.95, "training_stability": 0.92, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.8903913278251859, "sharpe_ratio": 1.7306835011813504, "max_drawdown": 0.08457046568554664, "win_rate": 0.7575004512761785}, "model_saved": true, "model_name": "FinBERT", "category": "sentiment", "training_duration": 11.980719, "start_time": "2025-07-17T13:26:13.702757", "end_time": "2025-07-17T13:26:25.683476", "preprocessing_results": {"memory_optimized": true, "continual_learning_ready": true}, "advanced_features_applied": {"memory_optimization": true, "genetic_optimization": false, "enhanced_replay": false, "continual_learning": true}}, {"success": true, "performance": 0.87, "metrics": {"accuracy": 0.87, "f1_score": 0.85, "precision": 0.88, "recall": 0.83}, "epochs_completed": 10, "best_epoch": 8, "advanced_metrics": {"memory_efficiency": 0.95, "training_stability": 0.92, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.8438184686187169, "sharpe_ratio": 1.854701495753048, "max_drawdown": 0.141775101270644, "win_rate": 0.6844915310867473}, "model_saved": true, "model_name": "CryptoBERT", "category": "sentiment", "training_duration": 11.447205, "start_time": "2025-07-17T13:26:30.710317", "end_time": "2025-07-17T13:26:42.157522", "preprocessing_results": {"memory_optimized": true, "continual_learning_ready": true}, "advanced_features_applied": {"memory_optimization": true, "genetic_optimization": false, "enhanced_replay": false, "continual_learning": true}}], "failed": [{"success": false, "error": "Prerequisites not met", "details": {"trainer_available": true, "data_available": true, "dependencies_installed": true, "memory_sufficient": false, "ready_to_train": false}, "model_name": "PPO_Agent"}, {"success": false, "error": "Prerequisites not met", "details": {"trainer_available": true, "data_available": true, "dependencies_installed": true, "memory_sufficient": false, "ready_to_train": false}, "model_name": "GRU_TimeSeries"}, {"success": false, "error": "Prerequisites not met", "details": {"trainer_available": true, "data_available": true, "dependencies_installed": true, "memory_sufficient": false, "ready_to_train": false}, "model_name": "LSTM_TimeSeries"}, {"success": false, "error": "Prerequisites not met", "details": {"trainer_available": true, "data_available": true, "dependencies_installed": true, "memory_sufficient": false, "ready_to_train": false}, "model_name": "DQN_Agent"}], "success_rate": 33.33333333333333}, "brain_analytics": {"decisions_made": 6, "performance_memory": {"PPO_Agent": [{"success": false, "performance": 0.0, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 13:25:19.875132"}], "GRU_TimeSeries": [{"success": false, "performance": 0.0, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 13:25:24.348675"}], "LSTM_TimeSeries": [{"success": false, "performance": 0.0, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 13:25:28.751719"}], "DQN_Agent": [{"success": false, "performance": 0.0, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 13:25:33.211515"}], "FinBERT": [{"success": true, "performance": 0.87, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 13:26:27.694431"}], "CryptoBERT": [{"success": true, "performance": 0.87, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 13:26:44.165448"}]}, "decision_history": [{"decision": {"action": "train", "model": "RealModelInfo(name='PPO_Agent', category='reinforcement_learning', priority=1, trainer_module='training.train_rl', trainer_class='PearlRLTrainer', config_class='RLTrainingConfig', data_requirements=['trading_environment', 'price_data'], estimated_time_hours=2.5, memory_gb=2.2)", "reasoning": "انتخاب PPO_Agent با امتیاز 1.025", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": false, "timestamp": "2025-07-17T13:25:19.875132"}, {"decision": {"action": "train", "model": "RealModelInfo(name='GRU_TimeSeries', category='timeseries', priority=1, trainer_module='training.train_timeseries', trainer_class='PearlTimeSeriesTrainer', config_class='TimeSeriesTrainingConfig', data_requirements=['price_data', 'technical_indicators'], estimated_time_hours=0.8, memory_gb=1.8)", "reasoning": "انتخاب GRU_TimeSeries با امتیاز 1.016", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": false, "timestamp": "2025-07-17T13:25:24.348675"}, {"decision": {"action": "train", "model": "RealModelInfo(name='LSTM_TimeSeries', category='timeseries', priority=1, trainer_module='training.train_timeseries', trainer_class='PearlTimeSeriesTrainer', config_class='TimeSeriesTrainingConfig', data_requirements=['price_data', 'technical_indicators'], estimated_time_hours=1.0, memory_gb=2.0)", "reasoning": "انتخاب LSTM_TimeSeries با امتیاز 0.994", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": false, "timestamp": "2025-07-17T13:25:28.751719"}, {"decision": {"action": "train", "model": "RealModelInfo(name='DQN_Agent', category='reinforcement_learning', priority=1, trainer_module='training.train_rl', trainer_class='PearlRLTrainer', config_class='RLTrainingConfig', data_requirements=['trading_environment', 'price_data'], estimated_time_hours=3.0, memory_gb=2.5)", "reasoning": "انتخاب DQN_Agent با امتیاز 0.988", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": false, "timestamp": "2025-07-17T13:25:33.211515"}, {"decision": {"action": "train", "model": "RealModelInfo(name='FinBERT', category='sentiment', priority=2, trainer_module='training.train_sentiment', trainer_class='PearlSentimentTrainer', config_class='SentimentTrainingConfig', data_requirements=['financial_news', 'sentiment_labels'], estimated_time_hours=2.0, memory_gb=3.0)", "reasoning": "انتخاب FinBERT با امتیاز 1.020", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": true, "timestamp": "2025-07-17T13:26:27.694431"}, {"decision": {"action": "train", "model": "RealModelInfo(name='CryptoBERT', category='sentiment', priority=2, trainer_module='training.train_sentiment', trainer_class='PearlSentimentTrainer', config_class='SentimentTrainingConfig', data_requirements=['crypto_news', 'sentiment_labels'], estimated_time_hours=1.5, memory_gb=2.8)", "reasoning": "انتخاب CryptoBERT با امتیاز 1.020", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": true, "timestamp": "2025-07-17T13:26:44.165448"}]}, "advanced_features_summary": {"memory_optimization_available": true, "enhanced_replay_available": true, "genetic_evolution_available": true, "continual_learning_available": true, "backtesting_available": true}}