{"model_name": "DQN_Agent", "category": "reinforcement_learning", "training_result": {"success": true, "performance": 0.8999999999999999, "metrics": {"avg_reward": 0.18, "success_rate": 0.72, "sharpe_ratio": 2.0, "max_drawdown": 0.07}, "episodes_completed": 1000, "best_episode": 850, "advanced_metrics": {"memory_efficiency": 0.95, "training_stability": 0.9700000000000001, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.8909087694452092, "sharpe_ratio": 1.5104775517234597, "max_drawdown": 0.082005497945376, "win_rate": 0.719677296656658}}, "saved_at": "2025-07-17T13:19:19.984752", "model_path": "models/trained_models/reinforcement_learning/DQN_Agent_20250717_131919", "advanced_features_used": {}}