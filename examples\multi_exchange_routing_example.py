"""مثال عملی برای Multi-exchange Auto-routing

این مثال نحوه استفاده از سیستم مسیریابی چندصرافی را نشان می‌دهد.
"""

import asyncio
import time
import numpy as np
import logging
from typing import List

from utils.multi_exchange_routing import (
    ExchangeInfo, ExchangeStatus, OrderType, OrderBookData,
    ArbitrageOpportunity, ExecutionOrder, MultiExchangeRouter
)

# تنظیم logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_exchanges() -> List[ExchangeInfo]:
    """ایجاد صرافی‌های نمونه"""
    exchanges = [
        ExchangeInfo(
            name="Binance",
            api_endpoint="https://api.binance.com",
            trading_fee=0.1,  # 0.1%
            withdrawal_fee=0.0005,
            min_order_size=0.001,
            max_order_size=1000,
            status=ExchangeStatus.ACTIVE,
            latency=50.0,  # 50ms
            reliability_score=0.95,
            supported_pairs={'BTCUSD', 'ETHUSD', 'ADAUSD'}
        ),
        ExchangeInfo(
            name="Coinbase",
            api_endpoint="https://api.coinbase.com",
            trading_fee=0.15,  # 0.15%
            withdrawal_fee=0.001,
            min_order_size=0.001,
            max_order_size=500,
            status=ExchangeStatus.ACTIVE,
            latency=80.0,  # 80ms
            reliability_score=0.98,
            supported_pairs={'BTCUSD', 'ETHUSD'}
        ),
        ExchangeInfo(
            name="Kraken",
            api_endpoint="https://api.kraken.com",
            trading_fee=0.12,  # 0.12%
            withdrawal_fee=0.0008,
            min_order_size=0.001,
            max_order_size=800,
            status=ExchangeStatus.ACTIVE,
            latency=100.0,  # 100ms
            reliability_score=0.92,
            supported_pairs={'BTCUSD', 'ETHUSD', 'XRPUSD'}
        ),
        ExchangeInfo(
            name="Bitfinex",
            api_endpoint="https://api.bitfinex.com",
            trading_fee=0.08,  # 0.08%
            withdrawal_fee=0.0004,
            min_order_size=0.001,
            max_order_size=1200,
            status=ExchangeStatus.ACTIVE,
            latency=120.0,  # 120ms
            reliability_score=0.88,
            supported_pairs={'BTCUSD', 'ETHUSD', 'LTCUSD'}
        )
    ]
    return exchanges


async def demonstrate_exchange_connections():
    """نمایش اتصال به صرافی‌ها"""
    print("=" * 60)
    print("نمایش اتصال به صرافی‌ها")
    print("=" * 60)
    
    exchanges = create_sample_exchanges()
    router = MultiExchangeRouter(exchanges)
    
    print("در حال اتصال به صرافی‌ها...")
    await router.initialize()
    
    # نمایش وضعیت اتصالات
    print("\nوضعیت اتصالات:")
    for name, connector in router.connectors.items():
        status = "متصل" if connector.connected else "قطع"
        print(f"  {name}: {status}")
        
    # دریافت نمونه order book
    print("\nنمونه Order Books:")
    for name, connector in router.connectors.items():
        if connector.connected:
            order_book = await connector.get_order_book('BTCUSD')
            if order_book:
                best_bid = order_book.bids[0][0] if order_book.bids else 0
                best_ask = order_book.asks[0][0] if order_book.asks else 0
                spread = best_ask - best_bid
                print(f"  {name}:")
                print(f"    بهترین خرید: ${best_bid:,.2f}")
                print(f"    بهترین فروش: ${best_ask:,.2f}")
                print(f"    اسپرد: ${spread:,.2f}")
                
    await router.shutdown()


async def demonstrate_arbitrage_detection():
    """نمایش تشخیص فرصت‌های آربیتراژ"""
    print("\n" + "=" * 60)
    print("نمایش تشخیص فرصت‌های آربیتراژ")
    print("=" * 60)
    
    exchanges = create_sample_exchanges()
    router = MultiExchangeRouter(exchanges)
    await router.initialize()
    
    # شبیه‌سازی اختلاف قیمت برای آربیتراژ
    print("شبیه‌سازی اختلاف قیمت بین صرافی‌ها...")
    
    # تنظیم قیمت‌های مختلف در صرافی‌ها
    base_prices = {
        "Binance": 50000,
        "Coinbase": 50150,  # قیمت بالاتر
        "Kraken": 49950,    # قیمت پایین‌تر
        "Bitfinex": 50080
    }
    
    # شبیه‌سازی order books با قیمت‌های مختلف
    for exchange_name, base_price in base_prices.items():
        connector = router.connectors[exchange_name]
        
        # ایجاد order book شبیه‌سازی شده
        bids = [(base_price - i * 0.5, np.random.exponential(5)) for i in range(1, 11)]
        asks = [(base_price + i * 0.5, np.random.exponential(5)) for i in range(1, 11)]
        
        order_book = OrderBookData(
            exchange=exchange_name,
            symbol='BTCUSD',
            bids=bids,
            asks=asks,
            timestamp=time.time()
        )
        
        connector.order_books['BTCUSD'] = order_book
    
    # اسکن فرصت‌های آربیتراژ
    print("\nاسکن فرصت‌های آربیتراژ...")
    opportunities = await router.scan_arbitrage_opportunities(['BTCUSD'])
    
    print(f"\nفرصت‌های آربیتراژ یافت شده: {len(opportunities)}")
    
    for i, opp in enumerate(opportunities[:3], 1):  # نمایش 3 فرصت اول
        print(f"\nفرصت {i}:")
        print(f"  خرید از: {opp.buy_exchange} @ ${opp.buy_price:,.2f}")
        print(f"  فروش در: {opp.sell_exchange} @ ${opp.sell_price:,.2f}")
        print(f"  حجم: {opp.volume:.4f} BTC")
        print(f"  سود: ${opp.profit:,.2f} ({opp.profit_percentage:.2f}%)")
        
        # شبیه‌سازی اجرای آربیتراژ
        print(f"  در حال اجرای آربیتراژ...")
        success = await router.execute_arbitrage(opp)
        if success:
            print(f"  ✓ آربیتراژ با موفقیت اجرا شد")
        else:
            print(f"  ✗ خطا در اجرای آربیتراژ")
    
    await router.shutdown()


async def demonstrate_order_execution():
    """نمایش اجرای سفارش بهینه"""
    print("\n" + "=" * 60)
    print("نمایش اجرای سفارش بهینه")
    print("=" * 60)
    
    exchanges = create_sample_exchanges()
    router = MultiExchangeRouter(exchanges)
    await router.initialize()
    
    # سناریو 1: سفارش کوچک
    print("\nسناریو 1: سفارش کوچک (1 BTC)")
    executed_orders = await router.execute_order(
        symbol="BTCUSD",
        side="buy",
        volume=1.0,
        order_type=OrderType.MARKET
    )
    
    print(f"تعداد سفارشات اجرا شده: {len(executed_orders)}")
    for order in executed_orders:
        print(f"  {order.exchange}: {order.volume:.4f} BTC @ ${order.execution_price:,.2f}")
        print(f"    کارمزد: ${order.fees:.4f}")
        print(f"    وضعیت: {order.status}")
    
    # سناریو 2: سفارش بزرگ (تقسیم بین صرافی‌ها)
    print("\nسناریو 2: سفارش بزرگ (10 BTC)")
    executed_orders = await router.execute_order(
        symbol="BTCUSD",
        side="sell",
        volume=10.0,
        order_type=OrderType.MARKET
    )
    
    print(f"تعداد سفارشات اجرا شده: {len(executed_orders)}")
    total_volume = 0
    total_fees = 0
    
    for order in executed_orders:
        print(f"  {order.exchange}: {order.volume:.4f} BTC @ ${order.execution_price:,.2f}")
        print(f"    کارمزد: ${order.fees:.4f}")
        total_volume += order.volume
        total_fees += order.fees
        
    print(f"\nخلاصه:")
    print(f"  کل حجم اجرا شده: {total_volume:.4f} BTC")
    print(f"  کل کارمزد: ${total_fees:.4f}")
    
    await router.shutdown()


async def demonstrate_failover_system():
    """نمایش سیستم failover"""
    print("\n" + "=" * 60)
    print("نمایش سیستم Failover")
    print("=" * 60)
    
    exchanges = create_sample_exchanges()
    router = MultiExchangeRouter(exchanges)
    await router.initialize()
    
    # شبیه‌سازی خرابی صرافی
    print("شبیه‌سازی خرابی صرافی Binance...")
    router.failover_system.exchange_health["Binance"] = {
        "status": ExchangeStatus.ERROR,
        "consecutive_failures": 5,
        "last_check": time.time()
    }
    
    # بررسی صرافی‌های سالم
    all_exchanges = list(router.exchanges.keys())
    healthy_exchanges = router.failover_system.get_healthy_exchanges(all_exchanges)
    
    print(f"صرافی‌های سالم: {healthy_exchanges}")
    
    # یافتن صرافی جایگزین
    failover_exchange = router.failover_system.get_failover_exchange("Binance")
    print(f"صرافی جایگزین برای Binance: {failover_exchange}")
    
    # اجرای سفارش با failover
    print("\nاجرای سفارش با استفاده از صرافی‌های سالم...")
    executed_orders = await router.execute_order(
        symbol="BTCUSD",
        side="buy",
        volume=2.0,
        order_type=OrderType.MARKET
    )
    
    print("سفارشات اجرا شده:")
    for order in executed_orders:
        print(f"  {order.exchange}: {order.volume:.4f} BTC")
        
    # بررسی که Binance استفاده نشده
    used_exchanges = [order.exchange for order in executed_orders]
    if "Binance" not in used_exchanges:
        print("✓ سیستم failover به درستی از صرافی خراب اجتناب کرد")
    
    await router.shutdown()


async def demonstrate_cost_optimization():
    """نمایش بهینه‌سازی هزینه"""
    print("\n" + "=" * 60)
    print("نمایش بهینه‌سازی هزینه اجرا")
    print("=" * 60)
    
    exchanges = create_sample_exchanges()
    router = MultiExchangeRouter(exchanges)
    await router.initialize()
    
    # تحلیل هزینه‌های مختلف صرافی‌ها
    print("تحلیل هزینه‌های صرافی‌ها:")
    
    order_volume = 5.0
    
    for exchange_name, exchange_info in router.exchanges.items():
        connector = router.connectors[exchange_name]
        order_book = await connector.get_order_book('BTCUSD')
        
        if order_book:
            # محاسبه لغزش قیمت
            slippage = router.cost_optimizer.estimate_slippage(
                order_book, order_volume, 'buy'
            )
            
            # محاسبه کل هزینه
            total_cost = router.cost_optimizer.calculate_total_cost(
                exchange_name,
                order_volume,
                exchange_info.trading_fee,
                slippage,
                exchange_info.latency * 0.001
            )
            
            print(f"\n{exchange_name}:")
            print(f"  کارمزد معاملات: {exchange_info.trading_fee:.2f}%")
            print(f"  لغزش قیمت: {slippage:.4f}%")
            print(f"  جریمه تأخیر: {exchange_info.latency * 0.001:.4f}%")
            print(f"  کل هزینه: {total_cost:.4f}%")
            print(f"  امتیاز اطمینان: {exchange_info.reliability_score:.2f}")
    
    # انتخاب صرافی بهینه
    print(f"\nانتخاب صرافی بهینه برای سفارش {order_volume} BTC:")
    
    # تهیه order books
    order_books = {}
    for exchange_name, connector in router.connectors.items():
        order_book = await connector.get_order_book('BTCUSD')
        if order_book:
            order_books[exchange_name] = order_book
    
    optimal_exchange = router.cost_optimizer.select_optimal_exchange(
        list(router.exchanges.keys()),
        router.exchanges,
        order_books,
        order_volume,
        'buy'
    )
    
    print(f"صرافی بهینه: {optimal_exchange}")
    
    await router.shutdown()


async def demonstrate_aggregated_orderbook():
    """نمایش order book تجمیعی"""
    print("\n" + "=" * 60)
    print("نمایش Order Book تجمیعی")
    print("=" * 60)
    
    exchanges = create_sample_exchanges()
    router = MultiExchangeRouter(exchanges)
    await router.initialize()
    
    # دریافت order books از همه صرافی‌ها
    print("جمع‌آوری order books از صرافی‌ها...")
    
    for exchange_name, connector in router.connectors.items():
        order_book = await connector.get_order_book('BTCUSD')
        if order_book:
            connector.order_books['BTCUSD'] = order_book
            print(f"  {exchange_name}: ✓")
    
    # تولید order book تجمیعی
    aggregated_book = router.get_aggregated_order_book('BTCUSD')
    
    if aggregated_book:
        print(f"\nOrder Book تجمیعی برای BTCUSD:")
        print(f"تعداد سطوح bid: {len(aggregated_book.bids)}")
        print(f"تعداد سطوح ask: {len(aggregated_book.asks)}")
        
        print(f"\n5 سطح اول Bids:")
        for i, (price, volume) in enumerate(aggregated_book.bids[:5], 1):
            print(f"  {i}. ${price:,.2f} - {volume:.4f} BTC")
            
        print(f"\n5 سطح اول Asks:")
        for i, (price, volume) in enumerate(aggregated_book.asks[:5], 1):
            print(f"  {i}. ${price:,.2f} - {volume:.4f} BTC")
            
        # محاسبه عمق کل بازار
        total_bid_volume = sum(volume for _, volume in aggregated_book.bids)
        total_ask_volume = sum(volume for _, volume in aggregated_book.asks)
        
        print(f"\nعمق کل بازار:")
        print(f"  کل حجم bid: {total_bid_volume:.2f} BTC")
        print(f"  کل حجم ask: {total_ask_volume:.2f} BTC")
        print(f"  کل نقدینگی: {total_bid_volume + total_ask_volume:.2f} BTC")
    
    await router.shutdown()


async def main():
    """تابع اصلی برای اجرای تمام مثال‌ها"""
    print("مثال جامع Multi-exchange Auto-routing")
    print("=" * 80)
    
    try:
        # نمایش اتصال به صرافی‌ها
        await demonstrate_exchange_connections()
        
        # نمایش تشخیص آربیتراژ
        await demonstrate_arbitrage_detection()
        
        # نمایش اجرای سفارش
        await demonstrate_order_execution()
        
        # نمایش سیستم failover
        await demonstrate_failover_system()
        
        # نمایش بهینه‌سازی هزینه
        await demonstrate_cost_optimization()
        
        # نمایش order book تجمیعی
        await demonstrate_aggregated_orderbook()
        
        print("\n" + "=" * 80)
        print("همه مثال‌ها با موفقیت اجرا شدند!")
        print("=" * 80)
        
    except Exception as e:
        logger.error(f"خطا در اجرای مثال: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main()) 