"""Test cases for Advanced Sentiment Analysis System"""

import pytest
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import pandas as pd

from utils.sentiment_analyzer import (
    AdvancedSentimentAnalyzer,
    SentimentAnalyzer,
    SentimentType,
    AspectCategory,
    SentimentResult,
    MarketSentiment,
    LanguageModel,
    AspectBasedSentimentAnalyzer,
    TemporalSentimentAnalyzer,
    SocialMediaSentimentAnalyzer,
    MarketSentimentAggregator,
    SentimentCache,
    SentimentImpactPredictor,
    EnsembleSentimentAnalyzer
)


class TestSentimentTypes:
    """Test sentiment type enums"""
    
    def test_sentiment_types(self):
        assert SentimentType.VERY_POSITIVE.value == "very_positive"
        assert SentimentType.POSITIVE.value == "positive"
        assert SentimentType.NEUTRAL.value == "neutral"
        assert SentimentType.NEGATIVE.value == "negative"
        assert SentimentType.VERY_NEGATIVE.value == "very_negative"
        assert SentimentType.MIXED.value == "mixed"
    
    def test_aspect_categories(self):
        assert AspectCategory.PRICE.value == "price"
        assert AspectCategory.EARNINGS.value == "earnings"
        assert AspectCategory.MANAGEMENT.value == "management"
        assert AspectCategory.PRODUCT.value == "product"


class TestSentimentResult:
    """Test sentiment result dataclass"""
    
    def test_sentiment_result_creation(self):
        result = SentimentResult(
            text="Test text",
            sentiment=SentimentType.POSITIVE,
            score=0.8,
            confidence=0.9,
            language="en",
            source="twitter"
        )
        
        assert result.text == "Test text"
        assert result.sentiment == SentimentType.POSITIVE
        assert result.score == 0.8
        assert result.confidence == 0.9
        assert result.language == "en"
        assert result.source == "twitter"
        assert isinstance(result.timestamp, datetime)
        assert isinstance(result.aspects, dict)
        assert isinstance(result.entities, list)


class TestLanguageModel:
    """Test language model wrapper"""
    
    @patch('utils.sentiment_analyzer.AutoTokenizer')
    @patch('utils.sentiment_analyzer.AutoModelForSequenceClassification')
    @patch('utils.sentiment_analyzer.pipeline')
    def test_language_model_init(self, mock_pipeline, mock_model, mock_tokenizer):
        # Setup mocks
        mock_tokenizer.from_pretrained.return_value = MagicMock()
        mock_model.from_pretrained.return_value = MagicMock()
        mock_pipeline.return_value = MagicMock()
        
        # Create model
        model = LanguageModel("en", "test-model", "cpu")
        
        assert model.language == "en"
        assert model.model_name == "test-model"
        assert model.device == "cpu"
        mock_tokenizer.from_pretrained.assert_called_once_with("test-model")
        mock_model.from_pretrained.assert_called_once_with("test-model")
    
    @patch('utils.sentiment_analyzer.pipeline')
    def test_analyze(self, mock_pipeline):
        # Setup mock
        mock_result = [{'label': 'POSITIVE', 'score': 0.95}]
        mock_pipeline.return_value = mock_result
        
        model = LanguageModel("en", "test-model")
        
        # Test analysis
        result = model.analyze("Test text")
        
        assert result['label'] == 'positive'
        assert result['score'] == 0.95


class TestAspectBasedSentimentAnalyzer:
    """Test aspect-based sentiment analysis"""
    
    def test_extract_aspects(self):
        analyzer = AspectBasedSentimentAnalyzer()
        
        text = "The price is too high but the product quality is excellent."
        aspects = analyzer.extract_aspects(text)
        
        assert AspectCategory.PRICE in aspects
        assert AspectCategory.PRODUCT in aspects
        assert len(aspects[AspectCategory.PRICE]) > 0
        assert len(aspects[AspectCategory.PRODUCT]) > 0
    
    def test_extract_aspects_persian(self):
        analyzer = AspectBasedSentimentAnalyzer()
        
        text = "قیمت بسیار بالا است اما مدیریت عالی است"
        aspects = analyzer.extract_aspects(text)
        
        assert AspectCategory.PRICE in aspects
        assert AspectCategory.MANAGEMENT in aspects


class TestTemporalSentimentAnalyzer:
    """Test temporal sentiment analysis"""
    
    def test_add_sentiment(self):
        analyzer = TemporalSentimentAnalyzer(window_size=10)
        
        # Add sentiments
        for i in range(5):
            analyzer.add_sentiment(i * 0.2, datetime.now())
        
        assert len(analyzer.sentiment_history) == 5
        assert len(analyzer.timestamp_history) == 5
    
    def test_get_trend(self):
        analyzer = TemporalSentimentAnalyzer()
        
        # Add declining sentiments
        for i in range(20):
            analyzer.add_sentiment(1.0 - i * 0.05, datetime.now())
        
        trend = analyzer.get_trend()
        assert trend == "declining"
        
        # Add improving sentiments
        analyzer = TemporalSentimentAnalyzer()
        for i in range(20):
            analyzer.add_sentiment(-0.5 + i * 0.05, datetime.now())
        
        trend = analyzer.get_trend()
        assert trend == "improving"
    
    def test_get_momentum(self):
        analyzer = TemporalSentimentAnalyzer()
        
        # Add accelerating positive sentiments
        for i in range(10):
            analyzer.add_sentiment(i ** 2 / 100, datetime.now())
        
        momentum = analyzer.get_momentum()
        assert momentum > 0  # Positive acceleration
    
    def test_get_volatility(self):
        analyzer = TemporalSentimentAnalyzer()
        
        # Add volatile sentiments
        for i in range(20):
            analyzer.add_sentiment(np.sin(i) * 0.5, datetime.now())
        
        volatility = analyzer.get_volatility()
        assert volatility > 0.2  # High volatility
    
    def test_predict_next_sentiment(self):
        analyzer = TemporalSentimentAnalyzer()
        
        # Add linear trend
        for i in range(20):
            analyzer.add_sentiment(0.1 * i - 1, datetime.now())
        
        prediction = analyzer.predict_next_sentiment()
        assert -1 <= prediction <= 1


class TestSocialMediaSentimentAnalyzer:
    """Test social media sentiment analysis"""
    
    def test_emoji_sentiment(self):
        analyzer = SocialMediaSentimentAnalyzer()
        
        # Test positive emojis
        text = "Great news! 🚀📈"
        result = analyzer.analyze_social_text(text)
        assert result['emoji_score'] > 0.5
        
        # Test negative emojis
        text = "Bad results 😢📉"
        result = analyzer.analyze_social_text(text)
        assert result['emoji_score'] < -0.5
    
    def test_platform_weights(self):
        analyzer = SocialMediaSentimentAnalyzer()
        
        text = "Market analysis"
        
        # Test different platforms
        twitter_result = analyzer.analyze_social_text(text, 'twitter')
        news_result = analyzer.analyze_social_text(text, 'news')
        
        assert twitter_result['platform_weight'] == 0.7
        assert news_result['platform_weight'] == 1.0
    
    def test_clean_social_text(self):
        analyzer = SocialMediaSentimentAnalyzer()
        
        text = "@user Check this http://example.com #stocks"
        clean = analyzer._clean_social_text(text)
        
        assert "@user" not in clean
        assert "http://example.com" not in clean
        assert "stocks" in clean  # Hashtag word kept
        assert "#stocks" not in clean  # Hash symbol removed


class TestMarketSentimentAggregator:
    """Test market sentiment aggregation"""
    
    def test_add_sentiment(self):
        aggregator = MarketSentimentAggregator(timedelta(hours=1))
        
        result = SentimentResult(
            text="Test",
            sentiment=SentimentType.POSITIVE,
            score=0.8,
            confidence=0.9,
            language="en",
            source="reuters"
        )
        
        aggregator.add_sentiment(result)
        assert len(aggregator.sentiment_buffer) == 1
    
    def test_calculate_market_sentiment(self):
        aggregator = MarketSentimentAggregator()
        
        # Add various sentiments
        for i in range(10):
            result = SentimentResult(
                text=f"Test {i}",
                sentiment=SentimentType.POSITIVE if i % 2 == 0 else SentimentType.NEGATIVE,
                score=0.5 if i % 2 == 0 else -0.5,
                confidence=0.8,
                language="en",
                source="reuters" if i % 3 == 0 else "twitter"
            )
            aggregator.add_sentiment(result)
        
        market_sentiment = aggregator.calculate_market_sentiment()
        
        assert isinstance(market_sentiment, MarketSentiment)
        assert market_sentiment.trend in ["bullish", "bearish", "neutral"]
        assert 0 <= market_sentiment.confidence <= 1
        assert isinstance(market_sentiment.sentiment_distribution, dict)
    
    def test_time_decay(self):
        aggregator = MarketSentimentAggregator(timedelta(hours=1))
        
        # Add old sentiment
        old_result = SentimentResult(
            text="Old",
            sentiment=SentimentType.NEGATIVE,
            score=-0.9,
            confidence=0.9,
            language="en",
            timestamp=datetime.now() - timedelta(hours=2)
        )
        aggregator.sentiment_buffer.append(old_result)
        
        # Add new sentiment
        new_result = SentimentResult(
            text="New",
            sentiment=SentimentType.POSITIVE,
            score=0.9,
            confidence=0.9,
            language="en"
        )
        aggregator.add_sentiment(new_result)
        
        # Old sentiment should be removed
        assert len(aggregator.sentiment_buffer) == 1
        assert aggregator.sentiment_buffer[0].text == "New"


class TestSentimentCache:
    """Test sentiment caching"""
    
    def test_cache_operations(self):
        cache = SentimentCache(max_size=10, ttl=timedelta(hours=1))
        
        result = SentimentResult(
            text="Test",
            sentiment=SentimentType.POSITIVE,
            score=0.8,
            confidence=0.9,
            language="en"
        )
        
        # Test put and get
        cache.put("Test", "en", "twitter", result)
        cached = cache.get("Test", "en", "twitter")
        
        assert cached is not None
        assert cached.text == "Test"
        assert cache.hit_count == 1
        
        # Test miss
        missing = cache.get("Other", "en", "twitter")
        assert missing is None
        assert cache.miss_count == 1
    
    def test_cache_expiry(self):
        cache = SentimentCache(ttl=timedelta(seconds=0))
        
        result = SentimentResult(
            text="Test",
            sentiment=SentimentType.POSITIVE,
            score=0.8,
            confidence=0.9,
            language="en"
        )
        
        cache.put("Test", "en", "twitter", result)
        
        # Should be expired immediately
        cached = cache.get("Test", "en", "twitter")
        assert cached is None
    
    def test_cache_stats(self):
        cache = SentimentCache()
        
        # Add some entries
        for i in range(5):
            result = SentimentResult(
                text=f"Test {i}",
                sentiment=SentimentType.NEUTRAL,
                score=0.0,
                confidence=0.5,
                language="en"
            )
            cache.put(f"Test {i}", "en", "source", result)
        
        # Get some entries
        cache.get("Test 0", "en", "source")
        cache.get("Test 999", "en", "source")  # Miss
        
        stats = cache.get_stats()
        assert stats['size'] == 5
        assert stats['hit_count'] == 1
        assert stats['miss_count'] == 1
        assert stats['hit_rate'] == 0.5


class TestSentimentImpactPredictor:
    """Test sentiment impact prediction"""
    
    def test_train(self):
        predictor = SentimentImpactPredictor()
        
        # Create training data
        data = pd.DataFrame({
            'sentiment_score': np.random.uniform(-1, 1, 100),
            'confidence': np.random.uniform(0.5, 1, 100),
            'source_credibility': np.random.uniform(0.5, 1, 100),
            'market_volatility': np.random.uniform(0.01, 0.05, 100),
            'trading_volume': np.random.uniform(1e6, 1e7, 100),
            'time_of_day': np.random.uniform(0, 1, 100),
            'day_of_week': np.random.uniform(0, 1, 100),
            'is_market_hours': np.random.choice([0, 1], 100),
            'trend_strength': np.random.uniform(-0.5, 0.5, 100),
            'sentiment_momentum': np.random.uniform(-0.1, 0.1, 100),
            'actual_price_change': np.random.uniform(-0.05, 0.05, 100)
        })
        
        predictor.train(data)
        assert predictor.is_trained
    
    def test_predict_impact(self):
        predictor = SentimentImpactPredictor()
        
        # Train first
        data = pd.DataFrame({
            'sentiment_score': [0.5, -0.5, 0.8, -0.8],
            'confidence': [0.9, 0.8, 0.95, 0.85],
            'source_credibility': [1.0, 0.9, 0.95, 0.8],
            'market_volatility': [0.02, 0.03, 0.025, 0.035],
            'trading_volume': [1e6, 1.5e6, 2e6, 1.2e6],
            'time_of_day': [0.5, 0.6, 0.4, 0.7],
            'day_of_week': [0.2, 0.4, 0.6, 0.8],
            'is_market_hours': [1, 1, 0, 1],
            'trend_strength': [0.2, -0.1, 0.3, -0.2],
            'sentiment_momentum': [0.05, -0.03, 0.08, -0.06],
            'actual_price_change': [0.02, -0.015, 0.03, -0.025]
        })
        
        predictor.train(data)
        
        # Test prediction
        result = SentimentResult(
            text="Test",
            sentiment=SentimentType.POSITIVE,
            score=0.7,
            confidence=0.85,
            language="en",
            credibility_weight=0.9
        )
        
        market_data = {
            'volatility': 0.025,
            'volume': 1.5e6,
            'trend_strength': 0.2,
            'sentiment_momentum': 0.05
        }
        
        impact = predictor.predict_impact(result, market_data)
        assert -0.05 <= impact <= 0.05


class TestEnsembleSentimentAnalyzer:
    """Test ensemble sentiment analysis"""
    
    @patch.object(LanguageModel, 'analyze')
    def test_analyze_ensemble(self, mock_analyze):
        # Create mock models
        models = []
        for i in range(3):
            model = MagicMock()
            model.model_name = f"model_{i}"
            model.analyze = MagicMock()
            models.append(model)
        
        # Set different results for each model
        models[0].analyze.return_value = {'label': 'positive', 'score': 0.9}
        models[1].analyze.return_value = {'label': 'positive', 'score': 0.8}
        models[2].analyze.return_value = {'label': 'neutral', 'score': 0.6}
        
        ensemble = EnsembleSentimentAnalyzer(models)
        result = ensemble.analyze_ensemble("Test text")
        
        assert 'score' in result
        assert 'confidence' in result
        assert 'individual_scores' in result
        assert len(result['individual_scores']) == 3


class TestAdvancedSentimentAnalyzer:
    """Test main advanced sentiment analyzer"""
    
    @patch('utils.sentiment_analyzer.LanguageModel')
    def test_initialization(self, mock_language_model):
        analyzer = AdvancedSentimentAnalyzer(languages=['en'], enable_cache=True)
        
        assert analyzer.device in ['cpu', 'cuda']
        assert 'en' in analyzer.languages
        assert analyzer.cache is not None
    
    @patch('utils.sentiment_analyzer.detect')
    def test_detect_language(self, mock_detect):
        analyzer = AdvancedSentimentAnalyzer()
        
        # Test English detection
        mock_detect.return_value = 'en'
        lang = analyzer.detect_language("This is English text")
        assert lang == 'en'
        
        # Test Persian detection
        mock_detect.return_value = 'fa'
        lang = analyzer.detect_language("این متن فارسی است")
        assert lang == 'fa'
        
        # Test unsupported language
        mock_detect.return_value = 'de'
        lang = analyzer.detect_language("Dies ist deutscher Text")
        assert lang == 'en'  # Falls back to English
    
    def test_backward_compatibility(self):
        """Test backward compatibility with old SentimentAnalyzer"""
        analyzer = SentimentAnalyzer()
        
        # Should work like old interface
        score = analyzer.get_sentiment_score("Positive text", source="twitter")
        assert isinstance(score, float)
        assert -1 <= score <= 1


class TestIntegration:
    """Integration tests"""
    
    @patch('utils.sentiment_analyzer.LanguageModel')
    def test_full_analysis_workflow(self, mock_language_model):
        # Setup mock
        mock_model = MagicMock()
        mock_model.analyze.return_value = {'label': 'positive', 'score': 0.85}
        mock_language_model.return_value = mock_model
        
        analyzer = AdvancedSentimentAnalyzer(languages=['en'])
        
        # Bypass actual model loading
        mock_ensemble = MagicMock()
        mock_ensemble.analyze_ensemble.return_value = {
            'score': 0.8,
            'confidence': 0.9,
            'individual_scores': {'model_1': 0.8}
        }
        analyzer.language_models['en'] = mock_ensemble
        
        # Test single text analysis
        result = analyzer.analyze(
            "The company reported excellent earnings!",
            source="reuters",
            analyze_aspects=True,
            analyze_entities=True,
            predict_impact=False
        )
        
        assert isinstance(result, SentimentResult)
        assert result.sentiment in [SentimentType.POSITIVE, SentimentType.VERY_POSITIVE]
        assert result.score > 0
        assert result.source == "reuters"
        assert result.credibility_weight == 1.0
    
    @patch('utils.sentiment_analyzer.LanguageModel')
    def test_batch_analysis(self, mock_language_model):
        # Setup mock
        mock_model = MagicMock()
        mock_model.analyze.return_value = {'label': 'positive', 'score': 0.85}
        mock_language_model.return_value = mock_model
        
        analyzer = AdvancedSentimentAnalyzer(languages=['en'])
        
        # Bypass actual model loading
        mock_ensemble = MagicMock()
        mock_ensemble.analyze_ensemble.return_value = {
            'score': 0.8,
            'confidence': 0.9,
            'individual_scores': {'model_1': 0.8}
        }
        analyzer.language_models['en'] = mock_ensemble
        
        # Test batch analysis
        texts = ["Text 1", "Text 2", "Text 3"]
        sources = ["reuters", "twitter", "bloomberg"]
        
        results = analyzer.batch_analyze(texts, sources)
        
        assert len(results) == 3
        assert all(isinstance(r, SentimentResult) for r in results)


if __name__ == "__main__":
    pytest.main(["-v", __file__]) 