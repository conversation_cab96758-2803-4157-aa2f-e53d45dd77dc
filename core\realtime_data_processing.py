#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ Real-time Data Processing System
سیستم پردازش داده‌های real-time با WebSocket و معماری event-driven
"""

import os
import sys
import time
import json
import asyncio
import aiohttp
import threading
from typing import Dict, List, Optional, Any, Callable, Union, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from decimal import Decimal
from abc import ABC, abstractmethod
import logging
from contextlib import asynccontextmanager
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException
import queue
import weakref

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.exceptions import TradingSystemError, ConnectionError, ValidationError
from core.multi_exchange import MarketData, DataType

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class EventType(Enum):
    """نوع رویداد"""
    MARKET_DATA = "market_data"
    ORDER_UPDATE = "order_update"
    TRADE_UPDATE = "trade_update"
    POSITION_UPDATE = "position_update"
    ACCOUNT_UPDATE = "account_update"
    NEWS_EVENT = "news_event"
    ALERT = "alert"
    SYSTEM_EVENT = "system_event"
    ERROR = "error"

class DataSource(Enum):
    """منبع داده"""
    WEBSOCKET = "websocket"
    REST_API = "rest_api"
    DATABASE = "database"
    FILE = "file"
    SIMULATION = "simulation"

class ProcessingStatus(Enum):
    """وضعیت پردازش"""
    IDLE = "idle"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    PROCESSING = "processing"
    ERROR = "error"
    DISCONNECTED = "disconnected"

@dataclass
class RealTimeEvent:
    """رویداد real-time"""
    event_id: str
    event_type: EventType
    timestamp: datetime
    source: DataSource
    data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if isinstance(self.timestamp, str):
            self.timestamp = datetime.fromisoformat(self.timestamp)

@dataclass
class DataStreamConfig:
    """تنظیمات stream داده"""
    stream_id: str
    name: str
    source: DataSource
    url: str = ""
    symbols: List[str] = field(default_factory=list)
    data_types: List[DataType] = field(default_factory=list)
    reconnect_interval: int = 5  # seconds
    heartbeat_interval: int = 30  # seconds
    max_reconnect_attempts: int = 10
    buffer_size: int = 1000
    enabled: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ProcessingMetrics:
    """معیارهای پردازش"""
    events_processed: int = 0
    events_dropped: int = 0
    processing_time_avg: float = 0.0
    processing_time_max: float = 0.0
    events_per_second: float = 0.0
    last_update: datetime = field(default_factory=datetime.now)
    error_count: int = 0
    connection_uptime: float = 0.0

class EventHandler(ABC):
    """کلاس پایه event handler"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{name}")
        self.enabled = True
        self.priority = 0
        self.metrics = ProcessingMetrics()
    
    @abstractmethod
    async def handle_event(self, event: RealTimeEvent) -> bool:
        """پردازش رویداد"""
        pass
    
    async def pre_process(self, event: RealTimeEvent) -> RealTimeEvent:
        """پیش‌پردازش رویداد"""
        return event
    
    async def post_process(self, event: RealTimeEvent, result: bool):
        """پس‌پردازش رویداد"""
        pass

class MarketDataHandler(EventHandler):
    """پردازشگر داده‌های بازار"""
    
    def __init__(self, name: str = "market_data_handler"):
        super().__init__(name)
        self.data_cache: Dict[str, MarketData] = {}
        self.subscribers: Set[Callable] = set()
        self.last_update = datetime.now()
    
    async def handle_event(self, event: RealTimeEvent) -> bool:
        """پردازش رویداد داده بازار"""
        try:
            start_time = time.time()
            
            if event.event_type != EventType.MARKET_DATA:
                return False
            
            # تبدیل به MarketData
            market_data = self._parse_market_data(event.data)
            if market_data:
                # ذخیره در cache
                cache_key = f"{market_data.symbol}_{market_data.exchange_id}"
                self.data_cache[cache_key] = market_data
                
                # اطلاع‌رسانی به subscribers
                await self._notify_subscribers(market_data)
                
                # بروزرسانی معیارها
                processing_time = time.time() - start_time
                self._update_metrics(processing_time)
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error processing market data event: {e}")
            self.metrics.error_count += 1
            return False
    
    def _parse_market_data(self, data: Dict[str, Any]) -> Optional[MarketData]:
        """تبدیل داده به MarketData"""
        try:
            return MarketData(
                symbol=data.get('symbol', ''),
                exchange_id=data.get('exchange_id', ''),
                data_type=DataType(data.get('data_type', 'tick')),
                timestamp=datetime.fromisoformat(data.get('timestamp', datetime.now().isoformat())),
                bid=Decimal(str(data.get('bid', 0))) if data.get('bid') else None,
                ask=Decimal(str(data.get('ask', 0))) if data.get('ask') else None,
                last=Decimal(str(data.get('last', 0))) if data.get('last') else None,
                volume=Decimal(str(data.get('volume', 0))) if data.get('volume') else None,
                high=Decimal(str(data.get('high', 0))) if data.get('high') else None,
                low=Decimal(str(data.get('low', 0))) if data.get('low') else None,
                open=Decimal(str(data.get('open', 0))) if data.get('open') else None,
                close=Decimal(str(data.get('close', 0))) if data.get('close') else None,
                metadata=data.get('metadata', {})
            )
        except Exception as e:
            self.logger.error(f"Error parsing market data: {e}")
            return None
    
    async def _notify_subscribers(self, market_data: MarketData):
        """اطلاع‌رسانی به subscribers"""
        for subscriber in self.subscribers:
            try:
                if asyncio.iscoroutinefunction(subscriber):
                    await subscriber(market_data)
                else:
                    subscriber(market_data)
            except Exception as e:
                self.logger.error(f"Error notifying subscriber: {e}")
    
    def _update_metrics(self, processing_time: float):
        """بروزرسانی معیارها"""
        self.metrics.events_processed += 1
        self.metrics.processing_time_max = max(self.metrics.processing_time_max, processing_time)
        
        # میانگین زمان پردازش
        if self.metrics.events_processed == 1:
            self.metrics.processing_time_avg = processing_time
        else:
            self.metrics.processing_time_avg = (
                (self.metrics.processing_time_avg * (self.metrics.events_processed - 1) + processing_time) /
                self.metrics.events_processed
            )
        
        # events per second
        time_diff = (datetime.now() - self.metrics.last_update).total_seconds()
        if time_diff > 0:
            self.metrics.events_per_second = self.metrics.events_processed / time_diff
        
        self.metrics.last_update = datetime.now()
    
    def subscribe(self, callback: Callable):
        """عضویت در دریافت داده"""
        self.subscribers.add(callback)
    
    def unsubscribe(self, callback: Callable):
        """لغو عضویت"""
        self.subscribers.discard(callback)
    
    def get_latest_data(self, symbol: str, exchange_id: str) -> Optional[MarketData]:
        """دریافت آخرین داده"""
        cache_key = f"{symbol}_{exchange_id}"
        return self.data_cache.get(cache_key)

class WebSocketDataStream:
    """WebSocket data stream"""
    
    def __init__(self, config: DataStreamConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{config.stream_id}")
        self.status = ProcessingStatus.IDLE
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.reconnect_attempts = 0
        self.last_heartbeat = datetime.now()
        self.event_queue = asyncio.Queue(maxsize=config.buffer_size)
        self.handlers: List[EventHandler] = []
        self.running = False
        self.connection_start_time = None
        
        # Statistics
        self.stats = {
            "messages_received": 0,
            "messages_processed": 0,
            "connection_errors": 0,
            "reconnect_attempts": 0,
            "uptime_start": None,
            "last_message_time": None
        }
    
    async def start(self):
        """شروع stream"""
        self.running = True
        self.stats["uptime_start"] = datetime.now()
        
        while self.running:
            try:
                await self._connect()
                await self._process_messages()
            except Exception as e:
                self.logger.error(f"Stream error: {e}")
                self.status = ProcessingStatus.ERROR
                await self._handle_disconnection()
    
    async def stop(self):
        """توقف stream"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
        self.status = ProcessingStatus.DISCONNECTED
    
    async def _connect(self):
        """اتصال به WebSocket"""
        self.status = ProcessingStatus.CONNECTING
        self.logger.info(f"Connecting to {self.config.url}")
        
        try:
            self.websocket = await websockets.connect(
                self.config.url,
                ping_interval=self.config.heartbeat_interval,
                ping_timeout=10
            )
            
            self.status = ProcessingStatus.CONNECTED
            self.connection_start_time = datetime.now()
            self.reconnect_attempts = 0
            self.logger.info(f"Connected to {self.config.url}")
            
        except Exception as e:
            self.logger.error(f"Connection failed: {e}")
            self.stats["connection_errors"] += 1
            raise
    
    async def _process_messages(self):
        """پردازش پیام‌ها"""
        self.status = ProcessingStatus.PROCESSING
        
        try:
            async for message in self.websocket:
                if not self.running:
                    break
                
                self.stats["messages_received"] += 1
                self.stats["last_message_time"] = datetime.now()
                
                # پردازش پیام
                event = await self._parse_message(message)
                if event:
                    await self._handle_event(event)
                    self.stats["messages_processed"] += 1
                
        except ConnectionClosed:
            self.logger.info("WebSocket connection closed")
        except Exception as e:
            self.logger.error(f"Error processing messages: {e}")
            raise
    
    async def _parse_message(self, message: str) -> Optional[RealTimeEvent]:
        """تبدیل پیام به رویداد"""
        try:
            data = json.loads(message)
            
            return RealTimeEvent(
                event_id=f"evt_{int(time.time() * 1000)}",
                event_type=EventType(data.get('type', 'market_data')),
                timestamp=datetime.now(),
                source=self.config.source,
                data=data.get('data', {}),
                metadata=data.get('metadata', {})
            )
            
        except Exception as e:
            self.logger.error(f"Error parsing message: {e}")
            return None
    
    async def _handle_event(self, event: RealTimeEvent):
        """پردازش رویداد"""
        for handler in self.handlers:
            if handler.enabled:
                try:
                    # پیش‌پردازش
                    processed_event = await handler.pre_process(event)
                    
                    # پردازش اصلی
                    result = await handler.handle_event(processed_event)
                    
                    # پس‌پردازش
                    await handler.post_process(processed_event, result)
                    
                except Exception as e:
                    self.logger.error(f"Error in handler {handler.name}: {e}")
                    handler.metrics.error_count += 1
    
    async def _handle_disconnection(self):
        """مدیریت قطع اتصال"""
        self.status = ProcessingStatus.DISCONNECTED
        
        if self.reconnect_attempts < self.config.max_reconnect_attempts:
            self.reconnect_attempts += 1
            self.stats["reconnect_attempts"] += 1
            
            wait_time = self.config.reconnect_interval * self.reconnect_attempts
            self.logger.info(f"Reconnecting in {wait_time} seconds (attempt {self.reconnect_attempts})")
            
            await asyncio.sleep(wait_time)
        else:
            self.logger.error("Max reconnection attempts reached")
            self.running = False
    
    def add_handler(self, handler: EventHandler):
        """اضافه کردن handler"""
        self.handlers.append(handler)
        self.handlers.sort(key=lambda h: h.priority, reverse=True)
    
    def remove_handler(self, handler: EventHandler):
        """حذف handler"""
        if handler in self.handlers:
            self.handlers.remove(handler)
    
    def get_uptime(self) -> float:
        """محاسبه uptime"""
        if self.connection_start_time:
            return (datetime.now() - self.connection_start_time).total_seconds()
        return 0.0
    
    def get_statistics(self) -> Dict[str, Any]:
        """دریافت آمار"""
        return {
            **self.stats,
            "status": self.status.value,
            "uptime": self.get_uptime(),
            "reconnect_attempts": self.reconnect_attempts,
            "handlers_count": len(self.handlers)
        }

class RealTimeDataProcessor:
    """پردازشگر اصلی داده‌های real-time"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.streams: Dict[str, WebSocketDataStream] = {}
        self.handlers: Dict[str, EventHandler] = {}
        self.event_bus = asyncio.Queue(maxsize=10000)
        self.running = False
        self.tasks: List[asyncio.Task] = []
        
        # Global statistics
        self.global_stats = {
            "total_events_processed": 0,
            "total_events_dropped": 0,
            "active_streams": 0,
            "active_handlers": 0,
            "start_time": datetime.now(),
            "last_event_time": None
        }
    
    async def start(self):
        """شروع پردازشگر"""
        self.running = True
        self.logger.info("Starting Real-time Data Processor")
        
        # شروع event bus processor
        self.tasks.append(asyncio.create_task(self._process_event_bus()))
        
        # شروع تمام streams
        for stream in self.streams.values():
            if stream.config.enabled:
                self.tasks.append(asyncio.create_task(stream.start()))
                self.global_stats["active_streams"] += 1
    
    async def stop(self):
        """توقف پردازشگر"""
        self.running = False
        self.logger.info("Stopping Real-time Data Processor")
        
        # توقف تمام streams
        for stream in self.streams.values():
            await stream.stop()
        
        # لغو تمام tasks
        for task in self.tasks:
            task.cancel()
        
        await asyncio.gather(*self.tasks, return_exceptions=True)
        self.tasks.clear()
    
    def add_stream(self, config: DataStreamConfig) -> str:
        """اضافه کردن stream"""
        stream = WebSocketDataStream(config)
        self.streams[config.stream_id] = stream
        
        # اضافه کردن handlers
        for handler in self.handlers.values():
            stream.add_handler(handler)
        
        self.logger.info(f"Added stream: {config.name} ({config.stream_id})")
        return config.stream_id
    
    def remove_stream(self, stream_id: str):
        """حذف stream"""
        if stream_id in self.streams:
            stream = self.streams[stream_id]
            asyncio.create_task(stream.stop())
            del self.streams[stream_id]
            self.logger.info(f"Removed stream: {stream_id}")
    
    def add_handler(self, handler: EventHandler):
        """اضافه کردن handler"""
        self.handlers[handler.name] = handler
        
        # اضافه کردن به تمام streams
        for stream in self.streams.values():
            stream.add_handler(handler)
        
        self.global_stats["active_handlers"] += 1
        self.logger.info(f"Added handler: {handler.name}")
    
    def remove_handler(self, handler_name: str):
        """حذف handler"""
        if handler_name in self.handlers:
            handler = self.handlers[handler_name]
            
            # حذف از تمام streams
            for stream in self.streams.values():
                stream.remove_handler(handler)
            
            del self.handlers[handler_name]
            self.global_stats["active_handlers"] -= 1
            self.logger.info(f"Removed handler: {handler_name}")
    
    async def _process_event_bus(self):
        """پردازش event bus"""
        while self.running:
            try:
                event = await asyncio.wait_for(self.event_bus.get(), timeout=1.0)
                await self._process_global_event(event)
                self.global_stats["total_events_processed"] += 1
                self.global_stats["last_event_time"] = datetime.now()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing event bus: {e}")
                self.global_stats["total_events_dropped"] += 1
    
    async def _process_global_event(self, event: RealTimeEvent):
        """پردازش رویداد سراسری"""
        # اینجا می‌توانید منطق پردازش سراسری اضافه کنید
        pass
    
    def publish_event(self, event: RealTimeEvent):
        """انتشار رویداد"""
        try:
            self.event_bus.put_nowait(event)
        except asyncio.QueueFull:
            self.logger.warning("Event bus is full, dropping event")
            self.global_stats["total_events_dropped"] += 1
    
    def get_stream_status(self, stream_id: str) -> Optional[Dict[str, Any]]:
        """وضعیت stream"""
        if stream_id in self.streams:
            return self.streams[stream_id].get_statistics()
        return None
    
    def get_all_streams_status(self) -> Dict[str, Dict[str, Any]]:
        """وضعیت تمام streams"""
        return {
            stream_id: stream.get_statistics()
            for stream_id, stream in self.streams.items()
        }
    
    def get_handler_metrics(self, handler_name: str) -> Optional[ProcessingMetrics]:
        """معیارهای handler"""
        if handler_name in self.handlers:
            return self.handlers[handler_name].metrics
        return None
    
    def get_global_statistics(self) -> Dict[str, Any]:
        """آمار سراسری"""
        return {
            **self.global_stats,
            "uptime": (datetime.now() - self.global_stats["start_time"]).total_seconds(),
            "streams_count": len(self.streams),
            "handlers_count": len(self.handlers)
        }

# Global instance
_realtime_processor: Optional[RealTimeDataProcessor] = None

def get_realtime_processor() -> RealTimeDataProcessor:
    """دریافت instance سراسری"""
    global _realtime_processor
    if _realtime_processor is None:
        _realtime_processor = RealTimeDataProcessor()
    return _realtime_processor

async def close_realtime_processor():
    """بستن processor سراسری"""
    global _realtime_processor
    if _realtime_processor:
        await _realtime_processor.stop()
        _realtime_processor = None

@asynccontextmanager
async def realtime_session():
    """Context manager برای session real-time"""
    processor = get_realtime_processor()
    try:
        await processor.start()
        yield processor
    finally:
        await processor.stop()

# Utility functions
def create_market_data_stream(
    stream_id: str,
    name: str,
    url: str,
    symbols: List[str],
    data_types: List[DataType] = None
) -> DataStreamConfig:
    """ایجاد تنظیمات stream داده بازار"""
    if data_types is None:
        data_types = [DataType.TICK, DataType.QUOTE]
    
    return DataStreamConfig(
        stream_id=stream_id,
        name=name,
        source=DataSource.WEBSOCKET,
        url=url,
        symbols=symbols,
        data_types=data_types
    )

async def test_realtime_processing():
    """تست سیستم real-time processing"""
    print("⚡ Testing Real-time Data Processing System...")
    
    try:
        # ایجاد processor
        processor = get_realtime_processor()
        
        # ایجاد handler
        market_handler = MarketDataHandler("test_market_handler")
        processor.add_handler(market_handler)
        
        # ایجاد stream (simulated)
        config = create_market_data_stream(
            "test_stream",
            "Test Market Stream",
            "wss://test.example.com/ws",
            ["EURUSD", "GBPUSD"]
        )
        
        stream_id = processor.add_stream(config)
        
        # شروع processor
        await processor.start()
        
        # تست انتشار رویداد
        test_event = RealTimeEvent(
            event_id="test_001",
            event_type=EventType.MARKET_DATA,
            timestamp=datetime.now(),
            source=DataSource.SIMULATION,
            data={
                "symbol": "EURUSD",
                "exchange_id": "test_exchange",
                "data_type": "tick",
                "bid": "1.0950",
                "ask": "1.0952",
                "timestamp": datetime.now().isoformat()
            }
        )
        
        processor.publish_event(test_event)
        
        # انتظار برای پردازش
        await asyncio.sleep(1)
        
        # بررسی آمار
        stats = processor.get_global_statistics()
        print(f"   ✓ Events processed: {stats['total_events_processed']}")
        print(f"   ✓ Active streams: {stats['active_streams']}")
        print(f"   ✓ Active handlers: {stats['active_handlers']}")
        
        # توقف processor
        await processor.stop()
        
        print("✅ Real-time Data Processing System test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_realtime_processing()) 