#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سیستم Backtesting با ادغام Sentiment Analysis
این ماژول سیستم backtesting پیشرفته با تحلیل احساسات فراهم می‌کند
"""

import os
import sys
import pandas as pd
import numpy as np
import backtrader as bt
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
import json

# تنظیم پروکسی
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:10809'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:10809'

# Import sentiment analyzer
try:
    from utils.offline_sentiment_analyzer import SentimentAnalyzer
    SENTIMENT_AVAILABLE = True
except ImportError:
    SENTIMENT_AVAILABLE = False
    print("⚠️ Sentiment Analyzer در دسترس نیست")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SentimentData:
    """کلاس داده‌های احساسات"""
    
    def __init__(self):
        self.sentiment_scores = []
        self.sentiment_labels = []
        self.timestamps = []
        self.sources = []
    
    def add_sentiment(self, score: float, label: str, timestamp: datetime, source: str = "news"):
        """اضافه کردن داده احساسات"""
        self.sentiment_scores.append(score)
        self.sentiment_labels.append(label)
        self.timestamps.append(timestamp)
        self.sources.append(source)
    
    def get_sentiment_at_time(self, target_time: datetime) -> Tuple[float, str]:
        """دریافت احساسات در زمان مشخص"""
        if not self.timestamps:
            return 0.5, "neutral"
        
        # پیدا کردن نزدیک‌ترین زمان
        closest_idx = 0
        min_diff = abs((self.timestamps[0] - target_time).total_seconds())
        
        for i, ts in enumerate(self.timestamps):
            diff = abs((ts - target_time).total_seconds())
            if diff < min_diff:
                min_diff = diff
                closest_idx = i
        
        # اگر فاصله زمانی بیش از 24 ساعت باشد، neutral برگردان
        if min_diff > 86400:  # 24 ساعت
            return 0.5, "neutral"
        
        return self.sentiment_scores[closest_idx], self.sentiment_labels[closest_idx]


class SentimentStrategy(bt.Strategy):
    """استراتژی معاملاتی با تحلیل احساسات"""
    
    params = (
        ('sentiment_threshold', 0.6),  # آستانه احساسات
        ('position_size', 0.1),        # اندازه موقعیت
        ('stop_loss', 0.02),           # حد ضرر
        ('take_profit', 0.04),         # حد سود
        ('sentiment_weight', 0.3),     # وزن احساسات در تصمیم‌گیری
    )
    
    def __init__(self):
        self.sentiment_data = SentimentData()
        self.sentiment_analyzer = None
        self.order = None
        self.stop_order = None
        self.profit_order = None
        
        # Initialize sentiment analyzer
        if SENTIMENT_AVAILABLE:
            try:
                self.sentiment_analyzer = SentimentAnalyzer()
                logger.info("✅ Sentiment Analyzer initialized")
            except Exception as e:
                logger.error(f"❌ Error initializing Sentiment Analyzer: {e}")
        
        # Indicators
        self.sma_short = bt.indicators.SimpleMovingAverage(self.data.close, period=10)
        self.sma_long = bt.indicators.SimpleMovingAverage(self.data.close, period=30)
        self.rsi = bt.indicators.RSI(self.data.close, period=14)
        
        # Sentiment indicators
        self.sentiment_signal = bt.indicators.SmoothedMovingAverage(
            self.data.volume, period=5
        )  # Placeholder for sentiment signal
        
        logger.info("✅ Sentiment Strategy initialized")
    
    def add_sentiment_data(self, text: str, timestamp: datetime, source: str = "news"):
        """اضافه کردن داده احساسات"""
        if self.sentiment_analyzer:
            try:
                result = self.sentiment_analyzer.analyze(text)
                self.sentiment_data.add_sentiment(
                    result.score, result.label, timestamp, source
                )
                logger.info(f"📊 Added sentiment: {result.label} ({result.score:.3f})")
            except Exception as e:
                logger.error(f"❌ Error analyzing sentiment: {e}")
    
    def get_current_sentiment(self) -> Tuple[float, str]:
        """دریافت احساسات فعلی"""
        if not self.sentiment_data.timestamps:
            return 0.5, "neutral"
        
        current_time = self.data.datetime.datetime()
        return self.sentiment_data.get_sentiment_at_time(current_time)
    
    def next(self):
        """منطق اصلی استراتژی"""
        if self.order:
            return  # منتظر تکمیل سفارش
        
        # دریافت احساسات فعلی
        sentiment_score, sentiment_label = self.get_current_sentiment()
        
        # محاسبه سیگنال‌های تکنیکال
        price_signal = 0
        if self.sma_short[0] > self.sma_long[0]:
            price_signal = 1
        elif self.sma_short[0] < self.sma_long[0]:
            price_signal = -1
        
        rsi_signal = 0
        if self.rsi[0] < 30:
            rsi_signal = 1  # Oversold
        elif self.rsi[0] > 70:
            rsi_signal = -1  # Overbought
        
        # محاسبه سیگنال احساسات
        sentiment_signal = 0
        if sentiment_score > 0.6:
            sentiment_signal = 1
        elif sentiment_score < 0.4:
            sentiment_signal = -1
        
        # ترکیب سیگنال‌ها
        technical_weight = 1 - self.params.sentiment_weight
        combined_signal = (
            price_signal * technical_weight * 0.5 +
            rsi_signal * technical_weight * 0.5 +
            sentiment_signal * self.params.sentiment_weight
        )
        
        # تصمیم‌گیری معاملاتی
        if combined_signal > self.params.sentiment_threshold and not self.position:
            # خرید
            size = self.broker.getcash() * self.params.position_size / self.data.close[0]
            self.order = self.buy(size=size)
            logger.info(f"🟢 BUY Signal - Sentiment: {sentiment_label} ({sentiment_score:.3f})")
            
        elif combined_signal < -self.params.sentiment_threshold and self.position:
            # فروش
            self.order = self.sell(size=self.position.size)
            logger.info(f"🔴 SELL Signal - Sentiment: {sentiment_label} ({sentiment_score:.3f})")
    
    def notify_order(self, order):
        """اطلاع‌رسانی سفارش"""
        if order.status in [order.Submitted, order.Accepted]:
            return
        
        if order.status in [order.Completed]:
            if order.isbuy():
                logger.info(f"✅ BUY EXECUTED - Price: {order.executed.price:.2f}")
                # تنظیم حد ضرر و حد سود
                stop_price = order.executed.price * (1 - self.params.stop_loss)
                profit_price = order.executed.price * (1 + self.params.take_profit)
                
                self.stop_order = self.sell(
                    exectype=bt.Order.Stop, price=stop_price, size=order.size
                )
                self.profit_order = self.sell(
                    exectype=bt.Order.Limit, price=profit_price, size=order.size
                )
            else:
                logger.info(f"✅ SELL EXECUTED - Price: {order.executed.price:.2f}")
        
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            logger.warning(f"⚠️ Order Canceled/Margin/Rejected: {order.status}")
        
        self.order = None


class SentimentBacktestingFramework:
    """فریم‌ورک اصلی Backtesting با Sentiment"""
    
    def __init__(self, initial_cash: float = 100000):
        self.cerebro = bt.Cerebro()
        self.initial_cash = initial_cash
        self.sentiment_analyzer = None
        self.results = {}
        
        # Initialize sentiment analyzer
        if SENTIMENT_AVAILABLE:
            try:
                self.sentiment_analyzer = SentimentAnalyzer()
                logger.info("✅ Sentiment Analyzer initialized for backtesting")
            except Exception as e:
                logger.error(f"❌ Error initializing Sentiment Analyzer: {e}")
        
        # Setup cerebro
        self.cerebro.broker.setcash(initial_cash)
        self.cerebro.broker.setcommission(commission=0.001)  # 0.1%
        self.cerebro.addsizer(bt.sizers.PercentSizer, percents=10)
        
        logger.info("✅ Backtesting Framework initialized")
    
    def load_data(self, data_path: str, symbol: str = "EURUSD") -> bool:
        """بارگذاری داده‌های قیمت"""
        try:
            # خواندن فایل CSV
            df = pd.read_csv(data_path)
            logger.info(f"📊 Loaded CSV with columns: {list(df.columns)}")
            
            # تشخیص و تبدیل ستون تاریخ
            datetime_columns = ['datetime', 'Date', 'date', 'time', 'Time', 'timestamp', 'Timestamp']
            datetime_col = None
            
            for col in datetime_columns:
                if col in df.columns:
                    datetime_col = col
                    df[col] = pd.to_datetime(df[col])
                    logger.info(f"✅ Found datetime column: {col}")
                    break
            
            if datetime_col is None:
                logger.error("❌ No datetime column found. Available columns: " + str(list(df.columns)))
                return False
            
            # تنظیم ستون‌های مورد نیاز
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            column_mapping = {
                'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'volume': 'Volume',
                'OPEN': 'Open', 'HIGH': 'High', 'LOW': 'Low', 'CLOSE': 'Close', 'VOLUME': 'Volume'
            }
            
            # اعمال mapping برای ستون‌های موجود
            for old_col, new_col in column_mapping.items():
                if old_col in df.columns and new_col not in df.columns:
                    df[new_col] = df[old_col]
                    logger.info(f"✅ Mapped {old_col} -> {new_col}")
            
            # بررسی ستون‌های الزامی
            missing_columns = []
            for col in required_columns:
                if col not in df.columns:
                    missing_columns.append(col)
            
            if missing_columns:
                logger.error(f"❌ Missing required columns: {missing_columns}")
                logger.info(f"Available columns: {list(df.columns)}")
                return False
            
            # مرتب‌سازی بر اساس تاریخ
            df = df.sort_values(datetime_col)
            df = df.reset_index(drop=True)
            
            # ایجاد DataFeed برای backtrader
            data_feed = bt.feeds.PandasData(
                dataname=df,
                datetime=datetime_col,
                open='Open',
                high='High',
                low='Low',
                close='Close',
                volume='Volume',
                openinterest=None
            )
            
            self.cerebro.adddata(data_feed, name=symbol)
            logger.info(f"✅ Successfully loaded data for {symbol}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error loading data: {e}")
            return False
    
    def add_sentiment_data(self, sentiment_data: List[Dict[str, Any]]):
        """اضافه کردن داده‌های احساسات"""
        if not self.sentiment_analyzer:
            logger.warning("⚠️ Sentiment Analyzer not available")
            return
        
        for item in sentiment_data:
            text = item.get('text', '')
            timestamp = pd.to_datetime(item.get('timestamp', datetime.now()))
            source = item.get('source', 'news')
            
            try:
                result = self.sentiment_analyzer.analyze(text)
                logger.info(f"📊 Sentiment: {result.label} ({result.score:.3f}) for: {text[:50]}...")
            except Exception as e:
                logger.error(f"❌ Error analyzing sentiment: {e}")
    
    def run_backtest(self, strategy_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """اجرای backtest"""
        try:
            # تنظیم پارامترهای استراتژی
            if strategy_params:
                self.cerebro.addstrategy(SentimentStrategy, **strategy_params)
            else:
                self.cerebro.addstrategy(SentimentStrategy)
            
            # اجرای backtest
            logger.info("🚀 Starting backtest...")
            initial_value = self.cerebro.broker.getvalue()
            
            results = self.cerebro.run()
            strategy = results[0]
            
            final_value = self.cerebro.broker.getvalue()
            total_return = (final_value - initial_value) / initial_value * 100
            
            # جمع‌آوری نتایج
            self.results = {
                'initial_value': initial_value,
                'final_value': final_value,
                'total_return': total_return,
                'strategy': strategy,
                'cerebro': self.cerebro
            }
            
            logger.info(f"✅ Backtest completed - Return: {total_return:.2f}%")
            return self.results
            
        except Exception as e:
            logger.error(f"❌ Error running backtest: {e}")
            return {}
    
    def generate_report(self) -> str:
        """تولید گزارش backtest"""
        if not self.results:
            return "❌ No results available"
        
        report = []
        report.append("📊 BACKTESTING REPORT")
        report.append("=" * 50)
        report.append(f"💰 Initial Capital: ${self.results['initial_value']:,.2f}")
        report.append(f"💰 Final Capital: ${self.results['final_value']:,.2f}")
        report.append(f"📈 Total Return: {self.results['total_return']:.2f}%")
        
        # آمار معاملات - فقط اگر اطلاعات قابل استخراج بود
        strategy = self.results['strategy']
        stats = getattr(strategy, 'stats', None)
        if stats and hasattr(stats, '__dict__') and stats.__dict__:
            report.append(f"ℹ️ Stats object detected, but trade stats not available in this strategy.")
        else:
            report.append(f"ℹ️ Trade statistics not available for this strategy.")
        
        # اطلاعات اضافی
        report.append(f"📅 Strategy: {strategy.__class__.__name__}")
        report.append(f"💼 Final Cash: ${self.cerebro.broker.getcash():,.2f}")
        
        return "\n".join(report)
    
    def plot_results(self, filename: str = "backtest_results.png"):
        """رسم نمودار نتایج"""
        try:
            self.cerebro.plot(style='candlestick', barup='green', bardown='red')
            logger.info(f"✅ Plot saved as {filename}")
        except Exception as e:
            logger.error(f"❌ Error plotting results: {e}")


def main():
    """تابع اصلی برای تست"""
    print("🧪 Testing Backtesting Framework with Sentiment Analysis")
    print("=" * 60)
    
    # ایجاد فریم‌ورک
    framework = SentimentBacktestingFramework(initial_cash=100000)
    
    # بارگذاری داده‌ها
    data_path = "data/EURUSD/H1.csv"
    if os.path.exists(data_path):
        success = framework.load_data(data_path, "EURUSD")
        if success:
            # اضافه کردن داده‌های احساسات نمونه
            sample_sentiments = [
                {
                    'text': 'شرکت اپل سود بالایی گزارش داد',
                    'timestamp': '2024-01-15 10:00:00',
                    'source': 'news'
                },
                {
                    'text': 'بازار سهام امروز افت کرد',
                    'timestamp': '2024-01-15 14:00:00',
                    'source': 'news'
                },
                {
                    'text': 'Apple reported high profits',
                    'timestamp': '2024-01-16 09:00:00',
                    'source': 'news'
                }
            ]
            
            framework.add_sentiment_data(sample_sentiments)
            
            # اجرای backtest
            results = framework.run_backtest()
            
            # تولید گزارش
            report = framework.generate_report()
            print(report)
            
            return True
        else:
            print("❌ Failed to load data")
            return False
    else:
        print(f"❌ Data file not found: {data_path}")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Backtesting Framework test completed successfully!")
    else:
        print("\n💥 Backtesting Framework test failed!") 