# سوالات متداول (FAQ) پروژه Trading Bot

## ۱. چطور تنظیمات را override کنم؟
- با متغیر محیطی (مثلاً `LOT_SIZE=0.5 python main.py`)
- با آرگومان خط فرمان (مثلاً `python main.py --lot_size 0.5`)

## ۲. خروجی نتایج در چه فرمت‌هایی ذخیره می‌شود؟
- CSV: `results.csv`
- JSON: `results.json`
- Excel: `results.xlsx`

## ۳. اگر config اشتباه باشد چه می‌شود؟
- اعتبارسنجی خودکار انجام می‌شود و در صورت خطا، پیام مناسب نمایش داده می‌شود.

## ۴. چطور داده‌ها نرمال‌سازی می‌شوند؟
- با pipeline داده و تابع `normalize_features` روی همه ویژگی‌های عددی.

## ۵. پاک‌سازی مدل‌های قدیمی چگونه است؟
- قبل از شروع هر اجرا، فقط آخرین ۳ مدل/چک‌پوینت در هر پوشه نگه داشته می‌شود.

## ۶. تست پروژه چگونه انجام می‌شود؟
- با اجرای `pytest` تمام تست‌های واحد و ماژول‌ها بررسی می‌شوند.

## ۷. ساختار پوشه‌ها و ماژول‌ها چگونه است؟
- ماژول‌های اصلی: `utils/`, `data/`, `env/`, `tests/`, `main.py`
- هر بخش (logger, config, export, pipeline, cleanup) ماژول جداگانه دارد.

## ۸. اگر نیاز به افزودن قابلیت جدید باشد؟
- کافیست ماژول جدید را در `utils/` یا `data/` اضافه و تست آن را در `tests/` بنویسید.
