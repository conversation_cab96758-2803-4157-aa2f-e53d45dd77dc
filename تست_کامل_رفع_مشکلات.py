#!/usr/bin/env python3
"""
🧪 تست کامل رفع مشکلات - نسخه پیشرفته
"""

import sys
import os
import traceback

def test_sklearn_fbeta_score():
    """تست رفع sklearn.metrics.fbeta_score"""
    print("🔍 Testing sklearn.fbeta_score fix...")
    try:
        # Import sklearn first
        import sklearn.metrics
        
        # Check if fbeta_score exists
        if hasattr(sklearn.metrics, 'fbeta_score'):
            print("✅ sklearn.metrics.fbeta_score exists")
            
            # Test functionality
            import numpy as np
            y_true = np.array([1, 1, 0, 0, 1, 0])
            y_pred = np.array([1, 0, 1, 0, 1, 1])
            
            try:
                score = sklearn.metrics.fbeta_score(y_true, y_pred, beta=1, average='binary')
                print(f"✅ fbeta_score test successful: {score:.3f}")
                return True
            except Exception as func_error:
                print(f"❌ fbeta_score function failed: {func_error}")
                return False
        else:
            print("❌ sklearn.metrics.fbeta_score does not exist")
            return False
            
    except Exception as e:
        print(f"❌ sklearn test failed: {e}")
        return False

def test_pandas_arrowdtype():
    """تست رفع pandas.ArrowDtype"""
    print("🔍 Testing pandas.ArrowDtype fix...")
    try:
        import pandas as pd
        
        if hasattr(pd, 'ArrowDtype'):
            print("✅ pandas.ArrowDtype exists")
            
            # Test functionality
            try:
                dtype = pd.ArrowDtype()
                print(f"✅ ArrowDtype creation successful: {dtype}")
                return True
            except Exception as func_error:
                print(f"❌ ArrowDtype creation failed: {func_error}")
                return False
        else:
            print("❌ pandas.ArrowDtype does not exist")
            return False
            
    except Exception as e:
        print(f"❌ pandas test failed: {e}")
        return False

def test_environment_variables():
    """تست متغیرهای محیطی"""
    print("🔍 Testing environment variables...")
    
    expected_vars = [
        'AUTOGLUON_DISABLE_FBETA',
        'AUTOGLUON_DISABLE_NEURALNET', 
        'SKLEARN_DISABLE_FBETA'
    ]
    
    all_set = True
    for var in expected_vars:
        if var in os.environ:
            print(f"✅ {var} = {os.environ[var]}")
        else:
            print(f"❌ {var} not set")
            all_set = False
    
    return all_set

def test_genius_indicators_logic():
    """تست منطق Genius Indicators"""
    print("🔍 Testing Genius Indicators validation logic...")
    try:
        import pandas as pd
        import numpy as np
        
        # Create test data with genius indicators
        test_data = pd.DataFrame({
            'close': np.random.randn(100).cumsum() + 100,
            'genius_test1': np.random.randn(100),
            'genius_test2': np.zeros(100),  # All zeros - should be invalid
            'genius_test3': [np.nan] * 100,  # All NaN - should be invalid
            'genius_test4': np.random.randn(100) + 1,  # Valid
        })
        
        # Test validation logic
        genius_columns = [col for col in test_data.columns if col.startswith('genius_')]
        print(f"🔍 Found {len(genius_columns)} genius columns")
        
        valid_indicators = 0
        for col in genius_columns:
            if not test_data[col].isnull().all() and not (test_data[col] == 0).all():
                valid_indicators += 1
                print(f"✅ {col} is valid")
            else:
                print(f"❌ {col} is invalid (all NaN or zeros)")
        
        print(f"🔍 Valid indicators: {valid_indicators}/{len(genius_columns)}")
        
        # Should have 2 valid indicators (test1 and test4)
        if valid_indicators == 2:
            print("✅ Genius indicators validation logic works correctly")
            return True
        else:
            print("❌ Genius indicators validation logic failed")
            return False
            
    except Exception as e:
        print(f"❌ Genius indicators test failed: {e}")
        traceback.print_exc()
        return False

def test_ray_tune_config():
    """تست Ray Tune configuration"""
    print("🔍 Testing Ray Tune configuration...")
    try:
        # Test if ray is available
        import ray
        print("✅ Ray is available")
        
        # Test tune import
        from ray import tune
        print("✅ Ray Tune is available")
        
        # Test initialization parameters
        init_params = {
            'ignore_reinit_error': True,
            'num_cpus': 2,
            'object_store_memory': 500000000,
            '_temp_dir': '/tmp/ray_temp',
            'log_to_driver': False,
            'configure_logging': False
        }
        
        print("✅ Ray Tune configuration parameters ready")
        
        # Test tune.run parameters
        tune_params = {
            'max_failures': 2,
            'time_budget_s': 60,
            'raise_on_failed_trial': False,
            'fail_fast': False,
            'checkpoint_freq': 0,
            'keep_checkpoints_num': 0
        }
        
        print("✅ Ray Tune run parameters ready")
        return True
        
    except ImportError as e:
        print(f"⚠️ Ray Tune not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Ray Tune test failed: {e}")
        return False

def test_cache_cleanup_logic():
    """تست منطق پاک کردن cache"""
    print("🔍 Testing cache cleanup logic...")
    try:
        import tempfile
        import glob
        
        # Create temporary cache directory
        with tempfile.TemporaryDirectory() as temp_dir:
            cache_dir = os.path.join(temp_dir, "genius_indicators")
            os.makedirs(cache_dir, exist_ok=True)
            
            # Create test cache files
            test_files = [
                "genius_test1.pkl",
                "genius_test2.pkl", 
                "other_file.pkl"
            ]
            
            for filename in test_files:
                filepath = os.path.join(cache_dir, filename)
                with open(filepath, 'w') as f:
                    f.write("test")
            
            print(f"✅ Created {len(test_files)} test files")
            
            # Test glob pattern
            pattern = os.path.join(cache_dir, "genius_*.pkl")
            genius_files = glob.glob(pattern)
            
            print(f"🔍 Found {len(genius_files)} genius cache files")
            
            # Should find 2 genius files
            if len(genius_files) == 2:
                print("✅ Cache cleanup glob pattern works correctly")
                return True
            else:
                print("❌ Cache cleanup glob pattern failed")
                return False
                
    except Exception as e:
        print(f"❌ Cache cleanup test failed: {e}")
        return False

def main():
    """اجرای تمام تست‌ها"""
    print("🧪 شروع تست کامل رفع مشکلات")
    print("=" * 60)
    
    tests = [
        ("sklearn.fbeta_score Fix", test_sklearn_fbeta_score),
        ("pandas.ArrowDtype Fix", test_pandas_arrowdtype),
        ("Environment Variables", test_environment_variables),
        ("Genius Indicators Logic", test_genius_indicators_logic),
        ("Ray Tune Configuration", test_ray_tune_config),
        ("Cache Cleanup Logic", test_cache_cleanup_logic)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"📊 Result: {status}")
        except Exception as e:
            print(f"💥 {test_name} crashed: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 نتایج نهایی:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 نتیجه کلی: {passed}/{total} تست موفق ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 تمام مشکلات رفع شده!")
        print("✅ کد آماده اجرای موفق است!")
    elif passed >= total * 0.8:
        print("🎯 اکثر مشکلات رفع شده!")
        print("⚠️ برخی مشکلات جزئی باقی مانده")
    else:
        print("⚠️ برخی مشکلات مهم هنوز باقی مانده")
        print("🔧 نیاز به بررسی بیشتر")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ تست توسط کاربر متوقف شد")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطای غیرمنتظره: {e}")
        traceback.print_exc()
        sys.exit(1)
