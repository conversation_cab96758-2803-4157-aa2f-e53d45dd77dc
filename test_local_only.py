#!/usr/bin/env python3
"""
🧪 تست فقط مدل محلی - بدون API
"""

from simple_financial_models import SimpleFinancialAnalyzer
import time

def test_local_model():
    """تست مدل محلی"""
    print("🧪 تست مدل محلی")
    print("=" * 25)
    
    # راه‌اندازی بدون token (فقط local)
    analyzer = SimpleFinancialAnalyzer(hf_token=None)
    
    # اجبار استفاده از local model
    analyzer.model_type = "LOCAL"
    
    try:
        from transformers import pipeline
        
        print("🔧 لود مدل محلی...")
        analyzer.model = pipeline(
            "sentiment-analysis",
            model="nlptown/bert-base-multilingual-uncased-sentiment",
            device=-1,
            return_all_scores=True
        )
        print("✅ مدل محلی لود شد")
        
        # تست
        test_texts = [
            "Apple stock rises 10% on strong earnings",
            "Market crashes amid economic uncertainty",
            "Stable trading observed today",
            "Tech sector shows strong growth",
            "Banking sector faces challenges"
        ]
        
        print("\n📰 تست تحلیل احساس:")
        results = []
        
        for text in test_texts:
            start_time = time.time()
            result = analyzer.analyze_sentiment(text)
            end_time = time.time()
            
            results.append(result)
            
            if "error" not in result:
                print(f"✅ {result['sentiment'].upper()} ({result['confidence']:.2f}) - {text}")
                print(f"   زمان: {end_time - start_time:.2f}s")
            else:
                print(f"❌ خطا: {result['error']}")
        
        # تست احساس بازار
        print("\n📈 تست احساس بازار:")
        market_sentiment = analyzer.get_market_sentiment(test_texts)
        
        if "error" not in market_sentiment:
            print(f"🎯 حالت کلی: {market_sentiment['overall_sentiment']}")
            print(f"📊 مثبت: {market_sentiment['positive_ratio']:.1%}")
            print(f"📊 منفی: {market_sentiment['negative_ratio']:.1%}")
            print(f"📊 خنثی: {market_sentiment['neutral_ratio']:.1%}")
            print(f"🎲 اطمینان: {market_sentiment['average_confidence']:.2f}")
        
        # تست سیگنال
        signal = analyzer.generate_trading_signal(test_texts, "up")
        print(f"\n🚦 سیگنال: {signal}")
        
        print("\n✅ تست محلی موفق!")
        return True
        
    except Exception as e:
        print(f"❌ خطا: {e}")
        return False

if __name__ == "__main__":
    test_local_model() 