import pytest
import pandas as pd
import numpy as np
import gym
from gym.spaces import Box
import matplotlib.pyplot as plt
from unittest.mock import MagicMock, patch
from evaluation.scenario_backtesting import ScenarioBacktesting


class MockEnv(gym.Env):
    """محیط مصنوعی برای تست"""
    def __init__(self):
        super(MockEnv, self).__init__()
        # ایجاد داده‌های مصنوعی
        dates = pd.date_range(start='2020-01-01', periods=100)
        self.df = pd.DataFrame({
            'open': np.linspace(100, 120, 100) + np.random.normal(0, 1, 100),
            'high': np.linspace(102, 122, 100) + np.random.normal(0, 1, 100),
            'low': np.linspace(98, 118, 100) + np.random.normal(0, 1, 100),
            'close': np.linspace(101, 121, 100) + np.random.normal(0, 1, 100),
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        self.action_space = Box(low=-1, high=1, shape=(1,))
        self.observation_space = Box(low=-np.inf, high=np.inf, shape=(10,))
        self.timeframe = "1h"
        self.symbol = "BTCUSD"
        self.current_step = 0
        
    def reset(self):
        self.current_step = 0
        return np.zeros(10)
        
    def step(self, action):
        self.current_step += 1
        done = self.current_step >= len(self.df) - 1
        reward = float(action[0]) * 0.01  # مقدار ساده برای تست
        return np.zeros(10), reward, done, {"balance": 1000 + self.current_step}


class MockModel:
    """مدل مصنوعی برای تست"""
    def __init__(self):
        self.policy = MagicMock()
        
    def predict(self, obs, deterministic=True):
        # همیشه عمل خرید را پیش‌بینی می‌کند
        return [np.array([0.8])], None


@pytest.fixture
def setup_scenario_backtesting():
    """فیکسچر برای راه‌اندازی ماژول بک‌تست سناریو"""
    env = MockEnv()
    model = MockModel()
    backtester = ScenarioBacktesting(env, model)
    return backtester, env, model


def test_historical_crisis_scenario(setup_scenario_backtesting):
    """تست شبیه‌سازی سناریوی بحران تاریخی"""
    backtester, env, _ = setup_scenario_backtesting
    
    # ایجاد داده‌های بحران مصنوعی
    dates = pd.date_range(start='2008-09-15', periods=30)  # بحران مالی 2008
    crisis_data = pd.DataFrame({
        'open': np.linspace(100, 60, 30) + np.random.normal(0, 2, 30),
        'high': np.linspace(105, 65, 30) + np.random.normal(0, 2, 30),
        'low': np.linspace(95, 55, 30) + np.random.normal(0, 2, 30),
        'close': np.linspace(98, 58, 30) + np.random.normal(0, 2, 30),
        'volume': np.random.randint(5000, 15000, 30)
    }, index=dates)
    
    # پچ کردن تابع calculate_metrics برای جلوگیری از وابستگی‌های خارجی
    with patch('evaluation.scenario_backtesting.calculate_metrics') as mock_metrics:
        mock_metrics.return_value = {
            'win_rate': 40.0,
            'sharpe_ratio': -1.2,
            'max_drawdown': 35.5,
            'daily_drawdown': 2.8,
            'equity_curve': [1000, 950, 900, 850, 800]
        }
        
        # اجرای سناریو با شدت و مدت زمان متفاوت
        result = backtester.historical_crisis_scenario(
            crisis_data=crisis_data,
            name="financial_crisis_2008",
            description="بحران مالی 2008",
            severity=1.5,  # شدت بیشتر
            duration_factor=0.8  # مدت زمان کوتاه‌تر
        )
        
        # بررسی نتایج
        assert result['name'] == "financial_crisis_2008"
        assert result['description'] == "بحران مالی 2008"
        assert 'metrics' in result
        assert result['metrics']['win_rate'] == 40.0
        assert result['metrics']['sharpe_ratio'] == -1.2
        
        # بررسی ذخیره نتایج در backtester
        assert "financial_crisis_2008" in backtester.scenario_results


def test_parameter_sensitivity_analysis(setup_scenario_backtesting):
    """تست تحلیل حساسیت پارامترهای استراتژی"""
    backtester, env, _ = setup_scenario_backtesting
    
    # پچ کردن توابع مورد نیاز
    with patch('evaluation.scenario_backtesting.ScenarioBacktesting._create_market_scenario') as mock_create_scenario:
        mock_create_scenario.return_value = env
        
        with patch('evaluation.scenario_backtesting.ScenarioBacktesting._run_backtest') as mock_run_backtest:
            mock_run_backtest.return_value = {
                'name': 'test_scenario',
                'metrics': {'win_rate': 50.0, 'sharpe_ratio': 1.0}
            }
            
            # اجرای تحلیل حساسیت
            results = backtester.parameter_sensitivity_analysis(
                param_name="learning_rate",
                param_values=[0.001, 0.01, 0.1],
                market_type="all"
            )
            
            # بررسی نتایج
            assert '0.001' in results
            assert '0.01' in results
            assert '0.1' in results
            
            # بررسی انواع بازار
            assert 'bullish' in results['0.001']
            assert 'bearish' in results['0.001']
            assert 'volatile' in results['0.001']
            
            # بررسی تعداد فراخوانی‌های توابع
            assert mock_create_scenario.call_count == 9  # 3 پارامتر × 3 نوع بازار
            assert mock_run_backtest.call_count == 9


def test_execution_delay_slippage_scenario(setup_scenario_backtesting):
    """تست شبیه‌سازی تأخیرهای اجرای سفارش و لغزش قیمت"""
    backtester, env, _ = setup_scenario_backtesting
    
    # پچ کردن توابع مورد نیاز
    with patch('evaluation.scenario_backtesting.ScenarioBacktesting._create_market_scenario') as mock_create_scenario:
        mock_create_scenario.return_value = env
        
        with patch('evaluation.scenario_backtesting.ScenarioBacktesting._run_backtest') as mock_run_backtest:
            mock_run_backtest.return_value = {
                'name': 'test_scenario',
                'metrics': {'win_rate': 45.0, 'sharpe_ratio': 0.8}
            }
            
            # اجرای سناریوی تأخیر و لغزش
            results = backtester.execution_delay_slippage_scenario(
                delay_ms=[0, 500],
                slippage_bps=[0, 5],
                market_conditions=["normal", "volatile"]
            )
            
            # بررسی نتایج
            assert "normal" in results
            assert "volatile" in results
            
            assert "delay0_slip0" in results["normal"]
            assert "delay0_slip5" in results["normal"]
            assert "delay500_slip0" in results["normal"]
            assert "delay500_slip5" in results["normal"]
            
            # بررسی تعداد فراخوانی‌های توابع
            assert mock_create_scenario.call_count == 1  # فقط برای بازار volatile
            assert mock_run_backtest.call_count == 8  # 2 شرایط بازار × 2 تأخیر × 2 لغزش


def test_regime_switching_scenario(setup_scenario_backtesting):
    """تست شبیه‌سازی تغییرات رژیم بازار"""
    backtester, env, _ = setup_scenario_backtesting
    
    # پچ کردن تابع calculate_metrics
    with patch('evaluation.scenario_backtesting.calculate_metrics') as mock_metrics:
        mock_metrics.return_value = {
            'win_rate': 48.0,
            'sharpe_ratio': 0.5,
            'max_drawdown': 15.0,
            'daily_drawdown': 1.2,
            'equity_curve': [1000, 1020, 990, 1030, 1010]
        }
        
        # اجرای سناریوی تغییر رژیم
        result = backtester.regime_switching_scenario(
            num_regimes=2,
            regime_lengths=[50, 50],
            transition_matrix=np.array([[0.9, 0.1], [0.1, 0.9]])
        )
        
        # بررسی نتایج
        assert result['name'] == "regime_switching_2"
        assert 'metrics' in result
        assert result['metrics']['win_rate'] == 48.0
        assert result['metrics']['sharpe_ratio'] == 0.5


def test_compare_scenarios(setup_scenario_backtesting):
    """تست مقایسه نتایج سناریوهای مختلف"""
    backtester, _, _ = setup_scenario_backtesting
    
    # اضافه کردن نتایج مصنوعی به backtester
    backtester.scenario_results = {
        "scenario1": {
            "name": "scenario1",
            "description": "سناریوی اول",
            "metrics": {
                "win_rate": 60.0,
                "sharpe_ratio": 1.5,
                "max_drawdown": 10.0,
                "daily_drawdown": 0.8,
                "equity_curve": [1000, 1050, 1100, 1150, 1200]
            }
        },
        "scenario2": {
            "name": "scenario2",
            "description": "سناریوی دوم",
            "metrics": {
                "win_rate": 40.0,
                "sharpe_ratio": 0.5,
                "max_drawdown": 20.0,
                "daily_drawdown": 1.5,
                "equity_curve": [1000, 950, 980, 930, 960]
            }
        }
    }
    
    # اجرای مقایسه
    comparison = backtester.compare_scenarios()
    
    # بررسی نتایج
    assert len(comparison) == 2
    assert "scenario1" in comparison["scenario"].values
    assert "scenario2" in comparison["scenario"].values
    
    # بررسی مقادیر
    scenario1_row = comparison[comparison["scenario"] == "scenario1"].iloc[0]
    assert scenario1_row["win_rate"] == 60.0
    assert scenario1_row["sharpe_ratio"] == 1.5
    assert scenario1_row["max_drawdown"] == 10.0
    
    scenario2_row = comparison[comparison["scenario"] == "scenario2"].iloc[0]
    assert scenario2_row["win_rate"] == 40.0
    assert scenario2_row["final_balance"] == 960


def test_visualize_scenario_results(setup_scenario_backtesting):
    """تست نمایش گرافیکی نتایج سناریوها"""
    backtester, _, _ = setup_scenario_backtesting
    
    # اضافه کردن نتایج مصنوعی به backtester
    backtester.scenario_results = {
        "scenario1": {
            "name": "scenario1",
            "description": "سناریوی اول",
            "metrics": {
                "win_rate": 60.0,
                "sharpe_ratio": 1.5,
                "max_drawdown": 10.0,
                "daily_drawdown": 0.8,
                "equity_curve": [1000, 1050, 1100, 1150, 1200]
            }
        },
        "scenario2": {
            "name": "scenario2",
            "description": "سناریوی دوم",
            "metrics": {
                "win_rate": 40.0,
                "sharpe_ratio": 0.5,
                "max_drawdown": 20.0,
                "daily_drawdown": 1.5,
                "equity_curve": [1000, 950, 980, 930, 960]
            }
        }
    }
    
    # پچ کردن توابع نمودار برای جلوگیری از نمایش واقعی
    with patch('matplotlib.pyplot.show'), \
         patch('matplotlib.pyplot.subplots') as mock_subplots:
        
        # ایجاد mock برای آبجکت‌های Axes
        mock_ax1 = MagicMock()
        mock_ax2 = MagicMock()
        mock_ax3 = MagicMock()
        mock_ax4 = MagicMock()
        
        # در حالت len(metrics) == 1، باید یک آبجکت Axes برگردانده شود
        mock_subplots.return_value = (MagicMock(), mock_ax1)
        
        # پچ کردن pandas.DataFrame.plot
        with patch('pandas.DataFrame.plot') as mock_plot:
            # اجرای نمایش گرافیکی
            backtester.visualize_scenario_results(
                scenario_names=["scenario1", "scenario2"],
                metrics=["equity_curve"]
            )
            
            # بررسی فراخوانی توابع نمودار
            assert mock_subplots.call_count >= 1
            assert mock_ax1.plot.call_count >= 1  # حداقل یک بار plot فراخوانی شده باشد
            assert mock_ax1.grid.call_count >= 1
            assert mock_ax1.legend.call_count >= 1


def test_create_market_scenario(setup_scenario_backtesting):
    """تست ایجاد سناریوهای مختلف بازار"""
    backtester, _, _ = setup_scenario_backtesting
    
    # تست بازار صعودی
    bullish_env = backtester._create_market_scenario("bullish")
    assert isinstance(bullish_env, MockEnv)
    assert bullish_env.df["close"].iloc[-1] > backtester.base_env.df["close"].iloc[-1]
    
    # تست بازار نزولی
    bearish_env = backtester._create_market_scenario("bearish")
    assert isinstance(bearish_env, MockEnv)
    assert bearish_env.df["close"].iloc[-1] < backtester.base_env.df["close"].iloc[-1]
    
    # تست بازار پر نوسان
    volatile_env = backtester._create_market_scenario("volatile")
    assert isinstance(volatile_env, MockEnv)
    
    # محاسبه نوسان در داده‌های اصلی و بازار پر نوسان
    original_volatility = (backtester.base_env.df["high"] - backtester.base_env.df["low"]).mean()
    new_volatility = (volatile_env.df["high"] - volatile_env.df["low"]).mean()
    
    # بررسی افزایش نوسان
    assert new_volatility > original_volatility


def test_monte_carlo_stress_scenarios(setup_scenario_backtesting):
    """تست تولید خودکار سناریوهای استرس با مدل‌های مونت کارلو"""
    backtester, env, _ = setup_scenario_backtesting
    with patch('evaluation.scenario_backtesting.ScenarioBacktesting._run_backtest') as mock_run_backtest:
        mock_run_backtest.side_effect = lambda scenario_env, name, desc: {"name": name, "metrics": {"win_rate": 50.0}}
        results = backtester.monte_carlo_stress_scenarios(n_scenarios=3, volatility_scale=1.5)
        assert len(results) == 3
        for i in range(1, 4):
            assert f"monte_carlo_stress_{i}" in results
            assert results[f"monte_carlo_stress_{i}"]["metrics"]["win_rate"] == 50.0


def test_multi_market_backtest(setup_scenario_backtesting):
    """تست بک‌تست چندبازاری با همبستگی‌های پویا"""
    backtester, env, _ = setup_scenario_backtesting
    # ایجاد دو بازار مصنوعی
    df1 = env.df.copy()
    df2 = env.df.copy()
    df2['close'] = df2['close'] * 1.1
    market_dfs = {"BTC": df1, "ETH": df2}
    corr = np.array([[1.0, 0.8], [0.8, 1.0]])
    with patch('evaluation.scenario_backtesting.ScenarioBacktesting._run_backtest') as mock_run_backtest:
        mock_run_backtest.side_effect = lambda scenario_env, name, desc: {"name": name, "metrics": {"sharpe_ratio": 1.2}}
        results = backtester.multi_market_backtest(market_dfs, correlation_matrix=corr)
        assert "BTC" in results and "ETH" in results and "multi_market_combined" in results
        assert results["BTC"]["metrics"]["sharpe_ratio"] == 1.2
        assert results["multi_market_combined"]["name"] == "multi_market_combined"


def test_news_event_impact_scenario(setup_scenario_backtesting):
    """تست شبیه‌سازی رویدادهای اخبار مهم و تأثیر آن‌ها"""
    backtester, env, _ = setup_scenario_backtesting
    news_events = [5, 10, 20]
    with patch('evaluation.scenario_backtesting.ScenarioBacktesting._run_backtest') as mock_run_backtest:
        mock_run_backtest.side_effect = lambda scenario_env, name, desc: {"name": name, "metrics": {"max_drawdown": 15.0}}
        results = backtester.news_event_impact_scenario(news_events, impact_scale=0.07)
        assert "news_event_impact" in results
        assert results["news_event_impact"]["metrics"]["max_drawdown"] == 15.0


def test_worst_case_robust_optimization(setup_scenario_backtesting):
    """تست تحلیل سناریوهای بدترین حالت با روش‌های بهینه‌سازی مقاوم"""
    backtester, env, _ = setup_scenario_backtesting
    with patch('evaluation.scenario_backtesting.ScenarioBacktesting._run_backtest') as mock_run_backtest:
        mock_run_backtest.side_effect = lambda scenario_env, name, desc: {"name": name, "metrics": {"final_balance": 900}}
        results = backtester.worst_case_robust_optimization(n_scenarios=2, perturbation_scale=0.2)
        assert len(results) == 2
        for i in range(1, 3):
            assert f"worst_case_{i}" in results
            assert results[f"worst_case_{i}"]["metrics"]["final_balance"] == 900


def test_liquidity_shock_scenario(setup_scenario_backtesting):
    """تست شبیه‌سازی شوک‌های نقدینگی و تغییرات ناگهانی در عمق بازار"""
    backtester, env, _ = setup_scenario_backtesting
    shock_indices = [3, 15, 40]
    with patch('evaluation.scenario_backtesting.ScenarioBacktesting._run_backtest') as mock_run_backtest:
        mock_run_backtest.side_effect = lambda scenario_env, name, desc: {"name": name, "metrics": {"max_drawdown": 22.0}}
        results = backtester.liquidity_shock_scenario(shock_indices, shock_scale=0.18)
        assert "liquidity_shock" in results
        assert results["liquidity_shock"]["metrics"]["max_drawdown"] == 22.0


def test_multi_agent_behavior_backtest(setup_scenario_backtesting):
    """تست بک‌تست چندعاملی با شبیه‌سازی رفتار سایر معامله‌گران"""
    backtester, env, _ = setup_scenario_backtesting
    agent_behaviors = [
        {"trend": 1.05, "volatility": 1.2},
        {"trend": 0.95, "volatility": 1.5}
    ]
    agent_weights = [0.6, 0.4]
    with patch('evaluation.scenario_backtesting.ScenarioBacktesting._run_backtest') as mock_run_backtest:
        mock_run_backtest.side_effect = lambda scenario_env, name, desc: {"name": name, "metrics": {"sharpe_ratio": 0.9}}
        results = backtester.multi_agent_behavior_backtest(agent_behaviors, agent_weights)
        assert "multi_agent_behavior" in results
        assert results["multi_agent_behavior"]["metrics"]["sharpe_ratio"] == 0.9


def test_market_manipulation_scenario(setup_scenario_backtesting):
    """تست شبیه‌سازی دستکاری بازار و حرکات قیمتی غیرطبیعی"""
    backtester, env, _ = setup_scenario_backtesting
    manipulation_indices = [7, 25, 60]
    with patch('evaluation.scenario_backtesting.ScenarioBacktesting._run_backtest') as mock_run_backtest:
        mock_run_backtest.side_effect = lambda scenario_env, name, desc: {"name": name, "metrics": {"win_rate": 33.0}}
        results = backtester.market_manipulation_scenario(manipulation_indices, manipulation_scale=0.22)
        assert "market_manipulation" in results
        assert results["market_manipulation"]["metrics"]["win_rate"] == 33.0


def test_whale_behavior_scenario(setup_scenario_backtesting):
    """تست شبیه‌سازی رفتار معامله‌گران بزرگ (نهنگ‌ها)"""
    backtester, env, _ = setup_scenario_backtesting
    whale_indices = [10, 30, 70]
    with patch('evaluation.scenario_backtesting.ScenarioBacktesting._run_backtest') as mock_run_backtest:
        mock_run_backtest.side_effect = lambda scenario_env, name, desc: {"name": name, "metrics": {"final_balance": 1200}}
        results = backtester.whale_behavior_scenario(whale_indices, whale_impact=0.3)
        assert "whale_behavior" in results
        assert results["whale_behavior"]["metrics"]["final_balance"] == 1200 