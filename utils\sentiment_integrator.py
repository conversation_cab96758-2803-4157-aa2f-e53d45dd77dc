import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Union, Any
import datetime

from .sentiment_analyzer import SentimentAnalyzer
from .source_credibility import SourceCredibility
from .news_volume_analyzer import NewsVolumeAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SentimentIntegrator:
    """
    A class that integrates sentiment signals with technical and fundamental factors
    to generate combined trading signals.
    
    This integrator combines:
    1. Sentiment analysis from news and social media
    2. News volume shock detection
    3. Technical indicators
    4. Fundamental data
    
    It uses configurable weights to balance the influence of each factor.
    """
    
    def __init__(
        self,
        sentiment_analyzer: Optional[SentimentAnalyzer] = None,
        volume_analyzer: Optional[NewsVolumeAnalyzer] = None,
        weights: Optional[Dict[str, float]] = None
    ):
        """
        Initialize the SentimentIntegrator.
        
        Args:
            sentiment_analyzer (SentimentAnalyzer, optional): Instance of SentimentAnalyzer.
                If None, a new instance will be created.
            volume_analyzer (NewsVolumeAnalyzer, optional): Instance of NewsVolumeAnalyzer.
                If None, a new instance will be created.
            weights (Dict[str, float], optional): Weights for different signal components.
                If None, default weights will be used.
        """
        # Initialize analyzers
        self.sentiment_analyzer = sentiment_analyzer or SentimentAnalyzer()
        self.volume_analyzer = volume_analyzer or NewsVolumeAnalyzer()
        
        # Set default weights if not provided
        self.weights = weights or {
            'sentiment': 0.3,           # Weight for sentiment signals
            'volume_shock': 0.15,       # Weight for news volume shock
            'technical': 0.35,          # Weight for technical indicators
            'fundamental': 0.2,         # Weight for fundamental data
            
            # Sub-weights for technical indicators
            'tech_trend': 0.4,          # Weight for trend indicators (e.g., moving averages)
            'tech_momentum': 0.3,       # Weight for momentum indicators (e.g., RSI, MACD)
            'tech_volatility': 0.2,     # Weight for volatility indicators (e.g., Bollinger Bands)
            'tech_volume': 0.1,         # Weight for volume indicators
            
            # Sub-weights for fundamental factors
            'fund_valuation': 0.5,      # Weight for valuation metrics (e.g., P/E, P/B)
            'fund_growth': 0.3,         # Weight for growth metrics (e.g., revenue growth)
            'fund_quality': 0.2,        # Weight for quality metrics (e.g., ROE, debt/equity)
        }
        
        # Normalize weights to ensure they sum to 1.0
        self._normalize_weights()
        
        logger.info("SentimentIntegrator initialized with weights: " + 
                   ", ".join([f"{k}: {v:.2f}" for k, v in self.weights.items() 
                             if k in ['sentiment', 'volume_shock', 'technical', 'fundamental']]))
    
    def _normalize_weights(self) -> None:
        """Normalize the main weights to ensure they sum to 1.0."""
        main_weights = ['sentiment', 'volume_shock', 'technical', 'fundamental']
        total = sum(self.weights[w] for w in main_weights)
        
        if abs(total - 1.0) > 0.001:  # If weights don't sum to 1 (within a small tolerance)
            for w in main_weights:
                self.weights[w] /= total
    
    def set_weights(self, new_weights: Dict[str, float]) -> None:
        """
        Update the weights used for signal integration.
        
        Args:
            new_weights (Dict[str, float]): New weights to use.
        """
        self.weights.update(new_weights)
        self._normalize_weights()
        logger.info("Updated weights: " + 
                   ", ".join([f"{k}: {v:.2f}" for k, v in self.weights.items() 
                             if k in ['sentiment', 'volume_shock', 'technical', 'fundamental']]))
    
    def process_news(
        self, 
        asset: str, 
        news_text: str, 
        source: Optional[str] = None,
        timestamp: Optional[datetime.datetime] = None
    ) -> Dict[str, Any]:
        """
        Process a single news item and update internal state.
        
        Args:
            asset (str): The asset or topic identifier
            news_text (str): The text content of the news
            source (str, optional): The source of the news
            timestamp (datetime, optional): Timestamp of the news. If None, uses current time.
            
        Returns:
            Dict[str, Any]: Information about the processed news
        """
        if timestamp is None:
            timestamp = datetime.datetime.now()
        
        # Analyze sentiment
        sentiment_result = self.sentiment_analyzer.analyze(news_text, source=source)
        
        # Add to volume analyzer
        self.volume_analyzer.add_news(asset, timestamp)
        
        return {
            'asset': asset,
            'timestamp': timestamp,
            'source': source,
            'sentiment': sentiment_result,
            'processed': True
        }
    
    def get_sentiment_signal(
        self, 
        asset: str, 
        lookback_hours: int = 24,
        current_time: Optional[datetime.datetime] = None
    ) -> float:
        """
        Calculate a normalized sentiment signal for an asset based on recent news.
        
        Args:
            asset (str): The asset or topic identifier
            lookback_hours (int): Hours to look back for sentiment analysis
            current_time (datetime, optional): Current timestamp for comparison. If None, uses current time.
            
        Returns:
            float: Sentiment signal between -1.0 (very bearish) and 1.0 (very bullish)
        """
        # This is a placeholder. In a real implementation, you would:
        # 1. Retrieve all sentiment scores for the asset within the lookback period
        # 2. Apply time decay (more recent news has higher weight)
        # 3. Calculate a weighted average
        
        # For now, we'll return a random sentiment score for demonstration
        return np.random.uniform(-0.8, 0.8)
    
    def get_volume_shock_signal(
        self, 
        asset: str,
        current_time: Optional[datetime.datetime] = None
    ) -> float:
        """
        Calculate a signal based on news volume shock detection.
        
        Args:
            asset (str): The asset or topic identifier
            current_time (datetime, optional): Current timestamp for comparison. If None, uses current time.
            
        Returns:
            float: Volume shock signal between 0.0 (no shock) and 1.0 (significant shock)
        """
        is_spike, metrics = self.volume_analyzer.detect_volume_spike(asset, current_time)
        
        if not is_spike:
            return 0.0
        
        # Calculate a normalized shock score based on the ratio and volume
        ratio = metrics.get('ratio', 0.0)
        short_volume = metrics.get('short_volume', 0)
        
        # Normalize ratio: 0.0 for ratio <= threshold, 1.0 for ratio >= 3*threshold
        threshold = self.volume_analyzer.threshold_ratio
        normalized_ratio = min(1.0, max(0.0, (ratio - threshold) / (2 * threshold)))
        
        # Normalize volume: 0.0 for min_count, 1.0 for 5*min_count
        min_count = self.volume_analyzer.min_news_count
        normalized_volume = min(1.0, max(0.0, (short_volume - min_count) / (4 * min_count)))
        
        # Combine normalized ratio and volume (giving more weight to ratio)
        return 0.7 * normalized_ratio + 0.3 * normalized_volume
    
    def get_technical_signal(
        self, 
        asset: str,
        technical_data: Dict[str, Any]
    ) -> float:
        """
        Calculate a signal based on technical indicators.
        
        Args:
            asset (str): The asset or topic identifier
            technical_data (Dict[str, Any]): Dictionary containing technical indicators
            
        Returns:
            float: Technical signal between -1.0 (very bearish) and 1.0 (very bullish)
        """
        # This is a placeholder. In a real implementation, you would:
        # 1. Process the technical indicators
        # 2. Apply the sub-weights to calculate a weighted signal
        
        # For demonstration, we'll assume technical_data contains normalized signals
        trend_signal = technical_data.get('trend', 0.0)
        momentum_signal = technical_data.get('momentum', 0.0)
        volatility_signal = technical_data.get('volatility', 0.0)
        volume_signal = technical_data.get('volume', 0.0)
        
        # Apply sub-weights
        weighted_signal = (
            self.weights['tech_trend'] * trend_signal +
            self.weights['tech_momentum'] * momentum_signal +
            self.weights['tech_volatility'] * volatility_signal +
            self.weights['tech_volume'] * volume_signal
        )
        
        return weighted_signal
    
    def get_fundamental_signal(
        self, 
        asset: str,
        fundamental_data: Dict[str, Any]
    ) -> float:
        """
        Calculate a signal based on fundamental data.
        
        Args:
            asset (str): The asset or topic identifier
            fundamental_data (Dict[str, Any]): Dictionary containing fundamental metrics
            
        Returns:
            float: Fundamental signal between -1.0 (very bearish) and 1.0 (very bullish)
        """
        # This is a placeholder. In a real implementation, you would:
        # 1. Process the fundamental metrics
        # 2. Apply the sub-weights to calculate a weighted signal
        
        # For demonstration, we'll assume fundamental_data contains normalized signals
        valuation_signal = fundamental_data.get('valuation', 0.0)
        growth_signal = fundamental_data.get('growth', 0.0)
        quality_signal = fundamental_data.get('quality', 0.0)
        
        # Apply sub-weights
        weighted_signal = (
            self.weights['fund_valuation'] * valuation_signal +
            self.weights['fund_growth'] * growth_signal +
            self.weights['fund_quality'] * quality_signal
        )
        
        return weighted_signal
    
    def get_integrated_signal(
        self, 
        asset: str,
        technical_data: Optional[Dict[str, Any]] = None,
        fundamental_data: Optional[Dict[str, Any]] = None,
        lookback_hours: int = 24,
        current_time: Optional[datetime.datetime] = None
    ) -> Dict[str, Any]:
        """
        Generate an integrated trading signal combining sentiment, technical, and fundamental factors.
        
        Args:
            asset (str): The asset or topic identifier
            technical_data (Dict[str, Any], optional): Dictionary containing technical indicators
            fundamental_data (Dict[str, Any], optional): Dictionary containing fundamental metrics
            lookback_hours (int): Hours to look back for sentiment analysis
            current_time (datetime, optional): Current timestamp for comparison. If None, uses current time.
            
        Returns:
            Dict[str, Any]: Integrated signal information including:
                - overall_signal: Float between -1.0 (very bearish) and 1.0 (very bullish)
                - component_signals: Dict of individual component signals
                - confidence: Float between 0.0 and 1.0 indicating signal confidence
        """
        # Get sentiment signal
        sentiment_signal = self.get_sentiment_signal(asset, lookback_hours, current_time)
        
        # Get volume shock signal
        volume_shock_signal = self.get_volume_shock_signal(asset, current_time)
        
        # Get technical signal (if data provided)
        technical_signal = 0.0
        if technical_data:
            technical_signal = self.get_technical_signal(asset, technical_data)
        
        # Get fundamental signal (if data provided)
        fundamental_signal = 0.0
        if fundamental_data:
            fundamental_signal = self.get_fundamental_signal(asset, fundamental_data)
        
        # Calculate weighted overall signal
        overall_signal = (
            self.weights['sentiment'] * sentiment_signal +
            self.weights['volume_shock'] * volume_shock_signal +
            self.weights['technical'] * technical_signal +
            self.weights['fundamental'] * fundamental_signal
        )
        
        # Calculate signal confidence based on agreement between components
        # Higher confidence when components agree in direction and magnitude
        components = [
            (sentiment_signal, self.weights['sentiment']),
            (volume_shock_signal, self.weights['volume_shock']),
            (technical_signal, self.weights['technical']),
            (fundamental_signal, self.weights['fundamental'])
        ]
        
        # Filter out components with zero weight
        components = [(signal, weight) for signal, weight in components if weight > 0]
        
        # Calculate weighted variance of signals
        if len(components) > 1:
            signals = [signal for signal, _ in components]
            weights = [weight for _, weight in components]
            weighted_variance = np.average((signals - np.average(signals, weights=weights))**2, weights=weights)
            
            # Convert variance to confidence (higher variance = lower confidence)
            # Normalize to [0, 1] range where 1 is high confidence (low variance)
            confidence = max(0.0, min(1.0, 1.0 - weighted_variance))
        else:
            confidence = 0.5  # Default confidence if only one component
        
        return {
            'asset': asset,
            'timestamp': current_time or datetime.datetime.now(),
            'overall_signal': overall_signal,
            'component_signals': {
                'sentiment': sentiment_signal,
                'volume_shock': volume_shock_signal,
                'technical': technical_signal,
                'fundamental': fundamental_signal
            },
            'confidence': confidence,
            'weights': {k: v for k, v in self.weights.items() 
                       if k in ['sentiment', 'volume_shock', 'technical', 'fundamental']}
        }

if __name__ == "__main__":
    # Example usage
    integrator = SentimentIntegrator()
    
    # Process some news
    integrator.process_news(
        asset="AAPL",
        news_text="Apple reports record quarterly earnings, exceeding analyst expectations.",
        source="Reuters"
    )
    
    integrator.process_news(
        asset="AAPL",
        news_text="Apple's new product launch receives mixed reviews from tech critics.",
        source="TechCrunch"
    )
    
    # Example technical data (normalized between -1 and 1)
    technical_data = {
        'trend': 0.6,        # Positive trend
        'momentum': 0.3,     # Moderate positive momentum
        'volatility': -0.2,  # Slightly decreasing volatility
        'volume': 0.1        # Slightly above average volume
    }
    
    # Example fundamental data (normalized between -1 and 1)
    fundamental_data = {
        'valuation': -0.4,   # Slightly overvalued
        'growth': 0.7,       # Strong growth
        'quality': 0.5       # Good quality metrics
    }
    
    # Get integrated signal
    signal = integrator.get_integrated_signal(
        asset="AAPL",
        technical_data=technical_data,
        fundamental_data=fundamental_data
    )
    
    print(f"Integrated signal for AAPL: {signal['overall_signal']:.2f}")
    print(f"Confidence: {signal['confidence']:.2f}")
    print("Component signals:")
    for component, value in signal['component_signals'].items():
        print(f"  {component}: {value:.2f}")
    
    # Try with different weights
    integrator.set_weights({
        'sentiment': 0.5,    # Increased weight for sentiment
        'volume_shock': 0.1,
        'technical': 0.3,
        'fundamental': 0.1
    })
    
    # Get new signal with updated weights
    new_signal = integrator.get_integrated_signal(
        asset="AAPL",
        technical_data=technical_data,
        fundamental_data=fundamental_data
    )
    
    print("\nAfter weight adjustment:")
    print(f"Integrated signal for AAPL: {new_signal['overall_signal']:.2f}")
    print(f"Confidence: {new_signal['confidence']:.2f}")
    print("Component signals:")
    for component, value in new_signal['component_signals'].items():
        print(f"  {component}: {value:.2f}") 