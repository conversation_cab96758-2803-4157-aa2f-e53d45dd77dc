#!/usr/bin/env python3
"""
📦 نصب آسان سیستم مدل‌های واقعی HuggingFace
"""

import subprocess
import sys
import os

def install_package(package):
    """نصب پکیج"""
    subprocess.check_call([sys.executable, "-m", "pip", "install", package])

def main():
    print("🚀 نصب سیستم مدل‌های واقعی HuggingFace")
    print("=" * 50)
    
    # لیست پکیج‌ها
    packages = [
        "requests>=2.25.1",
        "numpy>=1.21.0", 
        "transformers>=4.21.0",
        "torch>=1.12.0",
        "datasets>=2.0.0",
        "tokenizers>=0.13.0",
        "huggingface-hub>=0.10.0",
        "accelerate>=0.12.0",
        "sentencepiece>=0.1.97",
        "protobuf>=3.20.0"
    ]
    
    print("📦 نصب پکیج‌های مورد نیاز...")
    
    for package in packages:
        try:
            print(f"  نصب {package}...")
            install_package(package)
            print(f"  ✅ {package} نصب شد")
        except Exception as e:
            print(f"  ❌ خطا در نصب {package}: {e}")
    
    print("\n🎯 ایجاد فایل پیکربندی...")
    
    # ایجاد فایل config
    config_content = """
# پیکربندی مدل‌های HuggingFace
HF_TOKEN=your_huggingface_token_here
DEFAULT_MODEL_SIZE=small
USE_PROXY=true
PROXY_PORT=10809
"""
    
    with open("hf_config.txt", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("  ✅ hf_config.txt ایجاد شد")
    
    print("\n🧪 تست سیستم...")
    
    try:
        # تست import
        exec(open("real_huggingface_models.py").read())
        print("  ✅ سیستم با موفقیت لود شد")
    except Exception as e:
        print(f"  ❌ خطا در تست: {e}")
        
    print("\n" + "=" * 50)
    print("🎉 نصب کامل شد!")
    print("\n📖 راهنمای استفاده:")
    print("1. توکن HuggingFace خود را در hf_config.txt قرار دهید")
    print("2. python real_huggingface_models.py را اجرا کنید")
    print("3. از تمام مدل‌های واقعی استفاده کنید!")

if __name__ == "__main__":
    main() 