<Conditions>
      <Condition use="true">
        <Left-Side valueType="column">
          <Column-Value column="AvgProfitPerDay" columnType="0" format="Decimal2PL" resultType="main" direction="0" sampleType="127" plType="10" confidenceLevel="50" market="undefined" subresult="30" pctRatio="0" class="AvgProfitPerDay"/>
        </Left-Side>
        <Comparator value="&gt;="/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="10"/>
        </Right-Side>
      </Condition>
      <Condition use="true">
        <Left-Side valueType="column">
          <Column-Value column="ProfitFactor" columnType="0" format="Decimal2" resultType="main" direction="0" sampleType="127" plType="10" confidenceLevel="50" market="1" subresult="30" pctRatio="0" class="ProfitFactor"/>
        </Left-Side>
        <Comparator value="&gt;="/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="1.5"/>
        </Right-Side>
      </Condition>
      <Condition use="true">
        <Left-Side valueType="column">
          <Column-Value column="ReturnDDRatio" columnType="0" format="Decimal2" resultType="main" direction="0" sampleType="127" plType="10" confidenceLevel="50" market="1" subresult="30" pctRatio="0" class="ReturnDDRatio"/>
        </Left-Side>
        <Comparator value="&gt;="/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="1.5"/>
        </Right-Side>
      </Condition>
      <Condition use="true">
        <Left-Side valueType="column">
          <Column-Value column="MaxIntradayDrawdown" columnType="0" format="Decimal2Pct" resultType="main" direction="0" sampleType="127" plType="10" confidenceLevel="50" market="1" subresult="30" pctRatio="0" class="MaxIntradayDrawdown"/>
        </Left-Side>
        <Comparator value="&lt;="/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="4"/>
        </Right-Side>
      </Condition>
      <Condition use="true">
        <Left-Side valueType="column">
          <Column-Value column="Drawdown" format="Decimal2PL" resultType="main" subresult="30" direction="0" sampleType="10" plType="10" confidenceLevel="50" market="1" class="Drawdown"/>
        </Left-Side>
        <Comparator value="&lt;="/>
        <Right-Side valueType="column">
          <Column-Value column="AvgDrawdown" columnType="0" format="Decimal2PL" resultType="main" direction="0" sampleType="127" plType="10" confidenceLevel="50" market="1" subresult="30" pctRatio="10" class="AvgDrawdown"/>
        </Right-Side>
      </Condition>
      <Condition use="true">
        <Left-Side valueType="column">
          <Column-Value column="SharpeRatio" format="Decimal2" resultType="main" subresult="30" direction="0" sampleType="10" plType="10" confidenceLevel="50" market="1" class="SharpeRatio"/>
        </Left-Side>
        <Comparator value="&gt;="/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="0.80"/>
        </Right-Side>
      </Condition>
      <Condition use="true">
        <Left-Side valueType="column">
          <Column-Value column="AmbiguousTrades" format="Integer" resultType="main" subresult="30" direction="0" sampleType="10" plType="10" confidenceLevel="50" market="1" class="AmbiguousTrades"/>
        </Left-Side>
        <Comparator value="&gt;"/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="0"/>
        </Right-Side>
      </Condition>
      <Condition use="false">
        <Left-Side valueType="column">
          <Column-Value column="AnnualPctReturn" format="Decimal2Pct" resultType="main" subresult="30" direction="0" sampleType="10" plType="10" confidenceLevel="50" market="1" class="AnnualPctReturn"/>
        </Left-Side>
        <Comparator value="&gt;="/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="30"/>
        </Right-Side>
      </Condition>
      <Condition use="false">
        <Left-Side valueType="column">
          <Column-Value column="NetProfit" format="Decimal2PL" resultType="main" subresult="30" direction="0" sampleType="10" plType="10" confidenceLevel="50" market="1" class="NetProfit"/>
        </Left-Side>
        <Comparator value="&gt;"/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="0"/>
        </Right-Side>
      </Condition>
      <Condition use="true">
        <Left-Side valueType="column">
          <Column-Value column="AvgWin" format="Decimal2PL" resultType="main" subresult="30" direction="0" sampleType="10" plType="10" confidenceLevel="50" market="1" class="AvgWin"/>
        </Left-Side>
        <Comparator value="&gt;"/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="50"/>
        </Right-Side>
      </Condition>
      <Condition use="true">
        <Left-Side valueType="column">
          <Column-Value column="MaxConsecLosses" format="Integer" resultType="main" subresult="30" direction="0" sampleType="10" plType="10" confidenceLevel="50" market="1" class="MaxConsecLosses"/>
        </Left-Side>
        <Comparator value="&lt;"/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="5"/>
        </Right-Side>
      </Condition>
      <Condition use="false">
        <Left-Side valueType="column">
          <Column-Value column="TotalTradingDays" format="Integer" resultType="main" subresult="30" direction="0" sampleType="10" plType="10" confidenceLevel="50" market="1" class="TotalTradingDays"/>
        </Left-Side>
        <Comparator value="&gt;="/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="3"/>
        </Right-Side>
      </Condition>
      <Condition use="true">
        <Left-Side valueType="column">
          <Column-Value column="MaxNewHighDuration" format="Integer" resultType="main" subresult="30" direction="0" sampleType="10" plType="10" confidenceLevel="50" market="1" class="MaxNewHighDuration"/>
        </Left-Side>
        <Comparator value="&gt;="/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="30"/>
        </Right-Side>
      </Condition>
      <Condition use="true">
        <Left-Side valueType="column">
          <Column-Value column="MaxLoss" format="Decimal2PL" resultType="main" subresult="30" direction="0" sampleType="10" plType="10" confidenceLevel="50" market="1" class="MaxLoss"/>
        </Left-Side>
        <Comparator value="&gt;"/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="100"/>
        </Right-Side>
      </Condition>
      <Condition use="false">
        <Left-Side valueType="column">
          <Column-Value column="LongestTrade" format="Integer" resultType="main" subresult="30" direction="0" sampleType="10" plType="10" confidenceLevel="50" market="1" class="LongestTrade"/>
        </Left-Side>
        <Comparator value="&lt;="/>
        <Right-Side valueType="numeric">
          <Numeric-Value value="2"/>
        </Right-Side>
      </Condition>
    </Conditions>