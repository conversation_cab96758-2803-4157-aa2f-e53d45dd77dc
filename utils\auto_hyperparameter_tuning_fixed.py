"""
Auto-Hyperparameter Tuning System (Fixed)
سیستم بهینه‌سازی خودکار پارامترها
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, asdict
import random

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class HyperparameterSpace:
    name: str
    param_type: str
    bounds: Tuple[float, float]
    choices: List[Any]

@dataclass
class OptimizationResult:
    best_params: Dict
    best_score: float
    optimization_history: List[Dict]
    total_evaluations: int
    convergence_info: Dict

class SimpleBayesianOptimizer:
    """بهینه‌ساز ساده برای پارامترها"""
    
    def __init__(self, objective_function: Callable, hyperparameter_spaces: List[HyperparameterSpace]):
        self.objective_function = objective_function
        self.hyperparameter_spaces = hyperparameter_spaces
        self.optimization_history = []
        
        logger.info(f"Simple Optimizer initialized with {len(hyperparameter_spaces)} parameters")
    
    def _generate_random_params(self) -> Dict:
        """تولید پارامترهای تصادفی"""
        params = {}
        
        for space in self.hyperparameter_spaces:
            if space.param_type == 'continuous':
                value = random.uniform(space.bounds[0], space.bounds[1])
                params[space.name] = value
                
            elif space.param_type == 'discrete':
                value = random.choice(space.choices)
                params[space.name] = value
                
            elif space.param_type == 'categorical':
                value = random.choice(space.choices)
                params[space.name] = value
        
        return params
    
    def _mutate_params(self, base_params: Dict, mutation_rate: float = 0.3) -> Dict:
        """جهش پارامترها بر اساس پارامترهای خوب"""
        params = base_params.copy()
        
        for space in self.hyperparameter_spaces:
            if random.random() < mutation_rate:
                if space.param_type == 'continuous':
                    current_value = params[space.name]
                    # Small mutation around current value
                    range_size = space.bounds[1] - space.bounds[0]
                    mutation = random.gauss(0, range_size * 0.1)
                    new_value = current_value + mutation
                    new_value = max(space.bounds[0], min(space.bounds[1], new_value))
                    params[space.name] = new_value
                    
                elif space.param_type in ['discrete', 'categorical']:
                    params[space.name] = random.choice(space.choices)
        
        return params
    
    def optimize(self, n_calls: int = 30, n_random_starts: int = 5) -> OptimizationResult:
        """بهینه‌سازی پارامترها"""
        logger.info(f"Starting optimization with {n_calls} evaluations")
        
        best_params = None
        best_score = -float('inf')
        
        # Random initialization
        for i in range(n_random_starts):
            params = self._generate_random_params()
            score = self.objective_function(params)
            
            if score > best_score:
                best_score = score
                best_params = params
            
            self.optimization_history.append({
                'iteration': i,
                'params': params,
                'score': score,
                'type': 'random'
            })
            
            logger.info(f"Random init {i+1}/{n_random_starts}: score={score:.6f}")
        
        # Guided search
        for i in range(n_random_starts, n_calls):
            # 70% chance to mutate best params, 30% random
            if random.random() < 0.7 and best_params:
                params = self._mutate_params(best_params)
            else:
                params = self._generate_random_params()
            
            score = self.objective_function(params)
            
            if score > best_score:
                best_score = score
                best_params = params
            
            self.optimization_history.append({
                'iteration': i,
                'params': params,
                'score': score,
                'type': 'guided'
            })
            
            logger.info(f"Guided iter {i+1}/{n_calls}: score={score:.6f}")
        
        # Convergence info
        convergence_info = {
            'converged': self._check_convergence(),
            'improvement_rate': self._calculate_improvement_rate()
        }
        
        result = OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_history=self.optimization_history,
            total_evaluations=len(self.optimization_history),
            convergence_info=convergence_info
        )
        
        logger.info(f"Optimization completed. Best score: {best_score:.6f}")
        return result
    
    def _check_convergence(self, window_size: int = 5) -> bool:
        """بررسی همگرایی"""
        if len(self.optimization_history) < window_size * 2:
            return False
        
        recent_scores = [entry['score'] for entry in self.optimization_history[-window_size:]]
        previous_scores = [entry['score'] for entry in self.optimization_history[-window_size*2:-window_size]]
        
        recent_mean = np.mean(recent_scores)
        previous_mean = np.mean(previous_scores)
        
        improvement = abs(recent_mean - previous_mean) / abs(previous_mean)
        return improvement < 0.01
    
    def _calculate_improvement_rate(self) -> float:
        """محاسبه نرخ بهبود"""
        if len(self.optimization_history) < 2:
            return 0.0
        
        initial_best = max([entry['score'] for entry in self.optimization_history[:5]])
        final_best = max([entry['score'] for entry in self.optimization_history])
        
        return (final_best - initial_best) / abs(initial_best)

class AutoHyperparameterTuner:
    """سیستم تنظیم خودکار پارامترها"""
    
    def __init__(self, db_path: str = "hyperparameter_tuning.db"):
        self.db_path = db_path
        self._init_database()
        logger.info("Auto-Hyperparameter Tuner initialized")
    
    def _init_database(self):
        """راه‌اندازی پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS optimization_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                model_name TEXT,
                symbol TEXT,
                timeframe TEXT,
                best_params TEXT,
                best_score REAL,
                total_evaluations INTEGER,
                convergence_info TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS optimization_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                optimization_id INTEGER,
                iteration INTEGER,
                params TEXT,
                score REAL,
                optimization_type TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def define_rl_hyperparameters(self) -> List[HyperparameterSpace]:
        """تعریف فضای جستجوی پارامترهای RL"""
        return [
            HyperparameterSpace("learning_rate", "continuous", (0.001, 0.1), []),
            HyperparameterSpace("discount_factor", "continuous", (0.9, 0.99), []),
            HyperparameterSpace("epsilon_decay", "continuous", (0.99, 0.999), []),
            HyperparameterSpace("epsilon_min", "continuous", (0.01, 0.1), []),
            HyperparameterSpace("batch_size", "discrete", (0, 0), [16, 32, 64, 128]),
        ]
    
    def define_prediction_hyperparameters(self) -> List[HyperparameterSpace]:
        """تعریف فضای جستجوی پارامترهای پیش‌بینی"""
        return [
            HyperparameterSpace("n_estimators", "discrete", (0, 0), [50, 100, 200, 300]),
            HyperparameterSpace("max_depth", "discrete", (0, 0), [5, 8, 10, 15, 20]),
            HyperparameterSpace("min_samples_split", "discrete", (0, 0), [2, 5, 10]),
            HyperparameterSpace("min_samples_leaf", "discrete", (0, 0), [1, 2, 4]),
            HyperparameterSpace("max_features", "categorical", (0, 0), ["sqrt", "log2", "auto"]),
        ]
    
    def optimize_rl_parameters(self, symbol: str, timeframe: str, 
                             evaluation_function: Callable) -> OptimizationResult:
        """بهینه‌سازی پارامترهای RL"""
        logger.info(f"Optimizing RL parameters for {symbol} {timeframe}")
        
        hyperparameter_spaces = self.define_rl_hyperparameters()
        optimizer = SimpleBayesianOptimizer(evaluation_function, hyperparameter_spaces)
        
        result = optimizer.optimize(n_calls=20, n_random_starts=5)
        
        self._save_optimization_result("RL_Agent", symbol, timeframe, result)
        return result
    
    def optimize_prediction_parameters(self, symbol: str, timeframe: str,
                                     evaluation_function: Callable) -> OptimizationResult:
        """بهینه‌سازی پارامترهای پیش‌بینی"""
        logger.info(f"Optimizing prediction parameters for {symbol} {timeframe}")
        
        hyperparameter_spaces = self.define_prediction_hyperparameters()
        optimizer = SimpleBayesianOptimizer(evaluation_function, hyperparameter_spaces)
        
        result = optimizer.optimize(n_calls=15, n_random_starts=3)
        
        self._save_optimization_result("Prediction_Model", symbol, timeframe, result)
        return result
    
    def _save_optimization_result(self, model_name: str, symbol: str, timeframe: str, 
                                result: OptimizationResult):
        """ذخیره نتایج بهینه‌سازی"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Convert all values to JSON-serializable types
        best_params_clean = {}
        for key, value in result.best_params.items():
            if isinstance(value, (np.integer, np.floating)):
                best_params_clean[key] = value.item()
            else:
                best_params_clean[key] = value
        
        cursor.execute('''
            INSERT INTO optimization_results 
            (timestamp, model_name, symbol, timeframe, best_params, best_score, 
             total_evaluations, convergence_info)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            datetime.now().isoformat(),
            model_name,
            symbol,
            timeframe,
            json.dumps(best_params_clean),
            float(result.best_score),
            result.total_evaluations,
            json.dumps(result.convergence_info)
        ))
        
        optimization_id = cursor.lastrowid
        
        # ذخیره تاریخچه
        for entry in result.optimization_history:
            params_clean = {}
            for key, value in entry['params'].items():
                if isinstance(value, (np.integer, np.floating)):
                    params_clean[key] = value.item()
                else:
                    params_clean[key] = value
            
            cursor.execute('''
                INSERT INTO optimization_history 
                (optimization_id, iteration, params, score, optimization_type)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                optimization_id,
                entry['iteration'],
                json.dumps(params_clean),
                float(entry['score']),
                entry['type']
            ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Optimization results saved for {model_name} {symbol} {timeframe}")
    
    def get_best_parameters(self, model_name: str, symbol: str, timeframe: str) -> Optional[Dict]:
        """دریافت بهترین پارامترها"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT best_params, best_score FROM optimization_results 
            WHERE model_name = ? AND symbol = ? AND timeframe = ?
            ORDER BY best_score DESC LIMIT 1
        ''', (model_name, symbol, timeframe))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return json.loads(result[0])
        return None
    
    def get_optimization_summary(self) -> Dict:
        """خلاصه بهینه‌سازی‌ها"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM optimization_results')
        total_optimizations = cursor.fetchone()[0]
        
        cursor.execute('''
            SELECT model_name, symbol, timeframe, best_score 
            FROM optimization_results 
            ORDER BY best_score DESC LIMIT 5
        ''')
        best_results = cursor.fetchall()
        
        cursor.execute('''
            SELECT model_name, COUNT(*), AVG(best_score) 
            FROM optimization_results 
            GROUP BY model_name
        ''')
        model_stats = cursor.fetchall()
        
        conn.close()
        
        return {
            'total_optimizations': total_optimizations,
            'best_results': [
                {
                    'model': row[0],
                    'symbol': row[1],
                    'timeframe': row[2],
                    'score': row[3]
                }
                for row in best_results
            ],
            'model_statistics': [
                {
                    'model': row[0],
                    'optimizations': row[1],
                    'avg_score': row[2]
                }
                for row in model_stats
            ]
        }

def main():
    """تست سیستم تنظیم خودکار پارامترها"""
    print("Auto-Hyperparameter Tuning System Test")
    print("=" * 45)
    
    tuner = AutoHyperparameterTuner("test_hyperparameter_tuning.db")
    
    def evaluate_rl_params(params: Dict) -> float:
        """تابع ارزیابی برای پارامترهای RL"""
        learning_rate = params['learning_rate']
        discount_factor = params['discount_factor']
        epsilon_decay = params['epsilon_decay']
        epsilon_min = params['epsilon_min']
        batch_size = params['batch_size']
        
        # شبیه‌سازی عملکرد
        score = (
            (1 - learning_rate) * 0.3 +
            discount_factor * 0.3 +
            epsilon_decay * 0.2 +
            (1 - epsilon_min) * 0.1 +
            (batch_size / 128) * 0.1
        )
        
        score += random.gauss(0, 0.05)
        return score
    
    def evaluate_prediction_params(params: Dict) -> float:
        """تابع ارزیابی برای پارامترهای پیش‌بینی"""
        n_estimators = params['n_estimators']
        max_depth = params['max_depth']
        min_samples_split = params['min_samples_split']
        min_samples_leaf = params['min_samples_leaf']
        max_features = params['max_features']
        
        base_score = 0.7
        base_score += (n_estimators / 300) * 0.15
        base_score += (max_depth / 20) * 0.1
        base_score -= (min_samples_split / 10) * 0.05
        base_score -= (min_samples_leaf / 4) * 0.03
        
        if max_features == "sqrt":
            base_score += 0.02
        elif max_features == "log2":
            base_score += 0.01
        
        base_score += random.gauss(0, 0.03)
        return base_score
    
    print("Testing RL parameter optimization...")
    rl_result = tuner.optimize_rl_parameters("EURUSD", "H1", evaluate_rl_params)
    
    print(f"RL Optimization Results:")
    print(f"  Best score: {rl_result.best_score:.6f}")
    print(f"  Best parameters:")
    for param, value in rl_result.best_params.items():
        print(f"    {param}: {value}")
    print(f"  Total evaluations: {rl_result.total_evaluations}")
    print(f"  Converged: {rl_result.convergence_info['converged']}")
    
    print(f"\nTesting Prediction parameter optimization...")
    pred_result = tuner.optimize_prediction_parameters("EURUSD", "H1", evaluate_prediction_params)
    
    print(f"Prediction Optimization Results:")
    print(f"  Best score: {pred_result.best_score:.6f}")
    print(f"  Best parameters:")
    for param, value in pred_result.best_params.items():
        print(f"    {param}: {value}")
    print(f"  Total evaluations: {pred_result.total_evaluations}")
    print(f"  Converged: {pred_result.convergence_info['converged']}")
    
    print(f"\nOptimization Summary:")
    summary = tuner.get_optimization_summary()
    print(f"  Total optimizations: {summary['total_optimizations']}")
    print(f"  Best results:")
    for result in summary['best_results']:
        print(f"    {result['model']} {result['symbol']}: {result['score']:.6f}")
    
    print(f"\n✅ Auto-Hyperparameter Tuning System test completed!")

if __name__ == "__main__":
    main() 