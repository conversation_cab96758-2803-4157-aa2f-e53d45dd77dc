"""Tests for Multi-exchange Auto-routing module"""

import unittest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from utils.multi_exchange_routing import (
    ExchangeInfo, ExchangeStatus, OrderType, OrderBookData,
    ArbitrageOpportunity, ExecutionOrder, ExchangeConnector,
    ArbitrageEngine, ExecutionCostOptimizer, LiquidityManager,
    FailoverSystem, MultiExchangeRouter
)


class TestExchangeConnector(unittest.TestCase):
    """تست‌های ExchangeConnector"""
    
    def setUp(self):
        self.exchange_info = ExchangeInfo(
            name="TestExchange",
            api_endpoint="https://api.test.com",
            trading_fee=0.1,
            withdrawal_fee=0.001,
            min_order_size=0.001,
            max_order_size=1000,
            supported_pairs={'BTCUSD', 'ETHUSD'}
        )
        self.connector = ExchangeConnector(self.exchange_info)
        
    def test_initialization(self):
        """تست مقداردهی اولیه"""
        self.assertEqual(self.connector.exchange_info.name, "TestExchange")
        self.assertFalse(self.connector.connected)
        self.assertEqual(len(self.connector.order_books), 0)
        
    async def test_connect_disconnect(self):
        """تست اتصال و قطع اتصال"""
        # تست اتصال
        success = await self.connector.connect()
        self.assertTrue(success)
        self.assertTrue(self.connector.connected)
        
        # تست قطع اتصال
        await self.connector.disconnect()
        self.assertFalse(self.connector.connected)
        
    async def test_get_order_book(self):
        """تست دریافت order book"""
        await self.connector.connect()
        
        order_book = await self.connector.get_order_book('BTCUSD')
        
        self.assertIsNotNone(order_book)
        self.assertEqual(order_book.exchange, "TestExchange")
        self.assertEqual(order_book.symbol, 'BTCUSD')
        self.assertGreater(len(order_book.bids), 0)
        self.assertGreater(len(order_book.asks), 0)
        
    async def test_place_order(self):
        """تست ثبت سفارش"""
        await self.connector.connect()
        
        order = ExecutionOrder(
            id="test_order_1",
            exchange="TestExchange",
            symbol="BTCUSD",
            side="buy",
            order_type=OrderType.MARKET,
            volume=1.0
        )
        
        success = await self.connector.place_order(order)
        
        self.assertTrue(success)
        self.assertEqual(order.status, "executed")
        self.assertIsNotNone(order.execution_price)
        self.assertGreater(order.fees, 0)


class TestArbitrageEngine(unittest.TestCase):
    """تست‌های ArbitrageEngine"""
    
    def setUp(self):
        self.engine = ArbitrageEngine(min_profit_threshold=0.5)
        
    def test_initialization(self):
        """تست مقداردهی اولیه"""
        self.assertEqual(self.engine.min_profit_threshold, 0.5)
        self.assertEqual(len(self.engine.opportunities), 0)
        
    def test_find_arbitrage_opportunities(self):
        """تست یافتن فرصت‌های آربیتراژ"""
        # تهیه order books با اختلاف قیمت برای آربیتراژ
        order_book1 = OrderBookData(
            exchange="Exchange1",
            symbol="BTCUSD",
            bids=[(49900, 1.0), (49890, 1.5)],
            asks=[(50000, 1.0), (50010, 1.5)],  # ask کم
            timestamp=time.time()
        )
        
        order_book2 = OrderBookData(
            exchange="Exchange2",
            symbol="BTCUSD",
            bids=[(50500, 1.0), (50490, 1.5)],  # bid بالا برای آربیتراژ
            asks=[(50600, 1.0), (50610, 1.5)],
            timestamp=time.time()
        )
        
        opportunities = self.engine.find_arbitrage_opportunities(
            [order_book1, order_book2], "BTCUSD"
        )
        
        self.assertGreater(len(opportunities), 0)
        
        # بررسی اولین فرصت
        opp = opportunities[0]
        self.assertEqual(opp.buy_exchange, "Exchange1")
        self.assertEqual(opp.sell_exchange, "Exchange2")
        self.assertGreater(opp.profit_percentage, 0.5)
        
    def test_rank_opportunities(self):
        """تست رتبه‌بندی فرصت‌ها"""
        opportunities = [
            ArbitrageOpportunity(
                buy_exchange="Ex1", sell_exchange="Ex2", symbol="BTCUSD",
                buy_price=50000, sell_price=50100, volume=1.0,
                profit=100, profit_percentage=0.2, timestamp=time.time()
            ),
            ArbitrageOpportunity(
                buy_exchange="Ex1", sell_exchange="Ex3", symbol="BTCUSD",
                buy_price=50000, sell_price=50200, volume=1.0,
                profit=200, profit_percentage=0.4, timestamp=time.time()
            )
        ]
        
        ranked = self.engine.rank_opportunities(opportunities)
        
        self.assertEqual(len(ranked), 2)
        self.assertGreater(ranked[0].profit_percentage, ranked[1].profit_percentage)


class TestExecutionCostOptimizer(unittest.TestCase):
    """تست‌های ExecutionCostOptimizer"""
    
    def setUp(self):
        self.optimizer = ExecutionCostOptimizer()
        
    def test_calculate_total_cost(self):
        """تست محاسبه کل هزینه"""
        cost = self.optimizer.calculate_total_cost(
            exchange="TestEx",
            volume=100,
            trading_fee=0.1,
            slippage=0.05,
            latency_penalty=0.01
        )
        
        self.assertAlmostEqual(cost, 0.16, places=5)  # 0.1 + 0.05 + 0.01
        
    def test_estimate_slippage(self):
        """تست تخمین لغزش قیمت"""
        order_book = OrderBookData(
            exchange="TestEx",
            symbol="BTCUSD",
            bids=[(50000, 1.0), (49990, 1.0)],
            asks=[(50010, 1.0), (50020, 1.0)],
            timestamp=time.time()
        )
        
        # تست برای خرید کوچک
        slippage = self.optimizer.estimate_slippage(order_book, 0.5, 'buy')
        self.assertEqual(slippage, 0.0)  # در همان سطح اول
        
        # تست برای خرید بزرگ
        slippage = self.optimizer.estimate_slippage(order_book, 1.5, 'buy')
        self.assertGreater(slippage, 0.0)  # نیاز به سطح دوم
        
    def test_select_optimal_exchange(self):
        """تست انتخاب صرافی بهینه"""
        exchanges = ["Ex1", "Ex2"]
        
        exchange_infos = {
            "Ex1": ExchangeInfo("Ex1", "api1", 0.1, 0.001, 0.001, 1000),
            "Ex2": ExchangeInfo("Ex2", "api2", 0.2, 0.001, 0.001, 1000)
        }
        
        order_books = {
            "Ex1": OrderBookData("Ex1", "BTCUSD", [(50000, 5.0)], [(50010, 5.0)], time.time()),
            "Ex2": OrderBookData("Ex2", "BTCUSD", [(50000, 5.0)], [(50010, 5.0)], time.time())
        }
        
        optimal = self.optimizer.select_optimal_exchange(
            exchanges, exchange_infos, order_books, 1.0, 'buy'
        )
        
        self.assertEqual(optimal, "Ex1")  # کارمزد کمتر


class TestLiquidityManager(unittest.TestCase):
    """تست‌های LiquidityManager"""
    
    def setUp(self):
        self.manager = LiquidityManager(max_order_size_ratio=0.1)
        
    def test_split_large_order(self):
        """تست تقسیم سفارش بزرگ"""
        order_books = {
            "Ex1": OrderBookData("Ex1", "BTCUSD", [(50000, 10.0)], [(50010, 10.0)], time.time()),
            "Ex2": OrderBookData("Ex2", "BTCUSD", [(50000, 20.0)], [(50010, 20.0)], time.time())
        }
        
        allocation = self.manager.split_large_order(5.0, order_books, 'buy')
        
        self.assertIn("Ex1", allocation)
        self.assertIn("Ex2", allocation)
        self.assertAlmostEqual(sum(allocation.values()), 5.0, places=1)
        
    def test_calculate_market_impact(self):
        """تست محاسبه تأثیر بازار"""
        order_book = OrderBookData(
            "TestEx", "BTCUSD",
            [(50000, 1.0), (49990, 1.0)],
            [(50010, 1.0), (50020, 1.0)],
            time.time()
        )
        
        # تأثیر سفارش کوچک
        impact = self.manager.calculate_market_impact(order_book, 0.5, 'buy')
        self.assertEqual(impact, 0.0)
        
        # تأثیر سفارش بزرگ
        impact = self.manager.calculate_market_impact(order_book, 1.5, 'buy')
        self.assertGreater(impact, 0.0)


class TestFailoverSystem(unittest.TestCase):
    """تست‌های FailoverSystem"""
    
    def setUp(self):
        self.failover = FailoverSystem(health_check_interval=1)
        
    def test_add_backup_exchanges(self):
        """تست اضافه کردن صرافی‌های پشتیبان"""
        self.failover.add_backup_exchanges("Primary", ["Backup1", "Backup2"])
        
        self.assertIn("Primary", self.failover.backup_exchanges)
        self.assertEqual(len(self.failover.backup_exchanges["Primary"]), 2)
        
    def test_get_healthy_exchanges(self):
        """تست دریافت صرافی‌های سالم"""
        # تنظیم وضعیت صرافی‌ها
        self.failover.exchange_health = {
            "Ex1": {"status": ExchangeStatus.ACTIVE},
            "Ex2": {"status": ExchangeStatus.ERROR},
            "Ex3": {"status": ExchangeStatus.ACTIVE}
        }
        
        healthy = self.failover.get_healthy_exchanges(["Ex1", "Ex2", "Ex3"])
        
        self.assertEqual(len(healthy), 2)
        self.assertIn("Ex1", healthy)
        self.assertIn("Ex3", healthy)
        self.assertNotIn("Ex2", healthy)
        
    def test_get_failover_exchange(self):
        """تست دریافت صرافی جایگزین"""
        self.failover.add_backup_exchanges("Primary", ["Backup1", "Backup2"])
        self.failover.exchange_health = {
            "Backup1": {"status": ExchangeStatus.ACTIVE},
            "Backup2": {"status": ExchangeStatus.ERROR}
        }
        
        failover_ex = self.failover.get_failover_exchange("Primary")
        
        self.assertEqual(failover_ex, "Backup1")


class TestMultiExchangeRouter(unittest.TestCase):
    """تست‌های MultiExchangeRouter"""
    
    def setUp(self):
        exchanges = [
            ExchangeInfo("Ex1", "api1", 0.1, 0.001, 0.001, 1000, supported_pairs={'BTCUSD'}),
            ExchangeInfo("Ex2", "api2", 0.15, 0.001, 0.001, 1000, supported_pairs={'BTCUSD'})
        ]
        self.router = MultiExchangeRouter(exchanges)
        
    def test_initialization(self):
        """تست مقداردهی اولیه"""
        self.assertEqual(len(self.router.exchanges), 2)
        self.assertIn("Ex1", self.router.exchanges)
        self.assertIn("Ex2", self.router.exchanges)
        
    async def test_initialize(self):
        """تست مقداردهی سیستم"""
        await self.router.initialize()
        
        self.assertEqual(len(self.router.connectors), 2)
        
        # بررسی اتصال
        for connector in self.router.connectors.values():
            self.assertTrue(connector.connected)
            
    async def test_scan_arbitrage_opportunities(self):
        """تست اسکن فرصت‌های آربیتراژ"""
        await self.router.initialize()
        
        # شبیه‌سازی اختلاف قیمت
        with patch.object(self.router.connectors["Ex1"], 'get_order_book') as mock1, \
             patch.object(self.router.connectors["Ex2"], 'get_order_book') as mock2:
            
            mock1.return_value = OrderBookData(
                "Ex1", "BTCUSD", [(49900, 1.0)], [(50000, 1.0)], time.time()
            )
            mock2.return_value = OrderBookData(
                "Ex2", "BTCUSD", [(50100, 1.0)], [(50200, 1.0)], time.time()
            )
            
            opportunities = await self.router.scan_arbitrage_opportunities(['BTCUSD'])
            
            self.assertGreater(len(opportunities), 0)
            
    def test_get_statistics(self):
        """تست دریافت آمار"""
        stats = self.router.get_statistics()
        
        self.assertIn('total_exchanges', stats)
        self.assertIn('connected_exchanges', stats)
        self.assertIn('total_arbitrages', stats)
        self.assertIn('total_profit', stats)
        self.assertIn('executed_orders', stats)


class TestAsyncIntegration(unittest.TestCase):
    """تست‌های یکپارچگی async"""
    
    def setUp(self):
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
    def tearDown(self):
        self.loop.close()
        
    def test_full_arbitrage_workflow(self):
        """تست کامل جریان کار آربیتراژ"""
        async def run_test():
            exchanges = [
                ExchangeInfo("Ex1", "api1", 0.1, 0.001, 0.001, 1000, supported_pairs={'BTCUSD'}),
                ExchangeInfo("Ex2", "api2", 0.1, 0.001, 0.001, 1000, supported_pairs={'BTCUSD'})
            ]
            
            router = MultiExchangeRouter(exchanges)
            await router.initialize()
            
            # شبیه‌سازی فرصت آربیتراژ
            opportunity = ArbitrageOpportunity(
                buy_exchange="Ex1",
                sell_exchange="Ex2",
                symbol="BTCUSD",
                buy_price=50000,
                sell_price=50100,
                volume=1.0,
                profit=100,
                profit_percentage=0.2,
                timestamp=time.time()
            )
            
            # اجرای آربیتراژ
            success = await router.execute_arbitrage(opportunity)
            
            self.assertTrue(success)
            self.assertEqual(router.total_arbitrages, 1)
            self.assertGreater(router.total_profit, 0)
            
            await router.shutdown()
            
        self.loop.run_until_complete(run_test())
        
    def test_order_execution_workflow(self):
        """تست جریان کار اجرای سفارش"""
        async def run_test():
            exchanges = [
                ExchangeInfo("Ex1", "api1", 0.1, 0.001, 0.001, 1000, supported_pairs={'BTCUSD'})
            ]
            
            router = MultiExchangeRouter(exchanges)
            await router.initialize()
            
            # اجرای سفارش
            executed_orders = await router.execute_order(
                symbol="BTCUSD",
                side="buy",
                volume=1.0,
                order_type=OrderType.MARKET
            )
            
            self.assertGreater(len(executed_orders), 0)
            self.assertEqual(executed_orders[0].status, "executed")
            
            await router.shutdown()
            
        self.loop.run_until_complete(run_test())


if __name__ == '__main__':
    unittest.main() 