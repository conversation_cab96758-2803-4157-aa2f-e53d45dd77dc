#!/usr/bin/env python3
"""
🧪 تست عملی مدل‌های دانلود شده
Practical Model Testing for Trading Use

آیا مدل‌های دانلود شده واقعاً قابل استفاده هستن؟
"""

import os
import json
from datetime import datetime

# Set proxy if available
if os.path.exists("PROXY.json"):
    os.environ["HTTP_PROXY"] = "http://127.0.0.1:10809"
    os.environ["HTTPS_PROXY"] = "http://127.0.0.1:10809"

def test_finbert_practical():
    """تست عملی FinBERT برای تحلیل احساسات مالی"""
    print("💰 Testing FinBERT for Financial Sentiment Analysis")
    
    try:
        from transformers import pipeline
        
        # Load the model
        classifier = pipeline("sentiment-analysis", model="ProsusAI/finbert")
        
        # Test with real financial texts
        financial_texts = [
            "Bitcoin reached a new all-time high of $100,000",
            "The Federal Reserve announced interest rate cuts",
            "Stock market crashed due to economic recession fears",
            "Tesla reported record quarterly earnings",
            "Cryptocurrency market shows strong bullish momentum",
            "Banking sector faces significant challenges",
            "Gold prices are stable amid market uncertainty",
            "Oil prices surge due to supply chain issues"
        ]
        
        print("   📊 Testing with real financial scenarios:")
        results = []
        
        for text in financial_texts:
            sentiment = classifier(text)[0]
            results.append({
                "text": text,
                "sentiment": sentiment["label"],
                "confidence": sentiment["score"]
            })
            
            emoji = "📈" if sentiment["label"] == "positive" else "📉" if sentiment["label"] == "negative" else "➡️"
            print(f"   {emoji} '{text[:50]}...' -> {sentiment['label']} ({sentiment['score']:.2f})")
        
        # Calculate statistics
        positive_count = sum(1 for r in results if r["sentiment"] == "positive")
        negative_count = sum(1 for r in results if r["sentiment"] == "negative")
        neutral_count = len(results) - positive_count - negative_count
        
        print(f"\n   📊 Results: {positive_count} positive, {negative_count} negative, {neutral_count} neutral")
        print("   ✅ FinBERT is working and useful for financial sentiment analysis!")
        
        return True, results
        
    except Exception as e:
        print(f"   ❌ FinBERT failed: {e}")
        return False, []

def test_cryptobert_practical():
    """تست عملی CryptoBERT برای تحلیل احساسات کریپتو"""
    print("\n🪙 Testing CryptoBERT for Crypto Sentiment Analysis")
    
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch
        
        # Load the model
        tokenizer = AutoTokenizer.from_pretrained("ElKulako/cryptobert")
        model = AutoModel.from_pretrained("ElKulako/cryptobert")
        
        # Test with crypto-related texts
        crypto_texts = [
            "Bitcoin is going to the moon",
            "Ethereum network upgrade shows promise",
            "Crypto market is experiencing a bear phase",
            "DeFi protocols are gaining adoption",
            "NFT market shows strong growth",
            "Regulatory concerns impact crypto prices"
        ]
        
        print("   📊 Testing with crypto scenarios:")
        results = []
        
        for text in crypto_texts:
            inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True)
            
            with torch.no_grad():
                outputs = model(**inputs)
                # Get average embedding
                embedding = outputs.last_hidden_state.mean(dim=1)
                
            results.append({
                "text": text,
                "embedding_norm": float(torch.norm(embedding).item()),
                "embedding_shape": str(embedding.shape)
            })
            
            print(f"   🔍 '{text[:50]}...' -> embedding norm: {results[-1]['embedding_norm']:.3f}")
        
        print("   ✅ CryptoBERT is working and can generate crypto-specific embeddings!")
        
        return True, results
        
    except Exception as e:
        print(f"   ❌ CryptoBERT failed: {e}")
        return False, []

def test_sentence_transformer_practical():
    """تست عملی Sentence Transformer برای similarity"""
    print("\n🔤 Testing Sentence Transformer for Similarity Analysis")
    
    try:
        from sentence_transformers import SentenceTransformer
        import numpy as np
        
        # Load the model
        model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")
        
        # Test with trading-related sentences
        trading_sentences = [
            "The stock market is bullish today",
            "Bitcoin price is rising significantly",
            "Gold prices are falling due to inflation",
            "Oil prices surge amid supply concerns",
            "Tech stocks show strong performance",
            "Banking sector faces regulatory challenges"
        ]
        
        print("   📊 Testing sentence similarity for trading:")
        
        # Generate embeddings
        embeddings = model.encode(trading_sentences)
        
        # Calculate similarity matrix
        similarity_matrix = np.dot(embeddings, embeddings.T)
        
        # Find most similar pairs
        results = []
        for i in range(len(trading_sentences)):
            for j in range(i+1, len(trading_sentences)):
                similarity = similarity_matrix[i][j]
                results.append({
                    "sentence1": trading_sentences[i],
                    "sentence2": trading_sentences[j],
                    "similarity": float(similarity)
                })
        
        # Sort by similarity
        results.sort(key=lambda x: x["similarity"], reverse=True)
        
        print("   🔍 Top 3 most similar sentence pairs:")
        for i, result in enumerate(results[:3]):
            print(f"   {i+1}. Similarity: {result['similarity']:.3f}")
            print(f"      '{result['sentence1'][:40]}...'")
            print(f"      '{result['sentence2'][:40]}...'")
            print()
        
        print("   ✅ Sentence Transformer is working and useful for text similarity!")
        
        return True, results
        
    except Exception as e:
        print(f"   ❌ Sentence Transformer failed: {e}")
        return False, []

def test_financial_bert_practical():
    """تست عملی Financial BERT برای اسناد مالی"""
    print("\n📄 Testing Financial BERT for Financial Document Analysis")
    
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch
        
        # Load the model
        tokenizer = AutoTokenizer.from_pretrained("nlpaueb/sec-bert-base")
        model = AutoModel.from_pretrained("nlpaueb/sec-bert-base")
        
        # Test with financial document excerpts
        financial_docs = [
            "The company reported net income of $2.5 billion for Q4 2023",
            "Revenue increased by 15% year-over-year driven by strong demand",
            "Operating expenses remained flat compared to previous quarter",
            "The board declared a quarterly dividend of $0.50 per share",
            "Management expects continued growth in the coming fiscal year",
            "Risk factors include regulatory changes and market volatility"
        ]
        
        print("   📊 Testing with financial document analysis:")
        results = []
        
        for doc in financial_docs:
            inputs = tokenizer(doc, return_tensors="pt", truncation=True, padding=True)
            
            with torch.no_grad():
                outputs = model(**inputs)
                # Get CLS token embedding (first token)
                cls_embedding = outputs.last_hidden_state[:, 0, :]
                
            results.append({
                "document": doc,
                "cls_embedding_norm": float(torch.norm(cls_embedding).item()),
                "sequence_length": inputs["input_ids"].shape[1]
            })
            
            print(f"   📊 '{doc[:50]}...' -> CLS norm: {results[-1]['cls_embedding_norm']:.3f}")
        
        print("   ✅ Financial BERT is working and can analyze financial documents!")
        
        return True, results
        
    except Exception as e:
        print(f"   ❌ Financial BERT failed: {e}")
        return False, []

def test_integration_with_trading_system():
    """تست ادغام با سیستم تریدینگ"""
    print("\n🔗 Testing Integration with Trading System")
    
    try:
        # Test if models can be used in trading pipeline
        print("   📈 Simulating trading pipeline integration...")
        
        # Sample market data
        market_news = [
            "Federal Reserve hints at interest rate cuts",
            "Tech earnings season shows mixed results",
            "Cryptocurrency regulation updates expected",
            "Oil prices volatile amid geopolitical tensions"
        ]
        
        # Sample price data
        price_data = {
            "BTCUSD": 95000,
            "EURUSD": 1.0950,
            "GOLD": 2650,
            "SP500": 4800
        }
        
        integration_results = {
            "sentiment_analysis": [],
            "price_correlation": [],
            "trading_signals": []
        }
        
        # Test sentiment analysis integration
        from transformers import pipeline
        classifier = pipeline("sentiment-analysis", model="ProsusAI/finbert")
        
        for news in market_news:
            sentiment = classifier(news)[0]
            integration_results["sentiment_analysis"].append({
                "news": news,
                "sentiment": sentiment["label"],
                "confidence": sentiment["score"]
            })
        
        # Generate mock trading signals based on sentiment
        for analysis in integration_results["sentiment_analysis"]:
            if analysis["sentiment"] == "positive" and analysis["confidence"] > 0.7:
                signal = "BUY"
            elif analysis["sentiment"] == "negative" and analysis["confidence"] > 0.7:
                signal = "SELL"
            else:
                signal = "HOLD"
            
            integration_results["trading_signals"].append({
                "news": analysis["news"],
                "signal": signal,
                "confidence": analysis["confidence"]
            })
        
        print("   📊 Integration test results:")
        for signal in integration_results["trading_signals"]:
            emoji = "📈" if signal["signal"] == "BUY" else "📉" if signal["signal"] == "SELL" else "➡️"
            print(f"   {emoji} {signal['signal']} - {signal['news'][:50]}... (conf: {signal['confidence']:.2f})")
        
        print("   ✅ Models can be integrated into trading system!")
        
        return True, integration_results
        
    except Exception as e:
        print(f"   ❌ Integration test failed: {e}")
        return False, {}

def main():
    """تابع اصلی"""
    print("🧪 Practical Model Testing for Trading System")
    print("=" * 60)
    
    test_results = {
        "timestamp": datetime.now().isoformat(),
        "tests": {}
    }
    
    # Test each model
    tests = [
        ("FinBERT", test_finbert_practical),
        ("CryptoBERT", test_cryptobert_practical),
        ("Sentence Transformer", test_sentence_transformer_practical),
        ("Financial BERT", test_financial_bert_practical),
        ("Integration", test_integration_with_trading_system)
    ]
    
    working_models = 0
    total_models = len(tests)
    
    for test_name, test_func in tests:
        try:
            success, results = test_func()
            test_results["tests"][test_name] = {
                "success": success,
                "results": results if success else "Failed"
            }
            
            if success:
                working_models += 1
                
        except Exception as e:
            print(f"   ❌ {test_name} test crashed: {e}")
            test_results["tests"][test_name] = {
                "success": False,
                "error": str(e)
            }
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎯 PRACTICAL TEST SUMMARY")
    print("=" * 60)
    
    print(f"📊 Total models tested: {total_models}")
    print(f"✅ Working models: {working_models}")
    print(f"❌ Failed models: {total_models - working_models}")
    print(f"📈 Success rate: {(working_models/total_models*100):.1f}%")
    
    if working_models >= 3:
        print("\n🎉 VERDICT: Models are PRACTICAL and USEFUL!")
        print("✅ Your downloaded models are ready for real trading applications!")
        print("💡 You can build a functional trading system with these models.")
    elif working_models >= 1:
        print("\n⚠️ VERDICT: Some models are working but limited functionality")
        print("💡 Consider focusing on the working models for your trading system.")
    else:
        print("\n❌ VERDICT: Downloaded models are NOT practically useful")
        print("💡 Internet bandwidth was wasted. Consider alternative approaches.")
    
    # Save report
    with open("practical_model_test_report.json", "w", encoding="utf-8") as f:
        json.dump(test_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Detailed report saved to: practical_model_test_report.json")

if __name__ == "__main__":
    main() 