import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.advanced_rl_agent import AdvancedRLTradingSystem
import numpy as np

def main():
    print("Advanced RL Trading System Test")
    print("=" * 40)
    
    # ایجاد سیستم
    rl_system = AdvancedRLTradingSystem("test_rl_system.db")
    
    # داده‌های نمونه
    sample_market_data = {
        'price_trend': 0.3,
        'volatility': 0.6,
        'volume_trend': 0.2,
        'rsi': 65.0,
        'regime': 'bull_market',
        'signal_confidence': 0.8,
        'recent_performance': 0.1
    }
    
    print("Sample Market Data:")
    for key, value in sample_market_data.items():
        print(f"  {key}: {value}")
    
    # دریافت توصیه
    recommendation = rl_system.generate_trading_recommendation(sample_market_data)
    
    print("\nTrading Recommendation:")
    print(f"  Action: {recommendation['recommended_action']}")
    print(f"  Position Size: {recommendation['position_size']:.3f}")
    print(f"  Stop Loss: {recommendation['stop_loss']:.3f}")
    print(f"  Take Profit: {recommendation['take_profit']:.3f}")
    print(f"  Confidence: {recommendation['confidence']:.3f}")
    
    print("\nAction Probabilities:")
    for action, prob in recommendation['action_probabilities'].items():
        print(f"  {action}: {prob:.3f}")
    
    # شبیه‌سازی تجربه
    print("\nSimulating trading experience...")
    
    state = rl_system.create_market_state(sample_market_data)
    action = rl_system.get_optimal_action(state, training=True)
    
    # شبیه‌سازی نتیجه بازار
    market_outcome = {
        'pnl': np.random.normal(0.001, 0.01),
        'price_change': np.random.normal(0.0005, 0.005)
    }
    
    reward = rl_system.calculate_reward(action, market_outcome)
    
    # ایجاد وضعیت بعدی
    next_market_data = sample_market_data.copy()
    next_market_data['recent_performance'] = reward / 10
    next_state = rl_system.create_market_state(next_market_data)
    
    # افزودن تجربه
    rl_system.add_experience(state, action, reward, next_state, False, "EURUSD")
    
    print(f"  Reward: {reward:.3f}")
    print(f"  Experience added to replay buffer")
    
    # آموزش agent
    print("\nTraining agent...")
    training_results = rl_system.train_agent(batch_size=16, episodes=5)
    
    print(f"  Episodes trained: {training_results['episodes_trained']}")
    print(f"  Average reward: {training_results['avg_reward']:.3f}")
    print(f"  Exploration rate: {training_results['exploration_rate']:.3f}")
    print(f"  Q-table size: {training_results['q_table_size']}")
    
    # آمار agent
    stats = rl_system.get_agent_statistics()
    print(f"\nAgent Statistics:")
    print(f"  Training episodes: {stats['training_episodes']}")
    print(f"  Q-table size: {stats['q_table_size']}")
    print(f"  Exploration rate: {stats['exploration_rate']:.3f}")
    print(f"  Replay buffer size: {stats['replay_buffer_size']}")
    
    # ذخیره سیستم
    rl_system.save_system()
    
    print("\nRL Trading System test completed successfully!")

if __name__ == "__main__":
    main() 