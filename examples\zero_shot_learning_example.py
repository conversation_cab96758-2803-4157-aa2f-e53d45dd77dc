#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Example of using Zero-Shot Learning for trading in new markets with limited or no data.

This example demonstrates:
1. Training a base model on a set of source markets
2. Using zero-shot learning to adapt to new markets without training
3. Using few-shot learning to adapt to new markets with minimal training
4. Comparing performance of zero-shot, few-shot, and baseline approaches
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import logging

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.rl_models import RLModelFactory
from models.zero_shot_learning import ZeroShotLearning
from env.trading_env import TradingEnv
from utils.data_utils import prepare_data_for_env

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def prepare_markets_data(source_markets, target_markets):
    """
    Prepare data for source and target markets
    
    Parameters:
    -----------
    source_markets : List[dict]
        List of source market configurations (symbol, timeframe)
    target_markets : List[dict]
        List of target market configurations (symbol, timeframe)
        
    Returns:
    --------
    dict
        Dictionary containing data for source and target markets
    """
    data = {
        'source': {},
        'target': {}
    }
    
    # Prepare source market data
    for market in source_markets:
        symbol = market['symbol']
        timeframe = market['timeframe']
        market_key = f"{symbol}_{timeframe}"
        
        logger.info(f"Preparing data for source market: {market_key}")
        
        market_data = prepare_data_for_env(
            symbol=symbol,
            timeframe=timeframe,
            train_ratio=0.7,
            test_ratio=0.3
        )
        
        data['source'][market_key] = market_data
    
    # Prepare target market data
    for market in target_markets:
        symbol = market['symbol']
        timeframe = market['timeframe']
        market_key = f"{symbol}_{timeframe}"
        
        logger.info(f"Preparing data for target market: {market_key}")
        
        market_data = prepare_data_for_env(
            symbol=symbol,
            timeframe=timeframe,
            train_ratio=0.7,
            test_ratio=0.3
        )
        
        data['target'][market_key] = market_data
    
    return data

def create_environments(market_data, window_size=20):
    """
    Create trading environments for market data
    
    Parameters:
    -----------
    market_data : dict
        Market data dictionary
    window_size : int
        Observation window size
        
    Returns:
    --------
    dict
        Dictionary containing environments for each market
    """
    environments = {}
    
    for market_type in ['source', 'target']:
        environments[market_type] = {}
        
        for market_key, data in market_data[market_type].items():
            # Create training environment
            train_env = TradingEnv(
                df=data['train'],
                symbol=market_key.split('_')[0],
                style='spot',
                timeframe=market_key.split('_')[1],
                indicators={'rsi': {'period': 14}, 'ma': {'period': 20}}
            )
            
            # Create test environment
            test_env = TradingEnv(
                df=data['test'],
                symbol=market_key.split('_')[0],
                style='spot',
                timeframe=market_key.split('_')[1],
                indicators={'rsi': {'period': 14}, 'ma': {'period': 20}}
            )
            
            environments[market_type][market_key] = {
                'train_env': train_env,
                'test_env': test_env,
                'data': data
            }
    
    return environments

def train_base_models(environments, num_timesteps=50000):
    """
    Train base models on source markets
    
    Parameters:
    -----------
    environments : dict
        Dictionary containing environments
    num_timesteps : int
        Number of timesteps to train each model
        
    Returns:
    --------
    dict
        Dictionary containing trained models for each source market
    """
    models = {}
    model_factory = RLModelFactory()
    
    for market_key, env_data in environments['source'].items():
        logger.info(f"Training base model for {market_key}")
        
        # Create model
        model = model_factory.create_model('ppo', env_data['train_env'])
        
        # Train model
        model.learn(total_timesteps=num_timesteps)
        
        # Store model
        models[market_key] = model
        
        # Save model
        os.makedirs('saved_models', exist_ok=True)
        model.save(f"saved_models/base_model_{market_key}.zip")
    
    return models

def evaluate_model(model, env, num_episodes=10):
    """
    Evaluate model performance
    
    Parameters:
    -----------
    model : RL model
        Model to evaluate
    env : TradingEnv
        Evaluation environment
    num_episodes : int
        Number of episodes to evaluate
        
    Returns:
    --------
    dict
        Evaluation metrics
    """
    rewards = []
    returns = []
    
    for episode in range(num_episodes):
        observation = env.reset()
        episode_reward = 0
        done = False
        
        while not done:
            action, _ = model.predict(observation, deterministic=True)
            observation, reward, done, info = env.step(action)
            episode_reward += reward
        
        rewards.append(episode_reward)
        returns.append(info.get('portfolio_return', 0))
    
    return {
        'mean_reward': np.mean(rewards),
        'std_reward': np.std(rewards),
        'mean_return': np.mean(returns),
        'std_return': np.std(returns)
    }

def main():
    """Main function to demonstrate zero-shot learning"""
    
    # Define source and target markets
    source_markets = [
        {'symbol': 'EURUSD', 'timeframe': 'H1'},
        {'symbol': 'GBPUSD', 'timeframe': 'H1'},
        {'symbol': 'USDJPY', 'timeframe': 'H1'}
    ]
    
    target_markets = [
        {'symbol': 'AUDUSD', 'timeframe': 'H1'},
        {'symbol': 'USDCAD', 'timeframe': 'H1'},
        {'symbol': 'XAUUSD', 'timeframe': 'H1'}  # Gold
    ]
    
    # Prepare data
    logger.info("Preparing market data")
    market_data = prepare_markets_data(source_markets, target_markets)
    
    # Create environments
    logger.info("Creating trading environments")
    environments = create_environments(market_data)
    
    # Train base models on source markets
    logger.info("Training base models on source markets")
    base_models = train_base_models(environments, num_timesteps=50000)
    
    # Create zero-shot learning model
    logger.info("Creating zero-shot learning model")
    zero_shot = ZeroShotLearning(
        base_model=list(base_models.values())[0],  # Use first model as base
        embedding_dim=64,
        similarity_threshold=0.6,
        use_market_embeddings=True,
        use_meta_learning=True,
        use_prototypes=True
    )
    
    # Create market embeddings for source markets
    logger.info("Creating market embeddings for source markets")
    for market_key, env_data in environments['source'].items():
        market_data = env_data['data']['train']
        embedding = zero_shot.compute_market_embedding(market_data, market_key)
        
        # Create prototype from trained model
        zero_shot.create_market_prototype(market_key, base_models[market_key])
    
    # Evaluate models on target markets
    logger.info("Evaluating models on target markets")
    results = {
        'baseline': {},
        'zero_shot': {},
        'few_shot': {}
    }
    
    for market_key, env_data in environments['target'].items():
        logger.info(f"Evaluating on target market: {market_key}")
        
        # Get market data
        market_data = env_data['data']['train']
        test_env = env_data['test_env']
        
        # Baseline: train new model from scratch
        logger.info(f"Training baseline model for {market_key}")
        model_factory = RLModelFactory()
        baseline_model = model_factory.create_model('ppo', env_data['train_env'])
        baseline_model.learn(total_timesteps=50000)
        baseline_metrics = evaluate_model(baseline_model, test_env)
        results['baseline'][market_key] = baseline_metrics
        
        # Zero-shot: adapt without training
        logger.info(f"Adapting zero-shot model for {market_key}")
        zero_shot_model = zero_shot.adapt_to_new_market(market_data, market_key, num_shots=0)
        zero_shot_metrics = evaluate_model(zero_shot_model, test_env)
        results['zero_shot'][market_key] = zero_shot_metrics
        
        # Few-shot: adapt with minimal training
        logger.info(f"Adapting few-shot model for {market_key}")
        few_shot_model = zero_shot.adapt_to_new_market(market_data, market_key, num_shots=5, adaptation_steps=1000)
        few_shot_metrics = evaluate_model(few_shot_model, test_env)
        results['few_shot'][market_key] = few_shot_metrics
        
        # Record performance
        zero_shot.record_performance(market_key, zero_shot_metrics['mean_return'])
    
    # Plot results
    plot_results(results, list(environments['target'].keys()))
    
    # Plot market similarity
    similarity_plot = zero_shot.plot_market_similarity()
    similarity_plot.savefig('market_similarity.png')
    
    # Plot performance transfer
    source_market_keys = list(environments['source'].keys())
    target_market_keys = list(environments['target'].keys())
    transfer_plot = zero_shot.plot_performance_transfer(source_market_keys, target_market_keys)
    transfer_plot.savefig('performance_transfer.png')
    
    # Save zero-shot learning model
    zero_shot.save('saved_models/zero_shot_learning')
    
    logger.info("Zero-shot learning experiment completed successfully")

def plot_results(results, market_keys):
    """
    Plot comparison of baseline, zero-shot, and few-shot approaches
    
    Parameters:
    -----------
    results : dict
        Results dictionary
    market_keys : List[str]
        List of market keys
    """
    # Plot mean returns
    plt.figure(figsize=(12, 6))
    
    x = np.arange(len(market_keys))
    width = 0.25
    
    baseline_returns = [results['baseline'][k]['mean_return'] for k in market_keys]
    zero_shot_returns = [results['zero_shot'][k]['mean_return'] for k in market_keys]
    few_shot_returns = [results['few_shot'][k]['mean_return'] for k in market_keys]
    
    plt.bar(x - width, baseline_returns, width, label='Baseline (Full Training)')
    plt.bar(x, zero_shot_returns, width, label='Zero-Shot (No Training)')
    plt.bar(x + width, few_shot_returns, width, label='Few-Shot (Minimal Training)')
    
    plt.xlabel('Target Market')
    plt.ylabel('Mean Return')
    plt.title('Performance Comparison')
    plt.xticks(x, market_keys, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    plt.savefig('zero_shot_comparison.png')
    
    # Plot training efficiency
    plt.figure(figsize=(10, 6))
    
    # Assuming baseline takes 100% training time, zero-shot takes 0%, and few-shot takes 2%
    training_time = [100, 0, 2]
    approaches = ['Baseline', 'Zero-Shot', 'Few-Shot']
    
    # Average returns across all markets
    avg_returns = [
        np.mean([results['baseline'][k]['mean_return'] for k in market_keys]),
        np.mean([results['zero_shot'][k]['mean_return'] for k in market_keys]),
        np.mean([results['few_shot'][k]['mean_return'] for k in market_keys])
    ]
    
    plt.scatter(training_time, avg_returns, s=100)
    
    for i, approach in enumerate(approaches):
        plt.annotate(approach, (training_time[i], avg_returns[i]), 
                    xytext=(10, 5), textcoords='offset points')
    
    plt.xlabel('Relative Training Time (%)')
    plt.ylabel('Average Return')
    plt.title('Training Efficiency')
    plt.grid(True)
    plt.tight_layout()
    
    plt.savefig('zero_shot_efficiency.png')

if __name__ == "__main__":
    main() 