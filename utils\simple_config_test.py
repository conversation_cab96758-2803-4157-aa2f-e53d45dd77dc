#!/usr/bin/env python3
"""
🧪 Simple Configuration Test
تست ساده سیستم پیکربندی
"""

import os
import sys
import json
import tempfile

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.simple_config import SimpleAdvancedConfig, TradingMode, DatabaseType, LogLevel

def test_simple_config():
    """تست ساده پیکربندی"""
    print("🧪 Testing Simple Configuration Management...")
    
    tests_passed = 0
    tests_failed = 0
    
    # Test 1: Basic config creation
    try:
        config = SimpleAdvancedConfig()
        assert config.app_name == "Advanced Trading System"
        assert config.version == "2.0.0"
        assert config.environment == "development"
        print("✅ Test 1: Basic config creation - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 1: Basic config creation - FAILED: {e}")
        tests_failed += 1
    
    # Test 2: Component configs
    try:
        config = SimpleAdvancedConfig()
        assert config.proxy.enabled == False
        assert config.logging.level == LogLevel.INFO
        assert config.database.type == DatabaseType.SQLITE
        assert config.trading.mode == TradingMode.DEMO
        print("✅ Test 2: Component configs - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 2: Component configs - FAILED: {e}")
        tests_failed += 1
    
    # Test 3: Database connection string
    try:
        config = SimpleAdvancedConfig()
        connection_string = config.database.get_connection_string()
        assert "sqlite:///trading_system.db" == connection_string
        print("✅ Test 3: Database connection string - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 3: Database connection string - FAILED: {e}")
        tests_failed += 1
    
    # Test 4: Config validation
    try:
        config = SimpleAdvancedConfig()
        validation_results = config.validate_configuration()
        assert isinstance(validation_results, dict)
        assert "is_valid" in validation_results
        assert validation_results["is_valid"] == True
        print("✅ Test 4: Config validation - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 4: Config validation - FAILED: {e}")
        tests_failed += 1
    
    # Test 5: Save/Load config
    try:
        config = SimpleAdvancedConfig()
        config.app_name = "Test Trading System"
        config.debug = True
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            temp_file = f.name
        
        config.save_to_file(temp_file)
        
        # Load from file
        loaded_config = SimpleAdvancedConfig.load_from_file(temp_file)
        assert loaded_config.app_name == "Test Trading System"
        assert loaded_config.debug == True
        
        # Cleanup
        os.unlink(temp_file)
        
        print("✅ Test 5: Save/Load config - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 5: Save/Load config - FAILED: {e}")
        tests_failed += 1
    
    # Test 6: Component config access
    try:
        config = SimpleAdvancedConfig()
        trading_config = config.get_component_config("trading")
        assert trading_config is not None
        assert trading_config.initial_capital == 10000.0
        print("✅ Test 6: Component config access - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 6: Component config access - FAILED: {e}")
        tests_failed += 1
    
    # Test 7: Export config report
    try:
        config = SimpleAdvancedConfig()
        report = config.export_config_report()
        assert isinstance(report, dict)
        assert "timestamp" in report
        assert "config" in report
        assert "validation" in report
        print("✅ Test 7: Export config report - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 7: Export config report - FAILED: {e}")
        tests_failed += 1
    
    # Test 8: Trading symbols validation
    try:
        config = SimpleAdvancedConfig()
        config.trading.symbols = ["EURUSD", "GBPUSD"]
        assert len(config.trading.symbols) == 2
        assert "EURUSD" in config.trading.symbols
        print("✅ Test 8: Trading symbols validation - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 8: Trading symbols validation - FAILED: {e}")
        tests_failed += 1
    
    # Test 9: Proxy configuration
    try:
        config = SimpleAdvancedConfig()
        config.proxy.enabled = True
        config.proxy.http_url = "http://127.0.0.1:10809"
        assert config.proxy.enabled == True
        assert config.proxy.http_url == "http://127.0.0.1:10809"
        print("✅ Test 9: Proxy configuration - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 9: Proxy configuration - FAILED: {e}")
        tests_failed += 1
    
    # Test 10: AI models configuration
    try:
        config = SimpleAdvancedConfig()
        config.ai_models.enabled = True
        config.ai_models.max_memory_usage = 2048
        assert config.ai_models.enabled == True
        assert config.ai_models.max_memory_usage == 2048
        print("✅ Test 10: AI models configuration - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 10: AI models configuration - FAILED: {e}")
        tests_failed += 1
    
    # Summary
    total_tests = tests_passed + tests_failed
    success_rate = (tests_passed / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n📊 TEST RESULTS:")
    print(f"✅ Passed: {tests_passed}")
    print(f"❌ Failed: {tests_failed}")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    if tests_failed == 0:
        print("\n🎉 ALL SIMPLE CONFIG TESTS PASSED!")
        print("✅ Simple Configuration Management is ready!")
        return True
    else:
        print("\n❌ Some tests failed!")
        return False

if __name__ == "__main__":
    success = test_simple_config()
    exit(0 if success else 1) 