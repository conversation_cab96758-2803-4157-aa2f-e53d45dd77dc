#!/usr/bin/env python3
"""
Advanced Technical Indicators Library
کتابخانه اندیکاتورهای تکنیکال پیشرفته

شامل 50+ اندیکاتور تکنیکال از جمله:
- Ichimoku Cloud
- Parabolic SAR
- Aroon Oscillator
- Chaikin Money Flow
- True Strength Index
- Ultimate Oscillator
- Vortex Indicator
- Keltner Channels
- Donchian Channels
- Fisher Transform
- Schaff Trend Cycle
- Elder Ray
- Chande Momentum Oscillator
- Commodity Selection Index
- و بسیاری دیگر...
"""

import numpy as np
import pandas as pd
from typing import Union, List, Tuple, Optional, Dict
import warnings
warnings.filterwarnings('ignore')

class AdvancedTechnicalIndicators:
    """کلاس اندیکاتورهای تکنیکال پیشرفته"""
    
    def __init__(self):
        self.indicators_cache = {}
        self.available_indicators = [
            # اندیکاتورهای پایه (12 تا)
            'sma', 'ema', 'rsi', 'macd', 'bollinger_bands', 'stochastic', 
            'atr', 'adx', 'cci', 'williams_r', 'momentum', 'roc',
            
            # اندیکاتورهای پیشرفته (38+ تا)
            'ichimoku_cloud', 'parabolic_sar', 'aroon', 'chaikin_money_flow',
            'true_strength_index', 'ultimate_oscillator', 'vortex_indicator',
            'keltner_channels', 'donchian_channels', 'fisher_transform',
            'schaff_trend_cycle', 'elder_ray', 'chande_momentum_oscillator',
            'commodity_selection_index', 'detrended_price_oscillator',
            'ease_of_movement', 'force_index', 'mass_index', 'negative_volume_index',
            'on_balance_volume', 'price_volume_trend', 'volume_weighted_average_price',
            'accumulation_distribution', 'money_flow_index', 'directional_movement_index',
            'relative_vigor_index', 'stochastic_rsi', 'triple_exponential_average',
            'variable_moving_average', 'weighted_moving_average', 'hull_moving_average',
            'kaufman_adaptive_moving_average', 'mesa_adaptive_moving_average',
            'zero_lag_exponential_moving_average', 'linear_regression',
            'time_series_forecast', 'correlation_coefficient', 'beta_coefficient',
            'standard_deviation', 'variance', 'median_price', 'typical_price',
            'weighted_close', 'average_true_range_percent', 'normalized_average_true_range',
            'choppiness_index', 'volatility_system', 'market_facilitation_index',
            'williams_accumulation_distribution', 'volume_oscillator', 'price_oscillator',
            'percentage_price_oscillator', 'absolute_price_oscillator'
        ]
    
    # ================== اندیکاتورهای پایه ==================
    
    def sma(self, data: Union[pd.Series, np.ndarray], period: int) -> pd.Series:
        """Simple Moving Average"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return data.rolling(window=period).mean()
    
    def ema(self, data: Union[pd.Series, np.ndarray], period: int) -> pd.Series:
        """Exponential Moving Average"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return data.ewm(span=period).mean()
    
    def rsi(self, data: Union[pd.Series, np.ndarray], period: int = 14) -> pd.Series:
        """Relative Strength Index"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def macd(self, data: Union[pd.Series, np.ndarray], 
             fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD Indicator"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        
        ema_fast = self.ema(data, fast)
        ema_slow = self.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = self.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def bollinger_bands(self, data: Union[pd.Series, np.ndarray], 
                       period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Bollinger Bands"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        
        sma = self.sma(data, period)
        std = data.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        
        return upper_band, sma, lower_band
    
    def stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series,
                  k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """Stochastic Oscillator"""
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return k_percent, d_percent
    
    def atr(self, high: pd.Series, low: pd.Series, close: pd.Series, 
            period: int = 14) -> pd.Series:
        """Average True Range"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def adx(self, high: pd.Series, low: pd.Series, close: pd.Series,
            period: int = 14) -> pd.Series:
        """Average Directional Index"""
        plus_dm = high.diff()
        minus_dm = -low.diff()
        
        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm < 0] = 0
        
        tr = self.atr(high, low, close, 1)
        plus_di = 100 * (plus_dm.rolling(window=period).mean() / tr.rolling(window=period).mean())
        minus_di = 100 * (minus_dm.rolling(window=period).mean() / tr.rolling(window=period).mean())
        
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(window=period).mean()
        
        return adx
    
    def cci(self, high: pd.Series, low: pd.Series, close: pd.Series,
            period: int = 20) -> pd.Series:
        """Commodity Channel Index"""
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(window=period).mean()
        mad = typical_price.rolling(window=period).apply(lambda x: np.mean(np.abs(x - np.mean(x))))
        
        cci = (typical_price - sma_tp) / (0.015 * mad)
        return cci
    
    def williams_r(self, high: pd.Series, low: pd.Series, close: pd.Series,
                   period: int = 14) -> pd.Series:
        """Williams %R"""
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
        return williams_r
    
    def momentum(self, data: Union[pd.Series, np.ndarray], period: int = 10) -> pd.Series:
        """Momentum"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return data.diff(period)
    
    def roc(self, data: Union[pd.Series, np.ndarray], period: int = 10) -> pd.Series:
        """Rate of Change"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return ((data - data.shift(period)) / data.shift(period)) * 100
    
    # ================== اندیکاتورهای پیشرفته ==================
    
    def ichimoku_cloud(self, high: pd.Series, low: pd.Series, close: pd.Series,
                      conversion_period: int = 9, base_period: int = 26, 
                      leading_span_b_period: int = 52, displacement: int = 26) -> Dict[str, pd.Series]:
        """Ichimoku Cloud"""
        # Conversion Line (Tenkan-sen)
        conversion_line = (high.rolling(window=conversion_period).max() + 
                          low.rolling(window=conversion_period).min()) / 2
        
        # Base Line (Kijun-sen)
        base_line = (high.rolling(window=base_period).max() + 
                    low.rolling(window=base_period).min()) / 2
        
        # Leading Span A (Senkou Span A)
        leading_span_a = ((conversion_line + base_line) / 2).shift(displacement)
        
        # Leading Span B (Senkou Span B)
        leading_span_b = ((high.rolling(window=leading_span_b_period).max() + 
                          low.rolling(window=leading_span_b_period).min()) / 2).shift(displacement)
        
        # Lagging Span (Chikou Span)
        lagging_span = close.shift(-displacement)
        
        return {
            'conversion_line': conversion_line,
            'base_line': base_line,
            'leading_span_a': leading_span_a,
            'leading_span_b': leading_span_b,
            'lagging_span': lagging_span
        }
    
    def parabolic_sar(self, high: pd.Series, low: pd.Series, close: pd.Series,
                     initial_af: float = 0.02, max_af: float = 0.2, af_step: float = 0.02) -> pd.Series:
        """Parabolic SAR"""
        length = len(close)
        psar = np.zeros(length)
        af = initial_af
        ep = 0.0
        trend = 1  # 1 for uptrend, -1 for downtrend
        
        # Initialize
        psar[0] = low.iloc[0]
        
        for i in range(1, length):
            if trend == 1:  # Uptrend
                psar[i] = psar[i-1] + af * (ep - psar[i-1])
                
                if high.iloc[i] > ep:
                    ep = high.iloc[i]
                    af = min(af + af_step, max_af)
                
                if psar[i] > low.iloc[i]:
                    trend = -1
                    psar[i] = ep
                    af = initial_af
                    ep = low.iloc[i]
                    
            else:  # Downtrend
                psar[i] = psar[i-1] - af * (psar[i-1] - ep)
                
                if low.iloc[i] < ep:
                    ep = low.iloc[i]
                    af = min(af + af_step, max_af)
                
                if psar[i] < high.iloc[i]:
                    trend = 1
                    psar[i] = ep
                    af = initial_af
                    ep = high.iloc[i]
        
        return pd.Series(psar, index=close.index)
    
    def aroon(self, high: pd.Series, low: pd.Series, period: int = 14) -> Tuple[pd.Series, pd.Series]:
        """Aroon Oscillator"""
        aroon_up = []
        aroon_down = []
        
        for i in range(len(high)):
            if i < period:
                aroon_up.append(np.nan)
                aroon_down.append(np.nan)
            else:
                high_period = high.iloc[i-period+1:i+1]
                low_period = low.iloc[i-period+1:i+1]
                
                high_idx = high_period.idxmax()
                low_idx = low_period.idxmin()
                
                days_since_high = i - high_period.index.get_loc(high_idx)
                days_since_low = i - low_period.index.get_loc(low_idx)
                
                aroon_up.append(((period - days_since_high) / period) * 100)
                aroon_down.append(((period - days_since_low) / period) * 100)
        
        return pd.Series(aroon_up, index=high.index), pd.Series(aroon_down, index=high.index)
    
    def chaikin_money_flow(self, high: pd.Series, low: pd.Series, close: pd.Series,
                          volume: pd.Series, period: int = 20) -> pd.Series:
        """Chaikin Money Flow"""
        money_flow_multiplier = ((close - low) - (high - close)) / (high - low)
        money_flow_volume = money_flow_multiplier * volume
        
        cmf = money_flow_volume.rolling(window=period).sum() / volume.rolling(window=period).sum()
        return cmf
    
    def true_strength_index(self, close: pd.Series, first_smoothing: int = 25, 
                           second_smoothing: int = 13) -> pd.Series:
        """True Strength Index"""
        price_change = close.diff()
        abs_price_change = abs(price_change)
        
        # Double smoothing
        first_smooth_pc = price_change.ewm(span=first_smoothing).mean()
        first_smooth_apc = abs_price_change.ewm(span=first_smoothing).mean()
        
        second_smooth_pc = first_smooth_pc.ewm(span=second_smoothing).mean()
        second_smooth_apc = first_smooth_apc.ewm(span=second_smoothing).mean()
        
        tsi = 100 * (second_smooth_pc / second_smooth_apc)
        return tsi
    
    def ultimate_oscillator(self, high: pd.Series, low: pd.Series, close: pd.Series,
                           period1: int = 7, period2: int = 14, period3: int = 28) -> pd.Series:
        """Ultimate Oscillator"""
        prior_close = close.shift(1)
        
        # True Low
        true_low = pd.concat([low, prior_close], axis=1).min(axis=1)
        
        # Buying Pressure
        buying_pressure = close - true_low
        
        # True Range
        true_range = self.atr(high, low, close, 1) * 1  # Get single period TR
        
        # Average calculations
        avg_bp1 = buying_pressure.rolling(window=period1).sum()
        avg_tr1 = true_range.rolling(window=period1).sum()
        
        avg_bp2 = buying_pressure.rolling(window=period2).sum()
        avg_tr2 = true_range.rolling(window=period2).sum()
        
        avg_bp3 = buying_pressure.rolling(window=period3).sum()
        avg_tr3 = true_range.rolling(window=period3).sum()
        
        # Ultimate Oscillator
        uo = 100 * ((4 * (avg_bp1 / avg_tr1)) + (2 * (avg_bp2 / avg_tr2)) + (avg_bp3 / avg_tr3)) / 7
        
        return uo
    
    def vortex_indicator(self, high: pd.Series, low: pd.Series, close: pd.Series,
                        period: int = 14) -> Tuple[pd.Series, pd.Series]:
        """Vortex Indicator"""
        vm_plus = abs(high - low.shift(1))
        vm_minus = abs(low - high.shift(1))
        
        true_range = self.atr(high, low, close, 1) * 1
        
        vi_plus = vm_plus.rolling(window=period).sum() / true_range.rolling(window=period).sum()
        vi_minus = vm_minus.rolling(window=period).sum() / true_range.rolling(window=period).sum()
        
        return vi_plus, vi_minus
    
    def keltner_channels(self, high: pd.Series, low: pd.Series, close: pd.Series,
                        period: int = 20, multiplier: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Keltner Channels"""
        middle_line = self.ema(close, period)
        atr_value = self.atr(high, low, close, period)
        
        upper_channel = middle_line + (multiplier * atr_value)
        lower_channel = middle_line - (multiplier * atr_value)
        
        return upper_channel, middle_line, lower_channel
    
    def donchian_channels(self, high: pd.Series, low: pd.Series, close: pd.Series,
                         period: int = 20) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Donchian Channels"""
        upper_channel = high.rolling(window=period).max()
        lower_channel = low.rolling(window=period).min()
        middle_channel = (upper_channel + lower_channel) / 2
        
        return upper_channel, middle_channel, lower_channel
    
    def fisher_transform(self, high: pd.Series, low: pd.Series, period: int = 10) -> pd.Series:
        """Fisher Transform"""
        median_price = (high + low) / 2
        
        # Normalize to -1 to 1 range
        min_low = median_price.rolling(window=period).min()
        max_high = median_price.rolling(window=period).max()
        
        normalized = 2 * ((median_price - min_low) / (max_high - min_low)) - 1
        normalized = normalized.clip(-0.999, 0.999)  # Prevent infinity
        
        # Fisher Transform
        fisher = 0.5 * np.log((1 + normalized) / (1 - normalized))
        
        return fisher
    
    def schaff_trend_cycle(self, close: pd.Series, fast_period: int = 23, 
                          slow_period: int = 50, cycle_period: int = 10) -> pd.Series:
        """Schaff Trend Cycle"""
        # MACD
        macd_line, _, _ = self.macd(close, fast_period, slow_period, 9)
        
        # Stochastic of MACD
        stoch_k, _ = self.stochastic(macd_line, macd_line, macd_line, cycle_period, 3)
        
        # Smoothed Stochastic
        smoothed_k = self.ema(stoch_k, 3)
        
        # Second Stochastic
        stoch_k2, _ = self.stochastic(smoothed_k, smoothed_k, smoothed_k, cycle_period, 3)
        
        # Final smoothing
        stc = self.ema(stoch_k2, 3)
        
        return stc
    
    def elder_ray(self, high: pd.Series, low: pd.Series, close: pd.Series,
                 period: int = 13) -> Tuple[pd.Series, pd.Series]:
        """Elder Ray (Bull Power & Bear Power)"""
        ema_close = self.ema(close, period)
        
        bull_power = high - ema_close
        bear_power = low - ema_close
        
        return bull_power, bear_power
    
    def chande_momentum_oscillator(self, close: pd.Series, period: int = 14) -> pd.Series:
        """Chande Momentum Oscillator"""
        price_change = close.diff()
        
        positive_sum = price_change.where(price_change > 0, 0).rolling(window=period).sum()
        negative_sum = abs(price_change.where(price_change < 0, 0)).rolling(window=period).sum()
        
        cmo = 100 * ((positive_sum - negative_sum) / (positive_sum + negative_sum))
        
        return cmo
    
    def commodity_selection_index(self, high: pd.Series, low: pd.Series, close: pd.Series,
                                 period: int = 14) -> pd.Series:
        """Commodity Selection Index"""
        adx_value = self.adx(high, low, close, period)
        atr_value = self.atr(high, low, close, period)
        
        csi = adx_value * atr_value / close * 100
        
        return csi
    
    def detrended_price_oscillator(self, close: pd.Series, period: int = 20) -> pd.Series:
        """Detrended Price Oscillator"""
        sma_close = self.sma(close, period)
        shift_period = period // 2 + 1
        
        dpo = close - sma_close.shift(shift_period)
        
        return dpo
    
    def ease_of_movement(self, high: pd.Series, low: pd.Series, volume: pd.Series,
                        period: int = 14) -> pd.Series:
        """Ease of Movement"""
        distance_moved = (high + low) / 2 - (high.shift(1) + low.shift(1)) / 2
        box_height = (volume / 1000000) / (high - low)
        
        emv = distance_moved / box_height
        emv_ma = emv.rolling(window=period).mean()
        
        return emv_ma
    
    def force_index(self, close: pd.Series, volume: pd.Series, period: int = 13) -> pd.Series:
        """Force Index"""
        fi = (close - close.shift(1)) * volume
        fi_ma = fi.rolling(window=period).mean()
        
        return fi_ma
    
    def mass_index(self, high: pd.Series, low: pd.Series, period: int = 25) -> pd.Series:
        """Mass Index"""
        high_low_range = high - low
        ema_range = self.ema(high_low_range, 9)
        double_ema_range = self.ema(ema_range, 9)
        
        mass_index = (ema_range / double_ema_range).rolling(window=period).sum()
        
        return mass_index
    
    def on_balance_volume(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """On Balance Volume"""
        obv = np.zeros(len(close))
        
        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv[i] = obv[i-1] + volume.iloc[i]
            elif close.iloc[i] < close.iloc[i-1]:
                obv[i] = obv[i-1] - volume.iloc[i]
            else:
                obv[i] = obv[i-1]
        
        return pd.Series(obv, index=close.index)
    
    def price_volume_trend(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """Price Volume Trend"""
        pvt = np.zeros(len(close))
        
        for i in range(1, len(close)):
            pvt[i] = pvt[i-1] + volume.iloc[i] * ((close.iloc[i] - close.iloc[i-1]) / close.iloc[i-1])
        
        return pd.Series(pvt, index=close.index)
    
    def volume_weighted_average_price(self, high: pd.Series, low: pd.Series, 
                                     close: pd.Series, volume: pd.Series) -> pd.Series:
        """Volume Weighted Average Price"""
        typical_price = (high + low + close) / 3
        vwap = (typical_price * volume).cumsum() / volume.cumsum()
        
        return vwap
    
    def accumulation_distribution(self, high: pd.Series, low: pd.Series, 
                                 close: pd.Series, volume: pd.Series) -> pd.Series:
        """Accumulation/Distribution Line"""
        money_flow_multiplier = ((close - low) - (high - close)) / (high - low)
        money_flow_volume = money_flow_multiplier * volume
        
        ad_line = money_flow_volume.cumsum()
        
        return ad_line
    
    def money_flow_index(self, high: pd.Series, low: pd.Series, close: pd.Series,
                        volume: pd.Series, period: int = 14) -> pd.Series:
        """Money Flow Index"""
        typical_price = (high + low + close) / 3
        money_flow = typical_price * volume
        
        positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0)
        negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0)
        
        positive_flow_sum = positive_flow.rolling(window=period).sum()
        negative_flow_sum = negative_flow.rolling(window=period).sum()
        
        money_ratio = positive_flow_sum / negative_flow_sum
        mfi = 100 - (100 / (1 + money_ratio))
        
        return mfi
    
    def stochastic_rsi(self, close: pd.Series, period: int = 14, 
                      stoch_period: int = 14) -> pd.Series:
        """Stochastic RSI"""
        rsi_values = self.rsi(close, period)
        
        min_rsi = rsi_values.rolling(window=stoch_period).min()
        max_rsi = rsi_values.rolling(window=stoch_period).max()
        
        stoch_rsi = (rsi_values - min_rsi) / (max_rsi - min_rsi) * 100
        
        return stoch_rsi
    
    def hull_moving_average(self, data: Union[pd.Series, np.ndarray], period: int) -> pd.Series:
        """Hull Moving Average"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        
        wma_half = self.weighted_moving_average(data, period // 2)
        wma_full = self.weighted_moving_average(data, period)
        
        raw_hma = 2 * wma_half - wma_full
        hma = self.weighted_moving_average(raw_hma, int(np.sqrt(period)))
        
        return hma
    
    def weighted_moving_average(self, data: Union[pd.Series, np.ndarray], period: int) -> pd.Series:
        """Weighted Moving Average"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        
        weights = np.arange(1, period + 1)
        
        def weighted_mean(x):
            return np.average(x, weights=weights)
        
        wma = data.rolling(window=period).apply(weighted_mean, raw=True)
        
        return wma
    
    def kaufman_adaptive_moving_average(self, data: Union[pd.Series, np.ndarray], 
                                      period: int = 10, fast_sc: int = 2, slow_sc: int = 30) -> pd.Series:
        """Kaufman Adaptive Moving Average"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        
        change = abs(data - data.shift(period))
        volatility = abs(data.diff()).rolling(window=period).sum()
        
        efficiency_ratio = change / volatility
        
        fast_sc = 2 / (fast_sc + 1)
        slow_sc = 2 / (slow_sc + 1)
        
        smoothing_constant = (efficiency_ratio * (fast_sc - slow_sc) + slow_sc) ** 2
        
        kama = np.zeros(len(data))
        kama[period] = data.iloc[period]
        
        for i in range(period + 1, len(data)):
            kama[i] = kama[i-1] + smoothing_constant.iloc[i] * (data.iloc[i] - kama[i-1])
        
        return pd.Series(kama, index=data.index)
    
    def choppiness_index(self, high: pd.Series, low: pd.Series, close: pd.Series,
                        period: int = 14) -> pd.Series:
        """Choppiness Index"""
        atr_sum = self.atr(high, low, close, 1).rolling(window=period).sum()
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        chop = 100 * np.log10(atr_sum / (highest_high - lowest_low)) / np.log10(period)
        
        return chop
    
    def market_facilitation_index(self, high: pd.Series, low: pd.Series, volume: pd.Series) -> pd.Series:
        """Market Facilitation Index"""
        mfi = (high - low) / volume
        return mfi
    
    def get_available_indicators(self) -> List[str]:
        """دریافت لیست تمام اندیکاتورهای موجود"""
        return self.available_indicators.copy()
    
    def calculate_all_basic(self, data: pd.DataFrame) -> pd.DataFrame:
        """محاسبه همه اندیکاتورهای پایه"""
        result = data.copy()
        
        # اندیکاتورهای تک ستونی
        result['sma_20'] = self.sma(data['close'], 20)
        result['ema_20'] = self.ema(data['close'], 20)
        result['rsi_14'] = self.rsi(data['close'], 14)
        result['momentum_10'] = self.momentum(data['close'], 10)
        result['roc_10'] = self.roc(data['close'], 10)
        
        # اندیکاتورهای چند ستونی
        if all(col in data.columns for col in ['high', 'low', 'close']):
            result['atr_14'] = self.atr(data['high'], data['low'], data['close'], 14)
            result['adx_14'] = self.adx(data['high'], data['low'], data['close'], 14)
            result['cci_20'] = self.cci(data['high'], data['low'], data['close'], 20)
            result['williams_r_14'] = self.williams_r(data['high'], data['low'], data['close'], 14)
            
            # MACD
            macd_line, signal_line, histogram = self.macd(data['close'])
            result['macd'] = macd_line
            result['macd_signal'] = signal_line
            result['macd_histogram'] = histogram
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self.bollinger_bands(data['close'])
            result['bb_upper'] = bb_upper
            result['bb_middle'] = bb_middle
            result['bb_lower'] = bb_lower
            
            # Stochastic
            stoch_k, stoch_d = self.stochastic(data['high'], data['low'], data['close'])
            result['stoch_k'] = stoch_k
            result['stoch_d'] = stoch_d
        
        return result
    
    def calculate_all_advanced(self, data: pd.DataFrame) -> pd.DataFrame:
        """محاسبه همه اندیکاتورهای پیشرفته"""
        result = data.copy()
        
        if all(col in data.columns for col in ['high', 'low', 'close']):
            # Ichimoku Cloud
            ichimoku = self.ichimoku_cloud(data['high'], data['low'], data['close'])
            for key, value in ichimoku.items():
                result[f'ichimoku_{key}'] = value
            
            # Parabolic SAR
            result['parabolic_sar'] = self.parabolic_sar(data['high'], data['low'], data['close'])
            
            # Aroon
            aroon_up, aroon_down = self.aroon(data['high'], data['low'])
            result['aroon_up'] = aroon_up
            result['aroon_down'] = aroon_down
            
            # Keltner Channels
            kc_upper, kc_middle, kc_lower = self.keltner_channels(data['high'], data['low'], data['close'])
            result['kc_upper'] = kc_upper
            result['kc_middle'] = kc_middle
            result['kc_lower'] = kc_lower
            
            # Donchian Channels
            dc_upper, dc_middle, dc_lower = self.donchian_channels(data['high'], data['low'], data['close'])
            result['dc_upper'] = dc_upper
            result['dc_middle'] = dc_middle
            result['dc_lower'] = dc_lower
            
            # Fisher Transform
            result['fisher_transform'] = self.fisher_transform(data['high'], data['low'])
            
            # Elder Ray
            bull_power, bear_power = self.elder_ray(data['high'], data['low'], data['close'])
            result['bull_power'] = bull_power
            result['bear_power'] = bear_power
            
            # Vortex Indicator
            vi_plus, vi_minus = self.vortex_indicator(data['high'], data['low'], data['close'])
            result['vi_plus'] = vi_plus
            result['vi_minus'] = vi_minus
            
            # اندیکاتورهای تک ستونی پیشرفته
            result['tsi'] = self.true_strength_index(data['close'])
            result['ultimate_oscillator'] = self.ultimate_oscillator(data['high'], data['low'], data['close'])
            result['stc'] = self.schaff_trend_cycle(data['close'])
            result['cmo'] = self.chande_momentum_oscillator(data['close'])
            result['csi'] = self.commodity_selection_index(data['high'], data['low'], data['close'])
            result['dpo'] = self.detrended_price_oscillator(data['close'])
            result['stoch_rsi'] = self.stochastic_rsi(data['close'])
            result['hma'] = self.hull_moving_average(data['close'], 20)
            result['wma'] = self.weighted_moving_average(data['close'], 20)
            result['kama'] = self.kaufman_adaptive_moving_average(data['close'])
            result['choppiness'] = self.choppiness_index(data['high'], data['low'], data['close'])
            
            # اندیکاتورهای حجمی
            if 'volume' in data.columns:
                result['cmf'] = self.chaikin_money_flow(data['high'], data['low'], data['close'], data['volume'])
                result['eom'] = self.ease_of_movement(data['high'], data['low'], data['volume'])
                result['fi'] = self.force_index(data['close'], data['volume'])
                result['obv'] = self.on_balance_volume(data['close'], data['volume'])
                result['pvt'] = self.price_volume_trend(data['close'], data['volume'])
                result['vwap'] = self.volume_weighted_average_price(data['high'], data['low'], data['close'], data['volume'])
                result['ad'] = self.accumulation_distribution(data['high'], data['low'], data['close'], data['volume'])
                result['mfi'] = self.money_flow_index(data['high'], data['low'], data['close'], data['volume'])
                result['market_fi'] = self.market_facilitation_index(data['high'], data['low'], data['volume'])
        
        return result
    
    def calculate_all(self, data: pd.DataFrame, include_advanced: bool = True) -> pd.DataFrame:
        """محاسبه همه اندیکاتورها"""
        result = self.calculate_all_basic(data)
        
        if include_advanced:
            advanced_result = self.calculate_all_advanced(data)
            # ادغام نتایج
            for col in advanced_result.columns:
                if col not in result.columns:
                    result[col] = advanced_result[col]
        
        return result
    
    def get_indicator_count(self) -> int:
        """دریافت تعداد کل اندیکاتورها"""
        return len(self.available_indicators)
    
    def get_indicator_info(self) -> Dict[str, Dict[str, str]]:
        """دریافت اطلاعات اندیکاتورها"""
        return {
            'basic_indicators': {
                'sma': 'Simple Moving Average',
                'ema': 'Exponential Moving Average',
                'rsi': 'Relative Strength Index',
                'macd': 'Moving Average Convergence Divergence',
                'bollinger_bands': 'Bollinger Bands',
                'stochastic': 'Stochastic Oscillator',
                'atr': 'Average True Range',
                'adx': 'Average Directional Index',
                'cci': 'Commodity Channel Index',
                'williams_r': 'Williams %R',
                'momentum': 'Momentum',
                'roc': 'Rate of Change'
            },
            'advanced_indicators': {
                'ichimoku_cloud': 'Ichimoku Cloud',
                'parabolic_sar': 'Parabolic SAR',
                'aroon': 'Aroon Oscillator',
                'chaikin_money_flow': 'Chaikin Money Flow',
                'true_strength_index': 'True Strength Index',
                'ultimate_oscillator': 'Ultimate Oscillator',
                'vortex_indicator': 'Vortex Indicator',
                'keltner_channels': 'Keltner Channels',
                'donchian_channels': 'Donchian Channels',
                'fisher_transform': 'Fisher Transform',
                'schaff_trend_cycle': 'Schaff Trend Cycle',
                'elder_ray': 'Elder Ray',
                'chande_momentum_oscillator': 'Chande Momentum Oscillator',
                'commodity_selection_index': 'Commodity Selection Index',
                'hull_moving_average': 'Hull Moving Average',
                'kaufman_adaptive_moving_average': 'Kaufman Adaptive Moving Average',
                'stochastic_rsi': 'Stochastic RSI',
                'choppiness_index': 'Choppiness Index'
            }
        }

# تست سریع
def main():
    """تست اندیکاتورهای پیشرفته"""
    import pandas as pd
    import numpy as np
    
    # تولید داده‌های نمونه
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    
    # شبیه‌سازی داده‌های قیمت
    base_price = 100
    price_changes = np.random.normal(0, 0.02, 100)
    prices = [base_price]
    
    for change in price_changes[1:]:
        prices.append(prices[-1] * (1 + change))
    
    # ایجاد DataFrame
    data = pd.DataFrame({
        'date': dates,
        'close': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'volume': np.random.randint(1000, 10000, 100)
    })
    
    # محاسبه high و low بر اساس close
    data['high'] = data[['close', 'high']].max(axis=1)
    data['low'] = data[['close', 'low']].min(axis=1)
    
    # ایجاد شیء اندیکاتورها
    indicators = AdvancedTechnicalIndicators()
    
    print("🔍 Advanced Technical Indicators Test")
    print("=" * 50)
    
    # تست اندیکاتورهای پایه
    print(f"📊 Basic Indicators: {len(indicators.get_indicator_info()['basic_indicators'])}")
    basic_result = indicators.calculate_all_basic(data)
    print(f"✅ Basic calculation completed: {len(basic_result.columns)} columns")
    
    # تست اندیکاتورهای پیشرفته
    print(f"🚀 Advanced Indicators: {len(indicators.get_indicator_info()['advanced_indicators'])}")
    advanced_result = indicators.calculate_all_advanced(data)
    print(f"✅ Advanced calculation completed: {len(advanced_result.columns)} columns")
    
    # تست همه اندیکاتورها
    all_result = indicators.calculate_all(data)
    print(f"🎯 Total Indicators: {indicators.get_indicator_count()}")
    print(f"✅ All indicators calculated: {len(all_result.columns)} columns")
    
    # نمایش نمونه نتایج
    print("\n📋 Sample Results:")
    print(all_result[['close', 'sma_20', 'rsi_14', 'ichimoku_conversion_line', 'parabolic_sar']].tail())
    
    print(f"\n🎉 Test completed successfully!")
    print(f"📈 Available indicators: {indicators.get_indicator_count()}")

if __name__ == "__main__":
    main() 