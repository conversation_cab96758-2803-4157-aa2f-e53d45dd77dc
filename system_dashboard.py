#!/usr/bin/env python3
"""
🎯 Comprehensive System Dashboard
داشبورد جامع سیستم معاملاتی
"""

import sys
import os
import asyncio
from datetime import datetime
import json

sys.path.insert(0, '.')

def display_system_status():
    """نمایش وضعیت سیستم"""
    try:
        from main_new import TradingSystemManager
        
        print("🚀 COMPREHENSIVE TRADING SYSTEM DASHBOARD")
        print("=" * 60)
        
        system_manager = TradingSystemManager()
        
        if system_manager.initialize_system():
            print("✅ System Initialization: SUCCESS")
            
            # نمایش وضعیت جامع
            status = system_manager.get_system_status()
            
            print(f"\n📊 System Status:")
            print(f"  🔧 Initialized: {'✅' if status['is_initialized'] else '❌'}")
            print(f"  ⚡ Advanced Components: {'✅' if status['advanced_components_initialized'] else '❌'}")
            
            print(f"\n📦 Core Components:")
            for component, available in status['components'].items():
                print(f"  - {component}: {'✅' if available else '❌'}")
            
            print(f"\n🚀 Advanced Components:")
            advanced_count = sum(1 for available in status['advanced_components'].values() if available)
            total_count = len(status['advanced_components'])
            print(f"  📈 Success Rate: {advanced_count}/{total_count} ({advanced_count/total_count*100:.1f}%)")
            
            for component, available in status['advanced_components'].items():
                print(f"  - {component}: {'✅' if available else '❌'}")
            
            # اجرای تست‌ها
            print(f"\n🧪 Running System Tests...")
            test_success = system_manager.run_system_tests()
            print(f"  Test Results: {'✅ PASSED' if test_success else '⚠️ PARTIAL'}")
            
            return True
        else:
            print("❌ System initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard error: {e}")
        return False

def start_trading_demo():
    """شروع دمو معاملاتی"""
    print("\n🎮 TRADING DEMO")
    print("=" * 30)
    
    try:
        from main_new import system_manager
        
        async def demo():
            await system_manager.start_trading_system()
            print("✅ Trading system started")
            
            # شبیه‌سازی معاملات
            print("📈 Simulating trades...")
            
            # نمایش آمار
            if system_manager.multi_exchange_manager:
                stats = system_manager.multi_exchange_manager.get_statistics()
                print(f"  📊 Exchanges: {stats['connected_exchanges']}/{stats['total_exchanges']}")
                print(f"  💹 Symbols: {stats['total_symbols']}")
            
            await system_manager.shutdown_system()
            print("🔴 System shutdown completed")
        
        asyncio.run(demo())
        return True
        
    except Exception as e:
        print(f"❌ Demo error: {e}")
        return False

def show_configuration_options():
    """نمایش گزینه‌های تنظیمات"""
    print("\n⚙️ CONFIGURATION OPTIONS")
    print("=" * 40)
    
    options = {
        "1": "🎯 Display System Status",
        "2": "🎮 Start Trading Demo", 
        "3": "📊 View Performance Metrics",
        "4": "🔧 System Health Check",
        "5": "📈 Market Data Test",
        "6": "🛠️ Component Diagnostics",
        "7": "🔄 Restart System",
        "8": "🔴 Shutdown System"
    }
    
    for key, value in options.items():
        print(f"  {key}. {value}")
    
    return options

def main():
    """نقطه ورود اصلی"""
    print("🎯 Advanced Trading System - Interactive Dashboard")
    print("=" * 60)
    
    while True:
        options = show_configuration_options()
        
        choice = input("\n🎯 Enter your choice (1-8, or 'q' to quit): ").strip()
        
        if choice.lower() == 'q':
            print("👋 Goodbye!")
            break
        elif choice == '1':
            display_system_status()
        elif choice == '2':
            start_trading_demo()
        elif choice == '3':
            print("📊 Performance metrics coming soon...")
        elif choice == '4':
            print("🔧 Health check coming soon...")
        elif choice == '5':
            print("📈 Market data test coming soon...")
        elif choice == '6':
            print("🛠️ Component diagnostics coming soon...")
        elif choice == '7':
            print("🔄 System restart coming soon...")
        elif choice == '8':
            print("🔴 System shutdown coming soon...")
        else:
            print("❌ Invalid choice. Please try again.")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
