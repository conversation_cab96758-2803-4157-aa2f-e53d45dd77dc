#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Database Transaction Management Test
تست سیستم مدیریت تراکنش‌های دیتابیس
"""

import os
import sys
import time
import asyncio
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_database_transaction_manager():
    """تست مدیر تراکنش‌های دیتابیس"""
    print("🧪 Testing Database Transaction Management System...")
    
    try:
        # Test 1: Import system
        print("\n1️⃣ Testing import...")
        from core.database_transaction_manager import (
            DatabaseTransactionManager,
            TransactionType,
            IsolationLevel,
            TradingSignal,
            TradingPosition,
            get_database_manager,
            transaction,
            execute_query,
            bulk_insert
        )
        print("   ✓ Import successful")
        
        # Test 2: Initialize database manager
        print("\n2️⃣ Testing database manager initialization...")
        db_manager = get_database_manager("sqlite:///test_trading_system.db")
        
        if db_manager.initialize():
            print("   ✓ Database manager initialized successfully")
        else:
            print("   ❌ Database manager initialization failed")
            return False
        
        # Test 3: Simple transaction
        print("\n3️⃣ Testing simple transaction...")
        with transaction(transaction_type=TransactionType.WRITE) as (session, metrics):
            # Insert trading signal
            signal = TradingSignal(
                symbol="EURUSD",
                signal_type="BUY",
                price=1.0950,
                confidence=0.85,
                source="test_system"
            )
            session.add(signal)
            session.flush()
            
            signal_id = signal.id
            print(f"   ✓ Trading signal inserted: ID={signal_id}")
            print(f"   ✓ Transaction ID: {metrics.transaction_id}")
            print(f"   ✓ Transaction type: {metrics.transaction_type.value}")
        
        # Test 4: Query execution
        print("\n4️⃣ Testing query execution...")
        results = execute_query(
            "SELECT * FROM trading_signals WHERE symbol = :symbol",
            {"symbol": "EURUSD"}
        )
        
        if results:
            print(f"   ✓ Query executed successfully: {len(results)} records found")
            for result in results:
                print(f"   ✓ Signal: {result['symbol']} - {result['signal_type']} at {result['price']}")
        else:
            print("   ❌ No results from query")
        
        # Test 5: Bulk insert
        print("\n5️⃣ Testing bulk insert...")
        bulk_data = [
            {
                "symbol": "GBPUSD",
                "signal_type": "SELL",
                "price": 1.2750,
                "confidence": 0.75,
                "source": "bulk_test"
            },
            {
                "symbol": "USDJPY",
                "signal_type": "BUY",
                "price": 110.50,
                "confidence": 0.90,
                "source": "bulk_test"
            },
            {
                "symbol": "AUDUSD",
                "signal_type": "HOLD",
                "price": 0.7125,
                "confidence": 0.60,
                "source": "bulk_test"
            }
        ]
        
        inserted_count = bulk_insert(TradingSignal, bulk_data)
        print(f"   ✓ Bulk insert successful: {inserted_count} records inserted")
        
        # Test 6: Transaction with rollback
        print("\n6️⃣ Testing transaction rollback...")
        try:
            with transaction(transaction_type=TransactionType.WRITE) as (session, metrics):
                # Insert a signal
                signal = TradingSignal(
                    symbol="BTCUSD",
                    signal_type="BUY",
                    price=50000.0,
                    confidence=0.95,
                    source="rollback_test"
                )
                session.add(signal)
                session.flush()
                
                # Force an error to trigger rollback
                raise ValueError("Intentional error for rollback test")
                
        except ValueError as e:
            print(f"   ✓ Transaction rolled back as expected: {e}")
        
        # Verify rollback worked
        rollback_results = execute_query(
            "SELECT * FROM trading_signals WHERE symbol = :symbol",
            {"symbol": "BTCUSD"}
        )
        
        if not rollback_results:
            print("   ✓ Rollback successful: No BTCUSD records found")
        else:
            print("   ❌ Rollback failed: BTCUSD record still exists")
        
        # Test 7: Transaction statistics
        print("\n7️⃣ Testing transaction statistics...")
        stats = db_manager.get_system_health()
        
        if stats:
            print("   ✓ System health retrieved successfully")
            print(f"   ✓ Database initialized: {stats['database_initialized']}")
            print(f"   ✓ Total transactions: {stats['transaction_stats']['total_transactions']}")
            print(f"   ✓ Success rate: {stats['transaction_stats']['success_rate']:.2f}%")
        else:
            print("   ❌ Failed to retrieve system health")
        
        # Test 8: Connection pool statistics
        print("\n8️⃣ Testing connection pool statistics...")
        pool_stats = db_manager.connection_pool.get_connection_stats()
        
        if pool_stats:
            print("   ✓ Connection pool statistics retrieved")
            print(f"   ✓ Pool size: {pool_stats.get('pool_size', 'N/A')}")
            print(f"   ✓ Total connections: {pool_stats.get('total_connections', 'N/A')}")
        else:
            print("   ❌ Failed to retrieve connection pool statistics")
        
        # Test 9: Multiple isolation levels
        print("\n9️⃣ Testing isolation levels...")
        isolation_levels = [
            IsolationLevel.READ_COMMITTED,
            IsolationLevel.REPEATABLE_READ,
            IsolationLevel.SERIALIZABLE
        ]
        
        for isolation_level in isolation_levels:
            try:
                with transaction(
                    transaction_type=TransactionType.READ,
                    isolation_level=isolation_level
                ) as (session, metrics):
                    result = session.execute(
                        "SELECT COUNT(*) as count FROM trading_signals"
                    ).fetchone()
                    
                    print(f"   ✓ {isolation_level.value}: {result['count']} records")
            except Exception as e:
                print(f"   ❌ {isolation_level.value} failed: {e}")
        
        # Test 10: Clean up
        print("\n🔟 Testing cleanup...")
        db_manager.close()
        print("   ✓ Database manager closed successfully")
        
        # Clean up test database
        test_db_path = "test_trading_system.db"
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
            print("   ✓ Test database file removed")
        
        print("\n🎉 All Database Transaction Management tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Database Transaction Management test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_database_transaction_manager()
    if success:
        print("\n✅ Database Transaction Management System is ready!")
    else:
        print("\n❌ Database Transaction Management System has issues!")
    
    sys.exit(0 if success else 1) 