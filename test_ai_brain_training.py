"""
🧠 Test AI Brain Controller Training Management
تست مدیریت آموزش مغز هوشمند

این تست شامل:
1. تست مدیریت هوشمند آموزش مدل‌ها
2. تست انتخاب بهترین مدل‌ها
3. تست بهینه‌سازی پارامترها
4. تست تصمیم‌گیری هوشمند
"""

import os
import sys
import time
import json
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ai_brain_training_management():
    """تست مدیریت آموزش مغز هوشمند"""
    print("🧠 Testing AI Brain Training Management...")
    
    try:
        from core.ai_brain_controller import AISystemController
        
        # Initialize AI Brain
        ai_brain = AISystemController()
        
        print("✅ AI Brain Controller initialized successfully")
        
        # Test intelligent training management
        print("\n🔄 Starting intelligent model training management...")
        training_results = ai_brain.intelligent_model_training_manager()
        
        if training_results.get("overall_success", False):
            print("✅ Intelligent training management completed successfully")
            
            # Print summary
            print(f"\n📊 Training Summary:")
            print(f"   Training Session ID: {training_results.get('training_session_id', 'N/A')}")
            print(f"   Training Opportunities: {training_results.get('training_opportunities', 0)}")
            print(f"   Training Decisions: {training_results.get('training_decisions', 0)}")
            print(f"   Improvement Achieved: {training_results.get('improvement_achieved', 0):.3f}")
            
            # Print training results
            training_data = training_results.get("training_results", {})
            completed = len(training_data.get("completed_trainings", []))
            failed = len(training_data.get("failed_trainings", []))
            print(f"   Completed Trainings: {completed}")
            print(f"   Failed Trainings: {failed}")
            
            # Print model selection results
            selection_data = training_results.get("model_selection", {})
            selected_models = selection_data.get("selected_models", {})
            print(f"   Selected Models: {len(selected_models)}")
            
            for category, model_info in selected_models.items():
                model_name = model_info.get("name", "Unknown")
                score = model_info.get("selection_score", 0)
                print(f"     - {category}: {model_name} (score: {score:.3f})")
            
            # Print AI Brain recommendations
            recommendations = selection_data.get("ai_brain_recommendations", {})
            if recommendations:
                print(f"\n🧠 AI Brain Recommendations:")
                print(f"   Deployment Ready: {recommendations.get('deployment_ready', False)}")
                print(f"   System Completeness: {recommendations.get('system_completeness', 0):.2f}")
                print(f"   Confidence Level: {recommendations.get('confidence_level', 0):.2f}")
                
                next_steps = recommendations.get("recommended_next_steps", [])
                if next_steps:
                    print(f"   Next Steps:")
                    for step in next_steps:
                        print(f"     - {step}")
            
            return True
        else:
            error = training_results.get("error", "Unknown error")
            print(f"❌ Intelligent training management failed: {error}")
            return False
            
    except Exception as e:
        print(f"❌ AI Brain training test failed: {e}")
        return False

def test_training_decision_making():
    """تست تصمیم‌گیری آموزش"""
    print("\n🎯 Testing Training Decision Making...")
    
    try:
        from core.ai_brain_controller import AISystemController, TrainingDecision
        
        ai_brain = AISystemController()
        
        # Test training opportunity identification
        opportunities = ai_brain._identify_training_opportunities()
        print(f"✅ Identified {len(opportunities)} training opportunities")
        
        # Test training decisions
        decisions = ai_brain._make_training_decisions(opportunities)
        print(f"✅ Made {len(decisions)} training decisions")
        
        # Print decision details
        for i, decision in enumerate(decisions[:3], 1):  # Show first 3
            print(f"   {i}. {decision.model_name}")
            print(f"      Priority: {decision.priority}")
            print(f"      Estimated Time: {decision.estimated_time} min")
            print(f"      Expected Improvement: {decision.expected_improvement:.3f}")
            print(f"      Training Strategy: {decision.training_strategy}")
            print(f"      Hyperparameter Strategy: {decision.hyperparameter_strategy}")
        
        if len(decisions) > 3:
            print(f"      ... and {len(decisions) - 3} more decisions")
        
        return True
        
    except Exception as e:
        print(f"❌ Training decision test failed: {e}")
        return False

def test_model_selection_intelligence():
    """تست هوش انتخاب مدل"""
    print("\n🤖 Testing Model Selection Intelligence...")
    
    try:
        from core.ai_brain_controller import AISystemController
        
        ai_brain = AISystemController()
        
        # Create mock training results
        mock_training_results = {
            "completed_trainings": [
                {
                    "model_name": "FinBERT",
                    "training_time": 30,
                    "performance": {"accuracy": 0.85},
                    "improvement": 0.12
                },
                {
                    "model_name": "LSTM_TimeSeries", 
                    "training_time": 25,
                    "performance": {"rmse": 0.03},
                    "improvement": 0.08
                },
                {
                    "model_name": "PPO_Agent",
                    "training_time": 40,
                    "performance": {"avg_reward": 75},
                    "improvement": 0.15
                }
            ],
            "failed_trainings": []
        }
        
        # Test model selection
        selection_results = ai_brain._intelligent_model_selection(mock_training_results)
        
        selected_models = selection_results.get("selected_models", {})
        print(f"✅ Selected {len(selected_models)} models intelligently")
        
        for category, model_info in selected_models.items():
            print(f"   {category}: {model_info['name']} (score: {model_info.get('selection_score', 0):.3f})")
        
        # Test AI Brain recommendations
        recommendations = selection_results.get("ai_brain_recommendations", {})
        if recommendations:
            print(f"✅ AI Brain provided recommendations:")
            print(f"   Deployment Ready: {recommendations.get('deployment_ready', False)}")
            print(f"   Confidence Level: {recommendations.get('confidence_level', 0):.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model selection test failed: {e}")
        return False

def test_performance_analysis():
    """تست تحلیل عملکرد"""
    print("\n📊 Testing Performance Analysis...")
    
    try:
        from core.ai_brain_controller import AISystemController
        
        ai_brain = AISystemController()
        
        # Test current performance analysis
        performance_analysis = ai_brain._analyze_current_performance()
        
        print("✅ Performance analysis completed:")
        print(f"   Sentiment Accuracy: {performance_analysis['sentiment_accuracy']:.3f}")
        print(f"   TimeSeries RMSE: {performance_analysis['timeseries_rmse']:.3f}")
        print(f"   RL Reward: {performance_analysis['rl_reward']:.1f}")
        print(f"   Overall Score: {performance_analysis['overall_score']:.3f}")
        
        weak_areas = performance_analysis.get("weak_areas", [])
        strong_areas = performance_analysis.get("strong_areas", [])
        
        print(f"   Weak Areas: {len(weak_areas)} - {', '.join(weak_areas)}")
        print(f"   Strong Areas: {len(strong_areas)} - {', '.join(strong_areas)}")
        
        improvement_potential = performance_analysis.get("improvement_potential", {})
        if improvement_potential:
            print(f"   Improvement Potential:")
            for area, potential in improvement_potential.items():
                print(f"     - {area}: {potential:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance analysis test failed: {e}")
        return False

def test_system_update():
    """تست به‌روزرسانی سیستم"""
    print("\n🔄 Testing System Update...")
    
    try:
        from core.ai_brain_controller import AISystemController
        
        ai_brain = AISystemController()
        
        # Create mock selection results
        mock_selection_results = {
            "selected_models": {
                "sentiment": {
                    "name": "FinBERT",
                    "performance": {"accuracy": 0.85},
                    "selection_score": 0.82
                },
                "timeseries": {
                    "name": "LSTM_TimeSeries",
                    "performance": {"rmse": 0.03},
                    "selection_score": 0.78
                }
            }
        }
        
        # Test system update
        update_results = ai_brain._update_system_with_best_models(mock_selection_results)
        
        updated_models = update_results.get("updated_models", [])
        print(f"✅ System updated with {len(updated_models)} models")
        
        deployment_status = update_results.get("deployment_status", {})
        for model, status in deployment_status.items():
            print(f"   {model}: {status}")
        
        # Check system status
        print(f"✅ System performance score: {ai_brain.system_status['performance_score']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ System update test failed: {e}")
        return False

def main():
    """اجرای تست‌های کامل"""
    print("🧠 AI BRAIN CONTROLLER TRAINING TESTS")
    print("=" * 60)
    
    tests = [
        ("AI Brain Training Management", test_ai_brain_training_management),
        ("Training Decision Making", test_training_decision_making),
        ("Model Selection Intelligence", test_model_selection_intelligence),
        ("Performance Analysis", test_performance_analysis),
        ("System Update", test_system_update)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
        
        print()  # Empty line between tests
    
    # Summary
    print("📊 AI BRAIN TRAINING TEST RESULTS")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    success_rate = (passed / total) * 100
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Success Rate: {success_rate:.1f}% ({passed}/{total})")
    
    if success_rate >= 80:
        print("🎉 AI Brain Controller training management is working excellently!")
    elif success_rate >= 60:
        print("⚠️ AI Brain Controller mostly working, minor issues remain")
    else:
        print("❌ AI Brain Controller needs significant improvements")
    
    # Save results
    results_file = f"ai_brain_training_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            "test_results": results,
            "success_rate": success_rate,
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total,
                "passed_tests": passed,
                "failed_tests": total - passed
            }
        }, f, indent=2, ensure_ascii=False)
    
    print(f"📄 Detailed results saved to: {results_file}")
    
    return success_rate

if __name__ == "__main__":
    success_rate = main()
    exit(0 if success_rate >= 80 else 1)
