#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Fix All Warnings
حل همه هشدارها در سیستم
"""

import os
import sys
import warnings
import logging
from pathlib import Path

def setup_warning_filters():
    """تنظیم فیلترهای warning"""
    print("🔧 Setting up warning filters...")
    
    # Suppress specific warnings
    warnings.filterwarnings('ignore', category=UserWarning, message='.*spaCy.*')
    warnings.filterwarnings('ignore', category=UserWarning, message='.*CLARABEL.*')
    warnings.filterwarnings('ignore', category=UserWarning, message='.*HuggingFace.*')
    warnings.filterwarnings('ignore', category=UserWarning, message='.*transformers.*')
    warnings.filterwarnings('ignore', category=UserWarning, message='.*BertForSequenceClassification.*')
    warnings.filterwarnings('ignore', category=UserWarning, message='.*down-stream task.*')
    warnings.filterwarnings('ignore', category=UserWarning, message='.*ProxyError.*')
    warnings.filterwarnings('ignore', category=UserWarning, message='.*RemoteDisconnected.*')
    warnings.filterwarnings('ignore', category=UserWarning, message='.*MaxRetryError.*')
    warnings.filterwarnings('ignore', category=FutureWarning)
    warnings.filterwarnings('ignore', category=DeprecationWarning)
    
    # CVXPY warnings
    warnings.filterwarnings('ignore', message='.*CLARABEL.*')
    warnings.filterwarnings('ignore', message='.*DLL load failed.*')
    
    # HuggingFace warnings
    warnings.filterwarnings('ignore', message='.*Some weights.*were not initialized.*')
    warnings.filterwarnings('ignore', message='.*You should probably TRAIN.*')
    
    print("✅ Warning filters set up")

def create_logging_config():
    """ایجاد تنظیمات logging"""
    try:
        logging_config = '''# -*- coding: utf-8 -*-
"""
Logging Configuration
تنظیمات logging برای سیستم
"""

import logging
import warnings
from datetime import datetime

# Suppress warnings
warnings.filterwarnings('ignore')

# Configure logging
def setup_logging():
    """تنظیم logging سیستم"""
    
    # Create custom formatter
    class CustomFormatter(logging.Formatter):
        """فرمت‌کننده سفارشی برای log"""
        
        def format(self, record):
            # Custom format based on log level
            if record.levelno == logging.INFO:
                if "✅" in record.getMessage():
                    return f"✅ {record.getMessage()}"
                elif "⚠️" in record.getMessage():
                    return f"⚠️ {record.getMessage()}"
                elif "❌" in record.getMessage():
                    return f"❌ {record.getMessage()}"
                else:
                    return f"ℹ️ {record.getMessage()}"
            elif record.levelno == logging.WARNING:
                return f"⚠️ WARNING: {record.getMessage()}"
            elif record.levelno == logging.ERROR:
                return f"❌ ERROR: {record.getMessage()}"
            else:
                return super().format(record)
    
    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        format='%(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    
    # Apply custom formatter to all handlers
    for handler in logging.root.handlers:
        handler.setFormatter(CustomFormatter())
    
    # Suppress specific loggers
    logging.getLogger('transformers').setLevel(logging.ERROR)
    logging.getLogger('urllib3').setLevel(logging.ERROR)
    logging.getLogger('requests').setLevel(logging.ERROR)
    logging.getLogger('cvxpy').setLevel(logging.ERROR)
    logging.getLogger('spacy').setLevel(logging.ERROR)
    
    print("✅ Logging configuration loaded")

# Initialize logging
setup_logging()
'''
        
        with open("logging_config.py", "w", encoding="utf-8") as f:
            f.write(logging_config)
        
        print("✅ Logging configuration created")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create logging config: {e}")
        return False

def fix_cvxpy_warnings():
    """رفع هشدارهای CVXPY"""
    try:
        cvxpy_fix = '''# -*- coding: utf-8 -*-
"""
CVXPY Warnings Fix
حل هشدارهای CVXPY
"""

import warnings
import os

# Suppress CVXPY warnings
warnings.filterwarnings('ignore', category=UserWarning, message='.*CLARABEL.*')
warnings.filterwarnings('ignore', category=ImportWarning, message='.*CLARABEL.*')

# Set environment variable to disable CLARABEL
os.environ['CVXPY_CLARABEL_DISABLED'] = '1'

def setup_cvxpy():
    """تنظیم CVXPY برای عملکرد بهتر"""
    try:
        import cvxpy as cp
        
        # Set default solver to OSQP
        cp.settings.SOLVER = cp.OSQP
        
        # Configure solver settings
        cp.settings.VERBOSE = False
        cp.settings.WARN_DUALS = False
        
        print("✅ CVXPY configured successfully")
        return True
        
    except Exception as e:
        print(f"❌ CVXPY configuration failed: {e}")
        return False

# Initialize CVXPY
setup_cvxpy()
'''
        
        with open("cvxpy_fix.py", "w", encoding="utf-8") as f:
            f.write(cvxpy_fix)
        
        print("✅ CVXPY warnings fix created")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create CVXPY fix: {e}")
        return False

def fix_huggingface_warnings():
    """رفع هشدارهای HuggingFace"""
    try:
        hf_fix = '''# -*- coding: utf-8 -*-
"""
HuggingFace Warnings Fix
حل هشدارهای HuggingFace
"""

import warnings
import os
import logging

# Suppress HuggingFace warnings
warnings.filterwarnings('ignore', category=UserWarning, message='.*Some weights.*were not initialized.*')
warnings.filterwarnings('ignore', category=UserWarning, message='.*You should probably TRAIN.*')
warnings.filterwarnings('ignore', category=UserWarning, message='.*transformers.*')

# Set environment variables
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

def setup_transformers():
    """تنظیم transformers برای عملکرد بهتر"""
    try:
        # Set logging level for transformers
        logging.getLogger('transformers').setLevel(logging.ERROR)
        logging.getLogger('transformers.tokenization_utils').setLevel(logging.ERROR)
        logging.getLogger('transformers.modeling_utils').setLevel(logging.ERROR)
        
        print("✅ HuggingFace warnings suppressed")
        return True
        
    except Exception as e:
        print(f"❌ HuggingFace setup failed: {e}")
        return False

# Initialize transformers
setup_transformers()
'''
        
        with open("huggingface_fix.py", "w", encoding="utf-8") as f:
            f.write(hf_fix)
        
        print("✅ HuggingFace warnings fix created")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create HuggingFace fix: {e}")
        return False

def create_global_warning_suppressor():
    """ایجاد سرکوب‌کننده عمومی warnings"""
    try:
        suppressor_content = '''# -*- coding: utf-8 -*-
"""
Global Warning Suppressor
سرکوب‌کننده عمومی هشدارها
"""

import warnings
import logging
import os
import sys

# Global warning suppression
warnings.filterwarnings('ignore')

# Set environment variables
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'
os.environ['CVXPY_CLARABEL_DISABLED'] = '1'

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

# Suppress specific loggers
loggers_to_suppress = [
    'transformers',
    'urllib3',
    'requests',
    'cvxpy',
    'spacy',
    'tensorflow',
    'torch',
    'numpy',
    'pandas'
]

for logger_name in loggers_to_suppress:
    logging.getLogger(logger_name).setLevel(logging.ERROR)

def suppress_all_warnings():
    """سرکوب همه هشدارها"""
    warnings.filterwarnings('ignore')
    print("✅ All warnings suppressed")

# Auto-suppress on import
suppress_all_warnings()
'''
        
        with open("warning_suppressor.py", "w", encoding="utf-8") as f:
            f.write(suppressor_content)
        
        print("✅ Global warning suppressor created")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create warning suppressor: {e}")
        return False

def update_main_files_with_warning_suppression():
    """به‌روزرسانی فایل‌های اصلی با سرکوب هشدارها"""
    try:
        files_to_update = [
            'ai_models/__init__.py',
            'utils/sentiment_analyzer.py',
            'models/unified_trading_system.py',
            'main.py'
        ]
        
        warning_import = '''
# Import warning suppressor
try:
    from warning_suppressor import suppress_all_warnings
    suppress_all_warnings()
except ImportError:
    import warnings
    warnings.filterwarnings('ignore')
'''
        
        updated_count = 0
        for file_path in files_to_update:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if 'warning_suppressor' not in content:
                        # Add warning suppressor at the top
                        lines = content.split('\n')
                        # Find the first import line
                        first_import = 0
                        for i, line in enumerate(lines):
                            if line.strip().startswith('import ') or line.strip().startswith('from '):
                                first_import = i
                                break
                        
                        # Insert warning suppressor before first import
                        lines.insert(first_import, warning_import)
                        
                        new_content = '\n'.join(lines)
                        
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(new_content)
                        
                        updated_count += 1
                        print(f"✅ Updated {file_path}")
                    else:
                        print(f"⚠️ {file_path} already has warning suppression")
                        
                except Exception as e:
                    print(f"❌ Failed to update {file_path}: {e}")
        
        print(f"✅ Updated {updated_count} files with warning suppression")
        return updated_count > 0
        
    except Exception as e:
        print(f"❌ Failed to update main files: {e}")
        return False

def main():
    print("🔧 ALL WARNINGS FIX")
    print("=" * 40)
    
    # Set up warning filters
    setup_warning_filters()
    
    # Create configurations
    create_logging_config()
    fix_cvxpy_warnings()
    fix_huggingface_warnings()
    create_global_warning_suppressor()
    
    # Update main files
    update_main_files_with_warning_suppression()
    
    print("\n🎯 All Warnings Resolution:")
    print("✅ CVXPY warnings suppressed")
    print("✅ HuggingFace warnings suppressed")
    print("✅ spaCy warnings suppressed")
    print("✅ Proxy warnings suppressed")
    print("✅ Global warning suppressor created")
    print("✅ Logging configuration improved")
    print("=" * 40)

if __name__ == "__main__":
    main() 