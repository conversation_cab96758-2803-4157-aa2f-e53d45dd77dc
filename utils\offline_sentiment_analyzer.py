#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سیستم تحلیل احساسات آفلاین
این ماژول بدون نیاز به اینترنت کار می‌کند
"""

import re
import json
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

class SentimentType(Enum):
    """انواع احساسات"""
    VERY_POSITIVE = "very_positive"
    POSITIVE = "positive"
    NEUTRAL = "neutral"
    NEGATIVE = "negative"
    VERY_NEGATIVE = "very_negative"

@dataclass
class SentimentResult:
    """نتیجه تحلیل احساسات"""
    text: str
    language: str
    label: str
    score: float
    confidence: float
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class OfflineSentimentAnalyzer:
    """تحلیلگر احساسات آفلاین"""
    
    def __init__(self):
        # کلمات کلیدی مثبت فارسی
        self.persian_positive_words = {
            'سود', 'افزایش', 'رشد', 'مثبت', 'خوب', 'عالی', 'برتر', 'قوی', 'بالا',
            'بهبود', 'پیشرفت', 'موفق', 'سودآور', 'مفید', 'سودمند', 'مطلوب',
            'مثبت', 'خوب', 'عالی', 'برتر', 'قوی', 'بالا', 'بهبود', 'پیشرفت',
            'موفق', 'سودآور', 'مفید', 'سودمند', 'مطلوب', 'سودده', 'سودآور',
            'سودمند', 'مفید', 'مطلوب', 'خوب', 'عالی', 'برتر', 'قوی', 'بالا'
        }
        
        # کلمات کلیدی منفی فارسی
        self.persian_negative_words = {
            'ضرر', 'کاهش', 'افت', 'منفی', 'بد', 'ضعیف', 'پایین', 'بدتر',
            'افت', 'کاهش', 'ضعیف', 'ناموفق', 'زیان', 'مضر', 'نامطلوب',
            'منفی', 'بد', 'ضعیف', 'پایین', 'بدتر', 'افت', 'کاهش', 'ضعیف',
            'ناموفق', 'زیان', 'مضر', 'نامطلوب', 'زیان‌ده', 'زیان‌آور',
            'زیانمند', 'مضر', 'نامطلوب', 'بد', 'ضعیف', 'پایین', 'بدتر'
        }
        
        # کلمات کلیدی مثبت انگلیسی
        self.english_positive_words = {
            'profit', 'increase', 'growth', 'positive', 'good', 'excellent', 'strong', 'high',
            'improve', 'progress', 'success', 'profitable', 'beneficial', 'favorable',
            'gain', 'rise', 'up', 'better', 'best', 'outstanding', 'superior',
            'advantage', 'opportunity', 'potential', 'promising', 'optimistic'
        }
        
        # کلمات کلیدی منفی انگلیسی
        self.english_negative_words = {
            'loss', 'decrease', 'decline', 'negative', 'bad', 'weak', 'low', 'worse',
            'fall', 'drop', 'weak', 'unsuccessful', 'harmful', 'unfavorable',
            'damage', 'risk', 'danger', 'problem', 'issue', 'concern', 'worry',
            'pessimistic', 'bearish', 'crash', 'collapse', 'bankruptcy'
        }
        
        # کلمات تقویت‌کننده
        self.intensifiers = {
            'خیلی', 'بسیار', 'شدید', 'کاملاً', 'کاملا', 'کاملاً', 'کاملا',
            'very', 'extremely', 'highly', 'completely', 'absolutely', 'totally'
        }
        
        # کلمات تضعیف‌کننده
        self.negators = {
            'نه', 'نمی', 'نمی‌', 'نمی‌', 'نمی', 'نمی‌', 'نمی', 'نمی‌',
            'not', 'no', 'never', 'none', 'neither', 'nor'
        }
    
    def detect_language(self, text: str) -> str:
        """تشخیص زبان متن"""
        # شمارش کاراکترهای فارسی
        persian_chars = len(re.findall(r'[\u0600-\u06FF]', text))
        # شمارش کاراکترهای انگلیسی
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        
        if persian_chars > english_chars:
            return 'fa'
        elif english_chars > persian_chars:
            return 'en'
        else:
            # اگر مساوی بود، بر اساس کلمات تصمیم بگیر
            persian_words = len(re.findall(r'[\u0600-\u06FF]+', text))
            english_words = len(re.findall(r'[a-zA-Z]+', text))
            return 'fa' if persian_words >= english_words else 'en'
    
    def clean_text(self, text: str) -> str:
        """پاک‌سازی متن"""
        # حذف کاراکترهای خاص
        text = re.sub(r'[^\w\s\u0600-\u06FF]', ' ', text)
        # حذف فاصله‌های اضافی
        text = re.sub(r'\s+', ' ', text)
        return text.strip().lower()
    
    def analyze_sentiment(self, text: str, language: str) -> Tuple[str, float, float]:
        """تحلیل احساسات متن"""
        cleaned_text = self.clean_text(text)
        words = cleaned_text.split()
        
        positive_score = 0
        negative_score = 0
        intensifier_count = 0
        negator_count = 0
        
        # انتخاب کلمات کلیدی بر اساس زبان
        if language == 'fa':
            positive_words = self.persian_positive_words
            negative_words = self.persian_negative_words
        else:
            positive_words = self.english_positive_words
            negative_words = self.english_negative_words
        
        # تحلیل کلمات
        for i, word in enumerate(words):
            # بررسی کلمات مثبت
            if word in positive_words:
                score = 1.0
                # بررسی تقویت‌کننده‌ها
                if i > 0 and words[i-1] in self.intensifiers:
                    score *= 1.5
                    intensifier_count += 1
                positive_score += score
            
            # بررسی کلمات منفی
            elif word in negative_words:
                score = 1.0
                # بررسی تقویت‌کننده‌ها
                if i > 0 and words[i-1] in self.intensifiers:
                    score *= 1.5
                    intensifier_count += 1
                negative_score += score
            
            # بررسی نفی‌کننده‌ها
            if word in self.negators:
                negator_count += 1
        
        # اعمال نفی‌کننده‌ها
        if negator_count > 0:
            positive_score, negative_score = negative_score, positive_score
        
        # محاسبه امتیاز نهایی
        total_score = positive_score + negative_score
        if total_score == 0:
            return 'neutral', 0.5, 0.3
        
        # محاسبه نسبت
        if positive_score > negative_score:
            ratio = positive_score / total_score
            if ratio > 0.7:
                label = 'positive'
            elif ratio > 0.6:
                label = 'positive'
            else:
                label = 'neutral'
        elif negative_score > positive_score:
            ratio = negative_score / total_score
            if ratio > 0.7:
                label = 'negative'
            elif ratio > 0.6:
                label = 'negative'
            else:
                label = 'neutral'
        else:
            label = 'neutral'
        
        # محاسبه confidence
        confidence = min(0.9, total_score / len(words) * 2)
        
        return label, ratio, confidence
    
    def analyze(self, text: str, language: Optional[str] = None) -> SentimentResult:
        """تحلیل احساسات متن"""
        if not text or not text.strip():
            return SentimentResult(
                text=text,
                language='unknown',
                label='neutral',
                score=0.5,
                confidence=0.0
            )
        
        # تشخیص زبان
        if language is None:
            language = self.detect_language(text)
        
        # تحلیل احساسات
        label, score, confidence = self.analyze_sentiment(text, language)
        
        return SentimentResult(
            text=text,
            language=language,
            label=label,
            score=score,
            confidence=confidence
        )
    
    def batch_analyze(self, texts: List[str]) -> List[SentimentResult]:
        """تحلیل دسته‌ای متون"""
        results = []
        for text in texts:
            result = self.analyze(text)
            results.append(result)
        return results

# کلاس اصلی برای سازگاری
class SentimentAnalyzer(OfflineSentimentAnalyzer):
    """کلاس اصلی Sentiment Analyzer برای سازگاری"""
    
    def __init__(self):
        super().__init__()
        print("✅ Offline Sentiment Analyzer initialized successfully")
    
    def get_sentiment_score(self, text: str, language: Optional[str] = None) -> float:
        """دریافت امتیاز احساسات"""
        result = self.analyze(text, language)
        # تبدیل label به امتیاز
        score_mapping = {
            'very_positive': 0.9,
            'positive': 0.7,
            'neutral': 0.5,
            'negative': 0.3,
            'very_negative': 0.1
        }
        return score_mapping.get(result.label, 0.5)

if __name__ == "__main__":
    # تست سیستم
    analyzer = SentimentAnalyzer()
    
    test_texts = [
        "شرکت اپل سود بالایی گزارش داد",
        "بازار سهام امروز افت کرد",
        "Apple reported high profits",
        "Stock market fell today",
        "شرکت در وضعیت خوبی قرار دارد",
        "The company is doing well"
    ]
    
    print("🧪 تست سیستم تحلیل احساسات آفلاین:")
    print("=" * 50)
    
    for text in test_texts:
        result = analyzer.analyze(text)
        print(f"📝 متن: {text[:30]}...")
        print(f"🌍 زبان: {result.language}")
        print(f"📊 احساس: {result.label}")
        print(f"🎯 امتیاز: {result.score:.3f}")
        print(f"🔒 اطمینان: {result.confidence:.3f}")
        print("-" * 30) 