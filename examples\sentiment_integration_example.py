#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Example script demonstrating how to use the SentimentIntegrator class
to combine sentiment signals with technical and fundamental factors.
"""

import sys
import os
import datetime
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any

# Add the parent directory to the path so we can import the modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.sentiment_integrator import SentimentIntegrator
from utils.sentiment_analyzer import SentimentAnalyzer
from utils.source_credibility import SourceCredibility
from utils.news_volume_analyzer import NewsVolumeAnalyzer

print("Sentiment Integration Example")
print("============================")

# Initialize the integrator
integrator = SentimentIntegrator()

# Asset we're analyzing
asset = "AAPL"

# Generate a simple signal
signal = integrator.get_integrated_signal(
    asset=asset,
    technical_data={'trend': 0.5, 'momentum': 0.3, 'volatility': -0.2, 'volume': 0.1},
    fundamental_data={'valuation': -0.3, 'growth': 0.6, 'quality': 0.4}
)

# Print signal information
print(f"Signal = {signal['overall_signal']:.3f}, Confidence = {signal['confidence']:.3f}")
print("Component signals:")
for component, value in signal['component_signals'].items():
    print(f"  {component}: {value:.3f}")

print("\nDone!")
