#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Integrated Trading System
سیستم معاملاتی یکپارچه با اهداف سود و مدیریت ریسک خاص

هدف: سود روزانه $5 از هر نماد با سرمایه $1000
حداکثر ضرر: 10% کل، 4% روزانه
اهداف: $5 روزانه، $30 هفتگی، $80 ماهانه
"""

import os
import sys
import json
import asyncio
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import threading
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from portfolio.smart_portfolio_manager import SmartPortfolioManager
from portfolio.advanced_risk_manager import RiskParameters
from models.unified_trading_system import UnifiedTradingSystem
from core.base import TradingSignal
from utils.adaptive_plutus_system import AdaptivePlutusSystem

class IntegratedTradingSystem:
    """سیستم معاملاتی یکپارچه"""
    
    def __init__(self):
        print("🚀 Initializing Integrated Trading System...")
        
        # تنظیمات ریسک بر اساس درخواست کاربر
        self.risk_params = RiskParameters(
            initial_capital=1000.0,           # سرمایه $1000
            max_drawdown_percent=10.0,        # حداکثر ضرر 10%
            daily_loss_limit_percent=4.0,     # حداکثر ضرر روزانه 4%
            daily_profit_target_per_symbol=5.0, # حداقل $5 روزانه هر نماد
            weekly_profit_target=30.0,        # حداقل $30 هفتگی
            monthly_profit_target=80.0,       # حداقل $80 ماهانه
            risk_per_trade_percent=2.0,       # 2% ریسک هر معامله
            max_positions=5,                  # حداکثر 5 موقعیت
            stop_loss_percent=1.5,            # 1.5% stop loss
            take_profit_percent=3.0           # 3% take profit
        )
        
        # اجزای اصلی سیستم
        self.portfolio_manager = SmartPortfolioManager(self.risk_params)
        self.trading_system = UnifiedTradingSystem()
        self.plutus_system = AdaptivePlutusSystem()
        
        # تنظیمات عملیاتی
        self.symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD"]
        self.active_symbols = []
        self.signal_history = []
        self.performance_log = []
        
        # وضعیت سیستم
        self.system_running = False
        self.auto_trading_enabled = True
        self.monitoring_enabled = True
        
        # آمار عملکرد
        self.session_stats = {
            "start_time": None,
            "total_signals": 0,
            "successful_trades": 0,
            "failed_trades": 0,
            "total_profit": 0.0,
            "best_symbol": None,
            "worst_symbol": None,
            "achievement_times": {
                "daily": None,
                "weekly": None,
                "monthly": None
            }
        }
        
        print("✅ Integrated Trading System initialized successfully")
    
    def start_system(self):
        """شروع سیستم"""
        print("\n🚀 STARTING INTEGRATED TRADING SYSTEM")
        print("=" * 60)
        
        self.system_running = True
        self.session_stats["start_time"] = datetime.now()
        
        # شروع portfolio manager
        self.portfolio_manager.start_auto_trading()
        
        # شروع monitoring
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        
        # شروع signal generation
        self.signal_thread = threading.Thread(target=self._signal_generation_loop)
        self.signal_thread.daemon = True
        self.signal_thread.start()
        
        print("✅ System started successfully")
        print(f"💰 Initial Capital: ${self.risk_params.initial_capital}")
        print(f"🎯 Daily Target: ${self.risk_params.daily_profit_target_per_symbol} per symbol")
        print(f"📊 Weekly Target: ${self.risk_params.weekly_profit_target}")
        print(f"🚀 Monthly Target: ${self.risk_params.monthly_profit_target}")
        print("=" * 60)
        
        return True
    
    def stop_system(self):
        """توقف سیستم"""
        print("\n⏹️ STOPPING INTEGRATED TRADING SYSTEM")
        
        self.system_running = False
        self.portfolio_manager.stop_auto_trading()
        
        # ذخیره وضعیت نهایی
        self.save_session_report()
        
        print("✅ System stopped successfully")
        return True
    
    def _signal_generation_loop(self):
        """حلقه تولید سیگنال"""
        
        while self.system_running:
            try:
                # تولید سیگنال برای هر نماد
                for symbol in self.symbols:
                    signal = self._generate_signal_for_symbol(symbol)
                    
                    if signal:
                        # ثبت سیگنال
                        self.signal_history.append(signal)
                        self.session_stats["total_signals"] += 1
                        
                        # ارسال به portfolio manager
                        success = self.portfolio_manager.add_trading_signal(signal)
                        
                        if success:
                            print(f"📊 Signal generated for {symbol}: {signal.action} @ {signal.price:.4f}")
                
                # استراحت 30 ثانیه
                time.sleep(30)
                
            except Exception as e:
                print(f"❌ Error in signal generation: {e}")
                time.sleep(5)
    
    def _generate_signal_for_symbol(self, symbol: str) -> Optional[TradingSignal]:
        """تولید سیگنال برای نماد"""
        
        try:
            # بررسی اینکه آیا می‌توان موقعیت باز کرد
            can_trade = self.portfolio_manager.risk_manager.check_risk_limits()
            
            if not can_trade["max_drawdown_ok"] or not can_trade["daily_loss_ok"]:
                return None
            
            # تولید سیگنال تصادفی (در عمل از مدل واقعی می‌آید)
            import random
            
            # شبیه‌سازی قیمت
            if symbol == "EURUSD":
                price = 1.0850 + random.uniform(-0.01, 0.01)
            elif symbol == "GBPUSD":
                price = 1.2650 + random.uniform(-0.01, 0.01)
            elif symbol == "USDJPY":
                price = 150.50 + random.uniform(-0.5, 0.5)
            elif symbol == "AUDUSD":
                price = 0.6750 + random.uniform(-0.01, 0.01)
            elif symbol == "USDCAD":
                price = 1.3550 + random.uniform(-0.01, 0.01)
            else:
                price = 1.0000
            
            # شبیه‌سازی سیگنال
            confidence = random.uniform(0.6, 0.95)
            action = random.choice(["buy", "sell"])
            
            # فقط سیگنال‌های با confidence بالا
            if confidence < 0.75:
                return None
            
            # فقط اگر از قبل موقعیت نداریم
            if symbol in self.portfolio_manager.risk_manager.positions:
                return None
            
            signal = TradingSignal(
                symbol=symbol,
                action=action,
                confidence=confidence,
                price=price,
                timestamp=datetime.now(),
                reasoning=f"Generated signal based on market analysis"
            )
            
            return signal
            
        except Exception as e:
            print(f"❌ Error generating signal for {symbol}: {e}")
            return None
    
    def _monitoring_loop(self):
        """حلقه نظارت"""
        
        while self.system_running:
            try:
                # بررسی وضعیت کلی
                self._check_system_health()
                
                # بررسی اهداف سود
                self._check_profit_achievements()
                
                # نمایش گزارش دوره‌ای
                self._display_periodic_report()
                
                # استراحت 10 ثانیه
                time.sleep(10)
                
            except Exception as e:
                print(f"❌ Error in monitoring: {e}")
                time.sleep(5)
    
    def _check_system_health(self):
        """بررسی سلامت سیستم"""
        
        portfolio_status = self.portfolio_manager.get_comprehensive_report()
        
        # بررسی ریسک
        if portfolio_status["system_status"]["risk_level"] == "extreme":
            print("🚨 EXTREME RISK LEVEL - Stopping new positions")
            self.auto_trading_enabled = False
        elif portfolio_status["system_status"]["risk_level"] == "high":
            print("⚠️ HIGH RISK LEVEL - Reducing position size")
        
        # بررسی سرمایه
        current_capital = portfolio_status["portfolio_status"]["capital"]["current"]
        if current_capital < self.risk_params.initial_capital * 0.5:
            print("🚨 CAPITAL CRITICALLY LOW - Emergency stop")
            self.stop_system()
    
    def _check_profit_achievements(self):
        """بررسی دستیابی به اهداف سود"""
        
        report = self.portfolio_manager.get_comprehensive_report()
        
        # بررسی هدف روزانه
        if report["profit_targets"]["daily"]["achieved"]:
            if not self.session_stats["achievement_times"]["daily"]:
                self.session_stats["achievement_times"]["daily"] = datetime.now()
                print("🎉 DAILY TARGET ACHIEVED!")
                print(f"   Target: ${report['profit_targets']['daily']['target']:.2f}")
                print(f"   Achieved: ${report['profit_targets']['daily']['current']:.2f}")
        
        # بررسی هدف هفتگی
        if report["profit_targets"]["weekly"]["achieved"]:
            if not self.session_stats["achievement_times"]["weekly"]:
                self.session_stats["achievement_times"]["weekly"] = datetime.now()
                print("🎉 WEEKLY TARGET ACHIEVED!")
                print(f"   Target: ${report['profit_targets']['weekly']['target']:.2f}")
                print(f"   Achieved: ${report['profit_targets']['weekly']['current']:.2f}")
        
        # بررسی هدف ماهانه
        if report["profit_targets"]["monthly"]["achieved"]:
            if not self.session_stats["achievement_times"]["monthly"]:
                self.session_stats["achievement_times"]["monthly"] = datetime.now()
                print("🎉 MONTHLY TARGET ACHIEVED!")
                print(f"   Target: ${report['profit_targets']['monthly']['target']:.2f}")
                print(f"   Achieved: ${report['profit_targets']['monthly']['current']:.2f}")
    
    def _display_periodic_report(self):
        """نمایش گزارش دوره‌ای"""
        
        # هر 5 دقیقه گزارش نمایش
        if not hasattr(self, 'last_report_time'):
            self.last_report_time = datetime.now()
        
        if (datetime.now() - self.last_report_time).seconds > 300:  # 5 دقیقه
            self.display_current_status()
            self.last_report_time = datetime.now()
    
    def display_current_status(self):
        """نمایش وضعیت فعلی"""
        
        print("\n" + "="*60)
        print("📊 CURRENT SYSTEM STATUS")
        print("="*60)
        
        # گزارش پرتفولیو
        report = self.portfolio_manager.get_comprehensive_report()
        
        print(f"💰 Capital Status:")
        print(f"   Current: ${report['portfolio_status']['capital']['current']:.2f}")
        print(f"   Total PnL: ${report['portfolio_status']['capital']['total_pnl']:.2f}")
        print(f"   Daily PnL: ${report['portfolio_status']['capital']['daily_pnl']:.2f}")
        
        print(f"\n🎯 Progress to Targets:")
        print(f"   Daily: ${report['profit_targets']['daily']['current']:.2f} / ${report['profit_targets']['daily']['target']:.2f} ({report['profit_targets']['daily']['progress_percent']:.1f}%)")
        print(f"   Weekly: ${report['profit_targets']['weekly']['current']:.2f} / ${report['profit_targets']['weekly']['target']:.2f} ({report['profit_targets']['weekly']['progress_percent']:.1f}%)")
        print(f"   Monthly: ${report['profit_targets']['monthly']['current']:.2f} / ${report['profit_targets']['monthly']['target']:.2f} ({report['profit_targets']['monthly']['progress_percent']:.1f}%)")
        
        print(f"\n📈 Trading Statistics:")
        print(f"   Signals Received: {report['trading_stats']['signals_received']}")
        print(f"   Action Rate: {report['trading_stats']['action_rate']:.1f}%")
        print(f"   Open Positions: {report['portfolio_status']['positions']['open_positions']}")
        
        print(f"\n⚡ System Status:")
        print(f"   Risk Level: {report['system_status']['risk_level'].upper()}")
        print(f"   Auto Trading: {'ON' if report['system_status']['auto_trading_enabled'] else 'OFF'}")
        
        # بررسی زمان اجرا
        if self.session_stats['start_time']:
            print(f"   Running Time: {datetime.now() - self.session_stats['start_time']}")
        else:
            print(f"   Status: Ready to start")
        
        print("="*60)
    
    def manual_trade(self, symbol: str, action: str, confidence: float = 0.8):
        """معامله دستی"""
        
        # شبیه‌سازی قیمت
        price = self._get_current_price(symbol)
        
        signal = TradingSignal(
            symbol=symbol,
            action=action,
            confidence=confidence,
            price=price,
            timestamp=datetime.now(),
            reasoning="Manual trade"
        )
        
        success = self.portfolio_manager.add_trading_signal(signal)
        
        if success:
            executed = self.portfolio_manager.execute_best_opportunity()
            print(f"✅ Manual trade {'executed' if executed else 'queued'} for {symbol}")
            return executed
        else:
            print(f"❌ Manual trade rejected for {symbol}")
            return False
    
    def _get_current_price(self, symbol: str) -> float:
        """دریافت قیمت فعلی (شبیه‌سازی)"""
        
        base_prices = {
            "EURUSD": 1.0850,
            "GBPUSD": 1.2650,
            "USDJPY": 150.50,
            "AUDUSD": 0.6750,
            "USDCAD": 1.3550
        }
        
        return base_prices.get(symbol, 1.0000)
    
    def force_close_all_positions(self):
        """بستن اجباری تمام موقعیت‌ها"""
        
        print("🚨 FORCE CLOSING ALL POSITIONS")
        
        positions = self.portfolio_manager.risk_manager.positions.copy()
        
        for symbol, position in positions.items():
            current_price = self._get_current_price(symbol)
            success = self.portfolio_manager.risk_manager.close_position(
                symbol, current_price, "force_close"
            )
            
            if success:
                print(f"✅ Force closed position: {symbol}")
            else:
                print(f"❌ Failed to force close: {symbol}")
        
        return len(positions)
    
    def save_session_report(self):
        """ذخیره گزارش جلسه"""
        
        report = self.portfolio_manager.get_comprehensive_report()
        
        session_report = {
            "session_info": {
                "start_time": self.session_stats["start_time"].isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration": str(datetime.now() - self.session_stats["start_time"]),
                "total_signals": self.session_stats["total_signals"]
            },
            "financial_results": {
                "initial_capital": self.risk_params.initial_capital,
                "final_capital": report["portfolio_status"]["capital"]["current"],
                "total_pnl": report["portfolio_status"]["capital"]["total_pnl"],
                "daily_pnl": report["portfolio_status"]["capital"]["daily_pnl"],
                "weekly_pnl": report["portfolio_status"]["capital"]["weekly_pnl"],
                "monthly_pnl": report["portfolio_status"]["capital"]["monthly_pnl"]
            },
            "target_achievements": {
                "daily_achieved": report["profit_targets"]["daily"]["achieved"],
                "weekly_achieved": report["profit_targets"]["weekly"]["achieved"],
                "monthly_achieved": report["profit_targets"]["monthly"]["achieved"],
                "achievement_times": self.session_stats["achievement_times"]
            },
            "performance_metrics": {
                "win_rate": report["portfolio_status"]["performance"]["win_rate"],
                "total_trades": report["portfolio_status"]["performance"]["total_trades"],
                "avg_profit_per_trade": report["portfolio_status"]["performance"]["avg_profit_per_trade"],
                "max_drawdown": report["portfolio_status"]["risk"]["max_drawdown"],
                "final_risk_level": report["system_status"]["risk_level"]
            },
            "full_report": report
        }
        
        filename = f"trading_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w') as f:
            json.dump(session_report, f, indent=2, default=str)
        
        print(f"✅ Session report saved to {filename}")
        return filename

# اجرای سیستم
if __name__ == "__main__":
    print("🚀 INTEGRATED TRADING SYSTEM")
    print("=" * 60)
    print("🎯 TARGET: $5 daily profit per symbol")
    print("💰 CAPITAL: $1000")
    print("📊 RISK LIMITS: 10% max drawdown, 4% daily limit")
    print("🎯 GOALS: $5 daily, $30 weekly, $80 monthly")
    print("=" * 60)
    
    # ایجاد سیستم
    trading_system = IntegratedTradingSystem()
    
    # نمایش وضعیت اولیه
    trading_system.display_current_status()
    
    # تست معامله دستی
    print("\n🔍 Testing manual trade...")
    trading_system.manual_trade("EURUSD", "buy", 0.9)
    
    # نمایش وضعیت نهایی
    trading_system.display_current_status()
    
    print("\n🎯 System ready for automated trading!")
    print("Commands:")
    print("  trading_system.start_system() - Start automated trading")
    print("  trading_system.stop_system() - Stop system")
    print("  trading_system.display_current_status() - Show current status")
    print("  trading_system.manual_trade(symbol, action) - Manual trade")
    print("  trading_system.force_close_all_positions() - Emergency close all") 