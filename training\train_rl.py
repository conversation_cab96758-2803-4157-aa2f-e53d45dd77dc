"""
🤖 Reinforcement Learning Models Training for Pearl-3x7B
آموزش مدل‌های یادگیری تقویتی مخصوص Pearl-3x7B

مطابق نقشه راه گنج:
- آم<PERSON><PERSON><PERSON> PPO (Proximal Policy Optimization)
- آ<PERSON><PERSON><PERSON><PERSON> A2C (Advantage Actor-Critic)
- آ<PERSON>وزش TD3 (Twin Delayed Deep Deterministic Policy Gradient)
- آموزش DQN (Deep Q-Network)
- ادغام با سیستم لاگینگ و مدیریت حافظه
"""

import os
import sys
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from pathlib import Path
import json
from dataclasses import dataclass, field
import time
import gym
from collections import deque
import random
import copy
from sklearn.preprocessing import StandardScaler

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import Pearl-3x7B components
from core.logger import get_pearl_logger, TrainingSession, log_training_step
from core.memory_manager import memory_manager, prepare_memory_for_training, cleanup_after_training
from data.preprocessor import create_preprocessor_for_pearl

# Import HuggingFace sentiment models
try:
    from ai_models.sentiment_models import HuggingFaceSentimentManager
    SENTIMENT_MODELS_AVAILABLE = True
except ImportError:
    SENTIMENT_MODELS_AVAILABLE = False

# Import experience replay enhancements
try:
    from training.experience_replay_enhancement import (
        PrioritizedReplayBuffer,
        EnhancedDQNAgent,
        ReplayConfig,
        create_enhanced_replay_config,
        CuriosityModule
    )
    ENHANCED_REPLAY_AVAILABLE = True
except ImportError:
    ENHANCED_REPLAY_AVAILABLE = False

# Configure logging
logger = get_pearl_logger("rl_training")

@dataclass
class RLTrainingConfig:
    """پیکربندی آموزش مدل‌های یادگیری تقویتی"""
    model_name: str
    algorithm: str  # ppo, a2c, td3, dqn
    environment: str = "trading"
    state_dim: int = 20
    action_dim: int = 3  # buy, sell, hold
    hidden_dim: int = 256
    learning_rate: float = 3e-4
    batch_size: int = 64
    buffer_size: int = 100000
    num_episodes: int = 1000
    max_steps_per_episode: int = 1000
    update_frequency: int = 4
    target_update_frequency: int = 100
    gamma: float = 0.99
    tau: float = 0.005
    epsilon_start: float = 1.0
    epsilon_end: float = 0.01
    epsilon_decay: float = 0.995
    output_dir: str = "models/trained_models"
    save_frequency: int = 100

    # 🌍 Multi-Symbol Training Configuration
    symbols: List[str] = None  # List of symbols to train on
    symbol_rotation_strategy: str = "random"  # random, sequential, adaptive
    episodes_per_symbol: int = 100  # Episodes before switching symbol
    universal_features: bool = True  # Use normalized features across symbols
    cross_symbol_validation: bool = True  # Validate on different symbols

    # 🎯 Dynamic Reward Configuration
    dynamic_rewards: bool = True  # Enable dynamic reward system
    reward_components: Dict[str, float] = None  # Reward component weights
    market_condition_adaptation: bool = True  # Adapt to market conditions

    # 📈 Enhanced Training Configuration
    curriculum_learning: bool = False  # Enable curriculum learning
    curriculum_stages: int = 4  # Number of curriculum stages
    extended_episodes: int = 5000  # Extended training episodes

    # ⚡ Experience Replay Enhancement Configuration
    enhanced_replay: bool = True  # Enable enhanced experience replay
    prioritized_replay: bool = True  # Use prioritized experience replay
    multi_step_learning: int = 3  # Multi-step learning steps
    curiosity_driven: bool = True  # Enable curiosity-driven exploration
    replay_buffer_size: int = 200000  # Enhanced buffer size

    # 🤗 Sentiment Analysis Integration
    use_sentiment_analysis: bool = True  # Enable sentiment analysis
    sentiment_weight: float = 0.1  # Weight for sentiment in reward

    def __post_init__(self):
        """پس از مقداردهی اولیه"""
        if self.symbols is None:
            self.symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD"]

        if self.reward_components is None:
            self.reward_components = {
                "profit": 0.4,
                "risk_adjusted": 0.25,
                "drawdown_penalty": 0.2,
                "consistency": 0.15
            }

        # Adjust buffer size for enhanced replay
        if self.enhanced_replay:
            self.buffer_size = max(self.buffer_size, self.replay_buffer_size)

class MultiSymbolDataManager:
    """مدیر داده‌های چند نمادی"""

    def __init__(self, symbols: List[str], universal_features: bool = True):
        self.symbols = symbols
        self.universal_features = universal_features
        self.symbol_data = {}
        self.feature_scalers = {}
        self.current_symbol = None

        # Symbol characteristics for realistic data generation
        self.symbol_characteristics = {
            "EURUSD": {"base_price": 1.1000, "volatility": 0.0005, "trend": 0.00001},
            "GBPUSD": {"base_price": 1.3000, "volatility": 0.0008, "trend": 0.00002},
            "USDJPY": {"base_price": 110.00, "volatility": 0.05, "trend": 0.001},
            "AUDUSD": {"base_price": 0.7500, "volatility": 0.0006, "trend": -0.00001},
            "USDCAD": {"base_price": 1.2500, "volatility": 0.0004, "trend": 0.00001},
            "NZDUSD": {"base_price": 0.6800, "volatility": 0.0007, "trend": -0.00002},
            "USDCHF": {"base_price": 0.9200, "volatility": 0.0003, "trend": 0.00000},
            "EURGBP": {"base_price": 0.8500, "volatility": 0.0004, "trend": 0.00001},
            "EURJPY": {"base_price": 120.00, "volatility": 0.06, "trend": 0.002},
            "GBPJPY": {"base_price": 140.00, "volatility": 0.08, "trend": 0.003},
            "AUDJPY": {"base_price": 82.000, "volatility": 0.07, "trend": 0.001},
            "EURCHF": {"base_price": 1.0800, "volatility": 0.0002, "trend": 0.00000}
        }

        logger.logger.info(f"🌍 MultiSymbolDataManager initialized with {len(symbols)} symbols")

    def generate_symbol_data(self, symbol: str, timeframe: str = "H1") -> pd.DataFrame:
        """تولید داده برای یک نماد خاص"""

        if symbol in self.symbol_data:
            return self.symbol_data[symbol]

        logger.logger.info(f"📊 Generating data for {symbol}")

        # Get symbol characteristics
        char = self.symbol_characteristics.get(symbol, {
            "base_price": 1.0000,
            "volatility": 0.0005,
            "trend": 0.00000
        })

        # Generate dates
        dates = pd.date_range(start='2020-01-01', end='2023-01-01', freq='H')

        # Generate realistic financial time series
        np.random.seed(hash(symbol) % 2**32)  # Different seed for each symbol
        price_data = []
        base_price = char["base_price"]

        for i, date in enumerate(dates):
            # Add trend, seasonality, and noise specific to symbol
            trend = char["trend"] * i
            seasonality = char["volatility"] * 2 * np.sin(2 * np.pi * i / (24 * 7))
            noise = np.random.normal(0, char["volatility"])

            price_change = trend + seasonality + noise
            base_price += price_change

            # Generate OHLCV data
            open_price = base_price + np.random.normal(0, char["volatility"] * 0.2)
            high_price = open_price + abs(np.random.normal(0, char["volatility"] * 0.4))
            low_price = open_price - abs(np.random.normal(0, char["volatility"] * 0.4))
            close_price = open_price + np.random.normal(0, char["volatility"] * 0.2)
            volume = np.random.randint(1000, 10000)

            # Ensure OHLC relationships
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)

            # Scale prices for JPY pairs
            if "JPY" in symbol:
                decimal_places = 3
            else:
                decimal_places = 5

            price_data.append({
                'datetime': date,
                'open': round(open_price, decimal_places),
                'high': round(high_price, decimal_places),
                'low': round(low_price, decimal_places),
                'close': round(close_price, decimal_places),
                'volume': volume
            })

        df = pd.DataFrame(price_data)

        # Add technical indicators
        preprocessor = create_preprocessor_for_pearl()
        processed_df = preprocessor.preprocess(df, symbol, timeframe, create_datasets=False)

        # Store data
        self.symbol_data[symbol] = processed_df

        logger.logger.info(f"✅ Generated {len(processed_df)} data points for {symbol}")
        return processed_df

    def get_normalized_features(self, symbol: str) -> pd.DataFrame:
        """دریافت ویژگی‌های نرمال‌سازی شده"""

        if symbol not in self.symbol_data:
            self.generate_symbol_data(symbol)

        df = self.symbol_data[symbol].copy()

        if self.universal_features:
            # Universal feature normalization across symbols
            if symbol not in self.feature_scalers:
                from sklearn.preprocessing import StandardScaler
                self.feature_scalers[symbol] = StandardScaler()

                # Select numeric columns for normalization
                numeric_columns = df.select_dtypes(include=[np.number]).columns
                numeric_columns = [col for col in numeric_columns if col not in ['datetime']]

                if len(numeric_columns) > 0:
                    df[numeric_columns] = self.feature_scalers[symbol].fit_transform(df[numeric_columns])

        return df

    def get_symbol_rotation_sequence(self, strategy: str, total_episodes: int, episodes_per_symbol: int) -> List[str]:
        """دریافت توالی چرخش نمادها"""

        sequence = []

        if strategy == "sequential":
            # Sequential rotation
            for episode in range(total_episodes):
                symbol_index = (episode // episodes_per_symbol) % len(self.symbols)
                sequence.append(self.symbols[symbol_index])

        elif strategy == "random":
            # Random rotation
            np.random.seed(42)  # For reproducibility
            for episode in range(total_episodes):
                if episode % episodes_per_symbol == 0:
                    current_symbol = np.random.choice(self.symbols)
                sequence.append(current_symbol)

        elif strategy == "adaptive":
            # Adaptive rotation based on performance (simplified)
            symbol_weights = {symbol: 1.0 for symbol in self.symbols}
            for episode in range(total_episodes):
                if episode % episodes_per_symbol == 0:
                    # Choose symbol based on weights (higher weight = more likely)
                    weights = list(symbol_weights.values())
                    current_symbol = np.random.choice(self.symbols, p=np.array(weights)/sum(weights))
                sequence.append(current_symbol)

        else:
            # Default: cycle through symbols
            for episode in range(total_episodes):
                symbol_index = episode % len(self.symbols)
                sequence.append(self.symbols[symbol_index])

        return sequence

class DynamicRewardSystem:
    """سیستم پاداش پویا"""

    def __init__(self, reward_components: Dict[str, float], market_adaptation: bool = True):
        self.reward_components = reward_components
        self.market_adaptation = market_adaptation
        self.market_volatility = 0.0005  # Default volatility
        self.recent_performance = deque(maxlen=100)

        logger.logger.info(f"🎯 DynamicRewardSystem initialized with components: {reward_components}")

    def calculate_reward(self,
                        action: int,
                        prev_balance: float,
                        current_balance: float,
                        position: int,
                        price_change: float,
                        step: int,
                        max_steps: int) -> float:
        """محاسبه پاداش پویا"""

        # Base profit/loss
        profit_loss = current_balance - prev_balance

        # Component 1: Profit-based reward (40%)
        profit_reward = profit_loss * 0.01  # Scale profit

        # Component 2: Risk-adjusted returns (25%)
        risk_adjusted_reward = 0.0
        if len(self.recent_performance) > 10:
            performance_std = np.std(self.recent_performance)
            if performance_std > 0:
                risk_adjusted_reward = profit_loss / (performance_std + 1e-8)

        # Component 3: Drawdown penalty (20%)
        drawdown_penalty = 0.0
        if profit_loss < 0:
            drawdown_penalty = abs(profit_loss) * 0.02  # Penalty for losses

        # Component 4: Consistency bonus (15%)
        consistency_bonus = 0.0
        if len(self.recent_performance) >= 5:
            recent_profits = list(self.recent_performance)[-5:]
            if all(p >= 0 for p in recent_profits):
                consistency_bonus = 0.5  # Bonus for consistent profits

        # Combine components
        total_reward = (
            profit_reward * self.reward_components["profit"] +
            risk_adjusted_reward * self.reward_components["risk_adjusted"] -
            drawdown_penalty * self.reward_components["drawdown_penalty"] +
            consistency_bonus * self.reward_components["consistency"]
        )

        # Market condition adaptation
        if self.market_adaptation:
            volatility_factor = min(2.0, self.market_volatility / 0.0005)  # Normalize volatility
            total_reward *= volatility_factor

        # Store performance for future calculations
        self.recent_performance.append(profit_loss)

        return total_reward

    def update_market_conditions(self, price_data: pd.DataFrame):
        """به‌روزرسانی شرایط بازار"""
        if len(price_data) > 20:
            recent_prices = price_data['close'].tail(20)
            returns = recent_prices.pct_change().dropna()
            self.market_volatility = returns.std()

class TradingEnvironment:
    """محیط معاملاتی برای آموزش RL با پشتیبانی چند نمادی"""

    def __init__(self, data: pd.DataFrame, initial_balance: float = 10000.0,
                 symbol: str = "EURUSD", dynamic_reward_system: DynamicRewardSystem = None):
        self.data = data.reset_index(drop=True)
        self.initial_balance = initial_balance
        self.current_step = 0
        self.balance = initial_balance
        self.prev_balance = initial_balance
        self.position = 0  # 0: no position, 1: long, -1: short
        self.entry_price = 0
        self.max_steps = len(data) - 1
        self.symbol = symbol
        self.dynamic_reward_system = dynamic_reward_system

        # State features
        self.state_features = [
            'open', 'high', 'low', 'close', 'volume',
            'rsi_14', 'macd', 'sma_20', 'sma_50', 'atr_14'
        ]

        # Normalize data
        self.normalized_data = self._normalize_data()

        # Performance tracking
        self.trade_history = []
        self.balance_history = [initial_balance]

    def _normalize_data(self) -> pd.DataFrame:
        """نرمال‌سازی داده‌ها"""
        normalized = self.data.copy()
        for feature in self.state_features:
            if feature in normalized.columns:
                mean = normalized[feature].mean()
                std = normalized[feature].std()
                if std > 0:
                    normalized[feature] = (normalized[feature] - mean) / std
        return normalized

    def reset(self) -> np.ndarray:
        """ریست محیط"""
        self.current_step = 0
        self.balance = self.initial_balance
        self.position = 0
        self.entry_price = 0
        return self._get_state()

    def _get_state(self) -> np.ndarray:
        """دریافت وضعیت فعلی"""
        if self.current_step >= len(self.normalized_data):
            self.current_step = len(self.normalized_data) - 1

        # Market features
        market_features = []
        for feature in self.state_features:
            if feature in self.normalized_data.columns:
                market_features.append(self.normalized_data.iloc[self.current_step][feature])
            else:
                market_features.append(0.0)

        # Portfolio features
        portfolio_features = [
            self.balance / self.initial_balance,  # Normalized balance
            self.position,  # Current position
            (self.data.iloc[self.current_step]['close'] - self.entry_price) / self.entry_price if self.entry_price > 0 else 0
        ]

        # Combine features
        state = np.array(market_features + portfolio_features, dtype=np.float32)

        # Pad or truncate to fixed size
        if len(state) < 20:
            state = np.pad(state, (0, 20 - len(state)), 'constant')
        else:
            state = state[:20]

        return state

    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict]:
        """اجرای یک قدم در محیط با سیستم پاداش پویا"""
        if self.current_step >= self.max_steps:
            return self._get_state(), 0, True, {"final_balance": self.balance}

        # Store previous balance for reward calculation
        self.prev_balance = self.balance
        current_price = self.data.iloc[self.current_step]['close']

        # Execute action
        trade_executed = False
        if action == 0:  # Hold
            pass
        elif action == 1:  # Buy/Long
            if self.position <= 0:
                if self.position < 0:  # Close short position
                    profit = (self.entry_price - current_price) / self.entry_price
                    self.balance *= (1 + profit)
                    trade_executed = True
                    self.trade_history.append({
                        "type": "close_short",
                        "entry_price": self.entry_price,
                        "exit_price": current_price,
                        "profit": profit,
                        "balance": self.balance
                    })

                # Open long position
                self.position = 1
                self.entry_price = current_price

        elif action == 2:  # Sell/Short
            if self.position >= 0:
                if self.position > 0:  # Close long position
                    profit = (current_price - self.entry_price) / self.entry_price
                    self.balance *= (1 + profit)
                    trade_executed = True
                    self.trade_history.append({
                        "type": "close_long",
                        "entry_price": self.entry_price,
                        "exit_price": current_price,
                        "profit": profit,
                        "balance": self.balance
                    })

                # Open short position
                self.position = -1
                self.entry_price = current_price

        # Calculate unrealized P&L for open positions
        if self.position != 0:
            price_change = (current_price - self.entry_price) / self.entry_price
            if self.position == 1:  # Long position
                unrealized_pnl = price_change * self.balance * 0.1  # 10% position size
            else:  # Short position
                unrealized_pnl = -price_change * self.balance * 0.1

            # Update balance with unrealized P&L (for reward calculation only)
            temp_balance = self.balance + unrealized_pnl
        else:
            temp_balance = self.balance
            price_change = 0

        # Calculate reward using dynamic reward system
        if self.dynamic_reward_system:
            reward = self.dynamic_reward_system.calculate_reward(
                action=action,
                prev_balance=self.prev_balance,
                current_balance=temp_balance,
                position=self.position,
                price_change=price_change,
                step=self.current_step,
                max_steps=self.max_steps
            )
        else:
            # Fallback to simple reward
            reward = (temp_balance - self.prev_balance) * 0.01

        # Store balance history
        self.balance_history.append(self.balance)

        self.current_step += 1
        done = self.current_step >= self.max_steps

        return self._get_state(), reward, done, {'balance': self.balance, 'position': self.position}

class DQNNetwork(nn.Module):
    """شبکه DQN"""

    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 256):
        super(DQNNetwork, self).__init__()
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, hidden_dim)
        self.fc4 = nn.Linear(hidden_dim, action_dim)
        self.dropout = nn.Dropout(0.2)

    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = F.relu(self.fc3(x))
        x = self.fc4(x)
        return x

class ActorNetwork(nn.Module):
    """شبکه Actor برای الگوریتم‌های Actor-Critic"""

    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 256):
        super(ActorNetwork, self).__init__()
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, action_dim)
        self.dropout = nn.Dropout(0.2)

    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = F.softmax(self.fc3(x), dim=-1)
        return x

class CriticNetwork(nn.Module):
    """شبکه Critic برای الگوریتم‌های Actor-Critic"""

    def __init__(self, state_dim: int, hidden_dim: int = 256):
        super(CriticNetwork, self).__init__()
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, 1)
        self.dropout = nn.Dropout(0.2)

    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        return x

class ReplayBuffer:
    """بافر تجربه برای ذخیره تجربیات"""

    def __init__(self, capacity: int):
        self.buffer = deque(maxlen=capacity)

    def push(self, state, action, reward, next_state, done):
        self.buffer.append((state, action, reward, next_state, done))

    def sample(self, batch_size: int):
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return state, action, reward, next_state, done

    def __len__(self):
        return len(self.buffer)

class DQNAgent:
    """عامل DQN"""

    def __init__(self, config: RLTrainingConfig):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Networks
        self.q_network = DQNNetwork(config.state_dim, config.action_dim, config.hidden_dim).to(self.device)
        self.target_network = DQNNetwork(config.state_dim, config.action_dim, config.hidden_dim).to(self.device)
        self.optimizer = torch.optim.Adam(self.q_network.parameters(), lr=config.learning_rate)

        # Copy weights to target network
        self.target_network.load_state_dict(self.q_network.state_dict())

        # Replay buffer
        self.replay_buffer = ReplayBuffer(config.buffer_size)

        # Exploration
        self.epsilon = config.epsilon_start

    def select_action(self, state: np.ndarray, training: bool = True) -> int:
        """انتخاب عمل"""
        if training and random.random() < self.epsilon:
            return random.randint(0, self.config.action_dim - 1)

        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            q_values = self.q_network(state_tensor)
            return q_values.argmax().item()

    def update(self):
        """به‌روزرسانی شبکه"""
        if len(self.replay_buffer) < self.config.batch_size:
            return 0

        # Sample batch
        states, actions, rewards, next_states, dones = self.replay_buffer.sample(self.config.batch_size)

        states = torch.FloatTensor(states).to(self.device)
        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        next_states = torch.FloatTensor(next_states).to(self.device)
        dones = torch.BoolTensor(dones).to(self.device)

        # Current Q values
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))

        # Next Q values
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0]
            target_q_values = rewards + (self.config.gamma * next_q_values * ~dones)

        # Loss
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)

        # Optimize
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), max_norm=1.0)
        self.optimizer.step()

        # Update epsilon
        self.epsilon = max(self.config.epsilon_end, self.epsilon * self.config.epsilon_decay)

        return loss.item()

    def update_target_network(self):
        """به‌روزرسانی target network"""
        self.target_network.load_state_dict(self.q_network.state_dict())

class A2CAgent:
    """عامل A2C"""

    def __init__(self, config: RLTrainingConfig):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Networks
        self.actor = ActorNetwork(config.state_dim, config.action_dim, config.hidden_dim).to(self.device)
        self.critic = CriticNetwork(config.state_dim, config.hidden_dim).to(self.device)

        # Optimizers
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(), lr=config.learning_rate)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(), lr=config.learning_rate)

        # Storage
        self.states = []
        self.actions = []
        self.rewards = []
        self.values = []
        self.log_probs = []

    def select_action(self, state: np.ndarray) -> Tuple[int, float, float]:
        """انتخاب عمل"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)

        # Get action probabilities and value
        action_probs = self.actor(state_tensor)
        value = self.critic(state_tensor)

        # Sample action
        action_dist = torch.distributions.Categorical(action_probs)
        action = action_dist.sample()
        log_prob = action_dist.log_prob(action)

        return action.item(), log_prob.item(), value.item()

    def store_transition(self, state, action, reward, log_prob, value):
        """ذخیره تجربه"""
        self.states.append(state)
        self.actions.append(action)
        self.rewards.append(reward)
        self.log_probs.append(log_prob)
        self.values.append(value)

    def update(self):
        """به‌روزرسانی شبکه‌ها"""
        if len(self.states) == 0:
            return 0, 0

        # Calculate returns
        returns = []
        discounted_sum = 0
        for reward in reversed(self.rewards):
            discounted_sum = reward + self.config.gamma * discounted_sum
            returns.insert(0, discounted_sum)

        # Convert to tensors
        states = torch.FloatTensor(np.array(self.states)).to(self.device)
        actions = torch.LongTensor(self.actions).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        log_probs = torch.FloatTensor(self.log_probs).to(self.device)
        values = torch.FloatTensor(self.values).to(self.device)

        # Normalize returns
        returns = (returns - returns.mean()) / (returns.std() + 1e-8)

        # Calculate advantages
        advantages = returns - values

        # Actor loss
        actor_loss = -(log_probs * advantages.detach()).mean()

        # Critic loss
        critic_loss = F.mse_loss(values, returns)

        # Update actor
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.actor.parameters(), max_norm=1.0)
        self.actor_optimizer.step()

        # Update critic
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic.parameters(), max_norm=1.0)
        self.critic_optimizer.step()

        # Clear storage
        self.clear_storage()

        return actor_loss.item(), critic_loss.item()

    def clear_storage(self):
        """پاک کردن ذخیره‌سازی"""
        self.states.clear()
        self.actions.clear()
        self.rewards.clear()
        self.values.clear()
        self.log_probs.clear()

class PPOAgent:
    """عامل PPO (ساده‌سازی شده)"""

    def __init__(self, config: RLTrainingConfig):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Networks (similar to A2C but with additional features)
        self.actor = ActorNetwork(config.state_dim, config.action_dim, config.hidden_dim).to(self.device)
        self.critic = CriticNetwork(config.state_dim, config.hidden_dim).to(self.device)

        # Optimizers
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(), lr=config.learning_rate)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(), lr=config.learning_rate)

        # PPO specific parameters
        self.clip_epsilon = 0.2
        self.ppo_epochs = 4

        # Storage
        self.states = []
        self.actions = []
        self.rewards = []
        self.values = []
        self.log_probs = []

    def select_action(self, state: np.ndarray) -> Tuple[int, float, float]:
        """انتخاب عمل (مشابه A2C)"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)

        action_probs = self.actor(state_tensor)
        value = self.critic(state_tensor)

        action_dist = torch.distributions.Categorical(action_probs)
        action = action_dist.sample()
        log_prob = action_dist.log_prob(action)

        return action.item(), log_prob.item(), value.item()

    def store_transition(self, state, action, reward, log_prob, value):
        """ذخیره تجربه"""
        self.states.append(state)
        self.actions.append(action)
        self.rewards.append(reward)
        self.log_probs.append(log_prob)
        self.values.append(value)

    def update(self):
        """به‌روزرسانی PPO"""
        if len(self.states) == 0:
            return 0, 0

        # Calculate returns (similar to A2C)
        returns = []
        discounted_sum = 0
        for reward in reversed(self.rewards):
            discounted_sum = reward + self.config.gamma * discounted_sum
            returns.insert(0, discounted_sum)

        # Convert to tensors
        states = torch.FloatTensor(np.array(self.states)).to(self.device)
        actions = torch.LongTensor(self.actions).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        old_log_probs = torch.FloatTensor(self.log_probs).to(self.device)
        values = torch.FloatTensor(self.values).to(self.device)

        # Normalize returns
        returns = (returns - returns.mean()) / (returns.std() + 1e-8)
        advantages = returns - values

        # PPO update for multiple epochs
        total_actor_loss = 0
        total_critic_loss = 0

        for _ in range(self.ppo_epochs):
            # Get current action probabilities
            action_probs = self.actor(states)
            action_dist = torch.distributions.Categorical(action_probs)
            new_log_probs = action_dist.log_prob(actions)

            # Calculate ratio
            ratio = torch.exp(new_log_probs - old_log_probs.detach())

            # PPO clipped objective
            surr1 = ratio * advantages.detach()
            surr2 = torch.clamp(ratio, 1 - self.clip_epsilon, 1 + self.clip_epsilon) * advantages.detach()
            actor_loss = -torch.min(surr1, surr2).mean()

            # Critic loss
            current_values = self.critic(states).squeeze()
            critic_loss = F.mse_loss(current_values, returns)

            # Update actor
            self.actor_optimizer.zero_grad()
            actor_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.actor.parameters(), max_norm=1.0)
            self.actor_optimizer.step()

            # Update critic
            self.critic_optimizer.zero_grad()
            critic_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.critic.parameters(), max_norm=1.0)
            self.critic_optimizer.step()

            total_actor_loss += actor_loss.item()
            total_critic_loss += critic_loss.item()

        # Clear storage
        self.clear_storage()

        return total_actor_loss / self.ppo_epochs, total_critic_loss / self.ppo_epochs

    def clear_storage(self):
        """پاک کردن ذخیره‌سازی"""
        self.states.clear()
        self.actions.clear()
        self.rewards.clear()
        self.values.clear()
        self.log_probs.clear()

class PearlRLTrainer:
    """آموزش‌دهنده مدل‌های یادگیری تقویتی برای Pearl-3x7B"""

    def __init__(self, config: RLTrainingConfig):
        self.config = config
        self.logger = logger
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.agent = None
        self.environment = None
        self.session_id = None

        # 🌍 Multi-Symbol Components
        self.multi_symbol_manager = MultiSymbolDataManager(
            symbols=config.symbols,
            universal_features=config.universal_features
        )
        self.symbol_rotation_sequence = self.multi_symbol_manager.get_symbol_rotation_sequence(
            strategy=config.symbol_rotation_strategy,
            total_episodes=config.num_episodes,
            episodes_per_symbol=config.episodes_per_symbol
        )
        self.current_symbol = None

        # 🎯 Dynamic Reward System
        if config.dynamic_rewards:
            self.dynamic_reward_system = DynamicRewardSystem(
                reward_components=config.reward_components,
                market_adaptation=config.market_condition_adaptation
            )
        else:
            self.dynamic_reward_system = None

        # Initialize memory management
        self.memory_session = prepare_memory_for_training(
            config.model_name,
            config.batch_size,
            config.max_steps_per_episode
        )

        self.logger.log_memory_usage(
            memory_manager.get_memory_stats().process_memory,
            "Initial memory usage"
        )

        # Multi-symbol training statistics
        self.symbol_performance = {symbol: [] for symbol in config.symbols}
        self.symbol_switch_count = 0

    def prepare_environment(self, symbol: str = None, timeframe: str = "H1") -> TradingEnvironment:
        """آماده‌سازی محیط معاملاتی چند نمادی"""

        # Use provided symbol or get from rotation sequence
        if symbol is None:
            symbol = self.config.symbols[0]  # Default to first symbol

        self.current_symbol = symbol
        self.logger.logger.info(f"🔄 Preparing trading environment for {symbol} ({self.config.model_name})")

        # Get normalized data for the symbol
        processed_df = self.multi_symbol_manager.get_normalized_features(symbol)

        # Update dynamic reward system with market conditions
        if self.dynamic_reward_system:
            self.dynamic_reward_system.update_market_conditions(processed_df)

        # Create trading environment with dynamic reward system
        self.environment = TradingEnvironment(
            data=processed_df,
            symbol=symbol,
            dynamic_reward_system=self.dynamic_reward_system
        )

        self.logger.logger.info(f"✅ Trading environment prepared for {symbol} with {len(processed_df)} data points")
        return self.environment

    def switch_symbol(self, new_symbol: str):
        """تغییر نماد فعلی"""
        if new_symbol != self.current_symbol:
            self.logger.logger.info(f"🔄 Switching from {self.current_symbol} to {new_symbol}")
            self.prepare_environment(new_symbol)
            self.symbol_switch_count += 1

    def initialize_agent(self):
        """مقداردهی اولیه عامل RL"""
        self.logger.logger.info(f"🤖 Initializing {self.config.algorithm} agent: {self.config.model_name}")

        try:
            # Ensure state_dim matches environment output
            if hasattr(self, 'environment') and self.environment:
                # Get actual state size from environment
                sample_state = self.environment._get_state()
                actual_state_dim = len(sample_state)

                if self.config.state_dim != actual_state_dim:
                    self.logger.logger.warning(
                        f"Adjusting state_dim from {self.config.state_dim} to {actual_state_dim} "
                        f"to match environment"
                    )
                    self.config.state_dim = actual_state_dim

            if self.config.algorithm.lower() == "dqn":
                self.agent = DQNAgent(self.config)
            elif self.config.algorithm.lower() == "a2c":
                self.agent = A2CAgent(self.config)
            elif self.config.algorithm.lower() == "ppo":
                self.agent = PPOAgent(self.config)
            elif self.config.algorithm.lower() == "td3":
                # TD3 is more complex, using PPO as placeholder
                self.agent = PPOAgent(self.config)
                self.logger.logger.warning("TD3 not fully implemented, using PPO instead")
            else:
                raise ValueError(f"Unsupported RL algorithm: {self.config.algorithm}")

            self.logger.logger.info(f"✅ Agent initialized: {self.config.model_name}")

            # Register model with memory manager
            memory_manager.ai_model_manager.register_model(
                self.config.model_name,
                self._estimate_model_size()
            )

        except Exception as e:
            self.logger.logger.error(f"❌ Failed to initialize agent: {e}")
            raise

    def _estimate_model_size(self) -> float:
        """تخمین حجم مدل به مگابایت"""
        if self.agent is None:
            return 20.0  # Default estimate

        try:
            total_params = 0
            if hasattr(self.agent, 'q_network'):
                total_params += sum(p.numel() for p in self.agent.q_network.parameters())
            if hasattr(self.agent, 'actor'):
                total_params += sum(p.numel() for p in self.agent.actor.parameters())
            if hasattr(self.agent, 'critic'):
                total_params += sum(p.numel() for p in self.agent.critic.parameters())

            # Assume 4 bytes per parameter (float32)
            size_mb = (total_params * 4) / (1024 * 1024)
            return size_mb
        except:
            return 20.0  # Fallback estimate

    def train_episode(self, episode: int) -> Dict[str, float]:
        """آموزش یک اپیزود"""
        state = self.environment.reset()
        total_reward = 0
        steps = 0
        losses = []

        for step in range(self.config.max_steps_per_episode):
            # Select action
            if isinstance(self.agent, DQNAgent):
                action = self.agent.select_action(state, training=True)
                next_state, reward, done, info = self.environment.step(action)

                # Store transition
                self.agent.replay_buffer.push(state, action, reward, next_state, done)

                # Update agent
                if step % self.config.update_frequency == 0:
                    loss = self.agent.update()
                    if loss > 0:
                        losses.append(loss)

                # Update target network
                if step % self.config.target_update_frequency == 0:
                    self.agent.update_target_network()

            else:  # A2C or PPO
                action, log_prob, value = self.agent.select_action(state)
                next_state, reward, done, info = self.environment.step(action)

                # Store transition
                self.agent.store_transition(state, action, reward, log_prob, value)

            total_reward += reward
            state = next_state
            steps += 1

            if done:
                break

        # Update agent at end of episode for A2C/PPO
        if not isinstance(self.agent, DQNAgent):
            actor_loss, critic_loss = self.agent.update()
            losses.extend([actor_loss, critic_loss])

        # Log training step
        avg_loss = np.mean(losses) if losses else 0
        self.logger.log_training_step(
            model_name=self.config.model_name,
            epoch=episode + 1,
            batch=1,
            loss=avg_loss,
            accuracy=total_reward,  # Use reward as performance metric
            memory_usage=memory_manager.get_memory_stats().process_memory,
            duration=0.1
        )

        return {
            'episode': episode,
            'total_reward': total_reward,
            'steps': steps,
            'avg_loss': avg_loss,
            'final_balance': self.environment.balance,
            'epsilon': getattr(self.agent, 'epsilon', 0)
        }

    def evaluate_agent(self, num_episodes: int = 10) -> Dict[str, float]:
        """ارزیابی عامل"""
        self.logger.logger.info("📊 Evaluating RL agent...")
        start_time = time.time()

        total_rewards = []
        final_balances = []
        episode_lengths = []

        for episode in range(num_episodes):
            state = self.environment.reset()
            total_reward = 0
            steps = 0

            for step in range(self.config.max_steps_per_episode):
                # Select action without exploration
                if isinstance(self.agent, DQNAgent):
                    action = self.agent.select_action(state, training=False)
                else:
                    action, _, _ = self.agent.select_action(state)

                state, reward, done, info = self.environment.step(action)
                total_reward += reward
                steps += 1

                if done:
                    break

            total_rewards.append(total_reward)
            final_balances.append(self.environment.balance)
            episode_lengths.append(steps)

        duration = time.time() - start_time

        metrics = {
            'avg_reward': np.mean(total_rewards),
            'std_reward': np.std(total_rewards),
            'avg_balance': np.mean(final_balances),
            'std_balance': np.std(final_balances),
            'avg_episode_length': np.mean(episode_lengths),
            'success_rate': np.mean([b > self.environment.initial_balance for b in final_balances]),
            'max_reward': np.max(total_rewards),
            'min_reward': np.min(total_rewards)
        }

        # Log evaluation results
        self.logger.log_evaluation_result(
            model_name=self.config.model_name,
            dataset="trading_environment",
            metrics=metrics,
            duration=duration,
            memory_peak=memory_manager.get_memory_stats().process_memory
        )

        return metrics

    def save_model(self) -> str:
        """ذخیره مدل"""
        output_path = Path(self.config.output_dir) / self.config.model_name
        output_path.mkdir(parents=True, exist_ok=True)

        # Save agent networks
        if isinstance(self.agent, DQNAgent):
            torch.save({
                'q_network_state_dict': self.agent.q_network.state_dict(),
                'target_network_state_dict': self.agent.target_network.state_dict(),
                'optimizer_state_dict': self.agent.optimizer.state_dict(),
                'epsilon': self.agent.epsilon,
                'config': self.config.__dict__
            }, output_path / "dqn_model.pth")

        else:  # A2C or PPO
            torch.save({
                'actor_state_dict': self.agent.actor.state_dict(),
                'critic_state_dict': self.agent.critic.state_dict(),
                'actor_optimizer_state_dict': self.agent.actor_optimizer.state_dict(),
                'critic_optimizer_state_dict': self.agent.critic_optimizer.state_dict(),
                'config': self.config.__dict__
            }, output_path / f"{self.config.algorithm}_model.pth")

        # Save metadata
        metadata_file = output_path / "metadata.json"
        metadata = {
            'model_name': self.config.model_name,
            'algorithm': self.config.algorithm,
            'training_config': self.config.__dict__,
            'training_date': datetime.now().isoformat(),
            'device': str(self.device),
            'model_size_mb': self._estimate_model_size(),
            'state_dim': self.config.state_dim,
            'action_dim': self.config.action_dim
        }

        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        self.logger.logger.info(f"💾 Model saved to {output_path}")
        return str(output_path)

    def run_training(self) -> Dict[str, Any]:
        """اجرای کامل آموزش"""
        self.logger.logger.info(f"🚀 Starting RL training for {self.config.model_name}")

        with TrainingSession(self.logger, self.config.model_name) as session:
            try:
                # Prepare environment and agent
                self.prepare_environment()
                self.initialize_agent()

                # 🌍 Multi-Symbol Training Loop
                training_history = []
                best_avg_reward = float('-inf')
                recent_rewards = deque(maxlen=100)

                for episode in range(self.config.num_episodes):
                    # 🔄 Symbol rotation for multi-symbol training
                    target_symbol = self.symbol_rotation_sequence[episode]
                    if target_symbol != self.current_symbol:
                        self.switch_symbol(target_symbol)

                    # Train episode
                    episode_metrics = self.train_episode(episode)
                    episode_metrics['symbol'] = self.current_symbol
                    training_history.append(episode_metrics)
                    recent_rewards.append(episode_metrics['total_reward'])

                    # Store symbol-specific performance
                    self.symbol_performance[self.current_symbol].append(episode_metrics['total_reward'])

                    # Calculate moving average
                    avg_reward = np.mean(recent_rewards)

                    # Check for best performance
                    if avg_reward > best_avg_reward:
                        best_avg_reward = avg_reward
                        self.logger.logger.info(f"🏆 New best average reward: {best_avg_reward:.2f} on {self.current_symbol}")

                    # Log episode summary with symbol info
                    if episode % 50 == 0:
                        symbol_avg = np.mean(self.symbol_performance[self.current_symbol][-10:]) if len(self.symbol_performance[self.current_symbol]) >= 10 else 0
                        self.logger.logger.info(
                            f"Episode {episode} [{self.current_symbol}] - "
                            f"Reward: {episode_metrics['total_reward']:.2f}, "
                            f"Avg Reward: {avg_reward:.2f}, "
                            f"Symbol Avg: {symbol_avg:.2f}, "
                            f"Balance: {episode_metrics['final_balance']:.2f}, "
                            f"Steps: {episode_metrics['steps']}"
                        )

                    # Periodic evaluation
                    if episode % 200 == 0 and episode > 0:
                        eval_metrics = self.evaluate_agent(num_episodes=5)
                        self.logger.logger.info(
                            f"Evaluation - Avg Reward: {eval_metrics['avg_reward']:.2f}, "
                            f"Success Rate: {eval_metrics['success_rate']:.2f}"
                        )

                    # Memory cleanup
                    if episode % 100 == 0:
                        memory_manager.check_memory_pressure()

                # 🧪 Multi-Symbol Final Evaluation
                final_eval_metrics = self._multi_symbol_evaluation()

                # Save final model
                model_path = self.save_model()

                # 📊 Comprehensive Training Summary
                summary = {
                    'model_name': self.config.model_name,
                    'algorithm': self.config.algorithm,
                    'training_completed': True,
                    'total_episodes': self.config.num_episodes,
                    'best_avg_reward': best_avg_reward,
                    'final_evaluation': final_eval_metrics,
                    'model_path': model_path,
                    'training_history': training_history[-100:],  # Keep last 100 episodes
                    'environment_info': {
                        'state_dim': self.config.state_dim,
                        'action_dim': self.config.action_dim,
                        'max_steps': self.config.max_steps_per_episode
                    },
                    # 🌍 Multi-Symbol Training Results
                    'multi_symbol_results': {
                        'symbols_trained': self.config.symbols,
                        'symbol_switches': self.symbol_switch_count,
                        'symbol_performance': {
                            symbol: {
                                'avg_reward': np.mean(rewards) if rewards else 0,
                                'best_reward': max(rewards) if rewards else 0,
                                'episodes_trained': len(rewards)
                            }
                            for symbol, rewards in self.symbol_performance.items()
                        },
                        'generalization_score': self._calculate_generalization_score(),
                        'rotation_strategy': self.config.symbol_rotation_strategy
                    },
                    # 🎯 Dynamic Reward System Results
                    'reward_system_info': {
                        'dynamic_rewards_enabled': self.config.dynamic_rewards,
                        'reward_components': self.config.reward_components if self.config.dynamic_rewards else None
                    }
                }

                self.logger.logger.info(f"✅ Multi-Symbol RL training completed successfully!")
                self.logger.logger.info(f"🌍 Trained on {len(self.config.symbols)} symbols with {self.symbol_switch_count} switches")
                self.logger.logger.info(f"Best average reward: {best_avg_reward:.2f}")
                self.logger.logger.info(f"Generalization score: {summary['multi_symbol_results']['generalization_score']:.3f}")
                self.logger.logger.info(f"Model saved to: {model_path}")

                return summary

            except Exception as e:
                self.logger.logger.error(f"❌ RL training failed: {e}")
                raise
            finally:
                # Cleanup
                if self.memory_session:
                    cleanup_after_training(self.memory_session)

    def _multi_symbol_evaluation(self) -> Dict[str, Any]:
        """ارزیابی چند نمادی"""
        self.logger.logger.info("🧪 Performing multi-symbol evaluation...")

        evaluation_results = {}

        for symbol in self.config.symbols:
            self.logger.logger.info(f"  📊 Evaluating on {symbol}...")

            # Switch to symbol
            original_symbol = self.current_symbol
            self.switch_symbol(symbol)

            # Evaluate
            symbol_eval = self.evaluate_agent(num_episodes=5)
            evaluation_results[symbol] = symbol_eval

            self.logger.logger.info(f"    ✅ {symbol}: Avg Reward = {symbol_eval['avg_reward']:.2f}")

        # Restore original symbol
        if original_symbol:
            self.switch_symbol(original_symbol)

        # Calculate overall metrics
        all_rewards = [result['avg_reward'] for result in evaluation_results.values()]
        overall_metrics = {
            'symbol_evaluations': evaluation_results,
            'overall_avg_reward': np.mean(all_rewards),
            'reward_std': np.std(all_rewards),
            'min_reward': min(all_rewards),
            'max_reward': max(all_rewards),
            'generalization_score': self._calculate_generalization_score()
        }

        self.logger.logger.info(f"🎯 Multi-symbol evaluation completed: Overall Avg = {overall_metrics['overall_avg_reward']:.2f}")
        return overall_metrics

    def _calculate_generalization_score(self) -> float:
        """محاسبه امتیاز تعمیم‌پذیری"""

        symbol_averages = []
        for symbol, rewards in self.symbol_performance.items():
            if rewards:
                symbol_averages.append(np.mean(rewards))

        if len(symbol_averages) < 2:
            return 0.0

        # Generalization score based on consistency across symbols
        mean_performance = np.mean(symbol_averages)
        std_performance = np.std(symbol_averages)

        # Higher score for higher mean and lower std (more consistent)
        if std_performance == 0:
            return 1.0  # Perfect consistency

        consistency_score = max(0, 1 - (std_performance / (abs(mean_performance) + 1e-8)))
        return consistency_score

class EnhancedPearlRLTrainer(PearlRLTrainer):
    """🚀 Enhanced Pearl RL Trainer with advanced features"""

    def __init__(self, config: RLTrainingConfig):
        super().__init__(config)

        # 🤗 Sentiment Analysis Manager
        self.sentiment_manager = None
        if config.use_sentiment_analysis and SENTIMENT_MODELS_AVAILABLE:
            try:
                self.sentiment_manager = HuggingFaceSentimentManager(prefer_local=True)
                sentiment_summary = self.sentiment_manager.initialize_all_models()
                if sentiment_summary["initialization_successful"]:
                    self.logger.logger.info("🤗 Sentiment analysis enabled")
                else:
                    self.sentiment_manager = None
                    self.logger.logger.warning("⚠️ Sentiment analysis initialization failed")
            except Exception as e:
                self.logger.logger.error(f"❌ Sentiment analysis setup failed: {e}")
                self.sentiment_manager = None

        # ⚡ Enhanced Replay Configuration
        self.enhanced_replay_config = None
        if config.enhanced_replay and ENHANCED_REPLAY_AVAILABLE:
            self.enhanced_replay_config = create_enhanced_replay_config(
                buffer_size=config.replay_buffer_size,
                prioritized=config.prioritized_replay,
                multi_step=config.multi_step_learning,
                curiosity_driven=config.curiosity_driven
            )
            self.logger.logger.info("⚡ Enhanced experience replay enabled")

        # Enhanced training statistics
        self.sentiment_scores = []
        self.curiosity_rewards = []
        self.priority_stats = []

    def create_agent(self) -> Any:
        """ایجاد agent (override parent method)"""
        return self.create_enhanced_agent()

    def create_enhanced_agent(self) -> Any:
        """ایجاد agent پیشرفته"""
        if self.config.algorithm == "dqn" and self.enhanced_replay_config and ENHANCED_REPLAY_AVAILABLE:
            # Use enhanced DQN agent
            try:
                agent = EnhancedDQNAgent(
                    state_dim=self.config.state_dim,
                    action_dim=self.config.action_dim,
                    config=self.enhanced_replay_config
                )
                self.logger.logger.info("🚀 Enhanced DQN agent created")
                return agent
            except Exception as e:
                self.logger.logger.error(f"❌ Failed to create enhanced DQN agent: {e}")
                self.logger.logger.info("🔄 Falling back to standard agent")
                return super().create_agent()
        else:
            # Fallback to standard agent
            self.logger.logger.info("🔄 Creating standard agent")
            return super().create_agent()

    def analyze_market_sentiment(self, current_data: pd.DataFrame) -> float:
        """تحلیل احساسات بازار"""
        if not self.sentiment_manager:
            return 0.0

        try:
            # Create market summary text
            latest_data = current_data.tail(1).iloc[0]
            price_change = (latest_data['close'] - latest_data['open']) / latest_data['open'] * 100

            if price_change > 0.1:
                market_text = f"The {self.current_symbol} market is showing strong bullish momentum with {price_change:.2f}% gains"
            elif price_change < -0.1:
                market_text = f"The {self.current_symbol} market is experiencing bearish pressure with {price_change:.2f}% decline"
            else:
                market_text = f"The {self.current_symbol} market is showing neutral sentiment with minimal price movement"

            # Analyze sentiment
            result = self.sentiment_manager.analyze_with_best_model(market_text)

            # Convert sentiment to numerical score
            sentiment_score = 0.0
            if result.sentiment == "positive":
                sentiment_score = result.confidence
            elif result.sentiment == "negative":
                sentiment_score = -result.confidence
            # neutral remains 0.0

            self.sentiment_scores.append(sentiment_score)
            return sentiment_score

        except Exception as e:
            self.logger.logger.error(f"❌ Sentiment analysis failed: {e}")
            return 0.0

    def train_episode_enhanced(self, episode: int) -> Dict[str, Any]:
        """آموزش episode پیشرفته"""
        if not self.environment:
            raise ValueError("Environment not prepared")

        if not self.agent:
            raise ValueError("Agent not created. Call create_agent() first.")

        # Reset environment
        state = self.environment.reset()
        total_reward = 0
        steps = 0
        episode_sentiment_scores = []

        # Analyze initial market sentiment
        if self.sentiment_manager:
            sentiment_score = self.analyze_market_sentiment(self.environment.data)
            episode_sentiment_scores.append(sentiment_score)

        # Episode loop
        while steps < self.config.max_steps_per_episode:
            # Select action
            if hasattr(self.agent, 'select_action'):
                # Enhanced agent
                action = self.agent.select_action(state, epsilon=self.config.epsilon_start)
            else:
                # Standard agent
                action = self.agent.act(state)

            # Take step
            next_state, reward, done, _ = self.environment.step(action)

            # Add sentiment bonus to reward
            if self.sentiment_manager and episode_sentiment_scores:
                sentiment_bonus = episode_sentiment_scores[-1] * self.config.sentiment_weight
                reward += sentiment_bonus

            # Store experience
            if hasattr(self.agent, 'replay_buffer'):
                # Enhanced agent with replay buffer
                self.agent.replay_buffer.push(
                    state, action, reward, next_state, done,
                    episode_id=episode, step_id=steps
                )
            else:
                # Standard agent
                if hasattr(self.agent, 'remember'):
                    self.agent.remember(state, action, reward, next_state, done)

            # Train agent
            if hasattr(self.agent, 'train_step'):
                # Enhanced agent
                if len(self.agent.replay_buffer) > self.config.batch_size:
                    train_metrics = self.agent.train_step()
                    if train_metrics:
                        self.priority_stats.append(train_metrics.get('buffer_size', 0))
            else:
                # Standard agent
                if hasattr(self.agent, 'replay'):
                    self.agent.replay(self.config.batch_size)

            # Update state
            state = next_state
            total_reward += reward
            steps += 1

            if done:
                break

        # Episode metrics
        metrics = {
            'episode': episode,
            'total_reward': total_reward,
            'steps': steps,
            'final_balance': self.environment.balance,
            'symbol': self.current_symbol,
            'sentiment_scores': episode_sentiment_scores,
            'avg_sentiment': np.mean(episode_sentiment_scores) if episode_sentiment_scores else 0.0
        }

        # Add enhanced metrics if available
        if hasattr(self.agent, 'replay_buffer'):
            buffer_stats = self.agent.replay_buffer.get_stats()
            metrics.update({
                'buffer_size': buffer_stats['size'],
                'unique_states_visited': buffer_stats['unique_states_visited'],
                'max_priority': buffer_stats['max_priority']
            })

        return metrics

def train_all_rl_models() -> Dict[str, Any]:
    """آموزش تمام مدل‌های یادگیری تقویتی"""
    logger.logger.info("🎯 Starting training for all RL models")

    # 🚀 Enhanced Multi-Symbol Training Configurations with Advanced Features
    models_to_train = [
        RLTrainingConfig(
            model_name="Enhanced_PPO_Agent_MultiSymbol",
            algorithm="ppo",
            state_dim=20,
            action_dim=3,
            hidden_dim=256,
            learning_rate=3e-4,
            num_episodes=800,  # Optimized for enhanced features
            batch_size=32,
            symbols=["EURUSD", "GBPUSD", "USDJPY", "AUDUSD"],
            symbol_rotation_strategy="random",
            episodes_per_symbol=50,
            dynamic_rewards=True,
            universal_features=True,
            cross_symbol_validation=True,
            # 🚀 Enhanced Features
            enhanced_replay=False,  # PPO doesn't use replay buffer
            use_sentiment_analysis=True,
            sentiment_weight=0.1
        ),
        RLTrainingConfig(
            model_name="Enhanced_DQN_Agent_MultiSymbol",
            algorithm="dqn",
            state_dim=20,
            action_dim=3,
            hidden_dim=256,
            learning_rate=1e-4,
            num_episodes=1000,  # Extended for enhanced training
            batch_size=64,
            buffer_size=100000,
            symbols=["EURUSD", "GBPUSD", "USDJPY"],
            symbol_rotation_strategy="sequential",
            episodes_per_symbol=80,
            dynamic_rewards=True,
            universal_features=True,
            # 🚀 Enhanced Features
            enhanced_replay=True,
            prioritized_replay=True,
            multi_step_learning=3,
            curiosity_driven=True,
            replay_buffer_size=200000,
            use_sentiment_analysis=True,
            sentiment_weight=0.15
        ),
        RLTrainingConfig(
            model_name="Enhanced_A2C_Agent_MultiSymbol",
            algorithm="a2c",
            state_dim=20,
            action_dim=3,
            hidden_dim=256,
            learning_rate=3e-4,
            num_episodes=600,
            batch_size=32,
            symbols=["EURUSD", "GBPUSD"],
            symbol_rotation_strategy="adaptive",
            episodes_per_symbol=75,
            dynamic_rewards=True,
            universal_features=True,
            # 🚀 Enhanced Features
            enhanced_replay=False,  # A2C doesn't use replay buffer
            use_sentiment_analysis=True,
            sentiment_weight=0.1
        )
    ]

    results = {}

    for config in models_to_train:
        try:
            logger.logger.info(f"🚀 Training {config.model_name} ({config.algorithm}) with enhanced features")

            # Create enhanced trainer if features are available
            if (config.enhanced_replay and ENHANCED_REPLAY_AVAILABLE) or \
               (config.use_sentiment_analysis and SENTIMENT_MODELS_AVAILABLE):
                trainer = EnhancedPearlRLTrainer(config)
                logger.logger.info(f"  🚀 Using Enhanced Trainer")
            else:
                trainer = PearlRLTrainer(config)
                logger.logger.info(f"  🔄 Using Standard Trainer")

            result = trainer.run_training()
            results[config.model_name] = result

            # Log enhanced features used
            if hasattr(trainer, 'sentiment_manager') and trainer.sentiment_manager:
                logger.logger.info(f"  🤗 Sentiment analysis enabled")
            if hasattr(trainer, 'enhanced_replay_config') and trainer.enhanced_replay_config:
                logger.logger.info(f"  ⚡ Enhanced replay enabled")

            # Brief pause between models
            time.sleep(3)

        except Exception as e:
            logger.logger.error(f"❌ Failed to train {config.model_name}: {e}")
            results[config.model_name] = {
                'training_completed': False,
                'error': str(e),
                'algorithm': config.algorithm
            }

    # Save overall results
    results_file = f"rl_training_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)

    logger.logger.info(f"📊 All RL models training completed")
    logger.logger.info(f"Results saved to: {results_file}")

    return results

def evaluate_rl_performance(model_results: Dict[str, Any]) -> Dict[str, Any]:
    """ارزیابی عملکرد مدل‌های RL"""
    logger.logger.info("📈 Evaluating RL model performance...")

    performance_summary = {
        'total_models': len(model_results),
        'successful_models': 0,
        'failed_models': 0,
        'best_model': None,
        'algorithm_comparison': {}
    }

    best_reward = float('-inf')
    best_model_name = None

    for model_name, result in model_results.items():
        if result.get('training_completed', False):
            performance_summary['successful_models'] += 1

            final_eval = result.get('final_evaluation', {})
            avg_reward = final_eval.get('avg_reward', float('-inf'))
            success_rate = final_eval.get('success_rate', 0)
            algorithm = result.get('algorithm', 'unknown')

            performance_summary['algorithm_comparison'][model_name] = {
                'algorithm': algorithm,
                'avg_reward': avg_reward,
                'success_rate': success_rate,
                'avg_balance': final_eval.get('avg_balance', 0),
                'best_avg_reward': result.get('best_avg_reward', 0)
            }

            if avg_reward > best_reward:
                best_reward = avg_reward
                best_model_name = model_name
        else:
            performance_summary['failed_models'] += 1

    performance_summary['best_model'] = {
        'name': best_model_name,
        'avg_reward': best_reward,
        'metrics': performance_summary['algorithm_comparison'].get(best_model_name, {})
    } if best_model_name else None

    # Save performance report
    report_file = f"rl_performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(performance_summary, f, indent=2, ensure_ascii=False, default=str)

    logger.logger.info(f"📊 RL performance evaluation completed")
    logger.logger.info(f"Best model: {best_model_name} (Avg Reward: {best_reward:.2f})")
    logger.logger.info(f"Report saved to: {report_file}")

    return performance_summary

if __name__ == "__main__":
    # Train all RL models
    logger.logger.info("🚀 Starting comprehensive RL training pipeline")

    # Step 1: Train individual models
    training_results = train_all_rl_models()

    # Step 2: Evaluate performance
    performance_report = evaluate_rl_performance(training_results)

    # Print summary
    print("\n" + "="*60)
    print("🤖 REINFORCEMENT LEARNING MODELS TRAINING SUMMARY")
    print("="*60)

    print(f"📊 Total Models: {performance_report['total_models']}")
    print(f"✅ Successful: {performance_report['successful_models']}")
    print(f"❌ Failed: {performance_report['failed_models']}")

    if performance_report['best_model']:
        best = performance_report['best_model']
        print(f"\n🏆 Best Model: {best['name']}")
        print(f"   Average Reward: {best['avg_reward']:.2f}")
        print(f"   Success Rate: {best['metrics'].get('success_rate', 0):.2f}")

    print(f"\n📋 Individual Model Results:")
    for model_name, result in training_results.items():
        if result.get('training_completed', False):
            final_eval = result.get('final_evaluation', {})
            avg_reward = final_eval.get('avg_reward', 0)
            success_rate = final_eval.get('success_rate', 0)
            algorithm = result.get('algorithm', 'unknown')
            print(f"  ✅ {model_name} ({algorithm}): Reward={avg_reward:.2f}, Success={success_rate:.2f}")
        else:
            error = result.get('error', 'Unknown error')
            algorithm = result.get('algorithm', 'unknown')
            print(f"  ❌ {model_name} ({algorithm}): Failed - {error}")

    print(f"\n📁 Detailed results and reports saved to JSON files")
    print("🎉 RL training pipeline completed!")