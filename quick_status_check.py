#!/usr/bin/env python3
"""
Quick status check for trading system components
"""
import sys
import os

# Configure UTF-8 encoding for Windows
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_component_status():
    """Check status of key components"""
    print("Quick Component Status Check")
    print("=" * 50)
    
    # Check backtesting framework
    try:
        from utils.backtesting_framework import SentimentBacktestingFramework
        print("OK Backtesting Framework: Available")
        backtesting_available = True
    except ImportError as e:
        print(f"FAIL Backtesting Framework: Not available ({e})")
        backtesting_available = False
    
    # Check configuration management
    try:
        from core.configuration_management import ConfigurationManager
        config_manager = ConfigurationManager()
        print("OK Configuration Management: Available")
        config_available = True
    except ImportError as e:
        print(f"FAIL Configuration Management: Not available ({e})")
        config_available = False
    
    # Check AI Brain
    try:
        from models.ai_agent import AIAgent
        print("OK AI Brain (Pearl-3x7B): Available")
        ai_available = True
    except ImportError as e:
        print(f"FAIL AI Brain: Not available ({e})")
        ai_available = False
    
    # Summary
    total_components = 3
    available_components = sum([backtesting_available, config_available, ai_available])
    success_rate = (available_components / total_components) * 100
    
    print("\nComponent Summary:")
    print(f"   Available: {available_components}/{total_components} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("SUCCESS: All key components are available!")
        return True
    else:
        print(f"WARNING: {total_components - available_components} components need attention")
        return False

if __name__ == "__main__":
    check_component_status()