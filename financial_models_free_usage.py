#!/usr/bin/env python3
"""
🆓 سیستم استفاده رایگان از مدل‌های مالی Hugging Face
بدون نیاز به GPU قوی محلی

توضیحات:
- خودکار بهترین روش رو انتخاب می‌کنه (GPU/CPU/API)
- از کش استفاده می‌کنه برای صرفه‌جویی
- قابلیت fallback به API رایگان
- بهینه‌سازی مصرف منابع

استفاده:
python financial_models_free_usage.py
"""

import torch
import psutil
import hashlib
import pickle
import os
import time
from typing import List, Dict, Any, Optional
import warnings
warnings.filterwarnings("ignore")

try:
    from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
    HF_AVAILABLE = True
except ImportError:
    print("⚠️  transformers نصب نیست. برای نصب: pip install transformers torch")
    HF_AVAILABLE = False

try:
    from huggingface_hub import InferenceClient
    HF_HUB_AVAILABLE = True
except ImportError:
    print("⚠️  huggingface_hub نصب نیست. برای نصب: pip install huggingface_hub")
    HF_HUB_AVAILABLE = False


class FinancialModelManager:
    """مدیر هوشمند مدل‌های مالی برای منابع محدود"""
    
    # مدل‌های پیشنهادی بر اساس منابع
    MODELS = {
        "high_resource": "ProsusAI/finbert",  # GPU قوی
        "medium_resource": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis", # GPU/CPU متوسط  
        "low_resource": "nlptown/bert-base-multilingual-uncased-sentiment",  # CPU ضعیف
        "api_fallback": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"  # API
    }
    
    def __init__(self, hf_token: Optional[str] = None, cache_file: str = "financial_cache.pkl"):
        """
        راه‌اندازی سیستم
        
        Args:
            hf_token: Hugging Face API token (اختیاری)
            cache_file: فایل کش برای ذخیره نتایج
        """
        self.hf_token = hf_token
        self.cache_file = cache_file
        self.cache = self._load_cache()
        self.device_info = self._detect_device()
        self.model = None
        self.model_type = None
        
        print(f"🔍 تشخیص سیستم: {self.device_info}")
        self._setup_model()
    
    def _detect_device(self) -> Dict[str, Any]:
        """تشخیص مشخصات سیستم"""
        info = {
            "cuda_available": torch.cuda.is_available() if HF_AVAILABLE else False,
            "gpu_memory_gb": 0,
            "cpu_cores": psutil.cpu_count(),
            "ram_gb": psutil.virtual_memory().total / (1024**3),
            "recommendation": "api"
        }
        
        if info["cuda_available"]:
            info["gpu_memory_gb"] = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            if info["gpu_memory_gb"] > 8:
                info["recommendation"] = "high_resource"
            elif info["gpu_memory_gb"] > 4:
                info["recommendation"] = "medium_resource"
            else:
                info["recommendation"] = "low_resource"
        elif info["ram_gb"] > 8:
            info["recommendation"] = "medium_resource"
        elif info["ram_gb"] > 4:
            info["recommendation"] = "low_resource"
        
        return info
    
    def _setup_model(self):
        """راه‌اندازی مدل بهینه"""
        if not HF_AVAILABLE:
            print("❌ کتابخانه transformers در دسترس نیست")
            self._setup_api_model()
            return
        
        recommendation = self.device_info["recommendation"]
        
        try:
            if recommendation == "api":
                self._setup_api_model()
            else:
                self._setup_local_model(recommendation)
        except Exception as e:
            print(f"⚠️  خطا در راه‌اندازی مدل محلی: {e}")
            print("🔄 تلاش برای استفاده از API...")
            self._setup_api_model()
    
    def _setup_local_model(self, resource_level: str):
        """راه‌اندازی مدل محلی"""
        model_name = self.MODELS[resource_level]
        device = 0 if self.device_info["cuda_available"] else -1
        
        print(f"🔧 در حال لود مدل {model_name}...")
        print(f"📱 دستگاه: {'GPU' if device == 0 else 'CPU'}")
        
        self.model = pipeline(
            "sentiment-analysis",
            model=model_name,
            device=device,
            return_all_scores=True
        )
        self.model_type = "local"
        print("✅ مدل محلی آماده است")
    
    def _setup_api_model(self):
        """راه‌اندازی API model"""
        if not HF_HUB_AVAILABLE:
            print("❌ کتابخانه huggingface_hub در دسترس نیست")
            print("💡 برای نصب: pip install huggingface_hub")
            return
        
        if not self.hf_token:
            print("🔑 برای استفاده از API نیاز به Hugging Face token دارید")
            print("🌐 برای دریافت token: https://hf.co/settings/tokens")
            self.hf_token = input("Token خود را وارد کنید (Enter برای ادامه بدون API): ").strip()
        
        if self.hf_token:
            try:
                self.model = InferenceClient(token=self.hf_token)
                self.model_type = "api"
                print("✅ API آماده است")
            except Exception as e:
                print(f"❌ خطا در اتصال به API: {e}")
                self.model = None
                self.model_type = None
        else:
            print("⚠️  بدون API token، امکانات محدود خواهد بود")
            self.model = None
            self.model_type = None
    
    def _load_cache(self) -> Dict[str, Any]:
        """لود کش از فایل"""
        try:
            with open(self.cache_file, 'rb') as f:
                return pickle.load(f)
        except FileNotFoundError:
            return {}
        except Exception as e:
            print(f"⚠️  خطا در لود کش: {e}")
            return {}
    
    def _save_cache(self):
        """ذخیره کش در فایل"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self.cache, f)
        except Exception as e:
            print(f"⚠️  خطا در ذخیره کش: {e}")
    
    def _get_cache_key(self, text: str) -> str:
        """تولید کلید کش"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def analyze_sentiment(self, text: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        تحلیل احساس متن مالی
        
        Args:
            text: متن برای تحلیل
            use_cache: استفاده از کش
            
        Returns:
            نتیجه تحلیل احساس
        """
        # چک کش
        if use_cache:
            cache_key = self._get_cache_key(text)
            if cache_key in self.cache:
                return self.cache[cache_key]
        
        # تحلیل واقعی
        result = self._perform_analysis(text)
        
        # ذخیره در کش
        if use_cache and result:
            self.cache[cache_key] = result
            self._save_cache()
        
        return result
    
    def _perform_analysis(self, text: str) -> Dict[str, Any]:
        """انجام تحلیل احساس"""
        if not self.model:
            return {"error": "مدل در دسترس نیست"}
        
        try:
            if self.model_type == "local":
                result = self.model(text)
                return self._format_local_result(result)
            elif self.model_type == "api":
                result = self.model.text_classification(
                    text=text,
                    model=self.MODELS["api_fallback"]
                )
                return self._format_api_result(result)
        except Exception as e:
            return {"error": f"خطا در تحلیل: {str(e)}"}
    
    def _format_local_result(self, result: List[Dict]) -> Dict[str, Any]:
        """فرمت نتیجه مدل محلی"""
        if not result or not result[0]:
            return {"error": "نتیجه نامعتبر"}
        
        scores = result[0]
        formatted = {
            "model_type": "local",
            "scores": {item["label"].lower(): round(item["score"], 4) for item in scores},
            "dominant_sentiment": max(scores, key=lambda x: x["score"])["label"].lower(),
            "confidence": round(max(scores, key=lambda x: x["score"])["score"], 4)
        }
        return formatted
    
    def _format_api_result(self, result) -> Dict[str, Any]:
        """فرمت نتیجه API"""
        if not result:
            return {"error": "نتیجه نامعتبر از API"}
        
        if isinstance(result, list) and len(result) > 0:
            item = result[0]
            formatted = {
                "model_type": "api", 
                "dominant_sentiment": item.get("label", "unknown").lower(),
                "confidence": round(item.get("score", 0), 4),
                "scores": {item.get("label", "unknown").lower(): round(item.get("score", 0), 4)}
            }
        else:
            formatted = {"error": "فرمت نتیجه API نامعتبر"}
        
        return formatted
    
    def batch_analyze(self, texts: List[str], batch_size: Optional[int] = None) -> List[Dict[str, Any]]:
        """تحلیل دسته‌ای متن‌ها"""
        if batch_size is None:
            # تشخیص اندازه batch بهینه
            ram_gb = self.device_info["ram_gb"]
            if ram_gb > 16:
                batch_size = 32
            elif ram_gb > 8:
                batch_size = 16
            elif ram_gb > 4:
                batch_size = 8
            else:
                batch_size = 4
        
        results = []
        total_batches = len(texts) // batch_size + (1 if len(texts) % batch_size else 0)
        
        print(f"🔄 پردازش {len(texts)} متن در {total_batches} batch...")
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i+batch_size]
            batch_results = []
            
            for text in batch:
                result = self.analyze_sentiment(text)
                batch_results.append(result)
            
            results.extend(batch_results)
            print(f"✅ Batch {len(results)//batch_size}/{total_batches} تکمیل شد")
        
        return results
    
    def get_stats(self) -> Dict[str, Any]:
        """آمار سیستم"""
        return {
            "device_info": self.device_info,
            "model_type": self.model_type,
            "cache_size": len(self.cache),
            "cache_file": self.cache_file,
            "hf_token_set": bool(self.hf_token)
        }


class TradingSignalGenerator:
    """تولیدکننده سیگنال معاملاتی بر اساس تحلیل احساس"""
    
    def __init__(self, sentiment_analyzer: FinancialModelManager):
        self.analyzer = sentiment_analyzer
        
    def analyze_news_impact(self, news_list: List[str]) -> Dict[str, Any]:
        """تحلیل تأثیر اخبار بر بازار"""
        if not news_list:
            return {"error": "لیست اخبار خالی است"}
        
        print(f"📰 تحلیل {len(news_list)} خبر...")
        sentiments = self.analyzer.batch_analyze(news_list)
        
        # محاسبه میانگین احساس
        positive_count = 0
        negative_count = 0
        neutral_count = 0
        total_confidence = 0
        
        for sentiment in sentiments:
            if "error" in sentiment:
                continue
                
            dominant = sentiment.get("dominant_sentiment", "neutral")
            confidence = sentiment.get("confidence", 0)
            
            if dominant == "positive":
                positive_count += 1
            elif dominant == "negative":
                negative_count += 1
            else:
                neutral_count += 1
            
            total_confidence += confidence
        
        total_valid = positive_count + negative_count + neutral_count
        if total_valid == 0:
            return {"error": "هیچ تحلیل معتبری انجام نشد"}
        
        avg_confidence = total_confidence / total_valid
        
        return {
            "positive_ratio": positive_count / total_valid,
            "negative_ratio": negative_count / total_valid, 
            "neutral_ratio": neutral_count / total_valid,
            "average_confidence": round(avg_confidence, 4),
            "total_news": total_valid,
            "sentiment_details": sentiments
        }
    
    def generate_signal(self, news_list: List[str], price_data: List[float] = None) -> str:
        """تولید سیگنال معاملاتی"""
        news_analysis = self.analyze_news_impact(news_list)
        
        if "error" in news_analysis:
            return "HOLD - خطا در تحلیل اخبار"
        
        positive_ratio = news_analysis["positive_ratio"]
        negative_ratio = news_analysis["negative_ratio"]
        confidence = news_analysis["average_confidence"]
        
        # تحلیل اخبار
        if positive_ratio > 0.6 and confidence > 0.7:
            news_signal = "BULLISH"
        elif negative_ratio > 0.6 and confidence > 0.7:
            news_signal = "BEARISH"
        else:
            news_signal = "NEUTRAL"
        
        # تحلیل قیمت (ساده)
        price_signal = "NEUTRAL"
        if price_data and len(price_data) >= 2:
            if price_data[-1] > price_data[-2]:
                price_signal = "UP"
            elif price_data[-1] < price_data[-2]:
                price_signal = "DOWN"
        
        # تصمیم‌گیری نهایی
        if news_signal == "BULLISH" and price_signal in ["UP", "NEUTRAL"]:
            return "BUY"
        elif news_signal == "BEARISH" and price_signal in ["DOWN", "NEUTRAL"]:
            return "SELL"
        else:
            return "HOLD"


def demo_usage():
    """نمایش استفاده از سیستم"""
    print("🚀 راه‌اندازی سیستم تحلیل مالی...")
    
    # راه‌اندازی
    analyzer = FinancialModelManager()
    
    # نمایش آمار سیستم
    stats = analyzer.get_stats()
    print(f"\n📊 آمار سیستم:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # تست تحلیل تکی
    print(f"\n🧪 تست تحلیل احساس:")
    test_texts = [
        "Apple stock jumps 15% on strong quarterly earnings",
        "Market crashes amid economic uncertainty and inflation fears", 
        "Stable trading observed with neutral market sentiment",
        "Tesla announces record delivery numbers, stock soars",
        "Banking sector faces regulatory pressures and challenges"
    ]
    
    start_time = time.time()
    
    for text in test_texts:
        result = analyzer.analyze_sentiment(text)
        if "error" not in result:
            print(f"📰 {text[:50]}...")
            print(f"   احساس: {result['dominant_sentiment']} (اطمینان: {result['confidence']})")
        else:
            print(f"❌ خطا: {result['error']}")
    
    end_time = time.time()
    print(f"\n⏱️  زمان تحلیل: {end_time - start_time:.2f} ثانیه")
    
    # تست سیگنال معاملاتی
    print(f"\n📈 تست تولید سیگنال معاملاتی:")
    signal_generator = TradingSignalGenerator(analyzer)
    
    news_for_signal = [
        "Strong corporate earnings boost market confidence",
        "Federal Reserve hints at interest rate cuts", 
        "Tech sector leads market rally with innovation news"
    ]
    
    price_data = [100, 102, 105, 103, 107]  # قیمت‌های نمونه
    signal = signal_generator.generate_signal(news_for_signal, price_data)
    print(f"🎯 سیگنال معاملاتی: {signal}")
    
    print(f"\n✅ تست تکمیل شد!")


if __name__ == "__main__":
    demo_usage() 