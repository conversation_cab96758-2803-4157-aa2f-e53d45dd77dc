{"log": {"access": "", "error": "", "loglevel": "warning"}, "inbounds": [{"tag": "socks", "port": 10808, "listen": "127.0.0.1", "protocol": "socks", "sniffing": {"enabled": true, "destOverride": ["http", "tls"], "routeOnly": false}, "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": true, "allowTransparent": false}}, {"tag": "http", "port": 10809, "listen": "127.0.0.1", "protocol": "http", "sniffing": {"enabled": true, "destOverride": ["http", "tls"], "routeOnly": false}, "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": true, "allowTransparent": false}}], "outbounds": [{"tag": "proxy", "protocol": "vless", "settings": {"vnext": [{"address": "xpanel-us.panelfreedom.com", "port": 20111, "users": [{"id": "3905ed5f-bc5e-461d-afc7-b44c8a5a29e4", "alterId": 0, "email": "<EMAIL>", "security": "auto", "encryption": "none"}]}]}, "streamSettings": {"network": "tcp", "security": "reality", "realitySettings": {"serverName": "refersion.com", "fingerprint": "firefox", "show": false, "publicKey": "lQbgwNDYw6Zbjdim0JtXUarzb-3GSjDvtX6FJYZD9Qo", "shortId": "", "spiderX": ""}}, "mux": {"enabled": false, "concurrency": -1}}, {"tag": "direct", "protocol": "freedom", "settings": {}}, {"tag": "block", "protocol": "blackhole", "settings": {"response": {"type": "http"}}}], "dns": {"servers": ["*******", "*******"]}, "routing": {"domainStrategy": "AsIs", "rules": [{"type": "field", "inboundTag": ["api"], "outboundTag": "api"}, {"type": "field", "port": "0-65535", "outboundTag": "proxy"}]}}