{"training_date": "2025-07-15T05:00:29.559543", "config": {"model_type": "lstm", "sequence_length": 60, "prediction_horizon": 1, "features": ["open", "high", "low", "close", "volume"], "technical_indicators": ["sma", "ema", "rsi", "macd", "bollinger"], "batch_size": 32, "epochs": 50, "learning_rate": 0.001, "validation_split": 0.2, "test_split": 0.1, "save_path": "models/trained_models/timeseries_model.pkl", "lstm_config": {"units": [50, 50], "dropout": 0.2, "recurrent_dropout": 0.2, "dense_units": [25, 1]}, "technical_indicators_config": {"sma_periods": [20, 50], "ema_periods": [12, 26], "rsi_period": 14, "macd_fast": 12, "macd_slow": 26, "macd_signal": 9, "bollinger_period": 20, "bollinger_std": 2}, "data_preprocessing": {"normalization": "minmax", "remove_outliers": true, "fill_missing": "forward"}, "training_config": {"early_stopping": true, "patience": 10, "reduce_lr_on_plateau": true, "min_delta": 0.001}}, "data_info": {"training_samples": 658, "validation_samples": 188, "test_samples": 94}}