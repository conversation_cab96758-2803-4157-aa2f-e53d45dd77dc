# مستند جامع: BacktestingFramework

## مسئولیت
فریم‌ورک backtesting پیشرفته با backtrader برای تست استراتژی‌های معاملاتی با متریک‌های دقیق و مدیریت ریسک.

## پارامترها
- start_date/end_date: بازه زمانی تست
- initial_capital: سرمایه اولیه
- commission/slippage: کارمزد و لغزش
- stop_loss/take_profit: حد ضرر و سود
- sma_fast/sma_slow: پارامترهای میانگین متحرک
- rsi_period: دوره RSI

## متدهای کلیدی
- validate_config: اعتبارسنجی تنظیمات
- prepare_data: آماده‌سازی داده‌ها
- run_backtest: اجرای backtesting
- save_results: ذخیره نتایج

## نمونه کد
```python
from utils.backtesting_framework import BacktestingFramework, BacktestConfig
config = BacktestConfig(symbol='EURUSD', initial_capital=10000)
framework = BacktestingFramework(config)
results = framework.run_backtest()
```

## مدیریت خطا
در صورت نبود داده یا تنظیمات نادرست، خطا لاگ و پیام مناسب نمایش داده می‌شود.

## بهترین شیوه
- قبل از اجرا، تنظیمات را اعتبارسنجی کنید.
- داده‌های کافی و تمیز استفاده کنید.

## نمودار
- نمودار equity curve، drawdown و توزیع معاملات قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول در main_new.py و core/__init__.py به صورت عملیاتی استفاده شده و در تست‌ها نیز فعال است.

## وضعیت عملیاتی
✅ عملیاتی و در جریان اصلی پروژه فعال است. 