"""
🧪 Test Fixtures and Configuration
تنظیمات و fixture های تست

این فایل شامل fixture های مشترک برای تمام تست‌ها است
"""

import pytest
import asyncio
import os
import sys
import tempfile
import shutil
from datetime import datetime
from unittest.mock import Mock, MagicMock
from typing import Dict, Any, List, Generator

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Import modules for testing
try:
    from core.config import get_config, ConfigManager
    from core.logger import get_logger
    from core.exceptions import TradingSystemError
    from core.utils import performance_monitor, memory_manager
    
    from ai_models import ModelRegistry, initialize_models
    from utils import initialize_utils
    from models import legacy_factory
    from env import env_factory, TradingEnvironmentV2
    from portfolio import portfolio_factory, PortfolioManagerV2
    # from evaluation import evaluation_engine, EvaluationEngine  # Temporarily disabled
    from optimization import optimization_engine, OptimizationEngine
    from api import api_manager, APIManager
    
    from main_new import system_manager, TradingSystemManager
    from execution_pipeline import execution_pipeline, ExecutionPipeline
    
    IMPORTS_AVAILABLE = True
    
except ImportError as e:
    print(f"⚠️ Some imports not available: {e}")
    IMPORTS_AVAILABLE = False

# Test configuration
TEST_CONFIG = {
    "system": {
        "debug": True,
        "environment": "test"
    },
    "trading": {
        "initial_balance": 1000.0,
        "max_position_size": 0.01,
        "risk_per_trade": 0.01
    },
    "models": {
        "test_model": {
            "name": "test",
            "model_type": "test",
            "device": "cpu"
        }
    },
    "api": {
        "host": "localhost",
        "port": 8001,  # Different port for testing
        "debug": True
    }
}

# ===== PYTEST CONFIGURATION =====

def pytest_configure(config):
    """pytest configuration"""
    # Set test environment
    os.environ["TESTING"] = "1"
    os.environ["LOG_LEVEL"] = "DEBUG"

def pytest_unconfigure(config):
    """pytest cleanup"""
    # Clean up environment
    if "TESTING" in os.environ:
        del os.environ["TESTING"]

# ===== BASIC FIXTURES =====

@pytest.fixture(scope="session")
def test_config():
    """Test configuration"""
    return TEST_CONFIG

@pytest.fixture(scope="session")
def temp_dir():
    """Temporary directory for tests"""
    temp_path = tempfile.mkdtemp(prefix="trading_test_")
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)

@pytest.fixture(scope="function")
def logger():
    """Test logger"""
    return get_logger("test")

@pytest.fixture(scope="function")
def mock_config():
    """Mock configuration"""
    mock = Mock()
    mock.trading.initial_balance = 1000.0
    mock.trading.max_position_size = 0.01
    mock.trading.risk_per_trade = 0.01
    mock.models = {}
    mock.api.host = "localhost"
    mock.api.port = 8001
    return mock

# ===== CORE MODULE FIXTURES =====

@pytest.fixture(scope="function")
def performance_monitor_fixture():
    """Performance monitor fixture"""
    if IMPORTS_AVAILABLE:
        performance_monitor.reset()
        yield performance_monitor
        performance_monitor.reset()
    else:
        yield Mock()

@pytest.fixture(scope="function")
def memory_manager_fixture():
    """Memory manager fixture"""
    if IMPORTS_AVAILABLE:
        yield memory_manager
    else:
        mock = Mock()
        mock.get_memory_info.return_value = {"used_mb": 100, "available_mb": 900}
        yield mock

# ===== AI MODELS FIXTURES =====

@pytest.fixture(scope="function")
def mock_model():
    """Mock AI model"""
    mock = Mock()
    mock.name = "test_model"
    mock.model_type = "test"
    mock.predict.return_value = {"prediction": 0.5, "confidence": 0.8}
    mock.batch_predict.return_value = [{"prediction": 0.5, "confidence": 0.8}]
    mock.health_check.return_value = {"status": "healthy"}
    mock.get_model_info.return_value = {"name": "test_model", "type": "test"}
    return mock

@pytest.fixture(scope="function")
def model_registry_fixture():
    """Model registry fixture"""
    if IMPORTS_AVAILABLE:
        registry = ModelRegistry()
        yield registry
    else:
        mock = Mock()
        mock.get_model.return_value = None
        mock.register_model.return_value = True
        mock.get_available_models.return_value = []
        yield mock

@pytest.fixture(scope="function")
def mock_sentiment_model():
    """Mock sentiment model"""
    mock = Mock()
    mock.predict.return_value = {
        "label": "positive",
        "confidence": 0.85,
        "scores": {"positive": 0.85, "negative": 0.15}
    }
    return mock

# ===== ENVIRONMENT FIXTURES =====

@pytest.fixture(scope="function")
def trading_environment():
    """Trading environment fixture"""
    if IMPORTS_AVAILABLE:
        env = TradingEnvironmentV2("EURUSD", "H1")
        env.initialize()
        yield env
        env.stop()
    else:
        mock = Mock()
        mock.symbol = "EURUSD"
        mock.timeframe = "H1"
        mock.initialize.return_value = True
        mock.start.return_value = True
        mock.stop.return_value = True
        mock.health_check.return_value = {"status": "healthy"}
        yield mock

@pytest.fixture(scope="function")
def mock_market_data():
    """Mock market data"""
    import pandas as pd
    import numpy as np
    
    dates = pd.date_range("2023-01-01", periods=100, freq="H")
    data = pd.DataFrame({
        "timestamp": dates,
        "open": np.random.uniform(1.0900, 1.1100, 100),
        "high": np.random.uniform(1.0950, 1.1150, 100),
        "low": np.random.uniform(1.0850, 1.1050, 100),
        "close": np.random.uniform(1.0900, 1.1100, 100),
        "volume": np.random.randint(100, 1000, 100)
    })
    return data

# ===== PORTFOLIO FIXTURES =====

@pytest.fixture(scope="function")
def portfolio_manager():
    """Portfolio manager fixture"""
    if IMPORTS_AVAILABLE:
        portfolio = PortfolioManagerV2(initial_balance=1000.0)
        portfolio.initialize()
        yield portfolio
        portfolio.stop()
    else:
        mock = Mock()
        mock.balance = 1000.0
        mock.equity = 1000.0
        mock.initialize.return_value = True
        mock.start.return_value = True
        mock.stop.return_value = True
        mock.health_check.return_value = {"status": "healthy"}
        mock.get_performance_summary.return_value = {
            "total_return": 5.0,
            "win_rate": 0.6,
            "total_trades": 10
        }
        yield mock

@pytest.fixture(scope="function")
def mock_position():
    """Mock trading position"""
    from portfolio import PositionV2
    from datetime import datetime
    
    if IMPORTS_AVAILABLE:
        position = PositionV2(
            symbol="EURUSD",
            size=0.1,
            entry_price=1.1000,
            entry_time=datetime.now(),
            position_type="long"
        )
        return position
    else:
        mock = Mock()
        mock.symbol = "EURUSD"
        mock.size = 0.1
        mock.entry_price = 1.1000
        mock.position_type = "long"
        mock.unrealized_pnl = 0.0
        return mock

# ===== EVALUATION FIXTURES =====

@pytest.fixture(scope="function")
def evaluation_engine_fixture():
    """Evaluation engine fixture"""
    if IMPORTS_AVAILABLE:
        engine = OptimizationEngine() # Changed from EvaluationEngine to OptimizationEngine
        engine.initialize()
        yield engine
        engine.stop()
    else:
        mock = Mock()
        mock.initialize.return_value = True
        mock.start.return_value = True
        mock.stop.return_value = True
        mock.health_check.return_value = {"initialized": True, "running": False}
        mock.optimize.return_value = Mock(best_score=0.9, best_params={"x": 1.0})
        yield mock

# ===== OPTIMIZATION FIXTURES =====

@pytest.fixture(scope="function")
def optimization_engine_fixture():
    """Optimization engine fixture"""
    if IMPORTS_AVAILABLE:
        engine = OptimizationEngine()
        engine.initialize()
        yield engine
        engine.stop()
    else:
        mock = Mock()
        mock.initialize.return_value = True
        mock.start.return_value = True
        mock.stop.return_value = True
        mock.health_check.return_value = {"initialized": True, "running": False}
        mock.optimize.return_value = Mock(best_score=0.9, best_params={"x": 1.0})
        yield mock

# ===== API FIXTURES =====

@pytest.fixture(scope="function")
def api_manager_fixture():
    """API manager fixture"""
    if IMPORTS_AVAILABLE:
        manager = APIManager({"host": "localhost", "port": 8001})
        manager.initialize()
        yield manager
        manager.stop()
    else:
        mock = Mock()
        mock.initialize.return_value = True
        mock.start.return_value = True
        mock.stop.return_value = True
        mock.health_check.return_value = {"initialized": True, "running": False}
        yield mock

@pytest.fixture(scope="function")
def api_client():
    """Test API client"""
    try:
        import requests
        
        class TestAPIClient:
            def __init__(self, base_url="http://localhost:8001"):
                self.base_url = base_url
                self.session = requests.Session()
            
            def get(self, endpoint):
                return self.session.get(f"{self.base_url}{endpoint}")
            
            def post(self, endpoint, data=None, json=None):
                return self.session.post(f"{self.base_url}{endpoint}", data=data, json=json)
        
        return TestAPIClient()
    
    except ImportError:
        return Mock()

# ===== SYSTEM FIXTURES =====

@pytest.fixture(scope="function")
def system_manager_fixture():
    """System manager fixture"""
    if IMPORTS_AVAILABLE:
        manager = TradingSystemManager()
        yield manager
    else:
        mock = Mock()
        mock.initialize.return_value = True
        mock.start.return_value = True
        mock.stop.return_value = True
        mock.health_check.return_value = {"initialized": True, "running": False}
        mock.get_system_status.return_value = {"status": "running"}
        yield mock

@pytest.fixture(scope="function")
def execution_pipeline_fixture():
    """Execution pipeline fixture"""
    if IMPORTS_AVAILABLE:
        pipeline = ExecutionPipeline()
        pipeline.initialize()
        yield pipeline
        pipeline.stop()
    else:
        mock = Mock()
        mock.initialize.return_value = True
        mock.start.return_value = True
        mock.stop.return_value = True
        mock.health_check.return_value = {"initialized": True, "running": False}
        yield mock

# ===== ASYNC FIXTURES =====

@pytest.fixture(scope="function")
def event_loop():
    """Event loop for async tests"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()

@pytest.fixture(scope="function")
async def async_trading_environment():
    """Async trading environment"""
    if IMPORTS_AVAILABLE:
        env = TradingEnvironmentV2("EURUSD", "H1")
        await asyncio.to_thread(env.initialize)
        yield env
        await asyncio.to_thread(env.stop)
    else:
        yield Mock()

# ===== DATA FIXTURES =====

@pytest.fixture(scope="function")
def sample_financial_data():
    """Sample financial data for testing"""
    return {
        "news": [
            "Bitcoin reaches new all-time high",
            "Federal Reserve raises interest rates",
            "Stock market shows strong performance"
        ],
        "prices": {
            "EURUSD": 1.1000,
            "GBPUSD": 1.3000,
            "USDJPY": 110.00
        },
        "technical_indicators": {
            "sma_20": 1.0950,
            "rsi": 65.5,
            "macd": 0.0012
        }
    }

@pytest.fixture(scope="function")
def sample_trading_signals():
    """Sample trading signals"""
    return [
        {
            "symbol": "EURUSD",
            "signal": "buy",
            "confidence": 0.85,
            "timestamp": datetime.now().isoformat()
        },
        {
            "symbol": "GBPUSD",
            "signal": "sell",
            "confidence": 0.72,
            "timestamp": datetime.now().isoformat()
        }
    ]

# ===== PERFORMANCE FIXTURES =====

@pytest.fixture(scope="function")
def performance_tracker():
    """Performance tracking fixture"""
    class PerformanceTracker:
        def __init__(self):
            self.start_time = None
            self.end_time = None
            self.memory_start = None
            self.memory_end = None
        
        def start(self):
            self.start_time = datetime.now()
            if IMPORTS_AVAILABLE:
                self.memory_start = memory_manager.get_memory_info()
        
        def stop(self):
            self.end_time = datetime.now()
            if IMPORTS_AVAILABLE:
                self.memory_end = memory_manager.get_memory_info()
        
        def get_duration(self):
            if self.start_time and self.end_time:
                return (self.end_time - self.start_time).total_seconds()
            return 0
        
        def get_memory_usage(self):
            if self.memory_start and self.memory_end:
                return {
                    "start_mb": self.memory_start.get("used_mb", 0),
                    "end_mb": self.memory_end.get("used_mb", 0),
                    "delta_mb": self.memory_end.get("used_mb", 0) - self.memory_start.get("used_mb", 0)
                }
            return {}
    
    return PerformanceTracker()

# ===== ERROR FIXTURES =====

@pytest.fixture(scope="function")
def error_scenarios():
    """Common error scenarios for testing"""
    return {
        "network_error": ConnectionError("Network connection failed"),
        "timeout_error": TimeoutError("Operation timed out"),
        "trading_error": TradingSystemError("Trading system error") if IMPORTS_AVAILABLE else Exception("Trading error"),
        "model_error": Exception("Model loading failed"),
        "data_error": ValueError("Invalid data format")
    }

# ===== CLEANUP FIXTURES =====

@pytest.fixture(autouse=True)
def cleanup_after_test():
    """Automatic cleanup after each test"""
    yield
    
    # Clean up any remaining resources
    if IMPORTS_AVAILABLE:
        try:
            # Stop any running components
            # Removed evaluation_engine stop as it's no longer imported
            
            if hasattr(optimization_engine, '_running') and optimization_engine._running:
                optimization_engine.stop()
            
            if hasattr(api_manager, '_running') and api_manager._running:
                api_manager.stop()
            
            # Clean up memory
            memory_manager.cleanup()
            
        except Exception:
            pass  # Ignore cleanup errors

# ===== SKIP CONDITIONS =====

def pytest_runtest_setup(item):
    """Setup conditions for running tests"""
    
    # Skip AI tests if models not available
    if item.get_closest_marker("ai") and not IMPORTS_AVAILABLE:
        pytest.skip("AI models not available")
    
    # Skip network tests if no internet
    if item.get_closest_marker("network"):
        try:
            import socket
            socket.create_connection(("*******", 53), timeout=3)
        except OSError:
            pytest.skip("Network not available")
    
    # Skip GPU tests if no GPU
    if item.get_closest_marker("gpu"):
        try:
            import torch
            if not torch.cuda.is_available():
                pytest.skip("GPU not available")
        except ImportError:
            pytest.skip("PyTorch not available")
    
    # Skip proxy tests if proxy not configured
    if item.get_closest_marker("proxy"):
        proxy_url = os.environ.get("HTTP_PROXY") or os.environ.get("http_proxy")
        if not proxy_url:
            pytest.skip("Proxy not configured")

# ===== CUSTOM MARKERS =====

# Register custom markers to avoid warnings
def pytest_configure(config):
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "performance: Performance tests")
    config.addinivalue_line("markers", "api: API tests")
    config.addinivalue_line("markers", "ai: AI model tests")
    config.addinivalue_line("markers", "slow: Slow tests")
    config.addinivalue_line("markers", "network: Tests requiring network")
    config.addinivalue_line("markers", "gpu: Tests requiring GPU")
    config.addinivalue_line("markers", "proxy: Tests requiring proxy")
    config.addinivalue_line("markers", "asyncio: Async tests")
