#!/usr/bin/env python3
"""
Ultra Simple Test - فقط imports و basic functionality
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_basic_imports():
    """تست imports اصلی"""
    print("🧪 Testing basic imports...")
    
    try:
        from portfolio.advanced_risk_manager import AdvancedRiskManager, RiskParameters
        print("✅ AdvancedRiskManager imported successfully")
        
        # Test instance creation
        risk_params = RiskParameters()
        risk_manager = AdvancedRiskManager(risk_params)
        print(f"✅ RiskManager created, capital: {risk_manager.performance_metrics.current_capital}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_portfolio_manager():
    """تست portfolio manager"""
    print("🧪 Testing portfolio manager...")
    
    try:
        from portfolio.smart_portfolio_manager import SmartPortfolioManager, ProfitTarget
        from portfolio.advanced_risk_manager import RiskParameters
        print("✅ SmartPortfolioManager imported successfully")
        
        # Test instance creation
        risk_params = RiskParameters()
        portfolio_manager = SmartPortfolioManager(risk_params)
        print(f"✅ PortfolioManager created, capital: {portfolio_manager.risk_manager.performance_metrics.current_capital}")
        
        return True
        
    except Exception as e:
        print(f"❌ Portfolio manager test failed: {e}")
        return False

def main():
    """اجرای اصلی"""
    print("🚀 ULTRA SIMPLE TEST RUNNER")
    print("=" * 50)
    
    test1 = test_basic_imports()
    test2 = test_portfolio_manager()
    
    print("\n📊 TEST RESULTS:")
    print("=" * 30)
    print(f"Basic Imports: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"Portfolio Manager: {'✅ PASSED' if test2 else '❌ FAILED'}")
    
    overall_success = test1 and test2
    print(f"\nOverall: {'✅ SUCCESS' if overall_success else '❌ FAILURE'}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 