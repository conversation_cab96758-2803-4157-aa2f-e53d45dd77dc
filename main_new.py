#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Advanced AI Trading System - Main Application
سیستم پیشرفته معاملات با هوش مصنوعی

*** AI BRAIN AS THE COMPLETE SYSTEM CONTROLLER ***
مغز هوشمند به عنوان کنترل‌کننده کامل سیستم

The AI Agent is now the complete brain that:
- Controls all system components
- Makes all decisions autonomously
- Monitors and optimizes everything
- Manages learning and adaptation
- Handles all system operations
"""

import os
import sys
import time
import logging
import warnings
from pathlib import Path
import asyncio
from datetime import datetime
from typing import Dict, Any, List

# Configure Windows console for UTF-8 support
if sys.platform == "win32":
    try:
        os.system("chcp 65001 > nul")
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')
    except Exception:
        pass

# Suppress deprecation warnings
warnings.filterwarnings('ignore', category=DeprecationWarning)

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# ============================================================================
# CORE & SYSTEM IMPORTS
# ============================================================================
from core.logger import setup_logger
from core.error_handler import AdvancedErrorHandler
from core.simple_database_manager import SimpleDatabaseManager
from core.configuration_management import ConfigurationManager
from core.advanced_integration_system import AdvancedIntegrationSystem
from core.memory_manager import AdvancedMemoryManager
from core.order_manager import AdvancedOrderManager
from core.multi_exchange import MultiExchangeManager
from core.model_versioning import ModelRegistry
from core.model_monitoring import ModelMonitoringManager
from core.advanced_risk_metrics import AdvancedRiskCalculator
from core.correlation_analysis import AdvancedCorrelationAnalyzer
from core.backtesting_framework import AdvancedBacktestingEngine
from core.enhanced_error_handling import EnhancedErrorHandler
from core.performance_optimizer import PerformanceOptimizer

# ============================================================================
# AI & MODELS IMPORTS
# ============================================================================
from models.ai_agent import AIAgent
from models.ensemble_model import EnsembleModel
from models.continual_learning import ContinualLearningSystem
from models.rl_models import RLModelFactory
from models.time_series_model import TimeSeriesModel
from models.hierarchical_rl import HierarchicalRL
from models.meta_learner import MetaLearner
from models.zero_shot_learning import ZeroShotLearning
from models.unified_trading_system import UnifiedTradingSystem
from ai_models.model_manager import ModelManager

# ============================================================================
# UTILS & PORTFOLIO IMPORTS
# ============================================================================
from utils.technical_indicators import AdvancedTechnicalIndicators
from utils.sentiment_analyzer import AdvancedSentimentAnalyzer
from utils.anomaly_detection_system import AnomalyDetectionSystem
from utils.genetic_strategy_evolution import GeneticStrategyEvolution
from utils.federated_learning_system import FederatedLearningSystem
from utils.data_loader import DataLoader
from utils.data_cleaning import DataCleaner
from utils.performance_metrics import PerformanceMetrics
from utils.market_regime_detector import MarketRegimeDetector
from portfolio.advanced_risk_manager import AdvancedRiskManager
from portfolio.smart_portfolio_manager import SmartPortfolioManager
from optimization.bayesian import BayesianOptimizer

# Configure logging
logger = setup_logger("adaptive_plutus_main")

class TradingSystem:
    """
    The main trading system, fully controlled by the AI Agent.
    """
    def __init__(self):
        self.logger = logger
        self.ai_brain = None
        self.is_running = False

    async def initialize_and_run(self):
        """
        Initializes and runs the entire system under the AI Brain's control.
        """
        self.logger.info("🚀 Initializing the Advanced AI Trading System...")
        
        try:
            # The AI Brain is the central controller
            self.ai_brain = AIAgent(config_path="config.yaml")
            self.logger.info("🧠 AI Brain (Pearl-3x7B) has been initialized as the system controller.")
            
            # The AI Brain will now handle the entire operational pipeline
            await self.ai_brain.run_full_pipeline()
            
            self.is_running = True
            self.logger.info("✅ System is now running under full AI control.")
            
            # Keep the system alive
            while self.is_running:
                await asyncio.sleep(60)
                self.logger.info("AI Brain supervision loop is active...")

        except Exception as e:
            self.logger.critical(f"❌ A critical error occurred during system startup: {e}", exc_info=True)
        finally:
            await self.shutdown()

    async def shutdown(self):
        """
        Gracefully shuts down the system.
        """
        self.logger.info("🔴 Shutting down the system...")
        self.is_running = False
        if self.ai_brain:
            await self.ai_brain.shutdown()
        self.logger.info("✅ System has been shut down.")

def main():
    """
    Main entry point of the application.
    """
    system = TradingSystem()
    try:
        asyncio.run(system.initialize_and_run())
    except KeyboardInterrupt:
        logger.info("⌨️ Keyboard interrupt received. Shutting down...")
    finally:
        # Ensure shutdown is called even if loop is interrupted
        if system.is_running:
            asyncio.run(system.shutdown())

if __name__ == "__main__":
    main()
