"""
🤗 Enhanced HuggingFace Sentiment Models for Financial Analysis
مدل‌های پیشرفته تحلیل احساسات مالی از HuggingFace

این فایل شامل:
1. FinBERT (ProsusAI/finbert)
2. Financial RoBERTa (mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis)
3. FinGPT Sentiment (FinGPT/fingpt-sentiment-cls)
4. Ensemble Sentiment Model
5. Real-time sentiment analysis pipeline
"""

import os
import sys
import logging
import time
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# HuggingFace imports
from transformers import (
    AutoTokenizer,
    AutoModelForSequenceClassification,
    pipeline,
    AutoConfig
)
import torch
from huggingface_hub import login

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import model downloader
from ai_models.model_downloader import HuggingFaceModelDownloader, ProxyManager

logger = logging.getLogger(__name__)

@dataclass
class SentimentResult:
    """نتیجه تحلیل احساسات"""
    model_name: str
    text: str
    sentiment: str
    confidence: float
    probabilities: Dict[str, float]
    processing_time: float
    timestamp: str

class LocalHuggingFaceModel:
    """🏠 Local HuggingFace Model with proxy support and offline capability"""

    def __init__(self, model_key: str, cache_dir: str = "./models_cache", use_proxy: bool = True):
        self.model_key = model_key
        self.cache_dir = cache_dir
        self.use_proxy = use_proxy
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.logger = logging.getLogger(self.__class__.__name__)

        # Initialize downloader
        self.downloader = HuggingFaceModelDownloader(cache_dir=cache_dir, use_proxy=use_proxy)

        # Get model info
        if model_key in self.downloader.available_models:
            self.model_info = self.downloader.available_models[model_key]
        else:
            raise ValueError(f"Unknown model key: {model_key}")

    def ensure_model_downloaded(self) -> bool:
        """اطمینان از دانلود مدل"""
        if self.downloader.check_model_availability(self.model_key):
            self.logger.info(f"✅ Model {self.model_key} already available locally")
            return True

        self.logger.info(f"🔄 Downloading model {self.model_key}...")
        return self.downloader.download_model(self.model_key)

    def load(self, force_download: bool = False) -> bool:
        """بارگذاری مدل محلی"""
        try:
            # Ensure model is downloaded
            if force_download or not self.ensure_model_downloaded():
                if not self.downloader.download_model(self.model_key, force_download=force_download):
                    self.logger.error(f"❌ Failed to download {self.model_key}")
                    return False

            # Get local path
            local_path = self.downloader.get_model_path(self.model_key)
            if not local_path:
                self.logger.error(f"❌ Local path not found for {self.model_key}")
                return False

            self.logger.info(f"🔄 Loading model from local path: {local_path}")

            # Load model and tokenizer from local path
            self.tokenizer = AutoTokenizer.from_pretrained(
                local_path,
                local_files_only=True,
                trust_remote_code=False
            )

            self.model = AutoModelForSequenceClassification.from_pretrained(
                local_path,
                local_files_only=True,
                trust_remote_code=False
            )

            # Create pipeline
            self.pipeline = pipeline(
                'sentiment-analysis',
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if torch.cuda.is_available() else -1
            )

            self.logger.info(f"✅ {self.model_info.name} loaded successfully from local cache")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to load local model {self.model_key}: {e}")
            return False

    def analyze_sentiment(self, text: str, return_all_scores: bool = True) -> SentimentResult:
        """تحلیل احساسات با مدل محلی"""
        if not self.pipeline:
            raise ValueError("Model not loaded. Call load() first.")

        start_time = time.time()

        try:
            results = self.pipeline(text, return_all_scores=return_all_scores)
            processing_time = time.time() - start_time

            # Debug: Check results format
            self.logger.debug(f"Pipeline results type: {type(results)}")
            self.logger.debug(f"Pipeline results: {results}")

            # Handle results - pipeline always returns a list
            if not isinstance(results, list) or len(results) == 0:
                raise ValueError(f"Unexpected results format: {type(results)}")

            # Process results based on model type
            label_mapping = None
            if self.model_key == "financial_roberta" or self.model_key == "cardiffnlp_roberta":
                label_mapping = {"LABEL_0": "negative", "LABEL_1": "neutral", "LABEL_2": "positive"}

            if return_all_scores:
                # When return_all_scores=True, results is [[{...}, {...}, ...]]
                if isinstance(results[0], list):
                    all_scores = results[0]  # Extract the inner list
                else:
                    all_scores = results

                probabilities = {}
                for item in all_scores:
                    label = item['label']
                    score = item['score']
                    if label_mapping:
                        label = label_mapping.get(label, label)
                    probabilities[label] = score

                # Find best result
                best_item = max(all_scores, key=lambda x: x['score'])
                sentiment = best_item['label']
                if label_mapping:
                    sentiment = label_mapping.get(sentiment, sentiment)
                confidence = best_item['score']
            else:
                # When return_all_scores=False, results is [{'label': ..., 'score': ...}]
                single_result = results[0]
                sentiment = single_result['label']
                confidence = single_result['score']

                if label_mapping:
                    sentiment = label_mapping.get(sentiment, sentiment)

                probabilities = {sentiment: confidence}

            return SentimentResult(
                model_name=f"Local_{self.model_info.name}",
                text=text,
                sentiment=sentiment,
                confidence=confidence,
                probabilities=probabilities,
                processing_time=processing_time,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            self.logger.error(f"❌ Local sentiment analysis failed: {e}")
            raise

class EnhancedFinBERTModel:
    """🤗 Enhanced FinBERT Model for financial sentiment analysis"""

    def __init__(self, model_name: str = "ProsusAI/finbert", cache_dir: str = "./models_cache"):
        self.model_name = model_name
        self.cache_dir = cache_dir
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.logger = logging.getLogger(self.__class__.__name__)

        # Create cache directory
        os.makedirs(cache_dir, exist_ok=True)

        # Model metadata
        self.model_info = {
            "name": "FinBERT",
            "source": "ProsusAI/finbert",
            "description": "BERT fine-tuned for financial sentiment analysis",
            "labels": ["positive", "negative", "neutral"],
            "performance": 0.89,
            "size_mb": 440
        }

    def load(self, use_cache: bool = True) -> bool:
        """بارگذاری مدل با قابلیت cache"""
        try:
            self.logger.info(f"🔄 Loading FinBERT model: {self.model_name}")

            # Load with caching for faster subsequent loads
            self.pipeline = pipeline(
                'sentiment-analysis',
                model=self.model_name,
                device=0 if torch.cuda.is_available() else -1,
                model_kwargs={
                    "cache_dir": self.cache_dir if use_cache else None,
                    "local_files_only": False  # Allow download if not cached
                }
            )

            self.logger.info(f"✅ FinBERT model loaded successfully")
            self.logger.info(f"📊 Model info: {self.model_info['description']}")
            self.logger.info(f"🎯 Expected performance: {self.model_info['performance']:.2f}")

            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to load FinBERT model: {e}")
            return False

    def analyze_sentiment(self, text: str, return_all_scores: bool = True) -> SentimentResult:
        """تحلیل احساسات پیشرفته"""
        if not self.pipeline:
            raise ValueError("Model not loaded. Call load() first.")

        start_time = time.time()

        try:
            # Get sentiment analysis results
            results = self.pipeline(text, return_all_scores=return_all_scores)
            processing_time = time.time() - start_time

            if return_all_scores:
                # Extract all probabilities
                probabilities = {result['label']: result['score'] for result in results}
                # Get the highest confidence prediction
                best_result = max(results, key=lambda x: x['score'])
                sentiment = best_result['label']
                confidence = best_result['score']
            else:
                sentiment = results[0]['label']
                confidence = results[0]['score']
                probabilities = {sentiment: confidence}

            return SentimentResult(
                model_name=self.model_name,
                text=text,
                sentiment=sentiment,
                confidence=confidence,
                probabilities=probabilities,
                processing_time=processing_time,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            self.logger.error(f"❌ Sentiment analysis failed: {e}")
            raise

    def batch_analyze(self, texts: List[str]) -> List[SentimentResult]:
        """تحلیل دسته‌ای احساسات"""
        if not self.pipeline:
            raise ValueError("Model not loaded. Call load() first.")

        results = []
        for text in texts:
            try:
                result = self.analyze_sentiment(text)
                results.append(result)
            except Exception as e:
                self.logger.error(f"❌ Failed to analyze text: {text[:50]}... Error: {e}")
                # Add error result
                results.append(SentimentResult(
                    model_name=self.model_name,
                    text=text,
                    sentiment="error",
                    confidence=0.0,
                    probabilities={"error": 1.0},
                    processing_time=0.0,
                    timestamp=datetime.now().isoformat()
                ))

        return results

class FinancialRoBERTaModel:
    """🤗 Financial RoBERTa Model for sentiment analysis"""

    def __init__(self, model_name: str = "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
                 cache_dir: str = "./models_cache"):
        self.model_name = model_name
        self.cache_dir = cache_dir
        self.pipeline = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.logger = logging.getLogger(self.__class__.__name__)

        # Create cache directory
        os.makedirs(cache_dir, exist_ok=True)

        # Model metadata
        self.model_info = {
            "name": "Financial RoBERTa",
            "source": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
            "description": "DistilRoBERTa fine-tuned for financial news sentiment",
            "labels": ["LABEL_0", "LABEL_1", "LABEL_2"],  # negative, neutral, positive
            "performance": 0.87,
            "size_mb": 320
        }

    def load(self, use_cache: bool = True) -> bool:
        """بارگذاری مدل RoBERTa"""
        try:
            self.logger.info(f"🔄 Loading Financial RoBERTa model: {self.model_name}")

            self.pipeline = pipeline(
                'sentiment-analysis',
                model=self.model_name,
                device=0 if torch.cuda.is_available() else -1,
                model_kwargs={
                    "cache_dir": self.cache_dir if use_cache else None,
                    "local_files_only": False
                }
            )

            self.logger.info(f"✅ Financial RoBERTa model loaded successfully")
            self.logger.info(f"📊 Model info: {self.model_info['description']}")

            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to load Financial RoBERTa model: {e}")
            return False

    def analyze_sentiment(self, text: str, return_all_scores: bool = True) -> SentimentResult:
        """تحلیل احساسات با RoBERTa"""
        if not self.pipeline:
            raise ValueError("Model not loaded. Call load() first.")

        start_time = time.time()

        try:
            results = self.pipeline(text, return_all_scores=return_all_scores)
            processing_time = time.time() - start_time

            if return_all_scores:
                # Map labels to meaningful names
                label_mapping = {"LABEL_0": "negative", "LABEL_1": "neutral", "LABEL_2": "positive"}
                probabilities = {label_mapping.get(result['label'], result['label']): result['score']
                               for result in results}

                best_result = max(results, key=lambda x: x['score'])
                sentiment = label_mapping.get(best_result['label'], best_result['label'])
                confidence = best_result['score']
            else:
                label_mapping = {"LABEL_0": "negative", "LABEL_1": "neutral", "LABEL_2": "positive"}
                sentiment = label_mapping.get(results[0]['label'], results[0]['label'])
                confidence = results[0]['score']
                probabilities = {sentiment: confidence}

            return SentimentResult(
                model_name=self.model_name,
                text=text,
                sentiment=sentiment,
                confidence=confidence,
                probabilities=probabilities,
                processing_time=processing_time,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            self.logger.error(f"❌ RoBERTa sentiment analysis failed: {e}")
            raise

class FinGPTSentimentModel:
    """🤗 FinGPT Sentiment Model for advanced financial sentiment analysis"""

    def __init__(self, model_name: str = "FinGPT/fingpt-sentiment-cls", cache_dir: str = "./models_cache"):
        self.model_name = model_name
        self.cache_dir = cache_dir
        self.pipeline = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.logger = logging.getLogger(self.__class__.__name__)

        # Create cache directory
        os.makedirs(cache_dir, exist_ok=True)

        # Model metadata
        self.model_info = {
            "name": "FinGPT Sentiment",
            "source": "FinGPT/fingpt-sentiment-cls",
            "description": "GPT-based financial sentiment classifier",
            "labels": ["positive", "negative", "neutral"],
            "performance": 0.92,
            "size_mb": 2800
        }

    def load(self, use_cache: bool = True) -> bool:
        """بارگذاری مدل FinGPT"""
        try:
            self.logger.info(f"🔄 Loading FinGPT Sentiment model: {self.model_name}")

            # Note: FinGPT might require special handling
            self.pipeline = pipeline(
                'text-classification',  # Use text-classification for FinGPT
                model=self.model_name,
                device=0 if torch.cuda.is_available() else -1,
                model_kwargs={
                    "cache_dir": self.cache_dir if use_cache else None,
                    "local_files_only": False
                }
            )

            self.logger.info(f"✅ FinGPT Sentiment model loaded successfully")
            self.logger.info(f"📊 Model info: {self.model_info['description']}")

            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to load FinGPT model: {e}")
            self.logger.warning("⚠️ FinGPT model might not be available or require special access")
            return False

    def analyze_sentiment(self, text: str, return_all_scores: bool = True) -> SentimentResult:
        """تحلیل احساسات با FinGPT"""
        if not self.pipeline:
            raise ValueError("Model not loaded. Call load() first.")

        start_time = time.time()

        try:
            results = self.pipeline(text, return_all_scores=return_all_scores)
            processing_time = time.time() - start_time

            if return_all_scores:
                probabilities = {result['label']: result['score'] for result in results}
                best_result = max(results, key=lambda x: x['score'])
                sentiment = best_result['label']
                confidence = best_result['score']
            else:
                sentiment = results[0]['label']
                confidence = results[0]['score']
                probabilities = {sentiment: confidence}

            return SentimentResult(
                model_name=self.model_name,
                text=text,
                sentiment=sentiment,
                confidence=confidence,
                probabilities=probabilities,
                processing_time=processing_time,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            self.logger.error(f"❌ FinGPT sentiment analysis failed: {e}")
            raise

class EnsembleSentimentModel:
    """🎯 Ensemble Sentiment Model combining multiple HuggingFace models"""

    def __init__(self, cache_dir: str = "./models_cache"):
        self.cache_dir = cache_dir
        self.models = {}
        self.model_weights = {
            "finbert": 0.4,      # Highest weight for FinBERT
            "roberta": 0.35,     # Good weight for RoBERTa
            "fingpt": 0.25       # Lower weight for FinGPT (might not always be available)
        }
        self.logger = logging.getLogger(self.__class__.__name__)

        # Ensemble metadata
        self.ensemble_info = {
            "name": "Ensemble Sentiment Model",
            "description": "Weighted ensemble of FinBERT, RoBERTa, and FinGPT",
            "expected_performance": 0.91,
            "models_count": 0
        }

    def load_all_models(self) -> Dict[str, bool]:
        """بارگذاری تمام مدل‌ها"""
        self.logger.info("🔄 Loading ensemble sentiment models...")

        load_results = {}

        # Load FinBERT
        try:
            self.models["finbert"] = EnhancedFinBERTModel(cache_dir=self.cache_dir)
            load_results["finbert"] = self.models["finbert"].load()
            if load_results["finbert"]:
                self.logger.info("✅ FinBERT loaded successfully")
        except Exception as e:
            self.logger.error(f"❌ Failed to load FinBERT: {e}")
            load_results["finbert"] = False

        # Load Financial RoBERTa
        try:
            self.models["roberta"] = FinancialRoBERTaModel(cache_dir=self.cache_dir)
            load_results["roberta"] = self.models["roberta"].load()
            if load_results["roberta"]:
                self.logger.info("✅ Financial RoBERTa loaded successfully")
        except Exception as e:
            self.logger.error(f"❌ Failed to load Financial RoBERTa: {e}")
            load_results["roberta"] = False

        # Load FinGPT (optional, might not be available)
        try:
            self.models["fingpt"] = FinGPTSentimentModel(cache_dir=self.cache_dir)
            load_results["fingpt"] = self.models["fingpt"].load()
            if load_results["fingpt"]:
                self.logger.info("✅ FinGPT loaded successfully")
            else:
                self.logger.warning("⚠️ FinGPT not available, continuing with other models")
        except Exception as e:
            self.logger.warning(f"⚠️ FinGPT not available: {e}")
            load_results["fingpt"] = False

        # Update model count and weights
        successful_models = [model for model, success in load_results.items() if success]
        self.ensemble_info["models_count"] = len(successful_models)

        # Adjust weights if some models failed to load
        if not load_results.get("fingpt", False):
            # Redistribute FinGPT weight to other models
            self.model_weights["finbert"] = 0.55
            self.model_weights["roberta"] = 0.45
            self.model_weights["fingpt"] = 0.0

        self.logger.info(f"🎯 Ensemble loaded: {len(successful_models)}/3 models")
        self.logger.info(f"📊 Model weights: {self.model_weights}")

        return load_results

    def analyze_sentiment(self, text: str) -> SentimentResult:
        """تحلیل احساسات ensemble"""
        if not self.models:
            raise ValueError("No models loaded. Call load_all_models() first.")

        start_time = time.time()
        individual_results = {}

        # Get predictions from all available models
        for model_name, model in self.models.items():
            if model and self.model_weights[model_name] > 0:
                try:
                    result = model.analyze_sentiment(text)
                    individual_results[model_name] = result
                except Exception as e:
                    self.logger.error(f"❌ {model_name} failed: {e}")

        if not individual_results:
            raise ValueError("No models produced valid results")

        # Combine results using weighted voting
        sentiment_scores = {"positive": 0.0, "negative": 0.0, "neutral": 0.0}
        total_weight = 0.0

        for model_name, result in individual_results.items():
            weight = self.model_weights[model_name]
            total_weight += weight

            # Add weighted scores
            for sentiment, score in result.probabilities.items():
                if sentiment in sentiment_scores:
                    sentiment_scores[sentiment] += score * weight

        # Normalize scores
        if total_weight > 0:
            sentiment_scores = {k: v / total_weight for k, v in sentiment_scores.items()}

        # Get final prediction
        final_sentiment = max(sentiment_scores, key=sentiment_scores.get)
        final_confidence = sentiment_scores[final_sentiment]

        processing_time = time.time() - start_time

        return SentimentResult(
            model_name="EnsembleSentimentModel",
            text=text,
            sentiment=final_sentiment,
            confidence=final_confidence,
            probabilities=sentiment_scores,
            processing_time=processing_time,
            timestamp=datetime.now().isoformat()
        )

    def get_detailed_analysis(self, text: str) -> Dict[str, Any]:
        """تحلیل تفصیلی با نتایج تمام مدل‌ها"""
        individual_results = {}

        for model_name, model in self.models.items():
            if model and self.model_weights[model_name] > 0:
                try:
                    result = model.analyze_sentiment(text)
                    individual_results[model_name] = {
                        "sentiment": result.sentiment,
                        "confidence": result.confidence,
                        "probabilities": result.probabilities,
                        "processing_time": result.processing_time
                    }
                except Exception as e:
                    self.logger.error(f"❌ {model_name} failed: {e}")
                    individual_results[model_name] = {"error": str(e)}

        # Get ensemble result
        ensemble_result = self.analyze_sentiment(text)

        return {
            "ensemble_result": {
                "sentiment": ensemble_result.sentiment,
                "confidence": ensemble_result.confidence,
                "probabilities": ensemble_result.probabilities
            },
            "individual_results": individual_results,
            "model_weights": self.model_weights,
            "text": text,
            "timestamp": datetime.now().isoformat()
        }

class LocalEnsembleModel:
    """🏠 Local Ensemble Model using downloaded HuggingFace models"""

    def __init__(self, cache_dir: str = "./models_cache", use_proxy: bool = True):
        self.cache_dir = cache_dir
        self.use_proxy = use_proxy
        self.models = {}
        self.model_weights = {
            "financial_roberta": 0.6,    # Higher weight for available model
            "cardiffnlp_roberta": 0.4    # Secondary model
        }
        self.logger = logging.getLogger(self.__class__.__name__)

        # Initialize downloader
        self.downloader = HuggingFaceModelDownloader(cache_dir=cache_dir, use_proxy=use_proxy)

    def download_and_load_models(self) -> Dict[str, bool]:
        """دانلود و بارگذاری مدل‌ها"""
        self.logger.info("🚀 Downloading and loading local ensemble models...")

        # First, try to download lightweight models
        download_results = self.downloader.download_lightweight_models()

        load_results = {}

        # Load successfully downloaded models
        for model_key, download_success in download_results.items():
            if download_success:
                try:
                    self.logger.info(f"🔄 Loading {model_key}...")
                    model = LocalHuggingFaceModel(model_key, cache_dir=self.cache_dir, use_proxy=self.use_proxy)

                    if model.load():
                        self.models[model_key] = model
                        load_results[model_key] = True
                        self.logger.info(f"✅ {model_key} loaded successfully")
                    else:
                        load_results[model_key] = False
                        self.logger.error(f"❌ Failed to load {model_key}")

                except Exception as e:
                    self.logger.error(f"❌ Error loading {model_key}: {e}")
                    load_results[model_key] = False
            else:
                load_results[model_key] = False

        # Adjust weights based on available models
        available_models = [key for key, success in load_results.items() if success]
        if available_models:
            # Redistribute weights equally among available models
            weight_per_model = 1.0 / len(available_models)
            self.model_weights = {key: weight_per_model if key in available_models else 0.0
                                for key in self.model_weights.keys()}

        self.logger.info(f"🎯 Local ensemble loaded: {len(available_models)} models")
        self.logger.info(f"📊 Model weights: {self.model_weights}")

        return load_results

    def analyze_sentiment(self, text: str) -> SentimentResult:
        """تحلیل احساسات با ensemble محلی"""
        if not self.models:
            raise ValueError("No models loaded. Call download_and_load_models() first.")

        start_time = time.time()
        individual_results = {}

        # Get predictions from all available models
        for model_key, model in self.models.items():
            if self.model_weights[model_key] > 0:
                try:
                    result = model.analyze_sentiment(text)
                    individual_results[model_key] = result
                except Exception as e:
                    self.logger.error(f"❌ {model_key} failed: {e}")

        if not individual_results:
            raise ValueError("No models produced valid results")

        # Combine results using weighted voting
        sentiment_scores = {"positive": 0.0, "negative": 0.0, "neutral": 0.0}
        total_weight = 0.0

        for model_key, result in individual_results.items():
            weight = self.model_weights[model_key]
            total_weight += weight

            # Add weighted scores
            for sentiment, score in result.probabilities.items():
                if sentiment in sentiment_scores:
                    sentiment_scores[sentiment] += score * weight

        # Normalize scores
        if total_weight > 0:
            sentiment_scores = {k: v / total_weight for k, v in sentiment_scores.items()}

        # Get final prediction
        final_sentiment = max(sentiment_scores, key=sentiment_scores.get)
        final_confidence = sentiment_scores[final_sentiment]

        processing_time = time.time() - start_time

        return SentimentResult(
            model_name="LocalEnsembleModel",
            text=text,
            sentiment=final_sentiment,
            confidence=final_confidence,
            probabilities=sentiment_scores,
            processing_time=processing_time,
            timestamp=datetime.now().isoformat()
        )

    def get_model_status(self) -> Dict[str, Any]:
        """دریافت وضعیت مدل‌ها"""
        return {
            "cache_directory": self.cache_dir,
            "models_loaded": len(self.models),
            "available_models": list(self.models.keys()),
            "model_weights": self.model_weights,
            "downloader_status": self.downloader.get_download_status()
        }

class HuggingFaceSentimentManager:
    """🤗 Enhanced HuggingFace Sentiment Manager with local model support"""

    def __init__(self, cache_dir: str = "./models_cache", use_proxy: bool = True, prefer_local: bool = True):
        self.cache_dir = cache_dir
        self.use_proxy = use_proxy
        self.prefer_local = prefer_local
        self.local_ensemble = None
        self.online_ensemble = None
        self.active_model = None
        self.logger = logging.getLogger(self.__class__.__name__)

        # Create cache directory
        os.makedirs(cache_dir, exist_ok=True)

        # Initialize proxy if needed
        if use_proxy:
            self.proxy_manager = ProxyManager()
            self.proxy_manager.load_proxy_config()
            self.proxy_manager.setup_environment_proxy()

    def initialize_all_models(self) -> Dict[str, Any]:
        """مقداردهی اولیه تمام مدل‌ها (محلی و آنلاین)"""
        self.logger.info("🚀 Initializing Enhanced HuggingFace Sentiment Models...")

        results = {
            "local_models": {},
            "online_models": {},
            "active_model_type": None,
            "initialization_successful": False
        }

        # Try local models first (preferred)
        if self.prefer_local:
            try:
                self.logger.info("🏠 Attempting to initialize local ensemble...")
                self.local_ensemble = LocalEnsembleModel(cache_dir=self.cache_dir, use_proxy=self.use_proxy)
                local_results = self.local_ensemble.download_and_load_models()
                results["local_models"] = local_results

                if any(local_results.values()):
                    self.active_model = self.local_ensemble
                    results["active_model_type"] = "local"
                    results["initialization_successful"] = True
                    self.logger.info("✅ Local ensemble initialized successfully")
                else:
                    self.logger.warning("⚠️ No local models loaded successfully")

            except Exception as e:
                self.logger.error(f"❌ Local ensemble initialization failed: {e}")

        # Fallback to online models if local failed or not preferred
        if not results["initialization_successful"]:
            try:
                self.logger.info("🌐 Attempting to initialize online ensemble...")
                self.online_ensemble = EnsembleSentimentModel(cache_dir=self.cache_dir)
                online_results = self.online_ensemble.load_all_models()
                results["online_models"] = online_results

                successful_online = sum(1 for success in online_results.values() if success)
                if successful_online > 0:
                    self.active_model = self.online_ensemble
                    results["active_model_type"] = "online"
                    results["initialization_successful"] = True
                    self.logger.info(f"✅ Online ensemble initialized with {successful_online} models")
                else:
                    self.logger.error("❌ No online models loaded successfully")

            except Exception as e:
                self.logger.error(f"❌ Online ensemble initialization failed: {e}")

        # Summary
        summary = {
            "initialization_successful": results["initialization_successful"],
            "active_model_type": results["active_model_type"],
            "cache_directory": self.cache_dir,
            "proxy_enabled": self.use_proxy,
            "prefer_local": self.prefer_local,
            "local_models_status": results["local_models"],
            "online_models_status": results["online_models"]
        }

        if results["initialization_successful"]:
            self.logger.info(f"🎯 Initialization successful using {results['active_model_type']} models")
        else:
            self.logger.error("❌ All model initialization attempts failed")

        return summary

    def analyze_with_best_model(self, text: str) -> SentimentResult:
        """تحلیل با بهترین مدل موجود"""
        if not self.active_model:
            raise ValueError("No sentiment models available. Call initialize_all_models() first.")

        return self.active_model.analyze_sentiment(text)

    def get_detailed_analysis(self, text: str) -> Dict[str, Any]:
        """تحلیل تفصیلی"""
        if not self.active_model:
            raise ValueError("No sentiment models available")

        if hasattr(self.active_model, 'get_detailed_analysis'):
            return self.active_model.get_detailed_analysis(text)
        else:
            # Simple analysis for local ensemble
            result = self.active_model.analyze_sentiment(text)
            return {
                "result": {
                    "sentiment": result.sentiment,
                    "confidence": result.confidence,
                    "probabilities": result.probabilities,
                    "model_name": result.model_name
                },
                "text": text,
                "timestamp": result.timestamp
            }

    def get_model_status(self) -> Dict[str, Any]:
        """دریافت وضعیت مدل‌ها"""
        status = {
            "active_model_type": None,
            "active_model_available": self.active_model is not None,
            "cache_directory": self.cache_dir,
            "proxy_enabled": self.use_proxy,
            "local_ensemble": None,
            "online_ensemble": None
        }

        if self.local_ensemble:
            status["local_ensemble"] = self.local_ensemble.get_model_status()
            if self.active_model == self.local_ensemble:
                status["active_model_type"] = "local"

        if self.online_ensemble:
            status["online_ensemble"] = {
                "available": True,
                "models_count": self.online_ensemble.ensemble_info["models_count"]
            }
            if self.active_model == self.online_ensemble:
                status["active_model_type"] = "online"

        return status

    def switch_to_local_models(self) -> bool:
        """تغییر به مدل‌های محلی"""
        if not self.local_ensemble:
            self.logger.info("🏠 Initializing local ensemble...")
            self.local_ensemble = LocalEnsembleModel(cache_dir=self.cache_dir, use_proxy=self.use_proxy)
            results = self.local_ensemble.download_and_load_models()

            if any(results.values()):
                self.active_model = self.local_ensemble
                self.logger.info("✅ Switched to local models")
                return True
            else:
                self.logger.error("❌ Failed to initialize local models")
                return False
        else:
            self.active_model = self.local_ensemble
            self.logger.info("✅ Switched to existing local models")
            return True

    def switch_to_online_models(self) -> bool:
        """تغییر به مدل‌های آنلاین"""
        if not self.online_ensemble:
            self.logger.info("🌐 Initializing online ensemble...")
            self.online_ensemble = EnsembleSentimentModel(cache_dir=self.cache_dir)
            results = self.online_ensemble.load_all_models()

            successful_models = sum(1 for success in results.values() if success)
            if successful_models > 0:
                self.active_model = self.online_ensemble
                self.logger.info("✅ Switched to online models")
                return True
            else:
                self.logger.error("❌ Failed to initialize online models")
                return False
        else:
            self.active_model = self.online_ensemble
            self.logger.info("✅ Switched to existing online models")
            return True

    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.pipeline is not None,
            'status': 'healthy' if self.pipeline else 'not_loaded'
        }

class CryptoBERTModel:
    """CryptoBERT Model for cryptocurrency sentiment analysis"""
    
    def __init__(self, model_name: str = "ElKulako/cryptobert"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            self.pipeline = pipeline(
                'sentiment-analysis',
                model=self.model_name,
                device=0 if torch.cuda.is_available() else -1
            )
            self.logger.info(f"✅ CryptoBERT model loaded: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to load CryptoBERT model: {e}")
            return False
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """تحلیل احساسات"""
        if not self.pipeline:
            return {'error': 'Model not loaded'}
        
        try:
            result = self.pipeline(text)
            return {
                'model_name': self.model_name,
                'text': text,
                'sentiment': result[0]['label'],
                'confidence': result[0]['score'],
                'label': result[0]['label'],
                'probabilities': {result[0]['label']: result[0]['score']}
            }
        except Exception as e:
            self.logger.error(f"Error in sentiment analysis: {e}")
            return {'error': str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.pipeline is not None,
            'status': 'healthy' if self.pipeline else 'not_loaded'
        }

class FinancialSentimentModel:
    """Financial Sentiment Model for general financial sentiment analysis"""
    
    def __init__(self, model_name: str = "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            self.pipeline = pipeline(
                'sentiment-analysis',
                model=self.model_name,
                device=0 if torch.cuda.is_available() else -1
            )
            self.logger.info(f"✅ Financial Sentiment model loaded: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to load Financial Sentiment model: {e}")
            return False
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """تحلیل احساسات"""
        if not self.pipeline:
            return {'error': 'Model not loaded'}
        
        try:
            result = self.pipeline(text)
            return {
                'model_name': self.model_name,
                'text': text,
                'sentiment': result[0]['label'],
                'confidence': result[0]['score'],
                'label': result[0]['label'],
                'probabilities': {result[0]['label']: result[0]['score']}
            }
        except Exception as e:
            self.logger.error(f"Error in sentiment analysis: {e}")
            return {'error': str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.pipeline is not None,
            'status': 'healthy' if self.pipeline else 'not_loaded'
        } 