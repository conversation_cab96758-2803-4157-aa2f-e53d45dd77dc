"""
🔧 Advanced Data Preprocessor - پردازشگر پیشرفته داده
مطابق نقشه راه گنج برای Pearl-3x7B

شامل:
- Anomaly Detection & Cleaning (پاکسازی ناهنجاری)
- Dataset Builder (ساخت دیتاست)
- Technical Indicators (اندیکاتورهای تکنیکال)
- Data Quality Assurance (تضمین کیفیت داده)
"""

import pandas as pd
import numpy as np
import logging
import os
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Technical Analysis
from ta import add_all_ta_features
from ta.trend import SMAIndicator, ADXIndicator, CCIIndicator
from ta.momentum import RSIIndicator, StochasticOscillator, WilliamsRIndicator
from ta.volatility import AverageTrueRange, BollingerBands
from ta.trend import MACD

# Statistical Analysis
from scipy import stats
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.ensemble import IsolationForest
from sklearn.cluster import DBSCAN

logger = logging.getLogger(__name__)

class AnomalyDetectionSystem:
    """سیستم تشخیص و حذف ناهنجاری‌ها"""

    def __init__(self, contamination: float = 0.1):
        self.contamination = contamination
        self.isolation_forest = IsolationForest(
            contamination=contamination,
            random_state=42,
            n_estimators=100
        )
        self.scaler = StandardScaler()

    def detect_price_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """تشخیص ناهنجاری‌های قیمتی"""
        logger.info("🔍 Detecting price anomalies...")

        # محاسبه نسبت‌های قیمتی
        df['high_low_ratio'] = df['high'] / df['low']
        df['open_close_ratio'] = df['open'] / df['close']
        df['volume_ma'] = df['volume'].rolling(window=20).mean() if 'volume' in df.columns else 1

        # تشخیص ناهنجاری با Isolation Forest
        features = ['high_low_ratio', 'open_close_ratio']
        if 'volume' in df.columns:
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            features.append('volume_ratio')

        # حذف NaN ها
        df_clean = df[features].dropna()
        if len(df_clean) < 10:
            logger.warning("⚠️ Not enough data for anomaly detection")
            return df

        # تشخیص ناهنجاری
        scaled_features = self.scaler.fit_transform(df_clean)
        anomaly_labels = self.isolation_forest.fit_predict(scaled_features)

        # علامت‌گذاری ناهنجاری‌ها
        df.loc[df_clean.index, 'is_anomaly'] = (anomaly_labels == -1)
        df['is_anomaly'] = df['is_anomaly'].fillna(False)

        anomaly_count = df['is_anomaly'].sum()
        logger.info(f"🎯 Detected {anomaly_count} price anomalies ({anomaly_count/len(df)*100:.2f}%)")

        return df

    def detect_statistical_outliers(self, df: pd.DataFrame, columns: List[str] = None) -> pd.DataFrame:
        """تشخیص outlier های آماری"""
        if columns is None:
            columns = ['open', 'high', 'low', 'close']

        logger.info("📊 Detecting statistical outliers...")

        df['is_outlier'] = False

        for col in columns:
            if col in df.columns:
                # محاسبه Z-score
                z_scores = np.abs(stats.zscore(df[col].dropna()))
                outliers = z_scores > 3

                # محاسبه IQR
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                iqr_outliers = (df[col] < lower_bound) | (df[col] > upper_bound)

                # ترکیب هر دو روش
                combined_outliers = outliers | iqr_outliers
                df.loc[combined_outliers, 'is_outlier'] = True

        outlier_count = df['is_outlier'].sum()
        logger.info(f"🎯 Detected {outlier_count} statistical outliers ({outlier_count/len(df)*100:.2f}%)")

        return df

    def clean_anomalies(self, df: pd.DataFrame, method: str = 'remove') -> pd.DataFrame:
        """پاکسازی ناهنجاری‌ها"""
        logger.info(f"🧹 Cleaning anomalies using method: {method}")

        original_length = len(df)

        if method == 'remove':
            # حذف کامل ردیف‌های ناهنجار
            df_clean = df[~(df.get('is_anomaly', False) | df.get('is_outlier', False))]
        elif method == 'interpolate':
            # جایگزینی با interpolation
            df_clean = df.copy()
            anomaly_mask = df.get('is_anomaly', False) | df.get('is_outlier', False)

            for col in ['open', 'high', 'low', 'close']:
                if col in df_clean.columns:
                    df_clean.loc[anomaly_mask, col] = np.nan
                    df_clean[col] = df_clean[col].interpolate(method='linear')
        else:
            df_clean = df.copy()

        cleaned_length = len(df_clean)
        removed_count = original_length - cleaned_length

        logger.info(f"✅ Cleaned {removed_count} anomalous records ({removed_count/original_length*100:.2f}%)")

        return df_clean

class DatasetBuilder:
    """سازنده دیتاست برای آموزش مدل‌ها"""

    def __init__(self, output_dir: str = "datasets/cleaned"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def create_training_datasets(self, df: pd.DataFrame, symbol: str,
                                timeframe: str, test_size: float = 0.2,
                                validation_size: float = 0.1) -> Dict[str, pd.DataFrame]:
        """ایجاد دیتاست‌های آموزش، اعتبارسنجی و تست"""
        logger.info(f"📦 Creating training datasets for {symbol} {timeframe}")

        # مرتب‌سازی بر اساس زمان
        df_sorted = df.sort_values('datetime').reset_index(drop=True)

        # تقسیم داده‌ها
        total_size = len(df_sorted)
        test_start = int(total_size * (1 - test_size))
        val_start = int(test_start * (1 - validation_size))

        train_data = df_sorted[:val_start].copy()
        val_data = df_sorted[val_start:test_start].copy()
        test_data = df_sorted[test_start:].copy()

        datasets = {
            'train': train_data,
            'validation': val_data,
            'test': test_data
        }

        # ذخیره فایل‌ها
        for split_name, data in datasets.items():
            filename = f"{symbol}_{timeframe}_{split_name}.csv"
            filepath = self.output_dir / filename
            data.to_csv(filepath, index=False)
            logger.info(f"💾 Saved {split_name} dataset: {filepath} ({len(data)} records)")

        # ذخیره metadata
        metadata = {
            'symbol': symbol,
            'timeframe': timeframe,
            'total_records': total_size,
            'train_records': len(train_data),
            'validation_records': len(val_data),
            'test_records': len(test_data),
            'created_at': datetime.now().isoformat(),
            'columns': list(df.columns)
        }

        metadata_file = self.output_dir / f"{symbol}_{timeframe}_metadata.json"
        import json
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)

        logger.info(f"📋 Saved metadata: {metadata_file}")

        return datasets

class AdvancedDataPreprocessor:
    """پردازشگر پیشرفته داده - نسخه جدید مطابق نقشه راه گنج"""

    def __init__(self, enable_anomaly_detection: bool = True):
        self.enable_anomaly_detection = enable_anomaly_detection
        self.anomaly_detector = AnomalyDetectionSystem() if enable_anomaly_detection else None
        self.dataset_builder = DatasetBuilder()

    def add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """اضافه کردن اندیکاتورهای تکنیکال"""
        logger.info("📈 Adding technical indicators...")

        # اندیکاتورهای اصلی
        df["rsi_14"] = RSIIndicator(close=df["close"], window=14).rsi()

        # MACD
        macd = MACD(close=df["close"], window_slow=26, window_fast=12, window_sign=9)
        df["macd"] = macd.macd()
        df["macd_signal"] = macd.macd_signal()
        df["macd_hist"] = macd.macd_diff()

        # Moving Averages
        df["sma_20"] = SMAIndicator(close=df["close"], window=20).sma_indicator()
        df["sma_50"] = SMAIndicator(close=df["close"], window=50).sma_indicator()
        df["sma_200"] = SMAIndicator(close=df["close"], window=200).sma_indicator()

        # Bollinger Bands
        bb = BollingerBands(close=df["close"], window=20, window_dev=2)
        df["bb_upper"] = bb.bollinger_hband()
        df["bb_middle"] = bb.bollinger_mavg()
        df["bb_lower"] = bb.bollinger_lband()
        df["bb_width"] = (df["bb_upper"] - df["bb_lower"]) / df["bb_middle"]

        # ATR
        df["atr_14"] = AverageTrueRange(
            high=df["high"], low=df["low"], close=df["close"], window=14
        ).average_true_range()

        # ADX
        df["adx_14"] = ADXIndicator(
            high=df["high"], low=df["low"], close=df["close"], window=14
        ).adx()

        # Stochastic
        stoch = StochasticOscillator(
            high=df["high"], low=df["low"], close=df["close"],
            window=14, smooth_window=3
        )
        df["stoch_k"] = stoch.stoch()
        df["stoch_d"] = stoch.stoch_signal()

        # CCI
        df["cci_20"] = CCIIndicator(
            high=df["high"], low=df["low"], close=df["close"], window=20
        ).cci()

        # Williams %R
        df["williams_r"] = WilliamsRIndicator(
            high=df["high"], low=df["low"], close=df["close"], lbp=14
        ).williams_r()

        # SuperTrend
        atr = df["atr_14"]
        hl2 = (df["high"] + df["low"]) / 2
        multiplier = 3
        upperband = hl2 + (multiplier * atr)
        lowerband = hl2 - (multiplier * atr)
        df["supertrend_upper"] = upperband
        df["supertrend_lower"] = lowerband

        # Price-based features
        df["price_change"] = df["close"].pct_change()
        df["high_low_ratio"] = df["high"] / df["low"]
        df["open_close_ratio"] = df["open"] / df["close"]

        # Volatility measures
        df["volatility_20"] = df["price_change"].rolling(window=20).std()
        df["volatility_50"] = df["price_change"].rolling(window=50).std()

        logger.info("✅ Technical indicators added successfully")
        return df

    def clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """پاکسازی و اعتبارسنجی داده‌ها"""
        logger.info("🧹 Cleaning and validating data...")

        # تبدیل datetime
        if 'datetime' in df.columns:
            df["datetime"] = pd.to_datetime(df["datetime"])
            df = df.set_index("datetime").sort_index()

        # حذف مقادیر نامعتبر
        df = df.replace([np.inf, -np.inf], np.nan)

        # بررسی ستون‌های اصلی
        required_columns = ["open", "high", "low", "close"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        # حذف ردیف‌های با مقادیر منفی یا صفر
        df = df[(df["high"] > 0) & (df["low"] > 0) & (df["close"] > 0) & (df["open"] > 0)]

        # بررسی منطقی قیمت‌ها
        df = df[df["high"] >= df["low"]]
        df = df[df["high"] >= df["close"]]
        df = df[df["high"] >= df["open"]]
        df = df[df["low"] <= df["close"]]
        df = df[df["low"] <= df["open"]]

        logger.info("✅ Data cleaned and validated")
        return df

    def handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """مدیریت مقادیر گمشده"""
        logger.info("🔧 Handling missing values...")

        # جایگزینی inf و -inf با NaN
        df = df.replace([np.inf, -np.inf], np.nan)

        # Forward fill برای قیمت‌ها
        price_columns = ["open", "high", "low", "close"]
        for col in price_columns:
            if col in df.columns:
                df[col] = df[col].fillna(method='ffill')

        # Interpolation برای اندیکاتورها
        indicator_columns = [col for col in df.columns if col not in price_columns + ['datetime', 'volume']]
        for col in indicator_columns:
            if col in df.columns:
                df[col] = df[col].interpolate(method='linear')

        # حذف ردیف‌هایی که هنوز NaN دارند
        initial_length = len(df)
        df = df.dropna()
        final_length = len(df)

        if initial_length != final_length:
            logger.info(f"🗑️ Removed {initial_length - final_length} rows with missing values")

        return df

    def preprocess(self, df: pd.DataFrame, symbol: str = None,
                   timeframe: str = None, create_datasets: bool = True) -> pd.DataFrame:
        """پردازش کامل داده‌ها"""
        logger.info(f"🚀 Starting advanced preprocessing for {symbol} {timeframe}")

        # مرحله 1: پاکسازی اولیه
        df = self.clean_and_validate_data(df)

        # مرحله 2: تشخیص ناهنجاری
        if self.enable_anomaly_detection and self.anomaly_detector:
            df = self.anomaly_detector.detect_price_anomalies(df)
            df = self.anomaly_detector.detect_statistical_outliers(df)
            df = self.anomaly_detector.clean_anomalies(df, method='remove')

        # مرحله 3: اضافه کردن اندیکاتورها
        df = self.add_technical_indicators(df)

        # مرحله 4: مدیریت مقادیر گمشده
        df = self.handle_missing_values(df)

        # مرحله 5: ایجاد دیتاست‌ها
        if create_datasets and symbol and timeframe:
            self.dataset_builder.create_training_datasets(df, symbol, timeframe)

        # Reset index
        df = df.reset_index()

        logger.info(f"✅ Preprocessing completed. Final dataset: {len(df)} records")
        return df

# Backward compatibility
class DataPreprocessor(AdvancedDataPreprocessor):
    """کلاس قدیمی برای سازگاری با کدهای موجود"""

    def __init__(self):
        super().__init__(enable_anomaly_detection=False)
        logger.warning("⚠️ Using legacy DataPreprocessor. Consider upgrading to AdvancedDataPreprocessor")

# Factory function برای Pearl-3x7B
def create_preprocessor_for_pearl(enable_anomaly_detection: bool = True) -> AdvancedDataPreprocessor:
    """ایجاد پردازشگر مخصوص Pearl-3x7B"""
    logger.info("🤖 Creating preprocessor for Pearl-3x7B")
    return AdvancedDataPreprocessor(enable_anomaly_detection=enable_anomaly_detection)

# Utility functions
def process_raw_data_directory(input_dir: str = "datasets/raw",
                              output_dir: str = "datasets/cleaned") -> Dict[str, Any]:
    """پردازش کل دایرکتوری داده‌های خام"""
    logger.info(f"📁 Processing raw data directory: {input_dir}")

    input_path = Path(input_dir)
    results = {}

    preprocessor = create_preprocessor_for_pearl()

    for symbol_dir in input_path.iterdir():
        if symbol_dir.is_dir():
            symbol = symbol_dir.name
            results[symbol] = {}

            for csv_file in symbol_dir.glob("*.csv"):
                if csv_file.stem.endswith('.temp_m5'):
                    continue  # Skip temporary files

                timeframe = csv_file.stem
                logger.info(f"📊 Processing {symbol} {timeframe}")

                try:
                    # بارگذاری داده
                    df = pd.read_csv(csv_file)

                    # پردازش
                    processed_df = preprocessor.preprocess(df, symbol, timeframe)

                    results[symbol][timeframe] = {
                        'status': 'success',
                        'original_records': len(df),
                        'processed_records': len(processed_df),
                        'reduction_percentage': (len(df) - len(processed_df)) / len(df) * 100
                    }

                except Exception as e:
                    logger.error(f"❌ Error processing {symbol} {timeframe}: {e}")
                    results[symbol][timeframe] = {
                        'status': 'error',
                        'error': str(e)
                    }

    logger.info("✅ Raw data directory processing completed")
    return results

if __name__ == "__main__":
    # تست سیستم
    logging.basicConfig(level=logging.INFO)

    # پردازش تمام داده‌های خام
    results = process_raw_data_directory()

    print("\n📊 Processing Results:")
    for symbol, timeframes in results.items():
        print(f"\n{symbol}:")
        for timeframe, result in timeframes.items():
            if result['status'] == 'success':
                print(f"  ✅ {timeframe}: {result['processed_records']} records "
                      f"({result['reduction_percentage']:.1f}% reduction)")
            else:
                print(f"  ❌ {timeframe}: {result['error']}")
