{"test_summary": {"total_duration": 32.929723501205444, "overall_success_rate": 0.8620689655172413, "test_suites_completed": 5, "test_suites_failed": 0, "timestamp": "2025-07-17T08:03:21.127104", "system_info": {"python_version": "3.9.0 (tags/v3.9.0:9cf6752, Oct  5 2020, 15:34:40) [MSC v.1927 64 bit (AMD64)]", "memory_total_gb": 7.915454864501953, "memory_available_gb": 0.9826736450195312, "cpu_count": 6, "platform": "win32"}}, "detailed_results": {"memory_management": {"basic_functionality": true, "memory_pools": true, "anomaly_detection": false, "training_optimization": true, "stress_test": true, "memory_leaks": false, "duration": 11.956493616104126, "status": "completed", "success_rate": 0.6666666666666666}, "logging_system": {"basic_logging": true, "structured_logging": false, "training_session": true, "performance_logging": true, "export_functionality": true, "concurrent_logging": true, "duration": 1.4130754470825195, "status": "completed", "success_rate": 0.8333333333333334}, "data_preprocessing": {"basic_preprocessing": true, "anomaly_detection": true, "dataset_building": true, "technical_indicators": true, "data_validation": true, "performance_test": true, "duration": 1.5368497371673584, "status": "completed", "success_rate": 1.0}, "sentiment_training": {"trainer_initialization": true, "data_preparation": true, "model_initialization": true, "training_simulation": true, "evaluation_system": true, "model_saving": true, "duration": 0.5710358619689941, "status": "completed", "success_rate": 1.0}, "system_integration": {"cross_component_communication": true, "end_to_end_workflow": true, "error_handling": true, "resource_cleanup": true, "concurrent_operations": false, "duration": 17.43017864227295, "status": "completed", "success_rate": 0.8}}, "recommendations": ["⚠️ memory_management needs attention (success rate: 66.7%)"]}