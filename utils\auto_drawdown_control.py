#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Auto-Drawdown Control System
سیستم کنترل خودکار افت سرمایه

این ماژول شامل:
- تشخیص خودکار drawdown
- سیستم ترمز تطبیقی
- پیش‌بینی drawdown با LSTM
- استراتژی بازیابی هوشمند
- مدیریت drawdown چندسطحی
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
from abc import ABC, abstractmethod
import warnings
warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DrawdownSeverity(Enum):
    """سطوح شدت drawdown"""
    LOW = "low"           # 0-5%
    MODERATE = "moderate" # 5-10%
    HIGH = "high"         # 10-20%
    SEVERE = "severe"     # 20%+


class DrawdownAction(Enum):
    """اقدامات کنترل drawdown"""
    MONITOR = "monitor"           # فقط مانیتورینگ
    REDUCE_EXPOSURE = "reduce"    # کاهش exposure
    HALT_TRADING = "halt"         # توقف معاملات
    EMERGENCY_EXIT = "emergency"  # خروج اضطراری


class RecoveryStrategy(Enum):
    """استراتژی‌های بازیابی"""
    GRADUAL = "gradual"           # بازیابی تدریجی
    AGGRESSIVE = "aggressive"     # بازیابی تهاجمی
    CONSERVATIVE = "conservative" # بازیابی محافظه‌کارانه
    ADAPTIVE = "adaptive"         # بازیابی تطبیقی


@dataclass
class DrawdownEvent:
    """رویداد drawdown"""
    start_date: datetime
    end_date: Optional[datetime] = None
    peak_value: float = 0.0
    trough_value: float = 0.0
    max_drawdown: float = 0.0
    duration_days: int = 0
    recovery_days: Optional[int] = None
    severity: DrawdownSeverity = DrawdownSeverity.LOW
    is_active: bool = True
    
    # Metadata
    trigger_conditions: List[str] = field(default_factory=list)
    actions_taken: List[DrawdownAction] = field(default_factory=list)
    recovery_strategy: Optional[RecoveryStrategy] = None


@dataclass
class DrawdownMetrics:
    """معیارهای drawdown"""
    current_drawdown: float
    max_drawdown: float
    drawdown_duration: int
    time_to_recovery: Optional[int]
    recovery_factor: float
    pain_index: float
    ulcer_index: float
    calmar_ratio: float
    sterling_ratio: float
    
    # Probability metrics
    drawdown_probability: float
    expected_recovery_time: float
    var_95: float
    cvar_95: float


@dataclass
class ControlParameters:
    """پارامترهای کنترل"""
    # Drawdown thresholds
    warning_threshold: float = 0.05    # 5%
    action_threshold: float = 0.10     # 10%
    emergency_threshold: float = 0.20  # 20%
    
    # Position sizing
    min_position_size: float = 0.1     # 10% minimum
    max_position_size: float = 1.0     # 100% maximum
    reduction_factor: float = 0.5      # 50% reduction
    
    # Recovery parameters
    recovery_threshold: float = 0.02   # 2% recovery to resume
    recovery_multiplier: float = 1.2   # 20% increase after recovery
    
    # Time parameters
    lookback_period: int = 252         # 1 year
    min_recovery_period: int = 21      # 21 days minimum
    
    # Risk parameters
    max_correlation: float = 0.8       # Maximum correlation threshold
    volatility_multiplier: float = 2.0 # Volatility-based adjustment


class DrawdownDetector:
    """تشخیص drawdown"""
    
    def __init__(self, lookback_period: int = 252):
        self.lookback_period = lookback_period
        self.peak_values = []
        self.drawdown_history = []
        self.current_event = None
    
    def update(self, portfolio_value: float, timestamp: datetime) -> DrawdownEvent:
        """به‌روزرسانی با مقدار جدید portfolio"""
        
        # Update peak values
        if not self.peak_values:
            self.peak_values.append((timestamp, portfolio_value))
            return None
        
        # Calculate current peak
        current_peak = max(self.peak_values, key=lambda x: x[1])[1]
        
        # Calculate drawdown
        current_drawdown = (portfolio_value - current_peak) / current_peak
        
        # Check if we're in a drawdown
        if current_drawdown < -0.001:  # More than 0.1% drawdown
            if self.current_event is None:
                # Start new drawdown event
                self.current_event = DrawdownEvent(
                    start_date=timestamp,
                    peak_value=current_peak,
                    trough_value=portfolio_value,
                    max_drawdown=abs(current_drawdown)
                )
            else:
                # Update existing drawdown
                self.current_event.trough_value = min(self.current_event.trough_value, portfolio_value)
                self.current_event.max_drawdown = max(self.current_event.max_drawdown, abs(current_drawdown))
                self.current_event.duration_days = (timestamp - self.current_event.start_date).days
                
                # Update severity
                self.current_event.severity = self._classify_severity(self.current_event.max_drawdown)
        
        else:
            # Check if we're recovering from a drawdown
            if self.current_event is not None:
                # End drawdown event
                self.current_event.end_date = timestamp
                self.current_event.is_active = False
                self.current_event.recovery_days = (timestamp - self.current_event.start_date).days
                
                # Add to history
                self.drawdown_history.append(self.current_event)
                self.current_event = None
        
        # Update peak values (keep only recent ones)
        self.peak_values.append((timestamp, portfolio_value))
        if len(self.peak_values) > self.lookback_period:
            self.peak_values.pop(0)
        
        return self.current_event
    
    def _classify_severity(self, drawdown: float) -> DrawdownSeverity:
        """تعیین شدت drawdown"""
        if drawdown < 0.05:
            return DrawdownSeverity.LOW
        elif drawdown < 0.10:
            return DrawdownSeverity.MODERATE
        elif drawdown < 0.20:
            return DrawdownSeverity.HIGH
        else:
            return DrawdownSeverity.SEVERE
    
    def get_current_metrics(self, portfolio_values: pd.Series) -> DrawdownMetrics:
        """محاسبه معیارهای فعلی drawdown"""
        
        # Calculate running maximum
        running_max = portfolio_values.expanding().max()
        
        # Calculate drawdown series
        drawdown_series = (portfolio_values - running_max) / running_max
        
        # Current drawdown
        current_drawdown = drawdown_series.iloc[-1]
        
        # Maximum drawdown
        max_drawdown = drawdown_series.min()
        
        # Drawdown duration
        drawdown_duration = 0
        if current_drawdown < 0:
            for i in range(len(drawdown_series) - 1, -1, -1):
                if drawdown_series.iloc[i] < 0:
                    drawdown_duration += 1
                else:
                    break
        
        # Recovery metrics
        recovery_factor = self._calculate_recovery_factor(portfolio_values)
        
        # Pain index (average drawdown)
        pain_index = abs(drawdown_series[drawdown_series < 0].mean()) if any(drawdown_series < 0) else 0
        
        # Ulcer index
        ulcer_index = np.sqrt(np.mean(drawdown_series ** 2))
        
        # Calmar ratio
        annual_return = (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) ** (252 / len(portfolio_values)) - 1
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Sterling ratio
        sterling_ratio = annual_return / abs(pain_index) if pain_index != 0 else 0
        
        # Probability metrics
        drawdown_probability = self._estimate_drawdown_probability(drawdown_series)
        expected_recovery_time = self._estimate_recovery_time()
        
        # VaR and CVaR
        returns = portfolio_values.pct_change().dropna()
        var_95 = returns.quantile(0.05)
        cvar_95 = returns[returns <= var_95].mean()
        
        return DrawdownMetrics(
            current_drawdown=current_drawdown,
            max_drawdown=max_drawdown,
            drawdown_duration=drawdown_duration,
            time_to_recovery=None,
            recovery_factor=recovery_factor,
            pain_index=pain_index,
            ulcer_index=ulcer_index,
            calmar_ratio=calmar_ratio,
            sterling_ratio=sterling_ratio,
            drawdown_probability=drawdown_probability,
            expected_recovery_time=expected_recovery_time,
            var_95=var_95,
            cvar_95=cvar_95
        )
    
    def _calculate_recovery_factor(self, portfolio_values: pd.Series) -> float:
        """محاسبه ضریب بازیابی"""
        if len(self.drawdown_history) == 0:
            return 1.0
        
        # Average recovery time vs drawdown magnitude
        recovery_times = [event.recovery_days for event in self.drawdown_history if event.recovery_days]
        drawdown_magnitudes = [event.max_drawdown for event in self.drawdown_history]
        
        if recovery_times and drawdown_magnitudes:
            avg_recovery_time = np.mean(recovery_times)
            avg_drawdown = np.mean(drawdown_magnitudes)
            return avg_recovery_time / (avg_drawdown * 100)  # Days per percent
        
        return 1.0
    
    def _estimate_drawdown_probability(self, drawdown_series: pd.Series) -> float:
        """تخمین احتمال drawdown"""
        # Probability of being in drawdown
        in_drawdown = (drawdown_series < -0.01).sum()
        total_periods = len(drawdown_series)
        return in_drawdown / total_periods if total_periods > 0 else 0
    
    def _estimate_recovery_time(self) -> float:
        """تخمین زمان بازیابی"""
        if len(self.drawdown_history) == 0:
            return 30.0  # Default 30 days
        
        recovery_times = [event.recovery_days for event in self.drawdown_history if event.recovery_days]
        return np.mean(recovery_times) if recovery_times else 30.0


class AdaptiveBrakeSystem:
    """سیستم ترمز تطبیقی"""
    
    def __init__(self, control_params: ControlParameters):
        self.control_params = control_params
        self.position_history = []
        self.brake_active = False
        self.brake_intensity = 0.0
    
    def calculate_position_adjustment(self, 
                                   current_drawdown: float,
                                   volatility: float,
                                   correlation: float) -> float:
        """محاسبه تعدیل position size"""
        
        # Base adjustment based on drawdown
        if abs(current_drawdown) < self.control_params.warning_threshold:
            base_adjustment = 1.0
        elif abs(current_drawdown) < self.control_params.action_threshold:
            # Linear reduction
            ratio = abs(current_drawdown) / self.control_params.action_threshold
            base_adjustment = 1.0 - (ratio * 0.3)  # Up to 30% reduction
        elif abs(current_drawdown) < self.control_params.emergency_threshold:
            # Aggressive reduction
            ratio = abs(current_drawdown) / self.control_params.emergency_threshold
            base_adjustment = 0.7 - (ratio * 0.5)  # 20% to 70% reduction
        else:
            # Emergency mode
            base_adjustment = 0.1  # 90% reduction
        
        # Volatility adjustment
        volatility_adjustment = 1.0 - min(volatility * self.control_params.volatility_multiplier, 0.5)
        
        # Correlation adjustment
        correlation_adjustment = 1.0 - max(0, (correlation - 0.5) * 2) * 0.3
        
        # Combined adjustment
        final_adjustment = base_adjustment * volatility_adjustment * correlation_adjustment
        
        # Apply bounds
        final_adjustment = max(self.control_params.min_position_size, 
                             min(self.control_params.max_position_size, final_adjustment))
        
        # Update brake status
        self.brake_active = final_adjustment < 0.9
        self.brake_intensity = 1.0 - final_adjustment
        
        return final_adjustment
    
    def get_recommended_action(self, current_drawdown: float) -> DrawdownAction:
        """توصیه اقدام بر اساس drawdown"""
        
        abs_drawdown = abs(current_drawdown)
        
        if abs_drawdown < self.control_params.warning_threshold:
            return DrawdownAction.MONITOR
        elif abs_drawdown < self.control_params.action_threshold:
            return DrawdownAction.REDUCE_EXPOSURE
        elif abs_drawdown < self.control_params.emergency_threshold:
            return DrawdownAction.HALT_TRADING
        else:
            return DrawdownAction.EMERGENCY_EXIT


class DrawdownPredictor:
    """پیش‌بینی drawdown"""
    
    def __init__(self, lookback_period: int = 252):
        self.lookback_period = lookback_period
        self.feature_history = []
        self.drawdown_history = []
        self.model = None
    
    def prepare_features(self, 
                        portfolio_values: pd.Series,
                        market_data: pd.DataFrame = None) -> pd.DataFrame:
        """آماده‌سازی ویژگی‌ها برای پیش‌بینی"""
        
        features = pd.DataFrame(index=portfolio_values.index)
        
        # Return-based features
        returns = portfolio_values.pct_change()
        features['return_1d'] = returns
        features['return_5d'] = returns.rolling(5).mean()
        features['return_21d'] = returns.rolling(21).mean()
        
        # Volatility features
        features['volatility_5d'] = returns.rolling(5).std()
        features['volatility_21d'] = returns.rolling(21).std()
        features['volatility_ratio'] = features['volatility_5d'] / features['volatility_21d']
        
        # Drawdown features
        running_max = portfolio_values.expanding().max()
        drawdown = (portfolio_values - running_max) / running_max
        features['current_drawdown'] = drawdown
        features['max_drawdown_21d'] = drawdown.rolling(21).min()
        features['drawdown_duration'] = self._calculate_drawdown_duration(drawdown)
        
        # Momentum features
        features['momentum_5d'] = portfolio_values.pct_change(5)
        features['momentum_21d'] = portfolio_values.pct_change(21)
        
        # Technical indicators
        features['rsi'] = self._calculate_rsi(portfolio_values)
        features['bollinger_position'] = self._calculate_bollinger_position(portfolio_values)
        
        # Market features (if available)
        if market_data is not None:
            for col in market_data.columns:
                if col in ['vix', 'volatility', 'fear_index']:
                    features[f'market_{col}'] = market_data[col]
        
        return features.dropna()
    
    def _calculate_drawdown_duration(self, drawdown_series: pd.Series) -> pd.Series:
        """محاسبه مدت drawdown"""
        duration = pd.Series(0, index=drawdown_series.index)
        current_duration = 0
        
        for i, dd in enumerate(drawdown_series):
            if dd < 0:
                current_duration += 1
            else:
                current_duration = 0
            duration.iloc[i] = current_duration
        
        return duration
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """محاسبه RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_bollinger_position(self, prices: pd.Series, window: int = 20) -> pd.Series:
        """محاسبه موقعیت در باند بولینگر"""
        rolling_mean = prices.rolling(window).mean()
        rolling_std = prices.rolling(window).std()
        upper_band = rolling_mean + (rolling_std * 2)
        lower_band = rolling_mean - (rolling_std * 2)
        
        # Position within bands (0 = lower band, 1 = upper band)
        position = (prices - lower_band) / (upper_band - lower_band)
        return position.clip(0, 1)
    
    def predict_drawdown_probability(self, 
                                   features: pd.DataFrame,
                                   horizon_days: int = 21) -> Dict[str, float]:
        """پیش‌بینی احتمال drawdown"""
        
        # Simple rule-based prediction (can be replaced with ML)
        latest_features = features.iloc[-1]
        
        # Risk factors
        risk_score = 0.0
        
        # Volatility risk
        if latest_features.get('volatility_ratio', 1.0) > 1.5:
            risk_score += 0.2
        
        # Momentum risk
        if latest_features.get('momentum_5d', 0) < -0.05:
            risk_score += 0.3
        
        # Current drawdown risk
        if latest_features.get('current_drawdown', 0) < -0.03:
            risk_score += 0.4
        
        # RSI risk
        rsi = latest_features.get('rsi', 50)
        if rsi < 30 or rsi > 70:
            risk_score += 0.1
        
        # Market risk (if available)
        if 'market_vix' in latest_features:
            if latest_features['market_vix'] > 25:
                risk_score += 0.2
        
        # Convert to probability
        probability = min(risk_score, 1.0)
        
        # Estimate severity
        if probability < 0.3:
            expected_severity = "LOW"
        elif probability < 0.6:
            expected_severity = "MODERATE"
        elif probability < 0.8:
            expected_severity = "HIGH"
        else:
            expected_severity = "SEVERE"
        
        return {
            'probability': probability,
            'expected_severity': expected_severity,
            'horizon_days': horizon_days,
            'risk_factors': {
                'volatility': latest_features.get('volatility_ratio', 1.0),
                'momentum': latest_features.get('momentum_5d', 0),
                'drawdown': latest_features.get('current_drawdown', 0),
                'rsi': latest_features.get('rsi', 50)
            }
        }


class RecoveryManager:
    """مدیریت بازیابی از drawdown"""
    
    def __init__(self, control_params: ControlParameters):
        self.control_params = control_params
        self.recovery_active = False
        self.recovery_start_date = None
        self.recovery_strategy = None
    
    def start_recovery(self, 
                      drawdown_event: DrawdownEvent,
                      market_conditions: Dict[str, Any]) -> RecoveryStrategy:
        """شروع فرآیند بازیابی"""
        
        # Determine recovery strategy based on conditions
        strategy = self._select_recovery_strategy(drawdown_event, market_conditions)
        
        self.recovery_active = True
        self.recovery_start_date = datetime.now()
        self.recovery_strategy = strategy
        
        logger.info(f"Starting recovery with strategy: {strategy.value}")
        return strategy
    
    def _select_recovery_strategy(self, 
                                drawdown_event: DrawdownEvent,
                                market_conditions: Dict[str, Any]) -> RecoveryStrategy:
        """انتخاب استراتژی بازیابی"""
        
        # Factors to consider
        drawdown_severity = drawdown_event.severity
        market_volatility = market_conditions.get('volatility', 0.2)
        market_trend = market_conditions.get('trend', 'neutral')
        
        # Decision logic
        if drawdown_severity == DrawdownSeverity.SEVERE:
            return RecoveryStrategy.CONSERVATIVE
        elif drawdown_severity == DrawdownSeverity.HIGH:
            if market_volatility > 0.3:
                return RecoveryStrategy.CONSERVATIVE
            else:
                return RecoveryStrategy.GRADUAL
        elif drawdown_severity == DrawdownSeverity.MODERATE:
            if market_trend == 'bullish' and market_volatility < 0.2:
                return RecoveryStrategy.AGGRESSIVE
            else:
                return RecoveryStrategy.GRADUAL
        else:  # LOW severity
            return RecoveryStrategy.ADAPTIVE
    
    def get_recovery_position_size(self, 
                                 current_recovery_rate: float,
                                 base_position_size: float) -> float:
        """محاسبه اندازه position برای بازیابی"""
        
        if not self.recovery_active:
            return base_position_size
        
        if self.recovery_strategy == RecoveryStrategy.CONSERVATIVE:
            # Gradual increase
            multiplier = 1.0 + (current_recovery_rate * 0.5)
        elif self.recovery_strategy == RecoveryStrategy.GRADUAL:
            # Moderate increase
            multiplier = 1.0 + (current_recovery_rate * 1.0)
        elif self.recovery_strategy == RecoveryStrategy.AGGRESSIVE:
            # Aggressive increase
            multiplier = 1.0 + (current_recovery_rate * 1.5)
        else:  # ADAPTIVE
            # Adaptive based on conditions
            multiplier = 1.0 + (current_recovery_rate * 1.2)
        
        # Apply recovery multiplier
        adjusted_size = base_position_size * multiplier * self.control_params.recovery_multiplier
        
        # Apply bounds
        return min(adjusted_size, self.control_params.max_position_size)
    
    def check_recovery_completion(self, current_drawdown: float) -> bool:
        """بررسی تکمیل بازیابی"""
        
        if not self.recovery_active:
            return False
        
        # Check if we've recovered enough
        if abs(current_drawdown) < self.control_params.recovery_threshold:
            self.recovery_active = False
            recovery_duration = (datetime.now() - self.recovery_start_date).days
            logger.info(f"Recovery completed in {recovery_duration} days")
            return True
        
        return False


class AutoDrawdownController:
    """کنترلر اصلی Auto-Drawdown"""
    
    def __init__(self, control_params: ControlParameters = None):
        self.control_params = control_params or ControlParameters()
        self.detector = DrawdownDetector(self.control_params.lookback_period)
        self.brake_system = AdaptiveBrakeSystem(self.control_params)
        self.predictor = DrawdownPredictor(self.control_params.lookback_period)
        self.recovery_manager = RecoveryManager(self.control_params)
        
        # State tracking
        self.portfolio_history = []
        self.control_history = []
        self.alerts = []
    
    def update(self, 
               portfolio_value: float,
               timestamp: datetime,
               market_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """به‌روزرسانی اصلی سیستم"""
        
        # Update detector
        current_event = self.detector.update(portfolio_value, timestamp)
        
        # Get current metrics
        if len(self.portfolio_history) > 0:
            portfolio_series = pd.Series([p['value'] for p in self.portfolio_history])
            current_metrics = self.detector.get_current_metrics(portfolio_series)
        else:
            current_metrics = None
        
        # Calculate position adjustment
        volatility = market_data.get('volatility', 0.2) if market_data else 0.2
        correlation = market_data.get('correlation', 0.5) if market_data else 0.5
        
        current_drawdown = current_metrics.current_drawdown if current_metrics else 0.0
        position_adjustment = self.brake_system.calculate_position_adjustment(
            current_drawdown, volatility, correlation
        )
        
        # Get recommended action
        recommended_action = self.brake_system.get_recommended_action(current_drawdown)
        
        # Check recovery status
        recovery_completed = self.recovery_manager.check_recovery_completion(current_drawdown)
        
        # Predict future drawdown
        drawdown_prediction = None
        if len(self.portfolio_history) > 30:
            try:
                portfolio_series = pd.Series([p['value'] for p in self.portfolio_history])
                features = self.predictor.prepare_features(portfolio_series)
                if len(features) > 0:
                    drawdown_prediction = self.predictor.predict_drawdown_probability(features)
            except Exception as e:
                logger.warning(f"Prediction failed: {e}")
        
        # Store history
        self.portfolio_history.append({
            'timestamp': timestamp,
            'value': portfolio_value,
            'drawdown': current_drawdown,
            'position_adjustment': position_adjustment
        })
        
        # Generate alerts
        self._generate_alerts(current_event, current_metrics, recommended_action)
        
        # Create response
        response = {
            'timestamp': timestamp,
            'portfolio_value': portfolio_value,
            'current_drawdown': current_drawdown,
            'position_adjustment': position_adjustment,
            'recommended_action': recommended_action.value,
            'brake_active': self.brake_system.brake_active,
            'brake_intensity': self.brake_system.brake_intensity,
            'recovery_active': self.recovery_manager.recovery_active,
            'recovery_completed': recovery_completed,
            'current_event': current_event,
            'metrics': current_metrics,
            'prediction': drawdown_prediction,
            'alerts': self.alerts[-5:] if self.alerts else []  # Last 5 alerts
        }
        
        return response
    
    def _generate_alerts(self, 
                        current_event: DrawdownEvent,
                        metrics: DrawdownMetrics,
                        action: DrawdownAction):
        """تولید هشدارها"""
        
        alerts = []
        
        # Drawdown severity alerts
        if current_event and current_event.is_active:
            if current_event.severity == DrawdownSeverity.SEVERE:
                alerts.append({
                    'type': 'SEVERE_DRAWDOWN',
                    'message': f'Severe drawdown detected: {current_event.max_drawdown:.2%}',
                    'timestamp': datetime.now(),
                    'severity': 'HIGH'
                })
            elif current_event.severity == DrawdownSeverity.HIGH:
                alerts.append({
                    'type': 'HIGH_DRAWDOWN',
                    'message': f'High drawdown detected: {current_event.max_drawdown:.2%}',
                    'timestamp': datetime.now(),
                    'severity': 'MEDIUM'
                })
        
        # Action alerts
        if action != DrawdownAction.MONITOR:
            alerts.append({
                'type': 'ACTION_REQUIRED',
                'message': f'Action required: {action.value}',
                'timestamp': datetime.now(),
                'severity': 'MEDIUM'
            })
        
        # Brake system alerts
        if self.brake_system.brake_active:
            alerts.append({
                'type': 'BRAKE_ACTIVE',
                'message': f'Brake system active: {self.brake_system.brake_intensity:.1%} intensity',
                'timestamp': datetime.now(),
                'severity': 'LOW'
            })
        
        self.alerts.extend(alerts)
        
        # Keep only recent alerts
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """داده‌های داشبورد"""
        
        if not self.portfolio_history:
            return {}
        
        # Recent portfolio values
        recent_values = [p['value'] for p in self.portfolio_history[-30:]]
        recent_dates = [p['timestamp'] for p in self.portfolio_history[-30:]]
        
        # Calculate metrics
        portfolio_series = pd.Series([p['value'] for p in self.portfolio_history])
        current_metrics = self.detector.get_current_metrics(portfolio_series)
        
        return {
            'current_status': {
                'portfolio_value': self.portfolio_history[-1]['value'],
                'current_drawdown': current_metrics.current_drawdown,
                'max_drawdown': current_metrics.max_drawdown,
                'drawdown_duration': current_metrics.drawdown_duration,
                'brake_active': self.brake_system.brake_active,
                'recovery_active': self.recovery_manager.recovery_active
            },
            'metrics': {
                'pain_index': current_metrics.pain_index,
                'ulcer_index': current_metrics.ulcer_index,
                'calmar_ratio': current_metrics.calmar_ratio,
                'sterling_ratio': current_metrics.sterling_ratio,
                'var_95': current_metrics.var_95,
                'cvar_95': current_metrics.cvar_95
            },
            'historical_data': {
                'dates': recent_dates,
                'values': recent_values,
                'drawdowns': [p['drawdown'] for p in self.portfolio_history[-30:]],
                'adjustments': [p['position_adjustment'] for p in self.portfolio_history[-30:]]
            },
            'drawdown_events': [
                {
                    'start_date': event.start_date,
                    'end_date': event.end_date,
                    'max_drawdown': event.max_drawdown,
                    'duration': event.duration_days,
                    'severity': event.severity.value
                }
                for event in self.detector.drawdown_history[-10:]  # Last 10 events
            ],
            'recent_alerts': self.alerts[-10:] if self.alerts else [],
            'control_parameters': {
                'warning_threshold': self.control_params.warning_threshold,
                'action_threshold': self.control_params.action_threshold,
                'emergency_threshold': self.control_params.emergency_threshold,
                'min_position_size': self.control_params.min_position_size,
                'max_position_size': self.control_params.max_position_size
            }
        }
    
    def simulate_scenarios(self, 
                          scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
        """شبیه‌سازی سناریوها"""
        
        results = {}
        
        for scenario_name, scenario_data in scenarios:
            # Simulate portfolio path
            portfolio_values = scenario_data['portfolio_values']
            timestamps = scenario_data['timestamps']
            
            # Reset controller state
            temp_controller = AutoDrawdownController(self.control_params)
            
            scenario_results = []
            for value, timestamp in zip(portfolio_values, timestamps):
                result = temp_controller.update(value, timestamp)
                scenario_results.append(result)
            
            # Analyze results
            final_metrics = temp_controller.detector.get_current_metrics(
                pd.Series(portfolio_values)
            )
            
            results[scenario_name] = {
                'final_drawdown': final_metrics.current_drawdown,
                'max_drawdown': final_metrics.max_drawdown,
                'total_alerts': len(temp_controller.alerts),
                'brake_activations': sum(1 for r in scenario_results if r['brake_active']),
                'average_position_adjustment': np.mean([r['position_adjustment'] for r in scenario_results]),
                'recovery_periods': len([event for event in temp_controller.detector.drawdown_history if event.recovery_days])
            }
        
        return results


# Backward compatibility
class DrawdownController(AutoDrawdownController):
    """Backward compatibility class"""
    pass


if __name__ == "__main__":
    # Example usage
    print("Auto-Drawdown Control System initialized successfully!")
    
    # Generate sample data
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=252, freq='D')
    
    # Simulate portfolio with drawdown periods
    returns = np.random.normal(0.001, 0.02, 252)
    
    # Add some drawdown periods
    returns[50:70] = np.random.normal(-0.01, 0.03, 20)  # Drawdown period 1
    returns[150:180] = np.random.normal(-0.008, 0.025, 30)  # Drawdown period 2
    
    portfolio_values = pd.Series(1000 * (1 + pd.Series(returns)).cumprod(), index=dates)
    
    # Initialize controller
    controller = AutoDrawdownController()
    
    # Simulate real-time updates
    results = []
    for date, value in portfolio_values.items():
        result = controller.update(value, date)
        results.append(result)
    
    # Print summary
    final_result = results[-1]
    print(f"Final Portfolio Value: ${final_result['portfolio_value']:.2f}")
    print(f"Current Drawdown: {final_result['current_drawdown']:.2%}")
    print(f"Max Drawdown: {final_result['metrics'].max_drawdown:.2%}")
    print(f"Total Alerts: {len(controller.alerts)}")
    print(f"Brake Activations: {sum(1 for r in results if r['brake_active'])}") 