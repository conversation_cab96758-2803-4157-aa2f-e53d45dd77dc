#!/usr/bin/env python3
"""
🔌 تست اتصال بدون پروکسی
"""

import os
import sys

# غیرفعال کردن کامل پروکسی
for proxy_env in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']:
    if proxy_env in os.environ:
        del os.environ[proxy_env]

try:
    from huggingface_hub import HfApi
    import requests
    
    # تست اتصال مستقیم
    print("🔄 تست اتصال مستقیم به HuggingFace...")
    
    # ایجاد session بدون proxy
    session = requests.Session()
    session.proxies = {}
    
    api = HfApi()
    
    # تست روی یک مدل ساده و معروف
    print("⏳ تست مدل distilbert...")
    try:
        info = api.model_info("distilbert-base-uncased", timeout=10)
        print(f"✅ موفق! دانلودها: {info.downloads:,}")
        
        # حالا تست یک مدل مالی
        print("⏳ تست مدل مالی...")
        info2 = api.model_info("ProsusAI/finbert", timeout=10)
        print(f"✅ موفق! FinBERT دانلودها: {info2.downloads:,}")
        
        print("\n🎉 اتصال موفق است! می‌توانیم مدل‌ها را تست کنیم.")
        
    except Exception as e:
        print(f"❌ خطا در تست مدل‌ها: {e}")
        
        # تست اتصال خام
        print("🔄 تست اتصال خام...")
        response = requests.get("https://httpbin.org/ip", timeout=5)
        print(f"IP شما: {response.json()}")
        
except ImportError as e:
    print(f"❌ خطا در import: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ خطای کلی: {e}")
    
    # راه‌حل‌های ممکن
    print("\n💡 راه‌حل‌های ممکن:")
    print("1. VPN روشن کنید")
    print("2. پروکسی سیستم را چک کنید")
    print("3. از Offline models استفاده کنید")
    print("4. اتصال اینترنت را بررسی کنید") 