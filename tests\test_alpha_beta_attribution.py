#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تست‌های Alpha/Beta Attribution System
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.alpha_beta_attribution import (
    AlphaBetaAttributionEngine,
    KalmanFilterAlphaBeta,
    MultiFactorModel,
    AlphaMLPredictor,
    RiskFactorType,
    AttributionPeriod,
    AlphaBetaResult,
    FactorExposure,
    AttributionResult
)


class TestKalmanFilterAlphaBeta:
    """تست فیلتر کالمن Alpha/Beta"""
    
    def test_initialization(self):
        """تست اولیه‌سازی"""
        kalman = KalmanFilterAlphaBeta()
        assert kalman.process_noise == 0.01
        assert kalman.observation_noise == 0.1
        assert not kalman.is_initialized
        assert np.allclose(kalman.state, [0.0, 1.0])
    
    def test_reset(self):
        """تست ریست کردن فیلتر"""
        kalman = KalmanFilterAlphaBeta()
        kalman.is_initialized = True
        kalman.state = np.array([0.05, 1.5])
        
        kalman.reset()
        
        assert not kalman.is_initialized
        assert np.allclose(kalman.state, [0.0, 1.0])
    
    def test_first_update(self):
        """تست اولین به‌روزرسانی"""
        kalman = KalmanFilterAlphaBeta()
        
        alpha, beta = kalman.update(0.02, 0.015)
        
        assert kalman.is_initialized
        assert alpha == 0.0  # First alpha should be 0
        assert abs(beta - (0.02 / 0.015)) < 1e-10
    
    def test_multiple_updates(self):
        """تست چندین به‌روزرسانی"""
        kalman = KalmanFilterAlphaBeta()
        
        # Generate sample data
        np.random.seed(42)
        market_returns = np.random.normal(0.001, 0.02, 10)
        portfolio_returns = 0.0005 + 1.2 * market_returns + np.random.normal(0, 0.01, 10)
        
        alphas = []
        betas = []
        
        for port_ret, market_ret in zip(portfolio_returns, market_returns):
            alpha, beta = kalman.update(port_ret, market_ret)
            alphas.append(alpha)
            betas.append(beta)
        
        # Beta should converge towards 1.2
        assert abs(betas[-1] - 1.2) < 0.5
        
        # Alpha should be positive (around 0.0005)
        assert alphas[-1] > -0.01


class TestMultiFactorModel:
    """تست مدل چندعاملی"""
    
    def test_initialization(self):
        """تست اولیه‌سازی"""
        model = MultiFactorModel()
        assert len(model.factors) == 4
        assert RiskFactorType.MARKET in model.factors
        assert not model.is_fitted
    
    def test_custom_factors(self):
        """تست فاکتورهای سفارشی"""
        factors = [RiskFactorType.MARKET, RiskFactorType.SIZE]
        model = MultiFactorModel(factors)
        assert len(model.factors) == 2
        assert model.factors == factors
    
    def test_fit_model(self):
        """تست آموزش مدل"""
        # Generate sample data
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        
        # Portfolio returns
        returns = pd.Series(np.random.normal(0.001, 0.02, 100), index=dates)
        
        # Factor returns
        factor_data = {
            'market': np.random.normal(0.0008, 0.015, 100),
            'size': np.random.normal(0.0002, 0.01, 100),
            'value': np.random.normal(0.0001, 0.008, 100),
            'momentum': np.random.normal(0.0003, 0.012, 100)
        }
        factor_returns = pd.DataFrame(factor_data, index=dates)
        
        model = MultiFactorModel()
        result = model.fit(returns, factor_returns)
        
        assert model.is_fitted
        assert 'alpha' in result
        assert 'exposures' in result
        assert 'r_squared' in result
        assert len(result['exposures']) == 4
    
    def test_predict_alpha(self):
        """تست پیش‌بینی Alpha"""
        # First fit the model
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        returns = pd.Series(np.random.normal(0.001, 0.02, 100), index=dates)
        
        factor_data = {
            'market': np.random.normal(0.0008, 0.015, 100),
            'size': np.random.normal(0.0002, 0.01, 100),
            'value': np.random.normal(0.0001, 0.008, 100),
            'momentum': np.random.normal(0.0003, 0.012, 100)
        }
        factor_returns = pd.DataFrame(factor_data, index=dates)
        
        model = MultiFactorModel()
        model.fit(returns, factor_returns)
        
        # Predict alpha
        new_factors = pd.DataFrame({
            'market': [0.01],
            'size': [0.005],
            'value': [0.002],
            'momentum': [0.008]
        })
        
        predicted_alpha = model.predict_alpha(new_factors)
        assert isinstance(predicted_alpha, float)


class TestAlphaMLPredictor:
    """تست پیش‌بینی Alpha با ML"""
    
    def test_initialization(self):
        """تست اولیه‌سازی"""
        predictor = AlphaMLPredictor()
        assert predictor.model_type == 'random_forest'
        assert predictor.model is None
    
    def test_prepare_features(self):
        """تست آماده‌سازی ویژگی‌ها"""
        # Generate sample data
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        returns = pd.Series(np.random.normal(0.001, 0.02, 50), index=dates)
        
        market_data = pd.DataFrame({
            'market_return': np.random.normal(0.0008, 0.015, 50)
        }, index=dates)
        
        predictor = AlphaMLPredictor()
        features = predictor.prepare_features(returns, market_data)
        
        assert isinstance(features, pd.DataFrame)
        assert len(features) < len(returns)  # Due to rolling windows
        assert 'return_1d' in features.columns
        assert 'volatility_21d' in features.columns
    
    def test_rsi_calculation(self):
        """تست محاسبه RSI"""
        prices = pd.Series([100, 102, 101, 103, 105, 104, 106, 108, 107, 109])
        predictor = AlphaMLPredictor()
        
        rsi = predictor._calculate_rsi(prices, window=5)
        
        assert isinstance(rsi, pd.Series)
        assert len(rsi) == len(prices)
        # RSI should be between 0 and 100
        valid_rsi = rsi.dropna()
        assert all(0 <= val <= 100 for val in valid_rsi)
    
    def test_train_and_predict(self):
        """تست آموزش و پیش‌بینی"""
        # Generate sample data
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        returns = pd.Series(np.random.normal(0.001, 0.02, 100), index=dates)
        
        market_data = pd.DataFrame({
            'market_return': np.random.normal(0.0008, 0.015, 100)
        }, index=dates)
        
        predictor = AlphaMLPredictor()
        features = predictor.prepare_features(returns, market_data)
        
        # Create target alpha (simplified)
        target_alpha = returns.rolling(21).mean().dropna()
        
        # Align features and target
        common_index = features.index.intersection(target_alpha.index)
        if len(common_index) > 10:
            features_aligned = features.loc[common_index]
            target_aligned = target_alpha.loc[common_index]
            
            # Train
            train_result = predictor.train(features_aligned, target_aligned)
            
            assert 'r2_score' in train_result
            assert 'mse' in train_result
            assert predictor.model is not None
            
            # Predict
            predictions = predictor.predict(features_aligned)
            assert len(predictions) == len(features_aligned)


class TestAlphaBetaAttributionEngine:
    """تست موتور اصلی Attribution"""
    
    def test_initialization(self):
        """تست اولیه‌سازی"""
        engine = AlphaBetaAttributionEngine()
        assert engine.benchmark_returns is None
        assert isinstance(engine.kalman_filter, KalmanFilterAlphaBeta)
        assert isinstance(engine.multi_factor_model, MultiFactorModel)
        assert isinstance(engine.ml_predictor, AlphaMLPredictor)
    
    def test_calculate_alpha_beta(self):
        """تست محاسبه Alpha/Beta اصلی"""
        # Generate sample data
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        
        benchmark_returns = pd.Series(np.random.normal(0.0008, 0.015, 100), index=dates)
        portfolio_returns = pd.Series(
            0.0005 + 1.2 * benchmark_returns + np.random.normal(0, 0.01, 100),
            index=dates
        )
        
        engine = AlphaBetaAttributionEngine()
        result = engine.calculate_alpha_beta(portfolio_returns, benchmark_returns)
        
        assert isinstance(result, AlphaBetaResult)
        assert abs(result.beta - 1.2) < 0.3  # Should be close to 1.2
        assert result.alpha > -0.01  # Should be positive
        assert 0 <= result.r_squared <= 1
        assert result.observations == 100
        assert result.period_start == dates[0]
        assert result.period_end == dates[-1]
    
    def test_realtime_alpha_beta(self):
        """تست Alpha/Beta زمان واقعی"""
        engine = AlphaBetaAttributionEngine()
        
        # Simulate real-time updates
        alpha, beta = engine.realtime_alpha_beta(0.02, 0.015)
        assert isinstance(alpha, float)
        assert isinstance(beta, float)
        
        # Check history
        assert len(engine.alpha_history) == 1
        assert engine.alpha_history[0]['alpha'] == alpha
        assert engine.alpha_history[0]['beta'] == beta
    
    def test_decompose_alpha_sources(self):
        """تست تجزیه منابع Alpha"""
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        portfolio_returns = pd.Series(np.random.normal(0.001, 0.02, 50), index=dates)
        
        engine = AlphaBetaAttributionEngine()
        alpha_sources = engine.decompose_alpha_sources(portfolio_returns)
        
        assert isinstance(alpha_sources, dict)
        assert 'market_timing' in alpha_sources
        assert 'sector_allocation' in alpha_sources
        assert 'momentum' in alpha_sources
        assert 'mean_reversion' in alpha_sources
    
    def test_generate_attribution_report(self):
        """تست تولید گزارش Attribution"""
        # Generate sample data
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        
        benchmark_returns = pd.Series(np.random.normal(0.0008, 0.015, 100), index=dates)
        portfolio_returns = pd.Series(
            0.0005 + 1.1 * benchmark_returns + np.random.normal(0, 0.01, 100),
            index=dates
        )
        
        engine = AlphaBetaAttributionEngine()
        report = engine.generate_attribution_report(portfolio_returns, benchmark_returns)
        
        assert isinstance(report, dict)
        assert 'timestamp' in report
        assert 'alpha_beta' in report
        assert 'alpha_sources' in report
        assert 'performance_summary' in report
        
        # Check alpha_beta results
        alpha_beta = report['alpha_beta']
        assert isinstance(alpha_beta, AlphaBetaResult)
        
        # Check performance summary
        perf_summary = report['performance_summary']
        assert 'total_return' in perf_summary
        assert 'annualized_return' in perf_summary
        assert 'volatility' in perf_summary
        assert 'sharpe_ratio' in perf_summary
    
    def test_get_realtime_metrics(self):
        """تست معیارهای زمان واقعی"""
        engine = AlphaBetaAttributionEngine()
        
        # Initially empty
        metrics = engine.get_realtime_metrics()
        assert metrics == {}
        
        # Add some data
        engine.realtime_alpha_beta(0.02, 0.015)
        engine.realtime_alpha_beta(0.018, 0.014)
        engine.realtime_alpha_beta(0.022, 0.016)
        
        metrics = engine.get_realtime_metrics()
        
        assert 'current_alpha' in metrics
        assert 'current_beta' in metrics
        assert 'alpha_trend' in metrics
        assert 'beta_trend' in metrics
        assert 'last_update' in metrics
        assert metrics['observations_count'] == 3


class TestIntegration:
    """تست‌های یکپارچگی"""
    
    def test_full_attribution_workflow(self):
        """تست جریان کامل Attribution"""
        # Generate realistic sample data
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        
        # Market factors
        market_return = np.random.normal(0.0008, 0.015, 252)
        size_factor = np.random.normal(0.0002, 0.01, 252)
        value_factor = np.random.normal(0.0001, 0.008, 252)
        momentum_factor = np.random.normal(0.0003, 0.012, 252)
        
        # Portfolio with known exposures
        true_alpha = 0.0005
        true_beta = 1.2
        size_exposure = 0.3
        value_exposure = -0.1
        momentum_exposure = 0.2
        
        portfolio_returns = pd.Series(
            true_alpha + 
            true_beta * market_return +
            size_exposure * size_factor +
            value_exposure * value_factor +
            momentum_exposure * momentum_factor +
            np.random.normal(0, 0.008, 252),
            index=dates
        )
        
        benchmark_returns = pd.Series(market_return, index=dates)
        
        factor_returns = pd.DataFrame({
            'market': market_return,
            'size': size_factor,
            'value': value_factor,
            'momentum': momentum_factor
        }, index=dates)
        
        # Initialize engine
        engine = AlphaBetaAttributionEngine(benchmark_returns)
        
        # Test basic alpha/beta calculation
        alpha_beta_result = engine.calculate_alpha_beta(portfolio_returns, benchmark_returns)
        
        # Should recover approximately true values
        assert abs(alpha_beta_result.alpha - true_alpha) < 0.001
        assert abs(alpha_beta_result.beta - true_beta) < 0.2
        assert alpha_beta_result.r_squared > 0.5
        
        # Test multi-factor attribution
        multi_factor_result = engine.multi_factor_attribution(portfolio_returns, factor_returns)
        assert 'alpha' in multi_factor_result
        assert 'exposures' in multi_factor_result
        
        # Test full report generation
        report = engine.generate_attribution_report(
            portfolio_returns, 
            benchmark_returns, 
            factor_returns
        )
        
        assert isinstance(report, dict)
        assert 'alpha_beta' in report
        assert 'multi_factor' in report
        assert 'alpha_sources' in report
        assert 'performance_summary' in report
    
    def test_real_time_updates(self):
        """تست به‌روزرسانی‌های زمان واقعی"""
        engine = AlphaBetaAttributionEngine()
        
        # Simulate a trading day with multiple updates
        np.random.seed(42)
        market_returns = np.random.normal(0.0008, 0.015, 20)
        portfolio_returns = 0.0005 + 1.1 * market_returns + np.random.normal(0, 0.01, 20)
        
        alphas = []
        betas = []
        
        for port_ret, market_ret in zip(portfolio_returns, market_returns):
            alpha, beta = engine.realtime_alpha_beta(port_ret, market_ret)
            alphas.append(alpha)
            betas.append(beta)
        
        # Check convergence
        assert len(alphas) == 20
        assert len(betas) == 20
        
        # Beta should converge towards 1.1
        assert abs(betas[-1] - 1.1) < 0.5
        
        # Get real-time metrics
        metrics = engine.get_realtime_metrics()
        assert metrics['observations_count'] == 20
        assert 'alpha_trend' in metrics
        assert 'beta_trend' in metrics


if __name__ == "__main__":
    pytest.main([__file__]) 