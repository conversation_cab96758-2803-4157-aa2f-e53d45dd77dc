# مستند جامع: AutoMarketMaker

## مسئولیت
سازنده بازار خودکار مبتنی بر یادگیری تقویتی عمیق با مدیریت موجودی تطبیقی، کنترل ریسک و spread هوشمند.

## پارامترها
- spread_range: محدوده spread (حداقل، حداکثر)
- inventory_limit: حد مجاز موجودی
- learning_rate: نرخ یادگیری
- gamma: ضریب تخفیف
- epsilon: نرخ اکتشاف
- risk_aversion: ضریب ریسک‌گریزی
- use_deep_rl: استفاده از یادگیری عمیق

## متدهای کلیدی
- decide_spread: تصمیم‌گیری spread بر اساس RL
- quote: تولید قیمت‌های bid/ask
- update_inventory: به‌روزرسانی موجودی
- simulate_market_making: شبیه‌سازی آفلاین
- save_model/load_model: ذخیره و بارگذاری مدل

## نمونه کد
```python
from utils.auto_market_maker import AutoMarketMaker
amm = AutoMarketMaker(spread_range=(0.0001, 0.001), inventory_limit=10000)
quote = amm.quote(order_book, 'EURUSD')
amm.update_inventory('buy', 100, quote['ask'])
```

## مدیریت خطا
در صورت نبود order book یا خطا در مدل، مقادیر پیش‌فرض برگردانده می‌شود.

## بهترین شیوه
- موجودی را مداوم نظارت و مدیریت کنید.
- از risk_aversion برای تنظیم محافظه‌کاری استفاده کنید.

## نمودار
- نمودار موجودی، PnL و spread در طول زمان قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول فقط در تست‌ها استفاده شده و در جریان اصلی معاملات فعال نیست.

## وضعیت عملیاتی
⚠️ فقط در سطح تست و توسعه - نیاز به اتصال به سیستم اصلی دارد. 