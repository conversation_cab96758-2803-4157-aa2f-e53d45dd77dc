"""
📝 Advanced Logging System for Pearl-3x7B
سیستم لاگینگ پیشرفته مخصوص Pearl-3x7B

مطابق نقشه راه گنج:
- ثبت دقیق لاگ‌های آموزش مدل‌ها
- نظارت بر عملکرد و ارزیابی
- لاگ‌گیری ساختاریافته برای تحلیل
- پشتیبانی از JSON و structured logging
"""

import os
import sys
import logging
import logging.handlers
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
import json
import time
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class LogLevel(Enum):
    """سطح لاگ"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class LogCategory(Enum):
    """دسته‌بندی لاگ‌ها"""
    SYSTEM = "system"
    MODEL_TRAINING = "model_training"
    MODEL_EVALUATION = "model_evaluation"
    DATA_PROCESSING = "data_processing"
    MEMORY_MANAGEMENT = "memory_management"
    PERFORMANCE = "performance"
    ERROR_HANDLING = "error_handling"
    PEARL_BRAIN = "pearl_brain"

@dataclass
class TrainingLogEntry:
    """ورودی لاگ آموزش"""
    timestamp: datetime
    model_name: str
    epoch: int
    batch: int
    loss: float
    accuracy: Optional[float] = None
    learning_rate: Optional[float] = None
    memory_usage: Optional[float] = None
    duration: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class EvaluationLogEntry:
    """ورودی لاگ ارزیابی"""
    timestamp: datetime
    model_name: str
    dataset: str
    metrics: Dict[str, float]
    duration: float
    memory_peak: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class StructuredJSONFormatter(logging.Formatter):
    """فرمت‌کننده JSON ساختاریافته"""

    def format(self, record):
        """فرمت کردن به JSON"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }

        # اضافه کردن اطلاعات اضافی
        if hasattr(record, 'model_name'):
            log_entry['model_name'] = record.model_name
        if hasattr(record, 'category'):
            log_entry['category'] = record.category
        if hasattr(record, 'session_id'):
            log_entry['session_id'] = record.session_id
        if hasattr(record, 'metrics'):
            log_entry['metrics'] = record.metrics
        if hasattr(record, 'memory_usage'):
            log_entry['memory_usage'] = record.memory_usage
        if hasattr(record, 'duration'):
            log_entry['duration'] = record.duration

        # Exception info
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)

        return json.dumps(log_entry, ensure_ascii=False, default=str)

class TradingSystemFormatter(logging.Formatter):
    """فرمت‌کننده سفارشی برای سیستم معاملاتی"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Color codes for different log levels
        self.colors = {
            'DEBUG': '\033[36m',     # Cyan
            'INFO': '\033[32m',      # Green
            'WARNING': '\033[33m',   # Yellow
            'ERROR': '\033[31m',     # Red
            'CRITICAL': '\033[35m',  # Magenta
            'RESET': '\033[0m'       # Reset
        }
    
    def format(self, record):
        """فرمت کردن پیام لاگ"""
        # Add color for console output
        if hasattr(record, 'levelname') and record.levelname in self.colors:
            color = self.colors[record.levelname]
            reset = self.colors['RESET']
            record.levelname = f"{color}{record.levelname}{reset}"
        
        # Add trading system context
        if hasattr(record, 'symbol'):
            record.msg = f"[{record.symbol}] {record.msg}"
        
        if hasattr(record, 'strategy'):
            record.msg = f"[{record.strategy}] {record.msg}"
        
        return super().format(record)

class PearlIntelligentLogger:
    """لاگر هوشمند مخصوص Pearl-3x7B"""

    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.logger = logging.getLogger(name)
        self.training_logs: List[TrainingLogEntry] = []
        self.evaluation_logs: List[EvaluationLogEntry] = []
        self.structured_logs: List[Dict[str, Any]] = []
        self.session_id = None
        self.setup_logger()

    def start_training_session(self, model_name: str, session_id: str = None):
        """شروع جلسه آموزش"""
        self.session_id = session_id or f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.log_training_start(model_name)
        return self.session_id

    def end_training_session(self):
        """پایان جلسه آموزش"""
        if self.session_id:
            self.log_training_end()
            self.session_id = None

    def log_training_step(self, model_name: str, epoch: int, batch: int,
                         loss: float, accuracy: float = None,
                         learning_rate: float = None, memory_usage: float = None,
                         duration: float = None, **kwargs):
        """لاگ مرحله آموزش"""
        entry = TrainingLogEntry(
            timestamp=datetime.now(),
            model_name=model_name,
            epoch=epoch,
            batch=batch,
            loss=loss,
            accuracy=accuracy,
            learning_rate=learning_rate,
            memory_usage=memory_usage,
            duration=duration,
            metadata=kwargs
        )

        self.training_logs.append(entry)

        # Log to file
        self.logger.info(
            f"Training step - Epoch: {epoch}, Batch: {batch}, Loss: {loss:.4f}",
            extra={
                'category': LogCategory.MODEL_TRAINING.value,
                'model_name': model_name,
                'session_id': self.session_id,
                'metrics': {'loss': loss, 'accuracy': accuracy},
                'memory_usage': memory_usage,
                'duration': duration
            }
        )

    def log_evaluation_result(self, model_name: str, dataset: str,
                            metrics: Dict[str, float], duration: float,
                            memory_peak: float = None, **kwargs):
        """لاگ نتیجه ارزیابی"""
        entry = EvaluationLogEntry(
            timestamp=datetime.now(),
            model_name=model_name,
            dataset=dataset,
            metrics=metrics,
            duration=duration,
            memory_peak=memory_peak,
            metadata=kwargs
        )

        self.evaluation_logs.append(entry)

        # Log to file
        metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        self.logger.info(
            f"Evaluation completed - Dataset: {dataset}, Metrics: {metrics_str}",
            extra={
                'category': LogCategory.MODEL_EVALUATION.value,
                'model_name': model_name,
                'session_id': self.session_id,
                'metrics': metrics,
                'duration': duration,
                'memory_usage': memory_peak
            }
        )

    def log_training_start(self, model_name: str):
        """لاگ شروع آموزش"""
        self.logger.info(
            f"🎯 Training started for model: {model_name}",
            extra={
                'category': LogCategory.MODEL_TRAINING.value,
                'model_name': model_name,
                'session_id': self.session_id
            }
        )

    def log_training_end(self):
        """لاگ پایان آموزش"""
        self.logger.info(
            f"✅ Training session completed: {self.session_id}",
            extra={
                'category': LogCategory.MODEL_TRAINING.value,
                'session_id': self.session_id
            }
        )

    def log_memory_usage(self, memory_mb: float, context: str = ""):
        """لاگ مصرف حافظه"""
        self.logger.info(
            f"Memory usage: {memory_mb:.1f} MB {context}",
            extra={
                'category': LogCategory.MEMORY_MANAGEMENT.value,
                'memory_usage': memory_mb,
                'session_id': self.session_id
            }
        )

    def log_performance_metric(self, metric_name: str, value: float, unit: str = ""):
        """لاگ متریک عملکرد"""
        self.logger.info(
            f"Performance - {metric_name}: {value:.4f} {unit}",
            extra={
                'category': LogCategory.PERFORMANCE.value,
                'metrics': {metric_name: value},
                'session_id': self.session_id
            }
        )

    def log_pearl_decision(self, decision: str, confidence: float, reasoning: str = ""):
        """لاگ تصمیم Pearl-3x7B"""
        self.logger.info(
            f"🧠 Pearl Decision: {decision} (Confidence: {confidence:.2f}) - {reasoning}",
            extra={
                'category': LogCategory.PEARL_BRAIN.value,
                'session_id': self.session_id,
                'metrics': {'confidence': confidence}
            }
        )
    
    def setup_logger(self):
        """تنظیم لاگر برای Pearl-3x7B"""
        # Clear existing handlers
        self.logger.handlers.clear()

        # Set level
        level = self.config.get('level', 'INFO')
        self.logger.setLevel(getattr(logging, level.upper()))

        # Create formatters
        console_formatter = TradingSystemFormatter(
            fmt=self.config.get('format',
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # Console handler
        if self.config.get('console_output', True):
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)

        # Standard file handler
        file_path = self.config.get('file_path', 'logs/pearl_system.log')
        if file_path:
            # Create directory if needed
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)

            # Rotating file handler
            file_handler = logging.handlers.RotatingFileHandler(
                file_path,
                maxBytes=self.config.get('max_file_size', 20971520),  # 20MB for AI logs
                backupCount=self.config.get('backup_count', 10),
                encoding='utf-8'
            )

            # File formatter (no colors)
            file_formatter = logging.Formatter(
                fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)

        # JSON structured log handler for analysis
        json_path = self.config.get('json_log_path', 'logs/pearl_structured.jsonl')
        if json_path:
            Path(json_path).parent.mkdir(parents=True, exist_ok=True)

            json_handler = logging.handlers.RotatingFileHandler(
                json_path,
                maxBytes=self.config.get('json_max_file_size', 50971520),  # 50MB for JSON
                backupCount=self.config.get('json_backup_count', 5),
                encoding='utf-8'
            )

            json_formatter = StructuredJSONFormatter()
            json_handler.setFormatter(json_formatter)
            self.logger.addHandler(json_handler)

        # Category-specific handlers
        self._setup_category_handlers()

    def _setup_category_handlers(self):
        """تنظیم handler های مخصوص هر دسته"""
        categories = {
            LogCategory.MODEL_TRAINING: 'logs/training.log',
            LogCategory.MODEL_EVALUATION: 'logs/evaluation.log',
            LogCategory.MEMORY_MANAGEMENT: 'logs/memory.log',
            LogCategory.PERFORMANCE: 'logs/performance.log',
            LogCategory.PEARL_BRAIN: 'logs/pearl_decisions.log'
        }

        for category, log_file in categories.items():
            if self.config.get(f'enable_{category.value}_log', True):
                Path(log_file).parent.mkdir(parents=True, exist_ok=True)

                handler = logging.handlers.RotatingFileHandler(
                    log_file,
                    maxBytes=10485760,  # 10MB
                    backupCount=3,
                    encoding='utf-8'
                )

                # Filter for specific category
                handler.addFilter(lambda record, cat=category:
                                getattr(record, 'category', None) == cat.value)

                formatter = logging.Formatter(
                    fmt='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
                handler.setFormatter(formatter)
                self.logger.addHandler(handler)
    
    def info(self, message: str, symbol: str = None, strategy: str = None, **kwargs):
        """پیام اطلاعاتی"""
        extra = {}
        if symbol:
            extra['symbol'] = symbol
        if strategy:
            extra['strategy'] = strategy
        extra.update(kwargs)
        
        self.logger.info(message, extra=extra)
    
    def error(self, message: str, symbol: str = None, strategy: str = None, **kwargs):
        """پیام خطا"""
        extra = {}
        if symbol:
            extra['symbol'] = symbol
        if strategy:
            extra['strategy'] = strategy
        extra.update(kwargs)
        
        self.logger.error(message, extra=extra)

    def log_structured(self, level: str, event_type: str, data: Dict[str, Any],
                      category: LogCategory = None, **kwargs):
        """لاگ ساختاریافته پیشرفته"""
        structured_data = {
            'event_type': event_type,
            'timestamp': datetime.now().isoformat(),
            'data': data,
            'category': category.value if category else 'general'
        }
        structured_data.update(kwargs)

        # Add to structured logs
        self.structured_logs.append(structured_data)

        # Log with appropriate level
        log_method = getattr(self.logger, level.lower(), self.logger.info)
        extra = {'structured_data': structured_data}
        if category:
            extra['category'] = category.value

        message = f"[{event_type}] {data.get('message', 'Structured log entry')}"
        log_method(message, extra=extra)

    def log_metric(self, metric_name: str, value: float, unit: str = None,
                   tags: Dict[str, str] = None, timestamp: datetime = None):
        """لاگ متریک‌های عملکرد"""
        metric_data = {
            'metric_name': metric_name,
            'value': value,
            'unit': unit or 'count',
            'tags': tags or {},
            'timestamp': (timestamp or datetime.now()).isoformat()
        }

        self.log_structured(
            level='info',
            event_type='metric',
            data=metric_data,
            category=LogCategory.PERFORMANCE
        )

    def log_event(self, event_name: str, event_data: Dict[str, Any],
                  severity: str = 'info', category: LogCategory = None):
        """لاگ رویداد سیستم"""
        event_entry = {
            'event_name': event_name,
            'severity': severity,
            'event_data': event_data,
            'timestamp': datetime.now().isoformat()
        }

        self.log_structured(
            level=severity,
            event_type='system_event',
            data=event_entry,
            category=category or LogCategory.PEARL_BRAIN
        )

    def log_transaction(self, transaction_id: str, action: str,
                       details: Dict[str, Any], status: str = 'started'):
        """لاگ تراکنش‌های سیستم"""
        transaction_data = {
            'transaction_id': transaction_id,
            'action': action,
            'status': status,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }

        self.log_structured(
            level='info',
            event_type='transaction',
            data=transaction_data,
            category=LogCategory.PEARL_BRAIN
        )

    def log_error_with_context(self, error: Exception, context: Dict[str, Any],
                              category: LogCategory = None):
        """لاگ خطا با context کامل"""
        error_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'traceback': traceback.format_exc() if hasattr(traceback, 'format_exc') else None,
            'timestamp': datetime.now().isoformat()
        }

        self.log_structured(
            level='error',
            event_type='error_with_context',
            data=error_data,
            category=category
        )

    def log_performance_profile(self, operation: str, duration: float,
                               resource_usage: Dict[str, Any], metadata: Dict[str, Any] = None):
        """لاگ پروفایل عملکرد"""
        profile_data = {
            'operation': operation,
            'duration_seconds': duration,
            'resource_usage': resource_usage,
            'metadata': metadata or {},
            'timestamp': datetime.now().isoformat()
        }

        self.log_structured(
            level='info',
            event_type='performance_profile',
            data=profile_data,
            category=LogCategory.PERFORMANCE
        )

    def log_model_lifecycle(self, model_name: str, lifecycle_event: str,
                           model_info: Dict[str, Any]):
        """لاگ چرخه حیات مدل"""
        lifecycle_data = {
            'model_name': model_name,
            'lifecycle_event': lifecycle_event,  # created, trained, evaluated, deployed, retired
            'model_info': model_info,
            'timestamp': datetime.now().isoformat()
        }

        self.log_structured(
            level='info',
            event_type='model_lifecycle',
            data=lifecycle_data,
            category=LogCategory.MODEL_TRAINING
        )

    def log_data_pipeline(self, pipeline_stage: str, input_info: Dict[str, Any],
                         output_info: Dict[str, Any], processing_stats: Dict[str, Any]):
        """لاگ pipeline پردازش داده"""
        pipeline_data = {
            'pipeline_stage': pipeline_stage,
            'input_info': input_info,
            'output_info': output_info,
            'processing_stats': processing_stats,
            'timestamp': datetime.now().isoformat()
        }

        self.log_structured(
            level='info',
            event_type='data_pipeline',
            data=pipeline_data,
            category=LogCategory.PEARL_BRAIN
        )

    def create_correlation_id(self) -> str:
        """ایجاد correlation ID برای tracking"""
        import uuid
        return str(uuid.uuid4())

    def log_with_correlation(self, correlation_id: str, level: str, message: str,
                           data: Dict[str, Any] = None, category: LogCategory = None):
        """لاگ با correlation ID"""
        correlated_data = {
            'correlation_id': correlation_id,
            'message': message,
            'data': data or {},
            'timestamp': datetime.now().isoformat()
        }

        self.log_structured(
            level=level,
            event_type='correlated_log',
            data=correlated_data,
            category=category
        )

    def get_structured_logs(self, event_type: str = None, category: LogCategory = None,
                           limit: int = 100) -> List[Dict[str, Any]]:
        """دریافت لاگ‌های ساختاریافته"""
        filtered_logs = self.structured_logs

        if event_type:
            filtered_logs = [log for log in filtered_logs if log.get('event_type') == event_type]

        if category:
            filtered_logs = [log for log in filtered_logs if log.get('category') == category.value]

        return filtered_logs[-limit:] if limit else filtered_logs

    def export_structured_logs(self, filepath: str, format: str = 'json'):
        """صادرات لاگ‌های ساختاریافته"""
        import json

        if format.lower() == 'json':
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.structured_logs, f, indent=2, ensure_ascii=False, default=str)
        elif format.lower() == 'jsonl':
            with open(filepath, 'w', encoding='utf-8') as f:
                for log in self.structured_logs:
                    f.write(json.dumps(log, ensure_ascii=False, default=str) + '\n')
        else:
            raise ValueError(f"Unsupported format: {format}")

        self.logger.info(f"Structured logs exported to {filepath} in {format} format")

    def clear_structured_logs(self):
        """پاک کردن لاگ‌های ساختاریافته از حافظه"""
        cleared_count = len(self.structured_logs)
        self.structured_logs.clear()
        self.logger.info(f"Cleared {cleared_count} structured logs from memory")
    
    def warning(self, message: str, symbol: str = None, strategy: str = None, **kwargs):
        """پیام هشدار"""
        extra = {}
        if symbol:
            extra['symbol'] = symbol
        if strategy:
            extra['strategy'] = strategy
        extra.update(kwargs)
        
        self.logger.warning(message, extra=extra)
    
    def debug(self, message: str, symbol: str = None, strategy: str = None, **kwargs):
        """پیام دیباگ"""
        extra = {}
        if symbol:
            extra['symbol'] = symbol
        if strategy:
            extra['strategy'] = strategy
        extra.update(kwargs)
        
        self.logger.debug(message, extra=extra)

    def export_training_logs(self, filename: str = None) -> str:
        """صادرات لاگ‌های آموزش"""
        if filename is None:
            filename = f"training_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        logs_data = [log.to_dict() for log in self.training_logs]

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(logs_data, f, indent=2, ensure_ascii=False, default=str)

        self.logger.info(f"📊 Training logs exported to {filename}")
        return filename

    def export_evaluation_logs(self, filename: str = None) -> str:
        """صادرات لاگ‌های ارزیابی"""
        if filename is None:
            filename = f"evaluation_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        logs_data = [log.to_dict() for log in self.evaluation_logs]

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(logs_data, f, indent=2, ensure_ascii=False, default=str)

        self.logger.info(f"📊 Evaluation logs exported to {filename}")
        return filename

    def get_training_summary(self) -> Dict[str, Any]:
        """خلاصه آموزش"""
        if not self.training_logs:
            return {}

        losses = [log.loss for log in self.training_logs if log.loss is not None]
        accuracies = [log.accuracy for log in self.training_logs if log.accuracy is not None]

        return {
            'total_steps': len(self.training_logs),
            'final_loss': losses[-1] if losses else None,
            'best_loss': min(losses) if losses else None,
            'final_accuracy': accuracies[-1] if accuracies else None,
            'best_accuracy': max(accuracies) if accuracies else None,
            'training_duration': (self.training_logs[-1].timestamp - self.training_logs[0].timestamp).total_seconds() if len(self.training_logs) > 1 else 0
        }

# Backward compatibility
TradingSystemLogger = PearlIntelligentLogger

# Global loggers
_loggers = {}

def get_logger(name: str, config: Dict[str, Any] = None) -> PearlIntelligentLogger:
    """دریافت لاگر"""
    if name not in _loggers:
        _loggers[name] = PearlIntelligentLogger(name, config)
    return _loggers[name]

def get_pearl_logger(name: str = "pearl_system", config: Dict[str, Any] = None) -> PearlIntelligentLogger:
    """دریافت لاگر مخصوص Pearl-3x7B"""
    if config is None:
        config = {
            'level': 'INFO',
            'file_path': 'logs/pearl_system.log',
            'json_log_path': 'logs/pearl_structured.jsonl',
            'console_output': True,
            'enable_model_training_log': True,
            'enable_model_evaluation_log': True,
            'enable_memory_management_log': True,
            'enable_performance_log': True,
            'enable_pearl_brain_log': True
        }

    return get_logger(name, config)

def configure_logging(config: Dict[str, Any]):
    """تنظیم logging کلی"""
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.get('level', 'INFO').upper()))
    
    # Update existing loggers
    for name, logger in _loggers.items():
        logger.config.update(config)
        logger.setup_logger()

def configure_logging_for_pearl(config: Dict[str, Any]):
    """تنظیم logging مخصوص Pearl-3x7B"""
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.get('level', 'INFO').upper()))

    # Update existing loggers
    for name, logger in _loggers.items():
        logger.config.update(config)
        logger.setup_logger()

# Context manager for training sessions
class TrainingSession:
    """Context manager برای جلسات آموزش"""

    def __init__(self, logger: PearlIntelligentLogger, model_name: str):
        self.logger = logger
        self.model_name = model_name
        self.session_id = None
        self.start_time = None

    def __enter__(self):
        self.start_time = time.time()
        self.session_id = self.logger.start_training_session(self.model_name)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        self.logger.log_performance_metric("session_duration", duration, "seconds")
        self.logger.end_training_session()

        if exc_type:
            self.logger.logger.error(f"Training session failed: {exc_val}")

# Decorator for automatic logging
def log_training_step(logger: PearlIntelligentLogger):
    """Decorator برای لاگ خودکار مراحل آموزش"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                # Extract training info from result if available
                if isinstance(result, dict):
                    model_name = result.get('model_name', 'unknown')
                    epoch = result.get('epoch', 0)
                    batch = result.get('batch', 0)
                    loss = result.get('loss', 0.0)
                    accuracy = result.get('accuracy')

                    logger.log_training_step(
                        model_name=model_name,
                        epoch=epoch,
                        batch=batch,
                        loss=loss,
                        accuracy=accuracy,
                        duration=duration
                    )

                return result
            except Exception as e:
                logger.logger.error(f"Training step failed: {e}")
                raise
        return wrapper
    return decorator

# Export main classes and functions
__all__ = [
    'PearlIntelligentLogger',
    'TradingSystemLogger',  # Backward compatibility
    'TradingSystemFormatter',
    'StructuredJSONFormatter',
    'TrainingLogEntry',
    'EvaluationLogEntry',
    'LogLevel',
    'LogCategory',
    'TrainingSession',
    'get_logger',
    'get_pearl_logger',
    'configure_logging',
    'configure_logging_for_pearl',
    'setup_logging',
    'log_training_step'
]

def setup_logging(config: Dict[str, Any] = None) -> PearlIntelligentLogger:
    """تنظیم و دریافت لاگر پیش‌فرض"""
    if config is None:
        config = {
            'level': 'INFO',
            'file_path': 'logs/pearl_system.log',
            'json_log_path': 'logs/pearl_structured.jsonl',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        }

    return get_pearl_logger('pearl_system', config)

# Test and example usage
if __name__ == "__main__":
    # Test Pearl logger
    logger = get_pearl_logger()

    # Test training session
    with TrainingSession(logger, "test_model") as session:
        logger.log_training_step("test_model", 1, 10, 0.5, 0.85, 0.001, 1024.5, 0.1)
        logger.log_memory_usage(2048.0, "during training")
        logger.log_pearl_decision("buy", 0.85, "strong upward trend detected")

    # Test evaluation
    logger.log_evaluation_result(
        "test_model",
        "validation_set",
        {"accuracy": 0.92, "f1_score": 0.89},
        15.5,
        1536.0
    )

    # Export logs
    training_file = logger.export_training_logs()
    evaluation_file = logger.export_evaluation_logs()

    print(f"✅ Pearl logger test completed!")
    print(f"Training logs: {training_file}")
    print(f"Evaluation logs: {evaluation_file}")
    print(f"Training summary: {logger.get_training_summary()}")