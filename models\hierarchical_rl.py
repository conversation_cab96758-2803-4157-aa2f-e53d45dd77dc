#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Hierarchical Reinforcement Learning for Trading

This module implements a hierarchical RL system with three levels:
1. Strategic Level: High-level strategy decisions (trend following, mean reversion, breakout)
2. Tactical Level: Entry/exit timing and risk management
3. Execution Level: Precise trade execution and position management

The architecture follows the Option-Critic framework with temporal abstraction.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from stable_baselines3 import PPO
from stable_baselines3.common.policies import ActorCriticPolicy
from stable_baselines3.common.vec_env import DummyVecEnv
from typing import Dict, List, Tuple, Optional, Any
import logging
import os
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class StrategicPolicy(nn.Module):
    """
    Strategic Level Policy: Decides high-level trading strategies
    """
    def __init__(self, obs_dim: int, n_strategies: int = 4, hidden_dim: int = 128):
        super(StrategicPolicy, self).__init__()
        self.n_strategies = n_strategies
        
        # Strategy network
        self.strategy_net = nn.Sequential(
            nn.Linear(obs_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, n_strategies)
        )
        
        # Value network for critic
        self.value_net = nn.Sequential(
            nn.Linear(obs_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
    def forward(self, obs: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        strategy_logits = self.strategy_net(obs)
        value = self.value_net(obs)
        return strategy_logits, value
    
    def get_strategy_probs(self, obs: torch.Tensor) -> torch.Tensor:
        logits, _ = self.forward(obs)
        return torch.softmax(logits, dim=-1)

class TacticalPolicy(nn.Module):
    """
    Tactical Level Policy: Manages entry/exit timing and risk
    """
    def __init__(self, obs_dim: int, n_actions: int = 3, hidden_dim: int = 128):
        super(TacticalPolicy, self).__init__()
        self.n_actions = n_actions  # 0: hold, 1: enter, 2: exit
        
        # Tactical network
        self.tactical_net = nn.Sequential(
            nn.Linear(obs_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, n_actions)
        )
        
        # Value network
        self.value_net = nn.Sequential(
            nn.Linear(obs_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
    def forward(self, obs: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        tactical_logits = self.tactical_net(obs)
        value = self.value_net(obs)
        return tactical_logits, value
    
    def get_action_probs(self, obs: torch.Tensor) -> torch.Tensor:
        logits, _ = self.forward(obs)
        return torch.softmax(logits, dim=-1)

class ExecutionPolicy(nn.Module):
    """
    Execution Level Policy: Precise trade execution and position management
    """
    def __init__(self, obs_dim: int, action_dim: int = 1, hidden_dim: int = 128):
        super(ExecutionPolicy, self).__init__()
        self.action_dim = action_dim
        
        # Execution network (continuous actions)
        self.execution_net = nn.Sequential(
            nn.Linear(obs_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, action_dim)
        )
        
        # Value network
        self.value_net = nn.Sequential(
            nn.Linear(obs_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
    def forward(self, obs: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        action = self.execution_net(obs)
        value = self.value_net(obs)
        return action, value

class HierarchicalRL:
    """
    Hierarchical Reinforcement Learning for Trading
    
    Architecture:
    - Strategic Level: High-level strategy selection
    - Tactical Level: Entry/exit timing and risk management  
    - Execution Level: Precise trade execution
    """
    
    def __init__(self, 
                 obs_dim: int,
                 device: str = 'cpu',
                 learning_rate: float = 3e-4,
                 gamma: float = 0.99,
                 gae_lambda: float = 0.95,
                 clip_ratio: float = 0.2,
                 value_loss_coef: float = 0.5,
                 entropy_coef: float = 0.01,
                 max_grad_norm: float = 0.5,
                 **kwargs):
        
        self.device = device
        self.obs_dim = obs_dim
        self.learning_rate = learning_rate
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        self.clip_ratio = clip_ratio
        self.value_loss_coef = value_loss_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        
        # Initialize policies with correct input dimensions
        self.strategic_policy = StrategicPolicy(10).to(device)  # Strategic uses first 10 features
        self.tactical_policy = TacticalPolicy(11).to(device)    # Tactical uses features 10-20 + strategy
        self.execution_policy = ExecutionPolicy(2).to(device)   # Execution uses remaining features + context
        
        # Optimizers
        self.strategic_optimizer = optim.Adam(self.strategic_policy.parameters(), lr=learning_rate)
        self.tactical_optimizer = optim.Adam(self.tactical_policy.parameters(), lr=learning_rate)
        self.execution_optimizer = optim.Adam(self.execution_policy.parameters(), lr=learning_rate)
        
        # Strategy definitions
        self.strategies = {
            0: 'trend_following',
            1: 'mean_reversion', 
            2: 'breakout',
            3: 'momentum'
        }
        
        # Training history
        self.training_history = {
            'strategic_loss': [],
            'tactical_loss': [],
            'execution_loss': [],
            'total_reward': [],
            'strategy_usage': {i: 0 for i in range(len(self.strategies))}
        }
        
        # Current state
        self.current_strategy = None
        self.current_tactical_action = None
        self.current_execution_action = None
        
    def get_strategic_observation(self, obs: np.ndarray) -> torch.Tensor:
        """
        Extract features relevant for strategic decisions
        """
        # Use market regime features, volatility, trend strength
        strategic_features = obs[:10]  # First 10 features for strategy
        return torch.FloatTensor(strategic_features).unsqueeze(0).to(self.device)
    
    def get_tactical_observation(self, obs: np.ndarray, strategy: int) -> torch.Tensor:
        """
        Extract features relevant for tactical decisions
        """
        # Include strategy information and timing features
        tactical_features = np.concatenate([obs[10:20], [strategy]])  # Features 10-20 + strategy
        return torch.FloatTensor(tactical_features).unsqueeze(0).to(self.device)
    
    def get_execution_observation(self, obs: np.ndarray, strategy: int, tactical_action: int) -> torch.Tensor:
        """
        Extract features relevant for execution decisions
        """
        # Include all context for precise execution
        # If obs has less than 20 features, pad with zeros
        if len(obs) < 20:
            padded_obs = np.concatenate([obs, np.zeros(20 - len(obs))])
        else:
            padded_obs = obs
        
        execution_features = np.concatenate([padded_obs[20:], [strategy, tactical_action]])
        return torch.FloatTensor(execution_features).unsqueeze(0).to(self.device)
    
    def select_strategy(self, obs: np.ndarray) -> int:
        """
        Select high-level strategy
        """
        strategic_obs = self.get_strategic_observation(obs)
        with torch.no_grad():
            strategy_probs = self.strategic_policy.get_strategy_probs(strategic_obs)
            strategy = torch.multinomial(strategy_probs, 1).item()
        
        self.current_strategy = strategy
        self.training_history['strategy_usage'][strategy] += 1
        return strategy
    
    def select_tactical_action(self, obs: np.ndarray, strategy: int) -> int:
        """
        Select tactical action (entry/exit timing)
        """
        tactical_obs = self.get_tactical_observation(obs, strategy)
        with torch.no_grad():
            action_probs = self.tactical_policy.get_action_probs(tactical_obs)
            action = torch.multinomial(action_probs, 1).item()
        
        self.current_tactical_action = action
        return action
    
    def select_execution_action(self, obs: np.ndarray, strategy: int, tactical_action: int) -> float:
        """
        Select precise execution action (continuous)
        """
        execution_obs = self.get_execution_observation(obs, strategy, tactical_action)
        with torch.no_grad():
            action, _ = self.execution_policy(execution_obs)
            action = torch.tanh(action).item()  # Bound to [-1, 1]
        
        self.current_execution_action = action
        return action
    
    def act(self, obs: np.ndarray) -> Tuple[int, int, float]:
        """
        Complete hierarchical decision making
        """
        strategy = self.select_strategy(obs)
        tactical_action = self.select_tactical_action(obs, strategy)
        execution_action = self.select_execution_action(obs, strategy, tactical_action)
        
        return strategy, tactical_action, execution_action
    
    def compute_gae(self, rewards: List[float], values: List[float], dones: List[bool]) -> List[float]:
        """
        Compute Generalized Advantage Estimation
        """
        advantages = []
        gae = 0
        
        for i in reversed(range(len(rewards))):
            if i == len(rewards) - 1:
                next_value = 0
            else:
                next_value = values[i + 1]
            
            delta = rewards[i] + self.gamma * next_value * (1 - dones[i]) - values[i]
            gae = delta + self.gamma * self.gae_lambda * (1 - dones[i]) * gae
            advantages.insert(0, gae)
        
        return advantages
    
    def update_strategic_policy(self, obs_batch: List[np.ndarray], 
                               actions: List[int], 
                               advantages: List[float],
                               old_probs: List[float]) -> float:
        """
        Update strategic policy using PPO
        """
        # Convert to strategic observations
        strategic_obs_batch = []
        for obs in obs_batch:
            strategic_obs = self.get_strategic_observation(obs)
            strategic_obs_batch.append(strategic_obs.squeeze().cpu().numpy())
        
        obs_tensor = torch.FloatTensor(np.array(strategic_obs_batch)).to(self.device)
        actions_tensor = torch.LongTensor(actions).to(self.device)
        advantages_tensor = torch.FloatTensor(advantages).to(self.device)
        old_probs_tensor = torch.FloatTensor(old_probs).to(self.device)
        
        # Get current probabilities
        logits, values = self.strategic_policy(obs_tensor)
        probs = torch.softmax(logits, dim=-1)
        action_probs = probs.gather(1, actions_tensor.unsqueeze(1)).squeeze(1)
        
        # Compute ratio
        ratio = action_probs / (old_probs_tensor + 1e-8)
        
        # PPO loss
        surr1 = ratio * advantages_tensor
        surr2 = torch.clamp(ratio, 1 - self.clip_ratio, 1 + self.clip_ratio) * advantages_tensor
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # Value loss
        value_loss = nn.MSELoss()(values.squeeze(), advantages_tensor + values.squeeze().detach())
        
        # Entropy loss
        entropy = -(probs * torch.log(probs + 1e-8)).sum(dim=1).mean()
        
        # Total loss
        total_loss = policy_loss + self.value_loss_coef * value_loss - self.entropy_coef * entropy
        
        # Update
        self.strategic_optimizer.zero_grad()
        total_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.strategic_policy.parameters(), self.max_grad_norm)
        self.strategic_optimizer.step()
        
        return total_loss.item()
    
    def update_tactical_policy(self, obs_batch: List[np.ndarray],
                              strategy_batch: List[int],
                              actions: List[int],
                              advantages: List[float],
                              old_probs: List[float]) -> float:
        """
        Update tactical policy using PPO
        """
        # Prepare tactical observations
        tactical_obs_batch = []
        for obs, strategy in zip(obs_batch, strategy_batch):
            tactical_obs = self.get_tactical_observation(obs, strategy)
            tactical_obs_batch.append(tactical_obs.squeeze().cpu().numpy())
        
        obs_tensor = torch.FloatTensor(np.array(tactical_obs_batch)).to(self.device)
        actions_tensor = torch.LongTensor(actions).to(self.device)
        advantages_tensor = torch.FloatTensor(advantages).to(self.device)
        old_probs_tensor = torch.FloatTensor(old_probs).to(self.device)
        
        # Get current probabilities
        logits, values = self.tactical_policy(obs_tensor)
        probs = torch.softmax(logits, dim=-1)
        action_probs = probs.gather(1, actions_tensor.unsqueeze(1)).squeeze(1)
        
        # Compute ratio
        ratio = action_probs / (old_probs_tensor + 1e-8)
        
        # PPO loss
        surr1 = ratio * advantages_tensor
        surr2 = torch.clamp(ratio, 1 - self.clip_ratio, 1 + self.clip_ratio) * advantages_tensor
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # Value loss
        value_loss = nn.MSELoss()(values.squeeze(), advantages_tensor + values.squeeze().detach())
        
        # Entropy loss
        entropy = -(probs * torch.log(probs + 1e-8)).sum(dim=1).mean()
        
        # Total loss
        total_loss = policy_loss + self.value_loss_coef * value_loss - self.entropy_coef * entropy
        
        # Update
        self.tactical_optimizer.zero_grad()
        total_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.tactical_policy.parameters(), self.max_grad_norm)
        self.tactical_optimizer.step()
        
        return total_loss.item()
    
    def update_execution_policy(self, obs_batch: List[np.ndarray],
                               strategy_batch: List[int],
                               tactical_batch: List[int],
                               actions: List[float],
                               advantages: List[float]) -> float:
        """
        Update execution policy using PPO
        """
        # Prepare execution observations
        execution_obs_batch = []
        for obs, strategy, tactical in zip(obs_batch, strategy_batch, tactical_batch):
            execution_obs = self.get_execution_observation(obs, strategy, tactical)
            execution_obs_batch.append(execution_obs.squeeze().cpu().numpy())
        
        obs_tensor = torch.FloatTensor(np.array(execution_obs_batch)).to(self.device)
        actions_tensor = torch.FloatTensor(actions).to(self.device)
        advantages_tensor = torch.FloatTensor(advantages).to(self.device)
        
        # Get current actions and values
        actions_pred, values = self.execution_policy(obs_tensor)
        actions_pred = torch.tanh(actions_pred).squeeze()
        
        # MSE loss for continuous actions
        action_loss = nn.MSELoss()(actions_pred, actions_tensor)
        
        # Value loss
        value_loss = nn.MSELoss()(values.squeeze(), advantages_tensor + values.squeeze().detach())
        
        # Total loss
        total_loss = action_loss + self.value_loss_coef * value_loss
        
        # Update
        self.execution_optimizer.zero_grad()
        total_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.execution_policy.parameters(), self.max_grad_norm)
        self.execution_optimizer.step()
        
        return total_loss.item()
    
    def train_step(self, batch_data: Dict[str, List]) -> Dict[str, float]:
        """
        Single training step for all hierarchical levels
        """
        obs_batch = batch_data['observations']
        strategy_batch = batch_data['strategies']
        tactical_batch = batch_data['tactical_actions']
        execution_batch = batch_data['execution_actions']
        rewards = batch_data['rewards']
        dones = batch_data['dones']
        
        # Compute advantages for each level
        strategic_values = []
        tactical_values = []
        execution_values = []
        
        for obs, strategy in zip(obs_batch, strategy_batch):
            strategic_obs = self.get_strategic_observation(obs)
            with torch.no_grad():
                _, value = self.strategic_policy(strategic_obs)
                strategic_values.append(value.item())
        
        for obs, strategy in zip(obs_batch, strategy_batch):
            tactical_obs = self.get_tactical_observation(obs, strategy)
            with torch.no_grad():
                _, value = self.tactical_policy(tactical_obs)
                tactical_values.append(value.item())
        
        for obs, strategy, tactical in zip(obs_batch, strategy_batch, tactical_batch):
            execution_obs = self.get_execution_observation(obs, strategy, tactical)
            with torch.no_grad():
                _, value = self.execution_policy(execution_obs)
                execution_values.append(value.item())
        
        # Compute GAE advantages
        strategic_advantages = self.compute_gae(rewards, strategic_values, dones)
        tactical_advantages = self.compute_gae(rewards, tactical_values, dones)
        execution_advantages = self.compute_gae(rewards, execution_values, dones)
        
        # Update policies
        strategic_loss = self.update_strategic_policy(obs_batch, strategy_batch, 
                                                    strategic_advantages, 
                                                    batch_data.get('old_strategic_probs', [0.25] * len(obs_batch)))
        
        tactical_loss = self.update_tactical_policy(obs_batch, strategy_batch, 
                                                  tactical_batch, tactical_advantages,
                                                  batch_data.get('old_tactical_probs', [0.33] * len(obs_batch)))
        
        execution_loss = self.update_execution_policy(obs_batch, strategy_batch, 
                                                    tactical_batch, execution_batch, 
                                                    execution_advantages)
        
        # Record losses
        self.training_history['strategic_loss'].append(strategic_loss)
        self.training_history['tactical_loss'].append(tactical_loss)
        self.training_history['execution_loss'].append(execution_loss)
        self.training_history['total_reward'].append(np.mean(rewards))
        
        return {
            'strategic_loss': strategic_loss,
            'tactical_loss': tactical_loss,
            'execution_loss': execution_loss,
            'mean_reward': np.mean(rewards)
        }
    
    def get_strategy_analysis(self) -> Dict[str, Any]:
        """
        Analyze strategy usage and performance
        """
        total_usage = sum(self.training_history['strategy_usage'].values())
        strategy_percentages = {
            self.strategies[i]: (count / total_usage * 100) if total_usage > 0 else 0
            for i, count in self.training_history['strategy_usage'].items()
        }
        
        return {
            'strategy_usage': strategy_percentages,
            'training_losses': {
                'strategic': np.mean(self.training_history['strategic_loss'][-100:]) if self.training_history['strategic_loss'] else 0,
                'tactical': np.mean(self.training_history['tactical_loss'][-100:]) if self.training_history['tactical_loss'] else 0,
                'execution': np.mean(self.training_history['execution_loss'][-100:]) if self.training_history['execution_loss'] else 0
            },
            'mean_reward': np.mean(self.training_history['total_reward'][-100:]) if self.training_history['total_reward'] else 0
        }
    
    def save(self, path: str):
        """
        Save hierarchical model
        """
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        torch.save({
            'strategic_policy': self.strategic_policy.state_dict(),
            'tactical_policy': self.tactical_policy.state_dict(),
            'execution_policy': self.execution_policy.state_dict(),
            'strategic_optimizer': self.strategic_optimizer.state_dict(),
            'tactical_optimizer': self.tactical_optimizer.state_dict(),
            'execution_optimizer': self.execution_optimizer.state_dict(),
            'training_history': self.training_history,
            'config': {
                'obs_dim': self.obs_dim,
                'learning_rate': self.learning_rate,
                'gamma': self.gamma,
                'device': self.device
            }
        }, path)
        
        logger.info(f"Hierarchical RL model saved to {path}")
    
    def load(self, path: str):
        """
        Load hierarchical model
        """
        checkpoint = torch.load(path, map_location=self.device)
        
        self.strategic_policy.load_state_dict(checkpoint['strategic_policy'])
        self.tactical_policy.load_state_dict(checkpoint['tactical_policy'])
        self.execution_policy.load_state_dict(checkpoint['execution_policy'])
        
        self.strategic_optimizer.load_state_dict(checkpoint['strategic_optimizer'])
        self.tactical_optimizer.load_state_dict(checkpoint['tactical_optimizer'])
        self.execution_optimizer.load_state_dict(checkpoint['execution_optimizer'])
        
        self.training_history = checkpoint.get('training_history', self.training_history)
        
        logger.info(f"Hierarchical RL model loaded from {path}")
    
    def get_current_state(self) -> Dict[str, Any]:
        """
        Get current hierarchical state
        """
        return {
            'current_strategy': self.current_strategy,
            'current_tactical_action': self.current_tactical_action,
            'current_execution_action': self.current_execution_action,
            'strategy_name': self.strategies.get(self.current_strategy, 'unknown') if self.current_strategy is not None else None
        } 