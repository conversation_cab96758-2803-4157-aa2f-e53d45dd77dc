"""
سیستم تطبیقی و یادگیری مداوم مدل‌های Plutus
Adaptive Learning System for Plutus Models
"""

import pandas as pd
import numpy as np
import json
import pickle
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import sqlite3
from concurrent.futures import ThreadPoolExecutor
import threading
import time

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from tests.test_plutus_models_comprehensive import PlutusModelTester
from examples.plutus_integration_final import PlutusIntegratedTradingSystem

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelPerformanceMetrics:
    """معیارهای عملکرد مدل"""
    symbol: str
    timeframe: str
    model_name: str
    timestamp: datetime
    prediction: str
    confidence: float
    actual_outcome: str
    accuracy: float
    profit_loss: float
    market_conditions: Dict[str, Any]
    technical_indicators: Dict[str, Any]

@dataclass
class AdaptiveWeights:
    """وزن‌های تطبیقی مدل‌ها"""
    chronos_weight: float = 0.6
    fingpt_weight: float = 0.4
    combined_threshold: float = 0.65
    confidence_multiplier: float = 1.0
    last_updated: datetime = None

class PerformanceDatabase:
    """پایگاه داده عملکرد مدل‌ها"""
    
    def __init__(self, db_path: str = "performance_history.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """ایجاد جداول پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول عملکرد مدل‌ها
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT,
                timeframe TEXT,
                model_name TEXT,
                timestamp DATETIME,
                prediction TEXT,
                confidence REAL,
                actual_outcome TEXT,
                accuracy REAL,
                profit_loss REAL,
                market_conditions TEXT,
                technical_indicators TEXT
            )
        ''')
        
        # جدول وزن‌های تطبیقی
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS adaptive_weights (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT,
                timeframe TEXT,
                chronos_weight REAL,
                fingpt_weight REAL,
                combined_threshold REAL,
                confidence_multiplier REAL,
                timestamp DATETIME
            )
        ''')
        
        # جدول شرایط بازار
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS market_conditions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT,
                timeframe TEXT,
                timestamp DATETIME,
                volatility REAL,
                trend_strength REAL,
                volume_ratio REAL,
                rsi REAL,
                ma_ratio REAL,
                conditions_json TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def save_performance(self, metrics: ModelPerformanceMetrics):
        """ذخیره عملکرد مدل"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO model_performance (
                symbol, timeframe, model_name, timestamp, prediction, confidence,
                actual_outcome, accuracy, profit_loss, market_conditions, technical_indicators
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            metrics.symbol, metrics.timeframe, metrics.model_name,
            metrics.timestamp, metrics.prediction, metrics.confidence,
            metrics.actual_outcome, metrics.accuracy, metrics.profit_loss,
            json.dumps(metrics.market_conditions), json.dumps(metrics.technical_indicators)
        ))
        
        conn.commit()
        conn.close()
    
    def get_recent_performance(self, symbol: str, model_name: str, 
                             days: int = 30) -> List[ModelPerformanceMetrics]:
        """دریافت عملکرد اخیر"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        cursor.execute('''
            SELECT * FROM model_performance 
            WHERE symbol = ? AND model_name = ? AND timestamp > ?
            ORDER BY timestamp DESC
        ''', (symbol, model_name, cutoff_date))
        
        results = []
        for row in cursor.fetchall():
            metrics = ModelPerformanceMetrics(
                symbol=row[1], timeframe=row[2], model_name=row[3],
                timestamp=datetime.fromisoformat(row[4]), prediction=row[5],
                confidence=row[6], actual_outcome=row[7], accuracy=row[8],
                profit_loss=row[9], market_conditions=json.loads(row[10]),
                technical_indicators=json.loads(row[11])
            )
            results.append(metrics)
        
        conn.close()
        return results
    
    def save_adaptive_weights(self, symbol: str, timeframe: str, weights: AdaptiveWeights):
        """ذخیره وزن‌های تطبیقی"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO adaptive_weights (
                symbol, timeframe, chronos_weight, fingpt_weight,
                combined_threshold, confidence_multiplier, timestamp
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            symbol, timeframe, weights.chronos_weight, weights.fingpt_weight,
            weights.combined_threshold, weights.confidence_multiplier,
            datetime.now()
        ))
        
        conn.commit()
        conn.close()
    
    def get_latest_weights(self, symbol: str, timeframe: str) -> Optional[AdaptiveWeights]:
        """دریافت آخرین وزن‌ها"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM adaptive_weights 
            WHERE symbol = ? AND timeframe = ?
            ORDER BY timestamp DESC LIMIT 1
        ''', (symbol, timeframe))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return AdaptiveWeights(
                chronos_weight=row[3], fingpt_weight=row[4],
                combined_threshold=row[5], confidence_multiplier=row[6],
                last_updated=datetime.fromisoformat(row[7])
            )
        return None

class AdaptiveLearningEngine:
    """موتور یادگیری تطبیقی"""
    
    def __init__(self, db: PerformanceDatabase):
        self.db = db
        self.model_tester = PlutusModelTester()
        self.learning_rate = 0.1
        self.min_samples = 20  # حداقل نمونه برای یادگیری
        
    def analyze_model_performance(self, symbol: str, model_name: str, 
                                days: int = 30) -> Dict[str, Any]:
        """تحلیل عملکرد مدل"""
        try:
            metrics = self.db.get_recent_performance(symbol, model_name, days)
            
            if len(metrics) < 5:
                return {"error": "Insufficient data for analysis"}
            
            # محاسبه آمار کلی
            accuracies = [m.accuracy for m in metrics]
            profits = [m.profit_loss for m in metrics]
            confidences = [m.confidence for m in metrics]
            
            analysis = {
                "total_predictions": len(metrics),
                "avg_accuracy": np.mean(accuracies),
                "avg_profit": np.mean(profits),
                "avg_confidence": np.mean(confidences),
                "win_rate": len([p for p in profits if p > 0]) / len(profits),
                "profit_factor": sum([p for p in profits if p > 0]) / abs(sum([p for p in profits if p < 0])) if any(p < 0 for p in profits) else float('inf'),
                "confidence_accuracy_correlation": np.corrcoef(confidences, accuracies)[0, 1] if len(confidences) > 1 else 0
            }
            
            # تحلیل عملکرد بر اساس شرایط بازار
            market_analysis = self.analyze_market_conditions_impact(metrics)
            analysis["market_conditions_impact"] = market_analysis
            
            # تحلیل روند عملکرد
            trend_analysis = self.analyze_performance_trend(metrics)
            analysis["performance_trend"] = trend_analysis
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing model performance: {str(e)}")
            return {"error": str(e)}
    
    def analyze_market_conditions_impact(self, metrics: List[ModelPerformanceMetrics]) -> Dict[str, Any]:
        """تحلیل تأثیر شرایط بازار"""
        try:
            # گروه‌بندی بر اساس volatility
            high_vol = [m for m in metrics if m.market_conditions.get("volatility", 0) > 0.02]
            low_vol = [m for m in metrics if m.market_conditions.get("volatility", 0) < 0.01]
            
            # گروه‌بندی بر اساس trend
            bullish = [m for m in metrics if m.market_conditions.get("trend_direction") == "bullish"]
            bearish = [m for m in metrics if m.market_conditions.get("trend_direction") == "bearish"]
            
            analysis = {
                "high_volatility_performance": {
                    "count": len(high_vol),
                    "avg_accuracy": np.mean([m.accuracy for m in high_vol]) if high_vol else 0,
                    "avg_profit": np.mean([m.profit_loss for m in high_vol]) if high_vol else 0
                },
                "low_volatility_performance": {
                    "count": len(low_vol),
                    "avg_accuracy": np.mean([m.accuracy for m in low_vol]) if low_vol else 0,
                    "avg_profit": np.mean([m.profit_loss for m in low_vol]) if low_vol else 0
                },
                "bullish_market_performance": {
                    "count": len(bullish),
                    "avg_accuracy": np.mean([m.accuracy for m in bullish]) if bullish else 0,
                    "avg_profit": np.mean([m.profit_loss for m in bullish]) if bullish else 0
                },
                "bearish_market_performance": {
                    "count": len(bearish),
                    "avg_accuracy": np.mean([m.accuracy for m in bearish]) if bearish else 0,
                    "avg_profit": np.mean([m.profit_loss for m in bearish]) if bearish else 0
                }
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing market conditions impact: {str(e)}")
            return {}
    
    def analyze_performance_trend(self, metrics: List[ModelPerformanceMetrics]) -> Dict[str, Any]:
        """تحلیل روند عملکرد"""
        try:
            # مرتب‌سازی بر اساس زمان
            sorted_metrics = sorted(metrics, key=lambda x: x.timestamp)
            
            # تقسیم به دو نیمه
            mid_point = len(sorted_metrics) // 2
            first_half = sorted_metrics[:mid_point]
            second_half = sorted_metrics[mid_point:]
            
            first_half_accuracy = np.mean([m.accuracy for m in first_half])
            second_half_accuracy = np.mean([m.accuracy for m in second_half])
            
            first_half_profit = np.mean([m.profit_loss for m in first_half])
            second_half_profit = np.mean([m.profit_loss for m in second_half])
            
            return {
                "accuracy_trend": "improving" if second_half_accuracy > first_half_accuracy else "declining",
                "accuracy_change": second_half_accuracy - first_half_accuracy,
                "profit_trend": "improving" if second_half_profit > first_half_profit else "declining",
                "profit_change": second_half_profit - first_half_profit,
                "recent_performance": {
                    "accuracy": second_half_accuracy,
                    "profit": second_half_profit
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing performance trend: {str(e)}")
            return {}
    
    def optimize_model_weights(self, symbol: str, timeframe: str) -> AdaptiveWeights:
        """بهینه‌سازی وزن‌های مدل"""
        try:
            logger.info(f"Optimizing weights for {symbol} {timeframe}")
            
            # تحلیل عملکرد هر مدل
            chronos_analysis = self.analyze_model_performance(symbol, "chronos")
            fingpt_analysis = self.analyze_model_performance(symbol, "fingpt")
            
            # دریافت وزن‌های فعلی
            current_weights = self.db.get_latest_weights(symbol, timeframe)
            if not current_weights:
                current_weights = AdaptiveWeights()
            
            # محاسبه وزن‌های جدید
            new_weights = AdaptiveWeights()
            
            if not chronos_analysis.get("error") and not fingpt_analysis.get("error"):
                chronos_score = (chronos_analysis["avg_accuracy"] * 0.4 + 
                               chronos_analysis["avg_profit"] * 0.6)
                fingpt_score = (fingpt_analysis["avg_accuracy"] * 0.4 + 
                              fingpt_analysis["avg_profit"] * 0.6)
                
                total_score = chronos_score + fingpt_score
                
                if total_score > 0:
                    new_chronos_weight = chronos_score / total_score
                    new_fingpt_weight = fingpt_score / total_score
                    
                    # تنظیم تدریجی (learning rate)
                    new_weights.chronos_weight = (current_weights.chronos_weight * (1 - self.learning_rate) + 
                                                new_chronos_weight * self.learning_rate)
                    new_weights.fingpt_weight = (current_weights.fingpt_weight * (1 - self.learning_rate) + 
                                               new_fingpt_weight * self.learning_rate)
                    
                    # تنظیم threshold بر اساس عملکرد
                    avg_accuracy = (chronos_analysis["avg_accuracy"] + fingpt_analysis["avg_accuracy"]) / 2
                    new_weights.combined_threshold = max(0.5, min(0.8, avg_accuracy))
                    
                    # تنظیم confidence multiplier
                    confidence_corr = (chronos_analysis.get("confidence_accuracy_correlation", 0) + 
                                     fingpt_analysis.get("confidence_accuracy_correlation", 0)) / 2
                    new_weights.confidence_multiplier = max(0.8, min(1.2, 1 + confidence_corr * 0.2))
            
            new_weights.last_updated = datetime.now()
            
            # ذخیره وزن‌های جدید
            self.db.save_adaptive_weights(symbol, timeframe, new_weights)
            
            logger.info(f"Updated weights - Chronos: {new_weights.chronos_weight:.3f}, "
                       f"FinGPT: {new_weights.fingpt_weight:.3f}")
            
            return new_weights
            
        except Exception as e:
            logger.error(f"Error optimizing weights: {str(e)}")
            return AdaptiveWeights()

class BacktestingEngine:
    """موتور بک‌تست پیشرفته"""
    
    def __init__(self, db: PerformanceDatabase):
        self.db = db
        self.model_tester = PlutusModelTester()
    
    def run_adaptive_backtest(self, symbol: str, timeframe: str = "H1", 
                            periods: int = 100, update_frequency: int = 20) -> Dict[str, Any]:
        """اجرای بک‌تست تطبیقی"""
        try:
            logger.info(f"Running adaptive backtest for {symbol}")
            
            # بارگذاری داده‌ها
            price_data = self.model_tester.load_real_project_data(symbol, timeframe)
            
            if price_data.empty or len(price_data) < periods + 100:
                return {"error": f"Insufficient data for {symbol}"}
            
            results = {
                "symbol": symbol,
                "timeframe": timeframe,
                "periods": periods,
                "trades": [],
                "weight_updates": [],
                "performance_metrics": {}
            }
            
            # شروع بک‌تست
            learning_engine = AdaptiveLearningEngine(self.db)
            current_weights = AdaptiveWeights()
            
            for i in range(100, len(price_data) - 50, 24):
                if len(results["trades"]) >= periods:
                    break
                
                # به‌روزرسانی وزن‌ها هر چند معامله
                if len(results["trades"]) % update_frequency == 0:
                    current_weights = learning_engine.optimize_model_weights(symbol, timeframe)
                    results["weight_updates"].append({
                        "trade_number": len(results["trades"]),
                        "weights": asdict(current_weights)
                    })
                
                # داده‌های آموزش
                train_data = price_data.iloc[:i]
                test_data = price_data.iloc[i:i+24]
                
                if len(test_data) < 24:
                    continue
                
                # پیش‌بینی‌ها
                chronos_pred = self.model_tester.test_chronos_model_offline(train_data, symbol)
                fingpt_pred = self.model_tester.test_fingpt_model_offline(train_data, symbol)
                
                # ترکیب پیش‌بینی‌ها با وزن‌های تطبیقی
                combined_signal = self.combine_predictions_with_weights(
                    chronos_pred, fingpt_pred, current_weights
                )
                
                if combined_signal:
                    # شبیه‌سازی معامله
                    trade_result = self.simulate_trade(combined_signal, test_data)
                    results["trades"].append(trade_result)
                    
                    # ذخیره عملکرد
                    self.save_backtest_performance(symbol, timeframe, combined_signal, 
                                                 trade_result, train_data)
            
            # محاسبه معیارهای نهایی
            results["performance_metrics"] = self.calculate_backtest_metrics(results["trades"])
            
            return results
            
        except Exception as e:
            logger.error(f"Error in adaptive backtest: {str(e)}")
            return {"error": str(e)}
    
    def combine_predictions_with_weights(self, chronos_pred: Dict, fingpt_pred: Dict, 
                                       weights: AdaptiveWeights) -> Optional[Dict]:
        """ترکیب پیش‌بینی‌ها با وزن‌های تطبیقی"""
        try:
            if chronos_pred.get("error") or fingpt_pred.get("error"):
                return None
            
            # استخراج سیگنال‌ها
            chronos_trend = chronos_pred["signals"]["trend"]
            chronos_conf = chronos_pred["signals"]["confidence"]
            
            fingpt_prediction = fingpt_pred["prediction"]
            fingpt_conf = fingpt_pred["confidence"]
            
            # تبدیل پیش‌بینی FinGPT
            fingpt_trend = "bullish" if fingpt_prediction == "up" else \
                          "bearish" if fingpt_prediction == "down" else "neutral"
            
            # محاسبه اعتماد ترکیبی
            weighted_confidence = (chronos_conf * weights.chronos_weight + 
                                 fingpt_conf * weights.fingpt_weight)
            weighted_confidence *= weights.confidence_multiplier
            
            # تعیین سیگنال نهایی
            if chronos_trend == fingpt_trend and chronos_trend != "neutral":
                final_trend = chronos_trend
                weighted_confidence *= 1.2  # تقویت در صورت توافق
            elif chronos_trend != "neutral" and fingpt_trend == "neutral":
                final_trend = chronos_trend
                weighted_confidence *= weights.chronos_weight
            elif fingpt_trend != "neutral" and chronos_trend == "neutral":
                final_trend = fingpt_trend
                weighted_confidence *= weights.fingpt_weight
            else:
                return None  # عدم توافق
            
            # بررسی threshold
            if weighted_confidence < weights.combined_threshold:
                return None
            
            return {
                "trend": final_trend,
                "confidence": weighted_confidence,
                "chronos_signal": chronos_trend,
                "fingpt_signal": fingpt_trend,
                "weights_used": asdict(weights)
            }
            
        except Exception as e:
            logger.error(f"Error combining predictions: {str(e)}")
            return None
    
    def simulate_trade(self, signal: Dict, price_data: pd.DataFrame) -> Dict:
        """شبیه‌سازی معامله"""
        try:
            entry_price = price_data.iloc[0]['close']
            direction = signal["trend"]
            confidence = signal["confidence"]
            
            # محاسبه اندازه موقعیت بر اساس اعتماد
            base_position = 0.02
            position_size = base_position * min(confidence / 0.8, 1.5)
            
            # تنظیم stop loss و take profit
            stop_loss_pct = 0.01
            take_profit_pct = 0.02
            
            if direction == "bullish":
                stop_loss = entry_price * (1 - stop_loss_pct)
                take_profit = entry_price * (1 + take_profit_pct)
            else:
                stop_loss = entry_price * (1 + stop_loss_pct)
                take_profit = entry_price * (1 - take_profit_pct)
            
            # شبیه‌سازی نتیجه
            exit_price = entry_price
            exit_reason = "time_exit"
            
            for _, row in price_data.iterrows():
                if direction == "bullish":
                    if row['low'] <= stop_loss:
                        exit_price = stop_loss
                        exit_reason = "stop_loss"
                        break
                    elif row['high'] >= take_profit:
                        exit_price = take_profit
                        exit_reason = "take_profit"
                        break
                else:
                    if row['high'] >= stop_loss:
                        exit_price = stop_loss
                        exit_reason = "stop_loss"
                        break
                    elif row['low'] <= take_profit:
                        exit_price = take_profit
                        exit_reason = "take_profit"
                        break
                
                exit_price = row['close']
            
            # محاسبه P&L
            if direction == "bullish":
                pnl = (exit_price - entry_price) * position_size * 10000  # برای forex
            else:
                pnl = (entry_price - exit_price) * position_size * 10000
            
            return {
                "entry_price": entry_price,
                "exit_price": exit_price,
                "direction": direction,
                "confidence": confidence,
                "position_size": position_size,
                "pnl": pnl,
                "exit_reason": exit_reason,
                "signal": signal
            }
            
        except Exception as e:
            logger.error(f"Error simulating trade: {str(e)}")
            return {"error": str(e)}
    
    def save_backtest_performance(self, symbol: str, timeframe: str, signal: Dict, 
                                trade_result: Dict, market_data: pd.DataFrame):
        """ذخیره عملکرد بک‌تست"""
        try:
            # محاسبه شرایط بازار
            close_prices = market_data['close'].values
            returns = np.diff(close_prices) / close_prices[:-1]
            volatility = np.std(returns) * np.sqrt(24)
            
            market_conditions = {
                "volatility": float(volatility),
                "price_level": float(close_prices[-1]),
                "recent_high": float(np.max(close_prices[-120:])),
                "recent_low": float(np.min(close_prices[-120:]))
            }
            
            # ایجاد metrics
            metrics = ModelPerformanceMetrics(
                symbol=symbol,
                timeframe=timeframe,
                model_name="adaptive_combined",
                timestamp=datetime.now(),
                prediction=signal["trend"],
                confidence=signal["confidence"],
                actual_outcome="win" if trade_result["pnl"] > 0 else "loss",
                accuracy=1.0 if trade_result["pnl"] > 0 else 0.0,
                profit_loss=trade_result["pnl"],
                market_conditions=market_conditions,
                technical_indicators={}
            )
            
            self.db.save_performance(metrics)
            
        except Exception as e:
            logger.error(f"Error saving backtest performance: {str(e)}")
    
    def calculate_backtest_metrics(self, trades: List[Dict]) -> Dict[str, Any]:
        """محاسبه معیارهای بک‌تست"""
        try:
            if not trades:
                return {}
            
            pnls = [trade["pnl"] for trade in trades if "pnl" in trade]
            
            if not pnls:
                return {}
            
            winning_trades = [pnl for pnl in pnls if pnl > 0]
            losing_trades = [pnl for pnl in pnls if pnl < 0]
            
            return {
                "total_trades": len(trades),
                "winning_trades": len(winning_trades),
                "losing_trades": len(losing_trades),
                "win_rate": len(winning_trades) / len(trades),
                "total_pnl": sum(pnls),
                "avg_win": np.mean(winning_trades) if winning_trades else 0,
                "avg_loss": np.mean(losing_trades) if losing_trades else 0,
                "profit_factor": sum(winning_trades) / abs(sum(losing_trades)) if losing_trades else float('inf'),
                "max_drawdown": self.calculate_max_drawdown(pnls),
                "sharpe_ratio": np.mean(pnls) / np.std(pnls) if len(pnls) > 1 and np.std(pnls) > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error calculating backtest metrics: {str(e)}")
            return {}
    
    def calculate_max_drawdown(self, pnls: List[float]) -> float:
        """محاسبه حداکثر افت"""
        try:
            cumulative = np.cumsum(pnls)
            peak = cumulative[0]
            max_drawdown = 0
            
            for value in cumulative:
                if value > peak:
                    peak = value
                drawdown = (peak - value) / peak if peak != 0 else 0
                max_drawdown = max(max_drawdown, drawdown)
            
            return max_drawdown
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {str(e)}")
            return 0

class AdaptivePlutusSystem:
    """سیستم جامع تطبیقی Plutus"""
    
    def __init__(self, db_path: str = "adaptive_plutus.db"):
        self.db = PerformanceDatabase(db_path)
        self.learning_engine = AdaptiveLearningEngine(self.db)
        self.backtest_engine = BacktestingEngine(self.db)
        self.trading_system = PlutusIntegratedTradingSystem()
        
        # Thread برای یادگیری مداوم
        self.learning_thread = None
        self.is_learning = False
        
        logger.info("Adaptive Plutus System initialized")
    
    def start_continuous_learning(self, symbols: List[str] = None, 
                                update_interval: int = 3600):
        """شروع یادگیری مداوم"""
        if symbols is None:
            symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD"]
        
        self.is_learning = True
        self.learning_thread = threading.Thread(
            target=self._continuous_learning_loop,
            args=(symbols, update_interval)
        )
        self.learning_thread.daemon = True
        self.learning_thread.start()
        
        logger.info(f"Started continuous learning for {len(symbols)} symbols")
    
    def stop_continuous_learning(self):
        """توقف یادگیری مداوم"""
        self.is_learning = False
        if self.learning_thread:
            self.learning_thread.join()
        logger.info("Stopped continuous learning")
    
    def _continuous_learning_loop(self, symbols: List[str], update_interval: int):
        """حلقه یادگیری مداوم"""
        while self.is_learning:
            try:
                for symbol in symbols:
                    if not self.is_learning:
                        break
                    
                    # بهینه‌سازی وزن‌ها
                    self.learning_engine.optimize_model_weights(symbol, "H1")
                    
                    # اجرای بک‌تست کوتاه
                    backtest_result = self.backtest_engine.run_adaptive_backtest(
                        symbol, "H1", periods=20, update_frequency=5
                    )
                    
                    if not backtest_result.get("error"):
                        logger.info(f"Adaptive backtest completed for {symbol}")
                    
                    time.sleep(10)  # وقفه بین نمادها
                
                # وقفه بین چرخه‌های یادگیری
                time.sleep(update_interval)
                
            except Exception as e:
                logger.error(f"Error in continuous learning loop: {str(e)}")
                time.sleep(60)  # وقفه در صورت خطا
    
    def get_adaptive_signal(self, symbol: str, timeframe: str = "H1") -> Dict[str, Any]:
        """دریافت سیگنال تطبیقی"""
        try:
            # دریافت وزن‌های بهینه
            weights = self.db.get_latest_weights(symbol, timeframe)
            if not weights:
                weights = AdaptiveWeights()
            
            # دریافت سیگنال با وزن‌های تطبیقی
            signal = self.trading_system.get_real_time_signal(symbol, timeframe)
            
            if not signal.get("error"):
                # اعمال وزن‌های تطبیقی
                combined_signal = signal.get("combined_signal", {})
                
                if combined_signal:
                    # تنظیم اعتماد بر اساس وزن‌های یادگیری شده
                    adjusted_confidence = combined_signal.get("confidence", 0) * weights.confidence_multiplier
                    combined_signal["confidence"] = min(0.95, adjusted_confidence)
                    combined_signal["adaptive_weights"] = asdict(weights)
                
                signal["combined_signal"] = combined_signal
            
            return signal
            
        except Exception as e:
            logger.error(f"Error getting adaptive signal: {str(e)}")
            return {"error": str(e)}
    
    def run_comprehensive_optimization(self, symbols: List[str] = None) -> Dict[str, Any]:
        """اجرای بهینه‌سازی جامع"""
        if symbols is None:
            symbols = ["EURUSD", "GBPUSD", "USDJPY"]
        
        logger.info("Starting comprehensive optimization")
        
        results = {
            "optimization_start": datetime.now().isoformat(),
            "symbols": {},
            "summary": {}
        }
        
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = {}
            
            for symbol in symbols:
                future = executor.submit(
                    self.backtest_engine.run_adaptive_backtest,
                    symbol, "H1", 100, 20
                )
                futures[symbol] = future
            
            for symbol, future in futures.items():
                try:
                    result = future.result(timeout=300)  # 5 دقیقه timeout
                    results["symbols"][symbol] = result
                except Exception as e:
                    logger.error(f"Error optimizing {symbol}: {str(e)}")
                    results["symbols"][symbol] = {"error": str(e)}
        
        # محاسبه خلاصه
        successful_optimizations = [r for r in results["symbols"].values() if not r.get("error")]
        
        if successful_optimizations:
            avg_win_rate = np.mean([r["performance_metrics"]["win_rate"] for r in successful_optimizations])
            avg_profit_factor = np.mean([r["performance_metrics"]["profit_factor"] for r in successful_optimizations if r["performance_metrics"]["profit_factor"] != float('inf')])
            
            results["summary"] = {
                "successful_optimizations": len(successful_optimizations),
                "avg_win_rate": avg_win_rate,
                "avg_profit_factor": avg_profit_factor,
                "optimization_end": datetime.now().isoformat()
            }
        
        logger.info("Comprehensive optimization completed")
        return results
    
    def generate_learning_report(self, symbol: str, days: int = 30) -> str:
        """تولید گزارش یادگیری"""
        try:
            # تحلیل عملکرد مدل‌ها
            chronos_analysis = self.learning_engine.analyze_model_performance(symbol, "chronos", days)
            fingpt_analysis = self.learning_engine.analyze_model_performance(symbol, "fingpt", days)
            combined_analysis = self.learning_engine.analyze_model_performance(symbol, "adaptive_combined", days)
            
            # وزن‌های فعلی
            current_weights = self.db.get_latest_weights(symbol, "H1")
            
            report = []
            report.append(f"ADAPTIVE LEARNING REPORT - {symbol}")
            report.append("=" * 50)
            report.append(f"Analysis Period: Last {days} days")
            report.append(f"Report Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report.append("")
            
            # عملکرد مدل‌ها
            report.append("MODEL PERFORMANCE:")
            report.append("-" * 20)
            
            if not chronos_analysis.get("error"):
                report.append(f"Chronos Model:")
                report.append(f"  Accuracy: {chronos_analysis['avg_accuracy']:.1%}")
                report.append(f"  Avg Profit: {chronos_analysis['avg_profit']:.2f}")
                report.append(f"  Win Rate: {chronos_analysis['win_rate']:.1%}")
                report.append(f"  Predictions: {chronos_analysis['total_predictions']}")
            
            if not fingpt_analysis.get("error"):
                report.append(f"FinGPT Model:")
                report.append(f"  Accuracy: {fingpt_analysis['avg_accuracy']:.1%}")
                report.append(f"  Avg Profit: {fingpt_analysis['avg_profit']:.2f}")
                report.append(f"  Win Rate: {fingpt_analysis['win_rate']:.1%}")
                report.append(f"  Predictions: {fingpt_analysis['total_predictions']}")
            
            if not combined_analysis.get("error"):
                report.append(f"Adaptive Combined:")
                report.append(f"  Accuracy: {combined_analysis['avg_accuracy']:.1%}")
                report.append(f"  Avg Profit: {combined_analysis['avg_profit']:.2f}")
                report.append(f"  Win Rate: {combined_analysis['win_rate']:.1%}")
                report.append(f"  Predictions: {combined_analysis['total_predictions']}")
            
            report.append("")
            
            # وزن‌های فعلی
            if current_weights:
                report.append("CURRENT ADAPTIVE WEIGHTS:")
                report.append("-" * 25)
                report.append(f"Chronos Weight: {current_weights.chronos_weight:.3f}")
                report.append(f"FinGPT Weight: {current_weights.fingpt_weight:.3f}")
                report.append(f"Combined Threshold: {current_weights.combined_threshold:.3f}")
                report.append(f"Confidence Multiplier: {current_weights.confidence_multiplier:.3f}")
                report.append(f"Last Updated: {current_weights.last_updated}")
                report.append("")
            
            # توصیه‌ها
            report.append("RECOMMENDATIONS:")
            report.append("-" * 15)
            
            if not chronos_analysis.get("error") and not fingpt_analysis.get("error"):
                if chronos_analysis["avg_accuracy"] > fingpt_analysis["avg_accuracy"]:
                    report.append("• Chronos model showing better accuracy")
                    report.append("• Consider increasing Chronos weight")
                else:
                    report.append("• FinGPT model showing better accuracy")
                    report.append("• Consider increasing FinGPT weight")
                
                if chronos_analysis.get("performance_trend", {}).get("accuracy_trend") == "improving":
                    report.append("• Chronos model improving over time")
                
                if fingpt_analysis.get("performance_trend", {}).get("accuracy_trend") == "improving":
                    report.append("• FinGPT model improving over time")
            
            return "\n".join(report)
            
        except Exception as e:
            logger.error(f"Error generating learning report: {str(e)}")
            return f"Error generating report: {str(e)}"

def main():
    """مثال استفاده از سیستم تطبیقی"""
    print("Adaptive Plutus System - Continuous Learning Demo")
    print("=" * 60)
    
    # ایجاد سیستم
    system = AdaptivePlutusSystem()
    
    # شروع یادگیری مداوم
    system.start_continuous_learning(["EURUSD", "GBPUSD"], update_interval=300)  # هر 5 دقیقه
    
    try:
        # اجرای بهینه‌سازی جامع
        optimization_results = system.run_comprehensive_optimization(["EURUSD"])
        
        print("\nOPTIMIZATION RESULTS:")
        print(json.dumps(optimization_results, indent=2, default=str))
        
        # تست سیگنال تطبیقی
        print("\nTESTING ADAPTIVE SIGNALS:")
        print("-" * 30)
        
        for symbol in ["EURUSD", "GBPUSD"]:
            signal = system.get_adaptive_signal(symbol)
            
            if not signal.get("error"):
                recommendation = signal.get("recommendation", {})
                print(f"{symbol}: {recommendation.get('action', 'HOLD')} - {recommendation.get('reason', 'No reason')}")
            else:
                print(f"{symbol}: ERROR - {signal['error']}")
        
        # تولید گزارش یادگیری
        print("\nLEARNING REPORT:")
        print("-" * 20)
        report = system.generate_learning_report("EURUSD", 30)
        print(report)
        
        # نگه داشتن سیستم برای یادگیری
        print("\nSystem is learning... Press Ctrl+C to stop")
        time.sleep(60)  # یک دقیقه یادگیری
        
    except KeyboardInterrupt:
        print("\nStopping system...")
    finally:
        system.stop_continuous_learning()
        print("System stopped successfully!")

if __name__ == "__main__":
    main() 