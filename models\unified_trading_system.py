"""
سیستم یکپارچه معاملاتی - ترکیب هوشمند مدل‌های RL و پلوتوس
Unified Trading System - Intelligent Integration of RL and Plutus Models

✅ Updated with AI Models Integration:
- Sentiment Analysis Integration
- Market Sentiment Scoring
- News Impact Analysis
- Social Media Sentiment
"""


# Import warning suppressor
try:
    from warning_suppressor import suppress_all_warnings
    suppress_all_warnings()
except ImportError:
    import warnings
    warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
import torch
import os
import json
import logging
import sys
from typing import Dict, List, Tuple, Any, Optional, Union
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from dataclasses import dataclass, asdict
import sqlite3
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import time
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import project modules
try:
    from models.ensemble_model import EnsembleModel
    from models.meta_learner import MetaLearner
    from models.rl_models import RLModelFactory
    from utils.plutus_integration import PlutusFinancialForecaster, PlutusConfig
    from utils.adaptive_plutus_system import AdaptivePlutusSystem, AdaptiveWeights
    from utils.sentiment_analyzer import AdvancedSentimentAnalyzer, SentimentResult
    from env.trading_env import TradingEnv
    SENTIMENT_AVAILABLE = True
except ImportError as e:
    # Fallback imports for testing
    logging.warning(f"Import warning: {e}")
    SENTIMENT_AVAILABLE = False
    
    class MockEnsembleModel:
        def __init__(self, *args, **kwargs):
            pass
        def predict(self, observation):
            return np.array([0.5]), None
    
    class MockMetaLearner:
        def __init__(self, *args, **kwargs):
            pass
        def extract_market_features(self, df):
            return {'volatility': 0.01, 'trend': 0.02}
    
    class MockRLModelFactory:
        def __init__(self):
            pass
    
    class MockAdaptivePlutusSystem:
        def __init__(self, *args, **kwargs):
            pass
        def get_adaptive_signal(self, symbol, timeframe):
            return {
                'trend': 'bullish',
                'confidence': 0.7,
                'forecast': {'mean': [1.1050]},
                'trend_strength': 0.6
            }
        def start_continuous_learning(self, *args, **kwargs):
            pass
        def stop_continuous_learning(self):
            pass
    
    class MockSentimentAnalyzer:
        def __init__(self, *args, **kwargs):
            pass
        def analyze(self, text, language=None, source=None):
            return type('SentimentResult', (), {
                'label': 'neutral',
                'score': 0.5,
                'confidence': 0.5,
                'market_impact': 0.0
            })()
        def get_market_sentiment(self):
            return type('MarketSentiment', (), {
                'overall_sentiment': 0.0,
                'trend': 'neutral',
                'confidence': 0.5
            })()
    
    EnsembleModel = MockEnsembleModel
    MetaLearner = MockMetaLearner
    RLModelFactory = MockRLModelFactory
    AdaptivePlutusSystem = MockAdaptivePlutusSystem
    AdvancedSentimentAnalyzer = MockSentimentAnalyzer
    
    class AdaptiveWeights:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class UnifiedSignal:
    """سیگنال یکپارچه شامل تمام اطلاعات"""
    symbol: str
    timeframe: str
    timestamp: datetime
    
    # RL Signals
    rl_action: str  # buy, sell, hold
    rl_confidence: float
    rl_model_used: str
    
    # Plutus Signals
    plutus_prediction: Dict[str, Any]
    plutus_confidence: float
    plutus_trend: str
    
    # Sentiment Signals - NEW
    sentiment_score: float
    sentiment_label: str
    sentiment_confidence: float
    market_sentiment: float
    news_impact: float
    
    # Combined Signal
    final_action: str
    final_confidence: float
    combination_method: str
    
    # Market Context
    market_regime: str
    volatility_level: str
    risk_score: float
    
    # Additional Info
    reasoning: str
    position_size: float
    stop_loss: float
    take_profit: float

@dataclass
class PerformanceMetrics:
    """معیارهای عملکرد سیستم یکپارچه"""
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    avg_trade_duration: float
    
    # Component Performance
    rl_only_performance: Dict[str, float]
    plutus_only_performance: Dict[str, float]
    combined_performance: Dict[str, float]

class FeatureEnhancer:
    """تقویت‌کننده ویژگی‌ها با استفاده از پیش‌بینی‌های پلوتوس"""
    
    def __init__(self):
        self.feature_history = {}
        self.enhancement_weights = {
            'price_prediction': 0.3,
            'trend_prediction': 0.25,
            'volatility_prediction': 0.2,
            'confidence_score': 0.15,
            'market_regime': 0.1
        }
    
    def enhance_features(self, 
                        market_data: pd.DataFrame, 
                        plutus_prediction: Dict[str, Any],
                        symbol: str) -> Dict[str, Any]:
        """تقویت ویژگی‌های بازار با پیش‌بینی‌های پلوتوس"""
        
        try:
            # Basic market features
            base_features = self._extract_base_features(market_data)
            
            # Plutus enhanced features
            plutus_features = self._extract_plutus_features(plutus_prediction)
            
            # Technical indicators enhanced with Plutus
            technical_features = self._enhance_technical_indicators(
                market_data, plutus_prediction
            )
            
            # Market regime features
            regime_features = self._extract_regime_features(
                market_data, plutus_prediction
            )
            
            # Combine all features
            enhanced_features = {
                **base_features,
                **plutus_features,
                **technical_features,
                **regime_features,
                'enhancement_timestamp': datetime.now().isoformat(),
                'symbol': symbol
            }
            
            # Store in history
            self.feature_history[symbol] = enhanced_features
            
            return enhanced_features
            
        except Exception as e:
            logger.error(f"Error enhancing features for {symbol}: {str(e)}")
            return self._extract_base_features(market_data)
    
    def _extract_base_features(self, market_data: pd.DataFrame) -> Dict[str, float]:
        """استخراج ویژگی‌های پایه بازار"""
        
        if market_data.empty:
            return {}
        
        close_prices = market_data['close'].values
        
        features = {
            'current_price': close_prices[-1],
            'price_change_1h': (close_prices[-1] - close_prices[-2]) / close_prices[-2] if len(close_prices) > 1 else 0,
            'price_change_24h': (close_prices[-1] - close_prices[-24]) / close_prices[-24] if len(close_prices) > 24 else 0,
            'volatility_1h': np.std(close_prices[-24:]) / np.mean(close_prices[-24:]) if len(close_prices) >= 24 else 0,
            'volume_ratio': market_data['volume'].iloc[-1] / market_data['volume'].mean() if 'volume' in market_data.columns else 1.0,
            'high_low_ratio': (market_data['high'].iloc[-1] - market_data['low'].iloc[-1]) / market_data['close'].iloc[-1] if 'high' in market_data.columns and 'low' in market_data.columns else 0
        }
        
        return features
    
    def _extract_plutus_features(self, plutus_prediction: Dict[str, Any]) -> Dict[str, float]:
        """استخراج ویژگی‌های پلوتوس"""
        
        if not plutus_prediction or 'forecast' not in plutus_prediction:
            return {}
        
        forecast = plutus_prediction['forecast']
        
        features = {
            'plutus_price_prediction': forecast.get('mean', [0])[-1] if forecast.get('mean') else 0,
            'plutus_confidence': plutus_prediction.get('confidence', 0),
            'plutus_trend_strength': plutus_prediction.get('trend_strength', 0),
            'plutus_volatility_forecast': plutus_prediction.get('volatility_forecast', 0),
            'plutus_prediction_horizon': len(forecast.get('mean', [])),
        }
        
        # Quantile features
        quantiles = forecast.get('quantiles', {})
        if quantiles:
            features['plutus_upper_bound'] = quantiles.get('0.9', [0])[-1] if quantiles.get('0.9') else 0
            features['plutus_lower_bound'] = quantiles.get('0.1', [0])[-1] if quantiles.get('0.1') else 0
            features['plutus_prediction_range'] = features['plutus_upper_bound'] - features['plutus_lower_bound']
        
        return features
    
    def _enhance_technical_indicators(self, 
                                    market_data: pd.DataFrame, 
                                    plutus_prediction: Dict[str, Any]) -> Dict[str, float]:
        """تقویت اندیکاتورهای فنی با پیش‌بینی‌های پلوتوس"""
        
        features = {}
        
        if market_data.empty:
            return features
        
        close_prices = market_data['close'].values
        
        # RSI enhanced with Plutus prediction
        rsi = self._calculate_rsi(close_prices)
        plutus_trend = plutus_prediction.get('trend', 'neutral')
        
        features['rsi'] = rsi
        features['rsi_plutus_alignment'] = 1.0 if (
            (rsi > 70 and plutus_trend == 'bearish') or
            (rsi < 30 and plutus_trend == 'bullish')
        ) else 0.0
        
        # Moving averages enhanced
        if len(close_prices) >= 20:
            ma_20 = np.mean(close_prices[-20:])
            features['ma_20'] = ma_20
            features['price_ma_ratio'] = close_prices[-1] / ma_20
            
            # Plutus prediction vs MA
            plutus_price = plutus_prediction.get('forecast', {}).get('mean', [0])
            if plutus_price:
                features['plutus_ma_divergence'] = (plutus_price[-1] - ma_20) / ma_20
        
        return features
    
    def _extract_regime_features(self, 
                               market_data: pd.DataFrame, 
                               plutus_prediction: Dict[str, Any]) -> Dict[str, float]:
        """استخراج ویژگی‌های رژیم بازار"""
        
        features = {}
        
        if market_data.empty:
            return features
        
        close_prices = market_data['close'].values
        
        # Volatility regime
        volatility = np.std(close_prices[-24:]) / np.mean(close_prices[-24:]) if len(close_prices) >= 24 else 0
        
        if volatility > 0.02:
            features['volatility_regime'] = 2  # High volatility
        elif volatility > 0.01:
            features['volatility_regime'] = 1  # Medium volatility
        else:
            features['volatility_regime'] = 0  # Low volatility
        
        # Trend regime
        trend_strength = plutus_prediction.get('trend_strength', 0)
        features['trend_regime'] = min(2, max(0, int(trend_strength * 2)))
        
        # Market phase (combining volatility and trend)
        if features['volatility_regime'] == 2 and features['trend_regime'] >= 1:
            features['market_phase'] = 3  # Trending volatile
        elif features['volatility_regime'] <= 1 and features['trend_regime'] >= 1:
            features['market_phase'] = 2  # Trending stable
        elif features['volatility_regime'] == 2 and features['trend_regime'] == 0:
            features['market_phase'] = 1  # Sideways volatile
        else:
            features['market_phase'] = 0  # Sideways stable
        
        return features
    
    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """محاسبه RSI"""
        
        if len(prices) < period + 1:
            return 50.0
        
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi

class IntelligentWeightingSystem:
    """سیستم وزن‌دهی هوشمند برای ترکیب سیگنال‌ها"""
    
    def __init__(self, db_path: str = "unified_system.db"):
        self.db_path = db_path
        self.weights = {
            'rl_weight': 0.6,
            'plutus_weight': 0.4,
            'confidence_multiplier': 1.0,
            'agreement_bonus': 0.2,
            'disagreement_penalty': 0.3
        }
        self.performance_history = []
        self.init_database()
    
    def init_database(self):
        """ایجاد پایگاه داده برای ذخیره عملکرد"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS signal_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT,
                    timeframe TEXT,
                    timestamp DATETIME,
                    rl_signal TEXT,
                    rl_confidence REAL,
                    plutus_signal TEXT,
                    plutus_confidence REAL,
                    combined_signal TEXT,
                    combined_confidence REAL,
                    actual_outcome TEXT,
                    profit_loss REAL,
                    rl_weight REAL,
                    plutus_weight REAL
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS weight_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME,
                    symbol TEXT,
                    rl_weight REAL,
                    plutus_weight REAL,
                    confidence_multiplier REAL,
                    reason TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
        except Exception as e:
            logger.warning(f"Database initialization warning: {e}")
    
    def calculate_combined_signal(self, 
                                rl_signal: Dict[str, Any], 
                                plutus_signal: Dict[str, Any],
                                market_context: Dict[str, Any]) -> Dict[str, Any]:
        """محاسبه سیگنال ترکیبی هوشمند"""
        
        try:
            # Extract signal components
            rl_action = rl_signal.get('action', 'hold')
            rl_confidence = rl_signal.get('confidence', 0.5)
            
            plutus_trend = plutus_signal.get('trend', 'neutral')
            plutus_confidence = plutus_signal.get('confidence', 0.5)
            
            # Convert plutus trend to action
            plutus_action = self._trend_to_action(plutus_trend)
            
            # Check signal agreement
            agreement = (rl_action == plutus_action)
            
            # Calculate base weights
            rl_weight = self.weights['rl_weight']
            plutus_weight = self.weights['plutus_weight']
            
            # Adjust weights based on confidence
            confidence_factor = (rl_confidence + plutus_confidence) / 2
            rl_weight *= (1 + (rl_confidence - 0.5) * self.weights['confidence_multiplier'])
            plutus_weight *= (1 + (plutus_confidence - 0.5) * self.weights['confidence_multiplier'])
            
            # Adjust weights based on agreement
            if agreement:
                # Boost both weights when models agree
                boost_factor = 1 + self.weights['agreement_bonus']
                rl_weight *= boost_factor
                plutus_weight *= boost_factor
                combined_confidence = min(0.95, (rl_confidence + plutus_confidence) / 2 * boost_factor)
                final_action = rl_action
            else:
                # Reduce weights when models disagree
                penalty_factor = 1 - self.weights['disagreement_penalty']
                rl_weight *= penalty_factor
                plutus_weight *= penalty_factor
                combined_confidence = (rl_confidence + plutus_confidence) / 2 * penalty_factor
                
                # Choose action based on higher confidence
                if rl_confidence > plutus_confidence:
                    final_action = rl_action
                else:
                    final_action = plutus_action
            
            # Normalize weights
            total_weight = rl_weight + plutus_weight
            if total_weight > 0:
                rl_weight /= total_weight
                plutus_weight /= total_weight
            
            # Market context adjustment
            market_adjustment = self._calculate_market_adjustment(market_context)
            combined_confidence *= market_adjustment
            
            # Final decision logic
            if combined_confidence < 0.6:
                final_action = 'hold'
                combined_confidence = 0.5
            
            return {
                'final_action': final_action,
                'combined_confidence': combined_confidence,
                'rl_weight': rl_weight,
                'plutus_weight': plutus_weight,
                'agreement': agreement,
                'confidence_factor': confidence_factor,
                'market_adjustment': market_adjustment,
                'reasoning': self._generate_reasoning(
                    rl_signal, plutus_signal, final_action, agreement, combined_confidence
                )
            }
            
        except Exception as e:
            logger.error(f"Error calculating combined signal: {str(e)}")
            return {
                'final_action': 'hold',
                'combined_confidence': 0.5,
                'rl_weight': 0.5,
                'plutus_weight': 0.5,
                'agreement': False,
                'error': str(e)
            }
    
    def _trend_to_action(self, trend: str) -> str:
        """تبدیل trend به action"""
        
        trend_mapping = {
            'bullish': 'buy',
            'bearish': 'sell',
            'neutral': 'hold',
            'up': 'buy',
            'down': 'sell',
            'sideways': 'hold'
        }
        
        return trend_mapping.get(trend.lower(), 'hold')
    
    def _calculate_market_adjustment(self, market_context: Dict[str, Any]) -> float:
        """محاسبه تعدیل بازار"""
        
        adjustment = 1.0
        
        # Volatility adjustment
        volatility_regime = market_context.get('volatility_regime', 1)
        if volatility_regime == 2:  # High volatility
            adjustment *= 0.8  # Reduce confidence in high volatility
        elif volatility_regime == 0:  # Low volatility
            adjustment *= 1.1  # Increase confidence in low volatility
        
        # Trend strength adjustment
        trend_regime = market_context.get('trend_regime', 1)
        if trend_regime >= 2:  # Strong trend
            adjustment *= 1.2  # Increase confidence in strong trends
        elif trend_regime == 0:  # No trend
            adjustment *= 0.9  # Reduce confidence in sideways markets
        
        return min(1.5, max(0.5, adjustment))
    
    def _generate_reasoning(self, 
                          rl_signal: Dict[str, Any], 
                          plutus_signal: Dict[str, Any],
                          final_action: str,
                          agreement: bool,
                          confidence: float) -> str:
        """تولید توضیح تصمیم"""
        
        reasoning_parts = []
        
        # Model agreement
        if agreement:
            reasoning_parts.append(f"Both RL and Plutus models agree on {final_action} signal")
        else:
            reasoning_parts.append(f"Models disagree - RL: {rl_signal.get('action', 'hold')}, Plutus: {plutus_signal.get('trend', 'neutral')}")
        
        # Confidence levels
        reasoning_parts.append(f"Combined confidence: {confidence:.2f}")
        reasoning_parts.append(f"RL confidence: {rl_signal.get('confidence', 0.5):.2f}")
        reasoning_parts.append(f"Plutus confidence: {plutus_signal.get('confidence', 0.5):.2f}")
        
        # Final decision
        reasoning_parts.append(f"Final decision: {final_action}")
        
        return " | ".join(reasoning_parts)
    
    def update_weights_from_performance(self, 
                                      symbol: str,
                                      performance_data: Dict[str, Any]):
        """به‌روزرسانی وزن‌ها بر اساس عملکرد"""
        
        try:
            rl_performance = performance_data.get('rl_performance', 0)
            plutus_performance = performance_data.get('plutus_performance', 0)
            
            # Calculate performance ratio
            total_performance = abs(rl_performance) + abs(plutus_performance)
            
            if total_performance > 0:
                rl_ratio = abs(rl_performance) / total_performance
                plutus_ratio = abs(plutus_performance) / total_performance
                
                # Update weights with momentum
                momentum = 0.1
                self.weights['rl_weight'] = (1 - momentum) * self.weights['rl_weight'] + momentum * rl_ratio
                self.weights['plutus_weight'] = (1 - momentum) * self.weights['plutus_weight'] + momentum * plutus_ratio
                
                # Normalize weights
                total_weight = self.weights['rl_weight'] + self.weights['plutus_weight']
                self.weights['rl_weight'] /= total_weight
                self.weights['plutus_weight'] /= total_weight
                
                # Save weight update
                self._save_weight_update(symbol, "performance_based")
                
                logger.info(f"Updated weights for {symbol}: RL={self.weights['rl_weight']:.3f}, Plutus={self.weights['plutus_weight']:.3f}")
        
        except Exception as e:
            logger.error(f"Error updating weights: {str(e)}")
    
    def _save_weight_update(self, symbol: str, reason: str):
        """ذخیره به‌روزرسانی وزن‌ها"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO weight_history (
                    timestamp, symbol, rl_weight, plutus_weight, confidence_multiplier, reason
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now(),
                symbol,
                self.weights['rl_weight'],
                self.weights['plutus_weight'],
                self.weights['confidence_multiplier'],
                reason
            ))
            
            conn.commit()
            conn.close()
        except Exception as e:
            logger.warning(f"Could not save weight update: {e}")

class UnifiedTradingSystem:
    """سیستم یکپارچه معاملاتی - ترکیب هوشمند RL و پلوتوس و Sentiment"""
    
    def __init__(self, 
                 config_path: str = "config.yaml",
                 db_path: str = "unified_system.db"):
        """
        مقداردهی اولیه سیستم یکپارچه
        
        پارامترها:
        -----------
        config_path : str
            مسیر فایل پیکربندی
        db_path : str
            مسیر پایگاه داده
        """
        
        self.config_path = config_path
        self.db_path = db_path
        
        # Initialize components
        self.rl_factory = RLModelFactory()
        self.meta_learner = MetaLearner(model_factory=self.rl_factory)
        self.ensemble_model = None  # Will be initialized later
        
        # Initialize Plutus system
        self.adaptive_plutus = AdaptivePlutusSystem(db_path=db_path)
        
        # Initialize Sentiment system - NEW
        self.sentiment_integrator = SentimentIntegrator(languages=['en', 'fa'])
        
        # Initialize enhancement and weighting systems
        self.feature_enhancer = FeatureEnhancer()
        self.weighting_system = IntelligentWeightingSystem(db_path=db_path)
        
        # Performance tracking
        self.performance_tracker = {}
        self.signal_history = []
        
        # System state
        self.is_running = False
        self.last_update = datetime.now()
        
        logger.info("✅ Unified Trading System initialized successfully with AI integration")
    
    def initialize_models(self, 
                         symbols: List[str],
                         model_configs: List[Dict[str, Any]] = None):
        """مقداردهی اولیه مدل‌ها"""
        
        try:
            # Default model configurations
            if model_configs is None:
                model_configs = [
                    {'model_type': 'ppo', 'params': {'learning_rate': 0.0003}},
                    {'model_type': 'sac', 'params': {'learning_rate': 0.0003}},
                    {'model_type': 'a2c', 'params': {'learning_rate': 0.0007}},
                ]
            
            # Initialize ensemble model
            self.ensemble_model = EnsembleModel(
                model_configs=model_configs,
                voting_method='weighted',
                confidence_threshold=0.6
            )
            
            # Start adaptive Plutus system
            self.adaptive_plutus.start_continuous_learning(
                symbols=symbols,
                update_interval=3600  # Update every hour
            )
            
            logger.info(f"Models initialized for symbols: {symbols}")
            
        except Exception as e:
            logger.error(f"Error initializing models: {str(e)}")
            # Don't raise - continue with mock models
    
    def get_unified_signal(self, 
                          symbol: str, 
                          timeframe: str = "H1",
                          market_data: pd.DataFrame = None,
                          news_texts: List[str] = None) -> UnifiedSignal:
        """دریافت سیگنال یکپارچه با تحلیل احساسات"""
        
        try:
            timestamp = datetime.now()
            
            # Load market data if not provided
            if market_data is None:
                market_data = self._load_market_data(symbol, timeframe)
            
            if market_data.empty:
                raise ValueError(f"No market data available for {symbol}")
            
            # Get Plutus signal
            plutus_signal = self.adaptive_plutus.get_adaptive_signal(symbol, timeframe)
            
            # Enhance features with Plutus predictions
            enhanced_features = self.feature_enhancer.enhance_features(
                market_data, plutus_signal, symbol
            )
            
            # Get RL signal using enhanced features
            rl_signal = self._get_rl_signal(enhanced_features, symbol, timeframe)
            
            # Get sentiment analysis - NEW
            sentiment_analysis = self.sentiment_integrator.analyze_market_sentiment(
                symbol, news_texts
            )
            sentiment_signal = self.sentiment_integrator.sentiment_to_trading_signal(
                sentiment_analysis
            )
            
            # Get market context
            market_context = self._analyze_market_context(market_data, plutus_signal)
            
            # Combine signals intelligently (now including sentiment)
            combined_signal = self.weighting_system.calculate_combined_signal(
                rl_signal, plutus_signal, market_context
            )
            
            # Apply sentiment adjustment to combined signal
            combined_signal = self._apply_sentiment_adjustment(
                combined_signal, sentiment_signal, sentiment_analysis
            )
            
            # Calculate position sizing and risk management
            position_info = self._calculate_position_sizing(
                combined_signal, market_context, symbol
            )
            
            # Create unified signal
            unified_signal = UnifiedSignal(
                symbol=symbol,
                timeframe=timeframe,
                timestamp=timestamp,
                
                # RL Components
                rl_action=rl_signal.get('action', 'hold'),
                rl_confidence=rl_signal.get('confidence', 0.5),
                rl_model_used=rl_signal.get('model_used', 'unknown'),
                
                # Plutus Components
                plutus_prediction=plutus_signal,
                plutus_confidence=plutus_signal.get('confidence', 0.5),
                plutus_trend=plutus_signal.get('trend', 'neutral'),
                
                # Sentiment Components - NEW
                sentiment_score=sentiment_analysis.get('overall_sentiment', 0.0),
                sentiment_label=sentiment_signal.get('action', 'neutral'),
                sentiment_confidence=sentiment_analysis.get('confidence', 0.0),
                market_sentiment=sentiment_analysis.get('market_sentiment', 0.0),
                news_impact=sentiment_analysis.get('impact_score', 0.0),
                
                # Combined Components
                final_action=combined_signal.get('final_action', 'hold'),
                final_confidence=combined_signal.get('combined_confidence', 0.5),
                combination_method='intelligent_weighting_with_sentiment',
                
                # Market Context
                market_regime=market_context.get('regime', 'unknown'),
                volatility_level=market_context.get('volatility_level', 'medium'),
                risk_score=market_context.get('risk_score', 0.5),
                
                # Additional Info
                reasoning=self._generate_enhanced_reasoning(
                    combined_signal, sentiment_signal, sentiment_analysis
                ),
                position_size=position_info.get('position_size', 0.01),
                stop_loss=position_info.get('stop_loss', 0.01),
                take_profit=position_info.get('take_profit', 0.02)
            )
            
            # Store signal in history
            self.signal_history.append(unified_signal)
            
            # Limit history size
            if len(self.signal_history) > 1000:
                self.signal_history = self.signal_history[-1000:]
            
            logger.info(f"📊 Generated unified signal for {symbol}: {unified_signal.final_action} (confidence: {unified_signal.final_confidence:.2f})")
            
            return unified_signal
            
        except Exception as e:
            logger.error(f"❌ Error generating unified signal for {symbol}: {str(e)}")
            
            # Return default signal
            return UnifiedSignal(
                symbol=symbol,
                timeframe=timeframe,
                timestamp=datetime.now(),
                rl_action='hold',
                rl_confidence=0.0,
                rl_model_used='error',
                plutus_prediction={},
                plutus_confidence=0.0,
                plutus_trend='neutral',
                sentiment_score=0.0,
                sentiment_label='neutral',
                sentiment_confidence=0.0,
                market_sentiment=0.0,
                news_impact=0.0,
                final_action='hold',
                final_confidence=0.0,
                combination_method='error_fallback',
                market_regime='unknown',
                volatility_level='medium',
                risk_score=0.5,
                reasoning=f"Error: {str(e)}",
                position_size=0.0,
                stop_loss=0.0,
                take_profit=0.0
            )
    
    def get_adaptive_signal(self, symbol: str, timeframe: str = "H1") -> Dict[str, Any]:
        """
        دریافت سیگنال تطبیقی - متد سازگار با نام‌های قدیمی
        این متد get_unified_signal را صدا می‌زند و نتیجه را به فرمت قدیمی تبدیل می‌کند
        """
        try:
            # Get unified signal
            unified_signal = self.get_unified_signal(symbol, timeframe)
            
            # Convert to old format for backward compatibility
            old_format_signal = {
                'action': unified_signal.final_action,
                'confidence': unified_signal.final_confidence,
                'trend': unified_signal.plutus_trend,
                'rl_action': unified_signal.rl_action,
                'rl_confidence': unified_signal.rl_confidence,
                'plutus_prediction': unified_signal.plutus_prediction,
                'plutus_confidence': unified_signal.plutus_confidence,
                'sentiment_score': unified_signal.sentiment_score,
                'sentiment_label': unified_signal.sentiment_label,
                'market_regime': unified_signal.market_regime,
                'volatility_level': unified_signal.volatility_level,
                'risk_score': unified_signal.risk_score,
                'reasoning': unified_signal.reasoning,
                'position_size': unified_signal.position_size,
                'stop_loss': unified_signal.stop_loss,
                'take_profit': unified_signal.take_profit,
                'timestamp': unified_signal.timestamp.isoformat()
            }
            
            return old_format_signal
            
        except Exception as e:
            logger.error(f"❌ Error in get_adaptive_signal for {symbol}: {str(e)}")
            
            # Return default signal in old format
            return {
                'action': 'hold',
                'confidence': 0.0,
                'trend': 'neutral',
                'rl_action': 'hold',
                'rl_confidence': 0.0,
                'plutus_prediction': {},
                'plutus_confidence': 0.0,
                'sentiment_score': 0.0,
                'sentiment_label': 'neutral',
                'market_regime': 'unknown',
                'volatility_level': 'medium',
                'risk_score': 0.5,
                'reasoning': 'Error occurred during signal generation',
                'position_size': 0.01,
                'stop_loss': 0.01,
                'take_profit': 0.02,
                'timestamp': datetime.now().isoformat()
            }
    
    def calculate_combined_signal(self, rl_signal: Dict[str, Any], plutus_signal: Dict[str, Any], market_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        محاسبه سیگنال ترکیبی - متد سازگار با نام‌های قدیمی
        این متد weighting_system.calculate_combined_signal را صدا می‌زند
        """
        try:
            # Use the weighting system to calculate combined signal
            combined_signal = self.weighting_system.calculate_combined_signal(
                rl_signal, plutus_signal, market_context
            )
            
            return combined_signal
            
        except Exception as e:
            logger.error(f"❌ Error in calculate_combined_signal: {str(e)}")
            
            # Return default combined signal
            return {
                'final_action': 'hold',
                'combined_confidence': 0.0,
                'rl_weight': 0.5,
                'plutus_weight': 0.5,
                'agreement': False,
                'reasoning': 'Error occurred during signal combination',
                'timestamp': datetime.now().isoformat()
            }
    
    def _load_market_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """بارگذاری داده‌های بازار"""
        
        try:
            # This would be replaced with actual data loading logic
            # For now, return empty DataFrame
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"Error loading market data for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    def _get_rl_signal(self, 
                      enhanced_features: Dict[str, Any], 
                      symbol: str, 
                      timeframe: str) -> Dict[str, Any]:
        """دریافت سیگنال RL با استفاده از ویژگی‌های تقویت‌شده"""
        
        try:
            if self.ensemble_model is None:
                # Use mock signal
                return {
                    'action': 'hold',
                    'confidence': 0.6,
                    'model_used': 'mock',
                    'features_used': list(enhanced_features.keys())
                }
            
            # Convert features to observation format
            observation = self._features_to_observation(enhanced_features)
            
            # Get prediction from ensemble
            action, _ = self.ensemble_model.predict(observation)
            
            # Calculate confidence (this would be model-specific)
            confidence = self._calculate_rl_confidence(action, enhanced_features)
            
            # Convert action to string
            action_str = self._action_to_string(action)
            
            return {
                'action': action_str,
                'confidence': confidence,
                'model_used': 'ensemble',
                'features_used': list(enhanced_features.keys())
            }
            
        except Exception as e:
            logger.error(f"Error getting RL signal: {str(e)}")
            return {
                'action': 'hold',
                'confidence': 0.5,
                'model_used': 'none',
                'error': str(e)
            }
    
    def _analyze_market_context(self, 
                              market_data: pd.DataFrame, 
                              plutus_signal: Dict[str, Any]) -> Dict[str, Any]:
        """تحلیل زمینه بازار"""
        
        try:
            context = {}
            
            if not market_data.empty:
                close_prices = market_data['close'].values
                
                # Volatility analysis
                returns = np.diff(close_prices) / close_prices[:-1]
                volatility = np.std(returns) * np.sqrt(24)
                
                if volatility > 0.02:
                    context['volatility_level'] = 'high'
                    context['volatility_regime'] = 2
                elif volatility > 0.01:
                    context['volatility_level'] = 'medium'
                    context['volatility_regime'] = 1
                else:
                    context['volatility_level'] = 'low'
                    context['volatility_regime'] = 0
                
                # Trend analysis
                if len(close_prices) >= 24:
                    trend = (close_prices[-1] - close_prices[-24]) / close_prices[-24]
                    if trend > 0.02:
                        context['regime'] = 'bullish'
                        context['trend_regime'] = 2
                    elif trend < -0.02:
                        context['regime'] = 'bearish'
                        context['trend_regime'] = 2
                    else:
                        context['regime'] = 'sideways'
                        context['trend_regime'] = 0
                else:
                    context['regime'] = 'unknown'
                    context['trend_regime'] = 1
                
                # Risk score
                risk_factors = [
                    context.get('volatility_regime', 1) / 2,
                    1 - plutus_signal.get('confidence', 0.5),
                    abs(trend) if 'trend' in locals() else 0.5
                ]
                context['risk_score'] = np.mean(risk_factors)
            
            return context
            
        except Exception as e:
            logger.error(f"Error analyzing market context: {str(e)}")
            return {
                'regime': 'unknown',
                'volatility_level': 'medium',
                'risk_score': 0.5,
                'volatility_regime': 1,
                'trend_regime': 1
            }
    
    def _calculate_position_sizing(self, 
                                 combined_signal: Dict[str, Any], 
                                 market_context: Dict[str, Any],
                                 symbol: str) -> Dict[str, Any]:
        """محاسبه اندازه موقعیت و مدیریت ریسک"""
        
        try:
            base_position_size = 0.02  # 2% base position
            confidence = combined_signal.get('combined_confidence', 0.5)
            risk_score = market_context.get('risk_score', 0.5)
            
            # Adjust position size based on confidence
            position_size = base_position_size * confidence
            
            # Adjust based on risk
            position_size *= (1 - risk_score * 0.5)
            
            # Ensure minimum and maximum limits
            position_size = max(0.005, min(0.05, position_size))
            
            # Calculate stop loss and take profit
            volatility = market_context.get('volatility_regime', 1)
            
            if volatility == 2:  # High volatility
                stop_loss = 0.015
                take_profit = 0.03
            elif volatility == 0:  # Low volatility
                stop_loss = 0.005
                take_profit = 0.015
            else:  # Medium volatility
                stop_loss = 0.01
                take_profit = 0.02
            
            return {
                'position_size': position_size,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_reward_ratio': take_profit / stop_loss
            }
            
        except Exception as e:
            logger.error(f"Error calculating position sizing: {str(e)}")
            return {
                'position_size': 0.01,
                'stop_loss': 0.01,
                'take_profit': 0.02,
                'risk_reward_ratio': 2.0
            }
    
    def _features_to_observation(self, features: Dict[str, Any]) -> np.ndarray:
        """تبدیل ویژگی‌ها به فرمت observation"""
        
        try:
            # Select numerical features
            numerical_features = []
            for key, value in features.items():
                if isinstance(value, (int, float)) and not np.isnan(value):
                    numerical_features.append(value)
            
            # Ensure we have enough features
            while len(numerical_features) < 10:
                numerical_features.append(0.0)
            
            return np.array(numerical_features[:20])  # Limit to 20 features
            
        except Exception as e:
            logger.error(f"Error converting features to observation: {str(e)}")
            return np.zeros(20)
    
    def _calculate_rl_confidence(self, action: np.ndarray, features: Dict[str, Any]) -> float:
        """محاسبه اطمینان سیگنال RL"""
        
        try:
            # This is a simplified confidence calculation
            # In practice, this would use model-specific methods
            
            base_confidence = 0.7
            
            # Adjust based on enhanced features
            if 'plutus_confidence' in features:
                plutus_conf = features['plutus_confidence']
                base_confidence = (base_confidence + plutus_conf) / 2
            
            # Add some noise to make it more realistic
            confidence = base_confidence + np.random.normal(0, 0.1)
            
            return max(0.1, min(0.95, confidence))
            
        except Exception as e:
            logger.error(f"Error calculating RL confidence: {str(e)}")
            return 0.5
    
    def _action_to_string(self, action: Union[int, np.ndarray]) -> str:
        """تبدیل action به string"""
        
        try:
            if isinstance(action, np.ndarray):
                action = action[0] if len(action) > 0 else 0
            
            if isinstance(action, (int, float)):
                if action > 0.5:
                    return 'buy'
                elif action < -0.5:
                    return 'sell'
                else:
                    return 'hold'
            
            return 'hold'
            
        except Exception as e:
            logger.error(f"Error converting action to string: {str(e)}")
            return 'hold'
    
    def update_performance(self, 
                          symbol: str, 
                          signal: UnifiedSignal,
                          actual_outcome: str,
                          profit_loss: float):
        """به‌روزرسانی عملکرد سیستم"""
        
        try:
            # Update weighting system
            performance_data = {
                'rl_performance': profit_loss if signal.rl_action == actual_outcome else -profit_loss,
                'plutus_performance': profit_loss if signal.plutus_trend == actual_outcome else -profit_loss
            }
            
            self.weighting_system.update_weights_from_performance(symbol, performance_data)
            
            # Update performance tracker
            if symbol not in self.performance_tracker:
                self.performance_tracker[symbol] = {
                    'total_trades': 0,
                    'winning_trades': 0,
                    'total_profit': 0,
                    'signals': []
                }
            
            tracker = self.performance_tracker[symbol]
            tracker['total_trades'] += 1
            tracker['total_profit'] += profit_loss
            
            if profit_loss > 0:
                tracker['winning_trades'] += 1
            
            tracker['signals'].append({
                'signal': signal,
                'outcome': actual_outcome,
                'profit_loss': profit_loss,
                'timestamp': datetime.now()
            })
            
            # Limit history
            if len(tracker['signals']) > 100:
                tracker['signals'] = tracker['signals'][-100:]
            
            logger.info(f"Updated performance for {symbol}: P&L={profit_loss:.4f}, Win Rate={tracker['winning_trades']/tracker['total_trades']:.2f}")
            
        except Exception as e:
            logger.error(f"Error updating performance: {str(e)}")
    
    def get_performance_summary(self, symbol: str = None) -> Dict[str, Any]:
        """دریافت خلاصه عملکرد"""
        
        try:
            if symbol:
                if symbol in self.performance_tracker:
                    tracker = self.performance_tracker[symbol]
                    return {
                        'symbol': symbol,
                        'total_trades': tracker['total_trades'],
                        'winning_trades': tracker['winning_trades'],
                        'win_rate': tracker['winning_trades'] / tracker['total_trades'] if tracker['total_trades'] > 0 else 0,
                        'total_profit': tracker['total_profit'],
                        'avg_profit_per_trade': tracker['total_profit'] / tracker['total_trades'] if tracker['total_trades'] > 0 else 0
                    }
                else:
                    return {'error': f'No performance data for {symbol}'}
            else:
                # Return overall summary
                summary = {
                    'total_symbols': len(self.performance_tracker),
                    'symbols': {}
                }
                
                for sym, tracker in self.performance_tracker.items():
                    summary['symbols'][sym] = {
                        'total_trades': tracker['total_trades'],
                        'win_rate': tracker['winning_trades'] / tracker['total_trades'] if tracker['total_trades'] > 0 else 0,
                        'total_profit': tracker['total_profit']
                    }
                
                return summary
                
        except Exception as e:
            logger.error(f"Error getting performance summary: {str(e)}")
            return {'error': str(e)}
    
    def save_system_state(self, filepath: str = None):
        """ذخیره وضعیت سیستم"""
        
        try:
            if filepath is None:
                filepath = f"unified_system_state_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            state = {
                'timestamp': datetime.now().isoformat(),
                'weights': self.weighting_system.weights,
                'performance_tracker': self.performance_tracker,
                'signal_history_count': len(self.signal_history),
                'system_config': {
                    'config_path': self.config_path,
                    'db_path': self.db_path
                }
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"System state saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving system state: {str(e)}")
    
    def stop_system(self):
        """توقف سیستم"""
        
        try:
            self.is_running = False
            
            # Stop adaptive Plutus system
            if hasattr(self.adaptive_plutus, 'stop_continuous_learning'):
                self.adaptive_plutus.stop_continuous_learning()
            
            # Save final state
            self.save_system_state()
            
            logger.info("Unified Trading System stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping system: {str(e)}")
    
    def __del__(self):
        """Destructor"""
        if hasattr(self, 'is_running') and self.is_running:
            self.stop_system() 

class SentimentIntegrator:
    """ادغام‌کننده تحلیل احساسات"""
    
    def __init__(self, languages: List[str] = None):
        self.languages = languages or ['en', 'fa']
        self.sentiment_analyzer = None
        self.news_cache = {}
        self.sentiment_weights = {
            'financial_news': 0.4,
            'social_media': 0.3,
            'analyst_reports': 0.2,
            'regulatory_news': 0.1
        }
        self.initialize_sentiment_analyzer()
    
    def initialize_sentiment_analyzer(self):
        """مقداردهی اولیه تحلیل‌گر احساسات"""
        try:
            if SENTIMENT_AVAILABLE:
                self.sentiment_analyzer = AdvancedSentimentAnalyzer(
                    languages=self.languages,
                    enable_cache=True
                )
                logger.info("✅ Sentiment analyzer initialized")
            else:
                logger.warning("⚠️ Sentiment analyzer not available, using mock")
                self.sentiment_analyzer = MockSentimentAnalyzer()
        except Exception as e:
            logger.error(f"❌ Failed to initialize sentiment analyzer: {e}")
            self.sentiment_analyzer = MockSentimentAnalyzer()
    
    def analyze_market_sentiment(self, symbol: str, 
                                news_texts: List[str] = None,
                                social_media_texts: List[str] = None) -> Dict[str, Any]:
        """تحلیل احساسات بازار"""
        try:
            overall_sentiment = 0.0
            confidence = 0.0
            impact_score = 0.0
            
            # Sample news for testing if none provided
            if not news_texts:
                news_texts = [
                    f"{symbol} shows strong performance in recent trading",
                    f"Market analysts are optimistic about {symbol} outlook",
                    f"Technical indicators suggest {symbol} upward trend"
                ]
            
            # Analyze news sentiment
            news_sentiments = []
            for text in news_texts:
                result = self.sentiment_analyzer.analyze(text, source='financial_news')
                if result:
                    news_sentiments.append({
                        'text': text,
                        'sentiment': result.score if result.label == 'positive' else -result.score,
                        'confidence': result.confidence,
                        'impact': getattr(result, 'market_impact', 0.0)
                    })
            
            # Calculate weighted sentiment
            if news_sentiments:
                total_weight = sum(s['confidence'] for s in news_sentiments)
                if total_weight > 0:
                    overall_sentiment = sum(
                        s['sentiment'] * s['confidence'] for s in news_sentiments
                    ) / total_weight
                    confidence = total_weight / len(news_sentiments)
                    impact_score = sum(s['impact'] for s in news_sentiments) / len(news_sentiments)
            
            # Get market sentiment
            market_sentiment = self.sentiment_analyzer.get_market_sentiment()
            if isinstance(market_sentiment, dict):
                market_overall = market_sentiment.get('overall_sentiment', 0.0)
                market_confidence = market_sentiment.get('confidence', 0.0)
            else:
                market_overall = getattr(market_sentiment, 'overall_sentiment', 0.0)
                market_confidence = getattr(market_sentiment, 'confidence', 0.0)
            
            # Combine individual and market sentiment
            combined_sentiment = (overall_sentiment * 0.6) + (market_overall * 0.4)
            combined_confidence = (confidence * 0.6) + (market_confidence * 0.4)
            
            return {
                'overall_sentiment': combined_sentiment,
                'confidence': combined_confidence,
                'impact_score': impact_score,
                'market_sentiment': market_overall,
                'individual_sentiment': overall_sentiment,
                'news_count': len(news_texts),
                'sentiment_breakdown': news_sentiments
            }
            
        except Exception as e:
            logger.error(f"Error analyzing market sentiment: {e}")
            return {
                'overall_sentiment': 0.0,
                'confidence': 0.0,
                'impact_score': 0.0,
                'market_sentiment': 0.0,
                'individual_sentiment': 0.0,
                'news_count': 0,
                'sentiment_breakdown': []
            }
    
    def sentiment_to_trading_signal(self, sentiment_data: Dict[str, Any]) -> Dict[str, Any]:
        """تبدیل احساسات به سیگنال معاملاتی"""
        sentiment_score = sentiment_data.get('overall_sentiment', 0.0)
        confidence = sentiment_data.get('confidence', 0.0)
        impact = sentiment_data.get('impact_score', 0.0)
        
        # Determine action based on sentiment
        if sentiment_score > 0.3 and confidence > 0.6:
            action = 'buy'
            signal_strength = min(sentiment_score * confidence, 1.0)
        elif sentiment_score < -0.3 and confidence > 0.6:
            action = 'sell'
            signal_strength = min(abs(sentiment_score) * confidence, 1.0)
        else:
            action = 'hold'
            signal_strength = 0.5
        
        # Adjust for market impact
        signal_strength = signal_strength * (1 + impact * 0.2)
        signal_strength = min(signal_strength, 1.0)
        
        return {
            'action': action,
            'strength': signal_strength,
            'sentiment_score': sentiment_score,
            'confidence': confidence,
            'impact': impact,
            'reasoning': f"Sentiment: {sentiment_score:.2f}, Confidence: {confidence:.2f}, Impact: {impact:.2f}"
        } 

    def _apply_sentiment_adjustment(self, 
                                  combined_signal: Dict[str, Any],
                                  sentiment_signal: Dict[str, Any],
                                  sentiment_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """اعمال تعدیل احساسات به سیگنال ترکیبی"""
        
        try:
            # Get sentiment strength
            sentiment_strength = sentiment_signal.get('strength', 0.0)
            sentiment_action = sentiment_signal.get('action', 'hold')
            
            # Original signal
            original_action = combined_signal.get('final_action', 'hold')
            original_confidence = combined_signal.get('combined_confidence', 0.5)
            
            # Sentiment weight (can be adjusted based on market conditions)
            sentiment_weight = min(sentiment_strength * 0.3, 0.2)  # Max 20% influence
            
            # Calculate adjusted confidence
            if sentiment_action == original_action:
                # Sentiment reinforces the signal
                adjusted_confidence = original_confidence + (sentiment_strength * sentiment_weight)
            else:
                # Sentiment conflicts with the signal
                adjusted_confidence = original_confidence - (sentiment_strength * sentiment_weight * 0.5)
            
            # Ensure confidence stays within bounds
            adjusted_confidence = max(0.0, min(1.0, adjusted_confidence))
            
            # Determine final action based on adjusted confidence
            if adjusted_confidence < 0.4:
                final_action = 'hold'
            elif sentiment_action != 'hold' and sentiment_strength > 0.6:
                # Strong sentiment can override weak technical signals
                final_action = sentiment_action
            else:
                final_action = original_action
            
            # Update combined signal
            combined_signal.update({
                'final_action': final_action,
                'combined_confidence': adjusted_confidence,
                'sentiment_adjustment': sentiment_weight,
                'sentiment_reinforcement': sentiment_action == original_action
            })
            
            return combined_signal
            
        except Exception as e:
            logger.error(f"Error applying sentiment adjustment: {e}")
            return combined_signal
    
    def _generate_enhanced_reasoning(self, 
                                   combined_signal: Dict[str, Any],
                                   sentiment_signal: Dict[str, Any],
                                   sentiment_analysis: Dict[str, Any]) -> str:
        """تولید توضیحات تفصیلی برای سیگنال"""
        
        try:
            original_reasoning = combined_signal.get('reasoning', '')
            sentiment_score = sentiment_analysis.get('overall_sentiment', 0.0)
            sentiment_confidence = sentiment_analysis.get('confidence', 0.0)
            news_count = sentiment_analysis.get('news_count', 0)
            
            sentiment_part = f"Sentiment: {sentiment_score:.2f} (conf: {sentiment_confidence:.2f}, news: {news_count})"
            
            if sentiment_analysis.get('sentiment_reinforcement', False):
                sentiment_impact = "reinforces"
            else:
                sentiment_impact = "conflicts with"
            
            sentiment_reasoning = f"{sentiment_part} {sentiment_impact} technical signals"
            
            return f"{original_reasoning} | {sentiment_reasoning}"
            
        except Exception as e:
            logger.error(f"Error generating enhanced reasoning: {e}")
            return combined_signal.get('reasoning', 'Error in reasoning generation') 