"""
🔥 Pearl-3x7B Custom Data Trainer
مربی مخصوص دیتاهای شما در Google Colab

استفاده از:
- دیتاهای شما در /content/drive/MyDrive/project2/data_new
- اندیکاتورهای پیشرفته موجود در پروژه
"""

import os
import sys
import time
import json
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, f1_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

# Mount Google Drive
try:
    from google.colab import drive
    drive.mount('/content/drive')
    print("✅ Google Drive mounted")
except:
    print("⚠️ Not in Colab or Drive already mounted")

class CustomDataLoader:
    """📊 بارگذار دیتاهای شما"""
    
    def __init__(self, data_path: str = "/content/drive/MyDrive/project2/data_new"):
        self.data_path = data_path
        self.indicators_loaded = False
        self._load_indicators()
    
    def _load_indicators(self):
        """بارگذاری اندیکاتورهای پیشرفته از پروژه"""
        try:
            # Add project path to sys.path
            if '/content/drive/MyDrive/project2' not in sys.path:
                sys.path.append('/content/drive/MyDrive/project2')
            
            # Import your advanced indicators
            from utils.technical_indicators import TechnicalIndicators
            from utils.advanced_technical_indicators import AdvancedTechnicalIndicators
            
            self.tech_indicators = TechnicalIndicators()
            self.advanced_indicators = AdvancedTechnicalIndicators()
            self.indicators_loaded = True
            
            print("✅ Advanced indicators loaded from your project")
            
        except ImportError as e:
            print(f"⚠️ Could not load indicators: {e}")
            print("   Will use basic indicators instead")
            self.indicators_loaded = False
    
    def load_your_data(self) -> Dict[str, pd.DataFrame]:
        """بارگذاری دیتاهای شما"""
        print(f"📊 Loading your data from: {self.data_path}")
        
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(f"Data path not found: {self.data_path}")
        
        data_files = {}
        
        # List all files in your data directory
        for file in os.listdir(self.data_path):
            if file.endswith(('.csv', '.pkl', '.parquet', '.json')):
                file_path = os.path.join(self.data_path, file)
                
                try:
                    # Load based on file extension
                    if file.endswith('.csv'):
                        df = pd.read_csv(file_path)
                    elif file.endswith('.pkl'):
                        df = pd.read_pickle(file_path)
                    elif file.endswith('.parquet'):
                        df = pd.read_parquet(file_path)
                    elif file.endswith('.json'):
                        df = pd.read_json(file_path)
                    
                    # Basic data validation
                    if len(df) > 0:
                        data_files[file] = df
                        print(f"   ✅ {file}: {len(df)} records, {len(df.columns)} columns")
                    else:
                        print(f"   ⚠️ {file}: Empty file")
                        
                except Exception as e:
                    print(f"   ❌ {file}: Error loading - {e}")
        
        if not data_files:
            raise ValueError("No valid data files found")
        
        print(f"📊 Total files loaded: {len(data_files)}")
        return data_files
    
    def add_advanced_indicators(self, df: pd.DataFrame, symbol: str = "data") -> pd.DataFrame:
        """اضافه کردن اندیکاتورهای پیشرفته شما"""
        print(f"🔧 Adding advanced indicators to {symbol}...")
        
        if not self.indicators_loaded:
            print("   ⚠️ Advanced indicators not available, using basic ones")
            return self._add_basic_indicators(df)
        
        try:
            # Make a copy to avoid modifying original
            enhanced_df = df.copy()
            
            # Ensure we have required columns
            required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
            missing_cols = [col for col in required_cols if col not in enhanced_df.columns]
            
            if missing_cols:
                print(f"   ⚠️ Missing columns: {missing_cols}")
                # Try to map common column names
                column_mapping = {
                    'open': 'Open', 'high': 'High', 'low': 'Low', 
                    'close': 'Close', 'volume': 'Volume',
                    'price': 'Close', 'value': 'Close'
                }
                
                for old_col, new_col in column_mapping.items():
                    if old_col in enhanced_df.columns and new_col not in enhanced_df.columns:
                        enhanced_df[new_col] = enhanced_df[old_col]
            
            # Add your technical indicators
            if all(col in enhanced_df.columns for col in ['High', 'Low', 'Close']):
                # Basic indicators
                enhanced_df = self.tech_indicators.add_all_indicators(enhanced_df)
                
                # Advanced indicators from your project
                enhanced_df = self.advanced_indicators.add_advanced_indicators(enhanced_df)
                
                print(f"   ✅ Added advanced indicators: {len(enhanced_df.columns) - len(df.columns)} new features")
            else:
                print("   ⚠️ Cannot add indicators - missing OHLC data")
            
            return enhanced_df
            
        except Exception as e:
            print(f"   ❌ Error adding indicators: {e}")
            return df
    
    def _add_basic_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """اضافه کردن اندیکاتورهای پایه"""
        enhanced_df = df.copy()
        
        if 'Close' in enhanced_df.columns:
            # Simple moving averages
            enhanced_df['SMA_10'] = enhanced_df['Close'].rolling(10).mean()
            enhanced_df['SMA_20'] = enhanced_df['Close'].rolling(20).mean()
            enhanced_df['SMA_50'] = enhanced_df['Close'].rolling(50).mean()
            
            # Returns
            enhanced_df['Returns'] = enhanced_df['Close'].pct_change()
            enhanced_df['Log_Returns'] = np.log(enhanced_df['Close'] / enhanced_df['Close'].shift(1))
            
            # Volatility
            enhanced_df['Volatility'] = enhanced_df['Returns'].rolling(20).std()
            
            # RSI
            delta = enhanced_df['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            enhanced_df['RSI'] = 100 - (100 / (1 + rs))
            
            print(f"   ✅ Added basic indicators: {len(enhanced_df.columns) - len(df.columns)} new features")
        
        return enhanced_df
    
    def prepare_sentiment_data(self, data_files: Dict[str, pd.DataFrame]) -> Optional[pd.DataFrame]:
        """آماده‌سازی داده‌های احساسات"""
        print("💭 Preparing sentiment data...")
        
        # Look for text/news data in your files
        sentiment_data = []
        
        for filename, df in data_files.items():
            # Check for text columns
            text_columns = []
            for col in df.columns:
                if any(keyword in col.lower() for keyword in ['text', 'news', 'title', 'content', 'message']):
                    text_columns.append(col)
            
            if text_columns:
                print(f"   📰 Found text data in {filename}: {text_columns}")
                
                for _, row in df.iterrows():
                    for text_col in text_columns:
                        text = str(row[text_col])
                        if len(text) > 10:  # Filter out very short texts
                            sentiment_data.append({
                                'text': text,
                                'source': filename,
                                'timestamp': row.get('timestamp', datetime.now())
                            })
        
        if sentiment_data:
            sentiment_df = pd.DataFrame(sentiment_data)
            print(f"   ✅ Prepared {len(sentiment_df)} sentiment samples")
            return sentiment_df
        else:
            print("   ⚠️ No text data found for sentiment analysis")
            return None
    
    def prepare_timeseries_data(self, data_files: Dict[str, pd.DataFrame]) -> Optional[pd.DataFrame]:
        """آماده‌سازی داده‌های سری زمانی"""
        print("📈 Preparing time series data...")
        
        # Find the best file for time series (most records with price data)
        best_file = None
        best_score = 0
        
        for filename, df in data_files.items():
            score = 0
            
            # Check for price columns
            price_cols = [col for col in df.columns if any(keyword in col.lower() 
                         for keyword in ['price', 'close', 'value', 'amount'])]
            score += len(price_cols) * 10
            
            # Check for time columns
            time_cols = [col for col in df.columns if any(keyword in col.lower() 
                        for keyword in ['time', 'date', 'timestamp'])]
            score += len(time_cols) * 5
            
            # Prefer larger datasets
            score += min(len(df) / 1000, 10)
            
            if score > best_score:
                best_score = score
                best_file = filename
        
        if best_file:
            df = data_files[best_file]
            print(f"   📊 Selected {best_file} for time series: {len(df)} records")
            
            # Add your advanced indicators
            enhanced_df = self.add_advanced_indicators(df, best_file)
            
            return enhanced_df
        else:
            print("   ⚠️ No suitable time series data found")
            return None
    
    def prepare_trading_environment_data(self, data_files: Dict[str, pd.DataFrame]) -> Optional[Dict[str, Any]]:
        """آماده‌سازی داده‌های محیط معاملاتی"""
        print("🤖 Preparing trading environment data...")
        
        timeseries_df = self.prepare_timeseries_data(data_files)
        
        if timeseries_df is None:
            return None
        
        # Find price column
        price_col = None
        for col in timeseries_df.columns:
            if any(keyword in col.lower() for keyword in ['close', 'price', 'value']):
                price_col = col
                break
        
        if price_col is None:
            print("   ❌ No price column found")
            return None
        
        # Prepare features and targets
        feature_cols = []
        for col in timeseries_df.columns:
            if col != price_col and timeseries_df[col].dtype in ['float64', 'int64']:
                feature_cols.append(col)
        
        if len(feature_cols) < 3:
            print("   ⚠️ Not enough features for RL environment")
            return None
        
        # Clean data
        clean_df = timeseries_df[feature_cols + [price_col]].dropna()
        
        if len(clean_df) < 100:
            print("   ⚠️ Not enough clean data for RL training")
            return None
        
        # Create features, rewards, and actions
        features = []
        rewards = []
        actions = []
        
        lookback = 20  # Use last 20 periods for state
        
        for i in range(lookback, len(clean_df) - 1):
            # State: normalized features from last lookback periods
            state_data = clean_df.iloc[i-lookback:i][feature_cols]
            
            # Normalize features
            state_normalized = (state_data - state_data.mean()) / (state_data.std() + 1e-8)
            features.append(state_normalized.values.flatten())
            
            # Reward: next period return
            current_price = clean_df[price_col].iloc[i]
            next_price = clean_df[price_col].iloc[i + 1]
            reward = (next_price - current_price) / current_price
            rewards.append(reward)
            
            # Action: based on price movement
            if reward > 0.002:  # Buy threshold
                action = 1
            elif reward < -0.002:  # Sell threshold
                action = 2
            else:  # Hold
                action = 0
            
            actions.append(action)
        
        trading_data = {
            'features': np.array(features),
            'rewards': np.array(rewards),
            'actions': np.array(actions),
            'feature_names': feature_cols,
            'price_column': price_col
        }
        
        print(f"   ✅ Created {len(features)} trading samples with {len(feature_cols)} features")
        return trading_data

def load_and_prepare_your_data() -> Dict[str, Any]:
    """بارگذاری و آماده‌سازی کامل دیتاهای شما"""
    print("🔥 LOADING YOUR CUSTOM DATA")
    print("=" * 50)
    
    loader = CustomDataLoader()
    
    # Load your data files
    data_files = loader.load_your_data()
    
    # Prepare different types of data
    results = {
        'raw_data': data_files,
        'sentiment_data': loader.prepare_sentiment_data(data_files),
        'timeseries_data': loader.prepare_timeseries_data(data_files),
        'trading_data': loader.prepare_trading_environment_data(data_files)
    }
    
    # Summary
    print(f"\n📊 DATA PREPARATION SUMMARY:")
    print(f"   📁 Raw files: {len(data_files)}")
    print(f"   💭 Sentiment data: {'✅' if results['sentiment_data'] is not None else '❌'}")
    print(f"   📈 Time series data: {'✅' if results['timeseries_data'] is not None else '❌'}")
    print(f"   🤖 Trading data: {'✅' if results['trading_data'] is not None else '❌'}")
    
    return results

# Main execution
if __name__ == "__main__":
    try:
        import google.colab
        print("🚀 Running in Google Colab")
        print("Ready to load your custom data!")
        print("Execute: load_and_prepare_your_data()")
    except ImportError:
        print("⚠️ This script is designed for Google Colab")
        load_and_prepare_your_data()
