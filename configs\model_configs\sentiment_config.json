{"model_type": "ensemble", "languages": ["en", "fa"], "batch_size": 32, "epochs": 10, "learning_rate": 2e-05, "max_length": 512, "validation_split": 0.2, "test_split": 0.1, "save_path": "models/trained_models/sentiment_model.pkl", "metrics": ["accuracy", "f1", "precision", "recall"], "models": {"finbert": {"model_name": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis", "max_length": 512, "batch_size": 16}, "cryptobert": {"model_name": "ElKulako/cryptobert", "max_length": 256, "batch_size": 32}, "persian_bert": {"model_name": "HooshvareLab/bert-fa-base-uncased", "max_length": 512, "batch_size": 16}}, "ensemble_weights": {"finbert": 0.4, "cryptobert": 0.3, "persian_bert": 0.3}, "preprocessing": {"remove_urls": true, "remove_emails": true, "remove_numbers": false, "lowercase": true, "remove_stopwords": false}}