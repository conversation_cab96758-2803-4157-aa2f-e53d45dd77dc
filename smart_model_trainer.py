"""
🧠 Smart Model Trainer with Pearl Brain
مربی هوشمند مدل‌ها با مغز Pearl

این سیستم شامل:
1. آموزش هوشمند مدل‌ها
2. تصمیم‌گیری با مغز متفکر
3. بهینه‌سازی منابع
4. گزارش‌دهی پیشرفته
"""

import os
import sys
import time
import json
from datetime import datetime
from typing import Dict, List, Any
from dataclasses import dataclass

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

@dataclass
class ModelTrainingInfo:
    """اطلاعات آموزش مدل"""
    name: str
    category: str
    priority: int  # 1=highest
    status: str
    estimated_time_min: int
    memory_mb: int
    dependencies: List[str]

class SmartBrain:
    """🧠 مغز هوشمند برای تصمیم‌گیری"""
    
    def __init__(self):
        self.decisions = []
        self.performance_history = []
        self.confidence_threshold = 0.7
        
    def decide_next_training(self, available_models: List[ModelTrainingInfo], 
                           available_memory: int, running_count: int) -> Dict[str, Any]:
        """تصمیم‌گیری برای آموزش بعدی"""
        
        if not available_models:
            return {
                "action": "wait",
                "reasoning": "هیچ مدل آماده آموزش موجود نیست",
                "confidence": 1.0
            }
        
        # فیلتر مدل‌های قابل آموزش بر اساس حافظه
        suitable_models = [m for m in available_models if m.memory_mb <= available_memory * 0.8]
        
        if not suitable_models:
            return {
                "action": "wait_memory",
                "reasoning": "حافظه کافی برای آموزش موجود نیست",
                "confidence": 0.9
            }
        
        # انتخاب بهترین مدل بر اساس اولویت
        best_model = min(suitable_models, key=lambda x: (x.priority, x.estimated_time_min))
        
        return {
            "action": "train",
            "model": best_model,
            "reasoning": f"انتخاب {best_model.name} بر اساس اولویت {best_model.priority}",
            "confidence": 0.8
        }
    
    def learn_from_result(self, model_name: str, success: bool, actual_time: int):
        """یادگیری از نتایج"""
        self.performance_history.append({
            "model": model_name,
            "success": success,
            "time": actual_time,
            "timestamp": datetime.now()
        })

class SmartModelTrainer:
    """🎯 مربی هوشمند مدل‌ها"""
    
    def __init__(self):
        self.brain = SmartBrain()
        self.models_to_train = self._get_all_models()
        self.completed_models = []
        self.failed_models = []
        self.max_memory_mb = 8000  # 8GB
        self.max_concurrent = 2
        
    def _get_all_models(self) -> List[ModelTrainingInfo]:
        """دریافت لیست تمام مدل‌ها"""
        return [
            # CRITICAL Models
            ModelTrainingInfo("FinBERT", "sentiment", 1, "not_trained", 45, 2048, ["transformers", "torch"]),
            ModelTrainingInfo("LSTM_TimeSeries", "timeseries", 1, "not_trained", 30, 1024, ["torch", "numpy"]),
            ModelTrainingInfo("DQN_Agent", "rl", 1, "partially_trained", 60, 1200, ["torch", "gym"]),
            ModelTrainingInfo("PPO_Agent", "rl", 1, "not_trained", 55, 1100, ["torch", "gym"]),
            ModelTrainingInfo("EnhancedDQNAgent", "rl", 1, "partially_trained", 80, 1500, ["torch", "gym"]),
            
            # HIGH Priority Models
            ModelTrainingInfo("CryptoBERT", "sentiment", 2, "not_trained", 40, 1800, ["transformers", "torch"]),
            ModelTrainingInfo("GRU_TimeSeries", "timeseries", 2, "not_trained", 25, 900, ["torch", "numpy"]),
            ModelTrainingInfo("A2C_Agent", "rl", 2, "not_trained", 45, 1000, ["torch", "gym"]),
            ModelTrainingInfo("TD3_Agent", "rl", 2, "not_trained", 70, 1300, ["torch", "gym"]),
            ModelTrainingInfo("Transformer_TimeSeries", "timeseries", 2, "not_trained", 50, 2048, ["torch"]),
            
            # MEDIUM Priority Models
            ModelTrainingInfo("FinancialSentimentModel", "sentiment", 3, "not_trained", 60, 3000, ["transformers"]),
            ModelTrainingInfo("ChronosModel", "timeseries", 3, "not_trained", 20, 1500, ["chronos", "torch"]),
            ModelTrainingInfo("TimeSeriesEnsemble", "timeseries", 3, "not_trained", 80, 3500, ["torch", "sklearn"]),
            ModelTrainingInfo("ModelEnsemble", "ensemble", 3, "not_trained", 40, 1500, ["sklearn", "numpy"]),
            ModelTrainingInfo("WeightedEnsemble", "ensemble", 3, "not_trained", 25, 800, ["sklearn"]),
            
            # LOW Priority Models
            ModelTrainingInfo("SentimentEnsemble", "sentiment", 4, "not_trained", 90, 4000, ["transformers"]),
            ModelTrainingInfo("VotingEnsemble", "ensemble", 4, "not_trained", 20, 600, ["sklearn"]),
            ModelTrainingInfo("AIAgent", "agent", 4, "not_trained", 90, 2000, ["torch", "transformers"]),
            
            # RESEARCH Models
            ModelTrainingInfo("HierarchicalRL", "advanced_rl", 5, "not_trained", 90, 2000, ["torch", "gym"]),
            ModelTrainingInfo("MetaLearner", "meta_learning", 5, "not_trained", 120, 2500, ["torch", "higher"]),
            ModelTrainingInfo("ZeroShotLearning", "zero_shot", 5, "not_trained", 80, 1800, ["torch", "clip"]),
        ]
    
    def simulate_training(self, model: ModelTrainingInfo) -> Dict[str, Any]:
        """شبیه‌سازی آموزش مدل"""
        print(f"🔄 Training {model.name} ({model.category})...")
        print(f"   Priority: {model.priority} | Memory: {model.memory_mb}MB | Time: {model.estimated_time_min}min")
        
        # شبیه‌سازی زمان آموزش (کاهش یافته برای تست)
        actual_time = min(model.estimated_time_min, 10)  # حداکثر 10 ثانیه
        time.sleep(actual_time)
        
        # شبیه‌سازی نتیجه (90% احتمال موفقیت)
        import random
        success = random.random() > 0.1
        
        if success:
            accuracy = random.uniform(0.75, 0.95)
            result = {
                "success": True,
                "accuracy": accuracy,
                "training_time": actual_time,
                "model_name": model.name,
                "category": model.category
            }
            print(f"✅ {model.name} trained successfully! Accuracy: {accuracy:.3f}")
        else:
            result = {
                "success": False,
                "error": "Training failed due to convergence issues",
                "training_time": actual_time,
                "model_name": model.name,
                "category": model.category
            }
            print(f"❌ {model.name} training failed!")
        
        return result
    
    def run_smart_training(self):
        """اجرای آموزش هوشمند"""
        print("🧠 SMART MODEL TRAINING WITH PEARL BRAIN")
        print("=" * 60)
        
        start_time = datetime.now()
        brain_decisions = 0
        
        # آموزش تا زمانی که مدل باقی مانده باشد
        while self.models_to_train:
            # محاسبه منابع موجود
            available_memory = self.max_memory_mb
            running_count = 0  # در این شبیه‌سازی همه sequential هستند
            
            # تصمیم‌گیری توسط مغز
            decision = self.brain.decide_next_training(
                self.models_to_train, 
                available_memory, 
                running_count
            )
            
            brain_decisions += 1
            
            print(f"\n🧠 Brain Decision #{brain_decisions}:")
            print(f"   Action: {decision['action']}")
            print(f"   Reasoning: {decision['reasoning']}")
            print(f"   Confidence: {decision['confidence']:.2f}")
            
            if decision["action"] == "train":
                model = decision["model"]
                
                # آموزش مدل
                result = self.simulate_training(model)
                
                # حذف از لیست انتظار
                self.models_to_train.remove(model)
                
                # اضافه کردن به نتایج
                if result["success"]:
                    self.completed_models.append(result)
                else:
                    self.failed_models.append(result)
                
                # یادگیری مغز
                self.brain.learn_from_result(
                    model.name, 
                    result["success"], 
                    result["training_time"]
                )
                
            elif decision["action"] == "wait":
                print("⏳ Waiting for more models...")
                break
            elif decision["action"] == "wait_memory":
                print("💾 Waiting for memory to free up...")
                time.sleep(5)
        
        # خلاصه نهایی
        self._print_final_summary(start_time, brain_decisions)
    
    def _print_final_summary(self, start_time: datetime, brain_decisions: int):
        """چاپ خلاصه نهایی"""
        total_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\n🎉 SMART TRAINING COMPLETED!")
        print("=" * 60)
        print(f"⏱️ Total Time: {total_time:.1f}s")
        print(f"✅ Successful Models: {len(self.completed_models)}")
        print(f"❌ Failed Models: {len(self.failed_models)}")
        print(f"⏳ Remaining Models: {len(self.models_to_train)}")
        print(f"🧠 Brain Decisions: {brain_decisions}")
        
        if self.completed_models:
            avg_accuracy = sum(m["accuracy"] for m in self.completed_models) / len(self.completed_models)
            print(f"📊 Average Accuracy: {avg_accuracy:.3f}")
        
        success_rate = len(self.completed_models) / (len(self.completed_models) + len(self.failed_models)) * 100
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        # نمایش مدل‌های موفق بر اساس دسته
        print(f"\n📂 Successful Models by Category:")
        categories = {}
        for model in self.completed_models:
            cat = model["category"]
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(model["model_name"])
        
        for category, models in categories.items():
            print(f"   {category}: {', '.join(models)}")
        
        # ذخیره نتایج
        self._save_results()
    
    def _save_results(self):
        """ذخیره نتایج"""
        results = {
            "timestamp": datetime.now().isoformat(),
            "completed_models": self.completed_models,
            "failed_models": self.failed_models,
            "remaining_models": [
                {
                    "name": m.name,
                    "category": m.category,
                    "priority": m.priority
                } for m in self.models_to_train
            ],
            "brain_performance": self.brain.performance_history
        }
        
        filename = f"smart_training_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, default=str, ensure_ascii=False)
        
        print(f"💾 Results saved to: {filename}")

def main():
    """اجرای مربی هوشمند"""
    try:
        trainer = SmartModelTrainer()
        trainer.run_smart_training()
        
        print(f"\n🧠 Pearl Brain has successfully guided the training of {len(trainer.completed_models)} models!")
        
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
