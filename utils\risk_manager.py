import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Union
import datetime


class RiskManager:
    """
    مدیریت ریسک پیشرفته برای کنترل درادون و مدیریت سرمایه
    
    این کلاس مسئول مدیریت ریسک معاملات است و از حد مجاز درادون روزانه و کلی
    اطمینان حاصل می‌کند. همچنین حجم معاملات را بر اساس سرمایه و ریسک تنظیم می‌کند.
    """
    
    def __init__(self, 
                 initial_balance: float = 1000.0,
                 max_daily_drawdown: float = 0.04,  # 4%
                 max_total_drawdown: float = 0.10,  # 10%
                 risk_per_trade: float = 0.01,      # 1%
                 max_open_positions: int = 5,
                 max_daily_trades: int = 10):
        """
        مقداردهی اولیه RiskManager
        
        پارامترها:
        -----------
        initial_balance : float, default=1000.0
            موجودی اولیه حساب (دلار)
        max_daily_drawdown : float, default=0.04
            حداکثر درادون روزانه مجاز (4%)
        max_total_drawdown : float, default=0.10
            حداکثر درادون کلی مجاز (10%)
        risk_per_trade : float, default=0.01
            درصد ریسک برای هر معامله (1%)
        max_open_positions : int, default=5
            حداکثر تعداد پوزیشن‌های باز همزمان
        max_daily_trades : int, default=10
            حداکثر تعداد معاملات روزانه
        """
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.max_daily_drawdown = max_daily_drawdown
        self.max_total_drawdown = max_total_drawdown
        self.risk_per_trade = risk_per_trade
        self.max_open_positions = max_open_positions
        self.max_daily_trades = max_daily_trades
        
        # ردیابی معاملات و درادون
        self.highest_balance = initial_balance
        self.daily_trades = {}
        self.open_positions = {}
        self.trade_history = []
        self.daily_drawdowns = {}
        
        # آمار و تحلیل ریسک
        self.risk_metrics = {
            'daily_drawdown': 0.0,
            'total_drawdown': 0.0,
            'volatility': 0.0,
            'sharpe_ratio': 0.0,
            'win_rate': 0.0,
            'risk_reward_ratio': 0.0
        }
        
    def update_balance(self, new_balance: float, timestamp: datetime.datetime = None):
        """
        به‌روزرسانی موجودی حساب و محاسبه درادون
        
        پارامترها:
        -----------
        new_balance : float
            موجودی جدید حساب
        timestamp : datetime.datetime, optional
            زمان به‌روزرسانی (اگر None باشد، زمان فعلی استفاده می‌شود)
        
        خروجی:
        -------
        Dict[str, Any]
            وضعیت ریسک پس از به‌روزرسانی
        """
        if timestamp is None:
            timestamp = datetime.datetime.now()
        
        date_str = timestamp.strftime('%Y-%m-%d')
        
        # به‌روزرسانی موجودی
        old_balance = self.current_balance
        self.current_balance = new_balance
        
        # به‌روزرسانی بالاترین موجودی
        if new_balance > self.highest_balance:
            self.highest_balance = new_balance
        
        # محاسبه درادون کلی
        total_drawdown = (self.highest_balance - new_balance) / self.highest_balance
        self.risk_metrics['total_drawdown'] = total_drawdown
        
        # محاسبه درادون روزانه
        if date_str not in self.daily_drawdowns:
            self.daily_drawdowns[date_str] = {
                'high': new_balance,
                'low': new_balance,
                'drawdown': 0.0
            }
        
        daily_data = self.daily_drawdowns[date_str]
        if new_balance > daily_data['high']:
            daily_data['high'] = new_balance
        if new_balance < daily_data['low']:
            daily_data['low'] = new_balance
        
        daily_drawdown = (daily_data['high'] - daily_data['low']) / daily_data['high']
        self.risk_metrics['daily_drawdown'] = daily_drawdown
        self.daily_drawdowns[date_str]['drawdown'] = daily_drawdown
        
        # ثبت تغییر موجودی در تاریخچه
        self.trade_history.append({
            'timestamp': timestamp,
            'old_balance': old_balance,
            'new_balance': new_balance,
            'change': new_balance - old_balance,
            'daily_drawdown': daily_drawdown,
            'total_drawdown': total_drawdown
        })
        
        # به‌روزرسانی آمار معاملات
        if len(self.trade_history) >= 2:
            changes = [trade['change'] for trade in self.trade_history]
            wins = sum(1 for change in changes if change > 0)
            self.risk_metrics['win_rate'] = wins / len(changes) if changes else 0
            self.risk_metrics['volatility'] = np.std(changes) if len(changes) > 1 else 0
            
            if wins > 0 and len(changes) - wins > 0:
                avg_win = sum(change for change in changes if change > 0) / wins
                avg_loss = abs(sum(change for change in changes if change < 0)) / (len(changes) - wins)
                self.risk_metrics['risk_reward_ratio'] = avg_win / avg_loss if avg_loss > 0 else 0
        
        return {
            'current_balance': self.current_balance,
            'daily_drawdown': daily_drawdown,
            'total_drawdown': total_drawdown,
            'risk_status': self.get_risk_status()
        }
    
    def get_risk_status(self) -> Dict[str, Any]:
        """
        دریافت وضعیت فعلی ریسک
        
        خروجی:
        -------
        Dict[str, Any]
            وضعیت ریسک فعلی شامل محدودیت‌های معاملاتی
        """
        daily_drawdown = self.risk_metrics['daily_drawdown']
        total_drawdown = self.risk_metrics['total_drawdown']
        
        # تعیین وضعیت ریسک
        if daily_drawdown >= self.max_daily_drawdown or total_drawdown >= self.max_total_drawdown:
            status = 'stop_trading'  # توقف معاملات
            reason = 'max_drawdown_reached'
        elif daily_drawdown >= self.max_daily_drawdown * 0.8:
            status = 'reduce_risk'  # کاهش ریسک
            reason = 'approaching_daily_drawdown_limit'
        elif total_drawdown >= self.max_total_drawdown * 0.8:
            status = 'reduce_risk'  # کاهش ریسک
            reason = 'approaching_total_drawdown_limit'
        elif len(self.open_positions) >= self.max_open_positions:
            status = 'wait'  # انتظار
            reason = 'max_positions_reached'
        else:
            status = 'normal'  # عادی
            reason = None
        
        return {
            'status': status,
            'reason': reason,
            'daily_drawdown': daily_drawdown,
            'total_drawdown': total_drawdown,
            'open_positions': len(self.open_positions),
            'risk_metrics': self.risk_metrics
        }
    
    def calculate_position_size(self, symbol: str, entry_price: float, stop_loss: float) -> Tuple[float, Dict[str, Any]]:
        """
        محاسبه حجم مناسب پوزیشن بر اساس مدیریت ریسک
        
        پارامترها:
        -----------
        symbol : str
            نماد معاملاتی
        entry_price : float
            قیمت ورود
        stop_loss : float
            قیمت حد ضرر
        
        خروجی:
        -------
        Tuple[float, Dict[str, Any]]
            (حجم پوزیشن، اطلاعات تکمیلی)
        """
        risk_status = self.get_risk_status()
        
        # اگر وضعیت ریسک اجازه معامله نمی‌دهد
        if risk_status['status'] == 'stop_trading':
            return 0.0, {'reason': 'trading_stopped', 'risk_status': risk_status}
        
        # محاسبه ریسک دلاری برای این معامله
        risk_amount = self.current_balance * self.risk_per_trade
        
        # اگر وضعیت ریسک نیاز به کاهش دارد
        if risk_status['status'] == 'reduce_risk':
            risk_amount *= 0.5  # کاهش 50% ریسک
        
        # محاسبه فاصله استاپ لاس
        stop_distance = abs(entry_price - stop_loss)
        if stop_distance == 0:
            return 0.0, {'reason': 'invalid_stop_distance', 'risk_status': risk_status}
        
        # محاسبه حجم بر اساس ریسک و فاصله استاپ لاس
        # فرمول: حجم = ریسک دلاری / (فاصله استاپ لاس * ارزش هر پیپ)
        # فرض: ارزش هر پیپ برای لات استاندارد 10 دلار است
        pip_value = 10.0  # دلار برای هر پیپ در لات استاندارد
        
        # تبدیل فاصله قیمت به پیپ (فرض: 4 رقم اعشار برای فارکس)
        if symbol.endswith('JPY'):
            pips = stop_distance * 100  # 2 رقم اعشار برای جفت‌ارزهای ین
        else:
            pips = stop_distance * 10000  # 4 رقم اعشار برای سایر جفت‌ارزها
        
        # محاسبه حجم
        lot_size = risk_amount / (pips * pip_value)
        
        # محدود کردن حجم به مقادیر معقول
        lot_size = max(0.01, min(lot_size, 1.0))  # بین 0.01 و 1.0
        
        # گرد کردن به 0.01 نزدیک‌تر
        lot_size = round(lot_size * 100) / 100
        
        return lot_size, {
            'risk_amount': risk_amount,
            'stop_distance_pips': pips,
            'risk_status': risk_status
        }
    
    def can_open_position(self, symbol: str, timestamp: datetime.datetime = None) -> Tuple[bool, str]:
        """
        بررسی امکان باز کردن پوزیشن جدید
        
        پارامترها:
        -----------
        symbol : str
            نماد معاملاتی
        timestamp : datetime.datetime, optional
            زمان معامله (اگر None باشد، زمان فعلی استفاده می‌شود)
        
        خروجی:
        -------
        Tuple[bool, str]
            (آیا می‌توان پوزیشن باز کرد، دلیل)
        """
        if timestamp is None:
            timestamp = datetime.datetime.now()
        
        date_str = timestamp.strftime('%Y-%m-%d')
        
        # بررسی تعداد معاملات روزانه
        if date_str not in self.daily_trades:
            self.daily_trades[date_str] = []
        
        if len(self.daily_trades[date_str]) >= self.max_daily_trades:
            return False, "max_daily_trades_reached"
        
        # بررسی درادون روزانه
        if date_str in self.daily_drawdowns and self.daily_drawdowns[date_str]['drawdown'] >= self.max_daily_drawdown:
            return False, "daily_drawdown_limit_reached"
        
        # بررسی درادون کلی
        if self.risk_metrics['total_drawdown'] >= self.max_total_drawdown:
            return False, "total_drawdown_limit_reached"
        
        # بررسی تعداد پوزیشن‌های باز
        if len(self.open_positions) >= self.max_open_positions:
            return False, "max_open_positions_reached"
        
        # بررسی پوزیشن تکراری روی یک نماد
        if symbol in self.open_positions:
            return False, "position_already_open_for_symbol"
        
        return True, "allowed"
    
    def open_position(self, symbol: str, direction: str, entry_price: float, 
                     stop_loss: float, take_profit: float, lot_size: float,
                     timestamp: datetime.datetime = None) -> Dict[str, Any]:
        """
        باز کردن پوزیشن جدید
        
        پارامترها:
        -----------
        symbol : str
            نماد معاملاتی
        direction : str
            جهت معامله ('buy' یا 'sell')
        entry_price : float
            قیمت ورود
        stop_loss : float
            قیمت حد ضرر
        take_profit : float
            قیمت حد سود
        lot_size : float
            حجم معامله
        timestamp : datetime.datetime, optional
            زمان معامله (اگر None باشد، زمان فعلی استفاده می‌شود)
        
        خروجی:
        -------
        Dict[str, Any]
            اطلاعات پوزیشن باز شده یا خطا
        """
        if timestamp is None:
            timestamp = datetime.datetime.now()
        
        date_str = timestamp.strftime('%Y-%m-%d')
        
        # بررسی امکان باز کردن پوزیشن
        can_open, reason = self.can_open_position(symbol, timestamp)
        if not can_open:
            return {'success': False, 'reason': reason}
        
        # ایجاد پوزیشن جدید
        position_id = f"{symbol}_{timestamp.strftime('%Y%m%d%H%M%S')}"
        position = {
            'id': position_id,
            'symbol': symbol,
            'direction': direction,
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'lot_size': lot_size,
            'open_time': timestamp,
            'status': 'open',
            'risk_amount': abs(entry_price - stop_loss) * lot_size * 10000  # ریسک تقریبی
        }
        
        # ثبت پوزیشن
        self.open_positions[symbol] = position
        if date_str not in self.daily_trades:
            self.daily_trades[date_str] = []
        self.daily_trades[date_str].append(position_id)
        
        return {'success': True, 'position': position}
    
    def close_position(self, symbol: str, exit_price: float, 
                      timestamp: datetime.datetime = None, reason: str = "manual") -> Dict[str, Any]:
        """
        بستن پوزیشن موجود
        
        پارامترها:
        -----------
        symbol : str
            نماد معاملاتی
        exit_price : float
            قیمت خروج
        timestamp : datetime.datetime, optional
            زمان بستن (اگر None باشد، زمان فعلی استفاده می‌شود)
        reason : str, default="manual"
            دلیل بستن پوزیشن (manual, stop_loss, take_profit, ...)
        
        خروجی:
        -------
        Dict[str, Any]
            اطلاعات پوزیشن بسته شده یا خطا
        """
        if timestamp is None:
            timestamp = datetime.datetime.now()
        
        # بررسی وجود پوزیشن
        if symbol not in self.open_positions:
            return {'success': False, 'reason': 'position_not_found'}
        
        position = self.open_positions[symbol]
        
        # محاسبه سود/زیان
        pip_multiplier = 100 if symbol.endswith('JPY') else 10000
        if position['direction'] == 'buy':
            profit_pips = (exit_price - position['entry_price']) * pip_multiplier
        else:  # sell
            profit_pips = (position['entry_price'] - exit_price) * pip_multiplier
        
        # محاسبه سود/زیان دلاری
        pip_value = 10.0 * position['lot_size']  # دلار برای هر پیپ
        profit_loss = profit_pips * pip_value
        
        # به‌روزرسانی پوزیشن
        position['exit_price'] = exit_price
        position['close_time'] = timestamp
        position['status'] = 'closed'
        position['profit_pips'] = profit_pips
        position['profit_loss'] = profit_loss
        position['close_reason'] = reason
        
        # به‌روزرسانی موجودی
        new_balance = self.current_balance + profit_loss
        balance_update = self.update_balance(new_balance, timestamp)
        
        # حذف از پوزیشن‌های باز
        del self.open_positions[symbol]
        
        return {
            'success': True,
            'position': position,
            'profit_loss': profit_loss,
            'balance_update': balance_update
        }
    
    def check_stop_levels(self, market_data: Dict[str, Dict[str, float]]) -> List[Dict[str, Any]]:
        """
        بررسی حد ضرر و حد سود برای همه پوزیشن‌های باز
        
        پارامترها:
        -----------
        market_data : Dict[str, Dict[str, float]]
            داده‌های بازار (قیمت‌های فعلی) برای هر نماد
        
        خروجی:
        -------
        List[Dict[str, Any]]
            لیست نتایج بستن پوزیشن‌ها
        """
        results = []
        symbols_to_check = list(self.open_positions.keys())
        
        for symbol in symbols_to_check:
            if symbol not in market_data:
                continue
                
            position = self.open_positions[symbol]
            current_price = market_data[symbol].get('close', None)
            
            if current_price is None:
                continue
            
            # بررسی حد ضرر
            if (position['direction'] == 'buy' and current_price <= position['stop_loss']) or \
               (position['direction'] == 'sell' and current_price >= position['stop_loss']):
                result = self.close_position(symbol, current_price, reason="stop_loss")
                results.append(result)
                continue
            
            # بررسی حد سود
            if (position['direction'] == 'buy' and current_price >= position['take_profit']) or \
               (position['direction'] == 'sell' and current_price <= position['take_profit']):
                result = self.close_position(symbol, current_price, reason="take_profit")
                results.append(result)
                continue
        
        return results
    
    def get_daily_summary(self, date_str: str = None) -> Dict[str, Any]:
        """
        دریافت خلاصه عملکرد روزانه
        
        پارامترها:
        -----------
        date_str : str, optional
            تاریخ (به فرمت YYYY-MM-DD). اگر None باشد، تاریخ امروز استفاده می‌شود.
        
        خروجی:
        -------
        Dict[str, Any]
            خلاصه عملکرد روزانه
        """
        if date_str is None:
            date_str = datetime.datetime.now().strftime('%Y-%m-%d')
        
        # اطلاعات پایه
        summary = {
            'date': date_str,
            'trades_count': 0,
            'win_count': 0,
            'loss_count': 0,
            'profit_sum': 0.0,
            'loss_sum': 0.0,
            'net_profit': 0.0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'max_drawdown': 0.0
        }
        
        # بررسی معاملات روز
        if date_str in self.daily_trades:
            # فیلتر معاملات بسته شده
            closed_positions = [
                trade for trade_id in self.daily_trades[date_str]
                for symbol, pos in list(self.open_positions.items()) + []  # ترکیب با لیست خالی برای اطمینان
                if pos.get('id') == trade_id and pos.get('status') == 'closed'
            ]
            
            summary['trades_count'] = len(closed_positions)
            
            for pos in closed_positions:
                if pos.get('profit_loss', 0) > 0:
                    summary['win_count'] += 1
                    summary['profit_sum'] += pos.get('profit_loss', 0)
                else:
                    summary['loss_count'] += 1
                    summary['loss_sum'] += abs(pos.get('profit_loss', 0))
            
            summary['net_profit'] = summary['profit_sum'] - summary['loss_sum']
            
            if summary['trades_count'] > 0:
                summary['win_rate'] = summary['win_count'] / summary['trades_count']
            
            if summary['loss_sum'] > 0:
                summary['profit_factor'] = summary['profit_sum'] / summary['loss_sum']
        
        # اطلاعات درادون
        if date_str in self.daily_drawdowns:
            summary['max_drawdown'] = self.daily_drawdowns[date_str]['drawdown']
        
        return summary
