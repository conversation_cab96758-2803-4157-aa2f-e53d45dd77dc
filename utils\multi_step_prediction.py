"""
Multi-Step Prediction System
سیستم پیش‌بینی چندمرحله‌ای برای پیش‌بینی قیمت در چندین بازه زمانی

این سیستم قادر است قیمت را در چندین افق زمانی (1h, 4h, 12h, 24h) پیش‌بینی کند
و از نتایج برای بهبود تصمیم‌گیری استفاده کند.
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import pickle
from sklearn.preprocessing import MinMaxScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PredictionTarget:
    """هدف پیش‌بینی"""
    horizon: str  # '1h', '4h', '12h', '24h'
    steps: int    # تعداد مراحل
    target_type: str  # 'price', 'return', 'direction'
    
@dataclass
class MultiStepPrediction:
    """پیش‌بینی چندمرحله‌ای"""
    symbol: str
    timestamp: datetime
    predictions: Dict[str, Dict]  # {horizon: {value, confidence, direction}}
    features_used: List[str]
    model_confidence: float
    ensemble_agreement: float

class FeatureExtractor:
    """استخراج ویژگی برای پیش‌بینی"""
    
    def __init__(self):
        self.feature_names = [
            'price_sma_5', 'price_sma_10', 'price_sma_20',
            'price_ema_5', 'price_ema_10', 'price_ema_20',
            'rsi_14', 'rsi_21',
            'volatility_5', 'volatility_10', 'volatility_20',
            'volume_sma_5', 'volume_sma_10',
            'price_position', 'trend_strength',
            'momentum_5', 'momentum_10',
            'bollinger_upper', 'bollinger_lower', 'bollinger_position',
            'macd', 'macd_signal', 'macd_histogram'
        ]
    
    def extract_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """استخراج ویژگی‌های تکنیکال"""
        features = pd.DataFrame(index=data.index)
        
        # Moving Averages
        features['price_sma_5'] = data['close'].rolling(5).mean()
        features['price_sma_10'] = data['close'].rolling(10).mean()
        features['price_sma_20'] = data['close'].rolling(20).mean()
        
        # Exponential Moving Averages
        features['price_ema_5'] = data['close'].ewm(span=5).mean()
        features['price_ema_10'] = data['close'].ewm(span=10).mean()
        features['price_ema_20'] = data['close'].ewm(span=20).mean()
        
        # RSI
        features['rsi_14'] = self._calculate_rsi(data['close'], 14)
        features['rsi_21'] = self._calculate_rsi(data['close'], 21)
        
        # Volatility
        returns = data['close'].pct_change()
        features['volatility_5'] = returns.rolling(5).std()
        features['volatility_10'] = returns.rolling(10).std()
        features['volatility_20'] = returns.rolling(20).std()
        
        # Volume
        if 'volume' in data.columns:
            features['volume_sma_5'] = data['volume'].rolling(5).mean()
            features['volume_sma_10'] = data['volume'].rolling(10).mean()
        else:
            features['volume_sma_5'] = 1.0
            features['volume_sma_10'] = 1.0
        
        # Price Position
        high_20 = data['high'].rolling(20).max()
        low_20 = data['low'].rolling(20).min()
        features['price_position'] = (data['close'] - low_20) / (high_20 - low_20)
        
        # Trend Strength
        features['trend_strength'] = (features['price_sma_5'] - features['price_sma_20']) / features['price_sma_20']
        
        # Momentum
        features['momentum_5'] = data['close'].pct_change(5)
        features['momentum_10'] = data['close'].pct_change(10)
        
        # Bollinger Bands
        bb_mean = data['close'].rolling(20).mean()
        bb_std = data['close'].rolling(20).std()
        features['bollinger_upper'] = bb_mean + (bb_std * 2)
        features['bollinger_lower'] = bb_mean - (bb_std * 2)
        features['bollinger_position'] = (data['close'] - features['bollinger_lower']) / (features['bollinger_upper'] - features['bollinger_lower'])
        
        # MACD
        ema_12 = data['close'].ewm(span=12).mean()
        ema_26 = data['close'].ewm(span=26).mean()
        features['macd'] = ema_12 - ema_26
        features['macd_signal'] = features['macd'].ewm(span=9).mean()
        features['macd_histogram'] = features['macd'] - features['macd_signal']
        
        # Fill NaN values
        features = features.fillna(method='ffill').fillna(0)
        
        return features
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """محاسبه RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

class MultiStepPredictor:
    """پیش‌بینی‌کننده چندمرحله‌ای"""
    
    def __init__(self, symbol: str):
        self.symbol = symbol
        self.models = {}  # {horizon: model}
        self.scalers = {}  # {horizon: scaler}
        self.feature_extractor = FeatureExtractor()
        self.prediction_targets = {
            '1h': PredictionTarget('1h', 1, 'price'),
            '4h': PredictionTarget('4h', 4, 'price'),
            '12h': PredictionTarget('12h', 12, 'price'),
            '24h': PredictionTarget('24h', 24, 'price')
        }
        
        logger.info(f"Multi-Step Predictor initialized for {symbol}")
    
    def prepare_training_data(self, data: pd.DataFrame, horizon: str) -> Tuple[np.ndarray, np.ndarray]:
        """آماده‌سازی داده‌های آموزش"""
        # استخراج ویژگی‌ها
        features = self.feature_extractor.extract_features(data)
        
        # تعیین هدف پیش‌بینی
        target = self.prediction_targets[horizon]
        
        # ایجاد برچسب‌ها
        if target.target_type == 'price':
            labels = data['close'].shift(-target.steps)
        elif target.target_type == 'return':
            labels = data['close'].pct_change(target.steps).shift(-target.steps)
        else:  # direction
            price_change = data['close'].shift(-target.steps) - data['close']
            labels = (price_change > 0).astype(int)
        
        # حذف NaN
        valid_indices = ~(features.isna().any(axis=1) | labels.isna())
        X = features[valid_indices].values
        y = labels[valid_indices].values
        
        return X, y
    
    def train_model(self, data: pd.DataFrame, horizon: str) -> Dict:
        """آموزش مدل برای یک افق زمانی"""
        try:
            # آماده‌سازی داده‌ها
            X, y = self.prepare_training_data(data, horizon)
            
            if len(X) < 100:
                logger.warning(f"Not enough data for training {horizon} model: {len(X)} samples")
                return {'success': False, 'error': 'Insufficient data'}
            
            # تقسیم داده‌ها
            split_idx = int(len(X) * 0.8)
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # نرمال‌سازی
            scaler = MinMaxScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # آموزش مدل
            model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            )
            
            model.fit(X_train_scaled, y_train)
            
            # ارزیابی
            y_pred = model.predict(X_test_scaled)
            mse = mean_squared_error(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            
            # ذخیره مدل و scaler
            self.models[horizon] = model
            self.scalers[horizon] = scaler
            
            # محاسبه اهمیت ویژگی‌ها
            feature_importance = dict(zip(
                self.feature_extractor.feature_names,
                model.feature_importances_
            ))
            
            logger.info(f"Model trained for {horizon}: MSE={mse:.6f}, MAE={mae:.6f}")
            
            return {
                'success': True,
                'mse': mse,
                'mae': mae,
                'feature_importance': feature_importance,
                'training_samples': len(X_train),
                'test_samples': len(X_test)
            }
            
        except Exception as e:
            logger.error(f"Error training model for {horizon}: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def predict(self, data: pd.DataFrame, current_price: float) -> MultiStepPrediction:
        """پیش‌بینی چندمرحله‌ای"""
        predictions = {}
        
        # استخراج ویژگی‌ها
        features = self.feature_extractor.extract_features(data)
        current_features = features.iloc[-1].values.reshape(1, -1)
        
        for horizon in self.prediction_targets.keys():
            if horizon in self.models:
                try:
                    # نرمال‌سازی ویژگی‌ها
                    scaled_features = self.scalers[horizon].transform(current_features)
                    
                    # پیش‌بینی
                    predicted_price = self.models[horizon].predict(scaled_features)[0]
                    
                    # محاسبه اعتماد (بر اساس تنوع پیش‌بینی‌های درختان)
                    tree_predictions = []
                    for tree in self.models[horizon].estimators_:
                        tree_pred = tree.predict(scaled_features)[0]
                        tree_predictions.append(tree_pred)
                    
                    confidence = 1.0 - (np.std(tree_predictions) / np.mean(tree_predictions))
                    confidence = max(0.1, min(1.0, confidence))
                    
                    # تعیین جهت
                    price_change = predicted_price - current_price
                    direction = 'up' if price_change > 0 else 'down'
                    
                    predictions[horizon] = {
                        'predicted_price': predicted_price,
                        'price_change': price_change,
                        'price_change_pct': (price_change / current_price) * 100,
                        'direction': direction,
                        'confidence': confidence,
                        'tree_std': np.std(tree_predictions)
                    }
                    
                except Exception as e:
                    logger.error(f"Error predicting {horizon}: {str(e)}")
                    predictions[horizon] = {
                        'predicted_price': current_price,
                        'price_change': 0,
                        'price_change_pct': 0,
                        'direction': 'neutral',
                        'confidence': 0.1,
                        'error': str(e)
                    }
        
        # محاسبه اجماع مدل‌ها
        ensemble_agreement = self._calculate_ensemble_agreement(predictions)
        model_confidence = np.mean([p['confidence'] for p in predictions.values()])
        
        return MultiStepPrediction(
            symbol=self.symbol,
            timestamp=datetime.now(),
            predictions=predictions,
            features_used=self.feature_extractor.feature_names,
            model_confidence=model_confidence,
            ensemble_agreement=ensemble_agreement
        )
    
    def _calculate_ensemble_agreement(self, predictions: Dict) -> float:
        """محاسبه میزان اجماع مدل‌ها"""
        if not predictions:
            return 0.0
        
        directions = [p['direction'] for p in predictions.values() if 'direction' in p]
        if not directions:
            return 0.0
        
        # محاسبه درصد اجماع
        most_common_direction = max(set(directions), key=directions.count)
        agreement_count = directions.count(most_common_direction)
        
        return agreement_count / len(directions)
    
    def save_models(self, filepath: str):
        """ذخیره مدل‌ها"""
        model_data = {
            'symbol': self.symbol,
            'models': self.models,
            'scalers': self.scalers,
            'prediction_targets': self.prediction_targets,
            'feature_names': self.feature_extractor.feature_names,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        logger.info(f"Multi-step models saved to {filepath}")
    
    def load_models(self, filepath: str):
        """بارگذاری مدل‌ها"""
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.models = model_data['models']
            self.scalers = model_data['scalers']
            self.prediction_targets = model_data['prediction_targets']
            
            logger.info(f"Multi-step models loaded from {filepath}")
            
        except (FileNotFoundError, AttributeError, ImportError) as e:
            logger.warning(f"Could not load models: {filepath}, error: {e}")

class MultiStepPredictionSystem:
    """سیستم مدیریت پیش‌بینی چندمرحله‌ای"""
    
    def __init__(self, db_path: str = "multi_step_predictions.db"):
        self.db_path = db_path
        self.predictors = {}  # {symbol: MultiStepPredictor}
        self.prediction_history = []
        
        # راه‌اندازی پایگاه داده
        self._init_database()
        
        logger.info("Multi-Step Prediction System initialized")
    
    def _init_database(self):
        """راه‌اندازی پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول پیش‌بینی‌ها
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                horizon TEXT,
                predicted_price REAL,
                actual_price REAL,
                price_change REAL,
                price_change_pct REAL,
                direction TEXT,
                confidence REAL,
                accuracy REAL,
                features_used TEXT
            )
        ''')
        
        # جدول عملکرد مدل‌ها
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                horizon TEXT,
                mse REAL,
                mae REAL,
                accuracy REAL,
                training_samples INTEGER,
                feature_importance TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_symbol(self, symbol: str):
        """افزودن نماد جدید"""
        if symbol not in self.predictors:
            self.predictors[symbol] = MultiStepPredictor(symbol)
            logger.info(f"Added predictor for {symbol}")
    
    def train_models(self, symbol: str, data: pd.DataFrame) -> Dict:
        """آموزش مدل‌ها برای یک نماد"""
        if symbol not in self.predictors:
            self.add_symbol(symbol)
        
        predictor = self.predictors[symbol]
        results = {}
        
        for horizon in predictor.prediction_targets.keys():
            result = predictor.train_model(data, horizon)
            results[horizon] = result
            
            # ذخیره عملکرد در پایگاه داده
            if result['success']:
                self._save_model_performance(symbol, horizon, result)
        
        # ذخیره مدل‌ها
        model_path = Path(self.db_path).parent / f"models_{symbol}.pkl"
        predictor.save_models(str(model_path))
        
        return results
    
    def _save_model_performance(self, symbol: str, horizon: str, result: Dict):
        """ذخیره عملکرد مدل"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO model_performance 
            (timestamp, symbol, horizon, mse, mae, accuracy, training_samples, feature_importance)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            datetime.now().isoformat(),
            symbol,
            horizon,
            result.get('mse', 0),
            result.get('mae', 0),
            0,  # accuracy placeholder
            result.get('training_samples', 0),
            json.dumps(result.get('feature_importance', {}))
        ))
        
        conn.commit()
        conn.close()
    
    def get_prediction(self, symbol: str, data: pd.DataFrame, current_price: float) -> Optional[MultiStepPrediction]:
        """دریافت پیش‌بینی چندمرحله‌ای"""
        if symbol not in self.predictors:
            logger.warning(f"No predictor available for {symbol}")
            return None
        
        # بارگذاری مدل‌ها اگر وجود دارد
        model_path = Path(self.db_path).parent / f"models_{symbol}.pkl"
        if model_path.exists():
            self.predictors[symbol].load_models(str(model_path))
        
        # پیش‌بینی
        prediction = self.predictors[symbol].predict(data, current_price)
        
        # ذخیره پیش‌بینی
        self._save_prediction(prediction)
        
        return prediction
    
    def _save_prediction(self, prediction: MultiStepPrediction):
        """ذخیره پیش‌بینی در پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for horizon, pred_data in prediction.predictions.items():
            cursor.execute('''
                INSERT INTO predictions 
                (timestamp, symbol, horizon, predicted_price, price_change, 
                 price_change_pct, direction, confidence, features_used)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                prediction.timestamp.isoformat(),
                prediction.symbol,
                horizon,
                pred_data.get('predicted_price', 0),
                pred_data.get('price_change', 0),
                pred_data.get('price_change_pct', 0),
                pred_data.get('direction', 'neutral'),
                pred_data.get('confidence', 0),
                json.dumps(prediction.features_used)
            ))
        
        conn.commit()
        conn.close()
    
    def get_prediction_summary(self, symbol: str) -> Dict:
        """خلاصه پیش‌بینی‌ها"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # آخرین پیش‌بینی‌ها
        cursor.execute('''
            SELECT horizon, predicted_price, price_change_pct, direction, confidence
            FROM predictions 
            WHERE symbol = ? AND timestamp = (
                SELECT MAX(timestamp) FROM predictions WHERE symbol = ?
            )
        ''', (symbol, symbol))
        
        latest_predictions = cursor.fetchall()
        
        # عملکرد مدل‌ها
        cursor.execute('''
            SELECT horizon, AVG(mse) as avg_mse, AVG(mae) as avg_mae, COUNT(*) as count
            FROM model_performance 
            WHERE symbol = ?
            GROUP BY horizon
        ''', (symbol,))
        
        performance_data = cursor.fetchall()
        
        conn.close()
        
        return {
            'symbol': symbol,
            'latest_predictions': {
                row[0]: {
                    'predicted_price': row[1],
                    'price_change_pct': row[2],
                    'direction': row[3],
                    'confidence': row[4]
                }
                for row in latest_predictions
            },
            'model_performance': {
                row[0]: {
                    'avg_mse': row[1],
                    'avg_mae': row[2],
                    'training_count': row[3]
                }
                for row in performance_data
            }
        }
    
    def generate_trading_signals(self, symbol: str, data: pd.DataFrame, current_price: float) -> Dict:
        """تولید سیگنال‌های معاملاتی بر اساس پیش‌بینی‌ها"""
        prediction = self.get_prediction(symbol, data, current_price)
        
        if not prediction:
            return {'error': 'No prediction available'}
        
        # تحلیل سیگنال‌ها
        signals = {}
        overall_direction = 'neutral'
        overall_confidence = 0
        
        up_votes = 0
        down_votes = 0
        total_confidence = 0
        
        for horizon, pred_data in prediction.predictions.items():
            if 'direction' in pred_data:
                confidence = pred_data['confidence']
                direction = pred_data['direction']
                
                if direction == 'up':
                    up_votes += confidence
                elif direction == 'down':
                    down_votes += confidence
                
                total_confidence += confidence
                
                signals[horizon] = {
                    'direction': direction,
                    'confidence': confidence,
                    'price_change_pct': pred_data.get('price_change_pct', 0)
                }
        
        # تعیین جهت کلی
        if up_votes > down_votes:
            overall_direction = 'up'
            overall_confidence = up_votes / (up_votes + down_votes)
        elif down_votes > up_votes:
            overall_direction = 'down'
            overall_confidence = down_votes / (up_votes + down_votes)
        else:
            overall_direction = 'neutral'
            overall_confidence = 0.5
        
        # توصیه معاملاتی
        if overall_confidence > 0.7:
            if overall_direction == 'up':
                action = 'BUY'
            elif overall_direction == 'down':
                action = 'SELL'
            else:
                action = 'HOLD'
        else:
            action = 'HOLD'
        
        return {
            'symbol': symbol,
            'timestamp': prediction.timestamp.isoformat(),
            'overall_direction': overall_direction,
            'overall_confidence': overall_confidence,
            'recommended_action': action,
            'ensemble_agreement': prediction.ensemble_agreement,
            'individual_signals': signals,
            'model_confidence': prediction.model_confidence
        }

def main():
    """تست سیستم پیش‌بینی چندمرحله‌ای"""
    print("Multi-Step Prediction System Test")
    print("=" * 40)
    
    # ایجاد سیستم
    prediction_system = MultiStepPredictionSystem("test_predictions.db")
    
    # ایجاد داده‌های نمونه
    dates = pd.date_range(start='2023-01-01', periods=1000, freq='H')
    np.random.seed(42)
    
    # شبیه‌سازی داده‌های قیمت
    base_price = 1.1000
    price_changes = np.random.normal(0, 0.001, 1000)
    prices = [base_price]
    
    for change in price_changes:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    prices = prices[1:]  # حذف قیمت اولیه
    
    # ایجاد DataFrame
    sample_data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.0005))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.0005))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000, 10000, 1000)
    }, index=dates)
    
    print("Sample data created:")
    print(f"  Records: {len(sample_data)}")
    print(f"  Price range: {sample_data['close'].min():.5f} - {sample_data['close'].max():.5f}")
    print(f"  Current price: {sample_data['close'].iloc[-1]:.5f}")
    
    # آموزش مدل‌ها
    print("\nTraining models...")
    training_results = prediction_system.train_models("EURUSD", sample_data)
    
    for horizon, result in training_results.items():
        if result['success']:
            print(f"  {horizon}: MSE={result['mse']:.6f}, MAE={result['mae']:.6f}")
        else:
            print(f"  {horizon}: Failed - {result['error']}")
    
    # پیش‌بینی
    print("\nGenerating predictions...")
    current_price = sample_data['close'].iloc[-1]
    prediction = prediction_system.get_prediction("EURUSD", sample_data, current_price)
    
    if prediction:
        print(f"Predictions for EURUSD:")
        print(f"  Current price: {current_price:.5f}")
        print(f"  Model confidence: {prediction.model_confidence:.3f}")
        print(f"  Ensemble agreement: {prediction.ensemble_agreement:.3f}")
        
        for horizon, pred_data in prediction.predictions.items():
            print(f"  {horizon}:")
            print(f"    Predicted price: {pred_data['predicted_price']:.5f}")
            print(f"    Price change: {pred_data['price_change_pct']:.3f}%")
            print(f"    Direction: {pred_data['direction']}")
            print(f"    Confidence: {pred_data['confidence']:.3f}")
    
    # تولید سیگنال‌های معاملاتی
    print("\nGenerating trading signals...")
    signals = prediction_system.generate_trading_signals("EURUSD", sample_data, current_price)
    
    if 'error' not in signals:
        print(f"Trading signals:")
        print(f"  Overall direction: {signals['overall_direction']}")
        print(f"  Overall confidence: {signals['overall_confidence']:.3f}")
        print(f"  Recommended action: {signals['recommended_action']}")
        print(f"  Ensemble agreement: {signals['ensemble_agreement']:.3f}")
    
    # خلاصه پیش‌بینی‌ها
    summary = prediction_system.get_prediction_summary("EURUSD")
    print(f"\nPrediction summary saved to database")
    print(f"Model performance data available for {len(summary['model_performance'])} horizons")
    
    print("\nMulti-Step Prediction System test completed!")

if __name__ == "__main__":
    main() 