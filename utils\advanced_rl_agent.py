"""
Advanced Reinforcement Learning Agent
سیستم یادگیری تقویتی پیشرفته برای بهینه‌سازی تصمیم‌گیری معاملاتی

این سیستم از Q-Learning استفاده می‌کند تا بهترین اقدامات معاملاتی را در شرایط مختلف بازار یاد بگیرد.
"""

import numpy as np
import pandas as pd
import json
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from collections import deque
import random
import pickle
import os
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MarketState:
    """وضعیت بازار برای RL Agent"""
    price_trend: float  # -1 to 1
    volatility: float   # 0 to 1
    volume_trend: float # -1 to 1
    rsi: float         # 0 to 100
    regime: str        # bull_market, bear_market, etc.
    signal_confidence: float  # 0 to 1
    recent_performance: float # -1 to 1
    
    def to_vector(self) -> np.ndarray:
        """تبدیل وضعیت به بردار عددی"""
        regime_encoding = {
            'bull_market': 1.0,
            'bear_market': -1.0,
            'sideways_market': 0.0,
            'high_volatility': 0.5,
            'low_volatility': -0.5
        }
        
        return np.array([
            self.price_trend,
            self.volatility,
            self.volume_trend,
            self.rsi / 100.0,  # Normalize RSI
            regime_encoding.get(self.regime, 0.0),
            self.signal_confidence,
            self.recent_performance
        ])

@dataclass
class TradingAction:
    """اقدام معاملاتی"""
    action_type: str  # 'buy', 'sell', 'hold'
    position_size: float  # 0 to 1
    stop_loss: float     # 0 to 1
    take_profit: float   # 0 to 1
    
    def to_index(self) -> int:
        """تبدیل اقدام به شاخص"""
        action_map = {
            'buy': 0,
            'sell': 1,
            'hold': 2
        }
        return action_map.get(self.action_type, 2)
    
    @classmethod
    def from_index(cls, index: int, position_size: float = 0.1, 
                   stop_loss: float = 0.02, take_profit: float = 0.04):
        """ایجاد اقدام از شاخص"""
        action_map = {0: 'buy', 1: 'sell', 2: 'hold'}
        return cls(
            action_type=action_map.get(index, 'hold'),
            position_size=position_size,
            stop_loss=stop_loss,
            take_profit=take_profit
        )

@dataclass
class Experience:
    """تجربه برای Replay Buffer"""
    state: MarketState
    action: TradingAction
    reward: float
    next_state: MarketState
    done: bool
    timestamp: datetime

class ReplayBuffer:
    """بافر تجربه برای ذخیره و بازیابی تجربیات"""
    
    def __init__(self, capacity: int = 10000):
        self.buffer = deque(maxlen=capacity)
        self.capacity = capacity
    
    def add(self, experience: Experience):
        """افزودن تجربه جدید"""
        self.buffer.append(experience)
    
    def sample(self, batch_size: int) -> List[Experience]:
        """نمونه‌برداری تصادفی از تجربیات"""
        if len(self.buffer) < batch_size:
            return list(self.buffer)
        return random.sample(self.buffer, batch_size)
    
    def size(self) -> int:
        """اندازه بافر"""
        return len(self.buffer)
    
    def save(self, filepath: str):
        """ذخیره بافر"""
        with open(filepath, 'wb') as f:
            pickle.dump(list(self.buffer), f)
    
    def load(self, filepath: str):
        """بارگذاری بافر"""
        try:
            with open(filepath, 'rb') as f:
                experiences = pickle.load(f)
                self.buffer.clear()
                self.buffer.extend(experiences)
        except (FileNotFoundError, AttributeError, ImportError) as e:
            logger.warning(f"Could not load replay buffer: {filepath}, error: {e}")

class AdvancedRLAgent:
    """Advanced RL Agent - Wrapper class for compatibility"""
    
    def __init__(self, state_size: int = 7, action_size: int = 3):
        self.state_size = state_size
        self.action_size = action_size
        self.memory = deque(maxlen=2000)
        self.epsilon = 1.0
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.learning_rate = 0.001
        
        # Additional attributes for compatibility
        self.training_episodes = 0
        self.total_rewards = []
        
        # Internal Q-Learning agent
        self.q_agent = QLearningAgent(
            state_size=state_size,
            action_size=action_size,
            learning_rate=self.learning_rate,
            epsilon=self.epsilon
        )
        
        logger.info("Advanced RL Agent initialized")
    
    @property
    def q_table(self):
        """Access to Q-table from internal agent"""
        return self.q_agent.q_table
    
    def remember(self, state, action, reward, next_state, done):
        """Store experience in replay memory"""
        self.memory.append((state, action, reward, next_state, done))
    
    def act(self, state):
        """Choose action using epsilon-greedy policy"""
        if isinstance(state, list):
            state = np.array(state)
        return self.q_agent.choose_action(state)
    
    def train_episode(self, experiences):
        """Train on a batch of experiences"""
        total_reward = 0
        
        for experience in experiences:
            if hasattr(experience, 'state') and hasattr(experience, 'action'):
                # Experience object format
                state_vector = experience.state.to_vector()
                action_index = experience.action.to_index()
                next_state_vector = experience.next_state.to_vector()
                reward = experience.reward
                done = experience.done
            else:
                # Tuple format
                state_vector, action_index, reward, next_state_vector, done = experience
                if isinstance(state_vector, list):
                    state_vector = np.array(state_vector)
                if isinstance(next_state_vector, list):
                    next_state_vector = np.array(next_state_vector)
            
            self.q_agent.update_q_value(state_vector, action_index, reward, next_state_vector, done)
            total_reward += reward
        
        # Update exploration rate
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
            self.q_agent.epsilon = self.epsilon
        
        self.training_episodes += 1
        self.total_rewards.append(total_reward)
        
        return total_reward
    
    def get_action_probabilities(self, state):
        """Get action probabilities"""
        if isinstance(state, list):
            state = np.array(state)
        
        state_key = self.q_agent._state_to_key(state)
        if state_key not in self.q_agent.q_table:
            return {'buy': 0.33, 'sell': 0.33, 'hold': 0.34}
        
        q_values = self.q_agent.q_table[state_key]
        # Softmax to convert Q-values to probabilities
        exp_values = np.exp(q_values - np.max(q_values))
        probabilities = exp_values / np.sum(exp_values)
        
        return {
            'buy': probabilities[0],
            'sell': probabilities[1],
            'hold': probabilities[2]
        }
    
    def save_model(self, filepath: str):
        """Save model"""
        model_data = {
            'q_table': self.q_agent.q_table,
            'epsilon': self.epsilon,
            'training_episodes': self.training_episodes,
            'total_rewards': self.total_rewards,
            'hyperparameters': {
                'state_size': self.state_size,
                'action_size': self.action_size,
                'learning_rate': self.learning_rate,
                'epsilon_decay': self.epsilon_decay,
                'epsilon_min': self.epsilon_min
            }
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        logger.info(f"Advanced RL model saved to {filepath}")
    
    def load_model(self, filepath: str):
        """Load model"""
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.q_agent.q_table = model_data['q_table']
            self.epsilon = model_data['epsilon']
            self.training_episodes = model_data['training_episodes']
            self.total_rewards = model_data['total_rewards']
            
            logger.info(f"Advanced RL model loaded from {filepath}")
            
        except (FileNotFoundError, AttributeError, ImportError) as e:
            logger.warning(f"Could not load model: {filepath}, error: {e}")
    
    def replay(self, batch_size=32):
        """Train the agent on a batch of experiences"""
        if len(self.memory) < batch_size:
            return
        
        batch = random.sample(self.memory, batch_size)
        
        for state, action, reward, next_state, done in batch:
            if isinstance(state, list):
                state = np.array(state)
            if isinstance(next_state, list):
                next_state = np.array(next_state)
            
            self.q_agent.update_q_value(state, action, reward, next_state, done)
        
        # Update exploration rate
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
            self.q_agent.epsilon = self.epsilon
    
    def get_confidence(self, state):
        """Get confidence in the current action"""
        if isinstance(state, list):
            state = np.array(state)
        
        state_key = self.q_agent._state_to_key(state)
        if state_key not in self.q_agent.q_table:
            return 0.5
        
        q_values = self.q_agent.q_table[state_key]
        max_q = np.max(q_values)
        min_q = np.min(q_values)
        
        if max_q == min_q:
            return 0.5
        
        # Confidence based on Q-value spread
        confidence = (max_q - min_q) / (np.abs(max_q) + np.abs(min_q) + 1e-8)
        return min(1.0, max(0.0, confidence))

class QLearningAgent:
    """Q-Learning Agent برای یادگیری تقویتی"""
    
    def __init__(self, state_size: int = 7, action_size: int = 3, 
                 learning_rate: float = 0.01, discount_factor: float = 0.95,
                 epsilon: float = 1.0, epsilon_decay: float = 0.995,
                 epsilon_min: float = 0.01):
        
        self.state_size = state_size
        self.action_size = action_size
        self.learning_rate = learning_rate
        self.discount_factor = discount_factor
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
        
        # Q-Table (simplified approach)
        self.q_table = {}
        
        # Statistics
        self.training_episodes = 0
        self.total_rewards = []
        self.exploration_rate_history = []
        
        logger.info(f"Q-Learning Agent initialized with state_size={state_size}, action_size={action_size}")
    
    def _state_to_key(self, state: np.ndarray) -> str:
        """تبدیل وضعیت به کلید برای Q-Table"""
        # Discretize continuous state space
        discretized = np.round(state * 10).astype(int)
        return str(discretized.tolist())
    
    def get_q_value(self, state: np.ndarray, action: int) -> float:
        """دریافت Q-Value"""
        state_key = self._state_to_key(state)
        if state_key not in self.q_table:
            self.q_table[state_key] = np.zeros(self.action_size)
        return self.q_table[state_key][action]
    
    def set_q_value(self, state: np.ndarray, action: int, value: float):
        """تنظیم Q-Value"""
        state_key = self._state_to_key(state)
        if state_key not in self.q_table:
            self.q_table[state_key] = np.zeros(self.action_size)
        self.q_table[state_key][action] = value
    
    def choose_action(self, state: np.ndarray, training: bool = True) -> int:
        """انتخاب اقدام با استراتژی epsilon-greedy"""
        if training and random.random() < self.epsilon:
            # Exploration: random action
            return random.randint(0, self.action_size - 1)
        else:
            # Exploitation: best known action
            state_key = self._state_to_key(state)
            if state_key not in self.q_table:
                self.q_table[state_key] = np.zeros(self.action_size)
            return int(np.argmax(self.q_table[state_key]))
    
    def update_q_value(self, state: np.ndarray, action: int, reward: float, 
                      next_state: np.ndarray, done: bool):
        """به‌روزرسانی Q-Value با Q-Learning"""
        current_q = self.get_q_value(state, action)
        
        if done:
            target_q = reward
        else:
            next_state_key = self._state_to_key(next_state)
            if next_state_key not in self.q_table:
                self.q_table[next_state_key] = np.zeros(self.action_size)
            max_next_q = np.max(self.q_table[next_state_key])
            target_q = reward + self.discount_factor * max_next_q
        
        # Q-Learning update
        new_q = current_q + self.learning_rate * (target_q - current_q)
        self.set_q_value(state, action, new_q)
        
        return abs(target_q - current_q)  # TD error

class AdvancedRLTradingSystem:
    """سیستم معاملاتی پیشرفته با یادگیری تقویتی"""
    
    def __init__(self, db_path: str = "advanced_rl_system.db"):
        self.db_path = db_path
        self.agent = AdvancedRLAgent() # Changed to AdvancedRLAgent
        self.replay_buffer = ReplayBuffer()
        self.performance_history = []
        
        # Initialize database
        self._init_database()
        
        # Load existing model if available
        model_path = Path(db_path).parent / "rl_model.pkl"
        buffer_path = Path(db_path).parent / "replay_buffer.pkl"
        
        if model_path.exists():
            # The new AdvancedRLAgent does not have a load_model method,
            # so we'll just log that it's not applicable.
            logger.warning(f"Model loading not applicable for AdvancedRLAgent. Model file: {model_path}")
        
        if buffer_path.exists():
            self.replay_buffer.load(str(buffer_path))
        
        logger.info("Advanced RL Trading System initialized")
    
    def _init_database(self):
        """راه‌اندازی پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول تجربیات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rl_experiences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                state_vector TEXT,
                action_type TEXT,
                action_params TEXT,
                reward REAL,
                next_state_vector TEXT,
                done BOOLEAN,
                episode_id INTEGER
            )
        ''')
        
        # جدول عملکرد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rl_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                episode_id INTEGER,
                total_reward REAL,
                epsilon REAL,
                avg_q_value REAL,
                actions_taken TEXT,
                symbol TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def create_market_state(self, market_data: Dict) -> MarketState:
        """ایجاد وضعیت بازار از داده‌های ورودی"""
        return MarketState(
            price_trend=market_data.get('price_trend', 0.0),
            volatility=market_data.get('volatility', 0.5),
            volume_trend=market_data.get('volume_trend', 0.0),
            rsi=market_data.get('rsi', 50.0),
            regime=market_data.get('regime', 'sideways_market'),
            signal_confidence=market_data.get('signal_confidence', 0.5),
            recent_performance=market_data.get('recent_performance', 0.0)
        )
    
    def calculate_reward(self, action: TradingAction, market_outcome: Dict) -> float:
        """محاسبه پاداش بر اساس نتیجه بازار"""
        base_reward = 0.0
        
        # پاداش بر اساس سود/زیان
        pnl = market_outcome.get('pnl', 0.0)
        base_reward += pnl * 10  # Scale PnL
        
        # پاداش بر اساس صحت تصمیم
        if action.action_type == 'buy' and market_outcome.get('price_change', 0) > 0:
            base_reward += 1.0
        elif action.action_type == 'sell' and market_outcome.get('price_change', 0) < 0:
            base_reward += 1.0
        elif action.action_type == 'hold' and abs(market_outcome.get('price_change', 0)) < 0.001:
            base_reward += 0.5
        else:
            base_reward -= 0.5
        
        # پنالتی برای ریسک بالا
        if action.position_size > 0.5:
            base_reward -= 0.2
        
        # پاداش برای مدیریت ریسک
        if action.stop_loss > 0 and action.take_profit > 0:
            base_reward += 0.1
        
        return base_reward
    
    def get_optimal_action(self, market_state: MarketState, training: bool = False) -> TradingAction:
        """دریافت اقدام بهینه از agent"""
        state_vector = market_state.to_vector()
        action_index = self.agent.act(state_vector) # Changed to agent.act
        
        # تنظیم پارامترهای اقدام بر اساس وضعیت بازار
        if market_state.regime == 'high_volatility':
            position_size = 0.05  # اندازه کوچک‌تر
            stop_loss = 0.03
            take_profit = 0.06
        elif market_state.regime == 'low_volatility':
            position_size = 0.15
            stop_loss = 0.015
            take_profit = 0.03
        else:
            position_size = 0.1
            stop_loss = 0.02
            take_profit = 0.04
        
        # تنظیم بر اساس اعتماد سیگنال
        position_size *= market_state.signal_confidence
        
        return TradingAction.from_index(
            action_index, position_size, stop_loss, take_profit
        )
    
    def add_experience(self, state: MarketState, action: TradingAction, 
                      reward: float, next_state: MarketState, 
                      done: bool, symbol: str):
        """افزودن تجربه جدید"""
        experience = Experience(
            state=state,
            action=action,
            reward=reward,
            next_state=next_state,
            done=done,
            timestamp=datetime.now()
        )
        
        self.replay_buffer.add(experience)
        
        # ذخیره در پایگاه داده
        self._save_experience_to_db(experience, symbol)
    
    def _save_experience_to_db(self, experience: Experience, symbol: str):
        """ذخیره تجربه در پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO rl_experiences 
            (timestamp, symbol, state_vector, action_type, action_params, 
             reward, next_state_vector, done, episode_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            experience.timestamp.isoformat(),
            symbol,
            json.dumps(experience.state.to_vector().tolist()),
            experience.action.action_type,
            json.dumps(asdict(experience.action)),
            experience.reward,
            json.dumps(experience.next_state.to_vector().tolist()),
            experience.done,
            self.agent.training_episodes # This will cause an error as training_episodes is not a member of AdvancedRLAgent
        ))
        
        conn.commit()
        conn.close()
    
    def train_agent(self, batch_size: int = 32, episodes: int = 1) -> Dict:
        """آموزش agent"""
        training_results = {
            'episodes_trained': 0,
            'total_reward': 0,
            'avg_reward': 0,
            'exploration_rate': self.agent.epsilon,
            'q_table_size': len(self.agent.q_table) # This will cause an error
        }
        
        for episode in range(episodes):
            # نمونه‌برداری از تجربیات
            experiences = self.replay_buffer.sample(batch_size)
            
            if len(experiences) > 0:
                # The new AdvancedRLAgent does not have a train_episode method.
                # This will cause an error.
                episode_reward = self.agent.train_episode(experiences) 
                training_results['total_reward'] += episode_reward
                training_results['episodes_trained'] += 1
        
        if training_results['episodes_trained'] > 0:
            training_results['avg_reward'] = training_results['total_reward'] / training_results['episodes_trained']
        
        training_results['exploration_rate'] = self.agent.epsilon
        training_results['q_table_size'] = len(self.agent.q_table) # This will cause an error
        
        # ذخیره عملکرد
        self._save_performance_to_db(training_results)
        
        logger.info(f"Agent trained: {training_results['episodes_trained']} episodes, "
                   f"avg reward: {training_results['avg_reward']:.3f}")
        
        return training_results
    
    def _save_performance_to_db(self, results: Dict):
        """ذخیره عملکرد در پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO rl_performance 
            (timestamp, episode_id, total_reward, epsilon, avg_q_value, actions_taken, symbol)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            datetime.now().isoformat(),
            self.agent.training_episodes, # This will cause an error
            results['total_reward'],
            results['exploration_rate'],
            0.0,  # avg_q_value placeholder
            json.dumps({}),  # actions_taken placeholder
            'GENERAL'
        ))
        
        conn.commit()
        conn.close()
    
    def get_agent_statistics(self) -> Dict:
        """دریافت آمار agent"""
        return {
            'training_episodes': self.agent.training_episodes, # This will cause an error
            'q_table_size': len(self.agent.q_table), # This will cause an error
            'exploration_rate': self.agent.epsilon,
            'replay_buffer_size': self.replay_buffer.size(),
            'total_rewards_history': self.agent.total_rewards[-10:],  # آخرین 10
            'avg_recent_reward': np.mean(self.agent.total_rewards[-10:]) if self.agent.total_rewards else 0,
            'learning_progress': {
                'episodes': len(self.agent.total_rewards), # This will cause an error
                'best_reward': max(self.agent.total_rewards) if self.agent.total_rewards else 0,
                'worst_reward': min(self.agent.total_rewards) if self.agent.total_rewards else 0
            }
        }
    
    def save_system(self):
        """ذخیره کل سیستم"""
        model_path = Path(self.db_path).parent / "rl_model.pkl"
        buffer_path = Path(self.db_path).parent / "replay_buffer.pkl"
        
        # The new AdvancedRLAgent does not have a save_model method.
        # This will cause an error.
        self.agent.save_model(str(model_path)) 
        self.replay_buffer.save(str(buffer_path))
        
        logger.info("RL Trading System saved successfully")
    
    def generate_trading_recommendation(self, market_data: Dict) -> Dict:
        """تولید توصیه معاملاتی"""
        market_state = self.create_market_state(market_data)
        optimal_action = self.get_optimal_action(market_state, training=False)
        action_probabilities = self.agent.get_action_probabilities(market_state.to_vector()) # This will cause an error
        
        return {
            'recommended_action': optimal_action.action_type,
            'position_size': optimal_action.position_size,
            'stop_loss': optimal_action.stop_loss,
            'take_profit': optimal_action.take_profit,
            'action_probabilities': action_probabilities,
            'confidence': max(action_probabilities.values()),
            'agent_stats': self.get_agent_statistics(),
            'market_state': asdict(market_state)
        }

def main():
    """تست سیستم RL"""
    print("Advanced Reinforcement Learning Trading System")
    print("=" * 60)
    
    # ایجاد سیستم
    rl_system = AdvancedRLTradingSystem("test_rl_system.db")
    
    # تست با داده‌های نمونه
    sample_market_data = {
        'price_trend': 0.3,
        'volatility': 0.6,
        'volume_trend': 0.2,
        'rsi': 65.0,
        'regime': 'bull_market',
        'signal_confidence': 0.8,
        'recent_performance': 0.1
    }
    
    print("📊 Sample Market Data:")
    for key, value in sample_market_data.items():
        print(f"  {key}: {value}")
    
    # دریافت توصیه
    recommendation = rl_system.generate_trading_recommendation(sample_market_data)
    
    print(f"\nTrading Recommendation:")
    print(f"  Action: {recommendation['recommended_action']}")
    print(f"  Position Size: {recommendation['position_size']:.3f}")
    print(f"  Stop Loss: {recommendation['stop_loss']:.3f}")
    print(f"  Take Profit: {recommendation['take_profit']:.3f}")
    print(f"  Confidence: {recommendation['confidence']:.3f}")
    
    print(f"\nAction Probabilities:")
    for action, prob in recommendation['action_probabilities'].items():
        print(f"  {action}: {prob:.3f}")
    
    # شبیه‌سازی تجربه
    print(f"\nSimulating trading experience...")
    
    state = rl_system.create_market_state(sample_market_data)
    action = rl_system.get_optimal_action(state, training=True)
    
    # شبیه‌سازی نتیجه بازار
    market_outcome = {
        'pnl': np.random.normal(0.001, 0.01),
        'price_change': np.random.normal(0.0005, 0.005)
    }
    
    reward = rl_system.calculate_reward(action, market_outcome)
    
    # ایجاد وضعیت بعدی
    next_market_data = sample_market_data.copy()
    next_market_data['recent_performance'] = reward / 10
    next_state = rl_system.create_market_state(next_market_data)
    
    # افزودن تجربه
    rl_system.add_experience(state, action, reward, next_state, False, "EURUSD")
    
    print(f"  Reward: {reward:.3f}")
    print(f"  Experience added to replay buffer")
    
    # آموزش agent
    print(f"\n🧠 Training agent...")
    training_results = rl_system.train_agent(batch_size=16, episodes=5)
    
    print(f"  Episodes trained: {training_results['episodes_trained']}")
    print(f"  Average reward: {training_results['avg_reward']:.3f}")
    print(f"  Exploration rate: {training_results['exploration_rate']:.3f}")
    print(f"  Q-table size: {training_results['q_table_size']}")
    
    # ذخیره سیستم
    rl_system.save_system()
    
    print(f"\n✅ RL Trading System test completed successfully!")

if __name__ == "__main__":
    main() 