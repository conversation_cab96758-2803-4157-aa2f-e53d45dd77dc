#!/usr/bin/env python3
"""
🧪 Comprehensive Test Verification
تست جامع تأیید رفع مشکلات
"""

import sys
import os
sys.path.insert(0, '.')

def test_order_manager_validate():
    """تست validate_order در Order Manager"""
    try:
        from core.order_manager import AdvancedOrderManager
        from core.shared_types import Order, OrderType, OrderSide, TimeInForce
        from decimal import Decimal
        
        manager = AdvancedOrderManager()
        
        test_order = Order(
            order_id="test_validate",
            symbol="EURUSD",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=Decimal('1000'),
            time_in_force=TimeInForce.GTC
        )
        
        if hasattr(manager, 'validate_order'):
            result = manager.validate_order(test_order)
            print(f"✅ Order Manager validate_order: {'SUCCESS' if result else 'FAILED'}")
            return result
        else:
            print("❌ Order Manager validate_order: METHOD NOT FOUND")
            return False
            
    except Exception as e:
        print(f"❌ Order Manager validate_order: ERROR - {e}")
        return False

def test_correlation_analyzer_data():
    """تست Correlation Analyzer با داده کم"""
    try:
        from core.correlation_analysis import AdvancedCorrelationAnalyzer
        import pandas as pd
        import numpy as np
        
        analyzer = AdvancedCorrelationAnalyzer()
        
        # تست با 3 نقطه داده
        test_data = pd.DataFrame({
            'EURUSD': [1.2000, 1.2010, 1.2005],
            'GBPUSD': [1.3000, 1.3015, 1.3008]
        })
        
        result = analyzer.calculate_correlation_matrix(test_data)
        print(f"✅ Correlation Analyzer (3 points): {'SUCCESS' if result else 'FAILED'}")
        return bool(result)
        
    except Exception as e:
        print(f"❌ Correlation Analyzer: ERROR - {e}")
        return False

def test_risk_calculator_portfolio():
    """تست Risk Calculator portfolio risk"""
    try:
        from core.advanced_risk_metrics import AdvancedRiskCalculator
        
        calculator = AdvancedRiskCalculator()
        
        test_portfolio = [
            {"symbol": "EURUSD", "return": 0.001, "value": 1000},
            {"symbol": "GBPUSD", "return": -0.002, "value": 1500}
        ]
        
        if hasattr(calculator, 'calculate_portfolio_risk'):
            result = calculator.calculate_portfolio_risk(test_portfolio)
            success = "error" not in result
            print(f"✅ Risk Calculator portfolio: {'SUCCESS' if success else 'FAILED'}")
            return success
        else:
            print("❌ Risk Calculator portfolio: METHOD NOT FOUND")
            return False
            
    except Exception as e:
        print(f"❌ Risk Calculator portfolio: ERROR - {e}")
        return False

def test_pytest_timeout_fix():
    """تست رفع مشکل pytest timeout"""
    try:
        with open('utils/test_runner.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        has_timeout = '--timeout=' in content
        print(f"✅ pytest timeout fix: {'SUCCESS' if not has_timeout else 'FAILED'}")
        return not has_timeout
        
    except Exception as e:
        print(f"❌ pytest timeout fix: ERROR - {e}")
        return False

def main():
    """اجرای تست‌های جامع"""
    print("🧪 Comprehensive Test Verification")
    print("=" * 50)
    
    tests = [
        ("Order Manager validate_order", test_order_manager_validate),
        ("Correlation Analyzer data", test_correlation_analyzer_data),
        ("Risk Calculator portfolio", test_risk_calculator_portfolio),
        ("pytest timeout fix", test_pytest_timeout_fix)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Testing: {test_name}")
        result = test_func()
        results.append(result)
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 Test Results: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("🎉 All issues fixed successfully!")
        return 0
    else:
        print("⚠️ Some issues remain")
        return 1

if __name__ == "__main__":
    exit(main())
