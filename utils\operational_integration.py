#!/usr/bin/env python3
"""
ادغام عملیاتی تست‌ها در سیستم
===============================

این ماژول تست‌ها را به صورت یکپارچه در سیستم اصلی ادغام می‌کند.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime
import time

from utils.logger import get_logger
from utils.test_runner import test_runner, run_startup_tests, run_health_checks


class OperationalIntegration:
    """ادغام عملیاتی تست‌ها"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.test_history = []
        self.enabled = True
        self.last_health_check = None
    
    def enable_testing(self):
        """فعال‌سازی تست‌ها"""
        self.enabled = True
        self.logger.info("✅ Operational testing enabled")
    
    def disable_testing(self):
        """غیرفعال‌سازی تست‌ها"""
        self.enabled = False
        self.logger.info("⚠️ Operational testing disabled")
    
    def run_startup_validation(self) -> bool:
        """اعتبارسنجی شروع سیستم"""
        if not self.enabled:
            self.logger.info("⏭️ Startup tests skipped (disabled)")
            return True
        
        self.logger.info("🧪 Starting system validation...")
        
        try:
            # Run startup tests
            success = run_startup_tests()
            
            # Record result
            self.test_history.append({
                "type": "startup",
                "timestamp": datetime.now().isoformat(),
                "success": success,
                "duration": time.time()
            })
            
            if success:
                self.logger.info("✅ System validation passed!")
                return True
            else:
                self.logger.error("❌ System validation failed!")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ System validation error: {e}")
            return False
    
    def run_health_validation(self) -> bool:
        """اعتبارسنجی سلامت سیستم"""
        if not self.enabled:
            return True
        
        self.logger.info("🏥 Running health validation...")
        
        try:
            # Run health checks
            success = run_health_checks()
            
            # Record result
            self.last_health_check = {
                "timestamp": datetime.now().isoformat(),
                "success": success
            }
            
            self.test_history.append({
                "type": "health",
                "timestamp": datetime.now().isoformat(),
                "success": success,
                "duration": time.time()
            })
            
            if success:
                self.logger.info("✅ Health validation passed!")
                return True
            else:
                self.logger.error("❌ Health validation failed!")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Health validation error: {e}")
            return False
    
    def get_test_status(self) -> Dict[str, Any]:
        """وضعیت تست‌ها"""
        return {
            "enabled": self.enabled,
            "last_health_check": self.last_health_check,
            "test_history_count": len(self.test_history),
            "recent_tests": self.test_history[-5:] if self.test_history else [],
            "test_runner_status": test_runner.get_test_status()
        }
    
    def integrate_with_system_startup(self, system_manager) -> bool:
        """ادغام با فرآیند شروع سیستم"""
        try:
            # Add startup validation
            if not self.run_startup_validation():
                self.logger.warning("⚠️ Startup validation failed, but continuing...")
                return False
            
            # Add health check to system manager
            if hasattr(system_manager, 'add_shutdown_callback'):
                system_manager.add_shutdown_callback(self._shutdown_callback)
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Integration failed: {e}")
            return False
    
    def _shutdown_callback(self):
        """Callback برای shutdown"""
        self.logger.info("🧪 Running shutdown tests...")
        
        # Run final health check
        self.run_health_validation()
        
        # Generate final report
        self._generate_shutdown_report()
    
    def _generate_shutdown_report(self):
        """تولید گزارش shutdown"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": len(self.test_history),
            "successful_tests": len([t for t in self.test_history if t["success"]]),
            "test_history": self.test_history
        }
        
        self.logger.info(f"📊 Shutdown report: {report['successful_tests']}/{report['total_tests']} tests passed")

    def perform_health_check(self) -> Dict[str, Any]:
        """انجام بررسی سلامت سیستم و برگرداندن نتایج تفصیلی"""
        if not self.enabled:
            return {
                "overall_health": True,
                "status": "disabled",
                "message": "Health checks disabled"
            }
        
        self.logger.info("🏥 Performing comprehensive health check...")
        
        try:
            # Run health validation
            health_success = self.run_health_validation()
            
            # Get detailed status
            status = self.get_test_status()
            
            # Build comprehensive health report
            health_report = {
                "overall_health": health_success,
                "status": "healthy" if health_success else "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "test_enabled": self.enabled,
                "last_health_check": self.last_health_check,
                "test_history_count": len(self.test_history),
                "recent_test_success_rate": self._calculate_recent_success_rate(),
                "details": status
            }
            
            if health_success:
                self.logger.info("✅ Health check completed successfully")
            else:
                self.logger.warning("⚠️ Health check found issues")
            
            return health_report
            
        except Exception as e:
            self.logger.error(f"❌ Health check error: {e}")
            return {
                "overall_health": False,
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _calculate_recent_success_rate(self) -> float:
        """محاسبه نرخ موفقیت تست‌های اخیر"""
        if not self.test_history:
            return 1.0
        
        # Get last 10 tests
        recent_tests = self.test_history[-10:]
        successful_tests = len([t for t in recent_tests if t["success"]])
        
        return successful_tests / len(recent_tests) if recent_tests else 1.0


# Global instance
operational_integration = OperationalIntegration()

# Quick access functions
def validate_system_startup() -> bool:
    """اعتبارسنجی سریع شروع سیستم"""
    return operational_integration.run_startup_validation()

def validate_system_health() -> bool:
    """اعتبارسنجی سریع سلامت سیستم"""
    return operational_integration.run_health_validation()

def get_operational_status() -> Dict[str, Any]:
    """وضعیت عملیاتی تست‌ها"""
    return operational_integration.get_test_status()

def enable_operational_testing():
    """فعال‌سازی تست‌های عملیاتی"""
    operational_integration.enable_testing()

def disable_operational_testing():
    """غیرفعال‌سازی تست‌های عملیاتی"""
    operational_integration.disable_testing()


if __name__ == "__main__":
    """تست مستقل ماژول"""
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 Testing operational integration...")
    
    # Test startup validation
    startup_success = validate_system_startup()
    print(f"Startup validation: {'✅ PASSED' if startup_success else '❌ FAILED'}")
    
    # Test health validation
    health_success = validate_system_health()
    print(f"Health validation: {'✅ PASSED' if health_success else '❌ FAILED'}")
    
    # Show status
    status = get_operational_status()
    print(f"📊 Status: {status}")
    
    print("✅ Operational integration test completed!") 