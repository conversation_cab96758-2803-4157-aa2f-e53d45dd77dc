#!/usr/bin/env python3
"""
تست کامل چهار مورد اصلی که ادعا شده رفع شده‌اند
"""
import sys
import traceback
from datetime import datetime
import numpy as np
import pandas as pd

def test_technical_indicators():
    """تست اندیکاتورهای تکنیکال"""
    print("🔍 Testing TechnicalIndicators...")
    try:
        from utils.technical_indicators import TechnicalIndicators
        ti = TechnicalIndicators()
        
        # تست متدهای موجود
        indicators = ti.get_available_indicators()
        print(f"  ✅ Found {len(indicators)} indicators")
        print(f"  📋 Available: {indicators}")
        
        # تست داده‌های نمونه
        sample_data = pd.DataFrame({
            'close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110],
            'high': [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111],
            'low': [99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109],
            'volume': [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000]
        })
        
        # تست اندیکاتورهای تک ستونی
        sma = ti.sma(sample_data['close'], 5)
        ema = ti.ema(sample_data['close'], 5)
        rsi = ti.rsi(sample_data['close'], 5)
        
        print(f"  ✅ SMA calculated: {len(sma)} values")
        print(f"  ✅ EMA calculated: {len(ema)} values")
        print(f"  ✅ RSI calculated: {len(rsi)} values")
        
        # تست اندیکاتورهای چند ستونی
        macd_line, signal_line, histogram = ti.macd(sample_data['close'])
        print(f"  ✅ MACD calculated: {len(macd_line)} values")
        
        bb_upper, bb_middle, bb_lower = ti.bollinger_bands(sample_data['close'])
        print(f"  ✅ Bollinger Bands calculated: {len(bb_upper)} values")
        
        # تست محاسبه همه اندیکاتورها
        all_indicators = ti.calculate_all(sample_data)
        print(f"  ✅ All indicators calculated: {len(all_indicators.columns)} columns")
        
        return True
        
    except Exception as e:
        print(f"  ❌ TechnicalIndicators failed: {e}")
        traceback.print_exc()
        return False

def test_continual_learning():
    """تست سیستم یادگیری مداوم"""
    print("🔍 Testing ContinualLearningSystem...")
    try:
        from models.continual_learning import ContinualLearningSystem
        cls = ContinualLearningSystem()
        
        # تست متدهای موجود
        methods = [m for m in dir(cls) if not m.startswith('_')]
        print(f"  ✅ Found {len(methods)} methods")
        print(f"  📋 Methods: {methods}")
        
        # تست اضافه کردن مدل
        cls.add_model("test_model", {"type": "dummy"})
        print(f"  ✅ Model added successfully")
        
        # تست ارزیابی
        performance = cls.evaluate_model("test_model", {"test": "data"})
        print(f"  ✅ Model evaluated: {performance}")
        
        # تست تطبیق
        adapted = cls.adapt_model("test_model", {"new": "data"})
        print(f"  ✅ Model adapted: {adapted}")
        
        # تست بهترین مدل
        best_model = cls.get_best_model()
        print(f"  ✅ Best model: {best_model}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ ContinualLearningSystem failed: {e}")
        traceback.print_exc()
        return False

def test_backtesting_framework():
    """تست فریم‌ورک بک‌تست"""
    print("🔍 Testing BacktestingFramework...")
    try:
        from core.backtesting_framework import BacktestingFramework
        bf = BacktestingFramework()
        
        # تست متدهای موجود
        methods = [m for m in dir(bf) if not m.startswith('_')]
        print(f"  ✅ Found {len(methods)} methods")
        print(f"  📋 Methods: {methods}")
        
        # تست داده‌های نمونه
        sample_data = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=10),
            'close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109],
            'high': [101, 102, 103, 104, 105, 106, 107, 108, 109, 110],
            'low': [99, 100, 101, 102, 103, 104, 105, 106, 107, 108],
            'volume': [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900]
        })
        
        # استراتژی ساده
        class SimpleStrategy:
            def generate_signal(self, row):
                return 0.1 if row['close'] > 105 else 0
        
        strategy = SimpleStrategy()
        
        # تست بک‌تست
        results = bf.run_backtest(strategy, sample_data)
        print(f"  ✅ Backtest completed: {len(results)} metrics")
        
        if 'error' not in results:
            print(f"  📊 Final balance: {results.get('final_balance', 'N/A')}")
            print(f"  📊 Total return: {results.get('total_return', 'N/A')}")
            print(f"  📊 Number of trades: {results.get('num_trades', 'N/A')}")
        
        # تست متریک‌های عملکرد
        metrics = bf.get_performance_metrics()
        print(f"  ✅ Performance metrics: {len(metrics)} items")
        
        return True
        
    except Exception as e:
        print(f"  ❌ BacktestingFramework failed: {e}")
        traceback.print_exc()
        return False

def test_bayesian_optimizer():
    """تست بهینه‌ساز بیزی"""
    print("🔍 Testing BayesianOptimizer...")
    try:
        from optimization.bayesian import BayesianOptimizer
        
        # تابع هدف ساده
        def objective_function(params):
            x, y = params
            return -(x - 2)**2 - (y - 3)**2  # Maximum at (2, 3)
        
        # محدوده پارامترها
        bounds = {
            'x': (0, 5),
            'y': (0, 5)
        }
        
        optimizer = BayesianOptimizer(
            objective_function=objective_function,
            parameter_bounds=bounds,
            n_initial_points=3,
            n_iterations=5
        )
        
        print(f"  ✅ BayesianOptimizer created")
        
        # تست بهینه‌سازی
        results = optimizer.optimize()
        print(f"  ✅ Optimization completed")
        print(f"  📊 Best params: {results['best_params']}")
        print(f"  📊 Best score: {results['best_score']}")
        print(f"  📊 Evaluations: {results['n_evaluations']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ BayesianOptimizer failed: {e}")
        traceback.print_exc()
        return False

def test_advanced_features():
    """تست پیشنهادات پیشرفته"""
    print("🔍 Testing Advanced Features...")
    
    advanced_features = [
        ('Auto-ML', 'utils.auto_ml_system'),
        ('Federated Learning', 'utils.federated_learning_system'),
        ('Walk-Forward Analysis', 'utils.walk_forward_analysis'),
        ('Monte Carlo Simulation', 'utils.monte_carlo_simulation'),
        ('Custom Indicators', 'utils.custom_indicators_ai'),
        ('Multi-timeframe Analysis', 'utils.multi_timeframe_analysis'),
        ('Optuna Tuning', 'utils.optuna_hyperparameter_tuning'),
        ('Neural Architecture Search', 'utils.neural_architecture_search'),
        ('Automated Strategy Generation', 'utils.automated_strategy_generation'),
        ('Real-time Model Monitoring', 'utils.realtime_model_monitoring'),
        ('A/B Testing', 'utils.ab_testing_system'),
        ('Adaptive Parameters', 'utils.adaptive_parameters_system')
    ]
    
    found_features = []
    missing_features = []
    
    for feature_name, module_path in advanced_features:
        try:
            module = __import__(module_path, fromlist=[''])
            found_features.append(feature_name)
            print(f"  ✅ {feature_name}: Found")
        except ImportError:
            missing_features.append(feature_name)
            print(f"  ❌ {feature_name}: Missing")
    
    print(f"\n📊 Advanced Features Summary:")
    print(f"  ✅ Found: {len(found_features)}/{len(advanced_features)} features")
    print(f"  ❌ Missing: {len(missing_features)}/{len(advanced_features)} features")
    
    return len(found_features) >= len(advanced_features) // 2  # At least 50% found

def main():
    """تست اصلی"""
    print("🚀 Starting Complete Test of Four Main Components")
    print("=" * 70)
    
    tests = [
        ("TechnicalIndicators", test_technical_indicators),
        ("ContinualLearningSystem", test_continual_learning),
        ("BacktestingFramework", test_backtesting_framework),
        ("BayesianOptimizer", test_bayesian_optimizer),
        ("Advanced Features", test_advanced_features)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        print("-" * 50)
        
        try:
            success = test_func()
            results.append((test_name, success))
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"  {status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"  ❌ FAILED: {e}")
    
    # خلاصه نتایج
    print("\n" + "=" * 70)
    print("📊 FINAL RESULTS:")
    print("=" * 70)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 Overall Score: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is working correctly.")
    elif passed >= total * 0.8:
        print("✅ Most components working correctly.")
    elif passed >= total * 0.5:
        print("⚠️ Some components working, others need attention.")
    else:
        print("❌ Major issues detected. System needs significant fixes.")
    
    return passed / total

if __name__ == "__main__":
    score = main()
    sys.exit(0 if score >= 0.8 else 1) 