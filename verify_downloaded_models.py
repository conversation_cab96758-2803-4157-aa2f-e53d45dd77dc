#!/usr/bin/env python3
"""
🔍 Verify Downloaded Models
تایید مدل‌های دانلود شده

بررسی تمام مدل‌هایی که دانلود شدن و تست عملکردشون
"""

import torch
from transformers import AutoTokenizer, AutoModel, pipeline
from pathlib import Path
import json
from datetime import datetime

def check_model_cache():
    """بررسی cache مدل‌ها"""
    print("🗂️ Checking model cache...")
    
    # Check HuggingFace cache
    cache_dir = Path.home() / ".cache" / "huggingface" / "hub"
    
    if cache_dir.exists():
        model_dirs = list(cache_dir.glob("models--*"))
        print(f"   📦 Found {len(model_dirs)} cached models:")
        
        for model_dir in model_dirs:
            model_name = model_dir.name.replace("models--", "").replace("--", "/")
            print(f"     • {model_name}")
        
        return model_dirs
    else:
        print("   ❌ No cache directory found")
        return []

def test_model_functionality(model_name, model_path):
    """تست عملکرد یک مدل"""
    print(f"\n🧪 Testing {model_name}:")
    
    try:
        # Load model and tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModel.from_pretrained(model_path)
        
        # Test basic functionality
        test_texts = [
            "Bitcoin price is rising significantly",
            "The market shows bearish sentiment",
            "Stable trading session with low volatility"
        ]
        
        print(f"   📝 Testing with {len(test_texts)} sample texts...")
        
        results = []
        for text in test_texts:
            # Tokenize
            inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True)
            
            # Get model output
            with torch.no_grad():
                outputs = model(**inputs)
            
            # Get embeddings
            embedding = outputs.last_hidden_state.mean(dim=1)
            
            results.append({
                "text": text,
                "embedding_shape": str(embedding.shape),
                "embedding_norm": float(torch.norm(embedding).item())
            })
            
            print(f"     ✅ '{text[:30]}...' -> {embedding.shape}")
        
        # Test sentiment analysis if applicable
        if "bert" in model_name.lower():
            try:
                print("   🎭 Testing sentiment analysis...")
                classifier = pipeline("sentiment-analysis", model=model_path, tokenizer=tokenizer)
                
                sentiment_results = []
                for text in test_texts:
                    sentiment = classifier(text)[0]
                    sentiment_results.append({
                        "text": text,
                        "label": sentiment["label"],
                        "score": sentiment["score"]
                    })
                    print(f"     📊 '{text[:30]}...' -> {sentiment['label']} ({sentiment['score']:.2f})")
                
                results.extend(sentiment_results)
                
            except Exception as e:
                print(f"     ⚠️ Sentiment analysis failed: {e}")
        
        return {
            "status": "success",
            "model_name": model_name,
            "model_path": model_path,
            "results": results
        }
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return {
            "status": "failed",
            "model_name": model_name,
            "model_path": model_path,
            "error": str(e)
        }

def benchmark_models():
    """بنچمارک مدل‌ها"""
    print("\n⚡ Benchmarking Models...")
    
    models_to_test = [
        ("DistilBERT", "distilbert-base-uncased"),
        ("FinBERT", "ProsusAI/finbert"),
        ("BERT Tiny", "prajjwal1/bert-tiny")
    ]
    
    benchmark_results = []
    
    for model_name, model_path in models_to_test:
        print(f"\n🔥 Benchmarking {model_name}...")
        
        try:
            import time
            
            # Load model
            start_time = time.time()
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModel.from_pretrained(model_path)
            load_time = time.time() - start_time
            
            # Test inference speed
            test_text = "This is a benchmark test for model performance"
            
            # Warm up
            inputs = tokenizer(test_text, return_tensors="pt")
            with torch.no_grad():
                model(**inputs)
            
            # Benchmark inference
            start_time = time.time()
            num_inferences = 10
            
            for _ in range(num_inferences):
                inputs = tokenizer(test_text, return_tensors="pt")
                with torch.no_grad():
                    outputs = model(**inputs)
            
            inference_time = (time.time() - start_time) / num_inferences
            
            # Model size
            model_size = sum(p.numel() for p in model.parameters())
            model_size_mb = model_size * 4 / 1024 / 1024  # Assuming float32
            
            benchmark_result = {
                "model_name": model_name,
                "load_time": load_time,
                "inference_time": inference_time,
                "model_size_mb": model_size_mb,
                "parameters": model_size
            }
            
            benchmark_results.append(benchmark_result)
            
            print(f"     ⏱️ Load time: {load_time:.2f}s")
            print(f"     🚀 Inference time: {inference_time*1000:.1f}ms")
            print(f"     💾 Model size: {model_size_mb:.1f}MB")
            print(f"     🧮 Parameters: {model_size:,}")
            
        except Exception as e:
            print(f"     ❌ Benchmark failed: {e}")
    
    return benchmark_results

def create_model_usage_examples():
    """ایجاد مثال‌های استفاده از مدل‌ها"""
    print("\n📚 Creating usage examples...")
    
    examples = {
        "distilbert_example": """
# DistilBERT Usage Example
from transformers import AutoTokenizer, AutoModel
import torch

tokenizer = AutoTokenizer.from_pretrained('distilbert-base-uncased')
model = AutoModel.from_pretrained('distilbert-base-uncased')

text = "Bitcoin price analysis shows bullish trend"
inputs = tokenizer(text, return_tensors='pt')
outputs = model(**inputs)
embeddings = outputs.last_hidden_state.mean(dim=1)
print(f"Embedding shape: {embeddings.shape}")
""",
        
        "finbert_example": """
# FinBERT Financial Sentiment Analysis
from transformers import pipeline

classifier = pipeline("sentiment-analysis", model="ProsusAI/finbert")

texts = [
    "The company's quarterly earnings exceeded expectations",
    "Market volatility increases due to economic uncertainty",
    "Stable performance with consistent growth"
]

for text in texts:
    result = classifier(text)[0]
    print(f"Text: {text}")
    print(f"Sentiment: {result['label']} (confidence: {result['score']:.2f})")
    print("-" * 50)
""",
        
        "bert_tiny_example": """
# BERT Tiny for Fast Processing
from transformers import AutoTokenizer, AutoModel
import torch

tokenizer = AutoTokenizer.from_pretrained('prajjwal1/bert-tiny')
model = AutoModel.from_pretrained('prajjwal1/bert-tiny')

# Fast text processing
texts = ["Quick analysis", "Fast inference", "Lightweight model"]

for text in texts:
    inputs = tokenizer(text, return_tensors='pt')
    with torch.no_grad():
        outputs = model(**inputs)
    print(f"'{text}' -> {outputs.last_hidden_state.shape}")
"""
    }
    
    # Save examples
    with open("model_usage_examples.py", "w", encoding="utf-8") as f:
        f.write("# 🤖 AI Model Usage Examples\n")
        f.write("# مثال‌های استفاده از مدل‌های هوش مصنوعی\n\n")
        
        for name, example in examples.items():
            f.write(f"# {name.replace('_', ' ').title()}\n")
            f.write(example)
            f.write("\n\n")
    
    print("   ✅ Examples saved to: model_usage_examples.py")

def main():
    """تابع اصلی"""
    print("🔍 Model Verification and Testing")
    print("=" * 50)
    
    # Check cache
    cache_models = check_model_cache()
    
    # Test functionality
    print("\n🧪 Testing Model Functionality")
    print("=" * 50)
    
    test_results = []
    
    models_to_test = [
        ("DistilBERT", "distilbert-base-uncased"),
        ("FinBERT", "ProsusAI/finbert"),
        ("BERT Tiny", "prajjwal1/bert-tiny")
    ]
    
    for model_name, model_path in models_to_test:
        result = test_model_functionality(model_name, model_path)
        test_results.append(result)
    
    # Benchmark
    benchmark_results = benchmark_models()
    
    # Create usage examples
    create_model_usage_examples()
    
    # Generate report
    print("\n📊 Final Report")
    print("=" * 50)
    
    successful_tests = [r for r in test_results if r["status"] == "success"]
    failed_tests = [r for r in test_results if r["status"] == "failed"]
    
    print(f"✅ Successful model tests: {len(successful_tests)}")
    print(f"❌ Failed model tests: {len(failed_tests)}")
    
    if successful_tests:
        print("\n🏆 Working Models:")
        for result in successful_tests:
            print(f"   • {result['model_name']}: Ready for production")
    
    if benchmark_results:
        print("\n⚡ Performance Comparison:")
        sorted_by_speed = sorted(benchmark_results, key=lambda x: x["inference_time"])
        for result in sorted_by_speed:
            print(f"   • {result['model_name']}: {result['inference_time']*1000:.1f}ms, {result['model_size_mb']:.1f}MB")
    
    # Save detailed report
    report = {
        "timestamp": datetime.now().isoformat(),
        "cache_models": len(cache_models),
        "test_results": test_results,
        "benchmark_results": benchmark_results,
        "summary": {
            "total_models": len(test_results),
            "successful": len(successful_tests),
            "failed": len(failed_tests)
        }
    }
    
    with open("model_verification_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Detailed report saved to: model_verification_report.json")
    
    print("\n🎯 Conclusion:")
    if len(successful_tests) == len(test_results):
        print("🎉 All models are successfully downloaded and working!")
        print("✅ Your AI system is ready for production use!")
    else:
        print(f"⚠️ {len(failed_tests)} models need attention")

if __name__ == "__main__":
    main() 