import numpy as np
import gymnasium as gym
from gymnasium import Env
from gymnasium.spaces import Box
import pandas as pd
from ta.trend import MACD
from ta.momentum import RSIIndicator, StochasticOscillator
from utils.risk_manager import RiskManager
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import sentiment analyzer
try:
    from utils.sentiment_analyzer import AdvancedSentimentAnalyzer
    SENTIMENT_AVAILABLE = True
except ImportError:
    SENTIMENT_AVAILABLE = False


class TradingEnvV2(Env):
    """
    Enhanced Trading Environment with Sentiment Analysis Integration
    محیط معاملاتی بهبود یافته با ادغام تحلیل احساسات
    """
    
    def __init__(self, df, symbol, style, timeframe, indicators=None, **params):
        super(TradingEnvV2, self).__init__()
        self.df = df.copy()  # کپی برای جلوگیری از تغییر داده‌های اصلی
        self.symbol = symbol
        self.style = style
        self.timeframe = timeframe
        self.lot_size = params.get("lot_size", 0.1)
        self.stop_loss = params.get("stop_loss", 10)
        self.take_profit = params.get("take_profit", 20)
        self.initial_balance = params.get("initial_balance", 1000)
        self.max_daily_drawdown = params.get("max_daily_drawdown", 0.04)  # 4%
        self.max_total_drawdown = params.get("max_total_drawdown", 0.10)  # 10%
        self.balance = self.initial_balance
        self.position = 0  # 0: No position, 1: Long, -1: Short
        self.current_step = 0
        self.max_steps = len(df) - 1
        self.trade_history = []
        
        # Sentiment Analysis Integration - NEW
        self.sentiment_enabled = params.get("sentiment_enabled", True)
        self.sentiment_weight = params.get("sentiment_weight", 0.2)
        self.sentiment_analyzer = None
        self.sentiment_history = []
        self.market_sentiment_score = 0.0
        
        # Initialize sentiment analyzer
        if self.sentiment_enabled and SENTIMENT_AVAILABLE:
            try:
                self.sentiment_analyzer = AdvancedSentimentAnalyzer(
                    languages=['en'], 
                    enable_cache=True
                )
                print(f"✅ Sentiment analyzer initialized for {symbol}")
            except Exception as e:
                print(f"⚠️ Failed to initialize sentiment analyzer: {e}")
                self.sentiment_enabled = False
        else:
            self.sentiment_enabled = False
            if not SENTIMENT_AVAILABLE:
                print("⚠️ Sentiment analyzer not available")
        
        # مدیریت ریسک
        self.risk_manager = RiskManager(
            initial_balance=self.initial_balance,
            max_daily_drawdown=self.max_daily_drawdown,
            max_total_drawdown=self.max_total_drawdown
        )
        
        # آمار و تحلیل معاملات
        self.stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_profit': 0,
            'total_loss': 0,
            'max_drawdown': 0,
            'daily_drawdown': 0,
            'sentiment_influenced_trades': 0,  # NEW
            'sentiment_accuracy': 0.0  # NEW
        }

        # تعریف فضای حالت و عمل (expanded for sentiment)
        self.action_space = Box(
            low=np.array([-1]), high=np.array([1]), dtype=np.float32
        )
        
        # Expanded observation space to include sentiment features
        observation_size = 13 if self.sentiment_enabled else 10
        self.observation_space = Box(
            low=-np.inf, high=np.inf, shape=(observation_size,), dtype=np.float32
        )

        # پردازش اندیکاتورها
        self.indicators = indicators or {}
        self.process_indicators()

    def process_indicators(self):
        if self.df is not None and not self.df.empty:
            # تمیز کردن داده‌ها
            self.df = self.df.dropna()  # حذف سطرهای با NaN
            if (
                len(self.df) < 20
            ):  # حداقل طول داده برای محاسبات (بسته به دوره اندیکاتورها)
                raise ValueError("داده‌ها برای محاسبات اندیکاتورها کافی نیستند.")

            if "rsi" in self.indicators:
                rsi_period = self.indicators["rsi"].get("period", 14)
                rsi = RSIIndicator(self.df["close"], window=rsi_period)
                self.df["rsi"] = rsi.rsi()
            if "ma" in self.indicators:
                ma_period = self.indicators["ma"].get("period", 20)
                self.df["ma"] = self.df["close"].rolling(window=ma_period).mean()
            if "stochastic" in self.indicators:
                sto_period = self.indicators["stochastic"].get("period", 14)
                sto = StochasticOscillator(
                    self.df["high"], self.df["low"], self.df["close"], window=sto_period
                )
                self.df["stoch_k"] = sto.stoch()
                self.df["stoch_d"] = sto.stoch_signal()

            # دوباره حذف NaN پس از محاسبات اندیکاتورها
            self.df = self.df.dropna()
    
    def get_market_sentiment(self):
        """دریافت احساسات بازار"""
        if not self.sentiment_enabled or not self.sentiment_analyzer:
            return {
                'sentiment_score': 0.0,
                'confidence': 0.0,
                'market_sentiment': 0.0
            }
        
        try:
            # Sample news texts for the current market condition
            # In a real implementation, this would come from a news feed
            sample_news = [
                f"{self.symbol} shows strong technical momentum",
                f"Market volatility affects {self.symbol} trading",
                f"Economic indicators impact {self.symbol} outlook"
            ]
            
            # Analyze sentiment
            sentiment_results = []
            for text in sample_news:
                result = self.sentiment_analyzer.analyze(text, source='market_news')
                if result:
                    sentiment_results.append({
                        'score': result.score if result.label == 'positive' else -result.score,
                        'confidence': result.confidence
                    })
            
            if sentiment_results:
                # Calculate weighted average
                total_confidence = sum(r['confidence'] for r in sentiment_results)
                if total_confidence > 0:
                    weighted_sentiment = sum(
                        r['score'] * r['confidence'] for r in sentiment_results
                    ) / total_confidence
                    avg_confidence = total_confidence / len(sentiment_results)
                else:
                    weighted_sentiment = 0.0
                    avg_confidence = 0.0
                
                # Get market sentiment
                market_sentiment = self.sentiment_analyzer.get_market_sentiment()
                if hasattr(market_sentiment, 'overall_sentiment'):
                    market_overall = market_sentiment.overall_sentiment
                else:
                    market_overall = 0.0
                
                return {
                    'sentiment_score': weighted_sentiment,
                    'confidence': avg_confidence,
                    'market_sentiment': market_overall
                }
            
            return {
                'sentiment_score': 0.0,
                'confidence': 0.0,
                'market_sentiment': 0.0
            }
            
        except Exception as e:
            print(f"Error getting market sentiment: {e}")
            return {
                'sentiment_score': 0.0,
                'confidence': 0.0,
                'market_sentiment': 0.0
            }

    def reset(self, *, seed=None, options=None):
        self.current_step = 0
        self.balance = self.initial_balance
        # اگر seed داده شد، مقداردهی تصادفی
        if seed is not None:
            np.random.seed(seed)
        obs = self._get_observation()
        info = {}
        return obs, info

    def _get_observation(self):
        if self.current_step >= len(self.df):
            return np.zeros(self.observation_space.shape)
        
        # اطلاعات قیمت
        price_data = [
            self.df["open"].iloc[self.current_step],
            self.df["high"].iloc[self.current_step],
            self.df["low"].iloc[self.current_step],
            self.df["close"].iloc[self.current_step],
            self.balance / self.initial_balance,
        ]
        
        # اطلاعات اندیکاتورها
        indicator_data = []
        if "rsi" in self.df.columns and not np.isnan(
            self.df["rsi"].iloc[self.current_step]
        ):
            indicator_data.append(self.df["rsi"].iloc[self.current_step] / 100.0)  # نرمال‌سازی
        else:
            indicator_data.append(0.5)  # مقدار پیش‌فرض
            
        if "ma" in self.df.columns and not np.isnan(
            self.df["ma"].iloc[self.current_step]
        ):
            # نسبت قیمت به میانگین متحرک
            ma_ratio = self.df["close"].iloc[self.current_step] / self.df["ma"].iloc[self.current_step] - 1.0
            indicator_data.append(ma_ratio)
        else:
            indicator_data.append(0.0)
            
        if "stoch_k" in self.df.columns and not np.isnan(
            self.df["stoch_k"].iloc[self.current_step]
        ):
            indicator_data.append(self.df["stoch_k"].iloc[self.current_step] / 100.0)  # نرمال‌سازی
        else:
            indicator_data.append(0.5)
            
        # اطلاعات پوزیشن و ریسک
        position_data = [
            float(self.position),  # پوزیشن فعلی
            self.risk_manager.risk_metrics['daily_drawdown'],  # درادون روزانه
            self.risk_manager.risk_metrics['total_drawdown']   # درادون کلی
        ]
        
        # Sentiment data - NEW
        sentiment_data = []
        if self.sentiment_enabled:
            sentiment_info = self.get_market_sentiment()
            sentiment_data = [
                sentiment_info['sentiment_score'],  # امتیاز احساسات
                sentiment_info['confidence'],       # اطمینان
                sentiment_info['market_sentiment']  # احساسات کلی بازار
            ]
            
            # Store sentiment for history
            self.sentiment_history.append(sentiment_info)
            self.market_sentiment_score = sentiment_info['sentiment_score']
        
        # ترکیب همه داده‌ها
        obs = price_data + indicator_data + position_data + sentiment_data
        
        # اطمینان از طول صحیح
        while len(obs) < self.observation_space.shape[0]:
            obs.append(0.0)
        
        return np.array(obs[:self.observation_space.shape[0]], dtype=np.float32)
    
    def _calculate_sentiment_adjusted_reward(self, base_reward, sentiment_score, confidence):
        """محاسبه reward با تعدیل احساسات"""
        if not self.sentiment_enabled:
            return base_reward
        
        # Sentiment bonus/penalty
        sentiment_adjustment = sentiment_score * confidence * self.sentiment_weight
        
        # Apply sentiment adjustment
        adjusted_reward = base_reward + sentiment_adjustment
        
        return adjusted_reward

    def step(self, action):
        if self.current_step >= self.max_steps:
            return self._get_observation(), 0, True, {}

        current_price = self.df["close"].iloc[self.current_step]
        action = np.clip(action[0], -1, 1)

        reward = 0
        done = False
        info = {
            "balance": self.balance, 
            "trade_executed": False,
            "sentiment_score": self.market_sentiment_score,
            "sentiment_enabled": self.sentiment_enabled
        }

        # بررسی محدودیت‌های ریسک قبل از اجرای معامله
        risk_status = self.risk_manager.get_risk_status()
        
        # اگر محدودیت درادون رعایت نشده، معامله جدید انجام نشود
        can_trade = risk_status['status'] != 'stop_trading'
        
        # Get current sentiment for decision making
        current_sentiment = 0.0
        sentiment_confidence = 0.0
        if self.sentiment_enabled:
            sentiment_info = self.get_market_sentiment()
            current_sentiment = sentiment_info['sentiment_score']
            sentiment_confidence = sentiment_info['confidence']
        
        if action > 0.5 and self.position == 0 and can_trade:  # Buy
            # محاسبه حجم مناسب
            entry_price = current_price
            stop_price = entry_price - self.stop_loss / 10000  # تبدیل پیپ به قیمت
            lot_size, risk_info = self.risk_manager.calculate_position_size(
                self.symbol, entry_price, stop_price
            )
            
            if lot_size > 0:
                # باز کردن پوزیشن
                position_result = self.risk_manager.open_position(
                    symbol=self.symbol,
                    direction='buy',
                    entry_price=entry_price,
                    stop_loss=stop_price,
                    take_profit=entry_price + self.take_profit / 10000,
                    lot_size=lot_size
                )
                
                if position_result['success']:
                    self.position = 1
                    info["trade_executed"] = True
                    
                    # Track sentiment influenced trades
                    if self.sentiment_enabled and abs(current_sentiment) > 0.3:
                        self.stats['sentiment_influenced_trades'] += 1
                    
                    # Sentiment-adjusted reward for opening position
                    base_reward = 0.1  # Small reward for opening position
                    reward = self._calculate_sentiment_adjusted_reward(
                        base_reward, current_sentiment, sentiment_confidence
                    )
                    
                    self.stats['total_trades'] += 1
                    
                    # Add trade to history with sentiment info
                    self.trade_history.append({
                        'action': 'buy',
                        'price': entry_price,
                        'step': self.current_step,
                        'sentiment_score': current_sentiment,
                        'sentiment_confidence': sentiment_confidence
                    })

        elif action < -0.5 and self.position == 0 and can_trade:  # Sell
            # محاسبه حجم مناسب
            entry_price = current_price
            stop_price = entry_price + self.stop_loss / 10000  # تبدیل پیپ به قیمت
            lot_size, risk_info = self.risk_manager.calculate_position_size(
                self.symbol, entry_price, stop_price
            )
            
            if lot_size > 0:
                # باز کردن پوزیشن
                position_result = self.risk_manager.open_position(
                    symbol=self.symbol,
                    direction='sell',
                    entry_price=entry_price,
                    stop_loss=stop_price,
                    take_profit=entry_price - self.take_profit / 10000,
                    lot_size=lot_size
                )
                
                if position_result['success']:
                    self.position = -1
                    info["trade_executed"] = True
                    
                    # Track sentiment influenced trades
                    if self.sentiment_enabled and abs(current_sentiment) > 0.3:
                        self.stats['sentiment_influenced_trades'] += 1
                    
                    # Sentiment-adjusted reward for opening position
                    base_reward = 0.1  # Small reward for opening position
                    reward = self._calculate_sentiment_adjusted_reward(
                        base_reward, -current_sentiment, sentiment_confidence  # Negative for short
                    )
                    
                    self.stats['total_trades'] += 1
                    
                    # Add trade to history with sentiment info
                    self.trade_history.append({
                        'action': 'sell',
                        'price': entry_price,
                        'step': self.current_step,
                        'sentiment_score': current_sentiment,
                        'sentiment_confidence': sentiment_confidence
                    })

        # بررسی بستن پوزیشن
        if self.position != 0:
            close_position = self.risk_manager.should_close_position(
                self.symbol, current_price
            )
            
            if close_position['should_close']:
                # Close position
                close_result = self.risk_manager.close_position(
                    self.symbol, current_price
                )
                
                if close_result['success']:
                    profit_loss = close_result['profit_loss']
                    self.balance += profit_loss
                    
                    # Update statistics
                    if profit_loss > 0:
                        self.stats['winning_trades'] += 1
                        self.stats['total_profit'] += profit_loss
                    else:
                        self.stats['losing_trades'] += 1
                        self.stats['total_loss'] += abs(profit_loss)
                    
                    # Sentiment-adjusted reward for closing position
                    base_reward = profit_loss / self.initial_balance * 100  # Scale reward
                    reward = self._calculate_sentiment_adjusted_reward(
                        base_reward, current_sentiment, sentiment_confidence
                    )
                    
                    self.position = 0
                    info["trade_executed"] = True
                    info["profit_loss"] = profit_loss
                    info["close_reason"] = close_position['reason']

        # به‌روزرسانی metrics ریسک
        self.risk_manager.update_drawdown(self.balance)
        
        # بررسی پایان episode
        if self.current_step >= self.max_steps:
            done = True
            
        # محاسبه accuracy احساسات
        if self.sentiment_enabled and len(self.sentiment_history) > 0:
            # Simple accuracy calculation based on sentiment vs price movement
            # This is a placeholder - in practice, you'd need more sophisticated metrics
            correct_predictions = sum(1 for s in self.sentiment_history if s['sentiment_score'] != 0)
            total_predictions = len(self.sentiment_history)
            self.stats['sentiment_accuracy'] = correct_predictions / total_predictions if total_predictions > 0 else 0.0

        self.current_step += 1
        return self._get_observation(), reward, done, info

    def render(self, mode="human"):
        pass

    def close(self):
        pass

    def set_reward_fn(self, new_reward_fn):
        self.reward_fn = new_reward_fn
    
    def get_sentiment_stats(self):
        """دریافت آمار احساسات"""
        if not self.sentiment_enabled:
            return {"sentiment_enabled": False}
        
        return {
            "sentiment_enabled": True,
            "sentiment_influenced_trades": self.stats['sentiment_influenced_trades'],
            "sentiment_accuracy": self.stats['sentiment_accuracy'],
            "total_sentiment_history": len(self.sentiment_history),
            "current_sentiment_score": self.market_sentiment_score
        }

# Keep original class for backward compatibility
class TradingEnv(TradingEnvV2):
    """Backward compatibility wrapper"""
    def __init__(self, *args, **kwargs):
        # Disable sentiment by default for backward compatibility
        kwargs.setdefault('sentiment_enabled', False)
        super().__init__(*args, **kwargs)

# Export both classes
__all__ = ['TradingEnv', 'TradingEnvV2']
