#!/usr/bin/env python3
"""
🎯 FINAL SYSTEM STATUS REPORT
گزارش نهایی وضعیت سیستم
"""

def main():
    print("=" * 60)
    print("🎯 FINAL SYSTEM STATUS REPORT")
    print("=" * 60)
    
    status = {
        "core_system": False,
        "ai_models": False,
        "trading_system": False,
        "main_system": False,
        "all_ready": False
    }
    
    # Test Core System
    try:
        from core.config import get_config
        from core.logger import get_logger
        from core.base import registry
        status["core_system"] = True
        print("✅ Core System: OPERATIONAL")
    except Exception as e:
        print(f"❌ Core System: {e}")
    
    # Test AI Models
    try:
        from ai_models import (
            ModelEnsemble, WeightedEnsemble, VotingEnsemble,
            DocumentAnalyzer, FinBERTModel, SentimentEnsemble,
            initialize_models, get_model, get_available_models
        )
        status["ai_models"] = True
        print("✅ AI Models: OPERATIONAL")
    except Exception as e:
        print(f"❌ AI Models: {e}")
    
    # Test Trading System
    try:
        from models.unified_trading_system import UnifiedTradingSystem
        from env.trading_env import TradingEnvV2
        from portfolio.portfolio_manager import PortfolioManagerV2
        status["trading_system"] = True
        print("✅ Trading System: OPERATIONAL")
    except Exception as e:
        print(f"❌ Trading System: {e}")
    
    # Test Main System
    try:
        from main_new import TradingSystemManager
        manager = TradingSystemManager()
        status["main_system"] = True
        print("✅ Main System: OPERATIONAL")
    except Exception as e:
        print(f"❌ Main System: {e}")
    
    # Overall Status
    status["all_ready"] = all(status.values())
    
    print("=" * 60)
    print("📊 COMPONENT STATUS:")
    print(f"   Core System: {'✅ READY' if status['core_system'] else '❌ FAILED'}")
    print(f"   AI Models: {'✅ READY' if status['ai_models'] else '❌ FAILED'}")
    print(f"   Trading System: {'✅ READY' if status['trading_system'] else '❌ FAILED'}")
    print(f"   Main System: {'✅ READY' if status['main_system'] else '❌ FAILED'}")
    print("=" * 60)
    
    if status["all_ready"]:
        print("🎉 SYSTEM STATUS: 100% OPERATIONAL!")
        print("🚀 READY FOR TRAINING & OPERATIONAL PHASE!")
        print()
        print("🔧 NEXT STEPS:")
        print("   1. Run training modules")
        print("   2. Start operational trading")
        print("   3. Monitor system performance")
        print("   4. Continuous learning and optimization")
    else:
        print("⚠️  SYSTEM STATUS: NEEDS ATTENTION")
        print("🔧 Please fix the failed components above")
    
    print("=" * 60)
    return status["all_ready"]

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 