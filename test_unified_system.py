"""
تست جامع سیستم یکپارچه معاملاتی
Comprehensive Test for Unified Trading System
"""

import pandas as pd
import numpy as np
import os
import sys
import json
import logging
from datetime import datetime, timedelta
import unittest
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.unified_trading_system import (
    UnifiedTradingSystem, 
    FeatureEnhancer, 
    IntelligentWeightingSystem,
    UnifiedSignal,
    PerformanceMetrics
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestUnifiedTradingSystem(unittest.TestCase):
    """تست کلاس سیستم یکپارچه معاملاتی"""
    
    def setUp(self):
        """تنظیمات اولیه تست"""
        self.test_db_path = "test_unified_system.db"
        self.symbols = ["EURUSD", "GBPUSD"]
        
        # حذف فایل پایگاه داده تست در صورت وجود
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
    
    def tearDown(self):
        """پاکسازی بعد از تست"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
    
    def create_sample_data(self, length=100):
        """ایجاد داده‌های نمونه برای تست"""
        dates = pd.date_range(start='2024-01-01', periods=length, freq='H')
        
        # ایجاد داده‌های قیمت تصادفی
        np.random.seed(42)
        base_price = 1.1000
        
        prices = []
        current_price = base_price
        
        for i in range(length):
            change = np.random.normal(0, 0.001)  # تغییرات کوچک
            current_price *= (1 + change)
            prices.append(current_price)
        
        df = pd.DataFrame({
            'datetime': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.0005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.0005))) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000, 10000, length)
        })
        
        return df
    
    @patch('models.unified_trading_system.AdaptivePlutusSystem')
    def test_system_initialization(self, mock_plutus):
        """تست مقداردهی اولیه سیستم"""
        
        # Mock Plutus system
        mock_plutus_instance = Mock()
        mock_plutus.return_value = mock_plutus_instance
        
        # ایجاد سیستم
        system = UnifiedTradingSystem(db_path=self.test_db_path)
        
        # بررسی مقداردهی اولیه
        self.assertIsNotNone(system.rl_factory)
        self.assertIsNotNone(system.meta_learner)
        self.assertIsNotNone(system.feature_enhancer)
        self.assertIsNotNone(system.weighting_system)
        self.assertEqual(system.db_path, self.test_db_path)
        
        logger.info("✅ تست مقداردهی اولیه سیستم موفق")
    
    def test_feature_enhancer(self):
        """تست تقویت‌کننده ویژگی‌ها"""
        
        enhancer = FeatureEnhancer()
        
        # ایجاد داده‌های نمونه
        market_data = self.create_sample_data(50)
        
        # پیش‌بینی نمونه پلوتوس
        plutus_prediction = {
            'forecast': {
                'mean': [1.1050, 1.1055, 1.1060],
                'quantiles': {
                    '0.1': [1.1040, 1.1045, 1.1050],
                    '0.9': [1.1060, 1.1065, 1.1070]
                }
            },
            'confidence': 0.75,
            'trend': 'bullish',
            'trend_strength': 0.8,
            'volatility_forecast': 0.015
        }
        
        # تقویت ویژگی‌ها
        enhanced_features = enhancer.enhance_features(
            market_data, plutus_prediction, "EURUSD"
        )
        
        # بررسی وجود ویژگی‌های اساسی
        self.assertIn('current_price', enhanced_features)
        self.assertIn('plutus_confidence', enhanced_features)
        self.assertIn('plutus_price_prediction', enhanced_features)
        self.assertIn('volatility_regime', enhanced_features)
        self.assertIn('market_phase', enhanced_features)
        
        # بررسی مقادیر معقول
        self.assertGreater(enhanced_features['plutus_confidence'], 0)
        self.assertLessEqual(enhanced_features['plutus_confidence'], 1)
        
        logger.info("✅ تست تقویت‌کننده ویژگی‌ها موفق")
    
    def test_intelligent_weighting_system(self):
        """تست سیستم وزن‌دهی هوشمند"""
        
        weighting_system = IntelligentWeightingSystem(db_path=self.test_db_path)
        
        # سیگنال‌های نمونه
        rl_signal = {
            'action': 'buy',
            'confidence': 0.8
        }
        
        plutus_signal = {
            'trend': 'bullish',
            'confidence': 0.7
        }
        
        market_context = {
            'volatility_regime': 1,
            'trend_regime': 2
        }
        
        # محاسبه سیگنال ترکیبی
        combined_signal = weighting_system.calculate_combined_signal(
            rl_signal, plutus_signal, market_context
        )
        
        # بررسی نتایج
        self.assertIn('final_action', combined_signal)
        self.assertIn('combined_confidence', combined_signal)
        self.assertIn('agreement', combined_signal)
        
        # بررسی توافق (هر دو مدل bullish/buy هستند)
        self.assertTrue(combined_signal['agreement'])
        self.assertEqual(combined_signal['final_action'], 'buy')
        
        logger.info("✅ تست سیستم وزن‌دهی هوشمند موفق")
    
    def test_disagreement_handling(self):
        """تست مدیریت عدم توافق بین مدل‌ها"""
        
        weighting_system = IntelligentWeightingSystem(db_path=self.test_db_path)
        
        # سیگنال‌های متضاد
        rl_signal = {
            'action': 'buy',
            'confidence': 0.6
        }
        
        plutus_signal = {
            'trend': 'bearish',
            'confidence': 0.8
        }
        
        market_context = {
            'volatility_regime': 1,
            'trend_regime': 1
        }
        
        # محاسبه سیگنال ترکیبی
        combined_signal = weighting_system.calculate_combined_signal(
            rl_signal, plutus_signal, market_context
        )
        
        # بررسی عدم توافق
        self.assertFalse(combined_signal['agreement'])
        
        # باید مدل با اطمینان بالاتر انتخاب شود (Plutus)
        self.assertEqual(combined_signal['final_action'], 'sell')
        
        # اطمینان ترکیبی باید کاهش یابد
        self.assertLess(combined_signal['combined_confidence'], 0.8)
        
        logger.info("✅ تست مدیریت عدم توافق موفق")
    
    @patch('models.unified_trading_system.AdaptivePlutusSystem')
    def test_unified_signal_generation(self, mock_plutus):
        """تست تولید سیگنال یکپارچه"""
        
        # Mock Plutus system
        mock_plutus_instance = Mock()
        mock_plutus.return_value = mock_plutus_instance
        
        # Mock پاسخ Plutus
        mock_plutus_instance.get_adaptive_signal.return_value = {
            'trend': 'bullish',
            'confidence': 0.75,
            'forecast': {
                'mean': [1.1050, 1.1055],
                'quantiles': {'0.1': [1.1040], '0.9': [1.1060]}
            }
        }
        
        # ایجاد سیستم
        system = UnifiedTradingSystem(db_path=self.test_db_path)
        
        # Mock ensemble model
        mock_ensemble = Mock()
        mock_ensemble.predict.return_value = (np.array([1]), None)  # Buy action
        system.ensemble_model = mock_ensemble
        
        # ایجاد داده‌های نمونه
        market_data = self.create_sample_data(50)
        
        # تولید سیگنال یکپارچه
        unified_signal = system.get_unified_signal(
            symbol="EURUSD",
            timeframe="H1",
            market_data=market_data
        )
        
        # بررسی نتایج
        self.assertIsInstance(unified_signal, UnifiedSignal)
        self.assertEqual(unified_signal.symbol, "EURUSD")
        self.assertEqual(unified_signal.timeframe, "H1")
        self.assertIn(unified_signal.final_action, ['buy', 'sell', 'hold'])
        self.assertGreaterEqual(unified_signal.final_confidence, 0)
        self.assertLessEqual(unified_signal.final_confidence, 1)
        
        logger.info("✅ تست تولید سیگنال یکپارچه موفق")
    
    def test_performance_tracking(self):
        """تست ردیابی عملکرد"""
        
        weighting_system = IntelligentWeightingSystem(db_path=self.test_db_path)
        
        # به‌روزرسانی وزن‌ها بر اساس عملکرد
        performance_data = {
            'rl_performance': 0.05,  # 5% سود
            'plutus_performance': 0.03  # 3% سود
        }
        
        # وزن‌های اولیه
        initial_rl_weight = weighting_system.weights['rl_weight']
        initial_plutus_weight = weighting_system.weights['plutus_weight']
        
        # به‌روزرسانی وزن‌ها
        weighting_system.update_weights_from_performance("EURUSD", performance_data)
        
        # بررسی تغییر وزن‌ها
        new_rl_weight = weighting_system.weights['rl_weight']
        new_plutus_weight = weighting_system.weights['plutus_weight']
        
        # وزن‌ها باید تغییر کرده باشند
        self.assertNotEqual(initial_rl_weight, new_rl_weight)
        self.assertNotEqual(initial_plutus_weight, new_plutus_weight)
        
        # مجموع وزن‌ها باید 1 باشد
        self.assertAlmostEqual(new_rl_weight + new_plutus_weight, 1.0, places=3)
        
        logger.info("✅ تست ردیابی عملکرد موفق")
    
    def test_market_context_analysis(self):
        """تست تحلیل زمینه بازار"""
        
        system = UnifiedTradingSystem(db_path=self.test_db_path)
        
        # ایجاد داده‌های پرنوسان
        market_data = self.create_sample_data(100)
        
        # افزایش نوسانات
        market_data['close'] = market_data['close'] * (1 + np.random.normal(0, 0.02, 100))
        
        plutus_signal = {
            'trend': 'bullish',
            'confidence': 0.6
        }
        
        # تحلیل زمینه بازار
        market_context = system._analyze_market_context(market_data, plutus_signal)
        
        # بررسی نتایج
        self.assertIn('regime', market_context)
        self.assertIn('volatility_level', market_context)
        self.assertIn('risk_score', market_context)
        
        # بررسی سطح نوسانات
        self.assertIn(market_context['volatility_level'], ['low', 'medium', 'high'])
        
        logger.info("✅ تست تحلیل زمینه بازار موفق")


def run_integration_test():
    """اجرای تست یکپارچه‌سازی کامل"""
    
    logger.info("🚀 شروع تست یکپارچه‌سازی کامل...")
    
    try:
        # ایجاد داده‌های نمونه
        test_data = {
            'EURUSD': pd.DataFrame({
                'datetime': pd.date_range('2024-01-01', periods=100, freq='H'),
                'open': np.random.uniform(1.08, 1.12, 100),
                'high': np.random.uniform(1.08, 1.12, 100),
                'low': np.random.uniform(1.08, 1.12, 100),
                'close': np.random.uniform(1.08, 1.12, 100),
                'volume': np.random.randint(1000, 10000, 100)
            })
        }
        
        # Mock Plutus system
        with patch('models.unified_trading_system.AdaptivePlutusSystem') as mock_plutus:
            mock_plutus_instance = Mock()
            mock_plutus.return_value = mock_plutus_instance
            
            # Mock responses
            mock_plutus_instance.get_adaptive_signal.return_value = {
                'trend': 'bullish',
                'confidence': 0.8,
                'forecast': {
                    'mean': [1.1050, 1.1055, 1.1060],
                    'quantiles': {
                        '0.1': [1.1040, 1.1045, 1.1050],
                        '0.9': [1.1060, 1.1065, 1.1070]
                    }
                },
                'trend_strength': 0.7,
                'volatility_forecast': 0.012
            }
            
            # ایجاد سیستم یکپارچه
            system = UnifiedTradingSystem(db_path="integration_test.db")
            
            # Mock ensemble model
            mock_ensemble = Mock()
            mock_ensemble.predict.return_value = (np.array([1]), None)
            system.ensemble_model = mock_ensemble
            
            # تست تولید سیگنال
            signal = system.get_unified_signal(
                symbol="EURUSD",
                timeframe="H1",
                market_data=test_data['EURUSD']
            )
            
            # بررسی نتایج
            assert signal.symbol == "EURUSD"
            assert signal.final_action in ['buy', 'sell', 'hold']
            assert 0 <= signal.final_confidence <= 1
            
            # تست به‌روزرسانی عملکرد
            system.update_performance(
                symbol="EURUSD",
                signal=signal,
                actual_outcome="bullish",
                profit_loss=0.02
            )
            
            # دریافت خلاصه عملکرد
            performance = system.get_performance_summary("EURUSD")
            assert 'total_trades' in performance
            assert 'win_rate' in performance
            
            # پاکسازی
            system.stop_system()
            
            # حذف فایل تست
            if os.path.exists("integration_test.db"):
                os.remove("integration_test.db")
            
            logger.info("✅ تست یکپارچه‌سازی کامل موفق!")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ خطا در تست یکپارچه‌سازی: {str(e)}")
        return False


def run_performance_benchmark():
    """اجرای بنچمارک عملکرد"""
    
    logger.info("📊 شروع بنچمارک عملکرد...")
    
    try:
        # ایجاد داده‌های بزرگ‌تر
        large_data = pd.DataFrame({
            'datetime': pd.date_range('2024-01-01', periods=1000, freq='H'),
            'open': np.random.uniform(1.08, 1.12, 1000),
            'high': np.random.uniform(1.08, 1.12, 1000),
            'low': np.random.uniform(1.08, 1.12, 1000),
            'close': np.random.uniform(1.08, 1.12, 1000),
            'volume': np.random.randint(1000, 10000, 1000)
        })
        
        # Mock Plutus system
        with patch('models.unified_trading_system.AdaptivePlutusSystem') as mock_plutus:
            mock_plutus_instance = Mock()
            mock_plutus.return_value = mock_plutus_instance
            
            mock_plutus_instance.get_adaptive_signal.return_value = {
                'trend': 'bullish',
                'confidence': 0.75,
                'forecast': {'mean': [1.1050]},
                'trend_strength': 0.6
            }
            
            # ایجاد سیستم
            system = UnifiedTradingSystem(db_path="benchmark_test.db")
            
            # Mock ensemble
            mock_ensemble = Mock()
            mock_ensemble.predict.return_value = (np.array([0.5]), None)
            system.ensemble_model = mock_ensemble
            
            # اندازه‌گیری زمان
            start_time = datetime.now()
            
            # تولید چندین سیگنال
            signals = []
            for i in range(10):
                signal = system.get_unified_signal(
                    symbol="EURUSD",
                    timeframe="H1",
                    market_data=large_data[i*100:(i+1)*100]
                )
                signals.append(signal)
            
            end_time = datetime.now()
            elapsed = (end_time - start_time).total_seconds()
            
            # گزارش عملکرد
            logger.info(f"⏱️ زمان تولید 10 سیگنال: {elapsed:.2f} ثانیه")
            logger.info(f"📈 میانگین زمان هر سیگنال: {elapsed/10:.3f} ثانیه")
            
            # بررسی کیفیت سیگنال‌ها
            confidences = [s.final_confidence for s in signals]
            avg_confidence = np.mean(confidences)
            
            logger.info(f"🎯 میانگین اطمینان سیگنال‌ها: {avg_confidence:.3f}")
            
            # پاکسازی
            system.stop_system()
            
            if os.path.exists("benchmark_test.db"):
                os.remove("benchmark_test.db")
            
            logger.info("✅ بنچمارک عملکرد موفق!")
            
            return {
                'total_time': elapsed,
                'avg_time_per_signal': elapsed/10,
                'avg_confidence': avg_confidence,
                'signals_generated': len(signals)
            }
            
    except Exception as e:
        logger.error(f"❌ خطا در بنچمارک: {str(e)}")
        return None


if __name__ == "__main__":
    # اجرای تست‌های واحد
    logger.info("🧪 شروع تست‌های واحد...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # اجرای تست یکپارچه‌سازی
    integration_success = run_integration_test()
    
    # اجرای بنچمارک
    benchmark_results = run_performance_benchmark()
    
    # گزارش نهایی
    logger.info("\n" + "="*60)
    logger.info("📋 گزارش نهایی تست‌ها")
    logger.info("="*60)
    
    if integration_success:
        logger.info("✅ تست یکپارچه‌سازی: موفق")
    else:
        logger.info("❌ تست یکپارچه‌سازی: ناموفق")
    
    if benchmark_results:
        logger.info("✅ بنچمارک عملکرد: موفق")
        logger.info(f"   - زمان کل: {benchmark_results['total_time']:.2f}s")
        logger.info(f"   - میانگین زمان: {benchmark_results['avg_time_per_signal']:.3f}s")
        logger.info(f"   - میانگین اطمینان: {benchmark_results['avg_confidence']:.3f}")
    else:
        logger.info("❌ بنچمارک عملکرد: ناموفق")
    
    logger.info("\n🎉 تست‌های سیستم یکپارچه تکمیل شد!") 