#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Particle Swarm Optimization (PSO)
بهینه‌سازی ازدحام ذرات
"""

import numpy as np
import pandas as pd
from typing import Callable, Dict, List, Any, Optional, Tuple
import random
import logging
from datetime import datetime
import json

# تنظیم logging
logger = logging.getLogger(__name__)

class Particle:
    """ذره در الگوریتم PSO"""
    
    def __init__(self, dimensions: int, bounds: List[Tuple[float, float]]):
        self.dimensions = dimensions
        self.bounds = bounds
        
        # موقعیت تصادفی
        self.position = np.array([
            random.uniform(bound[0], bound[1]) 
            for bound in bounds
        ])
        
        # سرعت تصادفی
        self.velocity = np.array([
            random.uniform(-1, 1) 
            for _ in range(dimensions)
        ])
        
        # بهترین موقعیت شخصی
        self.best_position = self.position.copy()
        self.best_fitness = float('inf')
        
        # فیتنس فعلی
        self.fitness = float('inf')
    
    def update_velocity(self, global_best_position: np.ndarray, 
                       w: float, c1: float, c2: float):
        """به‌روزرسانی سرعت"""
        r1 = random.random()
        r2 = random.random()
        
        # محاسبه سرعت جدید
        cognitive_component = c1 * r1 * (self.best_position - self.position)
        social_component = c2 * r2 * (global_best_position - self.position)
        
        self.velocity = w * self.velocity + cognitive_component + social_component
        
        # محدود کردن سرعت
        max_velocity = 0.1 * np.array([bound[1] - bound[0] for bound in self.bounds])
        self.velocity = np.clip(self.velocity, -max_velocity, max_velocity)
    
    def update_position(self):
        """به‌روزرسانی موقعیت"""
        self.position += self.velocity
        
        # اعمال محدودیت‌ها
        for i, (min_val, max_val) in enumerate(self.bounds):
            if self.position[i] < min_val:
                self.position[i] = min_val
                self.velocity[i] = 0
            elif self.position[i] > max_val:
                self.position[i] = max_val
                self.velocity[i] = 0
    
    def evaluate(self, fitness_function: Callable):
        """ارزیابی فیتنس"""
        self.fitness = fitness_function(self.position)
        
        # به‌روزرسانی بهترین موقعیت شخصی
        if self.fitness < self.best_fitness:
            self.best_fitness = self.fitness
            self.best_position = self.position.copy()

class ParticleSwarmOptimizer:
    """بهینه‌ساز ازدحام ذرات"""
    
    def __init__(self, 
                 fitness_function: Callable,
                 dimensions: int,
                 bounds: List[Tuple[float, float]],
                 num_particles: int = 30,
                 max_iterations: int = 100,
                 w: float = 0.9,
                 c1: float = 2.0,
                 c2: float = 2.0,
                 w_min: float = 0.4,
                 w_max: float = 0.9):
        
        self.fitness_function = fitness_function
        self.dimensions = dimensions
        self.bounds = bounds
        self.num_particles = num_particles
        self.max_iterations = max_iterations
        
        # پارامترهای PSO
        self.w = w  # وزن اینرسی
        self.c1 = c1  # ضریب شناختی
        self.c2 = c2  # ضریب اجتماعی
        self.w_min = w_min
        self.w_max = w_max
        
        # ذرات
        self.particles = []
        self.global_best_position = None
        self.global_best_fitness = float('inf')
        
        # تاریخچه
        self.fitness_history = []
        self.position_history = []
        
        # آمار
        self.convergence_iteration = None
        self.total_evaluations = 0
        
        self._initialize_swarm()
    
    def _initialize_swarm(self):
        """مقداردهی اولیه ازدحام"""
        logger.info(f"Initializing swarm with {self.num_particles} particles")
        
        self.particles = [
            Particle(self.dimensions, self.bounds)
            for _ in range(self.num_particles)
        ]
        
        # ارزیابی اولیه
        for particle in self.particles:
            particle.evaluate(self.fitness_function)
            self.total_evaluations += 1
            
            # به‌روزرسانی بهترین جهانی
            if particle.fitness < self.global_best_fitness:
                self.global_best_fitness = particle.fitness
                self.global_best_position = particle.position.copy()
    
    def optimize(self) -> Dict[str, Any]:
        """اجرای بهینه‌سازی"""
        logger.info(f"Starting PSO optimization for {self.max_iterations} iterations")
        
        for iteration in range(self.max_iterations):
            # به‌روزرسانی وزن اینرسی
            self.w = self.w_max - (self.w_max - self.w_min) * iteration / self.max_iterations
            
            # به‌روزرسانی ذرات
            for particle in self.particles:
                # به‌روزرسانی سرعت
                particle.update_velocity(
                    self.global_best_position, 
                    self.w, self.c1, self.c2
                )
                
                # به‌روزرسانی موقعیت
                particle.update_position()
                
                # ارزیابی
                particle.evaluate(self.fitness_function)
                self.total_evaluations += 1
                
                # به‌روزرسانی بهترین جهانی
                if particle.fitness < self.global_best_fitness:
                    self.global_best_fitness = particle.fitness
                    self.global_best_position = particle.position.copy()
                    
                    if self.convergence_iteration is None:
                        self.convergence_iteration = iteration
            
            # ذخیره تاریخچه
            self.fitness_history.append(self.global_best_fitness)
            self.position_history.append(self.global_best_position.copy())
            
            # گزارش پیشرفت
            if iteration % 10 == 0:
                logger.info(f"Iteration {iteration}: Best fitness = {self.global_best_fitness:.6f}")
            
            # بررسی همگرایی
            if self._check_convergence(iteration):
                logger.info(f"Converged at iteration {iteration}")
                break
        
        # نتایج نهایی
        result = {
            'best_position': self.global_best_position.tolist(),
            'best_fitness': self.global_best_fitness,
            'convergence_iteration': self.convergence_iteration,
            'total_evaluations': self.total_evaluations,
            'fitness_history': self.fitness_history,
            'final_swarm_diversity': self._calculate_diversity(),
            'optimization_time': datetime.now().isoformat()
        }
        
        logger.info(f"PSO optimization completed. Best fitness: {self.global_best_fitness:.6f}")
        return result
    
    def _check_convergence(self, iteration: int, tolerance: float = 1e-6) -> bool:
        """بررسی همگرایی"""
        if len(self.fitness_history) < 10:
            return False
        
        # بررسی تغییرات در 10 تکرار اخیر
        recent_fitness = self.fitness_history[-10:]
        fitness_change = max(recent_fitness) - min(recent_fitness)
        
        return fitness_change < tolerance
    
    def _calculate_diversity(self) -> float:
        """محاسبه تنوع ازدحام"""
        if len(self.particles) < 2:
            return 0.0
        
        positions = np.array([particle.position for particle in self.particles])
        center = np.mean(positions, axis=0)
        
        # محاسبه میانگین فاصله از مرکز
        distances = [np.linalg.norm(pos - center) for pos in positions]
        return np.mean(distances)
    
    def get_swarm_statistics(self) -> Dict[str, Any]:
        """آمار ازدحام"""
        fitnesses = [particle.fitness for particle in self.particles]
        
        return {
            'best_fitness': min(fitnesses),
            'worst_fitness': max(fitnesses),
            'average_fitness': np.mean(fitnesses),
            'fitness_std': np.std(fitnesses),
            'diversity': self._calculate_diversity(),
            'convergence_rate': self.convergence_iteration / self.max_iterations if self.convergence_iteration else 1.0
        }
    
    def plot_convergence(self, save_path: str = None):
        """رسم منحنی همگرایی"""
        try:
            import matplotlib.pyplot as plt
            
            plt.figure(figsize=(10, 6))
            plt.plot(self.fitness_history, 'b-', linewidth=2)
            plt.title('PSO Convergence Curve')
            plt.xlabel('Iteration')
            plt.ylabel('Best Fitness')
            plt.grid(True, alpha=0.3)
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"Convergence plot saved to {save_path}")
            else:
                plt.show()
            
            plt.close()
            
        except ImportError:
            logger.warning("matplotlib not available for plotting")

class HybridPSO(ParticleSwarmOptimizer):
    """PSO ترکیبی با local search"""
    
    def __init__(self, *args, local_search_probability: float = 0.1, **kwargs):
        super().__init__(*args, **kwargs)
        self.local_search_probability = local_search_probability
    
    def _local_search(self, particle: Particle) -> bool:
        """جستجوی محلی"""
        original_position = particle.position.copy()
        original_fitness = particle.fitness
        
        # جستجوی در همسایگی
        for _ in range(5):  # تعداد تلاش‌های محلی
            # ایجاد موقعیت جدید در همسایگی
            perturbation = np.random.normal(0, 0.1, self.dimensions)
            new_position = original_position + perturbation
            
            # اعمال محدودیت‌ها
            for i, (min_val, max_val) in enumerate(self.bounds):
                new_position[i] = np.clip(new_position[i], min_val, max_val)
            
            # ارزیابی
            new_fitness = self.fitness_function(new_position)
            self.total_evaluations += 1
            
            # به‌روزرسانی در صورت بهبود
            if new_fitness < original_fitness:
                particle.position = new_position
                particle.fitness = new_fitness
                return True
        
        return False
    
    def optimize(self) -> Dict[str, Any]:
        """بهینه‌سازی ترکیبی"""
        logger.info("Starting Hybrid PSO optimization")
        
        # اجرای PSO معمولی
        result = super().optimize()
        
        # اعمال local search
        improved_particles = 0
        for particle in self.particles:
            if random.random() < self.local_search_probability:
                if self._local_search(particle):
                    improved_particles += 1
                    
                    # به‌روزرسانی بهترین جهانی
                    if particle.fitness < self.global_best_fitness:
                        self.global_best_fitness = particle.fitness
                        self.global_best_position = particle.position.copy()
        
        result['improved_particles'] = improved_particles
        result['best_fitness'] = self.global_best_fitness
        result['best_position'] = self.global_best_position.tolist()
        
        logger.info(f"Hybrid PSO completed. Improved {improved_particles} particles")
        return result

# توابع تست
def sphere_function(x: np.ndarray) -> float:
    """تابع کره (برای تست)"""
    return np.sum(x**2)

def rosenbrock_function(x: np.ndarray) -> float:
    """تابع Rosenbrock (برای تست)"""
    return np.sum(100 * (x[1:] - x[:-1]**2)**2 + (1 - x[:-1])**2)

def rastrigin_function(x: np.ndarray) -> float:
    """تابع Rastrigin (برای تست)"""
    n = len(x)
    return 10 * n + np.sum(x**2 - 10 * np.cos(2 * np.pi * x))

def main():
    """تست الگوریتم PSO"""
    print("🔍 Testing Particle Swarm Optimization")
    print("=" * 50)
    
    # تست با تابع کره
    print("\n🎯 Testing with Sphere Function")
    
    pso = ParticleSwarmOptimizer(
        fitness_function=sphere_function,
        dimensions=5,
        bounds=[(-10, 10)] * 5,
        num_particles=30,
        max_iterations=100
    )
    
    result = pso.optimize()
    
    print(f"Best position: {result['best_position']}")
    print(f"Best fitness: {result['best_fitness']:.6f}")
    print(f"Convergence iteration: {result['convergence_iteration']}")
    
    # تست PSO ترکیبی
    print("\n🔄 Testing Hybrid PSO")
    
    hybrid_pso = HybridPSO(
        fitness_function=rosenbrock_function,
        dimensions=3,
        bounds=[(-5, 5)] * 3,
        num_particles=20,
        max_iterations=50,
        local_search_probability=0.2
    )
    
    hybrid_result = hybrid_pso.optimize()
    
    print(f"Hybrid PSO Best fitness: {hybrid_result['best_fitness']:.6f}")
    print(f"Improved particles: {hybrid_result['improved_particles']}")
    
    # ذخیره نتایج
    with open('pso_results.json', 'w') as f:
        json.dump({
            'standard_pso': result,
            'hybrid_pso': hybrid_result
        }, f, indent=2)
    
    print("\n💾 Results saved to pso_results.json")

if __name__ == "__main__":
    main()
