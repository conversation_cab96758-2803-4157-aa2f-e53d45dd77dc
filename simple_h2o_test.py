#!/usr/bin/env python3
"""
🧪 تست ساده برای بررسی حل مشکل hyperparameter_suggestions
"""

import pandas as pd
import numpy as np
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_brain_analysis_keys():
    """تست کلیدهای تحلیل مغزها"""
    print("🧪 Testing Brain Analysis Keys Fix")
    print("=" * 50)
    
    # Create sample data
    data = pd.DataFrame({
        'close': np.random.uniform(1.1000, 1.1100, 50),
        'volume': np.random.randint(1000, 10000, 50),
        'rsi': np.random.uniform(20, 80, 50)
    })
    
    print(f"📊 Sample data: {len(data)} rows")
    
    # Import and test the fixed system
    try:
        from fixed_ultimate_main import MultiBrainSystem
        print("✅ Successfully imported MultiBrainSystem")
        
        # Initialize system
        multi_brain = MultiBrainSystem()
        print("✅ MultiBrainSystem initialized")
        
        # Test analysis
        print("\n🔍 Testing market analysis...")
        analysis_results = multi_brain.analyze_market_conditions(
            data=data,
            model_type="LSTM",
            symbol="EURUSD"
        )
        
        print("✅ Analysis completed successfully")
        
        # Check for required keys
        required_keys = [
            'hyperparameter_suggestions',
            'model_recommendations', 
            'distributed_config',
            'data_insights'
        ]
        
        print("\n🔑 Checking required keys:")
        all_present = True
        for key in required_keys:
            if key in analysis_results:
                print(f"   ✅ {key}: Present")
                # Check if it's a dict and doesn't have error
                if isinstance(analysis_results[key], dict):
                    if 'error' in analysis_results[key]:
                        print(f"      ⚠️ Has error: {analysis_results[key]['error']}")
                    else:
                        print(f"      ✅ Valid data")
            else:
                print(f"   ❌ {key}: Missing")
                all_present = False
        
        print(f"\n🎯 All keys present: {'✅ YES' if all_present else '❌ NO'}")
        
        # Check the specific error that was happening
        if 'hyperparameter_suggestions' in analysis_results:
            hps = analysis_results['hyperparameter_suggestions']
            if isinstance(hps, dict):
                print(f"✅ hyperparameter_suggestions is dict with {len(hps)} items")
            else:
                print(f"⚠️ hyperparameter_suggestions is {type(hps)}")
        
        return all_present
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_scenario():
    """تست سناریو خطا"""
    print("\n🛡️ Testing Error Scenario")
    print("=" * 50)
    
    # Create data that might cause errors
    bad_data = pd.DataFrame({
        'text': ['hello', 'world'],  # Non-numeric
        'nulls': [None, None]        # All null
    })
    
    try:
        from fixed_ultimate_main import MultiBrainSystem
        multi_brain = MultiBrainSystem()
        
        # This should not crash and should provide all required keys
        analysis_results = multi_brain.analyze_market_conditions(
            data=bad_data,
            model_type="LSTM",
            symbol="TEST"
        )
        
        # Check that all keys exist even with bad data
        required_keys = [
            'hyperparameter_suggestions',
            'model_recommendations',
            'distributed_config', 
            'data_insights'
        ]
        
        all_present = all(key in analysis_results for key in required_keys)
        print(f"🔑 All keys present with bad data: {'✅ YES' if all_present else '❌ NO'}")
        
        return all_present
        
    except Exception as e:
        print(f"❌ Error scenario test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 SIMPLE H2O BRAIN TEST")
    print("=" * 60)
    
    # Test 1: Normal scenario
    test1 = test_brain_analysis_keys()
    
    # Test 2: Error scenario  
    test2 = test_error_scenario()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Normal Scenario: {'PASSED' if test1 else 'FAILED'}")
    print(f"🛡️ Error Scenario: {'PASSED' if test2 else 'FAILED'}")
    
    overall = test1 and test2
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if overall else '❌ SOME TESTS FAILED'}")
    
    if overall:
        print("\n🎉 The hyperparameter_suggestions error has been FIXED!")
        print("💡 Multi-Brain System now handles errors gracefully.")
    else:
        print("\n⚠️ Some issues remain.")
