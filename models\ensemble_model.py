import numpy as np
import pandas as pd
import torch
import os
import json
import logging
from typing import Dict, List, Tuple, Any, Optional, Union
from datetime import datetime
import matplotlib.pyplot as plt

from .rl_models import RLModelFactory

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnsembleModel:
    """
    کلاس EnsembleModel برای ترکیب چندین مدل RL با استفاده از voting هوشمند
    
    این کلاس امکان ترکیب خروجی چندین مدل RL را فراهم می‌کند و با استفاده از
    مکانیزم‌های وزن‌دهی هوشمند، تصمیم نهایی را اتخاذ می‌کند.
    
    ویژگی‌های اصلی:
    1. وزن‌دهی پویا به مدل‌ها بر اساس عملکرد اخیر
    2. امکان استفاده از مدل‌های مختلف (PPO, A2C, SAC, و غیره)
    3. مکانیزم تنوع‌سازی برای اطمینان از پوشش سناریوهای مختلف بازار
    4. اندازه‌گیری اطمینان برای تصمیمات ensemble
    5. یادگیری آنلاین برای به‌روزرسانی وزن‌ها در زمان اجرا
    """
    
    def __init__(
        self, 
        model_configs: List[Dict[str, Any]],
        env=None,
        voting_method: str = 'weighted',
        weight_update_freq: int = 100,
        confidence_threshold: float = 0.6,
        diversity_weight: float = 0.3,
        history_file: str = "ensemble_history.json"
    ):
        """
        مقداردهی اولیه EnsembleModel
        
        پارامترها:
        -----------
        model_configs : List[Dict[str, Any]]
            لیستی از دیکشنری‌های پیکربندی مدل‌ها. هر دیکشنری باید شامل:
            - 'model_type': نوع مدل (مثلاً 'ppo', 'a2c', 'sac', و غیره)
            - 'checkpoint_path': مسیر فایل checkpoint (اختیاری)
            - 'params': پارامترهای اضافی برای ایجاد مدل (اختیاری)
        env : محیط یادگیری تقویتی
            محیط مورد استفاده برای مدل‌ها
        voting_method : str
            روش رأی‌گیری ('weighted', 'majority', 'rank', 'confidence')
        weight_update_freq : int
            تعداد گام‌های بین به‌روزرسانی وزن‌ها
        confidence_threshold : float
            آستانه اطمینان برای تصمیم‌گیری (بین 0 و 1)
        diversity_weight : float
            وزن تنوع در محاسبه وزن‌های نهایی (بین 0 و 1)
        history_file : str
            مسیر فایل برای ذخیره تاریخچه عملکرد ensemble
        """
        self.model_factory = RLModelFactory()
        self.env = env
        self.voting_method = voting_method
        self.weight_update_freq = weight_update_freq
        self.confidence_threshold = confidence_threshold
        self.diversity_weight = diversity_weight
        self.history_file = history_file
        
        # مدل‌ها و وزن‌های مربوطه
        self.models = []
        self.model_weights = []
        self.model_names = []
        self.model_performance = []
        
        # تاریخچه عملکرد
        self.history = {
            'rewards': [],
            'weights': [],
            'confidences': [],
            'decisions': [],
            'timestamps': []
        }
        
        # شمارنده گام‌ها برای به‌روزرسانی وزن‌ها
        self.step_counter = 0
        
        # بارگذاری مدل‌ها
        self._load_models(model_configs)
        
        # مقداردهی اولیه وزن‌ها (یکسان)
        if self.models:
            self.model_weights = [1.0 / len(self.models) for _ in self.models]
            self.model_performance = [0.0 for _ in self.models]
        
        logger.info(f"EnsembleModel initialized with {len(self.models)} models and '{voting_method}' voting method")
    
    def _load_models(self, model_configs: List[Dict[str, Any]]):
        """
        بارگذاری مدل‌ها بر اساس پیکربندی‌ها
        
        پارامترها:
        -----------
        model_configs : List[Dict[str, Any]]
            لیستی از دیکشنری‌های پیکربندی مدل‌ها
        """
        for i, config in enumerate(model_configs):
            model_type = config.get('model_type')
            checkpoint_path = config.get('checkpoint_path')
            params = config.get('params', {})
            
            if not model_type:
                logger.warning(f"Model config at index {i} missing 'model_type'. Skipping.")
                continue
            
            try:
                if checkpoint_path and os.path.exists(checkpoint_path):
                    # بارگذاری مدل از checkpoint
                    model = self.model_factory.load_checkpoint(
                        model_type, self.env, checkpoint_path, **params
                    )
                    model_name = f"{model_type}_{os.path.basename(checkpoint_path)}"
                else:
                    # ایجاد مدل جدید
                    model = self.model_factory.create_model(model_type, self.env, **params)
                    model_name = f"{model_type}_{i}"
                
                self.models.append(model)
                self.model_names.append(model_name)
                logger.info(f"Loaded model: {model_name}")
                
            except Exception as e:
                logger.error(f"Error loading model {model_type}: {e}")
    
    def predict(self, observation, state=None, deterministic=True):
        """
        پیش‌بینی اقدام با استفاده از ensemble مدل‌ها
        
        پارامترها:
        -----------
        observation : مشاهده محیط
        state : وضعیت اختیاری برای مدل‌های با حافظه
        deterministic : آیا پیش‌بینی قطعی باشد یا خیر
        
        خروجی:
        -------
        Tuple[np.ndarray, Optional[np.ndarray]]
            اقدام پیش‌بینی‌شده و وضعیت جدید
        """
        if not self.models:
            raise ValueError("No models available in the ensemble")
        
        actions = []
        states = []
        confidences = []
        
        # جمع‌آوری پیش‌بینی‌ها از همه مدل‌ها
        for model in self.models:
            action, new_state = model.predict(observation, state=state, deterministic=deterministic)
            actions.append(action)
            states.append(new_state)
            
            # محاسبه اطمینان (برای مدل‌هایی که از توزیع احتمال پشتیبانی می‌کنند)
            if hasattr(model, 'policy') and hasattr(model.policy, 'get_distribution'):
                try:
                    dist = model.policy.get_distribution(observation)
                    if hasattr(dist, 'entropy'):
                        # اطمینان معکوس آنتروپی است (آنتروپی کمتر = اطمینان بیشتر)
                        entropy = dist.entropy().mean().item()
                        max_entropy = np.log(model.action_space.n) if hasattr(model.action_space, 'n') else 2.0
                        confidence = 1.0 - min(1.0, entropy / max_entropy)
                    else:
                        confidence = 0.8  # مقدار پیش‌فرض اگر آنتروپی در دسترس نیست
                except:
                    confidence = 0.8  # مقدار پیش‌فرض در صورت خطا
            else:
                confidence = 0.8  # مقدار پیش‌فرض برای مدل‌هایی که توزیع را ارائه نمی‌دهند
                
            confidences.append(confidence)
        
        # ترکیب اقدام‌ها با روش رأی‌گیری انتخاب‌شده
        final_action, ensemble_confidence = self._combine_actions(
            actions, confidences
        )
        
        # ثبت در تاریخچه
        self.history['decisions'].append({
            'actions': [a.tolist() if isinstance(a, np.ndarray) else a for a in actions],
            'final_action': final_action.tolist() if isinstance(final_action, np.ndarray) else final_action,
            'confidences': confidences,
            'ensemble_confidence': ensemble_confidence
        })
        self.history['timestamps'].append(datetime.now().isoformat())
        
        # به‌روزرسانی وزن‌ها در صورت نیاز
        self.step_counter += 1
        if self.step_counter >= self.weight_update_freq:
            self._update_weights()
            self.step_counter = 0
        
        # ترکیب وضعیت‌ها (برای مدل‌های با حافظه)
        final_state = None
        if any(s is not None for s in states):
            # استفاده از وضعیت مدل با بالاترین وزن
            max_weight_idx = np.argmax(self.model_weights)
            final_state = states[max_weight_idx]
        
        return final_action, final_state
    
    def _combine_actions(self, actions, confidences):
        """
        ترکیب اقدام‌های پیش‌بینی‌شده با روش رأی‌گیری انتخاب‌شده
        
        پارامترها:
        -----------
        actions : List[np.ndarray]
            لیست اقدام‌های پیش‌بینی‌شده توسط مدل‌ها
        confidences : List[float]
            لیست اطمینان‌های مربوط به هر پیش‌بینی
        
        خروجی:
        -------
        Tuple[np.ndarray, float]
            اقدام نهایی و اطمینان ensemble
        """
        # تبدیل به آرایه‌های numpy برای سهولت محاسبات
        actions_array = np.array(actions)
        confidences_array = np.array(confidences)
        weights_array = np.array(self.model_weights)
        
        if self.voting_method == 'weighted':
            # وزن‌دهی بر اساس وزن مدل و اطمینان پیش‌بینی
            combined_weights = weights_array * confidences_array
            combined_weights = combined_weights / np.sum(combined_weights)  # نرمال‌سازی
            
            # میانگین وزن‌دار اقدام‌ها
            final_action = np.sum(actions_array * combined_weights[:, np.newaxis], axis=0)
            ensemble_confidence = np.sum(combined_weights * confidences_array)
            
        elif self.voting_method == 'majority':
            # رأی اکثریت (برای فضاهای اقدام گسسته)
            if actions_array.shape[1] == 1:  # اقدام‌های تک‌بعدی
                unique_actions, counts = np.unique(actions_array, return_counts=True)
                final_action = unique_actions[np.argmax(counts)]
                ensemble_confidence = np.max(counts) / len(actions)
            else:
                # برای اقدام‌های چندبعدی، از میانگین استفاده می‌کنیم
                final_action = np.mean(actions_array, axis=0)
                ensemble_confidence = np.mean(confidences_array)
        
        elif self.voting_method == 'rank':
            # رتبه‌بندی مدل‌ها بر اساس عملکرد و وزن‌دهی بر اساس رتبه
            ranks = np.argsort(np.argsort(-weights_array)) + 1  # رتبه‌بندی معکوس (بهترین = 1)
            rank_weights = 1.0 / ranks
            rank_weights = rank_weights / np.sum(rank_weights)  # نرمال‌سازی
            
            # میانگین وزن‌دار بر اساس رتبه
            final_action = np.sum(actions_array * rank_weights[:, np.newaxis], axis=0)
            ensemble_confidence = np.sum(rank_weights * confidences_array)
        
        elif self.voting_method == 'confidence':
            # انتخاب اقدام با بیشترین اطمینان
            max_conf_idx = np.argmax(confidences_array)
            final_action = actions_array[max_conf_idx]
            ensemble_confidence = confidences_array[max_conf_idx]
        
        else:
            # روش پیش‌فرض: میانگین ساده
            final_action = np.mean(actions_array, axis=0)
            ensemble_confidence = np.mean(confidences_array)
        
        return final_action, float(ensemble_confidence)
    
    def _update_weights(self):
        """
        به‌روزرسانی وزن‌های مدل‌ها بر اساس عملکرد اخیر
        """
        # اگر داده‌های کافی برای به‌روزرسانی نداریم، از تابع خارج می‌شویم
        if len(self.history['rewards']) < self.weight_update_freq:
            return
        
        # محاسبه عملکرد اخیر هر مدل
        recent_rewards = self.history['rewards'][-self.weight_update_freq:]
        recent_decisions = self.history['decisions'][-self.weight_update_freq:]
        
        # محاسبه همبستگی بین اقدام‌های هر مدل و پاداش‌های دریافتی
        performance_scores = np.zeros(len(self.models))
        
        for i, model in enumerate(self.models):
            actions = []
            for decision in recent_decisions:
                if i < len(decision['actions']):
                    action = decision['actions'][i]
                    if isinstance(action, list):
                        # برای اقدام‌های چندبعدی، از میانگین استفاده می‌کنیم
                        action = np.mean(action)
                    actions.append(action)
            
            if actions:
                # محاسبه همبستگی بین اقدام‌ها و پاداش‌ها
                try:
                    correlation = np.corrcoef(actions, recent_rewards)[0, 1]
                    # تبدیل همبستگی به امتیاز مثبت
                    performance_scores[i] = max(0, correlation)
                except:
                    # در صورت خطا، از امتیاز قبلی استفاده می‌کنیم
                    performance_scores[i] = self.model_performance[i]
        
        # اگر همه امتیازها صفر باشند، از وزن‌های یکسان استفاده می‌کنیم
        if np.sum(performance_scores) == 0:
            performance_scores = np.ones(len(self.models)) / len(self.models)
        
        # محاسبه تنوع مدل‌ها
        diversity_scores = self._calculate_diversity(recent_decisions)
        
        # ترکیب امتیاز عملکرد و تنوع
        combined_scores = (1 - self.diversity_weight) * performance_scores + self.diversity_weight * diversity_scores
        
        # نرمال‌سازی وزن‌ها
        new_weights = combined_scores / np.sum(combined_scores)
        
        # به‌روزرسانی وزن‌ها و عملکرد
        self.model_weights = new_weights.tolist()
        self.model_performance = performance_scores.tolist()
        
        # ثبت وزن‌های جدید در تاریخچه
        self.history['weights'].append(self.model_weights)
        
        logger.info(f"Updated model weights: {[f'{name}: {weight:.3f}' for name, weight in zip(self.model_names, self.model_weights)]}")
    
    def _calculate_diversity(self, decisions):
        """
        محاسبه تنوع بین مدل‌ها بر اساس تفاوت در اقدام‌های پیش‌بینی‌شده
        
        پارامترها:
        -----------
        decisions : List[Dict]
            لیست تصمیمات اخیر
        
        خروجی:
        -------
        np.ndarray
            امتیاز تنوع برای هر مدل
        """
        n_models = len(self.models)
        diversity_scores = np.zeros(n_models)
        
        for decision in decisions:
            actions = decision['actions']
            if len(actions) < n_models:
                continue
                
            # تبدیل اقدام‌ها به آرایه‌های یک‌بعدی
            flat_actions = []
            for action in actions:
                if isinstance(action, list) or isinstance(action, np.ndarray):
                    flat_actions.append(np.mean(action))
                else:
                    flat_actions.append(action)
            
            # محاسبه میانگین اقدام‌ها
            mean_action = np.mean(flat_actions)
            
            # محاسبه فاصله هر اقدام از میانگین
            for i, action in enumerate(flat_actions):
                if i < n_models:
                    diversity_scores[i] += abs(action - mean_action)
        
        # نرمال‌سازی امتیازها
        if np.sum(diversity_scores) > 0:
            diversity_scores = diversity_scores / np.sum(diversity_scores)
        else:
            diversity_scores = np.ones(n_models) / n_models
        
        return diversity_scores
    
    def update_reward(self, reward):
        """
        به‌روزرسانی تاریخچه پاداش‌ها
        
        پارامترها:
        -----------
        reward : float
            پاداش دریافتی از محیط
        """
        self.history['rewards'].append(reward)
    
    def save(self, path):
        """
        ذخیره مدل ensemble و تاریخچه آن
        
        پارامترها:
        -----------
        path : str
            مسیر پایه برای ذخیره‌سازی
        """
        # ایجاد دایرکتوری در صورت نیاز
        os.makedirs(path, exist_ok=True)
        
        # ذخیره هر مدل
        model_paths = []
        for i, model in enumerate(self.models):
            model_path = os.path.join(path, f"model_{i}.zip")
            self.model_factory.save_checkpoint(model, model_path)
            model_paths.append(model_path)
        
        # ذخیره پیکربندی و تاریخچه
        config = {
            'model_paths': model_paths,
            'model_names': self.model_names,
            'model_weights': self.model_weights,
            'model_performance': self.model_performance,
            'voting_method': self.voting_method,
            'weight_update_freq': self.weight_update_freq,
            'confidence_threshold': self.confidence_threshold,
            'diversity_weight': self.diversity_weight,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(os.path.join(path, 'ensemble_config.json'), 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        # ذخیره تاریخچه (با حذف داده‌های بزرگ)
        history_to_save = {
            'rewards': self.history['rewards'],
            'weights': self.history['weights'],
            'confidences': [d.get('ensemble_confidence') for d in self.history['decisions']],
            'timestamps': self.history['timestamps']
        }
        
        with open(os.path.join(path, 'ensemble_history.json'), 'w', encoding='utf-8') as f:
            json.dump(history_to_save, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Ensemble model saved to {path}")
    
    @classmethod
    def load(cls, path, env):
        """
        بارگذاری مدل ensemble از مسیر مشخص‌شده
        
        پارامترها:
        -----------
        path : str
            مسیر بارگذاری مدل ensemble
        env : محیط یادگیری تقویتی
            محیط مورد استفاده برای مدل‌ها
        
        خروجی:
        -------
        EnsembleModel
            نمونه بارگذاری‌شده از EnsembleModel
        """
        config_path = os.path.join(path, 'ensemble_config.json')
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Ensemble config not found at {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # بارگذاری پیکربندی
        model_paths = config.get('model_paths', [])
        model_names = config.get('model_names', [])
        model_weights = config.get('model_weights', [])
        model_performance = config.get('model_performance', [])
        voting_method = config.get('voting_method', 'weighted')
        weight_update_freq = config.get('weight_update_freq', 100)
        confidence_threshold = config.get('confidence_threshold', 0.6)
        diversity_weight = config.get('diversity_weight', 0.3)
        
        # ایجاد پیکربندی‌های مدل
        model_configs = []
        for model_path in model_paths:
            if os.path.exists(model_path):
                # استخراج نوع مدل از نام فایل یا متادیتا
                model_type = 'ppo'  # پیش‌فرض
                model_configs.append({
                    'model_type': model_type,
                    'checkpoint_path': model_path
                })
        
        # ایجاد نمونه جدید
        ensemble = cls(
            model_configs=model_configs,
            env=env,
            voting_method=voting_method,
            weight_update_freq=weight_update_freq,
            confidence_threshold=confidence_threshold,
            diversity_weight=diversity_weight
        )
        
        # بازیابی وزن‌ها و نام‌ها
        if model_weights and len(model_weights) == len(ensemble.models):
            ensemble.model_weights = model_weights
        if model_names and len(model_names) == len(ensemble.models):
            ensemble.model_names = model_names
        if model_performance and len(model_performance) == len(ensemble.models):
            ensemble.model_performance = model_performance
        
        # بارگذاری تاریخچه
        history_path = os.path.join(path, 'ensemble_history.json')
        if os.path.exists(history_path):
            with open(history_path, 'r', encoding='utf-8') as f:
                history = json.load(f)
                ensemble.history['rewards'] = history.get('rewards', [])
                ensemble.history['weights'] = history.get('weights', [])
                ensemble.history['timestamps'] = history.get('timestamps', [])
                
                # بازسازی تاریخچه تصمیمات با اطلاعات محدود
                confidences = history.get('confidences', [])
                ensemble.history['decisions'] = [{'ensemble_confidence': conf} for conf in confidences]
        
        logger.info(f"Ensemble model loaded from {path} with {len(ensemble.models)} models")
        return ensemble
    
    def plot_weights_history(self):
        """
        رسم نمودار تاریخچه وزن‌های مدل‌ها
        """
        if not self.history['weights']:
            logger.warning("No weight history available to plot")
            return
        
        weights_history = np.array(self.history['weights'])
        plt.figure(figsize=(12, 6))
        
        for i, name in enumerate(self.model_names):
            if i < weights_history.shape[1]:
                plt.plot(weights_history[:, i], label=name)
        
        plt.title('Model Weights History')
        plt.xlabel('Update Steps')
        plt.ylabel('Weight')
        plt.legend()
        plt.grid(True)
        
        return plt.gcf()
    
    def plot_performance(self):
        """
        رسم نمودار عملکرد ensemble (پاداش و اطمینان)
        """
        if not self.history['rewards']:
            logger.warning("No reward history available to plot")
            return
        
        rewards = self.history['rewards']
        confidences = [d.get('ensemble_confidence', 0) for d in self.history['decisions']]
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
        
        # نمودار پاداش
        ax1.plot(rewards, 'b-', label='Reward')
        ax1.set_title('Ensemble Reward History')
        ax1.set_ylabel('Reward')
        ax1.legend()
        ax1.grid(True)
        
        # نمودار اطمینان
        if confidences:
            ax2.plot(confidences, 'r-', label='Confidence')
            ax2.set_title('Ensemble Confidence History')
            ax2.set_xlabel('Steps')
            ax2.set_ylabel('Confidence')
            ax2.legend()
            ax2.grid(True)
        
        plt.tight_layout()
        return fig
