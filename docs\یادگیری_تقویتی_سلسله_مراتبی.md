# مستند جامع: HierarchicalRL

## مسئولیت
سیستم یادگیری تقویتی سلسله‌مراتبی با سه سطح: استراتژیک (انتخاب استراتژی)، تاکتیکی (زمان‌بندی ورود/خروج) و اجرایی (اجرای دقیق معاملات).

## پارامترها
- obs_dim: بعد مشاهدات
- device: دستگاه محاسباتی (cpu/cuda)
- learning_rate: نرخ یادگیری
- gamma: ضریب تخفیف
- gae_lambda: ضریب GAE
- clip_ratio: نسبت کلیپ PPO

## متدهای کلیدی
- act: تصمیم‌گیری کامل سلسله‌مراتبی
- select_strategy: انتخاب استراتژی سطح بالا
- select_tactical_action: انتخاب اقدام تاکتیکی
- select_execution_action: انتخاب اقدام اجرایی
- train_step: گام آموزشی برای همه سطوح

## نمونه کد
```python
from models.hierarchical_rl import HierarchicalRL
hrl = HierarchicalRL(obs_dim=20, device='cpu')
strategy, tactical, execution = hrl.act(observation)
```

## مدیریت خطا
در صورت خطا در محاسبه یا آموزش، خطا لاگ و مقادیر پیش‌فرض برگردانده می‌شود.

## بهترین شیوه
- از ابعاد مناسب برای مشاهدات هر سطح استفاده کنید.
- عملکرد استراتژی‌ها را مداوم نظارت کنید.

## نمودار
- نمودار استفاده از استراتژی‌ها و losses سطوح مختلف قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول در تست‌ها و مثال‌ها استفاده شده اما در جریان اصلی معاملات فعال نیست.

## وضعیت عملیاتی
⚠️ فقط در سطح تست و توسعه - نیاز به اتصال به سیستم اصلی دارد. 