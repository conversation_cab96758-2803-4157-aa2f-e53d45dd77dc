from stable_baselines3 import PPO, A2C, DQN, SAC, TD3, DDPG
from sb3_contrib import RecurrentPPO, QRDQN, TQC, MaskablePPO
from sb3_contrib.common.wrappers import TimeFeatureWrapper
import torch
import numpy as np
import os
import pickle


class PPOAgent:
    """کلاس wrapper برای مدل PPO"""
    
    def __init__(self, model_path=None):
        self.model = None
        self.model_path = model_path or 'models/ppo_model.pkl'
        
    def load_model(self, path=None):
        """بارگذاری مدل از فایل"""
        try:
            if path:
                self.model_path = path
            if os.path.exists(self.model_path):
                with open(self.model_path, 'rb') as f:
                    self.model = pickle.load(f)
                return True
            return False
        except Exception as e:
            print(f"Failed to load PPO model: {e}")
            return False
    
    def predict(self, state):
        """پیش‌بینی action برای state داده شده"""
        if self.model is None:
            # Return random action if model not loaded
            return np.random.randint(0, 3)  # 0: hold, 1: buy, 2: sell
        
        try:
            # Convert state to proper format
            if isinstance(state, (list, tuple)):
                state = np.array(state)
            elif hasattr(state, 'values'):
                state = state.values
            
            # Ensure state is 1D array
            if state.ndim > 1:
                state = state.flatten()
            
            # Predict action
            action, _ = self.model.predict(state, deterministic=True)
            return action
        except Exception as e:
            print(f"PPO prediction failed: {e}")
            return np.random.randint(0, 3)


class A2CAgent:
    """کلاس wrapper برای مدل A2C"""
    
    def __init__(self, model_path=None):
        self.model = None
        self.model_path = model_path or 'models/a2c_model.pkl'
        
    def load_model(self, path=None):
        """بارگذاری مدل از فایل"""
        try:
            if path:
                self.model_path = path
            if os.path.exists(self.model_path):
                with open(self.model_path, 'rb') as f:
                    self.model = pickle.load(f)
                return True
            return False
        except Exception as e:
            print(f"Failed to load A2C model: {e}")
            return False
    
    def predict(self, state):
        """پیش‌بینی action برای state داده شده"""
        if self.model is None:
            # Return random action if model not loaded
            return np.random.randint(0, 3)  # 0: hold, 1: buy, 2: sell
        
        try:
            # Convert state to proper format
            if isinstance(state, (list, tuple)):
                state = np.array(state)
            elif hasattr(state, 'values'):
                state = state.values
            
            # Ensure state is 1D array
            if state.ndim > 1:
                state = state.flatten()
            
            # Predict action
            action, _ = self.model.predict(state, deterministic=True)
            return action
        except Exception as e:
            print(f"A2C prediction failed: {e}")
            return np.random.randint(0, 3)


class DQNAgent:
    """کلاس wrapper برای مدل DQN"""
    
    def __init__(self, model_path=None):
        self.model = None
        self.model_path = model_path or 'models/dqn_model.pkl'
        
    def load_model(self, path=None):
        """بارگذاری مدل از فایل"""
        try:
            if path:
                self.model_path = path
            if os.path.exists(self.model_path):
                with open(self.model_path, 'rb') as f:
                    self.model = pickle.load(f)
                return True
            return False
        except Exception as e:
            print(f"Failed to load DQN model: {e}")
            return False
    
    def predict(self, state):
        """پیش‌بینی action برای state داده شده"""
        if self.model is None:
            # Return random action if model not loaded
            return np.random.randint(0, 3)  # 0: hold, 1: buy, 2: sell
        
        try:
            # Convert state to proper format
            if isinstance(state, (list, tuple)):
                state = np.array(state)
            elif hasattr(state, 'values'):
                state = state.values
            
            # Ensure state is 1D array
            if state.ndim > 1:
                state = state.flatten()
            
            # Predict action
            action, _ = self.model.predict(state, deterministic=True)
            return action
        except Exception as e:
            print(f"DQN prediction failed: {e}")
            return np.random.randint(0, 3)


class RLModelFactory:

    def resume_training(self, model_type, env, checkpoint_path, new_params=None, transfer_weights=False, new_env=None, curriculum_steps=None, network_kwargs=None, new_reward_fn=None, optimizer_kwargs=None, **kwargs):
        """
        ادامه آموزش مدل RL با قابلیت تغییر پارامتر، انتقال وزن (transfer learning)، warm start و curriculum learning
        - new_params: دیکشنری پارامترهای جدید (مثلاً learning_rate)
        - transfer_weights: اگر True، وزن‌ها را به ساختار جدید منتقل می‌کند (در صورت سازگاری)
        - new_env: اگر داده یا محیط جدید باشد، روی آن ادامه می‌دهد
        - curriculum_steps: لیست از (env, timesteps) برای curriculum learning
        - network_kwargs: دیکشنری پارامترهای جدید معماری شبکه (مثلاً تعداد لایه‌ها، سایز لایه‌ها)
        - new_reward_fn: تابع پاداش جدید (در صورت نیاز به تغییر reward function)
        - optimizer_kwargs: دیکشنری پارامترهای جدید optimizer (مثلاً نوع optimizer یا پارامترهای آن)
        """
        # اگر network_kwargs داده شده، مدل جدید با معماری جدید ساخته شود
        if network_kwargs is not None:
            model = self.create_model(model_type, env, **network_kwargs, **kwargs)
            old_model = model.load(checkpoint_path, env=env)
            # تلاش برای انتقال وزن‌ها (در صورت سازگاری)
            try:
                model.policy.load_state_dict(old_model.policy.state_dict(), strict=False)
            except Exception as e:
                print(f"[NetworkChange] انتقال وزن‌ها به معماری جدید ممکن نشد: {e}")
        else:
            model = self.create_model(model_type, env, **kwargs)
            model = model.load(checkpoint_path, env=env)

        # تغییر تابع پاداش (در صورت نیاز)
        if new_reward_fn is not None:
            if hasattr(model, 'reward_fn'):
                model.reward_fn = new_reward_fn
            elif hasattr(model.env, 'set_reward_fn'):
                try:
                    model.env.set_reward_fn(new_reward_fn)
                except Exception as e:
                    print(f"[RewardChange] تغییر تابع پاداش ممکن نشد: {e}")
            else:
                print("[RewardChange] مدل یا محیط RL قابلیت تغییر reward function را ندارد.")

        # تغییر optimizer (در صورت نیاز)
        if optimizer_kwargs is not None:
            try:
                # مثال: {'type': torch.optim.Adam, 'lr': 0.0001}
                opt_type = optimizer_kwargs.get('type', None)
                opt_params = {k: v for k, v in optimizer_kwargs.items() if k != 'type'}
                if opt_type is not None and hasattr(model, 'policy'):
                    model.policy.optimizer = opt_type(model.policy.parameters(), **opt_params)
            except Exception as e:
                print(f"[OptimizerChange] تغییر optimizer ممکن نشد: {e}")
        # تغییر پارامترها (مثلاً learning_rate)
        if new_params:
            for k, v in new_params.items():
                if hasattr(model, k):
                    setattr(model, k, v)
                elif hasattr(model.policy, k):
                    setattr(model.policy, k, v)
        # انتقال وزن‌ها به ساختار جدید (transfer learning)
        if transfer_weights and new_env is not None:
            new_model = self.create_model(model_type, new_env, **kwargs)
            try:
                new_model.policy.load_state_dict(model.policy.state_dict())
                model = new_model
            except Exception as e:
                print(f"[TransferLearning] انتقال وزن‌ها ممکن نشد: {e}")
        # warm start روی محیط جدید
        if new_env is not None and not transfer_weights:
            model.set_env(new_env)
        # curriculum learning
        if curriculum_steps:
            for env_step, timesteps in curriculum_steps:
                model.set_env(env_step)
                model.learn(total_timesteps=timesteps, reset_num_timesteps=False)
        return model

    def auto_checkpoint(self, model, path, epoch=None, reward=None, loss=None, best_model=False):
        """
        ذخیره checkpoint با metadata و hash. اگر best_model=True، به عنوان بهترین مدل ذخیره می‌شود.
        """
        import json, hashlib, os
        # ذخیره مدل
        model.save(path)
        # محاسبه hash فایل
        def file_hash(filepath):
            h = hashlib.sha256()
            with open(filepath, 'rb') as f:
                while True:
                    chunk = f.read(8192)
                    if not chunk:
                        break
                    h.update(chunk)
            return h.hexdigest()
        hash_val = file_hash(path)
        # ذخیره metadata
        meta = {
            'epoch': epoch,
            'reward': reward,
            'loss': loss,
            'hash': hash_val,
            'best_model': best_model
        }
        meta_path = path + '.meta.json'
        with open(meta_path, 'w', encoding='utf-8') as f:
            json.dump(meta, f, ensure_ascii=False, indent=2)
        # اگر بهترین مدل است، یک کپی جداگانه ذخیره کن
        if best_model:
            best_path = os.path.splitext(path)[0] + '_best.zip'
            model.save(best_path)
            with open(best_path + '.meta.json', 'w', encoding='utf-8') as f:
                json.dump(meta, f, ensure_ascii=False, indent=2)
    def __init__(self):
        self.models = {
            "ppo": self._create_ppo,
            "a2c": self._create_a2c,
            "dqn": self._create_dqn,
            "sac": self._create_sac,
            "td3": self._create_td3,
            "ddpg": self._create_ddpg,
            "ppo_lstm": self._create_ppo_lstm,
            "qrdqn": self._create_qrdqn,
            "tqc": self._create_tqc,
            "maskable_ppo": self._create_maskable_ppo,
            # "decision_transformer": self._create_decision_transformer,  # غیرفعال تا زمان نصب صحیح پکیج
        }

    def save_checkpoint(self, model, path):
        """
        ذخیره مدل RL در مسیر مشخص‌شده (checkpoint)
        """
        model.save(path)

    def load_checkpoint(self, model_type, env, path, **kwargs):
        """
        بارگذاری مدل RL از checkpoint و اتصال به env جدید
        """
        model = self.create_model(model_type, env, **kwargs)
        model = model.load(path, env=env)
        return model
        model = model.load(path, env=env)
        return model
        self.models = {
            "ppo": self._create_ppo,
            "a2c": self._create_a2c,
            "dqn": self._create_dqn,
            "sac": self._create_sac,
            "td3": self._create_td3,
            "ddpg": self._create_ddpg,
            "ppo_lstm": self._create_ppo_lstm,
            "qrdqn": self._create_qrdqn,
            "tqc": self._create_tqc,
            "maskable_ppo": self._create_maskable_ppo,
            # "decision_transformer": self._create_decision_transformer,  # غیرفعال تا زمان نصب صحیح پکیج
        }

    # def _create_decision_transformer(self, env, **kwargs):
    #     from decision_transformer.models.decision_transformer import DecisionTransformer
    #     import torch
    #     obs_dim = env.observation_space.shape[0]
    #     act_dim = env.action_space.n if hasattr(env.action_space, 'n') else env.action_space.shape[0]
    #     model = DecisionTransformer(
    #         state_dim=obs_dim,
    #         act_dim=act_dim,
    #         max_length=20,
    #         hidden_size=128,
    #         n_layer=2,
    #         n_head=2,
    #         n_inner=256,
    #         activation_function="relu",
    #         n_positions=1024,
    #         resid_pdrop=0.1,
    #         attn_pdrop=0.1,
    #         **kwargs
    #     )
    #     return model
    def _create_ddpg(self, env, **kwargs):
        from stable_baselines3 import DDPG
        return DDPG(
            "MlpPolicy",
            env,
            learning_rate=0.001,
            buffer_size=1000000,
            batch_size=100,
            tau=0.005,
            gamma=0.99,
            train_freq=1,
            gradient_steps=1,
            **kwargs,
        )

    def _create_qrdqn(self, env, **kwargs):
        return QRDQN(
            "MlpPolicy",
            env,
            learning_rate=0.0001,
            buffer_size=100000,
            learning_starts=1000,
            batch_size=32,
            tau=1.0,
            gamma=0.99,
            train_freq=4,
            gradient_steps=1,
            target_update_interval=1000,
            exploration_fraction=0.1,
            exploration_final_eps=0.02,
            **kwargs,
        )

    def _create_tqc(self, env, **kwargs):
        return TQC(
            "MlpPolicy",
            env,
            learning_rate=0.0003,
            buffer_size=100000,
            batch_size=256,
            tau=0.005,
            gamma=0.99,
            train_freq=1,
            gradient_steps=1,
            ent_coef='auto',
            **kwargs,
        )

    def _create_maskable_ppo(self, env, **kwargs):
        return MaskablePPO(
            "MlpPolicy",
            env,
            learning_rate=0.0003,
            n_steps=2048,
            batch_size=64,
            n_epochs=10,
            gamma=0.99,
            gae_lambda=0.95,
            ent_coef=0.0,
            **kwargs,
        )

    def _create_bc(self, env, expert_data=None, **kwargs):
        # expert_data: path to expert demonstrations or DataFrame
        return BC(
            observation_space=env.observation_space,
            action_space=env.action_space,
            demonstrations=expert_data,
            rng=torch.Generator(),
            device="cpu",
            **kwargs,
        )
    def _create_sac(self, env, **kwargs):
        return SAC(
            "MlpPolicy",
            env,
            learning_rate=0.0003,
            buffer_size=100000,
            batch_size=256,
            tau=0.005,
            gamma=0.99,
            train_freq=1,
            gradient_steps=1,
            ent_coef='auto',
            **kwargs,
        )

    def _create_td3(self, env, **kwargs):
        return TD3(
            "MlpPolicy",
            env,
            learning_rate=0.001,
            buffer_size=1000000,
            batch_size=100,
            tau=0.005,
            gamma=0.99,
            train_freq=1,
            gradient_steps=1,
            policy_delay=2,
            target_policy_noise=0.2,
            target_noise_clip=0.5,
            **kwargs,
        )

    def _create_ppo_lstm(self, env, **kwargs):
        return RecurrentPPO(
            "MlpLstmPolicy",
            env,
            learning_rate=0.0003,
            n_steps=128,
            batch_size=64,
            n_epochs=10,
            gamma=0.99,
            gae_lambda=0.95,
            ent_coef=0.0,
            **kwargs,
        )

    def create_model(self, model_type, env, **kwargs):
        if model_type not in self.models:
            raise ValueError(
                f"Model type {model_type} not supported. Available models: {list(self.models.keys())}"
            )
        return self.models[model_type](env, **kwargs)

    def _create_ppo(self, env, **kwargs):
        return PPO(
            "MlpPolicy",
            env,
            learning_rate=0.0003,
            n_steps=2048,
            batch_size=64,
            n_epochs=10,
            gamma=0.99,
            gae_lambda=0.95,
            ent_coef=0.0,
            **kwargs,
        )

    def _create_a2c(self, env, **kwargs):
        return A2C(
            "MlpPolicy",
            env,
            learning_rate=0.0007,
            n_steps=5,
            gamma=0.99,
            gae_lambda=1.0,
            ent_coef=0.0,
            **kwargs,
        )

    def _create_dqn(self, env, **kwargs):
        return DQN(
            "MlpPolicy",
            env,
            learning_rate=0.0001,
            buffer_size=100000,
            learning_starts=1000,
            batch_size=32,
            tau=1.0,
            gamma=0.99,
            train_freq=4,
            gradient_steps=1,
            target_update_interval=1000,
            exploration_fraction=0.1,
            exploration_final_eps=0.02,
            **kwargs,
        )
