{"timestamp": "2025-07-15T04:35:52.709309", "models": {"sentiment": {"error": "train_model() takes 1 positional argument but 2 were given", "traceback": "Traceback (most recent call last):\n  File \"d:\\project\\main_pipeline.py\", line 69, in run_full_pipeline\n    train_success = model.train_model(cleaned_data)\nTypeError: train_model() takes 1 positional argument but 2 were given\n"}, "timeseries": {"error": "train_model() takes 1 positional argument but 2 were given", "traceback": "Traceback (most recent call last):\n  File \"d:\\project\\main_pipeline.py\", line 69, in run_full_pipeline\n    train_success = model.train_model(cleaned_data)\nTypeError: train_model() takes 1 positional argument but 2 were given\n"}}, "overall_status": "failed", "model_comparison": {}}