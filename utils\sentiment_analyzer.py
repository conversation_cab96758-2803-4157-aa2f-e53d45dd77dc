"""Advanced Multi-language Sentiment Analysis System

این ماژول سیستم پیشرفته تحلیل احساسات چندزبانه برای متون مالی فراهم می‌کند.
ویژگی‌های کلیدی:
- پشتیبانی از زبان‌های متعدد با مدل‌های تخصصی
- تحلیل احساسات در سطح جنبه (Aspect-based)
- تحلیل احساسات زمانی (Temporal)
- ترکیب چند مدل (Ensemble)
- تحلیل احساسات بازار (Market Sentiment)
- پیش‌بینی تأثیر احساسات
- تحلیل احساسات رسانه‌های اجتماعی
- سیستم کش هوشمند
- تحلیل احساسات چندمنبعی

Dependencies: transformers, torch, langdetect, textblob, vaderSentiment, numpy, pandas
"""


# Import warning suppressor
try:
    from warning_suppressor import suppress_all_warnings
    suppress_all_warnings()
except ImportError:
    import warnings
    warnings.filterwarnings('ignore')

import os
import logging
import json
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional, Union, Any
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque

import numpy as np
import pandas as pd
import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForSequenceClassification, 
    pipeline
)
from langdetect import detect
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
import spacy
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor

# Persian sentiment fallback
try:
    from persian_sentiment_fallback import analyze_persian_text
    PERSIAN_FALLBACK_AVAILABLE = True
except ImportError:
    PERSIAN_FALLBACK_AVAILABLE = False


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set proxy for transformers if needed
if os.path.exists('PROXY.json'):
    with open('PROXY.json', 'r') as f:
        proxy_config = json.load(f)
        os.environ['HTTP_PROXY'] = proxy_config.get('http', '')
        os.environ['HTTPS_PROXY'] = proxy_config.get('https', '')
    logger.info("پروکسی با موفقیت تنظیم شد.")


class SentimentType(Enum):
    """انواع احساسات"""
    VERY_POSITIVE = "very_positive"
    POSITIVE = "positive"
    NEUTRAL = "neutral"
    NEGATIVE = "negative"
    VERY_NEGATIVE = "very_negative"
    MIXED = "mixed"


class AspectCategory(Enum):
    """دسته‌بندی جنبه‌ها"""
    PRICE = "price"
    EARNINGS = "earnings"
    MANAGEMENT = "management"
    PRODUCT = "product"
    MARKET = "market"
    REGULATION = "regulation"
    COMPETITION = "competition"
    TECHNOLOGY = "technology"
    GENERAL = "general"


@dataclass
class SentimentResult:
    """نتیجه تحلیل احساسات"""
    text: str
    language: str
    label: str
    score: float
    confidence: float
    source: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    aspects: Optional[Dict[str, float]] = None
    entities: List[Tuple[str, str, float]] = field(default_factory=list)
    temporal_info: Optional[Dict[str, Any]] = None
    market_impact: float = 0.0
    credibility_weight: float = 1.0
    model_scores: Dict[str, float] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Backward compatibility
    @property
    def sentiment(self) -> SentimentType:
        """تبدیل label به SentimentType برای سازگاری"""
        label_mapping = {
            'positive': SentimentType.POSITIVE,
            'negative': SentimentType.NEGATIVE,
            'neutral': SentimentType.NEUTRAL,
            'very_positive': SentimentType.VERY_POSITIVE,
            'very_negative': SentimentType.VERY_NEGATIVE,
            'mixed': SentimentType.MIXED
        }
        return label_mapping.get(self.label.lower(), SentimentType.NEUTRAL)


@dataclass
class MarketSentiment:
    """احساسات کلی بازار"""
    overall_sentiment: float
    sentiment_distribution: Dict[SentimentType, float]
    trend: str  # "bullish", "bearish", "neutral"
    momentum: float
    volatility: float
    confidence: float
    top_topics: List[Tuple[str, float]]
    time_window: timedelta
    source_distribution: Dict[str, float]


class LanguageModel:
    """مدل تحلیل احساسات برای یک زبان خاص"""
    
    def __init__(self, language: str, model_name: str, device: str = 'cpu'):
        self.language = language
        self.model_name = model_name
        self.device = device
        self.model = None
        self.tokenizer = None
        self.weight = 1.0
        self.vader_analyzer = None
        
        # Set up proxy for HuggingFace
        self._setup_proxy()
        
        try:
            self._load_model()
        except Exception as e:
            logger.error(f"Error loading {language} model: {e}")
            # Initialize VADER as fallback
            self.vader_analyzer = SentimentIntensityAnalyzer()
            logger.info(f"Using VADER fallback for {language}")
    
    def _setup_proxy(self):
        """تنظیم پروکسی برای HuggingFace"""
        import os
        os.environ['HTTP_PROXY'] = 'http://127.0.0.1:10809'
        os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:10809'
        os.environ['http_proxy'] = 'http://127.0.0.1:10809'
        os.environ['https_proxy'] = 'http://127.0.0.1:10809'
    
    def _load_model(self):
        """بارگذاری مدل"""
        try:
            from transformers import pipeline
            self.model = pipeline(
                'sentiment-analysis',
                model=self.model_name,
                device=0 if self.device == 'cuda' else -1,
                return_all_scores=True
            )
            logger.info(f"Successfully loaded model: {self.model_name}")
        except Exception as e:
            logger.error(f"Failed to load model {self.model_name}: {e}")
            raise
    
    def analyze(self, text: str) -> Dict[str, Any]:
        """تحلیل احساسات متن"""
        if self.model:
            try:
                result = self.model(text)
                if result and len(result) > 0:
                    # Convert to standard format
                    scores = {item['label'].lower(): item['score'] for item in result[0]}
                    
                    # Find dominant sentiment
                    max_score = max(scores.values())
                    dominant_label = [k for k, v in scores.items() if v == max_score][0]
                    
                    # Map labels to standard format
                    label_mapping = {
                        'positive': 'positive',
                        'negative': 'negative', 
                        'neutral': 'neutral',
                        'pos': 'positive',
                        'neg': 'negative',
                        'neu': 'neutral'
                    }
                    
                    standard_label = label_mapping.get(dominant_label, dominant_label)
                    
                    return {
                        'label': standard_label,
                        'score': max_score,
                        'all_scores': scores
                    }
            except Exception as e:
                logger.error(f"Error in model analysis: {e}")
        
        # Fallback to VADER
        if self.vader_analyzer:
            vader_result = self.vader_analyzer.polarity_scores(text)
            compound = vader_result['compound']
            
            if compound >= 0.05:
                label = 'positive'
                score = vader_result['pos']
            elif compound <= -0.05:
                label = 'negative'
                score = vader_result['neg']
            else:
                label = 'neutral'
                score = vader_result['neu']
            
            return {
                'label': label,
                'score': score,
                'all_scores': vader_result
            }
        
        # Last resort
        return {
            'label': 'neutral',
            'score': 0.5,
            'all_scores': {}
        }


class AspectBasedSentimentAnalyzer:
    """تحلیل احساسات بر اساس جنبه"""
    
    def __init__(self, device: str = "cpu"):
        self.device = device
        self.aspect_keywords = {
            AspectCategory.PRICE: ['price', 'cost', 'expensive', 'cheap', 'value', 'قیمت', 'هزینه'],
            AspectCategory.EARNINGS: ['earnings', 'profit', 'revenue', 'income', 'loss', 'سود', 'درآمد'],
            AspectCategory.MANAGEMENT: ['ceo', 'management', 'leadership', 'executive', 'مدیریت', 'مدیرعامل'],
            AspectCategory.PRODUCT: ['product', 'service', 'quality', 'feature', 'محصول', 'خدمات'],
            AspectCategory.MARKET: ['market', 'industry', 'sector', 'competition', 'بازار', 'صنعت'],
            AspectCategory.REGULATION: ['regulation', 'law', 'compliance', 'policy', 'قانون', 'مقررات'],
            AspectCategory.COMPETITION: ['competitor', 'rival', 'competition', 'رقیب', 'رقابت'],
            AspectCategory.TECHNOLOGY: ['technology', 'innovation', 'digital', 'AI', 'فناوری', 'نوآوری']
        }
        
        # Load spaCy model for entity recognition
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except:
            logger.warning("spaCy model not found. Entity recognition will be limited.")
            self.nlp = None
    
    def extract_aspects(self, text: str) -> Dict[AspectCategory, List[str]]:
        """استخراج جنبه‌ها از متن"""
        aspects = defaultdict(list)
        text_lower = text.lower()
        
        for category, keywords in self.aspect_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    # Extract sentence containing the keyword
                    sentences = text.split('.')
                    for sentence in sentences:
                        if keyword in sentence.lower():
                            aspects[category].append(sentence.strip())
                            break
        
        return dict(aspects)
    
    def analyze_aspect_sentiment(self, text: str, sentiment_analyzer) -> Dict[AspectCategory, float]:
        """تحلیل احساسات برای هر جنبه"""
        aspects = self.extract_aspects(text)
        aspect_sentiments = {}
        
        for category, sentences in aspects.items():
            if sentences:
                # Calculate average sentiment for all sentences in this aspect
                sentiments = []
                for sentence in sentences:
                    result = sentiment_analyzer.analyze_text(sentence)
                    sentiments.append(result['score'])
                
                aspect_sentiments[category] = np.mean(sentiments)
            else:
                aspect_sentiments[category] = 0.0
        
        return aspect_sentiments
    
    def extract_entities(self, text: str) -> List[Tuple[str, str]]:
        """استخراج موجودیت‌ها از متن"""
        entities = []
        
        if self.nlp:
            try:
                doc = self.nlp(text)
                for ent in doc.ents:
                    if ent.label_ in ['ORG', 'PERSON', 'MONEY', 'PERCENT', 'DATE']:
                        entities.append((ent.text, ent.label_))
            except Exception as e:
                logger.error(f"Error in entity extraction: {e}")
        
        return entities


class TemporalSentimentAnalyzer:
    """تحلیل احساسات زمانی"""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.sentiment_history = deque(maxlen=window_size)
        self.timestamp_history = deque(maxlen=window_size)
    
    def add_sentiment(self, sentiment: float, timestamp: datetime):
        """افزودن احساس جدید به تاریخچه"""
        self.sentiment_history.append(sentiment)
        self.timestamp_history.append(timestamp)
    
    def get_trend(self) -> str:
        """محاسبه روند احساسات"""
        if len(self.sentiment_history) < 5:
            return "neutral"
        
        recent = list(self.sentiment_history)[-10:]
        older = list(self.sentiment_history)[-20:-10] if len(self.sentiment_history) >= 20 else recent
        
        recent_avg = np.mean(recent)
        older_avg = np.mean(older)
        
        if recent_avg > older_avg + 0.1:
            return "improving"
        elif recent_avg < older_avg - 0.1:
            return "declining"
        else:
            return "stable"
    
    def get_momentum(self) -> float:
        """محاسبه شتاب تغییر احساسات"""
        if len(self.sentiment_history) < 3:
            return 0.0
        
        sentiments = list(self.sentiment_history)
        # Calculate first derivative (velocity)
        velocity = np.diff(sentiments)
        # Calculate second derivative (acceleration)
        if len(velocity) > 1:
            acceleration = np.diff(velocity)
            return np.mean(acceleration[-5:]) if len(acceleration) >= 5 else np.mean(acceleration)
        return 0.0
    
    def get_volatility(self) -> float:
        """محاسبه نوسان احساسات"""
        if len(self.sentiment_history) < 5:
            return 0.0
        
        return np.std(list(self.sentiment_history)[-20:])
    
    def predict_next_sentiment(self) -> float:
        """پیش‌بینی احساس بعدی"""
        if len(self.sentiment_history) < 10:
            return 0.0
        
        # Simple linear extrapolation
        x = np.arange(len(self.sentiment_history))
        y = np.array(list(self.sentiment_history))
        
        # Fit polynomial
        coeffs = np.polyfit(x[-10:], y[-10:], 2)
        poly = np.poly1d(coeffs)
        
        # Predict next value
        next_x = len(self.sentiment_history)
        return float(np.clip(poly(next_x), -1, 1))


class SocialMediaSentimentAnalyzer:
    """تحلیل احساسات رسانه‌های اجتماعی"""
    
    def __init__(self):
        self.platform_weights = {
            'twitter': 0.7,
            'reddit': 0.8,
            'telegram': 0.6,
            'news': 1.0,
            'blog': 0.5
        }
        
        # VADER for social media text
        self.vader = SentimentIntensityAnalyzer()
        
        # Emoji sentiment mapping
        self.emoji_sentiments = {
            '😊': 0.8, '😃': 0.9, '😄': 0.9, '😁': 0.8, '😆': 0.7,
            '😍': 1.0, '🥰': 0.9, '😘': 0.8, '💕': 0.9, '❤️': 0.8,
            '😢': -0.8, '😭': -0.9, '😔': -0.7, '😞': -0.8, '😟': -0.6,
            '😡': -0.9, '😠': -0.8, '🤬': -1.0, '😤': -0.7, '😒': -0.5,
            '🚀': 0.9, '📈': 0.8, '💰': 0.7, '🎯': 0.6, '✅': 0.5,
            '📉': -0.8, '💸': -0.6, '⚠️': -0.4, '🚨': -0.7, '❌': -0.6
        }
    
    def analyze_social_text(self, text: str, platform: str = 'twitter') -> Dict[str, float]:
        """تحلیل متن رسانه‌های اجتماعی"""
        # Extract emojis and calculate emoji sentiment
        emoji_score = self._calculate_emoji_sentiment(text)
        
        # Remove URLs and mentions for cleaner analysis
        clean_text = self._clean_social_text(text)
        
        # VADER analysis
        vader_scores = self.vader.polarity_scores(clean_text)
        
        # Combine scores
        combined_score = (vader_scores['compound'] + emoji_score) / 2
        
        # Apply platform weight
        platform_weight = self.platform_weights.get(platform, 0.5)
        weighted_score = combined_score * platform_weight
        
        return {
            'score': weighted_score,
            'vader_compound': vader_scores['compound'],
            'emoji_score': emoji_score,
            'platform_weight': platform_weight
        }
    
    def _calculate_emoji_sentiment(self, text: str) -> float:
        """محاسبه احساس بر اساس ایموجی‌ها"""
        emoji_scores = []
        for emoji, score in self.emoji_sentiments.items():
            if emoji in text:
                emoji_scores.append(score)
        
        return np.mean(emoji_scores) if emoji_scores else 0.0
    
    def _clean_social_text(self, text: str) -> str:
        """پاکسازی متن رسانه‌های اجتماعی"""
        import re
        # Remove URLs
        text = re.sub(r'http\S+|www.\S+', '', text)
        # Remove mentions
        text = re.sub(r'@\w+', '', text)
        # Remove hashtags (but keep the word)
        text = re.sub(r'#(\w+)', r'\1', text)
        return text.strip()


class MarketSentimentAggregator:
    """تجمیع احساسات بازار"""
    
    def __init__(self, time_window: timedelta = timedelta(hours=24)):
        self.time_window = time_window
        self.sentiment_buffer = deque()
        self.source_weights = {
            'reuters': 1.0,
            'bloomberg': 1.0,
            'financial_times': 0.95,
            'wsj': 0.95,
            'twitter': 0.7,
            'reddit': 0.75,
            'seeking_alpha': 0.8,
            'yahoo_finance': 0.85
        }
    
    def add_sentiment(self, result: SentimentResult):
        """افزودن نتیجه احساس به بافر"""
        self.sentiment_buffer.append(result)
        # Remove old sentiments
        cutoff_time = datetime.now() - self.time_window
        while self.sentiment_buffer and self.sentiment_buffer[0].timestamp < cutoff_time:
            self.sentiment_buffer.popleft()
    
    def calculate_market_sentiment(self) -> MarketSentiment:
        """محاسبه احساسات کلی بازار"""
        if not self.sentiment_buffer:
            return self._empty_market_sentiment()
        
        # Weight sentiments by source credibility and recency
        weighted_sentiments = []
        sentiment_distribution = defaultdict(float)
        source_distribution = defaultdict(float)
        
        for result in self.sentiment_buffer:
            # Calculate time decay
            age = (datetime.now() - result.timestamp).total_seconds() / 3600  # hours
            time_weight = np.exp(-age / 24)  # exponential decay over 24 hours
            
            # Get source weight
            source_weight = self.source_weights.get(result.source, 0.5)
            
            # Combined weight
            weight = time_weight * source_weight * result.credibility_weight
            
            weighted_sentiments.append((result.score, weight))
            sentiment_distribution[result.sentiment] += weight
            source_distribution[result.source] += weight
        
        # Calculate weighted average
        total_weight = sum(w for _, w in weighted_sentiments)
        if total_weight == 0:
            return self._empty_market_sentiment()
        
        overall_sentiment = sum(s * w for s, w in weighted_sentiments) / total_weight
        
        # Normalize distributions
        for key in sentiment_distribution:
            sentiment_distribution[key] /= total_weight
        for key in source_distribution:
            source_distribution[key] /= total_weight
        
        # Determine trend
        if overall_sentiment > 0.2:
            trend = "bullish"
        elif overall_sentiment < -0.2:
            trend = "bearish"
        else:
            trend = "neutral"
        
        # Calculate momentum and volatility
        recent_sentiments = [r.score for r in list(self.sentiment_buffer)[-20:]]
        momentum = np.mean(np.diff(recent_sentiments)) if len(recent_sentiments) > 1 else 0.0
        volatility = np.std(recent_sentiments) if len(recent_sentiments) > 1 else 0.0
        
        # Extract top topics (simplified - based on aspect mentions)
        topic_counts = defaultdict(int)
        for result in self.sentiment_buffer:
            for aspect in result.aspects:
                topic_counts[aspect.value] += 1
        
        top_topics = sorted(
            [(topic, count/len(self.sentiment_buffer)) for topic, count in topic_counts.items()],
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        # Calculate confidence based on agreement
        confidence = 1.0 - volatility if volatility < 0.5 else 0.5
        
        return MarketSentiment(
            overall_sentiment=overall_sentiment,
            sentiment_distribution=dict(sentiment_distribution),
            trend=trend,
            momentum=momentum,
            volatility=volatility,
            confidence=confidence,
            top_topics=top_topics,
            time_window=self.time_window,
            source_distribution=dict(source_distribution)
        )
    
    def _empty_market_sentiment(self) -> MarketSentiment:
        """بازگرداندن احساسات خالی بازار"""
        return MarketSentiment(
            overall_sentiment=0.0,
            sentiment_distribution={},
            trend="neutral",
            momentum=0.0,
            volatility=0.0,
            confidence=0.0,
            top_topics=[],
            time_window=self.time_window,
            source_distribution={}
        )


class SentimentCache:
    """کش هوشمند برای نتایج تحلیل احساسات"""
    
    def __init__(self, max_size: int = 10000, ttl: timedelta = timedelta(hours=1)):
        self.max_size = max_size
        self.ttl = ttl
        self.cache = {}
        self.access_times = {}
        self.hit_count = 0
        self.miss_count = 0
    
    def _get_cache_key(self, text: str, language: str, source: str) -> str:
        """تولید کلید کش"""
        content = f"{text}:{language}:{source}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def get(self, text: str, language: str, source: str) -> Optional[SentimentResult]:
        """دریافت از کش"""
        key = self._get_cache_key(text, language, source)
        
        if key in self.cache:
            # Check if expired
            if datetime.now() - self.access_times[key] < self.ttl:
                self.hit_count += 1
                self.access_times[key] = datetime.now()  # Update access time
                return self.cache[key]
            else:
                # Expired, remove from cache
                del self.cache[key]
                del self.access_times[key]
        
        self.miss_count += 1
        return None
    
    def put(self, text: str, language: str, source: str, result: SentimentResult):
        """ذخیره در کش"""
        key = self._get_cache_key(text, language, source)
        
        # Check cache size
        if len(self.cache) >= self.max_size:
            # Remove oldest entry
            oldest_key = min(self.access_times, key=self.access_times.get)
            del self.cache[oldest_key]
            del self.access_times[oldest_key]
        
        self.cache[key] = result
        self.access_times[key] = datetime.now()
    
    def get_stats(self) -> Dict[str, Any]:
        """آمار کش"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
        
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'hit_count': self.hit_count,
            'miss_count': self.miss_count,
            'hit_rate': hit_rate,
            'ttl': self.ttl.total_seconds()
        }


class SentimentImpactPredictor:
    """پیش‌بینی تأثیر احساسات بر بازار"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = [
            'sentiment_score', 'confidence', 'source_credibility',
            'market_volatility', 'trading_volume', 'time_of_day',
            'day_of_week', 'is_market_hours', 'trend_strength',
            'sentiment_momentum'
        ]
        self.is_trained = False
    
    def train(self, historical_data: pd.DataFrame):
        """آموزش مدل پیش‌بینی تأثیر"""
        if len(historical_data) < 100:
            logger.warning("Insufficient data for training impact predictor")
            return
        
        try:
            # Prepare features
            X = historical_data[self.feature_names].values
            y = historical_data['actual_price_change'].values
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Train model
            self.model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42
            )
            self.model.fit(X_scaled, y)
            self.is_trained = True
            
            logger.info("Sentiment impact predictor trained successfully")
        except Exception as e:
            logger.error(f"Error training impact predictor: {e}")
    
    def predict_impact(self, sentiment_result: SentimentResult, 
                      market_data: Dict[str, float]) -> float:
        """پیش‌بینی تأثیر احساس بر قیمت"""
        if not self.is_trained:
            return 0.0
        
        try:
            # Prepare features
            now = datetime.now()
            features = [
                sentiment_result.score,
                sentiment_result.confidence,
                sentiment_result.credibility_weight,
                market_data.get('volatility', 0.02),
                market_data.get('volume', 1000000),
                now.hour / 24,
                now.weekday() / 7,
                1 if 9 <= now.hour <= 16 else 0,  # Market hours
                market_data.get('trend_strength', 0.0),
                market_data.get('sentiment_momentum', 0.0)
            ]
            
            # Scale and predict
            features_scaled = self.scaler.transform([features])
            impact = self.model.predict(features_scaled)[0]
            
            # Clip to reasonable range (-5% to +5%)
            return np.clip(impact, -0.05, 0.05)
            
        except Exception as e:
            logger.error(f"Error predicting impact: {e}")
            return 0.0


class EnsembleSentimentAnalyzer:
    """ترکیب چند مدل برای تحلیل احساسات"""
    
    def __init__(self, models: List[LanguageModel]):
        self.models = models
        self.model_weights = {model.model_name: 1.0 for model in models}
        
    def analyze_ensemble(self, text: str) -> Dict[str, float]:
        """تحلیل با استفاده از همه مدل‌ها"""
        results = []
        weights = []
        
        for model in self.models:
            try:
                result = model.analyze(text)
                score = self._label_to_score(result['label'], result['score'])
                results.append(score)
                weights.append(self.model_weights[model.model_name])
            except Exception as e:
                logger.error(f"Error in ensemble model {model.model_name}: {e}")
                continue
        
        if not results:
            return {'score': 0.0, 'confidence': 0.0}
        
        # Weighted average
        weighted_score = np.average(results, weights=weights)
        
        # Confidence based on agreement
        confidence = 1.0 - np.std(results)
        
        return {
            'score': weighted_score,
            'confidence': confidence,
            'individual_scores': {
                model.model_name: score 
                for model, score in zip(self.models, results)
            }
        }
    
    def _label_to_score(self, label: str, confidence: float) -> float:
        """تبدیل برچسب به امتیاز"""
        label_map = {
            'positive': 1.0,
            'negative': -1.0,
            'neutral': 0.0,
            'very_positive': 1.0,
            'very_negative': -1.0
        }
        base_score = label_map.get(label, 0.0)
        return base_score * confidence
    
    def update_weights(self, performance_data: Dict[str, float]):
        """به‌روزرسانی وزن‌ها بر اساس عملکرد"""
        for model_name, performance in performance_data.items():
            if model_name in self.model_weights:
                # Update weight based on performance
                self.model_weights[model_name] = max(0.1, min(2.0, performance))


class AdvancedSentimentAnalyzer:
    """سیستم پیشرفته تحلیل احساسات چندزبانه"""
    
    def __init__(self, languages: List[str] = None, enable_cache: bool = True):
        """
        Initialize the Advanced Sentiment Analyzer
        
        Args:
            languages: List of language codes to support. If None, defaults to ['en', 'fa']
            enable_cache: Whether to enable result caching
        """
        # Set device
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Device set to use {self.device}")
        
        # Languages to support
        if languages is None:
            languages = ['en', 'fa']
        self.languages = languages
        
        # Initialize components
        self.language_models = {}
        self.aspect_analyzer = AspectBasedSentimentAnalyzer(self.device)
        self.temporal_analyzer = TemporalSentimentAnalyzer()
        self.social_analyzer = SocialMediaSentimentAnalyzer()
        self.market_aggregator = MarketSentimentAggregator()
        self.impact_predictor = SentimentImpactPredictor()
        
        # Initialize cache
        self.cache = SentimentCache() if enable_cache else None
        
        # Model configurations
        self.model_configs = {
            'en': [
                ('ProsusAI/finbert', 1.2),
                ('yiyanghkust/finbert-tone', 1.0),
                ('ahmedrachid/FinancialBERT-Sentiment-Analysis', 0.8)
            ],
            'fa': [
                ('HooshvareLab/bert-fa-base-uncased', 1.0),
                ('HooshvareLab/bert-fa-zwnj-base', 0.9)
            ]
        }
        
        # Load models
        self._load_language_models()
        
        # Source credibility (simplified - in production, use source_credibility.py)
        self.source_credibility = {
            'reuters': 1.0,
            'bloomberg': 1.0,
            'financial_times': 0.95,
            'wsj': 0.95,
            'cnbc': 0.9,
            'twitter': 0.7,
            'reddit': 0.75,
            'telegram': 0.6,
            'unknown': 0.5
        }
    
    def _load_language_models(self):
        """بارگذاری مدل‌های زبانی"""
        # Updated working models from HuggingFace
        model_configs = {
            'en': [
                'mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis',
                'siebert/sentiment-roberta-large-english',
                'cardiffnlp/twitter-roberta-base-sentiment-latest'
            ],
            'fa': [
                'HooshvareLab/bert-fa-base-uncased',
                'HooshvareLab/bert-fa-zwnj-base',
                'mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis'  # Fallback for Persian
            ]
        }
        
        for language in self.languages:
            models_for_lang = model_configs.get(language, [])
            self.language_models[language] = []
            
            for model_name in models_for_lang:
                try:
                    model = LanguageModel(language, model_name, self.device)
                    self.language_models[language].append(model)
                    logger.info(f"Successfully loaded {language} model: {model_name}")
                    break  # Use first successful model
                except Exception as e:
                    logger.error(f"Failed to load {model_name}: {e}")
                    continue
            
            # If no models loaded, use VADER as fallback
            if not self.language_models[language]:
                logger.warning(f"No model for language {language}, using VADER fallback")
                self.language_models[language] = []
    
    def detect_language(self, text: str) -> str:
        """تشخیص زبان متن"""
        try:
            lang = detect(text)
            # Map to supported languages
            if lang in self.languages:
                return lang
            elif lang == 'ar' and 'fa' in self.languages:
                # Arabic might be detected for Persian
                return 'fa'
            else:
                return 'en'  # Default to English
        except Exception as e:
            logger.error(f"Language detection error: {e}")
            return 'en'
    
    def analyze(self, text: str, language: Optional[str] = None, 
                source: Optional[str] = None, 
                aspect: Optional[str] = None) -> SentimentResult:
        """تحلیل احساسات متن"""
        
        # Detect language if not provided
        if language is None:
            language = self.detect_language(text)
        
        # Get models for the language
        models = self.language_models.get(language, [])
        
        if not models:
            # Use VADER as fallback
            vader_analyzer = SentimentIntensityAnalyzer()
            vader_result = vader_analyzer.polarity_scores(text)
            compound = vader_result['compound']
            
            if compound >= 0.05:
                label = 'positive'
                score = vader_result['pos']
            elif compound <= -0.05:
                label = 'negative'
                score = vader_result['neg']
            else:
                label = 'neutral'
                score = vader_result['neu']
            
            return SentimentResult(
                text=text,
                language=language,
                label=label,
                score=score,
                confidence=abs(compound),
                aspects={} if aspect else None,
                entities=[],
                temporal_info=None,
                source=source
            )
        
        # Use the first available model
        model = models[0]
        try:
            result = model.analyze(text)
            
            # بهبود تحلیل فارسی با کلمات کلیدی
            if language == 'fa':
                enhanced_result = self._enhance_persian_analysis(text, result)
                return SentimentResult(
                    text=text,
                    language=language,
                    label=enhanced_result['label'],
                    score=enhanced_result['score'],
                    confidence=enhanced_result['confidence'],
                    aspects={} if aspect else None,
                    entities=[],
                    temporal_info=None,
                    source=source
                )
            
            return SentimentResult(
                text=text,
                language=language,
                label=result['label'],
                score=result['score'],
                confidence=result['score'],
                aspects={} if aspect else None,
                entities=[],
                temporal_info=None,
                source=source
            )
        except Exception as e:
            logger.error(f"Error analyzing text: {e}")
            
            # Final fallback to VADER
            vader_analyzer = SentimentIntensityAnalyzer()
            vader_result = vader_analyzer.polarity_scores(text)
            compound = vader_result['compound']
            
            if compound >= 0.05:
                label = 'positive'
                score = vader_result['pos']
            elif compound <= -0.05:
                label = 'negative'
                score = vader_result['neg']
            else:
                label = 'neutral'
                score = vader_result['neu']
            
            return SentimentResult(
                text=text,
                language=language,
                label=label,
                score=score,
                confidence=abs(compound),
                aspects={} if aspect else None,
                entities=[],
                temporal_info=None,
                source=source
            )

    def _enhance_persian_analysis(self, text: str, base_result: Dict[str, Any]) -> Dict[str, Any]:
        """بهبود تحلیل فارسی با کلمات کلیدی"""
        
        # کلمات کلیدی مثبت
        positive_words = [
            'سود', 'افزایش', 'رشد', 'موفقیت', 'بهبود', 'قوی', 'عالی', 'خوب', 'مثبت',
            'بالا', 'زیاد', 'قدرتمند', 'برتر', 'بهترین', 'عالی', 'خوب', 'مطلوب',
            'پیشرفت', 'توسعه', 'گسترش', 'ارتقا', 'بهینه', 'کارآمد', 'سودآور'
        ]
        
        # کلمات کلیدی منفی
        negative_words = [
            'ضرر', 'کاهش', 'افت', 'شکست', 'بدتر', 'ضعیف', 'بد', 'منفی', 'کم',
            'کاهش', 'افت', 'سقوط', 'مشکل', 'خطر', 'ریسک', 'نگران', 'ترس',
            'افت', 'کاهش', 'ضعف', 'مشکل', 'خطرناک', 'نامطلوب', 'بد'
        ]
        
        # کلمات کلیدی مالی مثبت
        financial_positive = [
            'سود', 'درآمد', 'فروش', 'رشد', 'سهام', 'بازار', 'قیمت', 'ارزش',
            'سرمایه', 'ثروت', 'دارایی', 'مزیت', 'فرصت', 'پتانسیل'
        ]
        
        # کلمات کلیدی مالی منفی
        financial_negative = [
            'ضرر', 'زیان', 'افت', 'کاهش', 'ریزش', 'سقوط', 'بحران', 'مشکل',
            'خطر', 'ریسک', 'نگرانی', 'ترس', 'اضطراب', 'شکست'
        ]
        
        text_lower = text.lower()
        
        # شمارش کلمات مثبت و منفی
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        financial_positive_count = sum(1 for word in financial_positive if word in text_lower)
        financial_negative_count = sum(1 for word in financial_negative if word in text_lower)
        
        # محاسبه امتیاز بهبود یافته
        base_score = base_result['score']
        base_label = base_result['label']
        
        # تنظیم بر اساس کلمات کلیدی
        keyword_adjustment = 0.0
        
        if positive_count > negative_count:
            keyword_adjustment += 0.2
        elif negative_count > positive_count:
            keyword_adjustment -= 0.2
            
        if financial_positive_count > financial_negative_count:
            keyword_adjustment += 0.15
        elif financial_negative_count > financial_positive_count:
            keyword_adjustment -= 0.15
        
        # اعمال تنظیمات
        enhanced_score = max(0.0, min(1.0, base_score + keyword_adjustment))
        
        # تعیین برچسب نهایی
        if enhanced_score >= 0.6:
            final_label = 'positive'
        elif enhanced_score <= 0.4:
            final_label = 'negative'
        else:
            final_label = 'neutral'
        
        return {
            'label': final_label,
            'score': enhanced_score,
            'confidence': enhanced_score,
            'metadata': {
                'base_score': base_score,
                'base_label': base_label,
                'positive_words': positive_count,
                'negative_words': negative_count,
                'financial_positive': financial_positive_count,
                'financial_negative': financial_negative_count,
                'keyword_adjustment': keyword_adjustment
            }
        }
    
    def _analyze_entity_sentiment(self, text: str, entity: str, language: str) -> float:
        """تحلیل احساس برای یک موجودیت خاص"""
        sentences = text.split('.')
        entity_sentences = [s for s in sentences if entity.lower() in s.lower()]
        
        if not entity_sentences:
            return 0.0
        
        sentiments = []
        for sentence in entity_sentences:
            result = self.language_models[language].analyze_ensemble(sentence)
            sentiments.append(result['score'])
        
        return np.mean(sentiments)
    
    def analyze_social_media(self, text: str, platform: str = 'twitter', 
                           source: Optional[str] = None) -> SentimentResult:
        """تحلیل ویژه رسانه‌های اجتماعی"""
        # Get social media specific analysis
        social_result = self.social_analyzer.analyze_social_text(text, platform)
        
        # Also run through main analyzer
        main_result = self.analyze(text, source=source or platform)
        
        # Combine results
        combined_score = (main_result.score + social_result['score']) / 2
        
        # Update result
        main_result.score = combined_score
        main_result.metadata.update({
            'platform': platform,
            'emoji_score': social_result['emoji_score'],
            'vader_compound': social_result['vader_compound'],
            'platform_weight': social_result['platform_weight']
        })
        
        return main_result
    
    def get_market_sentiment(self) -> MarketSentiment:
        """دریافت احساسات کلی بازار"""
        return self.market_aggregator.calculate_market_sentiment()
    
    def get_sentiment_trend(self) -> Dict[str, Any]:
        """دریافت روند احساسات"""
        return {
            'trend': self.temporal_analyzer.get_trend(),
            'momentum': self.temporal_analyzer.get_momentum(),
            'volatility': self.temporal_analyzer.get_volatility(),
            'next_prediction': self.temporal_analyzer.predict_next_sentiment()
        }
    
    def train_impact_predictor(self, historical_data: pd.DataFrame):
        """آموزش مدل پیش‌بینی تأثیر"""
        self.impact_predictor.train(historical_data)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """آمار کش"""
        if self.cache:
            return self.cache.get_stats()
        return {'enabled': False}
    
    def batch_analyze(self, texts: List[str], sources: Optional[List[str]] = None,
                     parallel: bool = True, batch_size: int = 32) -> List[SentimentResult]:
        """تحلیل دسته‌ای بهینه"""
        if sources and len(sources) != len(texts):
            raise ValueError("Length of sources must match length of texts")
        
        if not sources:
            sources = ['unknown'] * len(texts)
        
        results = []
        
        # Process in batches
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_sources = sources[i:i + batch_size]
            
            batch_results = []
            for text, source in zip(batch_texts, batch_sources):
                result = self.analyze(text, source=source)
                batch_results.append(result)
            
            results.extend(batch_results)
        
        return results


# Simplified SentimentAnalyzer for backward compatibility
class SentimentAnalyzer(AdvancedSentimentAnalyzer):
    """Backward compatibility class"""
    
    def __init__(self, credibility_manager=None):
        super().__init__(languages=['en'], enable_cache=False)
        self.credibility_manager = credibility_manager
        # Initialize VADER as fallback
        self.vader_analyzer = SentimentIntensityAnalyzer()
    
    def get_sentiment_score(self, text: str, language: Optional[str] = None, 
                           source: Optional[str] = None) -> float:
        """Get sentiment score for text (backward compatibility)"""
        try:
            # Try advanced analysis first
            result = self.analyze(text, language=language, source=source)
            return result.score
        except Exception as e:
            logger.warning(f"Advanced analysis failed, using VADER fallback: {e}")
            # Fallback to VADER
            vader_scores = self.vader_analyzer.polarity_scores(text)
            return vader_scores['compound']


if __name__ == "__main__":
    # Example usage
    analyzer = AdvancedSentimentAnalyzer()
    
    # Test texts
    test_texts = [
        ("The company reported exceptional earnings, beating all analyst expectations! 🚀", "twitter"),
        ("Stock price plummeted after CEO resignation announcement 😢", "reddit"),
        ("Market remains stable with moderate trading volume", "reuters"),
        ("شرکت سود فوق‌العاده‌ای گزارش داد که فراتر از انتظارات بود", "financial_times"),
        ("قیمت سهام پس از اعلام استعفای مدیرعامل سقوط کرد", "telegram")
    ]
    
    print("=== Advanced Sentiment Analysis Demo ===\n")
    
    for text, source in test_texts:
        result = analyzer.analyze(
            text, 
            source=source,
            analyze_aspects=True,
            analyze_entities=True,
            predict_impact=True,
            market_data={
                'volatility': 0.02,
                'volume': 1000000,
                'trend_strength': 0.5,
                'sentiment_momentum': 0.1
            }
        )
        
        print(f"Text: {text}")
        print(f"Source: {source}")
        print(f"Language: {result.language}")
        print(f"Sentiment: {result.sentiment.value} (score: {result.score:.3f})")
        print(f"Confidence: {result.confidence:.3f}")
        print(f"Credibility Weight: {result.credibility_weight:.2f}")
        
        if result.aspects:
            print("Aspects:")
            for aspect, score in result.aspects.items():
                print(f"  - {aspect.value}: {score:.3f}")
        
        if result.entities:
            print("Entities:")
            for entity, entity_type, sentiment in result.entities:
                print(f"  - {entity} ({entity_type}): {sentiment:.3f}")
        
        if result.market_impact:
            print(f"Predicted Market Impact: {result.market_impact:.4f}")
        
        print("-" * 80)
    
    # Market sentiment
    print("\n=== Market Sentiment ===")
    market_sentiment = analyzer.get_market_sentiment()
    print(f"Overall Sentiment: {market_sentiment.overall_sentiment:.3f}")
    print(f"Trend: {market_sentiment.trend}")
    print(f"Momentum: {market_sentiment.momentum:.3f}")
    print(f"Volatility: {market_sentiment.volatility:.3f}")
    print(f"Confidence: {market_sentiment.confidence:.3f}")
    
    # Sentiment trend
    print("\n=== Sentiment Trend ===")
    trend = analyzer.get_sentiment_trend()
    print(f"Trend Direction: {trend['trend']}")
    print(f"Momentum: {trend['momentum']:.3f}")
    print(f"Volatility: {trend['volatility']:.3f}")
    print(f"Next Prediction: {trend['next_prediction']:.3f}")
    
    # Cache stats
    print("\n=== Cache Statistics ===")
    cache_stats = analyzer.get_cache_stats()
    for key, value in cache_stats.items():
        print(f"{key}: {value}") 