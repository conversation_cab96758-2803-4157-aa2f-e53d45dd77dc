#!/usr/bin/env python3
"""
🔍 بررسی کامل تمام مدل‌های معرفی شده
Complete Model Audit

این اسکریپت همه مدل‌هایی که تا الان معرفی شدن رو چک می‌کنه
"""

import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path

# Set proxy if available
if os.path.exists("PROXY.json"):
    os.environ["HTTP_PROXY"] = "http://127.0.0.1:10809"
    os.environ["HTTPS_PROXY"] = "http://127.0.0.1:10809"

class ModelAuditor:
    def __init__(self):
        self.all_models = {
            # Financial Sentiment Models
            "finbert": {
                "path": "ProsusAI/finbert",
                "type": "sentiment",
                "description": "تحلیل احساسات مالی",
                "mentioned_in": "core/ai_models.py",
                "expected_size": "~134MB"
            },
            "cryptobert": {
                "path": "ElKulako/cryptobert",
                "type": "sentiment",
                "description": "تحلیل احساسات کریپتو",
                "mentioned_in": "core/ai_models.py",
                "expected_size": "~110MB"
            },
            "financial_sentiment": {
                "path": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
                "type": "sentiment",
                "description": "تحلیل احساسات اخبار مالی",
                "mentioned_in": "core/ai_models.py",
                "expected_size": "~80MB"
            },
            
            # Time Series Models
            "chronos_tiny": {
                "path": "amazon/chronos-t5-tiny",
                "type": "time_series",
                "description": "پیش‌بینی time series سبک",
                "mentioned_in": "core/ai_models.py",
                "expected_size": "~50MB"
            },
            "chronos_small": {
                "path": "amazon/chronos-t5-small",
                "type": "time_series",
                "description": "پیش‌بینی time series متوسط",
                "mentioned_in": "core/ai_models.py",
                "expected_size": "~200MB"
            },
            "chronos_base": {
                "path": "amazon/chronos-t5-base",
                "type": "time_series",
                "description": "پیش‌بینی time series پایه",
                "mentioned_in": "core/ai_models.py",
                "expected_size": "~400MB"
            },
            
            # Embedding Models
            "sentence_transformer": {
                "path": "sentence-transformers/all-MiniLM-L6-v2",
                "type": "embedding",
                "description": "تولید embedding کلی",
                "mentioned_in": "core/ai_models.py",
                "expected_size": "~90MB"
            },
            "financial_bert": {
                "path": "nlpaueb/sec-bert-base",
                "type": "embedding",
                "description": "BERT مالی",
                "mentioned_in": "core/ai_models.py",
                "expected_size": "~440MB"
            },
            
            # Document Processing Models
            "layoutlm": {
                "path": "microsoft/layoutlmv3-base",
                "type": "document",
                "description": "پردازش اسناد",
                "mentioned_in": "core/ai_models.py",
                "expected_size": "~500MB"
            },
            
            # Summarization Models
            "bart_cnn": {
                "path": "facebook/bart-large-cnn",
                "type": "summarization",
                "description": "خلاصه‌سازی متن",
                "mentioned_in": "core/ai_models.py",
                "expected_size": "~1.6GB"
            },
            
            # General Language Models
            "distilbert": {
                "path": "distilbert-base-uncased",
                "type": "general",
                "description": "مدل زبان عمومی",
                "mentioned_in": "test files",
                "expected_size": "~268MB"
            },
            "bert_tiny": {
                "path": "prajjwal1/bert-tiny",
                "type": "general",
                "description": "مدل سبک و سریع",
                "mentioned_in": "test files",
                "expected_size": "~17MB"
            },
            "roberta_base": {
                "path": "roberta-base",
                "type": "general",
                "description": "مدل RoBERTa پایه",
                "mentioned_in": "install scripts",
                "expected_size": "~500MB"
            },
            "distilgpt2": {
                "path": "distilgpt2",
                "type": "generation",
                "description": "تولید متن",
                "mentioned_in": "install scripts",
                "expected_size": "~350MB"
            },
            
            # Specialized Financial Models
            "sec_bert": {
                "path": "nlpaueb/sec-bert-base",
                "type": "financial",
                "description": "BERT برای اسناد SEC",
                "mentioned_in": "core/ai_models.py",
                "expected_size": "~440MB"
            },
            
            # Additional Models mentioned
            "trading_bert": {
                "path": "jean-baptiste/camembert-ner-with-dates",
                "type": "ner",
                "description": "تشخیص نام‌های تجاری",
                "mentioned_in": "examples",
                "expected_size": "~440MB"
            }
        }
        
        self.results = {}
        
    def check_local_cache(self):
        """بررسی cache محلی"""
        print("🗂️ Checking local HuggingFace cache...")
        
        cache_dir = Path.home() / ".cache" / "huggingface" / "hub"
        
        cached_models = []
        
        if cache_dir.exists():
            model_dirs = list(cache_dir.glob("models--*"))
            
            for model_dir in model_dirs:
                model_name = model_dir.name.replace("models--", "").replace("--", "/")
                cached_models.append(model_name)
                print(f"   📦 Found: {model_name}")
        
        return cached_models
    
    def test_model_import(self, model_name, model_path):
        """تست import کردن مدل"""
        print(f"\n🧪 Testing: {model_name}")
        print(f"   Path: {model_path}")
        
        result = {
            "name": model_name,
            "path": model_path,
            "can_import": False,
            "can_load": False,
            "can_use": False,
            "error": None,
            "test_result": None
        }
        
        try:
            # Try to import transformers
            from transformers import AutoTokenizer, AutoModel
            
            # Check if model exists in cache (offline mode)
            print("   📥 Checking cache...")
            
            try:
                # Try to load without internet
                tokenizer = AutoTokenizer.from_pretrained(model_path, local_files_only=True)
                model = AutoModel.from_pretrained(model_path, local_files_only=True)
                
                result["can_import"] = True
                result["can_load"] = True
                
                print("   ✅ Found in cache, loading...")
                
                # Test basic functionality
                test_text = "Test sentence for model"
                inputs = tokenizer(test_text, return_tensors="pt")
                
                import torch
                with torch.no_grad():
                    outputs = model(**inputs)
                
                result["can_use"] = True
                result["test_result"] = f"Output shape: {outputs.last_hidden_state.shape}"
                
                print(f"   ✅ Working! {result['test_result']}")
                
            except Exception as offline_error:
                print(f"   ❌ Not in cache: {offline_error}")
                
                # Try online download
                print("   📡 Trying online download...")
                
                try:
                    tokenizer = AutoTokenizer.from_pretrained(model_path)
                    model = AutoModel.from_pretrained(model_path)
                    
                    result["can_import"] = True
                    result["can_load"] = True
                    
                    # Test basic functionality
                    test_text = "Test sentence for model"
                    inputs = tokenizer(test_text, return_tensors="pt")
                    
                    import torch
                    with torch.no_grad():
                        outputs = model(**inputs)
                    
                    result["can_use"] = True
                    result["test_result"] = f"Output shape: {outputs.last_hidden_state.shape}"
                    
                    print(f"   ✅ Downloaded and working! {result['test_result']}")
                    
                except Exception as online_error:
                    result["error"] = f"Online download failed: {online_error}"
                    print(f"   ❌ Download failed: {online_error}")
                
        except ImportError as e:
            result["error"] = f"Import failed: {e}"
            print(f"   ❌ Import failed: {e}")
        
        except Exception as e:
            result["error"] = f"Unexpected error: {e}"
            print(f"   ❌ Unexpected error: {e}")
        
        return result
    
    def test_specialized_models(self):
        """تست مدل‌های تخصصی"""
        print("\n🎯 Testing specialized models...")
        
        # Test sentence transformers
        try:
            from sentence_transformers import SentenceTransformer
            
            print("   🔤 Testing sentence transformers...")
            
            try:
                model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")
                
                test_sentences = ["Test sentence", "Another test"]
                embeddings = model.encode(test_sentences)
                
                print(f"   ✅ Sentence Transformers working! Shape: {embeddings.shape}")
                
                return True
                
            except Exception as e:
                print(f"   ❌ Sentence Transformers failed: {e}")
                return False
                
        except ImportError:
            print("   ⚠️ sentence-transformers not installed")
            return False
    
    def audit_all_models(self):
        """بررسی تمام مدل‌ها"""
        print("🔍 Starting complete model audit...")
        print("=" * 60)
        
        # Check cache first
        cached_models = self.check_local_cache()
        
        # Test each model
        for model_name, model_info in self.all_models.items():
            result = self.test_model_import(model_name, model_info["path"])
            result.update(model_info)
            
            # Check if in cache
            result["in_cache"] = model_info["path"] in cached_models
            
            self.results[model_name] = result
            
            # Small delay between tests
            time.sleep(0.5)
        
        # Test specialized models
        self.test_specialized_models()
        
        return self.results
    
    def generate_report(self):
        """تولید گزارش"""
        print("\n📊 Generating comprehensive report...")
        
        # Categorize results
        working_models = []
        cached_models = []
        failed_models = []
        
        for name, result in self.results.items():
            if result["can_use"]:
                working_models.append(result)
            elif result["in_cache"]:
                cached_models.append(result)
            else:
                failed_models.append(result)
        
        # Print summary
        print("\n" + "=" * 60)
        print("🎯 AUDIT SUMMARY")
        print("=" * 60)
        
        print(f"📊 Total models checked: {len(self.results)}")
        print(f"✅ Working models: {len(working_models)}")
        print(f"💾 Cached models: {len(cached_models)}")
        print(f"❌ Failed models: {len(failed_models)}")
        
        if working_models:
            print("\n🏆 WORKING MODELS:")
            for model in working_models:
                print(f"   ✅ {model['name']}: {model['description']}")
                print(f"      Path: {model['path']}")
                print(f"      Test: {model['test_result']}")
                print()
        
        if cached_models:
            print("\n💾 CACHED BUT NOT TESTED:")
            for model in cached_models:
                print(f"   📦 {model['name']}: {model['description']}")
                print(f"      Path: {model['path']}")
                print()
        
        if failed_models:
            print("\n❌ FAILED MODELS:")
            for model in failed_models:
                print(f"   ❌ {model['name']}: {model['description']}")
                print(f"      Path: {model['path']}")
                print(f"      Error: {model['error']}")
                print()
        
        # Save detailed report
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total": len(self.results),
                "working": len(working_models),
                "cached": len(cached_models),
                "failed": len(failed_models)
            },
            "results": self.results
        }
        
        report_file = f"complete_model_audit_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Detailed report saved to: {report_file}")
        
        return report

def main():
    """تابع اصلی"""
    print("🔍 Complete AI Model Audit Tool")
    print("ابزار بررسی کامل مدل‌های هوش مصنوعی")
    print("=" * 60)
    
    auditor = ModelAuditor()
    
    try:
        # Run complete audit
        results = auditor.audit_all_models()
        
        # Generate report
        report = auditor.generate_report()
        
        print("\n🎉 Audit completed successfully!")
        
        # Final recommendation
        working_count = report["summary"]["working"]
        total_count = report["summary"]["total"]
        
        if working_count > 0:
            print(f"\n✅ You have {working_count}/{total_count} models working!")
            print("💡 These models are ready for use in your trading system.")
        else:
            print("\n⚠️ No models are currently working.")
            print("💡 Consider checking your internet connection and proxy settings.")
    
    except KeyboardInterrupt:
        print("\n⚠️ Audit interrupted by user")
    except Exception as e:
        print(f"\n❌ Audit failed: {e}")

if __name__ == "__main__":
    main() 