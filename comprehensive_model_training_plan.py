"""
🎯 Comprehensive Model Training Plan for Pearl-3x7B
نقشه جامع آموزش مدل‌ها برای Pearl-3x7B

این فایل شامل:
1. شناسایی کامل تمام مدل‌ها و قابلیت‌ها
2. طراحی pipeline آموزش بهینه
3. سیستم ارزیابی و انتخاب بهترین مدل‌ها
4. استراتژی بهینه‌سازی و تست
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

@dataclass
class ModelInfo:
    """اطلاعات مدل"""
    name: str
    category: str
    type: str
    trainer_class: str
    config_class: str
    evaluation_metrics: List[str]
    training_time_estimate: int  # minutes
    memory_requirement: int  # MB
    dependencies: List[str]
    status: str = "not_trained"

@dataclass
class TrainingPlan:
    """طرح آموزش"""
    model_info: ModelInfo
    priority: int  # 1=highest, 5=lowest
    training_config: Dict[str, Any]
    evaluation_strategy: str
    optimization_strategy: str
    expected_performance: Dict[str, float]

class ComprehensiveModelInventory:
    """فهرست جامع مدل‌های موجود"""
    
    def __init__(self):
        self.models = self._discover_all_models()
        self.training_plans = self._create_training_plans()
        
    def _discover_all_models(self) -> Dict[str, ModelInfo]:
        """شناسایی تمام مدل‌های موجود"""
        
        models = {}
        
        # 1. Sentiment Analysis Models
        models.update({
            "FinBERT": ModelInfo(
                name="FinBERT",
                category="sentiment",
                type="transformer",
                trainer_class="PearlSentimentTrainer",
                config_class="SentimentTrainingConfig",
                evaluation_metrics=["accuracy", "precision", "recall", "f1_score"],
                training_time_estimate=45,
                memory_requirement=2048,
                dependencies=["transformers", "torch"]
            ),
            "CryptoBERT": ModelInfo(
                name="CryptoBERT", 
                category="sentiment",
                type="transformer",
                trainer_class="PearlSentimentTrainer",
                config_class="SentimentTrainingConfig",
                evaluation_metrics=["accuracy", "precision", "recall", "f1_score"],
                training_time_estimate=40,
                memory_requirement=1800,
                dependencies=["transformers", "torch"]
            ),
            "FinancialSentimentModel": ModelInfo(
                name="FinancialSentimentModel",
                category="sentiment", 
                type="ensemble",
                trainer_class="PearlSentimentTrainer",
                config_class="SentimentTrainingConfig",
                evaluation_metrics=["accuracy", "precision", "recall", "f1_score", "ensemble_agreement"],
                training_time_estimate=60,
                memory_requirement=3000,
                dependencies=["transformers", "torch", "sklearn"]
            ),
            "SentimentEnsemble": ModelInfo(
                name="SentimentEnsemble",
                category="sentiment",
                type="ensemble",
                trainer_class="PearlSentimentTrainer", 
                config_class="SentimentTrainingConfig",
                evaluation_metrics=["accuracy", "precision", "recall", "f1_score", "ensemble_diversity"],
                training_time_estimate=90,
                memory_requirement=4000,
                dependencies=["transformers", "torch", "sklearn"]
            )
        })
        
        # 2. Time Series Models
        models.update({
            "LSTM_TimeSeries": ModelInfo(
                name="LSTM_TimeSeries",
                category="timeseries",
                type="lstm",
                trainer_class="PearlTimeSeriesTrainer",
                config_class="TimeSeriesTrainingConfig", 
                evaluation_metrics=["rmse", "mae", "mape", "directional_accuracy"],
                training_time_estimate=30,
                memory_requirement=1024,
                dependencies=["torch", "numpy", "pandas"]
            ),
            "GRU_TimeSeries": ModelInfo(
                name="GRU_TimeSeries",
                category="timeseries",
                type="gru",
                trainer_class="PearlTimeSeriesTrainer",
                config_class="TimeSeriesTrainingConfig",
                evaluation_metrics=["rmse", "mae", "mape", "directional_accuracy"],
                training_time_estimate=25,
                memory_requirement=900,
                dependencies=["torch", "numpy", "pandas"]
            ),
            "Transformer_TimeSeries": ModelInfo(
                name="Transformer_TimeSeries",
                category="timeseries", 
                type="transformer",
                trainer_class="PearlTimeSeriesTrainer",
                config_class="TimeSeriesTrainingConfig",
                evaluation_metrics=["rmse", "mae", "mape", "directional_accuracy", "attention_weights"],
                training_time_estimate=50,
                memory_requirement=2048,
                dependencies=["torch", "numpy", "pandas"]
            ),
            "ChronosModel": ModelInfo(
                name="ChronosModel",
                category="timeseries",
                type="foundation",
                trainer_class="PearlTimeSeriesTrainer",
                config_class="TimeSeriesTrainingConfig",
                evaluation_metrics=["rmse", "mae", "mape", "directional_accuracy"],
                training_time_estimate=20,
                memory_requirement=1500,
                dependencies=["chronos", "torch"]
            ),
            "TimeSeriesEnsemble": ModelInfo(
                name="TimeSeriesEnsemble",
                category="timeseries",
                type="ensemble", 
                trainer_class="PearlTimeSeriesTrainer",
                config_class="TimeSeriesTrainingConfig",
                evaluation_metrics=["rmse", "mae", "mape", "directional_accuracy", "ensemble_variance"],
                training_time_estimate=80,
                memory_requirement=3500,
                dependencies=["torch", "numpy", "pandas", "sklearn"]
            )
        })
        
        # 3. Reinforcement Learning Models
        models.update({
            "DQN_Agent": ModelInfo(
                name="DQN_Agent",
                category="reinforcement_learning",
                type="dqn",
                trainer_class="PearlRLTrainer",
                config_class="RLTrainingConfig",
                evaluation_metrics=["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"],
                training_time_estimate=60,
                memory_requirement=1200,
                dependencies=["torch", "gym", "numpy"]
            ),
            "A2C_Agent": ModelInfo(
                name="A2C_Agent",
                category="reinforcement_learning",
                type="a2c",
                trainer_class="PearlRLTrainer", 
                config_class="RLTrainingConfig",
                evaluation_metrics=["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"],
                training_time_estimate=45,
                memory_requirement=1000,
                dependencies=["torch", "gym", "numpy"]
            ),
            "PPO_Agent": ModelInfo(
                name="PPO_Agent",
                category="reinforcement_learning",
                type="ppo",
                trainer_class="PearlRLTrainer",
                config_class="RLTrainingConfig", 
                evaluation_metrics=["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"],
                training_time_estimate=55,
                memory_requirement=1100,
                dependencies=["torch", "gym", "numpy"]
            ),
            "TD3_Agent": ModelInfo(
                name="TD3_Agent",
                category="reinforcement_learning",
                type="td3",
                trainer_class="PearlRLTrainer",
                config_class="RLTrainingConfig",
                evaluation_metrics=["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"],
                training_time_estimate=70,
                memory_requirement=1300,
                dependencies=["torch", "gym", "numpy"]
            )
        })
        
        # 4. Advanced Models (from existing codebase)
        models.update({
            "HierarchicalRL": ModelInfo(
                name="HierarchicalRL",
                category="advanced_rl",
                type="hierarchical",
                trainer_class="HierarchicalRLTrainer",
                config_class="HierarchicalRLConfig",
                evaluation_metrics=["avg_reward", "success_rate", "hierarchy_efficiency"],
                training_time_estimate=90,
                memory_requirement=2000,
                dependencies=["torch", "gym", "numpy"]
            ),
            "MetaLearner": ModelInfo(
                name="MetaLearner",
                category="meta_learning",
                type="meta",
                trainer_class="MetaLearnerTrainer",
                config_class="MetaLearnerConfig",
                evaluation_metrics=["adaptation_speed", "few_shot_accuracy", "generalization"],
                training_time_estimate=120,
                memory_requirement=2500,
                dependencies=["torch", "higher", "numpy"]
            ),
            "ZeroShotLearning": ModelInfo(
                name="ZeroShotLearning",
                category="zero_shot",
                type="zero_shot",
                trainer_class="ZeroShotTrainer",
                config_class="ZeroShotConfig",
                evaluation_metrics=["zero_shot_accuracy", "transfer_efficiency"],
                training_time_estimate=80,
                memory_requirement=1800,
                dependencies=["torch", "transformers", "clip"]
            ),
            "ContinualLearning": ModelInfo(
                name="ContinualLearning",
                category="continual",
                type="continual",
                trainer_class="ContinualLearningTrainer",
                config_class="ContinualLearningConfig",
                evaluation_metrics=["retention_rate", "plasticity", "catastrophic_forgetting"],
                training_time_estimate=100,
                memory_requirement=2200,
                dependencies=["torch", "avalanche", "numpy"]
            )
        })
        
        # 5. Ensemble and Unified Models
        models.update({
            "UnifiedTradingSystem": ModelInfo(
                name="UnifiedTradingSystem",
                category="unified",
                type="ensemble",
                trainer_class="UnifiedSystemTrainer",
                config_class="UnifiedSystemConfig",
                evaluation_metrics=["overall_performance", "component_agreement", "system_stability"],
                training_time_estimate=150,
                memory_requirement=5000,
                dependencies=["torch", "sklearn", "numpy", "pandas"]
            ),
            "ModelEnsemble": ModelInfo(
                name="ModelEnsemble",
                category="ensemble",
                type="weighted_ensemble",
                trainer_class="EnsembleTrainer",
                config_class="EnsembleConfig",
                evaluation_metrics=["ensemble_accuracy", "diversity_score", "weight_stability"],
                training_time_estimate=40,
                memory_requirement=1500,
                dependencies=["sklearn", "numpy"]
            )
        })
        
        return models
    
    def _create_training_plans(self) -> Dict[str, TrainingPlan]:
        """ایجاد طرح‌های آموزش"""
        
        plans = {}
        
        # Priority 1: Core Trading Models (Essential)
        core_models = ["DQN_Agent", "PPO_Agent", "LSTM_TimeSeries", "FinBERT"]
        
        # Priority 2: Enhanced Models
        enhanced_models = ["A2C_Agent", "GRU_TimeSeries", "CryptoBERT", "ChronosModel"]
        
        # Priority 3: Advanced Models
        advanced_models = ["Transformer_TimeSeries", "TD3_Agent", "FinancialSentimentModel"]
        
        # Priority 4: Ensemble Models
        ensemble_models = ["TimeSeriesEnsemble", "SentimentEnsemble", "ModelEnsemble"]
        
        # Priority 5: Research Models
        research_models = ["HierarchicalRL", "MetaLearner", "ZeroShotLearning", "ContinualLearning", "UnifiedTradingSystem"]
        
        for model_name, model_info in self.models.items():
            if model_name in core_models:
                priority = 1
            elif model_name in enhanced_models:
                priority = 2
            elif model_name in advanced_models:
                priority = 3
            elif model_name in ensemble_models:
                priority = 4
            else:
                priority = 5
            
            plans[model_name] = TrainingPlan(
                model_info=model_info,
                priority=priority,
                training_config=self._get_training_config(model_info),
                evaluation_strategy=self._get_evaluation_strategy(model_info),
                optimization_strategy=self._get_optimization_strategy(model_info, priority),
                expected_performance=self._get_expected_performance(model_info)
            )
        
        return plans
    
    def _get_training_config(self, model_info: ModelInfo) -> Dict[str, Any]:
        """دریافت پیکربندی آموزش"""
        
        base_config = {
            "batch_size": 32,
            "learning_rate": 0.001,
            "num_epochs": 50,
            "early_stopping": True,
            "patience": 10,
            "validation_split": 0.2,
            "test_split": 0.1
        }
        
        # Customize based on model type
        if model_info.category == "sentiment":
            base_config.update({
                "learning_rate": 2e-5,
                "num_epochs": 10,
                "batch_size": 16
            })
        elif model_info.category == "timeseries":
            base_config.update({
                "sequence_length": 60,
                "prediction_horizon": 1,
                "num_epochs": 100
            })
        elif model_info.category == "reinforcement_learning":
            base_config.update({
                "num_episodes": 1000,
                "max_steps_per_episode": 1000,
                "exploration_rate": 0.1
            })
        
        return base_config
    
    def _get_evaluation_strategy(self, model_info: ModelInfo) -> str:
        """دریافت استراتژی ارزیابی"""
        
        if model_info.category == "sentiment":
            return "cross_validation_with_stratification"
        elif model_info.category == "timeseries":
            return "time_series_split_validation"
        elif model_info.category == "reinforcement_learning":
            return "episode_based_evaluation"
        else:
            return "holdout_validation"
    
    def _get_optimization_strategy(self, model_info: ModelInfo, priority: int = 3) -> str:
        """دریافت استراتژی بهینه‌سازی"""

        if priority <= 2:
            return "bayesian_optimization"
        elif priority <= 4:
            return "grid_search"
        else:
            return "random_search"
    
    def _get_expected_performance(self, model_info: ModelInfo) -> Dict[str, float]:
        """دریافت عملکرد مورد انتظار"""
        
        if model_info.category == "sentiment":
            return {
                "accuracy": 0.85,
                "precision": 0.82,
                "recall": 0.88,
                "f1_score": 0.85
            }
        elif model_info.category == "timeseries":
            return {
                "rmse": 0.05,
                "mae": 0.03,
                "mape": 5.0,
                "directional_accuracy": 0.65
            }
        elif model_info.category == "reinforcement_learning":
            return {
                "avg_reward": 100.0,
                "success_rate": 0.7,
                "sharpe_ratio": 1.5,
                "max_drawdown": 0.15
            }
        else:
            return {"performance_score": 0.8}
    
    def get_training_order(self) -> List[str]:
        """دریافت ترتیب آموزش مدل‌ها"""
        
        # Sort by priority, then by training time
        sorted_plans = sorted(
            self.training_plans.items(),
            key=lambda x: (x[1].priority, x[1].model_info.training_time_estimate)
        )
        
        return [plan[0] for plan in sorted_plans]
    
    def get_models_by_category(self, category: str) -> List[str]:
        """دریافت مدل‌ها بر اساس دسته‌بندی"""
        
        return [
            name for name, model in self.models.items()
            if model.category == category
        ]
    
    def get_memory_requirements(self) -> Dict[str, int]:
        """دریافت نیازهای حافظه"""
        
        return {
            name: model.memory_requirement
            for name, model in self.models.items()
        }
    
    def export_training_plan(self, filepath: str):
        """صادرات طرح آموزش"""
        
        export_data = {
            "models": {name: asdict(model) for name, model in self.models.items()},
            "training_plans": {name: asdict(plan) for name, plan in self.training_plans.items()},
            "training_order": self.get_training_order(),
            "categories": {
                "sentiment": self.get_models_by_category("sentiment"),
                "timeseries": self.get_models_by_category("timeseries"), 
                "reinforcement_learning": self.get_models_by_category("reinforcement_learning"),
                "advanced": self.get_models_by_category("advanced_rl") + 
                           self.get_models_by_category("meta_learning") +
                           self.get_models_by_category("zero_shot") +
                           self.get_models_by_category("continual"),
                "ensemble": self.get_models_by_category("ensemble") + 
                           self.get_models_by_category("unified")
            },
            "memory_requirements": self.get_memory_requirements(),
            "total_training_time_estimate": sum(
                plan.model_info.training_time_estimate 
                for plan in self.training_plans.values()
            ),
            "generated_at": datetime.now().isoformat()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📄 Training plan exported to: {filepath}")

if __name__ == "__main__":
    print("🎯 COMPREHENSIVE MODEL TRAINING PLAN")
    print("=" * 60)
    
    # Create inventory
    inventory = ComprehensiveModelInventory()
    
    # Print summary
    print(f"📊 Total Models Discovered: {len(inventory.models)}")
    print(f"⏱️  Total Training Time Estimate: {sum(plan.model_info.training_time_estimate for plan in inventory.training_plans.values())} minutes")
    print(f"💾 Total Memory Requirement: {sum(model.memory_requirement for model in inventory.models.values())} MB")
    
    print("\n📋 Models by Category:")
    categories = {}
    for model in inventory.models.values():
        if model.category not in categories:
            categories[model.category] = []
        categories[model.category].append(model.name)
    
    for category, models in categories.items():
        print(f"  {category}: {len(models)} models")
        for model in models:
            priority = inventory.training_plans[model].priority
            print(f"    - {model} (Priority {priority})")
    
    print("\n🎯 Recommended Training Order:")
    training_order = inventory.get_training_order()
    for i, model_name in enumerate(training_order[:10], 1):  # Show first 10
        plan = inventory.training_plans[model_name]
        print(f"  {i:2d}. {model_name} (Priority {plan.priority}, {plan.model_info.training_time_estimate}min)")
    
    if len(training_order) > 10:
        print(f"     ... and {len(training_order) - 10} more models")
    
    # Export plan
    export_file = f"comprehensive_training_plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    inventory.export_training_plan(export_file)
    
    print(f"\n🎉 Comprehensive training plan ready!")
    print(f"📁 Detailed plan saved to: {export_file}")
