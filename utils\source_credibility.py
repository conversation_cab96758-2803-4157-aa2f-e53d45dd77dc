import json
import os
import logging
from typing import Dict, Optional, List, Tuple
import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SourceCredibility:
    """
    A class to manage credibility scores for news sources.
    
    The credibility score is a value between 0.0 and 1.0, where:
    - 1.0 represents a completely trustworthy source
    - 0.0 represents a completely untrustworthy source
    - 0.5 is the default for unknown sources
    """
    
    def __init__(self, initial_sources: Dict[str, float] = None, storage_path: str = None):
        """
        Initialize the SourceCredibility manager.
        
        Args:
            initial_sources (Dict[str, float], optional): Initial mapping of source names to credibility scores.
            storage_path (str, optional): Path to store/load credibility data. If None, uses default path.
        """
        # Default credibility score for unknown sources
        self.default_score = 0.5
        
        # Set storage path
        self.storage_path = storage_path or os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'data',
            'source_credibility.json'
        )
        
        # Initialize source credibility scores
        self.sources = {}
        
        # Load existing data if available
        self._load_data()
        
        # Add initial sources if provided
        if initial_sources:
            for source, score in initial_sources.items():
                self.set_score(source, score)
    
    def _load_data(self) -> None:
        """Load credibility data from storage if it exists."""
        try:
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.sources = data.get('sources', {})
                    logger.info(f"Loaded {len(self.sources)} source credibility records")
            else:
                logger.info("No existing credibility data found, starting with empty dataset")
        except Exception as e:
            logger.error(f"Error loading credibility data: {e}")
            # Continue with empty data if loading fails
    
    def _save_data(self) -> None:
        """Save credibility data to storage."""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
            
            # Save data with timestamp
            data = {
                'sources': self.sources,
                'last_updated': datetime.datetime.now().isoformat()
            }
            
            with open(self.storage_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Saved {len(self.sources)} source credibility records")
        except Exception as e:
            logger.error(f"Error saving credibility data: {e}")
    
    def get_score(self, source: Optional[str]) -> float:
        """
        Get the credibility score for a source.
        
        Args:
            source (str, optional): The name of the news source.
            
        Returns:
            float: Credibility score between 0.0 and 1.0. Returns default_score for unknown sources.
        """
        if not source:
            return self.default_score
            
        # Normalize source name (lowercase)
        source = source.lower()
        
        # Return the score or default if not found
        return self.sources.get(source, self.default_score)
    
    def set_score(self, source: str, score: float) -> None:
        """
        Set the credibility score for a source.
        
        Args:
            source (str): The name of the news source.
            score (float): Credibility score between 0.0 and 1.0.
            
        Raises:
            ValueError: If score is not between 0.0 and 1.0.
        """
        if not 0.0 <= score <= 1.0:
            raise ValueError("Credibility score must be between 0.0 and 1.0")
            
        if not source or not isinstance(source, str):
            raise ValueError("Source name must be a non-empty string")
        
        # Normalize source name (lowercase)
        source = source.lower()
        
        # Update the score
        self.sources[source] = float(score)
        
        # Save the updated data
        self._save_data()
        
        logger.info(f"Updated credibility score for '{source}': {score}")
    
    def adjust_score(self, source: str, adjustment: float) -> float:
        """
        Adjust the credibility score for a source by a relative amount.
        
        Args:
            source (str): The name of the news source.
            adjustment (float): Amount to adjust the score by (positive or negative).
            
        Returns:
            float: The new credibility score after adjustment.
        """
        if not source or not isinstance(source, str):
            raise ValueError("Source name must be a non-empty string")
            
        # Normalize source name
        source = source.lower()
        
        # Get current score or default
        current_score = self.get_score(source)
        
        # Calculate new score, ensuring it stays within bounds
        new_score = max(0.0, min(1.0, current_score + adjustment))
        
        # Update the score
        self.set_score(source, new_score)
        
        return new_score
    
    def get_top_sources(self, limit: int = 10) -> List[Tuple[str, float]]:
        """
        Get the top most credible sources.
        
        Args:
            limit (int): Maximum number of sources to return.
            
        Returns:
            List[Tuple[str, float]]: List of (source, score) tuples, sorted by score in descending order.
        """
        return sorted(self.sources.items(), key=lambda x: x[1], reverse=True)[:limit]
    
    def get_bottom_sources(self, limit: int = 10) -> List[Tuple[str, float]]:
        """
        Get the least credible sources.
        
        Args:
            limit (int): Maximum number of sources to return.
            
        Returns:
            List[Tuple[str, float]]: List of (source, score) tuples, sorted by score in ascending order.
        """
        return sorted(self.sources.items(), key=lambda x: x[1])[:limit]
    
    def apply_credibility_weight(self, sentiment_score: float, source: Optional[str]) -> float:
        """
        Apply source credibility weighting to a sentiment score.
        
        Args:
            sentiment_score (float): The original sentiment score (typically between -1.0 and 1.0).
            source (str, optional): The name of the news source.
            
        Returns:
            float: Weighted sentiment score. Less credible sources have their sentiment impact reduced.
        """
        credibility = self.get_score(source)
        
        # Apply weighting: full weight for credibility=1.0, zero weight for credibility=0.0
        return sentiment_score * credibility

if __name__ == "__main__":
    # Example usage
    credibility_manager = SourceCredibility(initial_sources={
        "Reuters": 0.9,
        "Bloomberg": 0.85,
        "Financial Times": 0.8,
        "Wall Street Journal": 0.8,
        "CNBC": 0.7,
        "Twitter": 0.4,
        "Anonymous Blog": 0.2
    })
    
    # Test getting scores
    print("\nCredibility Scores:")
    for source in ["Reuters", "Twitter", "Unknown Source"]:
        score = credibility_manager.get_score(source)
        print(f"{source}: {score:.2f}")
    
    # Test applying weights to sentiment
    print("\nWeighted Sentiment Scores:")
    sentiment = 0.8  # Strong positive sentiment
    for source in ["Reuters", "Twitter", "Anonymous Blog"]:
        weighted = credibility_manager.apply_credibility_weight(sentiment, source)
        print(f"{source} (cred: {credibility_manager.get_score(source):.2f}): {sentiment:.2f} -> {weighted:.2f}")
    
    # Test getting top and bottom sources
    print("\nTop 3 Most Credible Sources:")
    for source, score in credibility_manager.get_top_sources(3):
        print(f"{source}: {score:.2f}")
        
    print("\nBottom 3 Least Credible Sources:")
    for source, score in credibility_manager.get_bottom_sources(3):
        print(f"{source}: {score:.2f}") 