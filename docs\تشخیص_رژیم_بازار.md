# مستند جامع: MarketRegimeDetector

## مسئولیت
تشخیص خودکار رژیم بازار (صعودی، نزولی، خنثی، پرنوسان) بر اساس داده‌های قیمتی و اندیکاتورها.

## پارامترها
- window: پنجره زمانی تحلیل
- threshold: آستانه تشخیص

## متدهای کلیدی
- detect_regime: تشخیص رژیم بازار
- get_regime_features: استخراج ویژگی‌های رژیم

## نمونه کد
```python
from utils.market_regime_detector import MarketRegimeDetector
detector = MarketRegimeDetector(window=20)
regime = detector.detect_regime(df['close'])
```

## مدیریت خطا
در صورت نبود داده کافی، خروجی خنثی یا پیش‌فرض برمی‌گرداند.

## بهترین شیوه
- داده‌های کافی و تمیز برای تحلیل استفاده کنید.
- خروجی را با سایر ماژول‌ها (ریسک، سیگنال) ترکیب کنید.

## نمودار
- نمودار تغییرات رژیم بازار و همبستگی با بازده قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول در سیستم معاملاتی یکپارچه و برخی تست‌ها استفاده شده است.

## وضعیت عملیاتی
✅ عملیاتی و در جریان اصلی پروژه فعال است. 