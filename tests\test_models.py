import pytest
from utils.rl_training_utils import AdaptiveTradeExecutor

def test_adaptive_trade_executor_learns_slippage():
    # Simulate order book and execution history
    order_book_history = [
        {'EURUSD': {'bid': 1.1000, 'ask': 1.1002}},
        {'EURUSD': {'bid': 1.1001, 'ask': 1.1003}},
        {'EURUSD': {'bid': 1.1002, 'ask': 1.1004}},
    ]
    execution_history = [
        {'side': 'buy', 'amount': 1000, 'price': 1.1002},
        {'side': 'sell', 'amount': 1000, 'price': 1.1001},
        {'side': 'buy', 'amount': 1000, 'price': 1.1004},
    ]
    agent = AdaptiveTradeExecutor(learning_rate=0.5, exploration=0.0)
    agent.fit(order_book_history, execution_history, 'EURUSD')
    # After fit, Q-table should have learned values
    assert len(agent.q_table) > 0
    for k, v in agent.q_table.items():
        assert isinstance(v, float)


def test_adaptive_trade_executor_action_selection():
    order_book = {'EURUSD': {'bid': 1.1000, 'ask': 1.1002}}
    agent = AdaptiveTradeExecutor(learning_rate=0.5, exploration=0.0)
    # Manually set Q-table to prefer 'buy'
    state = agent._state_from_order_book(order_book, 'EURUSD')
    agent.q_table[('EURUSD', state, 'buy')] = 1.0
    agent.q_table[('EURUSD', state, 'sell')] = 0.0
    action = agent.select_action(order_book, 'EURUSD')
    assert action == 'buy'


def test_adaptive_trade_executor_execute_and_online_update():
    order_book = {'EURUSD': {'bid': 1.1000, 'ask': 1.1002}}
    agent = AdaptiveTradeExecutor(learning_rate=0.5, exploration=0.0)
    # Q-table empty, should default to 'buy' (equal Q)
    exec1 = agent.execute(order_book, 'EURUSD', 1000)
    assert exec1['symbol'] == 'EURUSD'
    assert exec1['amount'] == 1000
    # After execution, Q-table should be updated
    state = agent._state_from_order_book(order_book, 'EURUSD')
    key = ('EURUSD', state, exec1['side'])
    assert key in agent.q_table
    assert isinstance(agent.q_table[key], float)


def test_deep_rl_execution_model_learns_order_type():
    import torch
    from utils.rl_training_utils import AdaptiveTradeExecutor
    order_book_history = [
        {'EURUSD': {'bid': 1.1000, 'ask': 1.1002}},
        {'EURUSD': {'bid': 1.1001, 'ask': 1.1003}},
        {'EURUSD': {'bid': 1.1002, 'ask': 1.1004}},
    ]
    execution_history = [
        {'side': 'buy', 'amount': 1000, 'price': 1.1002, 'type': 'market'},
        {'side': 'buy', 'amount': 1000, 'price': 1.1003, 'type': 'limit'},
        {'side': 'buy', 'amount': 1000, 'price': 1.1004, 'type': 'iceberg'},
    ]
    agent = AdaptiveTradeExecutor(deep_rl=True)
    agent.fit_deep_rl(order_book_history, execution_history, 'EURUSD')
    # Should predict a valid order type
    pred_type = agent.predict_order_type(order_book_history[0], 'EURUSD')
    assert pred_type in agent.order_types


def test_order_book_depth_and_feature_extraction():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    ob = {'EURUSD': {'bid': 1.1000, 'ask': 1.1002}}
    agent = AdaptiveTradeExecutor()
    features = agent._extract_features(ob, 'EURUSD')
    assert len(features) == 4
    assert features[0] == pytest.approx(1.1000)
    assert features[1] == pytest.approx(1.1002)


def test_high_frequency_execution():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    # Simulate high-frequency order book ticks
    ob_ticks = [
        {'EURUSD': {'bid': 1.1000 + 0.0001*i, 'ask': 1.1002 + 0.0001*i}} for i in range(10)
    ]
    agent = AdaptiveTradeExecutor()
    for ob in ob_ticks:
        exec_result = agent.execute(ob, 'EURUSD', 100)
        assert exec_result['symbol'] == 'EURUSD'
        assert exec_result['amount'] == 100


def test_advanced_order_types_execution():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    ob = {'EURUSD': {'bid': 1.1000, 'ask': 1.1002}}
    agent = AdaptiveTradeExecutor()
    for order_type in ['market', 'limit', 'iceberg']:
        exec_result = agent.execute(ob, 'EURUSD', 1000, order_type=order_type)
        assert exec_result['type'] == order_type
        assert exec_result['price'] > 0


def test_market_impact_simulation():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent = AdaptiveTradeExecutor()
    impact_market = agent._simulate_market_impact(1000, 'market')
    impact_limit = agent._simulate_market_impact(1000, 'limit')
    impact_iceberg = agent._simulate_market_impact(1000, 'iceberg')
    assert impact_market > impact_iceberg > impact_limit >= 0


def test_multi_agent_learning():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agents = [AdaptiveTradeExecutor() for _ in range(2)]
    ob_histories = [
        [{'EURUSD': {'bid': 1.1, 'ask': 1.1002}} for _ in range(3)],
        [{'EURUSD': {'bid': 1.2, 'ask': 1.2002}} for _ in range(3)]
    ]
    ex_histories = [
        [{'side': 'buy', 'amount': 1000, 'price': 1.1002} for _ in range(3)],
        [{'side': 'sell', 'amount': 1000, 'price': 1.2002} for _ in range(3)]
    ]
    agents[0].fit_multi_agent(agents, ob_histories, ex_histories, 'EURUSD')
    assert len(agents[0].q_table) > 0
    assert len(agents[1].q_table) > 0


def test_liquidity_shock_detection():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent = AdaptiveTradeExecutor()
    ob1 = {'EURUSD': {'bid': 1.1, 'ask': 1.1002}}
    ob2 = {'EURUSD': {'bid': 1.1, 'ask': 1.1010}}
    agent.detect_liquidity_shock(ob1, 'EURUSD')
    shock = agent.detect_liquidity_shock(ob2, 'EURUSD')
    assert shock is True


def test_adapt_to_abnormal_conditions():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent = AdaptiveTradeExecutor(exploration=0.1)
    ob = {'EURUSD': {'bid': 1.1, 'ask': 1.102}}
    agent.adapt_to_abnormal_conditions(ob, 'EURUSD', abnormal_threshold=0.001)
    assert agent.exploration > 0.1
    ob2 = {'EURUSD': {'bid': 1.1, 'ask': 1.1005}}
    agent.adapt_to_abnormal_conditions(ob2, 'EURUSD', abnormal_threshold=0.01)
    assert agent.exploration <= 1.0


def test_order_book_snapshot():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent = AdaptiveTradeExecutor()
    ob = {'EURUSD': {'bid': 1.1, 'ask': 1.1002}}
    snap = agent.get_order_book_snapshot(ob, 'EURUSD')
    assert snap['symbol'] == 'EURUSD'
    assert 'bid' in snap and 'ask' in snap and 'spread' in snap


def test_execute_dual_mode():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent = AdaptiveTradeExecutor()
    ob = {'EURUSD': {'bid': 1.1, 'ask': 1.1002}}
    exec1 = agent.execute_dual_mode(ob, 'EURUSD', 100, mode='backtest')
    assert exec1['symbol'] == 'EURUSD'
    exec2 = agent.execute_dual_mode(ob, 'EURUSD', 100, mode='live')
    assert exec2['live'] is True


def test_real_slippage_modeling():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent = AdaptiveTradeExecutor()
    slippage = agent.compute_real_slippage(1.1005, 1.1002)
    assert slippage == pytest.approx(0.0003)


def test_parameter_sensitivity():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent = AdaptiveTradeExecutor()
    ob = {'EURUSD': {'bid': 1.1, 'ask': 1.1002}}
    grid = {'type': ['market', 'limit'], 'amount': [100, 200]}
    results = agent.parameter_sensitivity(ob, 'EURUSD', grid)
    assert len(results) == 4
    for r in results:
        assert 'result' in r


def test_explain_execution():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent = AdaptiveTradeExecutor()
    ob = {'EURUSD': {'bid': 1.1, 'ask': 1.1002}}
    explanation = agent.explain_execution(ob, 'EURUSD')
    assert explanation['policy'] in ['deep_rl', 'q_learning']
    assert 'spread' in explanation


def test_transfer_learning_q_table():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent1 = AdaptiveTradeExecutor()
    agent2 = AdaptiveTradeExecutor()
    agent1.q_table[('EURUSD', 0.0002, 'buy')] = 1.0
    agent2.transfer_q_table(agent1, 'EURUSD')
    assert ('EURUSD', 0.0002, 'buy') in agent2.q_table
    assert agent2.q_table[('EURUSD', 0.0002, 'buy')] == 1.0


def test_block_trade_splitting():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent = AdaptiveTradeExecutor()
    chunks = agent.split_block_trade(2500, max_chunk=1000)
    assert sum(chunks) == 2500
    assert all(c <= 1000 for c in chunks)


def test_online_update_q_table():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent = AdaptiveTradeExecutor()
    ob = {'EURUSD': {'bid': 1.1, 'ask': 1.1002}}
    exec_result = {'side': 'buy', 'amount': 100, 'price': 1.1003}
    agent.online_update(ob, 'EURUSD', exec_result)
    state = agent._state_from_order_book(ob, 'EURUSD')
    key = ('EURUSD', state, 'buy')
    assert key in agent.q_table


def test_optimize_commission():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent = AdaptiveTradeExecutor()
    ob = {'EURUSD': {'bid': 1.1, 'ask': 1.1002}}
    result = agent.optimize_commission(ob, 'EURUSD', 100)
    assert 'best_type' in result and 'best_cost' in result


def test_safe_execute_live_handles_error():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    agent = AdaptiveTradeExecutor()
    # Pass an invalid mode to force error
    ob = {'EURUSD': {'bid': 1.1, 'ask': 1.1002}}
    result = agent.safe_execute_live(ob, 'EURUSD', 100, mode='invalid')
    assert 'error' in result


def test_sentiment_analyzer_basic():
    from utils.rl_training_utils import SentimentAnalyzer
    analyzer = SentimentAnalyzer()
    assert analyzer.score_text('Strong BUY signal!') > 0
    assert analyzer.score_text('Market crash, big loss') < 0
    batch = ['buy now', 'sell everything', 'profit up', 'bearish trend']
    scores = analyzer.analyze_batch(batch)
    assert len(scores) == 4
    agg = analyzer.aggregate_score(scores)
    assert isinstance(agg, float)


def test_sentiment_aware_execute():
    from utils.rl_training_utils import AdaptiveTradeExecutor
    ob = {'EURUSD': {'bid': 1.1, 'ask': 1.1002}}
    agent = AdaptiveTradeExecutor()
    # Strong positive sentiment
    exec1 = agent.sentiment_aware_execute(ob, 'EURUSD', 1000, sentiment_score=1.0)
    assert exec1['side'] == 'buy'
    # Strong negative sentiment
    exec2 = agent.sentiment_aware_execute(ob, 'EURUSD', 1000, sentiment_score=-1.0)
    assert exec2['side'] == 'sell'
    # Neutral sentiment (should fallback to policy)
    exec3 = agent.sentiment_aware_execute(ob, 'EURUSD', 1000, sentiment_score=0.0)
    assert exec3['side'] in ['buy', 'sell']


def test_meta_learner_selection():
    from utils.rl_training_utils import MetaLearner
    factories = {'A': lambda: 'modelA', 'B': lambda: 'modelB'}
    meta = MetaLearner(factories)
    meta.record_performance('A', 1.0)
    meta.record_performance('B', 0.5)
    assert meta.select_best() == 'A'
    assert meta.get_model() == 'modelA'


def test_auto_ensemble_voting():
    from utils.rl_training_utils import AutoEnsemble
    class DummyModel:
        def __init__(self, action): self.action = action
        def predict(self, obs): return self.action
    models = [DummyModel('buy'), DummyModel('buy'), DummyModel('sell')]
    ensemble = AutoEnsemble(models, voting='hard')
    assert ensemble.predict(None) == 'buy'


def test_continual_learner_memory_and_rehearsal():
    from utils.rl_training_utils import ContinualLearner
    class DummyModel:
        def __init__(self): self.seen = []
        def learn_from_experience(self, obs, action, reward, next_obs):
            self.seen.append((obs, action, reward, next_obs))
    model = DummyModel()
    learner = ContinualLearner(model, memory_size=3)
    learner.store_experience(1, 'a', 1, 2)
    learner.store_experience(2, 'b', 2, 3)
    learner.store_experience(3, 'c', 3, 4)
    learner.store_experience(4, 'd', 4, 5)  # oldest should be dropped
    assert len(learner.memory) == 3
    learner.rehearse(n_samples=2)
    assert len(model.seen) == 2


def test_advanced_sentiment_analyzer():
    from utils.rl_training_utils import AdvancedSentimentAnalyzer
    analyzer = AdvancedSentimentAnalyzer()
    texts = ['Strong BUY signal!', 'Market crash, big loss', 'profit up', 'bearish trend']
    sources = ['news1', 'news2', 'news1', 'news3']
    source_weights = {'news1': 2.0, 'news2': 1.0, 'news3': 0.5}
    scores = analyzer.analyze_batch(texts)
    agg = analyzer.aggregate_score(scores)
    weighted = analyzer.weighted_source_score(texts, sources, source_weights)
    assert isinstance(agg, float)
    assert isinstance(weighted, float)
    timestamps = [0, 10, 20, 70, 80, 130]
    assert analyzer.news_volume_shock(timestamps, window=60) == 3


def test_meta_learner_transfer_and_explain():
    from utils.rl_training_utils import MetaLearner
    factories = {'A': lambda: 'modelA', 'B': lambda: 'modelB'}
    meta = MetaLearner(factories)
    meta.record_performance('A', 1.0, explanation='A strong in bull')
    meta.record_performance('B', 0.5, explanation='B weak in bear')
    assert meta.select_best() == 'A'
    assert meta.get_model() == 'modelA'
    assert 'A strong' in meta.explain_selection()[0]
    # Transfer knowledge stub (no-op)
    class Dummy:
        def get_weights(self): return [1,2,3]
        def set_weights(self, w): self.w = w
    m1, m2 = Dummy(), Dummy()
    meta.transfer_knowledge(m1, m2)
    assert hasattr(m2, 'w')


def test_auto_ensemble_weighted():
    from utils.rl_training_utils import AutoEnsemble
    class DummyModel:
        def __init__(self, action): self.action = action
        def predict(self, obs): return self.action
    models = [DummyModel('buy'), DummyModel('buy'), DummyModel('sell')]
    ensemble = AutoEnsemble(models, voting='weighted', weights=[2,1,1])
    assert ensemble.predict(None) == 'buy'
    ensemble.update_weights([0,0,1])
    assert ensemble.predict(None) == 'sell'


def test_continual_learner_regularization():
    from utils.rl_training_utils import ContinualLearner
    class DummyModel:
        def __init__(self): self._w = [1,2,3]; self.seen=[]
        def get_weights(self): return self._w
        def set_weights(self, w): self._w = w
        def learn_from_experience(self, obs, action, reward, next_obs): self.seen.append(obs)
    model = DummyModel()
    learner = ContinualLearner(model, memory_size=3, regularization_strength=0.5)
    learner.store_experience(1, 'a', 1, 2)
    learner.update_prev_weights()
    model.set_weights([2,2,3])
    penalty = learner.regularize()
    assert penalty > 0
    learner.rehearse(n_samples=1)
    assert len(model.seen) == 1


def test_auto_market_maker():
    from utils.rl_training_utils import AutoMarketMaker
    ob = {'EURUSD': {'bid': 1.1000, 'ask': 1.1002}}
    mm = AutoMarketMaker(spread_range=(0.0001, 0.001), inventory_limit=100)
    quote = mm.quote(ob, 'EURUSD')
    assert 'bid' in quote and 'ask' in quote
    mm.update_inventory('buy', 50)
    mm.update_inventory('sell', 30)
    assert mm.inventory == 20
    obs = [ob]*3
    acts = [{'side':'buy','amount':10},{'side':'sell','amount':5},{'side':'buy','amount':15}]
    results = mm.simulate_market_making(obs, 'EURUSD', acts)
    assert isinstance(results, list) and 'quote' in results[0]
