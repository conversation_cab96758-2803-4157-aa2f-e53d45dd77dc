import pytest
import os
import tempfile
import json
from utils.source_credibility import SourceCredibility

@pytest.fixture
def temp_storage_path():
    """Create a temporary file for credibility storage."""
    fd, path = tempfile.mkstemp()
    os.close(fd)
    yield path
    if os.path.exists(path):
        os.unlink(path)

@pytest.fixture
def credibility_manager(temp_storage_path):
    """Create a SourceCredibility instance with test data."""
    return SourceCredibility(
        initial_sources={
            "Reuters": 0.9,
            "Bloomberg": 0.85,
            "Twitter": 0.4,
            "Anonymous Blog": 0.2
        },
        storage_path=temp_storage_path
    )

def test_initialization():
    """Test basic initialization."""
    manager = SourceCredibility()
    assert manager.default_score == 0.5
    assert isinstance(manager.sources, dict)

def test_initialization_with_sources(credibility_manager):
    """Test initialization with initial sources."""
    assert credibility_manager.get_score("Reuters") == 0.9
    assert credibility_manager.get_score("Bloomberg") == 0.85
    assert credibility_manager.get_score("Twitter") == 0.4
    assert credibility_manager.get_score("Anonymous Blog") == 0.2

def test_get_score(credibility_manager):
    """Test getting scores for various sources."""
    # Known sources
    assert credibility_manager.get_score("Reuters") == 0.9
    assert credibility_manager.get_score("twitter") == 0.4  # Case insensitive
    
    # Unknown source should return default score
    assert credibility_manager.get_score("Unknown Source") == 0.5
    
    # None or empty source should return default score
    assert credibility_manager.get_score(None) == 0.5
    assert credibility_manager.get_score("") == 0.5

def test_set_score(credibility_manager):
    """Test setting scores for sources."""
    # Set new source
    credibility_manager.set_score("New Source", 0.75)
    assert credibility_manager.get_score("New Source") == 0.75
    
    # Update existing source
    credibility_manager.set_score("Reuters", 0.95)
    assert credibility_manager.get_score("Reuters") == 0.95
    
    # Test case insensitivity
    credibility_manager.set_score("TWITTER", 0.45)
    assert credibility_manager.get_score("twitter") == 0.45
    
    # Test invalid scores
    with pytest.raises(ValueError):
        credibility_manager.set_score("Invalid", -0.1)
    
    with pytest.raises(ValueError):
        credibility_manager.set_score("Invalid", 1.1)
    
    # Test invalid source names
    with pytest.raises(ValueError):
        credibility_manager.set_score("", 0.5)
    
    with pytest.raises(ValueError):
        credibility_manager.set_score(None, 0.5)

def test_adjust_score(credibility_manager):
    """Test adjusting scores for sources."""
    # Adjust existing source
    new_score = credibility_manager.adjust_score("Reuters", 0.05)
    assert abs(new_score - 0.95) < 0.0001  # Use approximate comparison for floating point
    assert abs(credibility_manager.get_score("Reuters") - 0.95) < 0.0001
    
    # Adjust unknown source (should start from default)
    new_score = credibility_manager.adjust_score("New Source", 0.1)
    assert abs(new_score - 0.6) < 0.0001
    assert abs(credibility_manager.get_score("New Source") - 0.6) < 0.0001
    
    # Test bounds (should not go below 0)
    new_score = credibility_manager.adjust_score("Twitter", -0.5)
    assert new_score == 0.0
    assert credibility_manager.get_score("Twitter") == 0.0
    
    # Test bounds (should not go above 1)
    new_score = credibility_manager.adjust_score("Bloomberg", 0.2)
    assert new_score == 1.0
    assert credibility_manager.get_score("Bloomberg") == 1.0

def test_get_top_sources(credibility_manager):
    """Test getting top credible sources."""
    top_sources = credibility_manager.get_top_sources(2)
    assert len(top_sources) == 2
    assert top_sources[0][0].lower() == "reuters"
    assert top_sources[0][1] == 0.9
    assert top_sources[1][0].lower() == "bloomberg"
    assert top_sources[1][1] == 0.85

def test_get_bottom_sources(credibility_manager):
    """Test getting least credible sources."""
    bottom_sources = credibility_manager.get_bottom_sources(2)
    assert len(bottom_sources) == 2
    assert bottom_sources[0][0].lower() == "anonymous blog"
    assert bottom_sources[0][1] == 0.2
    assert bottom_sources[1][0].lower() == "twitter"
    assert bottom_sources[1][1] == 0.4

def test_apply_credibility_weight(credibility_manager):
    """Test applying credibility weights to sentiment scores."""
    # Positive sentiment with high credibility source
    weighted = credibility_manager.apply_credibility_weight(0.8, "Reuters")
    assert abs(weighted - (0.8 * 0.9)) < 0.0001  # Use approximate comparison
    
    # Negative sentiment with low credibility source
    weighted = credibility_manager.apply_credibility_weight(-0.7, "Anonymous Blog")
    assert abs(weighted - (-0.7 * 0.2)) < 0.0001
    
    # Unknown source should use default credibility
    weighted = credibility_manager.apply_credibility_weight(0.6, "Unknown Source")
    assert abs(weighted - (0.6 * 0.5)) < 0.0001

def test_persistence(temp_storage_path):
    """Test that credibility scores are persisted and can be reloaded."""
    # Create and populate a manager
    manager1 = SourceCredibility(
        initial_sources={"Test Source": 0.75},
        storage_path=temp_storage_path
    )
    
    # Verify data was saved
    assert os.path.exists(temp_storage_path)
    
    # Create a new manager that should load the saved data
    manager2 = SourceCredibility(storage_path=temp_storage_path)
    
    # Verify the data was loaded
    assert manager2.get_score("Test Source") == 0.75
    
    # Check the file contents directly
    with open(temp_storage_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
        assert "sources" in data
        assert data["sources"].get("test source") == 0.75 