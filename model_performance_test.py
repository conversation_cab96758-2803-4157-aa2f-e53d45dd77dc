#!/usr/bin/env python3
"""
تست عملکرد کامل مدل‌های آموزش دیده
شامل تمام مدل‌های PPO، A2C، Sentiment، Time Series، Deep Learning و سایرین
"""

import os
import sys
import json
import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

# اضافه کردن مسیر پروژه
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# تنظیمات پروکسی
try:
    with open('PROXY.json', 'r') as f:
        proxy_config = json.load(f)
    os.environ['HTTP_PROXY'] = proxy_config.get('http_proxy', '')
    os.environ['HTTPS_PROXY'] = proxy_config.get('https_proxy', '')
    os.environ['SOCKS_PROXY'] = proxy_config.get('socks_proxy', '')
except:
    pass

# تنظیمات لاگینگ
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('model_performance_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ModelPerformanceTester:
    """تست عملکرد کامل تمام مدل‌های آموزش دیده"""
    
    def __init__(self):
        self.results = {}
        self.test_data = None
        self.models = {}
        
    def load_test_data(self):
        """بارگذاری داده‌های تست"""
        logger.info("بارگذاری داده‌های تست...")
        
        # بارگذاری داده‌های EURUSD برای تست
        data_path = "data/EURUSD/H1.csv"
        if os.path.exists(data_path):
            self.test_data = pd.read_csv(data_path)
            self.test_data['datetime'] = pd.to_datetime(self.test_data['datetime'])
            self.test_data = self.test_data.sort_values('datetime').reset_index(drop=True)
            
            # آماده‌سازی ویژگی‌ها
            self.test_data['returns'] = self.test_data['close'].pct_change()
            self.test_data['volatility'] = self.test_data['returns'].rolling(20).std()
            self.test_data['sma_20'] = self.test_data['close'].rolling(20).mean()
            self.test_data['rsi'] = self.calculate_rsi(self.test_data['close'])
            
            logger.info(f"داده‌های تست بارگذاری شد: {len(self.test_data)} رکورد")
        else:
            logger.error(f"فایل داده یافت نشد: {data_path}")
            return False
        return True
    
    def calculate_rsi(self, prices, period=14):
        """محاسبه RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def load_models(self):
        """بارگذاری تمام مدل‌های آموزش دیده"""
        logger.info("بارگذاری مدل‌های آموزش دیده...")
        
        try:
            # بررسی فایل‌های مدل موجود
            model_files = os.listdir("best_models")
            logger.info(f"فایل‌های مدل موجود: {model_files}")
            
            # بارگذاری مدل‌های PPO موجود
            ppo_models = [f for f in model_files if 'ppo' in f.lower()]
            for ppo_file in ppo_models:
                model_name = ppo_file.replace('.zip', '').replace('_', ' ')
                self.models[f'ppo_{ppo_file}'] = {
                    'file': ppo_file,
                    'type': 'PPO',
                    'size': os.path.getsize(f"best_models/{ppo_file}"),
                    'loaded': True
                }
                logger.info(f"✅ مدل PPO بارگذاری شد: {ppo_file}")
            
            # تست مدل‌های Sentiment آفلاین
            try:
                from utils.offline_sentiment_analyzer import SentimentAnalyzer
                self.models['sentiment'] = SentimentAnalyzer()
                logger.info("✅ مدل Sentiment آفلاین بارگذاری شد")
            except Exception as e:
                logger.warning(f"❌ خطا در بارگذاری Sentiment: {e}")
            
            # تست مدل‌های Time Series
            try:
                from utils.backtesting_framework import SentimentBacktestingFramework
                self.models['backtesting'] = SentimentBacktestingFramework()
                logger.info("✅ فریم‌ورک Backtesting بارگذاری شد")
            except Exception as e:
                logger.warning(f"❌ خطا در بارگذاری Backtesting: {e}")
            
        except Exception as e:
            logger.error(f"خطا در بارگذاری مدل‌ها: {e}")
        
        logger.info(f"تعداد مدل‌های بارگذاری شده: {len(self.models)}")
        return len(self.models) > 0
    
    def test_rl_models(self):
        """تست مدل‌های Reinforcement Learning"""
        logger.info("تست مدل‌های Reinforcement Learning...")
        
        rl_results = {}
        
        for model_name in ['ppo', 'a2c', 'dqn']:
            if model_name in self.models:
                try:
                    model = self.models[model_name]
                    
                    # تست پیش‌بینی
                    test_state = np.random.randn(10, 20)  # حالت تست
                    action = model.predict(test_state)
                    
                    # تست عملکرد
                    performance = {
                        'model_loaded': True,
                        'prediction_success': True,
                        'action_shape': action.shape if hasattr(action, 'shape') else len(action),
                        'test_timestamp': datetime.now().isoformat()
                    }
                    
                    rl_results[model_name] = performance
                    logger.info(f"✅ {model_name.upper()} - تست موفق")
                    
                except Exception as e:
                    logger.error(f"❌ خطا در تست {model_name}: {e}")
                    rl_results[model_name] = {
                        'model_loaded': True,
                        'prediction_success': False,
                        'error': str(e)
                    }
        
        return rl_results
    
    def test_sentiment_model(self):
        """تست مدل Sentiment Analysis"""
        logger.info("تست مدل Sentiment Analysis...")
        
        if 'sentiment' not in self.models:
            return {'error': 'مدل Sentiment بارگذاری نشده'}
        
        try:
            model = self.models['sentiment']
            
            # تست متون مختلف
            test_texts = [
                "شرکت اپل سود بالایی گزارش داد و سهام آن رشد کرد",
                "بازار سهام امروز افت کرد و سرمایه‌گذاران نگران هستند",
                "Apple reported strong profits and stock price increased",
                "Stock market fell today and investors are worried"
            ]
            
            results = []
            for text in test_texts:
                try:
                    sentiment = model.predict(text)
                    results.append({
                        'text': text[:50] + "...",
                        'sentiment': sentiment,
                        'success': True
                    })
                except Exception as e:
                    results.append({
                        'text': text[:50] + "...",
                        'error': str(e),
                        'success': False
                    })
            
            return {
                'model_loaded': True,
                'test_results': results,
                'success_rate': sum(1 for r in results if r['success']) / len(results)
            }
            
        except Exception as e:
            logger.error(f"❌ خطا در تست Sentiment: {e}")
            return {'error': str(e)}
    
    def test_timeseries_model(self):
        """تست مدل Time Series"""
        logger.info("تست مدل Time Series...")
        
        if 'timeseries' not in self.models:
            return {'error': 'مدل Time Series بارگذاری نشده'}
        
        try:
            model = self.models['timeseries']
            
            # آماده‌سازی داده‌های تست
            if self.test_data is None:
                return {'error': 'داده‌های تست در دسترس نیست'}
            
            # انتخاب بخشی از داده‌ها برای تست
            test_sample = self.test_data.tail(100)[['close', 'volume']].values
            
            # تست پیش‌بینی
            prediction = model.predict(test_sample)
            
            return {
                'model_loaded': True,
                'prediction_success': True,
                'input_shape': test_sample.shape,
                'prediction_shape': prediction.shape if hasattr(prediction, 'shape') else len(prediction),
                'sample_prediction': prediction[:5].tolist() if hasattr(prediction, 'tolist') else prediction[:5]
            }
            
        except Exception as e:
            logger.error(f"❌ خطا در تست Time Series: {e}")
            return {'error': str(e)}
    
    def test_deeplearning_model(self):
        """تست مدل Deep Learning"""
        logger.info("تست مدل Deep Learning...")
        
        if 'deeplearning' not in self.models:
            return {'error': 'مدل Deep Learning بارگذاری نشده'}
        
        try:
            model = self.models['deeplearning']
            
            # تست با داده‌های مصنوعی
            test_input = np.random.randn(32, 10, 20)  # batch_size, sequence_length, features
            
            # تست پیش‌بینی
            prediction = model.predict(test_input)
            
            return {
                'model_loaded': True,
                'prediction_success': True,
                'input_shape': test_input.shape,
                'prediction_shape': prediction.shape if hasattr(prediction, 'shape') else len(prediction),
                'model_type': type(model).__name__
            }
            
        except Exception as e:
            logger.error(f"❌ خطا در تست Deep Learning: {e}")
            return {'error': str(e)}
    
    def run_comprehensive_test(self):
        """اجرای تست جامع تمام مدل‌ها"""
        logger.info("شروع تست جامع عملکرد مدل‌ها...")
        
        # بارگذاری داده‌ها
        if not self.load_test_data():
            logger.error("خطا در بارگذاری داده‌های تست")
            return False
        
        # بارگذاری مدل‌ها
        if not self.load_models():
            logger.error("خطا در بارگذاری مدل‌ها")
            return False
        
        # تست مدل‌های مختلف
        self.results = {
            'test_timestamp': datetime.now().isoformat(),
            'rl_models': self.test_rl_models(),
            'sentiment_model': self.test_sentiment_model(),
            'timeseries_model': self.test_timeseries_model(),
            'deeplearning_model': self.test_deeplearning_model()
        }
        
        # محاسبه آمار کلی
        total_models = len(self.models)
        successful_tests = 0
        
        for category, result in self.results.items():
            if category != 'test_timestamp':
                if isinstance(result, dict) and result.get('error') is None:
                    successful_tests += 1
        
        self.results['summary'] = {
            'total_models': total_models,
            'successful_tests': successful_tests,
            'success_rate': successful_tests / max(1, len(self.results) - 2)  # -2 for timestamp and summary
        }
        
        return True
    
    def generate_report(self):
        """تولید گزارش تست"""
        if not self.results:
            return "❌ هیچ نتیجه‌ای برای گزارش وجود ندارد"
        
        report = []
        report.append("=" * 60)
        report.append("📊 گزارش تست عملکرد مدل‌های آموزش دیده")
        report.append("=" * 60)
        report.append(f"🕐 زمان تست: {self.results['test_timestamp']}")
        report.append("")
        
        # خلاصه کلی
        summary = self.results.get('summary', {})
        report.append("📈 خلاصه کلی:")
        report.append(f"  • تعداد کل مدل‌ها: {summary.get('total_models', 0)}")
        report.append(f"  • تست‌های موفق: {summary.get('successful_tests', 0)}")
        report.append(f"  • نرخ موفقیت: {summary.get('success_rate', 0):.1%}")
        report.append("")
        
        # نتایج مدل‌های RL
        report.append("🤖 مدل‌های Reinforcement Learning:")
        rl_results = self.results.get('rl_models', {})
        for model_name, result in rl_results.items():
            status = "✅" if result.get('prediction_success', False) else "❌"
            report.append(f"  • {model_name.upper()}: {status}")
        report.append("")
        
        # نتایج مدل Sentiment
        report.append("📝 مدل Sentiment Analysis:")
        sentiment_result = self.results.get('sentiment_model', {})
        if 'error' in sentiment_result:
            report.append(f"  • وضعیت: ❌ {sentiment_result['error']}")
        else:
            success_rate = sentiment_result.get('success_rate', 0)
            report.append(f"  • وضعیت: ✅ موفق")
            report.append(f"  • نرخ موفقیت: {success_rate:.1%}")
        report.append("")
        
        # نتایج مدل Time Series
        report.append("📈 مدل Time Series:")
        timeseries_result = self.results.get('timeseries_model', {})
        if 'error' in timeseries_result:
            report.append(f"  • وضعیت: ❌ {timeseries_result['error']}")
        else:
            report.append(f"  • وضعیت: ✅ موفق")
            report.append(f"  • شکل ورودی: {timeseries_result.get('input_shape', 'N/A')}")
        report.append("")
        
        # نتایج مدل Deep Learning
        report.append("🧠 مدل Deep Learning:")
        dl_result = self.results.get('deeplearning_model', {})
        if 'error' in dl_result:
            report.append(f"  • وضعیت: ❌ {dl_result['error']}")
        else:
            report.append(f"  • وضعیت: ✅ موفق")
            report.append(f"  • نوع مدل: {dl_result.get('model_type', 'N/A')}")
        report.append("")
        
        report.append("=" * 60)
        
        return "\n".join(report)

def main():
    """تابع اصلی"""
    print("🚀 شروع تست عملکرد مدل‌های آموزش دیده...")
    print("=" * 60)
    
    # ایجاد تستر
    tester = ModelPerformanceTester()
    
    # اجرای تست جامع
    success = tester.run_comprehensive_test()
    
    if success:
        # تولید و نمایش گزارش
        report = tester.generate_report()
        print(report)
        
        # ذخیره نتایج
        with open('model_performance_results.json', 'w', encoding='utf-8') as f:
            json.dump(tester.results, f, indent=2, ensure_ascii=False)
        
        print("\n💾 نتایج در فایل 'model_performance_results.json' ذخیره شد")
        
        # خلاصه نهایی
        summary = tester.results.get('summary', {})
        success_rate = summary.get('success_rate', 0)
        
        if success_rate >= 0.8:
            print(f"\n🎉 عالی! نرخ موفقیت: {success_rate:.1%}")
        elif success_rate >= 0.6:
            print(f"\n✅ خوب! نرخ موفقیت: {success_rate:.1%}")
        else:
            print(f"\n⚠️ نیاز به بهبود! نرخ موفقیت: {success_rate:.1%}")
            
    else:
        print("❌ تست با شکست مواجه شد")
    
    print("=" * 60)

if __name__ == "__main__":
    main() 