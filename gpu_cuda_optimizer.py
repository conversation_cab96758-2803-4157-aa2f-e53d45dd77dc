"""
🔥 GPU/CUDA Optimizer for Ultimate Trading System
بهینه‌ساز GPU برای حداکثر استفاده از قدرت پردازش
"""

import os
import sys
import subprocess

def setup_cuda_environment():
    """تنظیم محیط CUDA برای حداکثر عملکرد"""
    print("🔥 Setting up CUDA environment for maximum performance...")
    
    # Set CUDA environment variables for optimal performance
    cuda_env_vars = {
        'CUDA_VISIBLE_DEVICES': '0',  # Use first GPU
        'CUDA_LAUNCH_BLOCKING': '0',  # Async execution
        'CUDA_CACHE_DISABLE': '0',    # Enable caching
        'CUDA_DEVICE_ORDER': 'PCI_BUS_ID',
        'TF_FORCE_GPU_ALLOW_GROWTH': 'true',
        'TF_GPU_ALLOCATOR': 'cuda_malloc_async',
        'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:512,garbage_collection_threshold:0.6'
    }
    
    for key, value in cuda_env_vars.items():
        os.environ[key] = value
        print(f"   ✅ {key} = {value}")
    
    print("🚀 CUDA environment optimized!")

def check_and_optimize_gpu():
    """بررسی و بهینه‌سازی GPU"""
    try:
        import torch
        
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            print(f"🔥 CUDA Available! Found {device_count} GPU(s)")
            
            for i in range(device_count):
                props = torch.cuda.get_device_properties(i)
                print(f"   GPU {i}: {props.name}")
                print(f"   💾 Memory: {props.total_memory / 1024**3:.1f} GB")
                print(f"   🔧 Compute Capability: {props.major}.{props.minor}")
            
            # Set default device
            torch.cuda.set_device(0)
            
            # Clear cache and optimize
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
            
            # Enable optimizations
            torch.backends.cudnn.enabled = True
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            
            print("🚀 GPU optimizations enabled!")
            return True
        else:
            print("⚠️ CUDA not available - using CPU")
            return False
            
    except ImportError:
        print("⚠️ PyTorch not available")
        return False

def optimize_memory_for_gpu():
    """بهینه‌سازی حافظه برای GPU"""
    try:
        import torch
        import gc
        
        if torch.cuda.is_available():
            # Clear GPU cache
            torch.cuda.empty_cache()
            
            # Force garbage collection
            gc.collect()
            
            # Get memory info
            memory_allocated = torch.cuda.memory_allocated() / 1024**3
            memory_reserved = torch.cuda.memory_reserved() / 1024**3
            
            print(f"💾 GPU Memory - Allocated: {memory_allocated:.2f} GB, Reserved: {memory_reserved:.2f} GB")
            
            # Set memory fraction if needed
            if memory_reserved > 8.0:  # If using more than 8GB
                torch.cuda.set_per_process_memory_fraction(0.8)
                print("🔧 Memory fraction limited to 80%")
            
            return True
    except:
        return False

def setup_tensorflow_gpu():
    """تنظیم TensorFlow برای GPU"""
    try:
        import tensorflow as tf
        
        # Configure GPU memory growth
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print(f"🔥 TensorFlow GPU configured: {len(gpus)} GPU(s)")
            return True
        else:
            print("⚠️ No TensorFlow GPUs found")
            return False
    except ImportError:
        print("⚠️ TensorFlow not available")
        return False

def install_cuda_packages():
    """نصب پکیج‌های CUDA مورد نیاز"""
    cuda_packages = [
        'torch',
        'torchvision', 
        'torchaudio',
        'tensorflow-gpu',
        'cupy-cuda12x',  # For CUDA 12.x
        'numba'
    ]
    
    print("📦 Installing CUDA-optimized packages...")
    
    for package in cuda_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package} already installed")
        except ImportError:
            try:
                print(f"   📦 Installing {package}...")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", package, 
                    "--upgrade", "--no-cache-dir"
                ])
                print(f"   ✅ {package} installed successfully")
            except subprocess.CalledProcessError as e:
                print(f"   ⚠️ Failed to install {package}: {e}")

def apply_gpu_optimizations():
    """اعمال تمام بهینه‌سازی‌های GPU"""
    print("🔥 APPLYING ULTIMATE GPU OPTIMIZATIONS")
    print("=" * 50)
    
    # Step 1: Setup CUDA environment
    setup_cuda_environment()
    
    # Step 2: Install CUDA packages
    install_cuda_packages()
    
    # Step 3: Check and optimize GPU
    gpu_available = check_and_optimize_gpu()
    
    # Step 4: Setup TensorFlow
    tf_gpu = setup_tensorflow_gpu()
    
    # Step 5: Optimize memory
    memory_optimized = optimize_memory_for_gpu()
    
    print("\n🎯 GPU OPTIMIZATION SUMMARY:")
    print(f"   🔥 CUDA Available: {'✅' if gpu_available else '❌'}")
    print(f"   🧠 TensorFlow GPU: {'✅' if tf_gpu else '❌'}")
    print(f"   💾 Memory Optimized: {'✅' if memory_optimized else '❌'}")
    
    if gpu_available:
        print("\n🚀 READY FOR ULTIMATE GPU-ACCELERATED TRAINING!")
    else:
        print("\n⚠️ Running on CPU - consider enabling GPU for better performance")
    
    return gpu_available

if __name__ == "__main__":
    apply_gpu_optimizations()
