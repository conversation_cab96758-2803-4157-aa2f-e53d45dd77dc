"""Tests for HFT Modeling module"""

import unittest
import numpy as np
import time
from unittest.mock import Mock, patch
from utils.hft_modeling import (
    OrderBookLevel, Trade, MarketMicrostructure,
    OrderBookAnalyzer, LatencyOptimizer, ShortTermPredictor,
    HFTStrategy, HFTModelingSystem
)


class TestOrderBookAnalyzer(unittest.TestCase):
    """تست‌های OrderBookAnalyzer"""
    
    def setUp(self):
        self.analyzer = OrderBookAnalyzer(max_levels=5, history_size=100)
        
    def test_initialization(self):
        """تست مقداردهی اولیه"""
        self.assertEqual(self.analyzer.max_levels, 5)
        self.assertEqual(self.analyzer.history_size, 100)
        self.assertEqual(len(self.analyzer.bid_history), 0)
        self.assertEqual(len(self.analyzer.ask_history), 0)
        
    def test_update_order_book(self):
        """تست به‌روزرسانی order book"""
        # تهیه داده‌های تست
        bids = [
            OrderBookLevel(100.0, 10.0, time.time()),
            OrderBookLevel(99.9, 15.0, time.time()),
            OrderBookLevel(99.8, 20.0, time.time())
        ]
        asks = [
            OrderBookLevel(100.1, 12.0, time.time()),
            OrderBookLevel(100.2, 18.0, time.time()),
            OrderBookLevel(100.3, 25.0, time.time())
        ]
        
        self.analyzer.update_order_book(bids, asks)
        
        # بررسی به‌روزرسانی
        self.assertEqual(len(self.analyzer.bid_history), 1)
        self.assertEqual(len(self.analyzer.ask_history), 1)
        self.assertAlmostEqual(self.analyzer.current_spread, 0.1, places=2)
        self.assertAlmostEqual(self.analyzer.current_depth, 100.0, places=2)  # 45 + 55
        
    def test_calculate_price_impact(self):
        """تست محاسبه تأثیر قیمت"""
        # تهیه order book
        bids = [
            OrderBookLevel(100.0, 10.0, time.time()),
            OrderBookLevel(99.9, 15.0, time.time())
        ]
        asks = [
            OrderBookLevel(100.1, 12.0, time.time()),
            OrderBookLevel(100.2, 18.0, time.time())
        ]
        
        self.analyzer.update_order_book(bids, asks)
        
        # تست تأثیر خرید
        buy_impact = self.analyzer.calculate_price_impact(10.0, 'buy')
        self.assertGreaterEqual(buy_impact, 0)
        
        # تست تأثیر فروش
        sell_impact = self.analyzer.calculate_price_impact(10.0, 'sell')
        self.assertGreaterEqual(sell_impact, 0)
        
    def test_get_market_microstructure(self):
        """تست دریافت میکروساختار بازار"""
        # تهیه داده‌های تست
        bids = [OrderBookLevel(100.0, 10.0, time.time())]
        asks = [OrderBookLevel(100.1, 12.0, time.time())]
        
        self.analyzer.update_order_book(bids, asks)
        
        microstructure = self.analyzer.get_market_microstructure()
        
        self.assertIsInstance(microstructure, MarketMicrostructure)
        self.assertAlmostEqual(microstructure.bid_ask_spread, 0.1, places=2)
        self.assertAlmostEqual(microstructure.market_depth, 22.0, places=2)
        self.assertGreaterEqual(microstructure.timestamp, 0)


class TestLatencyOptimizer(unittest.TestCase):
    """تست‌های LatencyOptimizer"""
    
    def setUp(self):
        self.optimizer = LatencyOptimizer()
        
    def test_measure_execution_time(self):
        """تست اندازه‌گیری زمان اجرا"""
        def dummy_function(x):
            time.sleep(0.001)  # 1ms delay
            return x * 2
            
        result, exec_time = self.optimizer.measure_execution_time(dummy_function, 5)
        
        self.assertEqual(result, 10)
        self.assertGreater(exec_time, 0)
        self.assertLess(exec_time, 100)  # باید کمتر از 100ms باشد
        
    def test_get_latency_stats(self):
        """تست دریافت آمار تأخیر"""
        # اضافه کردن چند اندازه‌گیری
        for i in range(5):
            self.optimizer.execution_times.append(i * 10)
            
        stats = self.optimizer.get_latency_stats()
        
        self.assertIn('avg_execution_time', stats)
        self.assertIn('p95_execution_time', stats)
        self.assertIn('p99_execution_time', stats)
        
    def test_optimize_data_structures(self):
        """تست بهینه‌سازی ساختار داده‌ها"""
        # تست با لیست اعداد
        data = [1, 2, 3, 4, 5]
        optimized = self.optimizer.optimize_data_structures(data)
        
        self.assertIsInstance(optimized, np.ndarray)
        np.testing.assert_array_equal(optimized, np.array(data))
        
        # تست با داده‌های غیرعددی
        text_data = ['a', 'b', 'c']
        optimized_text = self.optimizer.optimize_data_structures(text_data)
        
        self.assertEqual(optimized_text, text_data)


class TestShortTermPredictor(unittest.TestCase):
    """تست‌های ShortTermPredictor"""
    
    def setUp(self):
        self.predictor = ShortTermPredictor(feature_window=20)
        
    def test_initialization(self):
        """تست مقداردهی اولیه"""
        self.assertEqual(self.predictor.feature_window, 20)
        self.assertFalse(self.predictor.is_trained)
        
    def test_extract_features(self):
        """تست استخراج ویژگی"""
        # تهیه داده‌های تست
        prices = [100 + i * 0.1 for i in range(20)]
        volumes = [1000 + i * 10 for i in range(20)]
        spreads = [0.01 + i * 0.001 for i in range(20)]
        
        features = self.predictor.extract_features(prices, volumes, spreads)
        
        self.assertIsInstance(features, np.ndarray)
        self.assertGreater(len(features), 0)
        
    def test_train_model(self):
        """تست آموزش مدل"""
        # تهیه داده‌های تست
        np.random.seed(42)
        price_data = [100 + np.random.randn() * 0.1 for _ in range(200)]
        volume_data = [1000 + np.random.randn() * 100 for _ in range(200)]
        spread_data = [0.01 + np.random.randn() * 0.001 for _ in range(200)]
        
        self.predictor.train_model(price_data, volume_data, spread_data)
        
        self.assertTrue(self.predictor.is_trained)
        self.assertIsNotNone(self.predictor.model)
        
    def test_predict_direction(self):
        """تست پیش‌بینی جهت"""
        # آموزش مدل
        np.random.seed(42)
        price_data = [100 + np.random.randn() * 0.1 for _ in range(200)]
        volume_data = [1000 + np.random.randn() * 100 for _ in range(200)]
        spread_data = [0.01 + np.random.randn() * 0.001 for _ in range(200)]
        
        self.predictor.train_model(price_data, volume_data, spread_data)
        
        # تست پیش‌بینی
        recent_prices = price_data[-50:]
        recent_volumes = volume_data[-50:]
        recent_spreads = spread_data[-50:]
        
        prediction, confidence = self.predictor.predict_direction(
            recent_prices, recent_volumes, recent_spreads
        )
        
        self.assertIsInstance(prediction, (int, float))
        self.assertIsInstance(confidence, (int, float))
        self.assertGreaterEqual(confidence, 0)
        self.assertLessEqual(confidence, 1)


class TestHFTStrategy(unittest.TestCase):
    """تست‌های HFTStrategy"""
    
    def setUp(self):
        self.strategy = HFTStrategy(
            symbol="BTCUSD",
            min_spread_threshold=0.001,
            max_position_size=1000,
            max_holding_time=10
        )
        
    def test_initialization(self):
        """تست مقداردهی اولیه"""
        self.assertEqual(self.strategy.symbol, "BTCUSD")
        self.assertEqual(self.strategy.min_spread_threshold, 0.001)
        self.assertEqual(self.strategy.max_position_size, 1000)
        self.assertEqual(self.strategy.current_position, 0.0)
        
    def test_should_enter_position(self):
        """تست تصمیم‌گیری برای ورود به پوزیشن"""
        # تهیه داده‌های تست
        market_data = MarketMicrostructure(
            bid_ask_spread=0.002,
            market_depth=10000,
            price_impact=0.001,
            order_flow_imbalance=0.1,
            volatility=0.02,
            timestamp=time.time()
        )
        
        # تست با پیش‌بینی صعودی قوی
        should_enter, direction, size = self.strategy.should_enter_position(
            market_data, prediction=0.8, confidence=0.7
        )
        
        self.assertTrue(should_enter)
        self.assertEqual(direction, 'buy')
        self.assertGreater(size, 0)
        
        # تست با پیش‌بینی نزولی قوی
        should_enter, direction, size = self.strategy.should_enter_position(
            market_data, prediction=0.2, confidence=0.7
        )
        
        self.assertTrue(should_enter)
        self.assertEqual(direction, 'sell')
        self.assertGreater(size, 0)
        
        # تست با اطمینان کم
        should_enter, direction, size = self.strategy.should_enter_position(
            market_data, prediction=0.8, confidence=0.3
        )
        
        self.assertFalse(should_enter)
        
    def test_execute_trade(self):
        """تست اجرای معامله"""
        # تست خرید
        success = self.strategy.execute_trade('buy', 100, 50000)
        self.assertTrue(success)
        self.assertEqual(self.strategy.current_position, 100)
        self.assertEqual(self.strategy.total_trades, 1)
        
        # تست فروش
        success = self.strategy.execute_trade('sell', 50, 50100)
        self.assertTrue(success)
        self.assertEqual(self.strategy.current_position, 50)
        self.assertEqual(self.strategy.total_trades, 2)
        
    def test_get_performance_stats(self):
        """تست دریافت آمار عملکرد"""
        # انجام چند معامله
        self.strategy.execute_trade('buy', 100, 50000)
        self.strategy.successful_trades = 1
        
        stats = self.strategy.get_performance_stats()
        
        self.assertIn('total_trades', stats)
        self.assertIn('successful_trades', stats)
        self.assertIn('win_rate', stats)
        self.assertIn('current_position', stats)
        self.assertIn('pnl', stats)
        
        self.assertEqual(stats['total_trades'], 1)
        self.assertEqual(stats['successful_trades'], 1)
        self.assertEqual(stats['win_rate'], 1.0)


class TestHFTModelingSystem(unittest.TestCase):
    """تست‌های HFTModelingSystem"""
    
    def setUp(self):
        self.system = HFTModelingSystem(['BTCUSD', 'ETHUSD'])
        
    def test_initialization(self):
        """تست مقداردهی اولیه"""
        self.assertEqual(len(self.system.symbols), 2)
        self.assertEqual(len(self.system.strategies), 2)
        self.assertIn('BTCUSD', self.system.strategies)
        self.assertIn('ETHUSD', self.system.strategies)
        
    def test_start_stop(self):
        """تست شروع و توقف سیستم"""
        self.assertFalse(self.system.is_running)
        
        self.system.start()
        self.assertTrue(self.system.is_running)
        
        self.system.stop()
        self.assertFalse(self.system.is_running)
        
    def test_process_market_data(self):
        """تست پردازش داده‌های بازار"""
        # تهیه داده‌های تست
        bids = [OrderBookLevel(50000, 1.0, time.time())]
        asks = [OrderBookLevel(50001, 1.0, time.time())]
        trades = [Trade(50000.5, 0.5, time.time(), 'buy')]
        
        # این تست فقط اطمینان می‌دهد که تابع بدون خطا اجرا می‌شود
        try:
            self.system.process_market_data('BTCUSD', bids, asks, trades)
            success = True
        except Exception:
            success = False
            
        self.assertTrue(success)
        
    def test_get_system_stats(self):
        """تست دریافت آمار سیستم"""
        stats = self.system.get_system_stats()
        
        self.assertIsInstance(stats, dict)
        self.assertIn('BTCUSD', stats)
        self.assertIn('ETHUSD', stats)
        
        # بررسی ساختار آمار هر نماد
        btc_stats = stats['BTCUSD']
        self.assertIn('total_trades', btc_stats)
        self.assertIn('current_position', btc_stats)


class TestIntegration(unittest.TestCase):
    """تست‌های یکپارچگی"""
    
    def test_full_hft_workflow(self):
        """تست کامل جریان کار HFT"""
        # ایجاد سیستم
        system = HFTModelingSystem(['BTCUSD'])
        strategy = system.strategies['BTCUSD']
        
        # شروع سیستم
        system.start()
        
        # تهیه داده‌های تست
        bids = [
            OrderBookLevel(50000, 1.0, time.time()),
            OrderBookLevel(49999, 1.5, time.time())
        ]
        asks = [
            OrderBookLevel(50001, 1.0, time.time()),
            OrderBookLevel(50002, 1.5, time.time())
        ]
        
        # به‌روزرسانی order book
        strategy.order_book_analyzer.update_order_book(bids, asks)
        
        # دریافت میکروساختار
        market_data = strategy.order_book_analyzer.get_market_microstructure()
        
        # بررسی که داده‌ها صحیح محاسبه شده‌اند
        self.assertGreater(market_data.bid_ask_spread, 0)
        self.assertGreater(market_data.market_depth, 0)
        
        # توقف سیستم
        system.stop()
        
        self.assertFalse(system.is_running)
        
    def test_latency_measurement(self):
        """تست اندازه‌گیری تأخیر"""
        optimizer = LatencyOptimizer()
        
        # تست اندازه‌گیری زمان اجرا
        def test_func():
            return sum(range(1000))
            
        result, exec_time = optimizer.measure_execution_time(test_func)
        
        self.assertEqual(result, sum(range(1000)))
        self.assertGreater(exec_time, 0)
        
        # دریافت آمار
        stats = optimizer.get_latency_stats()
        self.assertIn('avg_execution_time', stats)


if __name__ == '__main__':
    unittest.main() 