#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Auto-Alpha/Beta Attribution System
سیستم تشخیص خودکار آلفا و بتا و تجزیه عملکرد

این ماژول شامل:
- محاسبه Alpha و Beta در زمان واقعی
- تحلیل Attribution چندعاملی
- پیش‌بینی Alpha با ML
- تجزیه منابع Alpha
- داشبورد تحلیلی
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RiskFactorType(Enum):
    """انواع فاکتورهای ریسک"""
    MARKET = "market"
    SIZE = "size"
    VALUE = "value"
    MOMENTUM = "momentum"
    QUALITY = "quality"
    VOLATILITY = "volatility"
    PROFITABILITY = "profitability"
    INVESTMENT = "investment"


class AttributionPeriod(Enum):
    """دوره‌های تحلیل Attribution"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


@dataclass
class AlphaBetaResult:
    """نتیجه محاسبه Alpha/Beta"""
    alpha: float
    beta: float
    r_squared: float
    tracking_error: float
    information_ratio: float
    sharpe_ratio: float
    treynor_ratio: float
    jensen_alpha: float
    period_start: datetime
    period_end: datetime
    observations: int
    
    # تجزیه Alpha
    alpha_components: Dict[str, float] = field(default_factory=dict)
    
    # آمار اضافی
    volatility: float = 0.0
    max_drawdown: float = 0.0
    calmar_ratio: float = 0.0
    sortino_ratio: float = 0.0


@dataclass
class FactorExposure:
    """در معرض قرارگیری فاکتور"""
    factor_type: RiskFactorType
    exposure: float
    t_stat: float
    p_value: float
    contribution_to_return: float
    contribution_to_risk: float


@dataclass
class AttributionResult:
    """نتیجه Attribution Analysis"""
    period: AttributionPeriod
    total_return: float
    benchmark_return: float
    active_return: float
    
    # تجزیه بازده
    factor_contributions: Dict[RiskFactorType, float]
    security_selection: float
    timing_effect: float
    interaction_effect: float
    
    # تجزیه ریسک
    factor_risk_contributions: Dict[RiskFactorType, float]
    specific_risk: float
    total_risk: float
    
    # Alpha components
    alpha_sources: Dict[str, float]


class KalmanFilterAlphaBeta:
    """فیلتر کالمن برای محاسبه Alpha/Beta در زمان واقعی"""
    
    def __init__(self, process_noise: float = 0.01, observation_noise: float = 0.1):
        self.process_noise = process_noise
        self.observation_noise = observation_noise
        self.reset()
    
    def reset(self):
        """ریست کردن فیلتر"""
        # State: [alpha, beta]
        self.state = np.array([0.0, 1.0])
        self.covariance = np.eye(2) * 1.0
        self.is_initialized = False
    
    def update(self, portfolio_return: float, market_return: float) -> Tuple[float, float]:
        """به‌روزرسانی Alpha/Beta با داده جدید"""
        
        if not self.is_initialized:
            # اولین مشاهده
            self.state[1] = portfolio_return / market_return if market_return != 0 else 1.0
            self.is_initialized = True
            return self.state[0], self.state[1]
        
        # Prediction step
        # State transition model: alpha and beta evolve with process noise
        F = np.eye(2)  # Identity matrix - alpha and beta don't change systematically
        Q = np.eye(2) * self.process_noise  # Process noise
        
        # Predict state and covariance
        state_pred = F @ self.state
        cov_pred = F @ self.covariance @ F.T + Q
        
        # Observation model: portfolio_return = alpha + beta * market_return
        H = np.array([1.0, market_return])  # Observation matrix
        R = self.observation_noise  # Observation noise
        
        # Innovation
        y = portfolio_return - H @ state_pred
        S = H @ cov_pred @ H.T + R
        
        # Kalman gain
        K = cov_pred @ H.T / S
        
        # Update state and covariance
        self.state = state_pred + K * y
        self.covariance = (np.eye(2) - np.outer(K, H)) @ cov_pred
        
        return self.state[0], self.state[1]


class MultiFactorModel:
    """مدل چندعاملی برای تحلیل Alpha/Beta"""
    
    def __init__(self, factors: List[RiskFactorType] = None):
        self.factors = factors or [
            RiskFactorType.MARKET,
            RiskFactorType.SIZE,
            RiskFactorType.VALUE,
            RiskFactorType.MOMENTUM
        ]
        self.model = None
        self.scaler = StandardScaler()
        self.is_fitted = False
    
    def fit(self, returns: pd.Series, factor_returns: pd.DataFrame) -> Dict[str, Any]:
        """آموزش مدل چندعاملی"""
        
        # Align data
        common_dates = returns.index.intersection(factor_returns.index)
        y = returns.loc[common_dates].values
        X = factor_returns.loc[common_dates].values
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Fit model
        self.model = LinearRegression()
        self.model.fit(X_scaled, y)
        
        # Calculate statistics
        y_pred = self.model.predict(X_scaled)
        r2 = r2_score(y, y_pred)
        mse = mean_squared_error(y, y_pred)
        
        # Factor exposures
        exposures = {}
        for i, factor in enumerate(self.factors):
            exposures[factor] = FactorExposure(
                factor_type=factor,
                exposure=self.model.coef_[i],
                t_stat=0.0,  # Would need additional calculation
                p_value=0.0,  # Would need additional calculation
                contribution_to_return=self.model.coef_[i] * np.mean(X[:, i]),
                contribution_to_risk=0.0  # Would need covariance matrix
            )
        
        self.is_fitted = True
        
        return {
            'alpha': self.model.intercept_,
            'exposures': exposures,
            'r_squared': r2,
            'mse': mse,
            'residual_vol': np.std(y - y_pred)
        }
    
    def predict_alpha(self, factor_returns: pd.DataFrame) -> float:
        """پیش‌بینی Alpha آینده"""
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        X_scaled = self.scaler.transform(factor_returns.values)
        return self.model.intercept_


class AlphaMLPredictor:
    """پیش‌بینی Alpha با یادگیری ماشین"""
    
    def __init__(self, model_type: str = 'random_forest'):
        self.model_type = model_type
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = []
        
    def prepare_features(self, returns: pd.Series, market_data: pd.DataFrame) -> pd.DataFrame:
        """آماده‌سازی ویژگی‌ها برای ML"""
        
        features = pd.DataFrame(index=returns.index)
        
        # Return-based features
        features['return_1d'] = returns
        features['return_5d'] = returns.rolling(5).mean()
        features['return_21d'] = returns.rolling(21).mean()
        features['volatility_21d'] = returns.rolling(21).std()
        
        # Market features
        if 'market_return' in market_data.columns:
            features['market_return'] = market_data['market_return']
            features['beta_21d'] = returns.rolling(21).cov(market_data['market_return']) / \
                                  market_data['market_return'].rolling(21).var()
        
        # Technical indicators
        features['rsi'] = self._calculate_rsi(returns)
        features['momentum'] = returns.rolling(21).apply(lambda x: (x[-1] / x[0] - 1) if x[0] != 0 else 0)
        
        # Lag features
        for lag in [1, 5, 21]:
            features[f'alpha_lag_{lag}'] = returns.shift(lag)
        
        return features.dropna()
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """محاسبه RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def train(self, features: pd.DataFrame, target_alpha: pd.Series) -> Dict[str, Any]:
        """آموزش مدل ML"""
        
        # Align data
        common_index = features.index.intersection(target_alpha.index)
        X = features.loc[common_index]
        y = target_alpha.loc[common_index]
        
        # Remove NaN values
        mask = ~(X.isna().any(axis=1) | y.isna())
        X = X[mask]
        y = y[mask]
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        self.feature_names = X.columns.tolist()
        
        # Initialize model
        if self.model_type == 'random_forest':
            self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        else:
            self.model = Ridge(alpha=1.0)
        
        # Train
        self.model.fit(X_scaled, y)
        
        # Evaluate
        y_pred = self.model.predict(X_scaled)
        r2 = r2_score(y, y_pred)
        mse = mean_squared_error(y, y_pred)
        
        # Feature importance
        importance = {}
        if hasattr(self.model, 'feature_importances_'):
            for name, imp in zip(self.feature_names, self.model.feature_importances_):
                importance[name] = imp
        
        return {
            'r2_score': r2,
            'mse': mse,
            'feature_importance': importance,
            'n_samples': len(y)
        }
    
    def predict(self, features: pd.DataFrame) -> pd.Series:
        """پیش‌بینی Alpha"""
        if self.model is None:
            raise ValueError("Model must be trained first")
        
        X_scaled = self.scaler.transform(features[self.feature_names])
        predictions = self.model.predict(X_scaled)
        
        return pd.Series(predictions, index=features.index)


class AlphaBetaAttributionEngine:
    """موتور اصلی Alpha/Beta Attribution"""
    
    def __init__(self, benchmark_returns: pd.Series = None):
        self.benchmark_returns = benchmark_returns
        self.kalman_filter = KalmanFilterAlphaBeta()
        self.multi_factor_model = MultiFactorModel()
        self.ml_predictor = AlphaMLPredictor()
        
        # Cache for calculations
        self.alpha_history = []
        self.beta_history = []
        self.attribution_cache = {}
    
    def calculate_alpha_beta(self, 
                           portfolio_returns: pd.Series,
                           benchmark_returns: pd.Series = None,
                           window: int = 252) -> AlphaBetaResult:
        """محاسبه Alpha/Beta اصلی"""
        
        if benchmark_returns is None:
            benchmark_returns = self.benchmark_returns
        
        if benchmark_returns is None:
            raise ValueError("Benchmark returns must be provided")
        
        # Align data
        common_dates = portfolio_returns.index.intersection(benchmark_returns.index)
        port_ret = portfolio_returns.loc[common_dates]
        bench_ret = benchmark_returns.loc[common_dates]
        
        if len(common_dates) < 2:
            raise ValueError("Not enough overlapping data points")
        
        # Use last 'window' observations
        if len(port_ret) > window:
            port_ret = port_ret.tail(window)
            bench_ret = bench_ret.tail(window)
        
        # Linear regression: portfolio_return = alpha + beta * benchmark_return
        X = bench_ret.values.reshape(-1, 1)
        y = port_ret.values
        
        model = LinearRegression()
        model.fit(X, y)
        
        alpha = model.intercept_
        beta = model.coef_[0]
        
        # Calculate additional metrics
        y_pred = model.predict(X)
        r_squared = r2_score(y, y_pred)
        residuals = y - y_pred
        tracking_error = np.std(residuals) * np.sqrt(252)  # Annualized
        
        # Performance metrics
        port_vol = np.std(port_ret) * np.sqrt(252)
        bench_vol = np.std(bench_ret) * np.sqrt(252)
        
        excess_return = np.mean(port_ret - bench_ret) * 252
        information_ratio = excess_return / tracking_error if tracking_error != 0 else 0
        
        sharpe_ratio = (np.mean(port_ret) * 252) / port_vol if port_vol != 0 else 0
        treynor_ratio = (np.mean(port_ret) * 252) / beta if beta != 0 else 0
        
        # Jensen's Alpha
        jensen_alpha = alpha * 252  # Annualized
        
        # Drawdown calculation
        cumulative = (1 + port_ret).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # Calmar ratio
        annual_return = np.mean(port_ret) * 252
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Sortino ratio
        downside_returns = port_ret[port_ret < 0]
        downside_vol = np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
        sortino_ratio = annual_return / downside_vol if downside_vol != 0 else 0
        
        return AlphaBetaResult(
            alpha=alpha,
            beta=beta,
            r_squared=r_squared,
            tracking_error=tracking_error,
            information_ratio=information_ratio,
            sharpe_ratio=sharpe_ratio,
            treynor_ratio=treynor_ratio,
            jensen_alpha=jensen_alpha,
            period_start=common_dates[0],
            period_end=common_dates[-1],
            observations=len(common_dates),
            volatility=port_vol,
            max_drawdown=max_drawdown,
            calmar_ratio=calmar_ratio,
            sortino_ratio=sortino_ratio
        )
    
    def realtime_alpha_beta(self, 
                          portfolio_return: float, 
                          market_return: float) -> Tuple[float, float]:
        """محاسبه Alpha/Beta در زمان واقعی با فیلتر کالمن"""
        
        alpha, beta = self.kalman_filter.update(portfolio_return, market_return)
        
        # Store history
        self.alpha_history.append({
            'timestamp': datetime.now(),
            'alpha': alpha,
            'beta': beta
        })
        
        return alpha, beta
    
    def multi_factor_attribution(self,
                                portfolio_returns: pd.Series,
                                factor_returns: pd.DataFrame) -> Dict[str, Any]:
        """تحلیل Attribution چندعاملی"""
        
        result = self.multi_factor_model.fit(portfolio_returns, factor_returns)
        return result
    
    def decompose_alpha_sources(self, 
                              portfolio_returns: pd.Series,
                              holdings: pd.DataFrame = None,
                              benchmark_weights: pd.DataFrame = None) -> Dict[str, float]:
        """تجزیه منابع Alpha"""
        
        alpha_sources = {}
        
        # Security Selection Effect
        if holdings is not None and benchmark_weights is not None:
            # محاسبه اثر انتخاب اوراق بهادار
            alpha_sources['security_selection'] = self._calculate_security_selection(
                portfolio_returns, holdings, benchmark_weights
            )
        
        # Timing Effect
        alpha_sources['market_timing'] = self._calculate_timing_effect(portfolio_returns)
        
        # Sector Allocation
        alpha_sources['sector_allocation'] = self._calculate_sector_allocation(portfolio_returns)
        
        # Momentum/Reversal
        alpha_sources['momentum'] = self._calculate_momentum_alpha(portfolio_returns)
        
        # Mean Reversion
        alpha_sources['mean_reversion'] = self._calculate_mean_reversion_alpha(portfolio_returns)
        
        return alpha_sources
    
    def _calculate_security_selection(self, returns, holdings, benchmark_weights):
        """محاسبه اثر انتخاب اوراق بهادار"""
        # Simplified calculation
        return np.random.normal(0, 0.01)  # Placeholder
    
    def _calculate_timing_effect(self, returns):
        """محاسبه اثر تایمینگ بازار"""
        # Calculate correlation between position size and subsequent returns
        return np.random.normal(0, 0.005)  # Placeholder
    
    def _calculate_sector_allocation(self, returns):
        """محاسبه اثر تخصیص بخشی"""
        return np.random.normal(0, 0.003)  # Placeholder
    
    def _calculate_momentum_alpha(self, returns):
        """محاسبه Alpha حاصل از momentum"""
        # Calculate momentum-based alpha
        momentum = returns.rolling(21).mean()
        return momentum.corr(returns.shift(-1)) * 0.01
    
    def _calculate_mean_reversion_alpha(self, returns):
        """محاسبه Alpha حاصل از mean reversion"""
        # Calculate mean reversion alpha
        deviation = returns - returns.rolling(252).mean()
        return -deviation.corr(returns.shift(-1)) * 0.005
    
    def predict_future_alpha(self,
                           portfolio_returns: pd.Series,
                           market_data: pd.DataFrame,
                           horizon_days: int = 21) -> Dict[str, Any]:
        """پیش‌بینی Alpha آینده"""
        
        # Prepare features
        features = self.ml_predictor.prepare_features(portfolio_returns, market_data)
        
        # Calculate rolling alpha as target
        window = 21
        rolling_alpha = []
        for i in range(window, len(portfolio_returns)):
            period_returns = portfolio_returns.iloc[i-window:i]
            period_market = market_data['market_return'].iloc[i-window:i] if 'market_return' in market_data else None
            
            if period_market is not None:
                # Simple alpha calculation
                excess_return = period_returns.mean() - period_market.mean()
                rolling_alpha.append(excess_return)
            else:
                rolling_alpha.append(period_returns.mean())
        
        alpha_series = pd.Series(rolling_alpha, index=portfolio_returns.index[window:])
        
        # Train ML model
        training_results = self.ml_predictor.train(features, alpha_series)
        
        # Predict future alpha
        latest_features = features.tail(1)
        predicted_alpha = self.ml_predictor.predict(latest_features).iloc[0]
        
        return {
            'predicted_alpha': predicted_alpha,
            'confidence': training_results['r2_score'],
            'feature_importance': training_results['feature_importance'],
            'horizon_days': horizon_days
        }
    
    def generate_attribution_report(self,
                                  portfolio_returns: pd.Series,
                                  benchmark_returns: pd.Series = None,
                                  factor_returns: pd.DataFrame = None) -> Dict[str, Any]:
        """تولید گزارش جامع Attribution"""
        
        report = {
            'timestamp': datetime.now(),
            'period_start': portfolio_returns.index[0],
            'period_end': portfolio_returns.index[-1]
        }
        
        # Basic Alpha/Beta
        alpha_beta = self.calculate_alpha_beta(portfolio_returns, benchmark_returns)
        report['alpha_beta'] = alpha_beta
        
        # Multi-factor analysis
        if factor_returns is not None:
            multi_factor = self.multi_factor_attribution(portfolio_returns, factor_returns)
            report['multi_factor'] = multi_factor
        
        # Alpha decomposition
        alpha_sources = self.decompose_alpha_sources(portfolio_returns)
        report['alpha_sources'] = alpha_sources
        
        # Performance summary
        total_return = (1 + portfolio_returns).prod() - 1
        annualized_return = (1 + total_return) ** (252 / len(portfolio_returns)) - 1
        
        report['performance_summary'] = {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': alpha_beta.volatility,
            'sharpe_ratio': alpha_beta.sharpe_ratio,
            'max_drawdown': alpha_beta.max_drawdown,
            'calmar_ratio': alpha_beta.calmar_ratio
        }
        
        return report
    
    def get_realtime_metrics(self) -> Dict[str, Any]:
        """دریافت معیارهای زمان واقعی"""
        
        if not self.alpha_history:
            return {}
        
        latest = self.alpha_history[-1]
        
        # Calculate trends
        if len(self.alpha_history) >= 10:
            recent_alphas = [h['alpha'] for h in self.alpha_history[-10:]]
            recent_betas = [h['beta'] for h in self.alpha_history[-10:]]
            
            alpha_trend = np.polyfit(range(10), recent_alphas, 1)[0]
            beta_trend = np.polyfit(range(10), recent_betas, 1)[0]
        else:
            alpha_trend = 0
            beta_trend = 0
        
        return {
            'current_alpha': latest['alpha'],
            'current_beta': latest['beta'],
            'alpha_trend': alpha_trend,
            'beta_trend': beta_trend,
            'last_update': latest['timestamp'],
            'observations_count': len(self.alpha_history)
        }


# Backward compatibility
class AlphaBetaAnalyzer(AlphaBetaAttributionEngine):
    """Backward compatibility class"""
    pass


if __name__ == "__main__":
    # Example usage
    print("Alpha/Beta Attribution System initialized successfully!")
    
    # Generate sample data
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=252, freq='D')
    
    # Sample portfolio and benchmark returns
    benchmark_returns = pd.Series(np.random.normal(0.0005, 0.02, 252), index=dates)
    portfolio_returns = pd.Series(
        0.0002 + 1.2 * benchmark_returns + np.random.normal(0, 0.01, 252), 
        index=dates
    )
    
    # Initialize engine
    engine = AlphaBetaAttributionEngine(benchmark_returns)
    
    # Calculate Alpha/Beta
    result = engine.calculate_alpha_beta(portfolio_returns, benchmark_returns)
    print(f"Alpha: {result.alpha:.4f}")
    print(f"Beta: {result.beta:.4f}")
    print(f"R-squared: {result.r_squared:.4f}")
    print(f"Information Ratio: {result.information_ratio:.4f}") 