import os
import glob

def cleanup_old_models(model_dirs: list, keep_last: int = 3) -> None:
    """
    حذف مدل‌ها و checkpointهای قدیمی، نگه‌داشتن فقط آخرین N فایل در هر پوشه.

    Args:
        model_dirs (list): لیست مسیر پوشه‌های مدل
        keep_last (int): تعداد فایل‌هایی که باید نگه داشته شوند
    """
    for model_dir in model_dirs:
        files = sorted(
            glob.glob(os.path.join(model_dir, '*.zip')) +
            glob.glob(os.path.join(model_dir, '*.pkl')),
            key=os.path.getmtime,
            reverse=True
        )
        for f in files[keep_last:]:
            try:
                os.remove(f)
            except Exception as e:
                print(f"Error removing {f}: {e}")
