{"type": "startup_tests", "timestamp": "2025-07-13T22:39:40.182268", "summary": {"total_tests": 4, "passed": 0, "failed": 4, "success_rate": 0.0, "total_duration": 141.58321261405945}, "results": [{"name": "test_advanced_risk_manager.py", "passed": false, "duration": 35.695462465286255, "output": "", "error": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 710, in _importconftest\n    mod = import_path(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\pathlib.py\", line 587, in import_path\n    importlib.import_module(module_name)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\__init__.py\", line 127, in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n  File \"<frozen importlib._bootstrap>\", line 1030, in _gcd_import\n  File \"<frozen importlib._bootstrap>\", line 1007, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 986, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 680, in _load_unlocked\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\assertion\\rewrite.py\", line 185, in exec_module\n    exec(co, module.__dict__)\n  File \"D:\\project\\tests\\conftest.py\", line 34, in <module>\n    from evaluation import evaluation_engine, EvaluationEngine\n  File \"D:\\project\\evaluation\\__init__.py\", line 26, in <module>\n    from .comparison import (\n  File \"D:\\project\\evaluation\\comparison.py\", line 226\n    report += f\"\n                ^\nSyntaxError: EOL while scanning string literal\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\capture.py\", line 154, in pytest_load_initial_conftests\n    @hookimpl(wrapper=True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 103, in _multicall\n    res = hook_impl.function(*args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1222, in pytest_load_initial_conftests\n    self.pluginmanager._set_initial_conftests(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 581, in _set_initial_conftests\n    self._try_load_conftest(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 619, in _try_load_conftest\n    self._loadconftestmodules(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 659, in _loadconftestmodules\n    mod = self._importconftest(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 718, in _importconftest\n    raise ConftestImportFailure(conftestpath, cause=e) from e\n_pytest.config.ConftestImportFailure: SyntaxError: EOL while scanning string literal (comparison.py, line 226) (from D:\\project\\tests\\conftest.py)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\runpy.py\", line 197, in _run_module_as_main\n    return _run_code(code, main_globals, None,\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\runpy.py\", line 87, in _run_code\n    exec(code, run_globals)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pytest\\__main__.py\", line 9, in <module>\n    raise SystemExit(pytest.console_main())\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 201, in console_main\n    code = main()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 156, in main\n    config = _prepareconfig(args, plugins)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 341, in _prepareconfig\n    config = pluginmanager.hook.pytest_cmdline_parse(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_hooks.py\", line 513, in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_manager.py\", line 120, in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 139, in _multicall\n    raise exception.with_traceback(exception.__traceback__)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 122, in _multicall\n    teardown.throw(exception)  # type: ignore[union-attr]\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\helpconfig.py\", line 103, in pytest_cmdline_parse\n    @pytest.hookimpl(wrapper=True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 103, in _multicall\n    res = hook_impl.function(*args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1140, in pytest_cmdline_parse\n    self.parse(args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1494, in parse\n    self._preparse(args, addopts=addopts)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1398, in _preparse\n    self.hook.pytest_load_initial_conftests(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_hooks.py\", line 513, in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_manager.py\", line 120, in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 139, in _multicall\n    raise exception.with_traceback(exception.__traceback__)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 122, in _multicall\n    teardown.throw(exception)  # type: ignore[union-attr]\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\warnings.py\", line 144, in pytest_load_initial_conftests\n    @pytest.hookimpl(wrapper=True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 122, in _multicall\n    teardown.throw(exception)  # type: ignore[union-attr]\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\capture.py\", line 177, in pytest_load_initial_conftests\n    sys.stdout.write(out)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u2705' in position 0: character maps to <undefined>\n", "timestamp": "2025-07-13T22:37:54.210928"}, {"name": "test_smart_portfolio_manager.py", "passed": false, "duration": 36.36194062232971, "output": "", "error": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 710, in _importconftest\n    mod = import_path(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\pathlib.py\", line 587, in import_path\n    importlib.import_module(module_name)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\__init__.py\", line 127, in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n  File \"<frozen importlib._bootstrap>\", line 1030, in _gcd_import\n  File \"<frozen importlib._bootstrap>\", line 1007, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 986, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 680, in _load_unlocked\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\assertion\\rewrite.py\", line 185, in exec_module\n    exec(co, module.__dict__)\n  File \"D:\\project\\tests\\conftest.py\", line 34, in <module>\n    from evaluation import evaluation_engine, EvaluationEngine\n  File \"D:\\project\\evaluation\\__init__.py\", line 26, in <module>\n    from .comparison import (\n  File \"D:\\project\\evaluation\\comparison.py\", line 226\n    report += f\"\n                ^\nSyntaxError: EOL while scanning string literal\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\capture.py\", line 154, in pytest_load_initial_conftests\n    @hookimpl(wrapper=True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 103, in _multicall\n    res = hook_impl.function(*args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1222, in pytest_load_initial_conftests\n    self.pluginmanager._set_initial_conftests(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 581, in _set_initial_conftests\n    self._try_load_conftest(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 619, in _try_load_conftest\n    self._loadconftestmodules(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 659, in _loadconftestmodules\n    mod = self._importconftest(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 718, in _importconftest\n    raise ConftestImportFailure(conftestpath, cause=e) from e\n_pytest.config.ConftestImportFailure: SyntaxError: EOL while scanning string literal (comparison.py, line 226) (from D:\\project\\tests\\conftest.py)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\runpy.py\", line 197, in _run_module_as_main\n    return _run_code(code, main_globals, None,\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\runpy.py\", line 87, in _run_code\n    exec(code, run_globals)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pytest\\__main__.py\", line 9, in <module>\n    raise SystemExit(pytest.console_main())\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 201, in console_main\n    code = main()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 156, in main\n    config = _prepareconfig(args, plugins)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 341, in _prepareconfig\n    config = pluginmanager.hook.pytest_cmdline_parse(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_hooks.py\", line 513, in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_manager.py\", line 120, in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 139, in _multicall\n    raise exception.with_traceback(exception.__traceback__)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 122, in _multicall\n    teardown.throw(exception)  # type: ignore[union-attr]\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\helpconfig.py\", line 103, in pytest_cmdline_parse\n    @pytest.hookimpl(wrapper=True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 103, in _multicall\n    res = hook_impl.function(*args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1140, in pytest_cmdline_parse\n    self.parse(args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1494, in parse\n    self._preparse(args, addopts=addopts)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1398, in _preparse\n    self.hook.pytest_load_initial_conftests(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_hooks.py\", line 513, in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_manager.py\", line 120, in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 139, in _multicall\n    raise exception.with_traceback(exception.__traceback__)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 122, in _multicall\n    teardown.throw(exception)  # type: ignore[union-attr]\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\warnings.py\", line 144, in pytest_load_initial_conftests\n    @pytest.hookimpl(wrapper=True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 122, in _multicall\n    teardown.throw(exception)  # type: ignore[union-attr]\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\capture.py\", line 177, in pytest_load_initial_conftests\n    sys.stdout.write(out)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u2705' in position 0: character maps to <undefined>\n", "timestamp": "2025-07-13T22:38:30.593726"}, {"name": "test_integrated_system.py", "passed": false, "duration": 34.78926706314087, "output": "", "error": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 710, in _importconftest\n    mod = import_path(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\pathlib.py\", line 587, in import_path\n    importlib.import_module(module_name)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\__init__.py\", line 127, in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n  File \"<frozen importlib._bootstrap>\", line 1030, in _gcd_import\n  File \"<frozen importlib._bootstrap>\", line 1007, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 986, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 680, in _load_unlocked\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\assertion\\rewrite.py\", line 185, in exec_module\n    exec(co, module.__dict__)\n  File \"D:\\project\\tests\\conftest.py\", line 34, in <module>\n    from evaluation import evaluation_engine, EvaluationEngine\n  File \"D:\\project\\evaluation\\__init__.py\", line 26, in <module>\n    from .comparison import (\n  File \"D:\\project\\evaluation\\comparison.py\", line 226\n    report += f\"\n                ^\nSyntaxError: EOL while scanning string literal\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\capture.py\", line 154, in pytest_load_initial_conftests\n    @hookimpl(wrapper=True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 103, in _multicall\n    res = hook_impl.function(*args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1222, in pytest_load_initial_conftests\n    self.pluginmanager._set_initial_conftests(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 581, in _set_initial_conftests\n    self._try_load_conftest(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 619, in _try_load_conftest\n    self._loadconftestmodules(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 659, in _loadconftestmodules\n    mod = self._importconftest(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 718, in _importconftest\n    raise ConftestImportFailure(conftestpath, cause=e) from e\n_pytest.config.ConftestImportFailure: SyntaxError: EOL while scanning string literal (comparison.py, line 226) (from D:\\project\\tests\\conftest.py)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\runpy.py\", line 197, in _run_module_as_main\n    return _run_code(code, main_globals, None,\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\runpy.py\", line 87, in _run_code\n    exec(code, run_globals)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pytest\\__main__.py\", line 9, in <module>\n    raise SystemExit(pytest.console_main())\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 201, in console_main\n    code = main()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 156, in main\n    config = _prepareconfig(args, plugins)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 341, in _prepareconfig\n    config = pluginmanager.hook.pytest_cmdline_parse(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_hooks.py\", line 513, in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_manager.py\", line 120, in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 139, in _multicall\n    raise exception.with_traceback(exception.__traceback__)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 122, in _multicall\n    teardown.throw(exception)  # type: ignore[union-attr]\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\helpconfig.py\", line 103, in pytest_cmdline_parse\n    @pytest.hookimpl(wrapper=True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 103, in _multicall\n    res = hook_impl.function(*args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1140, in pytest_cmdline_parse\n    self.parse(args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1494, in parse\n    self._preparse(args, addopts=addopts)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1398, in _preparse\n    self.hook.pytest_load_initial_conftests(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_hooks.py\", line 513, in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_manager.py\", line 120, in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 139, in _multicall\n    raise exception.with_traceback(exception.__traceback__)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 122, in _multicall\n    teardown.throw(exception)  # type: ignore[union-attr]\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\warnings.py\", line 144, in pytest_load_initial_conftests\n    @pytest.hookimpl(wrapper=True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 122, in _multicall\n    teardown.throw(exception)  # type: ignore[union-attr]\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\capture.py\", line 177, in pytest_load_initial_conftests\n    sys.stdout.write(out)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u2705' in position 0: character maps to <undefined>\n", "timestamp": "2025-07-13T22:39:05.407615"}, {"name": "test_integration_*.py", "passed": false, "duration": 34.73654246330261, "output": "", "error": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 710, in _importconftest\n    mod = import_path(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\pathlib.py\", line 587, in import_path\n    importlib.import_module(module_name)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\__init__.py\", line 127, in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n  File \"<frozen importlib._bootstrap>\", line 1030, in _gcd_import\n  File \"<frozen importlib._bootstrap>\", line 1007, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 986, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 680, in _load_unlocked\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\assertion\\rewrite.py\", line 185, in exec_module\n    exec(co, module.__dict__)\n  File \"D:\\project\\tests\\conftest.py\", line 34, in <module>\n    from evaluation import evaluation_engine, EvaluationEngine\n  File \"D:\\project\\evaluation\\__init__.py\", line 26, in <module>\n    from .comparison import (\n  File \"D:\\project\\evaluation\\comparison.py\", line 226\n    report += f\"\n                ^\nSyntaxError: EOL while scanning string literal\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\capture.py\", line 154, in pytest_load_initial_conftests\n    @hookimpl(wrapper=True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 103, in _multicall\n    res = hook_impl.function(*args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1222, in pytest_load_initial_conftests\n    self.pluginmanager._set_initial_conftests(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 589, in _set_initial_conftests\n    self._try_load_conftest(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 629, in _try_load_conftest\n    self._loadconftestmodules(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 659, in _loadconftestmodules\n    mod = self._importconftest(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 718, in _importconftest\n    raise ConftestImportFailure(conftestpath, cause=e) from e\n_pytest.config.ConftestImportFailure: SyntaxError: EOL while scanning string literal (comparison.py, line 226) (from D:\\project\\tests\\conftest.py)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\runpy.py\", line 197, in _run_module_as_main\n    return _run_code(code, main_globals, None,\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\runpy.py\", line 87, in _run_code\n    exec(code, run_globals)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pytest\\__main__.py\", line 9, in <module>\n    raise SystemExit(pytest.console_main())\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 201, in console_main\n    code = main()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 156, in main\n    config = _prepareconfig(args, plugins)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 341, in _prepareconfig\n    config = pluginmanager.hook.pytest_cmdline_parse(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_hooks.py\", line 513, in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_manager.py\", line 120, in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 139, in _multicall\n    raise exception.with_traceback(exception.__traceback__)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 122, in _multicall\n    teardown.throw(exception)  # type: ignore[union-attr]\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\helpconfig.py\", line 103, in pytest_cmdline_parse\n    @pytest.hookimpl(wrapper=True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 103, in _multicall\n    res = hook_impl.function(*args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1140, in pytest_cmdline_parse\n    self.parse(args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1494, in parse\n    self._preparse(args, addopts=addopts)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\config\\__init__.py\", line 1398, in _preparse\n    self.hook.pytest_load_initial_conftests(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_hooks.py\", line 513, in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_manager.py\", line 120, in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 139, in _multicall\n    raise exception.with_traceback(exception.__traceback__)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 122, in _multicall\n    teardown.throw(exception)  # type: ignore[union-attr]\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\warnings.py\", line 144, in pytest_load_initial_conftests\n    @pytest.hookimpl(wrapper=True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pluggy\\_callers.py\", line 122, in _multicall\n    teardown.throw(exception)  # type: ignore[union-attr]\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_pytest\\capture.py\", line 177, in pytest_load_initial_conftests\n    sys.stdout.write(out)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u2705' in position 0: character maps to <undefined>\n", "timestamp": "2025-07-13T22:39:40.163209"}]}