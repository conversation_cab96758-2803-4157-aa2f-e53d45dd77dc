#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Complete Module Test Script
اسکریپت تست جامع تمام ماژول‌های پروژه
"""

import sys
import logging
from datetime import datetime

# تنظیم logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_semi_active_modules():
    """تست ماژول‌های نیمه‌فعال"""
    print('\n⚠️ ماژول‌های نیمه‌فعال:')
    print('=' * 40)
    
    # 1. AnomalyDetectionSystem
    try:
        from utils.anomaly_detection_system import AnomalyDetectionSystem
        print('✅ AnomalyDetectionSystem - کلاس اصلی موجود')
        
        # تست عملکرد
        system = AnomalyDetectionSystem()
        print(f'   - Database: {system.db_path}')
        print(f'   - Initialized: {system.is_initialized}')
        print(f'   - Feature Extractor: {type(system.feature_extractor).__name__}')
        print(f'   - Anomaly Detector: {type(system.anomaly_detector).__name__}')
        print(f'   - Adaptation Engine: {type(system.adaptation_engine).__name__}')
        print('   - Status: ✅ کامل و عملیاتی')
        
    except Exception as e:
        print(f'❌ AnomalyDetectionSystem - خطا: {e}')
    
    # 2. FederatedLearningSystem
    try:
        from utils.federated_learning_system import FederatedLearningServer, FederatedLearningClient
        print('\n✅ FederatedLearningSystem - کلاس‌های اصلی موجود')
        
        # تست عملکرد
        server = FederatedLearningServer()
        client = FederatedLearningClient('test_client')
        print(f'   - Server models: {len(server.federated_models)}')
        print(f'   - Client ID: {client.client_id}')
        print(f'   - Server aggregator: {type(server.aggregator).__name__}')
        print(f'   - Client local models: {len(client.local_models)}')
        print('   - Status: ✅ کامل و عملیاتی')
        
    except Exception as e:
        print(f'❌ FederatedLearningSystem - خطا: {e}')
    
    # 3. GeneticStrategyEvolution
    try:
        from utils.genetic_strategy_evolution import GeneticStrategyEvolution
        print('\n✅ GeneticStrategyEvolution - کلاس اصلی موجود')
        
        # تست عملکرد
        evolution = GeneticStrategyEvolution()
        print(f'   - Database: {evolution.db_path}')
        print(f'   - Historical data: {len(evolution.historical_data)} points')
        print(f'   - Best strategies: {len(evolution.best_strategies)}')
        print(f'   - Evolution history: {len(evolution.evolution_history)}')
        print('   - Status: ✅ کامل و عملیاتی')
        
    except Exception as e:
        print(f'❌ GeneticStrategyEvolution - خطا: {e}')

def test_inactive_modules():
    """تست ماژول‌های غیرفعال"""
    print('\n❌ ماژول‌های غیرفعال:')
    print('=' * 40)
    
    # 1. AdaptiveMarginControl
    try:
        from utils.adaptive_margin_control import AdaptiveMarginControl
        print('✅ AdaptiveMarginControl - کلاس اصلی موجود')
        
        # تست عملکرد
        margin_control = AdaptiveMarginControl()
        test_result = margin_control.calculate_margin_requirement('EURUSD', 1.1000, 1000)
        print(f'   - Base rates: {len(margin_control.base_margin_rates)} symbols')
        print(f'   - Test margin: {test_result["margin_requirement"]:.4f}')
        print(f'   - ML Model: {margin_control.use_ml_model}')
        print(f'   - Price history: {len(margin_control.price_history)} symbols')
        print('   - Status: ✅ کامل و عملیاتی')
        
    except Exception as e:
        print(f'❌ AdaptiveMarginControl - خطا: {e}')
    
    # 2. AdvancedRewardSystem
    try:
        from utils.advanced_reward_system import AdvancedRewardSystem
        print('\n✅ AdvancedRewardSystem - کلاس اصلی موجود')
        
        # تست عملکرد
        reward_system = AdvancedRewardSystem()
        test_reward = reward_system.calculate_reward({'type': 'buy'}, {'profit': 100})
        print(f'   - Test reward: {test_reward:.4f}')
        print(f'   - Adaptive system: {type(reward_system.adaptive_system).__name__}')
        print('   - Status: ✅ کامل و عملیاتی')
        
    except Exception as e:
        print(f'❌ AdvancedRewardSystem - خطا: {e}')
    
    # 3. AutoMarketMaker
    try:
        from utils.auto_market_maker import AutoMarketMaker
        print('\n✅ AutoMarketMaker - کلاس اصلی موجود')
        
        # تست عملکرد
        market_maker = AutoMarketMaker()
        print(f'   - Market maker type: {type(market_maker).__name__}')
        print('   - Status: ✅ کامل و عملیاتی')
        
    except Exception as e:
        print(f'❌ AutoMarketMaker - خطا: {e}')
    
    # 4. HierarchicalRL
    try:
        from utils.hierarchical_rl import HierarchicalRL
        print('\n✅ HierarchicalRL - کلاس اصلی موجود')
        
        # تست عملکرد
        hierarchical_rl = HierarchicalRL()
        test_action = hierarchical_rl.get_action({'price': 1.1})
        print(f'   - Test action: {test_action.high_level}')
        print(f'   - Action type: {type(test_action).__name__}')
        print('   - Status: ✅ کامل و عملیاتی')
        
    except Exception as e:
        print(f'❌ HierarchicalRL - خطا: {e}')
    
    # 5. ZeroShotLearning
    try:
        from utils.zero_shot_learning import ZeroShotLearning
        print('\n✅ ZeroShotLearning - کلاس اصلی موجود')
        
        # تست عملکرد
        zero_shot = ZeroShotLearning()
        test_prediction = zero_shot.predict({'features': [1.1, 1.2, 1.15]})
        print(f'   - Test prediction: {test_prediction:.4f}')
        print(f'   - Zero shot type: {type(zero_shot).__name__}')
        print('   - Status: ✅ کامل و عملیاتی')
        
    except Exception as e:
        print(f'❌ ZeroShotLearning - خطا: {e}')

def test_main_system_integration():
    """تست یکپارچگی با سیستم اصلی"""
    print('\n🔗 تست یکپارچگی با سیستم اصلی:')
    print('=' * 40)
    
    try:
        # بررسی main_new.py
        print('📋 بررسی main_new.py...')
        
        # خواندن فایل main_new.py
        with open('main_new.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        # بررسی import های ماژول‌ها
        modules_to_check = [
            'AnomalyDetectionSystem',
            'FederatedLearningServer',
            'GeneticStrategyEvolution',
            'AdaptiveMarginControl',
            'AdvancedRewardSystem',
            'AutoMarketMaker',
            'HierarchicalRL',
            'ZeroShotLearning'
        ]
        
        integrated_modules = []
        for module in modules_to_check:
            if module in main_content:
                integrated_modules.append(module)
                print(f'   ✅ {module} - یکپارچه شده')
            else:
                print(f'   ❌ {module} - یکپارچه نشده')
        
        print(f'\n📊 نتیجه یکپارچگی: {len(integrated_modules)}/{len(modules_to_check)} ماژول')
        
    except Exception as e:
        print(f'❌ خطا در بررسی یکپارچگی: {e}')

def generate_final_report():
    """تولید گزارش نهایی"""
    print('\n📊 گزارش نهایی:')
    print('=' * 60)
    
    # شمارش ماژول‌ها
    total_modules = 8  # 3 نیمه‌فعال + 5 غیرفعال
    operational_modules = 0
    
    # تست سریع همه ماژول‌ها
    modules_status = {}
    
    # ماژول‌های نیمه‌فعال
    semi_active_modules = [
        ('AnomalyDetectionSystem', 'utils.anomaly_detection_system'),
        ('FederatedLearningServer', 'utils.federated_learning_system'),
        ('GeneticStrategyEvolution', 'utils.genetic_strategy_evolution')
    ]
    
    # ماژول‌های غیرفعال
    inactive_modules = [
        ('AdaptiveMarginControl', 'utils.adaptive_margin_control'),
        ('AdvancedRewardSystem', 'utils.advanced_reward_system'),
        ('AutoMarketMaker', 'utils.auto_market_maker'),
        ('HierarchicalRL', 'utils.hierarchical_rl'),
        ('ZeroShotLearning', 'utils.zero_shot_learning')
    ]
    
    all_modules = semi_active_modules + inactive_modules
    
    for module_name, module_path in all_modules:
        try:
            exec(f'from {module_path} import {module_name}')
            modules_status[module_name] = '✅ عملیاتی'
            operational_modules += 1
        except Exception as e:
            modules_status[module_name] = f'❌ خطا: {str(e)[:50]}...'
    
    # نمایش گزارش
    print(f'📈 آمار کلی:')
    print(f'   - تعداد کل ماژول‌ها: {total_modules}')
    print(f'   - ماژول‌های عملیاتی: {operational_modules}')
    print(f'   - درصد عملیاتی: {(operational_modules/total_modules)*100:.1f}%')
    
    print(f'\n📋 جزئیات ماژول‌ها:')
    for module_name, status in modules_status.items():
        print(f'   - {module_name}: {status}')
    
    # نتیجه‌گیری
    if operational_modules == total_modules:
        print(f'\n🎉 نتیجه: همه ماژول‌ها با موفقیت عملیاتی هستند!')
        return True
    else:
        print(f'\n⚠️ نتیجه: {total_modules - operational_modules} ماژول نیاز به رفع مشکل دارند')
        return False

def main():
    """اجرای تست جامع"""
    print('🔍 تست جامع ماژول‌های نیمه‌فعال و غیرفعال')
    print('=' * 60)
    print(f'⏰ زمان شروع: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    try:
        # تست ماژول‌های نیمه‌فعال
        test_semi_active_modules()
        
        # تست ماژول‌های غیرفعال
        test_inactive_modules()
        
        # تست یکپارچگی
        test_main_system_integration()
        
        # گزارش نهایی
        success = generate_final_report()
        
        print(f'\n⏰ زمان پایان: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        
        if success:
            print('🎯 تست با موفقیت کامل شد!')
            return 0
        else:
            print('⚠️ تست با مشکلاتی مواجه شد')
            return 1
            
    except Exception as e:
        print(f'❌ خطای کلی در تست: {e}')
        return 1

if __name__ == "__main__":
    sys.exit(main()) 