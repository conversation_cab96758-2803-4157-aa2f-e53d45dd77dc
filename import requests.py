import requests

proxies = {
    "http": "http://127.0.0.1:10809",
    "https": "http://127.0.0.1:10809"
}

url = "https://api.groq.com/openai/v1/chat/completions"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer ********************************************************"
}
payload = {
    "model": "meta-llama/llama-4-scout-17b-16e-instruct",
    "messages": [
        {
            "role": "user",
            "content": "Explain the importance of fast language models"
        }
    ]
}

print("🔁 ارسال درخواست به Groq...")
response = requests.post(url, headers=headers, json=payload, proxies=proxies)

if response.status_code == 200:
    print("✅ پاسخ مدل:")
    print(response.json()['choices'][0]['message']['content'])
else:
    print(f"❌ خطا [{response.status_code}]:")
    print(response.text)
