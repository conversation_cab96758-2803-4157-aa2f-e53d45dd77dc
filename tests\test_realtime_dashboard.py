#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تست‌های Real-time Dashboard System
"""

import pytest
import asyncio
import json
from fastapi.testclient import TestClient
from fastapi.websockets import WebSocket
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.realtime_dashboard import (
    RealTimeDashboard,
    ConnectionManager,
    DataAggregator,
    ReportGenerator,
    AlertSeverity,
    Alert,
    DashboardConfig
)


class TestConnectionManager:
    """تست مدیریت اتصالات"""
    
    def test_initialization(self):
        """تست اولیه‌سازی"""
        manager = ConnectionManager()
        assert len(manager.active_connections) == 0
        assert len(manager.user_preferences) == 0
    
    def test_disconnect(self):
        """تست قطع اتصال"""
        manager = ConnectionManager()
        client_id = "test_client"
        
        # Simulate connection
        manager.active_connections[client_id] = None
        
        # Test disconnect
        manager.disconnect(client_id)
        assert client_id not in manager.active_connections
    
    def test_get_connected_clients(self):
        """تست دریافت کلاینت‌های متصل"""
        manager = ConnectionManager()
        
        # Add some clients
        manager.active_connections["client1"] = None
        manager.active_connections["client2"] = None
        
        clients = manager.get_connected_clients()
        assert len(clients) == 2
        assert "client1" in clients
        assert "client2" in clients


class TestDataAggregator:
    """تست تجمیع داده‌ها"""
    
    def test_initialization(self):
        """تست اولیه‌سازی"""
        aggregator = DataAggregator()
        
        assert aggregator.alpha_beta_engine is not None
        assert aggregator.drawdown_controller is not None
        assert aggregator.sentiment_analyzer is not None
        assert aggregator.hft_system is not None
        assert aggregator.exchange_router is not None
        assert aggregator.reward_system is not None
        
        assert len(aggregator.portfolio_data) == 0
        assert len(aggregator.market_data) == 0
        assert len(aggregator.alerts) == 0
    
    def test_simulate_portfolio_value(self):
        """تست شبیه‌سازی مقدار portfolio"""
        aggregator = DataAggregator()
        
        # First call should return initial value
        value1 = aggregator._simulate_portfolio_value()
        assert value1 == 1000000.0
        
        # Add some data
        aggregator.portfolio_data.append({
            'value': value1,
            'return': 0.0
        })
        
        # Second call should return different value
        value2 = aggregator._simulate_portfolio_value()
        assert isinstance(value2, float)
        assert value2 > 0
    
    def test_simulate_market_data(self):
        """تست شبیه‌سازی داده‌های بازار"""
        aggregator = DataAggregator()
        market_data = aggregator._simulate_market_data()
        
        assert 'return' in market_data
        assert 'volatility' in market_data
        assert 'volume' in market_data
        assert 'bid_ask_spread' in market_data
        assert 'correlation' in market_data
        
        assert isinstance(market_data['return'], float)
        assert isinstance(market_data['volatility'], float)
        assert market_data['volatility'] > 0
    
    def test_get_hft_metrics(self):
        """تست دریافت معیارهای HFT"""
        aggregator = DataAggregator()
        metrics = aggregator._get_hft_metrics()
        
        assert 'latency_ms' in metrics
        assert 'order_fill_rate' in metrics
        assert 'market_impact' in metrics
        assert 'profit_factor' in metrics
        assert 'active_orders' in metrics
        assert 'executed_trades' in metrics
        
        assert metrics['latency_ms'] > 0
        assert 0 <= metrics['order_fill_rate'] <= 1
        assert metrics['active_orders'] >= 0
        assert metrics['executed_trades'] >= 0
    
    def test_get_routing_status(self):
        """تست دریافت وضعیت routing"""
        aggregator = DataAggregator()
        status = aggregator._get_routing_status()
        
        assert 'active_exchanges' in status
        assert 'total_volume' in status
        assert 'best_execution_rate' in status
        assert 'arbitrage_opportunities' in status
        assert 'exchange_status' in status
        
        assert status['active_exchanges'] > 0
        assert status['total_volume'] > 0
        assert 0 <= status['best_execution_rate'] <= 1
        assert status['arbitrage_opportunities'] >= 0
        assert isinstance(status['exchange_status'], dict)
    
    def test_calculate_volatility(self):
        """تست محاسبه نوسانات"""
        aggregator = DataAggregator()
        
        # No data case
        volatility = aggregator._calculate_volatility()
        assert volatility == 0.2
        
        # With data
        for i in range(25):
            aggregator.portfolio_data.append({
                'return': 0.01 if i % 2 == 0 else -0.01
            })
        
        volatility = aggregator._calculate_volatility()
        assert isinstance(volatility, float)
        assert volatility > 0
    
    def test_calculate_sharpe_ratio(self):
        """تست محاسبه نسبت شارپ"""
        aggregator = DataAggregator()
        
        # No data case
        sharpe = aggregator._calculate_sharpe_ratio()
        assert sharpe == 0.0
        
        # With positive returns
        for i in range(15):
            aggregator.portfolio_data.append({
                'return': 0.001  # Positive return
            })
        
        sharpe = aggregator._calculate_sharpe_ratio()
        assert isinstance(sharpe, float)
    
    def test_add_alert(self):
        """تست افزودن هشدار"""
        aggregator = DataAggregator()
        
        alert = aggregator.add_alert(
            "Test Alert",
            "This is a test message",
            AlertSeverity.MEDIUM,
            "Test Source"
        )
        
        assert isinstance(alert, Alert)
        assert alert.title == "Test Alert"
        assert alert.message == "This is a test message"
        assert alert.severity == AlertSeverity.MEDIUM
        assert alert.source == "Test Source"
        assert not alert.acknowledged
        
        assert len(aggregator.alerts) == 1
        assert aggregator.alerts[0] == alert
    
    @pytest.mark.asyncio
    async def test_collect_real_time_data(self):
        """تست جمع‌آوری داده‌های زمان واقعی"""
        aggregator = DataAggregator()
        
        data = await aggregator.collect_real_time_data()
        
        assert 'timestamp' in data
        assert 'portfolio' in data
        assert 'drawdown' in data
        assert 'alpha_beta' in data
        assert 'sentiment' in data
        assert 'hft' in data
        assert 'routing' in data
        assert 'market' in data
        assert 'alerts' in data
        
        # Check portfolio data
        portfolio = data['portfolio']
        assert 'value' in portfolio
        assert 'daily_return' in portfolio
        assert 'total_return' in portfolio
        assert 'volatility' in portfolio
        assert 'sharpe_ratio' in portfolio
        
        # Check drawdown data
        drawdown = data['drawdown']
        assert 'current' in drawdown
        assert 'max' in drawdown
        assert 'duration' in drawdown
        assert 'brake_active' in drawdown
        assert 'recommended_action' in drawdown


class TestReportGenerator:
    """تست تولید گزارش"""
    
    def test_initialization(self):
        """تست اولیه‌سازی"""
        aggregator = DataAggregator()
        generator = ReportGenerator(aggregator)
        
        assert generator.data_aggregator == aggregator
        assert generator.reports_dir == "reports"
        assert os.path.exists(generator.reports_dir)
    
    @pytest.mark.asyncio
    async def test_generate_daily_report_insufficient_data(self):
        """تست تولید گزارش روزانه با داده ناکافی"""
        aggregator = DataAggregator()
        generator = ReportGenerator(aggregator)
        
        report = await generator.generate_daily_report()
        assert "error" in report
        assert "Insufficient data" in report["error"]
    
    @pytest.mark.asyncio
    async def test_generate_daily_report_with_data(self):
        """تست تولید گزارش روزانه با داده"""
        aggregator = DataAggregator()
        generator = ReportGenerator(aggregator)
        
        # Add some sample data
        from datetime import datetime
        current_time = datetime.now()
        
        for i in range(5):
            aggregator.portfolio_data.append({
                'timestamp': current_time,
                'value': 1000000 + i * 1000,
                'return': 0.001
            })
        
        report = await generator.generate_daily_report()
        
        assert 'date' in report
        assert 'summary' in report
        assert 'performance' in report
        assert 'risk_metrics' in report
        
        # Check summary
        summary = report['summary']
        assert 'daily_return' in summary
        assert 'daily_volatility' in summary
        assert 'total_trades' in summary
        assert 'portfolio_value' in summary
        assert 'alerts_count' in summary


class TestDashboardConfig:
    """تست تنظیمات داشبورد"""
    
    def test_default_config(self):
        """تست تنظیمات پیش‌فرض"""
        config = DashboardConfig()
        
        assert config.theme == DashboardConfig().theme
        assert config.refresh_interval == 1000
        assert config.max_data_points == 1000
        assert config.enable_notifications == True
        assert config.enable_sound_alerts == False
        assert config.auto_save_reports == True
        assert config.report_interval == 3600
        
        # Widget settings
        assert config.show_performance_chart == True
        assert config.show_drawdown_chart == True
        assert config.show_alpha_beta_chart == True
        assert config.show_sentiment_gauge == True
        assert config.show_hft_metrics == True
        assert config.show_exchange_routing == True
        
        # Alert settings
        assert config.max_alerts == 100
        assert config.alert_retention_hours == 24


class TestRealTimeDashboard:
    """تست داشبورد اصلی"""
    
    def test_initialization(self):
        """تست اولیه‌سازی"""
        dashboard = RealTimeDashboard()
        
        assert dashboard.app is not None
        assert dashboard.connection_manager is not None
        assert dashboard.data_aggregator is not None
        assert dashboard.report_generator is not None
        assert dashboard.config is not None
    
    def test_get_dashboard_html(self):
        """تست تولید HTML داشبورد"""
        dashboard = RealTimeDashboard()
        html = dashboard.get_dashboard_html()
        
        assert isinstance(html, str)
        assert "<!DOCTYPE html>" in html
        assert "Trading Dashboard" in html
        assert "portfolio-value" in html
        assert "current-drawdown" in html
        assert "alpha-beta" in html
        assert "market-sentiment" in html
    
    @pytest.mark.asyncio
    async def test_check_and_generate_alerts(self):
        """تست بررسی و تولید هشدارها"""
        dashboard = RealTimeDashboard()
        
        # Test data with high drawdown
        data = {
            'drawdown': {
                'current': -0.08,  # 8% drawdown
                'max': -0.10
            },
            'portfolio': {
                'daily_return': -0.04  # 4% daily loss
            },
            'hft': {
                'latency_ms': 6.0  # High latency
            }
        }
        
        initial_alerts = len(dashboard.data_aggregator.alerts)
        
        await dashboard.check_and_generate_alerts(data)
        
        # Should have generated alerts
        assert len(dashboard.data_aggregator.alerts) > initial_alerts
        
        # Check alert types
        alert_titles = [alert.title for alert in dashboard.data_aggregator.alerts]
        assert any("Drawdown" in title for title in alert_titles)
        assert any("Daily Loss" in title for title in alert_titles)
        assert any("Latency" in title for title in alert_titles)
    
    @pytest.mark.asyncio
    async def test_handle_alert_acknowledge(self):
        """تست تایید هشدار"""
        dashboard = RealTimeDashboard()
        
        # Add an alert
        alert = dashboard.data_aggregator.add_alert(
            "Test Alert",
            "Test Message",
            AlertSeverity.LOW,
            "Test"
        )
        
        assert not alert.acknowledged
        
        # Acknowledge the alert
        await dashboard.handle_alert_acknowledge(alert.id)
        
        assert alert.acknowledged
        assert alert.dismiss_time is not None


class TestAPIEndpoints:
    """تست endpoints API"""
    
    def setup_method(self):
        """راه‌اندازی برای هر تست"""
        self.dashboard = RealTimeDashboard()
        self.client = TestClient(self.dashboard.app)
    
    def test_health_endpoint(self):
        """تست endpoint سلامت"""
        response = self.client.get("/api/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "connected_clients" in data
        assert "active_alerts" in data
        assert data["status"] == "healthy"
    
    def test_config_endpoints(self):
        """تست endpoints تنظیمات"""
        # Get config
        response = self.client.get("/api/config")
        assert response.status_code == 200
        
        config_data = response.json()
        assert "theme" in config_data
        assert "refresh_interval" in config_data
        
        # Update config
        new_config = {"refresh_interval": 2000}
        response = self.client.post("/api/config", json=new_config)
        assert response.status_code == 200
        
        result = response.json()
        assert result["status"] == "success"
    
    def test_alerts_endpoints(self):
        """تست endpoints هشدارها"""
        # Add an alert first
        alert = self.dashboard.data_aggregator.add_alert(
            "Test Alert",
            "Test Message",
            AlertSeverity.MEDIUM,
            "Test"
        )
        
        # Get alerts
        response = self.client.get("/api/alerts")
        assert response.status_code == 200
        
        alerts = response.json()
        assert len(alerts) >= 1
        assert any(a["id"] == alert.id for a in alerts)
        
        # Acknowledge alert
        response = self.client.post(f"/api/alerts/{alert.id}/acknowledge")
        assert response.status_code == 200
        
        result = response.json()
        assert result["status"] == "success"
    
    def test_reports_endpoints(self):
        """تست endpoints گزارش‌ها"""
        # Daily report
        response = self.client.get("/api/reports/daily")
        assert response.status_code == 200
        
        # Weekly report
        response = self.client.get("/api/reports/weekly")
        assert response.status_code == 200
        
        # Monthly report
        response = self.client.get("/api/reports/monthly")
        assert response.status_code == 200
    
    def test_data_endpoint(self):
        """تست endpoint داده‌ها"""
        response = self.client.get("/api/data")
        assert response.status_code == 200
        
        data = response.json()
        assert "timestamp" in data
        assert "portfolio" in data
        assert "drawdown" in data
        assert "alpha_beta" in data
        assert "sentiment" in data
        assert "hft" in data
        assert "routing" in data
        assert "market" in data
        assert "alerts" in data
    
    def test_dashboard_html_endpoint(self):
        """تست endpoint HTML داشبورد"""
        response = self.client.get("/")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        
        content = response.text
        assert "Trading Dashboard" in content
        assert "portfolio-value" in content


class TestIntegration:
    """تست‌های یکپارچگی"""
    
    @pytest.mark.asyncio
    async def test_full_dashboard_workflow(self):
        """تست جریان کامل داشبورد"""
        dashboard = RealTimeDashboard()
        
        # Simulate data collection
        data = await dashboard.data_aggregator.collect_real_time_data()
        assert data is not None
        
        # Check alert generation
        await dashboard.check_and_generate_alerts(data)
        
        # Generate report
        report = await dashboard.report_generator.generate_daily_report()
        assert report is not None
        
        # Test API endpoints
        client = TestClient(dashboard.app)
        
        # Health check
        response = client.get("/api/health")
        assert response.status_code == 200
        
        # Get current data
        response = client.get("/api/data")
        assert response.status_code == 200
        
        # Get alerts
        response = client.get("/api/alerts")
        assert response.status_code == 200
    
    def test_dashboard_performance(self):
        """تست عملکرد داشبورد"""
        dashboard = RealTimeDashboard()
        
        # Add many data points
        import time
        start_time = time.time()
        
        for i in range(100):
            dashboard.data_aggregator.add_alert(
                f"Alert {i}",
                f"Message {i}",
                AlertSeverity.LOW,
                "Performance Test"
            )
        
        end_time = time.time()
        
        # Should complete quickly
        assert end_time - start_time < 1.0  # Less than 1 second
        
        # Check data integrity
        assert len(dashboard.data_aggregator.alerts) == 100
    
    def test_concurrent_connections(self):
        """تست اتصالات همزمان"""
        manager = ConnectionManager()
        
        # Simulate multiple connections
        for i in range(10):
            client_id = f"client_{i}"
            manager.active_connections[client_id] = None
        
        assert len(manager.get_connected_clients()) == 10
        
        # Disconnect some clients
        for i in range(5):
            manager.disconnect(f"client_{i}")
        
        assert len(manager.get_connected_clients()) == 5


if __name__ == "__main__":
    pytest.main([__file__]) 