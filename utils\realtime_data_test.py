#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ Real-time Data Processing Test
"""

import os
import sys
import time
import asyncio

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_realtime_data_processing():
    """تست سیستم پردازش داده‌های real-time"""
    print("⚡ Testing Real-time Data Processing System...")
    
    try:
        # Test 1: Import
        print("1️⃣ Testing import...")
        from core.realtime_data_processing import (
            RealTimeDataProcessor,
            EventHandler,
            MarketDataHandler,
            RealTimeEvent,
            EventType,
            DataSource,
            ProcessingStatus,
            DataStreamConfig,
            get_realtime_processor,
            create_market_data_stream
        )
        print("   ✓ Import successful")
        
        # Test 2: Create processor
        print("2️⃣ Testing processor creation...")
        processor = get_realtime_processor()
        print("   ✓ Processor created")
        
        # Test 3: Create market data handler
        print("3️⃣ Testing market data handler...")
        market_handler = MarketDataHandler("test_handler")
        processor.add_handler(market_handler)
        print("   ✓ Market data handler added")
        
        # Test 4: Create data stream config
        print("4️⃣ Testing data stream configuration...")
        config = create_market_data_stream(
            "test_stream",
            "Test Market Stream",
            "wss://test.example.com/ws",
            ["EURUSD", "GBPUSD"]
        )
        print(f"   ✓ Stream config created: {config.name}")
        
        # Test 5: Add stream
        print("5️⃣ Testing stream addition...")
        stream_id = processor.add_stream(config)
        print(f"   ✓ Stream added: {stream_id}")
        
        # Test 6: Create test event
        print("6️⃣ Testing event creation...")
        test_event = RealTimeEvent(
            event_id="test_001",
            event_type=EventType.MARKET_DATA,
            timestamp=time.time(),
            source=DataSource.SIMULATION,
            data={
                "symbol": "EURUSD",
                "exchange_id": "test_exchange",
                "data_type": "tick",
                "bid": "1.0950",
                "ask": "1.0952",
                "timestamp": time.time()
            }
        )
        print(f"   ✓ Test event created: {test_event.event_id}")
        
        # Test 7: Publish event
        print("7️⃣ Testing event publishing...")
        processor.publish_event(test_event)
        print("   ✓ Event published")
        
        # Test 8: Get statistics
        print("8️⃣ Testing statistics...")
        stats = processor.get_global_statistics()
        print(f"   ✓ Total events: {stats['total_events_processed']}")
        print(f"   ✓ Active streams: {stats['active_streams']}")
        print(f"   ✓ Active handlers: {stats['active_handlers']}")
        
        # Test 9: Get handler metrics
        print("9️⃣ Testing handler metrics...")
        metrics = processor.get_handler_metrics("test_handler")
        if metrics:
            print(f"   ✓ Events processed: {metrics.events_processed}")
            print(f"   ✓ Error count: {metrics.error_count}")
        
        # Test 10: Get stream status
        print("🔟 Testing stream status...")
        stream_status = processor.get_stream_status(stream_id)
        if stream_status:
            print(f"   ✓ Stream status: {stream_status['status']}")
            print(f"   ✓ Messages received: {stream_status['messages_received']}")
        
        print("\n🎉 All Real-time Data Processing tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_market_data_handler():
    """تست market data handler"""
    print("\n📊 Testing Market Data Handler...")
    
    try:
        from core.realtime_data_processing import MarketDataHandler, RealTimeEvent, EventType, DataSource
        
        # Create handler
        handler = MarketDataHandler("test_market_handler")
        
        # Create test event
        event = RealTimeEvent(
            event_id="market_001",
            event_type=EventType.MARKET_DATA,
            timestamp=time.time(),
            source=DataSource.SIMULATION,
            data={
                "symbol": "GBPUSD",
                "exchange_id": "test_exchange",
                "data_type": "tick",
                "bid": "1.2750",
                "ask": "1.2752",
                "last": "1.2751",
                "volume": "1000000",
                "timestamp": time.time()
            }
        )
        
        # Process event
        result = await handler.handle_event(event)
        print(f"   ✓ Event processed: {result}")
        
        # Check metrics
        print(f"   ✓ Events processed: {handler.metrics.events_processed}")
        print(f"   ✓ Processing time avg: {handler.metrics.processing_time_avg:.4f}s")
        
        # Get latest data
        latest_data = handler.get_latest_data("GBPUSD", "test_exchange")
        if latest_data:
            print(f"   ✓ Latest data: {latest_data.symbol} {latest_data.bid}/{latest_data.ask}")
        
        print("✅ Market Data Handler test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Market Data Handler test failed: {e}")
        return False

async def test_event_types():
    """تست انواع رویدادها"""
    print("\n🎯 Testing Event Types...")
    
    try:
        from core.realtime_data_processing import RealTimeEvent, EventType, DataSource
        
        # Test different event types
        event_types = [
            EventType.MARKET_DATA,
            EventType.ORDER_UPDATE,
            EventType.TRADE_UPDATE,
            EventType.NEWS_EVENT,
            EventType.ALERT
        ]
        
        for event_type in event_types:
            event = RealTimeEvent(
                event_id=f"test_{event_type.value}",
                event_type=event_type,
                timestamp=time.time(),
                source=DataSource.SIMULATION,
                data={"test": "data"}
            )
            print(f"   ✓ {event_type.value} event created: {event.event_id}")
        
        print("✅ Event Types test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Event Types test failed: {e}")
        return False

async def main():
    """تابع اصلی تست"""
    print("🚀 Starting Real-time Data Processing Tests...\n")
    
    # Run tests
    tests = [
        test_realtime_data_processing(),
        test_market_data_handler(),
        test_event_types()
    ]
    
    results = await asyncio.gather(*tests, return_exceptions=True)
    
    # Summary
    print("\n📊 Test Results Summary:")
    print("=" * 30)
    
    passed = 0
    total = len(results)
    
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"❌ Test {i+1} failed: {result}")
        elif result:
            print(f"✅ Test {i+1} passed")
            passed += 1
        else:
            print(f"❌ Test {i+1} failed")
    
    print(f"\n📈 Success rate: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All Real-time Data Processing tests passed!")
        print("✅ Real-time Data Processing System is ready!")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    asyncio.run(main()) 