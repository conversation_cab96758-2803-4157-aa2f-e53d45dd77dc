"""
🔍 بررسی فایل‌های Colab
"""

import os

def check_colab_files():
    """بررسی فایل‌های موجود در Colab"""
    print("🔍 CHECKING COLAB FILES")
    print("=" * 40)
    
    # بررسی فایل‌های /content/
    print("📁 Files in /content/:")
    try:
        content_files = os.listdir('/content/')
        for file in sorted(content_files):
            file_path = f'/content/{file}'
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"   📄 {file} ({size} bytes)")
            else:
                print(f"   📁 {file}/ (directory)")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print()
    
    # بررسی فایل‌های مربوط به colab
    print("🧠 Colab-related files:")
    colab_files = []
    try:
        all_files = os.listdir('/content/')
        colab_files = [f for f in all_files if 'colab' in f.lower()]
        
        if colab_files:
            for file in colab_files:
                file_path = f'/content/{file}'
                size = os.path.getsize(file_path)
                print(f"   ✅ {file} ({size} bytes)")
        else:
            print("   ❌ No colab files found")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print()
    
    # بررسی مخصوص colab_brain_trainer.py
    print("🎯 Checking colab_brain_trainer.py:")
    
    possible_paths = [
        '/content/colab_brain_trainer.py',
        'colab_brain_trainer.py',
        './colab_brain_trainer.py'
    ]
    
    found = False
    for path in possible_paths:
        try:
            if os.path.exists(path):
                size = os.path.getsize(path)
                print(f"   ✅ Found at: {path} ({size} bytes)")
                
                # تست بارگذاری
                try:
                    with open(path, 'r') as f:
                        content = f.read()
                    if 'ColabModelTrainer' in content:
                        print(f"   ✅ File content looks correct")
                    else:
                        print(f"   ⚠️ File content might be wrong")
                except Exception as e:
                    print(f"   ❌ Can't read file: {e}")
                
                found = True
                break
        except Exception as e:
            continue
    
    if not found:
        print("   ❌ colab_brain_trainer.py not found!")
        print("   💡 Please upload the file again")
    
    print()
    
    # راهنمای آپلود
    if not found:
        print("📋 HOW TO UPLOAD:")
        print("1. Run this code:")
        print("   from google.colab import files")
        print("   uploaded = files.upload()")
        print("2. Select colab_brain_trainer.py from your computer")
        print("3. Wait for upload to complete")
        print("4. Run check_colab_files() again")
    
    return found

def test_brain_import():
    """تست وارد کردن مغز متفکر"""
    print("\n🧠 TESTING BRAIN IMPORT")
    print("=" * 30)
    
    possible_paths = [
        '/content/colab_brain_trainer.py',
        'colab_brain_trainer.py',
        './colab_brain_trainer.py'
    ]
    
    for path in possible_paths:
        try:
            print(f"🔄 Trying to import from: {path}")
            exec(open(path).read())
            print(f"✅ Successfully imported from: {path}")
            
            # تست ایجاد trainer
            try:
                trainer = ColabModelTrainer()
                print("✅ ColabModelTrainer created successfully")
                print(f"   GPU: {trainer.brain.system_info.gpu_name}")
                print(f"   Available models: {len(trainer.heavy_models)}")
                return True
            except Exception as e:
                print(f"❌ Error creating trainer: {e}")
                return False
                
        except FileNotFoundError:
            print(f"❌ File not found: {path}")
            continue
        except Exception as e:
            print(f"❌ Import error from {path}: {e}")
            continue
    
    print("❌ Could not import brain trainer from any path")
    return False

def upload_file_helper():
    """کمک برای آپلود فایل"""
    print("📁 UPLOAD FILE HELPER")
    print("=" * 25)
    
    try:
        from google.colab import files
        print("🔄 Starting file upload...")
        print("📋 Please select colab_brain_trainer.py from your computer")
        uploaded = files.upload()
        
        if uploaded:
            for filename in uploaded.keys():
                print(f"✅ Uploaded: {filename}")
                size = len(uploaded[filename])
                print(f"   Size: {size} bytes")
                
                # بررسی محتوا
                content = uploaded[filename].decode('utf-8')
                if 'ColabModelTrainer' in content:
                    print("✅ File content looks correct")
                else:
                    print("⚠️ File might not be the correct brain trainer")
        
        return True
        
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        return False

# اجرای خودکار
if __name__ == "__main__":
    found = check_colab_files()
    
    if found:
        print("\n🎯 File found! Testing import...")
        success = test_brain_import()
        
        if success:
            print("\n🎉 ALL GOOD! Ready to start training!")
            print("Run: run_colab_brain_training()")
        else:
            print("\n❌ Import failed. File might be corrupted.")
    else:
        print("\n📁 File not found. Use upload_file_helper() to upload.")
