"""Fix Cursor Proxy and Internet Connection

اسکریپت برای حل مشکل پروکسی و اتصال اینترنت در Cursor
"""

import os
import sys
import subprocess
import json
import time
import winreg
import requests
from pathlib import Path


def set_system_proxy(proxy_server, proxy_port):
    """تنظیم پروکسی سیستم ویندوز"""
    try:
        # باز کردن registry key
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
            0,
            winreg.KEY_ALL_ACCESS
        )
        
        # تنظیم پروکسی
        winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 1)
        winreg.SetValueEx(key, "ProxyServer", 0, winreg.REG_SZ, f"{proxy_server}:{proxy_port}")
        
        # بستن key
        winreg.<PERSON><PERSON><PERSON>(key)
        
        print(f"✅ System proxy set to {proxy_server}:{proxy_port}")
        return True
        
    except Exception as e:
        print(f"❌ Error setting system proxy: {e}")
        return False


def disable_system_proxy():
    """غیرفعال کردن پروکسی سیستم"""
    try:
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
            0,
            winreg.KEY_ALL_ACCESS
        )
        
        winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 0)
        winreg.CloseKey(key)
        
        print("✅ System proxy disabled")
        return True
        
    except Exception as e:
        print(f"❌ Error disabling system proxy: {e}")
        return False


def set_environment_variables():
    """تنظیم متغیرهای محیطی برای پروکسی"""
    proxy_config = load_proxy_config()
    
    if proxy_config:
        http_proxy = f"http://127.0.0.1:{proxy_config['http_port']}"
        
        # تنظیم متغیرهای محیطی
        env_vars = {
            'HTTP_PROXY': http_proxy,
            'HTTPS_PROXY': http_proxy,
            'http_proxy': http_proxy,
            'https_proxy': http_proxy,
            'NO_PROXY': 'localhost,127.0.0.1',
            'no_proxy': 'localhost,127.0.0.1'
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            
        print("✅ Environment variables set")
        return True
        
    return False


def load_proxy_config():
    """بارگذاری تنظیمات پروکسی"""
    try:
        with open("PROXY.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        return {
            'socks_port': config['inbounds'][0]['port'],  # 10808
            'http_port': config['inbounds'][1]['port']    # 10809
        }
    except Exception as e:
        print(f"❌ Error loading proxy config: {e}")
        return None


def test_internet_connection():
    """تست اتصال اینترنت"""
    test_urls = [
        "http://www.google.com",
        "http://github.com",
        "http://httpbin.org/ip"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ Successfully connected to {url}")
                return True
        except Exception as e:
            print(f"❌ Failed to connect to {url}: {e}")
            continue
            
    return False


def restart_cursor():
    """راه‌اندازی مجدد Cursor"""
    try:
        # بستن Cursor
        subprocess.run(['taskkill', '/f', '/im', 'Cursor.exe'], 
                      capture_output=True, text=True)
        time.sleep(2)
        
        # پیدا کردن مسیر Cursor
        cursor_paths = [
            os.path.expanduser(r"~\AppData\Local\Programs\cursor\Cursor.exe"),
            r"C:\Users\<USER>\AppData\Local\Programs\cursor\Cursor.exe",
            r"C:\Program Files\Cursor\Cursor.exe"
        ]
        
        for path in cursor_paths:
            expanded_path = os.path.expandvars(path)
            if os.path.exists(expanded_path):
                print(f"🔄 Restarting Cursor from {expanded_path}")
                subprocess.Popen([expanded_path])
                return True
                
        print("❌ Cursor executable not found")
        return False
        
    except Exception as e:
        print(f"❌ Error restarting Cursor: {e}")
        return False


def flush_dns():
    """پاک کردن DNS cache"""
    try:
        subprocess.run(['ipconfig', '/flushdns'], capture_output=True, text=True)
        print("✅ DNS cache flushed")
        return True
    except Exception as e:
        print(f"❌ Error flushing DNS: {e}")
        return False


def reset_winsock():
    """ریست کردن Winsock"""
    try:
        subprocess.run(['netsh', 'winsock', 'reset'], capture_output=True, text=True)
        print("✅ Winsock reset (restart required)")
        return True
    except Exception as e:
        print(f"❌ Error resetting Winsock: {e}")
        return False


def main():
    """تابع اصلی برای حل مشکل اتصال"""
    print("🔧 Fixing Cursor Internet Connection...")
    print("=" * 50)
    
    # 1. بارگذاری تنظیمات پروکسی
    proxy_config = load_proxy_config()
    if not proxy_config:
        print("❌ Cannot load proxy configuration")
        return
        
    # 2. تنظیم متغیرهای محیطی
    set_environment_variables()
    
    # 3. تست اتصال مستقیم
    print("\n📡 Testing direct connection...")
    if test_internet_connection():
        print("✅ Direct connection works")
    else:
        print("❌ Direct connection failed")
        
        # 4. تنظیم پروکسی سیستم
        print("\n🔧 Setting system proxy...")
        set_system_proxy("127.0.0.1", proxy_config['http_port'])
        
        # 5. تست اتصال با پروکسی
        print("\n📡 Testing proxy connection...")
        if not test_internet_connection():
            print("❌ Proxy connection also failed")
            
            # 6. اقدامات اضافی
            print("\n🔧 Trying additional fixes...")
            flush_dns()
            
            # پیشنهاد ریست Winsock (نیاز به restart)
            print("\n⚠️  Consider running 'netsh winsock reset' and restarting")
    
    # 7. راه‌اندازی مجدد Cursor
    print("\n🔄 Restarting Cursor...")
    restart_cursor()
    
    print("\n✅ Process completed!")
    print("💡 Tips:")
    print("   - Make sure your VPN/Proxy is running")
    print("   - Try disabling antivirus temporarily")
    print("   - Check Windows Firewall settings")
    print("   - Restart computer if issues persist")


if __name__ == "__main__":
    # اجرا با دسترسی Administrator
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print("⚠️  Running without admin privileges")
            print("   Some features may not work properly")
            
        main()
        
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        
    input("\nPress Enter to exit...") 