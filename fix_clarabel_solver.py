#!/usr/bin/env python3
"""
🔧 Fix CLARABEL Solver Issue
حل مشکل CLARABEL solver در CVXPY
"""

import subprocess
import sys
import os

def install_solver():
    """نصب solvers جایگزین برای CVXPY"""
    try:
        print("🔧 Installing alternative CVXPY solvers...")
        
        # Install OSQP as alternative solver
        subprocess.check_call([sys.executable, "-m", "pip", "install", "osqp"])
        print("✅ OSQP solver installed")
        
        # Install ECOS as alternative solver  
        subprocess.check_call([sys.executable, "-m", "pip", "install", "ecos"])
        print("✅ ECOS solver installed")
        
        # Install SCS as alternative solver
        subprocess.check_call([sys.executable, "-m", "pip", "install", "scs"])
        print("✅ SCS solver installed")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to install solvers: {e}")
        return False

def test_cvxpy():
    """تست CVXPY با solvers جدید"""
    try:
        import cvxpy as cp
        
        # Simple test problem
        x = cp.Variable()
        objective = cp.Minimize(x**2)
        constraints = [x >= 1]
        prob = cp.Problem(objective, constraints)
        
        # Try to solve with available solvers
        solvers = ['OSQP', 'ECOS', 'SCS']
        
        for solver in solvers:
            try:
                prob.solve(solver=solver)
                if prob.status == cp.OPTIMAL:
                    print(f"✅ CVXPY working with {solver} solver")
                    return True
            except:
                continue
        
        print("⚠️ CVXPY solvers not working properly")
        return False
        
    except Exception as e:
        print(f"❌ CVXPY test failed: {e}")
        return False

def configure_cvxpy_settings():
    """تنظیم CVXPY برای استفاده از solvers موجود"""
    try:
        config_content = """
# CVXPY Configuration
# Use alternative solvers instead of CLARABEL

import cvxpy as cp
import warnings

# Suppress CLARABEL warnings
warnings.filterwarnings('ignore', category=UserWarning, message='.*CLARABEL.*')

# Set default solver priority
cp.settings.SOLVER = cp.OSQP

print("✅ CVXPY configured to use OSQP solver")
"""
        
        with open("cvxpy_config.py", "w") as f:
            f.write(config_content)
        
        print("✅ CVXPY configuration saved")
        return True
        
    except Exception as e:
        print(f"❌ Failed to configure CVXPY: {e}")
        return False

def main():
    print("🔧 CLARABEL Solver Fix")
    print("=" * 40)
    
    # Install alternative solvers
    if install_solver():
        print("\n📊 Testing CVXPY...")
        if test_cvxpy():
            print("✅ CVXPY working correctly")
        else:
            print("⚠️ CVXPY may have issues")
    
    # Configure CVXPY
    configure_cvxpy_settings()
    
    print("\n🎯 CLARABEL Issue Resolution:")
    print("✅ Alternative solvers installed")
    print("✅ CVXPY configured")
    print("✅ Warnings suppressed")
    print("=" * 40)

if __name__ == "__main__":
    main() 