import pytest
from utils.portfolio_visualizer import PortfolioVisualizer


from typing import List, Dict, Any

@pytest.fixture
def sample_trades() -> List[Dict[str, Any]]:
    return [
        {"symbol": "AAPL", "profit": 100},
        {"symbol": "AAPL", "profit": -50},
        {"symbol": "AAPL", "profit": 200},
        {"symbol": "AAPL", "profit": -30},
        {"symbol": "AAPL", "profit": 70},
    ]


def test_plot_pnl_curve(sample_trades: List[Dict[str, Any]]):
    vis = PortfolioVisualizer(sample_trades)
    vis.plot_pnl_curve()  # باید بدون خطا اجرا شود


def test_plot_trade_distribution(sample_trades: List[Dict[str, Any]]):
    vis = PortfolioVisualizer(sample_trades)
    vis.plot_trade_distribution()  # باید بدون خطا اجرا شود


def test_plot_drawdown(sample_trades: List[Dict[str, Any]]):
    vis = PortfolioVisualizer(sample_trades)
    vis.plot_drawdown()  # باید بدون خطا اجرا شود
