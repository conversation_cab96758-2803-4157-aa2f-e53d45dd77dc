"""
💼 Portfolio Package - Refactored for v2.0
پکیج مدیریت پورتفولیو - بازسازی شده برای نسخه 2.0

این پکیج شامل مدیریت پورتفولیو و پوزیشن‌ها سازگار با معماری جدید است
"""

# Import from core
from core.base import BaseComponent, TradingSignal, MarketData
from core.logger import get_logger
from core.config import get_config
from core.exceptions import TradingSystemError, ValidationError, ResourceError

# Legacy imports - maintained for backward compatibility
try:
    from .portfolio_manager import (
        PortfolioManager,
        Portfolio,
        Position,
        Trade
    )
except ImportError:
    # Will be created if doesn't exist
    pass

# New portfolio classes compatible with v2.0
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass
import pandas as pd
import numpy as np

@dataclass
class PositionV2:
    """پوزیشن نسخه 2.0"""
    symbol: str
    size: float
    entry_price: float
    entry_time: datetime
    position_type: str  # 'long' or 'short'
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    commission: float = 0.0
    swap: float = 0.0
    
    def update_price(self, current_price: float):
        """به‌روزرسانی قیمت فعلی"""
        self.current_price = current_price
        
        if self.position_type == "long":
            self.unrealized_pnl = (current_price - self.entry_price) * self.size
        else:
            self.unrealized_pnl = (self.entry_price - current_price) * self.size
        
        self.unrealized_pnl -= self.commission + self.swap
    
    def is_profitable(self) -> bool:
        """بررسی سودآوری"""
        return self.unrealized_pnl > 0
    
    def should_close(self) -> bool:
        """بررسی نیاز به بستن"""
        if self.stop_loss and self.position_type == "long":
            return self.current_price <= self.stop_loss
        elif self.stop_loss and self.position_type == "short":
            return self.current_price >= self.stop_loss
        
        if self.take_profit and self.position_type == "long":
            return self.current_price >= self.take_profit
        elif self.take_profit and self.position_type == "short":
            return self.current_price <= self.take_profit
        
        return False

@dataclass
class TradeV2:
    """معامله نسخه 2.0"""
    symbol: str
    size: float
    entry_price: float
    exit_price: float
    entry_time: datetime
    exit_time: datetime
    trade_type: str  # 'long' or 'short'
    pnl: float
    commission: float = 0.0
    swap: float = 0.0
    duration: Optional[float] = None
    
    def __post_init__(self):
        if self.duration is None:
            self.duration = (self.exit_time - self.entry_time).total_seconds()
    
    def is_profitable(self) -> bool:
        """بررسی سودآوری"""
        return self.pnl > 0
    
    def get_return_pct(self) -> float:
        """درصد بازده"""
        if self.entry_price == 0:
            return 0.0
        return (self.pnl / (self.entry_price * self.size)) * 100

class PortfolioManagerV2(BaseComponent):
    """مدیر پورتفولیو نسخه 2.0"""
    
    def __init__(self, initial_balance: float = 10000.0, config: Dict[str, Any] = None):
        super().__init__()
        
        self.config = config or get_config().trading.__dict__
        
        # Portfolio state
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.equity = initial_balance
        self.free_margin = initial_balance
        self.used_margin = 0.0
        
        # Positions and trades
        self.positions: Dict[str, PositionV2] = {}
        self.trade_history: List[TradeV2] = []
        
        # Risk management
        self.max_position_size = self.config.get("max_position_size", 0.1)
        self.max_open_positions = self.config.get("max_open_positions", 5)
        self.risk_per_trade = self.config.get("risk_per_trade", 0.02)
        self.stop_loss_pct = self.config.get("stop_loss_pct", 0.02)
        self.take_profit_pct = self.config.get("take_profit_pct", 0.04)
        
        # Performance metrics
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.max_balance = initial_balance
        
        # Market data
        self.current_prices: Dict[str, float] = {}
        
        self.logger = get_logger(__name__)
    
    def initialize(self) -> bool:
        """مقداردهی اولیه"""
        try:
            # Initialize portfolio state
            self._reset_portfolio()
            
            self.logger.info(f"Portfolio manager initialized with balance: {self.initial_balance}")
            self._initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize portfolio manager: {e}")
            return False
    
    def start(self) -> bool:
        """شروع مدیریت پورتفولیو"""
        if not self._initialized:
            if not self.initialize():
                return False
        
        self._running = True
        self.logger.info("Portfolio manager started")
        return True
    
    def stop(self) -> bool:
        """توقف مدیریت پورتفولیو"""
        try:
            # Close all positions
            self.close_all_positions()
            
            # Calculate final performance
            self._calculate_final_performance()
            
            self._running = False
            self.logger.info("Portfolio manager stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping portfolio manager: {e}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت پورتفولیو"""
        return {
            "initialized": self._initialized,
            "running": self._running,
            "balance": self.balance,
            "equity": self.equity,
            "free_margin": self.free_margin,
            "used_margin": self.used_margin,
            "open_positions": len(self.positions),
            "total_trades": self.total_trades,
            "win_rate": self.get_win_rate(),
            "total_pnl": self.total_pnl,
            "max_drawdown": self.max_drawdown
        }
    
    def update_price(self, symbol: str, price: float):
        """به‌روزرسانی قیمت نماد"""
        self.current_prices[symbol] = price
        
        # Update position if exists
        if symbol in self.positions:
            self.positions[symbol].update_price(price)
        
        # Update equity
        self._update_equity()
    
    def can_open_position(self, symbol: str, size: float) -> bool:
        """بررسی امکان باز کردن پوزیشن"""
        # Check maximum open positions
        if len(self.positions) >= self.max_open_positions:
            return False
        
        # Check if position already exists
        if symbol in self.positions:
            return False
        
        # Check margin requirements
        required_margin = self._calculate_required_margin(symbol, size)
        if required_margin > self.free_margin:
            return False
        
        # Check position size limits
        if size > self.max_position_size:
            return False
        
        return True
    
    def open_position(self, symbol: str, size: float, position_type: str, 
                     entry_price: float, stop_loss: float = None, 
                     take_profit: float = None) -> bool:
        """باز کردن پوزیشن"""
        try:
            if not self.can_open_position(symbol, size):
                self.logger.warning(f"Cannot open position for {symbol}")
                return False
            
            # Create position
            position = PositionV2(
                symbol=symbol,
                size=size,
                entry_price=entry_price,
                entry_time=datetime.now(),
                position_type=position_type,
                stop_loss=stop_loss,
                take_profit=take_profit,
                current_price=entry_price
            )
            
            # Calculate commission
            position.commission = self._calculate_commission(symbol, size)
            
            # Add to positions
            self.positions[symbol] = position
            
            # Update margins
            self._update_margins()
            
            self.logger.info(f"Position opened: {symbol} {position_type} {size}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error opening position: {e}")
            return False
    
    def close_position(self, symbol: str, exit_price: float = None) -> bool:
        """بستن پوزیشن"""
        try:
            if symbol not in self.positions:
                self.logger.warning(f"Position not found: {symbol}")
                return False
            
            position = self.positions[symbol]
            
            # Use current price if not provided
            if exit_price is None:
                exit_price = self.current_prices.get(symbol, position.current_price)
            
            # Calculate final PnL
            if position.position_type == "long":
                pnl = (exit_price - position.entry_price) * position.size
            else:
                pnl = (position.entry_price - exit_price) * position.size
            
            pnl -= position.commission + position.swap
            
            # Create trade record
            trade = TradeV2(
                symbol=symbol,
                size=position.size,
                entry_price=position.entry_price,
                exit_price=exit_price,
                entry_time=position.entry_time,
                exit_time=datetime.now(),
                trade_type=position.position_type,
                pnl=pnl,
                commission=position.commission,
                swap=position.swap
            )
            
            # Update statistics
            self.trade_history.append(trade)
            self.total_trades += 1
            self.total_pnl += pnl
            self.balance += pnl
            
            if pnl > 0:
                self.winning_trades += 1
            else:
                self.losing_trades += 1
            
            # Update max balance and drawdown
            if self.balance > self.max_balance:
                self.max_balance = self.balance
            
            drawdown = (self.max_balance - self.balance) / self.max_balance
            if drawdown > self.max_drawdown:
                self.max_drawdown = drawdown
            
            # Remove position
            del self.positions[symbol]
            
            # Update margins
            self._update_margins()
            
            self.logger.info(f"Position closed: {symbol} PnL: {pnl:.2f}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error closing position: {e}")
            return False
    
    def close_all_positions(self):
        """بستن تمام پوزیشن‌ها"""
        symbols = list(self.positions.keys())
        for symbol in symbols:
            self.close_position(symbol)
    
    def check_stop_loss_take_profit(self):
        """بررسی stop loss و take profit"""
        positions_to_close = []
        
        for symbol, position in self.positions.items():
            if position.should_close():
                positions_to_close.append(symbol)
        
        for symbol in positions_to_close:
            self.close_position(symbol)
    
    def get_position_size(self, symbol: str, risk_amount: float = None) -> float:
        """محاسبه اندازه پوزیشن"""
        if risk_amount is None:
            risk_amount = self.balance * self.risk_per_trade
        
        current_price = self.current_prices.get(symbol, 0.0)
        if current_price == 0:
            return 0.0
        
        stop_loss_distance = current_price * self.stop_loss_pct
        
        if stop_loss_distance > 0:
            position_size = risk_amount / stop_loss_distance
            return min(position_size, self.max_position_size)
        
        return 0.0
    
    def get_open_positions(self) -> List[PositionV2]:
        """دریافت پوزیشن‌های باز"""
        return list(self.positions.values())
    
    def get_trade_history(self) -> List[TradeV2]:
        """دریافت تاریخچه معاملات"""
        return self.trade_history
    
    def get_win_rate(self) -> float:
        """نرخ برد"""
        if self.total_trades == 0:
            return 0.0
        return self.winning_trades / self.total_trades
    
    def get_profit_factor(self) -> float:
        """ضریب سود"""
        winning_pnl = sum(trade.pnl for trade in self.trade_history if trade.is_profitable())
        losing_pnl = sum(abs(trade.pnl) for trade in self.trade_history if not trade.is_profitable())
        
        if losing_pnl == 0:
            return float('inf') if winning_pnl > 0 else 0.0
        
        return winning_pnl / losing_pnl
    
    def get_sharpe_ratio(self) -> float:
        """نسبت شارپ"""
        if not self.trade_history:
            return 0.0
        
        returns = [trade.get_return_pct() for trade in self.trade_history]
        
        if len(returns) < 2:
            return 0.0
        
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return == 0:
            return 0.0
        
        return mean_return / std_return
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """خلاصه عملکرد"""
        return {
            "initial_balance": self.initial_balance,
            "current_balance": self.balance,
            "equity": self.equity,
            "total_return": ((self.balance - self.initial_balance) / self.initial_balance) * 100,
            "total_trades": self.total_trades,
            "winning_trades": self.winning_trades,
            "losing_trades": self.losing_trades,
            "win_rate": self.get_win_rate(),
            "profit_factor": self.get_profit_factor(),
            "sharpe_ratio": self.get_sharpe_ratio(),
            "max_drawdown": self.max_drawdown * 100,
            "open_positions": len(self.positions),
            "total_pnl": self.total_pnl
        }
    
    def _reset_portfolio(self):
        """بازنشانی پورتفولیو"""
        self.balance = self.initial_balance
        self.equity = self.initial_balance
        self.free_margin = self.initial_balance
        self.used_margin = 0.0
        self.positions.clear()
        self.trade_history.clear()
        self.current_prices.clear()
        
        # Reset statistics
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.max_balance = self.initial_balance
    
    def _update_equity(self):
        """به‌روزرسانی equity"""
        unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        self.equity = self.balance + unrealized_pnl
    
    def _update_margins(self):
        """به‌روزرسانی margins"""
        self.used_margin = sum(self._calculate_required_margin(pos.symbol, pos.size) 
                              for pos in self.positions.values())
        self.free_margin = self.equity - self.used_margin
    
    def _calculate_required_margin(self, symbol: str, size: float) -> float:
        """محاسبه margin مورد نیاز"""
        # Simple margin calculation (can be enhanced)
        leverage = self.config.get("leverage", 100)
        price = self.current_prices.get(symbol, 1.0)
        return (price * size) / leverage
    
    def _calculate_commission(self, symbol: str, size: float) -> float:
        """محاسبه کمیسیون"""
        commission_rate = self.config.get("commission_rate", 0.0001)
        price = self.current_prices.get(symbol, 1.0)
        return price * size * commission_rate
    
    def _calculate_final_performance(self):
        """محاسبه عملکرد نهایی"""
        self.logger.info("=== Portfolio Performance Summary ===")
        summary = self.get_performance_summary()
        
        for key, value in summary.items():
            self.logger.info(f"{key}: {value}")

# Legacy compatibility wrapper
class LegacyPortfolioManager(PortfolioManagerV2):
    """مدیر پورتفولیو قدیمی برای backward compatibility"""
    
    def __init__(self, initial_balance=10000.0, max_positions=5):
        super().__init__(
            initial_balance=initial_balance,
            config={"max_open_positions": max_positions}
        )
    
    def get_balance(self):
        """دریافت موجودی"""
        return self.balance
    
    def get_equity(self):
        """دریافت equity"""
        return self.equity
    
    def get_positions(self):
        """دریافت پوزیشن‌ها"""
        return self.get_open_positions()
    
    def add_position(self, symbol, size, position_type, entry_price):
        """افزودن پوزیشن"""
        return self.open_position(symbol, size, position_type, entry_price)
    
    def remove_position(self, symbol):
        """حذف پوزیشن"""
        return self.close_position(symbol)

# Portfolio factory
class PortfolioFactory:
    """کارخانه پورتفولیو"""
    
    @staticmethod
    def create_portfolio_manager(initial_balance=10000.0, version="v2"):
        """ایجاد مدیر پورتفولیو"""
        if version == "v2":
            return PortfolioManagerV2(initial_balance=initial_balance)
        else:
            return LegacyPortfolioManager(initial_balance=initial_balance)

# Global factory instance
portfolio_factory = PortfolioFactory()

# Export all available functions and classes
__all__ = [
    # New classes
    "PositionV2",
    "TradeV2",
    "PortfolioManagerV2",
    "LegacyPortfolioManager",
    "PortfolioFactory",
    "portfolio_factory",
    
    # Base classes
    "BaseComponent",
    "TradingSignal",
    "MarketData"
]

# Version info
__version__ = "2.0.0"
__author__ = "Trading System Team"

# Migration message
import warnings
warnings.warn(
    "The portfolio package has been refactored for v2.0. "
    "Please update your code to use the new PortfolioManagerV2 class. "
    "Legacy PortfolioManager is maintained for backward compatibility but may be removed in future versions.",
    DeprecationWarning,
    stacklevel=2
)

# Initialize portfolio package
def initialize_portfolio_package():
    """مقداردهی اولیه پکیج پورتفولیو"""
    logger = get_logger(__name__)
    
    try:
        logger.info("(OK) Portfolio package initialized successfully")
        return True

    except Exception as e:
        logger.error(f"Portfolio initialization failed: {e}")
        return False

# Auto-initialize when imported
if __name__ != "__main__":
    initialize_portfolio_package()
