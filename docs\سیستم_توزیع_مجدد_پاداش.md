# مستند جامع: AdvancedRewardRedistributor

## مسئولیت
سیستم پیشرفته تعدیل پاداش در دوره‌های مختلف معاملاتی بر اساس drawdown، عملکرد، ریسک و شرایط بازار.

## پارامترها
- strategy: استراتژی تعدیل (Linear, Exponential, Adaptive)
- max_multiplier/min_multiplier: محدوده ضریب تعدیل
- enable_prediction: فعال‌سازی پیش‌بینی
- enable_quality_monitoring: نظارت کیفیت
- enable_risk_adjustment: تعدیل ریسک

## متدهای کلیدی
- redistribute_single: تعدیل یک پاداش
- redistribute_batch: تعدیل دسته‌ای
- get_performance_summary: خلاصه عملکرد
- predict_next_reward: پیش‌بینی پاداش بعدی
- optimize_strategy: بهینه‌سازی استراتژی

## نمونه کد
```python
from utils.reward_redistribution import AdvancedRewardRedistributor
redistributor = AdvancedRewardRedistributor()
new_reward = redistributor.redistribute_single(reward, equity_curve, action)
```

## مدیریت خطا
در صورت کاهش کیفیت پاداش، هشدار ارسال می‌شود.

## بهترین شیوه
- از استراتژی تطبیقی برای شرایط متغیر بازار استفاده کنید.
- عملکرد سیستم را به طور منظم نظارت کنید.

## نمودار
- نمودار تغییرات پاداش، کیفیت و عملکرد قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول در api/realtime_dashboard.py استفاده شده و در تست‌ها و مثال‌ها فعال است.

## وضعیت عملیاتی
✅ عملیاتی و در جریان اصلی پروژه فعال است. 