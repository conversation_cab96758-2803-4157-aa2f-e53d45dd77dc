import numpy as np
import pandas as pd
from stable_baselines3 import PPO, A2C
from stable_baselines3.common.vec_env import DummyVecEnv
from sklearn.model_selection import train_test_split
from env.trading_env import TradingEnv
from models.rl_models import RLModelFactory
from multiprocessing import Pool
import os
import pickle
import torch
import gc
import time


class ModelTrainer:
    def __init__(self):
        self.model_factory = RLModelFactory()
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.task_counter = 0
        self.max_concurrent_tasks = self.calculate_max_tasks()

    def calculate_max_tasks(self):
        """محاسبه تعداد تسک‌های همزمان بر اساس ظرفیت GPU"""
        if self.device != "cuda":
            return 2  # برای CPU، تعداد تسک‌ها محدود باشد

        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**2  # MB
        allocated_memory = torch.cuda.memory_allocated() / 1024**2
        reserved_memory = torch.cuda.memory_reserved() / 1024**2
        available_memory = total_memory - max(allocated_memory, reserved_memory)

        # هر تسک به طور متوسط 50 مگابایت حافظه نیاز دارد (بر اساس لاگ‌ها)
        memory_per_task = 50  # MB
        max_tasks = int(available_memory // memory_per_task)
        max_tasks = max(1, min(max_tasks, 4))  # حداقل 1 و حداکثر 4 تسک
        print(
            f"GPU Total Memory: {total_memory:.2f} MB, Available Memory: {available_memory:.2f} MB, Max Tasks: {max_tasks}"
        )
        return max_tasks

    def train_and_evaluate(
        self,
        df,
        symbol,
        style,
        timeframe,
        model_type,
        params,
        indicators=None,
        total_timesteps=5000,
        curriculum_steps=None,  # اضافه کردن ورودی curriculum_steps
    ):
        def make_env(df, symbol, style, timeframe, params, indicators):
            return TradingEnv(
                df, symbol, style, timeframe, indicators=indicators, **params
            )

        checkpoint_dir = "checkpoints"
        if not os.path.exists(checkpoint_dir):
            os.makedirs(checkpoint_dir)
        checkpoint_path = (
            f"{checkpoint_dir}/{symbol}_{timeframe}_{style}_{model_type}_checkpoint.pkl"
        )
        model_path = f"{checkpoint_dir}/{symbol}_{timeframe}_{style}_{model_type}.zip"
        auto_ckpt_path = f"{checkpoint_dir}/{symbol}_{timeframe}_{style}_{model_type}_auto.zip"

        start_time = time.time()
        env = DummyVecEnv(
            [lambda: make_env(df, symbol, style, timeframe, params, indicators)]
        )

        try:
            start_timestep = 0
            best_reward = None
            if os.path.exists(checkpoint_path) and os.path.exists(model_path):
                with open(checkpoint_path, "rb") as f:
                    checkpoint_data = pickle.load(f)
                start_timestep = checkpoint_data["timesteps_completed"]
                best_reward = checkpoint_data.get("best_reward", None)
                print(
                    f"Resuming training for {symbol}_{timeframe}_{style}_{model_type} from timestep {start_timestep}"
                )

            if start_timestep >= total_timesteps:
                print(
                    f"Training already completed for {symbol}_{timeframe}_{style}_{model_type}"
                )
                model = self.model_factory.create_model(model_type, env, verbose=1)
                model = model.load(model_path, env=env)
                if self.device == "cuda":
                    model.policy = model.policy.to(self.device)
                total_reward = checkpoint_data.get("total_reward", 0)
                returns = checkpoint_data.get("returns", [])
                trade_count = checkpoint_data.get("trade_count", 0)
            else:
                env_train_vec = DummyVecEnv(
                    [lambda: make_env(df, symbol, style, timeframe, params, indicators)]
                )

                if start_timestep > 0:
                    # اگر curriculum_steps یا سایر پارامترهای resume پیشرفته تعریف شده باشد، آموزش مرحله‌ای/پیشرفته انجام بده
                    model = self.model_factory.resume_training(
                        model_type,
                        env_train_vec,
                        model_path,
                        new_params=None,  # یا دیکشنری پارامتر جدید
                        curriculum_steps=curriculum_steps,
                        network_kwargs=params.get('network_kwargs', None),
                        optimizer_kwargs=params.get('optimizer_kwargs', None),
                        new_reward_fn=params.get('new_reward_fn', None)
                    )
                    if self.device == "cuda":
                        model.policy = model.policy.to(self.device)
                else:
                    model = self.model_factory.create_model(
                        model_type, env_train_vec, verbose=1
                    )

                remaining_timesteps = total_timesteps - start_timestep
                if remaining_timesteps > 0:
                    model.learn(total_timesteps=remaining_timesteps)

                temp_dir = "temp_models"
                if not os.path.exists(temp_dir):
                    os.makedirs(temp_dir)
                model.save(f"{temp_dir}/{symbol}_{timeframe}_{style}_{model_type}.zip")

                train_df, test_df = train_test_split(df, test_size=0.2, shuffle=False)
                env_test = DummyVecEnv(
                    [
                        lambda: make_env(
                            test_df, symbol, style, timeframe, params, indicators
                        )
                    ]
                )

                obs = env_test.reset()
                done = np.zeros(env_test.num_envs, dtype=bool)
                total_reward = 0
                returns = []
                trade_count = 0
                while not done.all():
                    obs_cpu = (
                        obs.cpu()
                        if isinstance(obs, torch.Tensor) and obs.is_cuda
                        else obs
                    )
                    action, _ = model.predict(obs_cpu, deterministic=True)
                    obs, reward, done, info = env_test.step(action)
                    total_reward += np.sum(reward)
                    if isinstance(info, list) and len(info) > 0:
                        if "balance" in info[0]:
                            balance = info[0]["balance"]
                        else:
                            balance = 10000
                        if "trade_executed" in info[0] and info[0]["trade_executed"]:
                            trade_count += 1
                    else:
                        balance = 10000
                    returns.append(balance - 10000)

                    # اعمال Stop-Loss
                    if balance < 9000:
                        print(
                            f"Stop-Loss triggered for {symbol}_{timeframe}_{style}_{model_type}"
                        )
                        break

                # ذخیره checkpoint با metadata و hash
                self.model_factory.auto_checkpoint(
                    model,
                    auto_ckpt_path,
                    epoch=total_timesteps,
                    reward=float(total_reward),
                    loss=None,
                    best_model=(best_reward is None or total_reward > best_reward)
                )
                # اگر بهترین مدل جدید بود، مقدار best_reward را به‌روزرسانی کن
                if best_reward is None or total_reward > best_reward:
                    best_reward = total_reward

                checkpoint_data = {
                    "timesteps_completed": total_timesteps,
                    "total_reward": total_reward,
                    "returns": returns,
                    "trade_count": trade_count,
                    "best_reward": best_reward
                }
                with open(checkpoint_path, "wb") as f:
                    pickle.dump(checkpoint_data, f)
                model.save(model_path)

            returns = np.array(returns)
            sharpe_ratio = np.mean(returns) / (np.std(returns) + 1e-10) * np.sqrt(252)
            max_drawdown = np.min(np.cumsum(returns))

            result = {
                "symbol": symbol,
                "style": style,
                "timeframe": timeframe,
                "model_type": model_type,
                "total_reward": total_reward,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
                "trade_count": trade_count,
                "indicators": indicators,
            }
            return result
        finally:
            if "env" in locals():
                env.close()
            if "env_train_vec" in locals():
                env_train_vec.close()
            if "env_test" in locals():
                env_test.close()
            if "model" in locals():
                del model
            self.task_counter += 1
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.reset_peak_memory_stats()
                allocated_memory = torch.cuda.memory_allocated()
                reserved_memory = torch.cuda.memory_reserved()
                print(
                    f"GPU memory after {symbol}_{timeframe}_{style}_{model_type} (time: {time.time() - start_time:.2f}s, task #{self.task_counter}, allocated: {allocated_memory / 1024**2:.2f} MB, reserved: {reserved_memory / 1024**2:.2f} MB, peak: {torch.cuda.max_memory_allocated() / 1024**2:.2f} MB, total: {torch.cuda.get_device_properties(0).total_memory / 1024**2:.2f} MB)"
                )
            gc.collect()


class PH3Optimizer:
    def __init__(self, fetcher, preprocessor, model_factory, trading_env_class, logger):
        self.fetcher = fetcher
        self.preprocessor = preprocessor
        self.model_factory = model_factory
        self.trading_env_class = trading_env_class
        self.logger = logger
        self.best_models = {}
        self.trainer = ModelTrainer()
        self.combination_results = []

    def train_task(self, args):
        try:
            df, symbol, style, timeframe, model_type, params, indicators = args
            print(f"Starting task for {symbol}_{timeframe}_{style}_{model_type}")
            start_time = time.time()
            result = self.trainer.train_and_evaluate(
                df, symbol, style, timeframe, model_type, params, indicators
            )
            print(
                f"Finished task for {symbol}_{timeframe}_{style}_{model_type} (time: {time.time() - start_time:.2f}s, trades: {result['trade_count']})"
            )
            return result
        except Exception as e:
            print(
                f"Error in train_task for {symbol}, {timeframe}, {style}, {model_type}: {str(e)}"
            )
            return None

    def evaluate_combination(self, df, symbol, timeframe, models, params, indicators):
        """ارزیابی ترکیب مدل‌ها"""
        env = DummyVecEnv(
            [
                lambda: self.trading_env_class(
                    df, symbol, "combined", timeframe, indicators=indicators, **params
                )
            ]
        )
        obs = env.reset()
        done = np.zeros(env.num_envs, dtype=bool)
        total_reward = 0
        returns = []
        trade_count = 0

        while not done.all():
            actions = []
            for model in models:
                action, _ = model.predict(obs, deterministic=True)
                actions.append(action)
            # ترکیب اقدامات (میانگین یا رای‌گیری)
            action = np.mean(actions, axis=0)
            obs, reward, done, info = env.step(action)
            total_reward += np.sum(reward)
            if isinstance(info, list) and len(info) > 0:
                if "balance" in info[0]:
                    balance = info[0]["balance"]
                else:
                    balance = 10000
                if "trade_executed" in info[0] and info[0]["trade_executed"]:
                    trade_count += 1
            else:
                balance = 10000
            returns.append(balance - 10000)

            if balance < 9000:
                print(f"Stop-Loss triggered for combined model on {symbol}_{timeframe}")
                break

        returns = np.array(returns)
        sharpe_ratio = np.mean(returns) / (np.std(returns) + 1e-10) * np.sqrt(252)
        max_drawdown = np.min(np.cumsum(returns))
        score = total_reward * 100 + sharpe_ratio * 50 - abs(max_drawdown) * 0.1

        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "style": "combined",
            "total_reward": total_reward,
            "sharpe_ratio": sharpe_ratio,
            "max_drawdown": max_drawdown,
            "trade_count": trade_count,
            "score": score,
            "models": models,
            "indicators": indicators,
        }

    def run_self_improving_system(
        self, symbols, timeframes, styles, model_types, trading_styles
    ):
        results = []
        tasks = []
        indicator_configs = [
            {"rsi": {"period": 14}, "ma": {"period": 20}},  # تنظیمات اندیکاتور 1
            {"rsi": {"period": 10}, "ma": {"period": 50}},  # تنظیمات اندیکاتور 2
            {"rsi": {"period": 20}, "ma": {"period": 10}},  # تنظیمات اندیکاتور 3
        ]

        for symbol in symbols:
            for timeframe in timeframes:
                print(f"Loading data for {symbol} on {timeframe}")
                df = self.load_data(symbol, timeframe)
                if df is not None and not df.empty:
                    print(f"Loaded manual data for {symbol} on {timeframe}")
                    df = self.preprocessor.preprocess(df)
                    print(f"Data info for {symbol} on {timeframe}:")
                    print(df.head())
                    print("Missing values:", df.isna().sum())
                    if df.isna().any().any():
                        print(
                            f"Warning: Data for {symbol} on {timeframe} contains NaN values!"
                        )
                    for style in styles:
                        for model_type in model_types:
                            for indicators in indicator_configs:
                                params = trading_styles[style]
                                tasks.append(
                                    (
                                        df,
                                        symbol,
                                        style,
                                        timeframe,
                                        model_type,
                                        params,
                                        indicators,
                                    )
                                )

        try:
            with Pool(processes=self.trainer.max_concurrent_tasks) as pool:
                results = pool.map(self.train_task, tasks)
        except Exception as e:
            print(f"Error in multiprocessing pool: {str(e)}")
            raise

        results = [r for r in results if r is not None]

        # انتخاب بهترین مدل‌ها
        for result in results:
            key = (
                result["symbol"],
                result["timeframe"],
                result["style"],
                str(result["indicators"]),
            )
            score = (
                result["total_reward"] * 100
                + result["sharpe_ratio"] * 50
                - abs(result["max_drawdown"]) * 0.1
            )
            model_path = f"checkpoints/{result['symbol']}_{result['timeframe']}_{result['style']}_{result['model_type']}.zip"
            env = DummyVecEnv(
                [
                    lambda: TradingEnv(
                        pd.DataFrame(),
                        result["symbol"],
                        result["style"],
                        result["timeframe"],
                    )
                ]
            )
            try:
                model = self.model_factory.create_model(
                    result["model_type"], env, verbose=1
                )
                model = model.load(model_path, env=env)
                result["model"] = model
                result["score"] = score

                if (
                    key not in self.best_models
                    or score > self.best_models[key]["score"]
                ):
                    self.best_models[key] = result
                    self.logger.log_message(
                        f"New best model for {key}: Score: {score}, Reward: {result['total_reward']}, "
                        f"Sharpe: {result['sharpe_ratio']}, Max Drawdown: {result['max_drawdown']}, "
                        f"Trades: {result['trade_count']}, Indicators: {result['indicators']}"
                    )
            finally:
                if "model" in locals():
                    del model
                if "env" in locals():
                    env.close()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.reset_peak_memory_stats()
                    print(
                        f"GPU memory after finalizing {result['symbol']}_{result['timeframe']}_{result['style']} (allocated: {torch.cuda.memory_allocated() / 1024**2:.2f} MB, reserved: {torch.cuda.memory_reserved() / 1024**2:.2f} MB, peak: {torch.cuda.max_memory_allocated() / 1024**2:.2f} MB, total: {torch.cuda.get_device_properties(0).total_memory / 1024**2:.2f} MB)"
                    )
                gc.collect()

        # آزمایش ترکیب مدل‌ها
        for symbol in symbols:
            for timeframe in timeframes:
                df = self.load_data(symbol, timeframe)
                if df is not None and not df.empty:
                    df = self.preprocessor.preprocess(df)
                    for indicators in indicator_configs:
                        # انتخاب دو مدل برتر برای هر symbol و timeframe
                        relevant_models = [
                            result
                            for key, result in self.best_models.items()
                            if key[0] == symbol
                            and key[1] == timeframe
                            and key[3] == str(indicators)
                        ]
                        if len(relevant_models) >= 2:
                            top_models = sorted(
                                relevant_models, key=lambda x: x["score"], reverse=True
                            )[:2]
                            models = [m["model"] for m in top_models]
                            params = trading_styles[
                                top_models[0]["style"]
                            ]  # استفاده از پارامترهای سبک برتر
                            combo_result = self.evaluate_combination(
                                df, symbol, timeframe, models, params, indicators
                            )
                            self.combination_results.append(combo_result)
                            self.logger.log_message(
                                f"Combination result for {symbol}_{timeframe}: Score: {combo_result['score']}, "
                                f"Reward: {combo_result['total_reward']}, Sharpe: {combo_result['sharpe_ratio']}, "
                                f"Max Drawdown: {combo_result['max_drawdown']}, Indicators: {indicators}"
                            )

        # انتخاب بهترین مدل یا ترکیب
        best_individual = max(
            self.best_models.values(), key=lambda x: x["score"], default=None
        )
        best_combination = max(
            self.combination_results, key=lambda x: x["score"], default=None
        )

        best_models_dir = "best_models"
        if not os.path.exists(best_models_dir):
            os.makedirs(best_models_dir)

        if best_individual and best_combination:
            if best_combination["score"] > best_individual["score"]:
                print(
                    f"Best result is a combination model for {best_combination['symbol']}_{best_combination['timeframe']}"
                )
                for i, model in enumerate(best_combination["models"]):
                    model.save(
                        f"{best_models_dir}/{best_combination['symbol']}_{best_combination['timeframe']}_combined_model_{i}.zip"
                    )
                return best_combination
            else:
                print(
                    f"Best result is an individual model for {best_individual['symbol']}_{best_individual['timeframe']}_{best_individual['style']}"
                )
                best_individual["model"].save(
                    f"{best_models_dir}/{best_individual['symbol']}_{best_individual['timeframe']}_{best_individual['style']}_{best_individual['model_type']}.zip"
                )
                return best_individual
        elif best_individual:
            print(
                f"Best result is an individual model for {best_individual['symbol']}_{best_individual['timeframe']}_{best_individual['style']}"
            )
            best_individual["model"].save(
                f"{best_models_dir}/{best_individual['symbol']}_{best_individual['timeframe']}_{best_individual['style']}_{best_individual['model_type']}.zip"
            )
            return best_individual
        elif best_combination:
            print(
                f"Best result is a combination model for {best_combination['symbol']}_{best_combination['timeframe']}"
            )
            for i, model in enumerate(best_combination["models"]):
                model.save(
                    f"{best_models_dir}/{best_combination['symbol']}_{best_combination['timeframe']}_combined_model_{i}.zip"
                )
            return best_combination
        else:
            print("No suitable models found.")
            return None

    def load_data(self, symbol, timeframe):
        try:
            df = self.fetcher.fetch_data(symbol, timeframe)
            return df
        except Exception as e:
            print(f"Error loading data for {symbol} on {timeframe}: {e}")
            return None
