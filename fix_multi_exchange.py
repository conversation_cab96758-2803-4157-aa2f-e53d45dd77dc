#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Fix Multi-Exchange Manager Script
اسکریپت رفع مشکل Multi-Exchange Manager
"""

import logging
from datetime import datetime

# تنظیم logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_multi_exchange_main():
    """رفع مشکل Multi-Exchange در main_new.py"""
    logger.info("🔧 Fixing Multi-Exchange Manager in main_new.py...")
    
    try:
        # خواندن فایل main_new.py
        with open('main_new.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # پیدا کردن بخش مشکل‌دار
        old_multi_exchange = '''            # Multi Exchange Manager
            if MULTI_EXCHANGE_AVAILABLE and multi_exchange_manager:
                try:
                    self.multi_exchange_manager = multi_exchange_manager
                    
                    # Check if ExchangeType is available
                    try:
                        from core.multi_exchange import ExchangeType
                        
                        # Add sample forex exchange
                        forex_config = ExchangeConfig(
                            exchange_id="forex_main",
                            name="Main Forex Exchange",
                            exchange_type=ExchangeType.FOREX,
                            api_url="https://api.forex.com",
                            sandbox=True
                        )
                        self.multi_exchange_manager.add_exchange(forex_config)
                        
                        # Add sample crypto exchange  
                        crypto_config = ExchangeConfig(
                            exchange_id="crypto_main",
                            name="Main Crypto Exchange", 
                            exchange_type=ExchangeType.CRYPTO,
                            api_url="https://api.crypto.com",
                            sandbox=True
                        )
                        self.multi_exchange_manager.add_exchange(crypto_config)
                        
                        logger.info("✅ Multi-Exchange Manager: Configured with 2 exchanges")
                        success_count += 1
                    except (ImportError, AttributeError) as enum_error:
                        logger.warning(f"⚠️ Multi-Exchange Manager: ExchangeType not available ({enum_error})")
                        logger.info("✅ Multi-Exchange Manager: Available (basic mode)")
                        success_count += 1
                        
                except Exception as e:
                    logger.error(f"❌ Multi-Exchange Manager error: {e}")
                total_count += 1'''
        
        # بخش جدید و درست
        new_multi_exchange = '''            # Multi Exchange Manager
            if MULTI_EXCHANGE_AVAILABLE and multi_exchange_manager:
                try:
                    self.multi_exchange_manager = multi_exchange_manager
                    
                    # Check if ExchangeType is available
                    try:
                        from core.multi_exchange import ExchangeType
                        from core.shared_types import ExchangeConfig
                        
                        # Add sample forex exchange
                        forex_config = ExchangeConfig(
                            exchange_id="forex_main",
                            name="Main Forex Exchange",
                            exchange_type=ExchangeType.FOREX,
                            api_url="https://api.forex.com",
                            timeout=30,
                            enabled=True,
                            sandbox=True,
                            supported_symbols=[]
                        )
                        if self.multi_exchange_manager.add_exchange(forex_config):
                            logger.info("✅ Forex exchange added successfully")
                        
                        # Add sample crypto exchange  
                        crypto_config = ExchangeConfig(
                            exchange_id="crypto_main",
                            name="Main Crypto Exchange", 
                            exchange_type=ExchangeType.CRYPTO,
                            api_url="https://api.crypto.com",
                            timeout=30,
                            enabled=True,
                            sandbox=True,
                            supported_symbols=[]
                        )
                        if self.multi_exchange_manager.add_exchange(crypto_config):
                            logger.info("✅ Crypto exchange added successfully")
                        
                        logger.info("✅ Multi-Exchange Manager: Configured with 2 exchanges")
                        success_count += 1
                    except (ImportError, AttributeError) as enum_error:
                        logger.warning(f"⚠️ Multi-Exchange Manager: ExchangeType not available ({enum_error})")
                        logger.info("✅ Multi-Exchange Manager: Available (basic mode)")
                        success_count += 1
                        
                except Exception as e:
                    logger.error(f"❌ Multi-Exchange Manager error: {e}")
                total_count += 1'''
        
        # جایگزینی
        if old_multi_exchange in content:
            content = content.replace(old_multi_exchange, new_multi_exchange)
            
            # ذخیره فایل
            with open('main_new.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("✅ Multi-Exchange Manager fixed")
            return True
        else:
            logger.warning("⚠️ Multi-Exchange section not found or already fixed")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error fixing Multi-Exchange Manager: {e}")
        return False

def fix_test_runner_main():
    """رفع مشکل Test Runner در main_new.py"""
    logger.info("🔧 Fixing Test Runner in main_new.py...")
    
    try:
        # خواندن فایل main_new.py
        with open('main_new.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # پیدا کردن بخش test runner
        old_test_runner = '''            # Test runner validation
            if hasattr(self.test_runner, 'run_test_suite'):
                logger.info("✅ Test runner initialized successfully")
                return True
            else:
                logger.error("❌ Test runner initialization failed")
                return False'''
        
        new_test_runner = '''            # Test runner validation
            if hasattr(self.test_runner, 'run_startup_tests'):
                logger.info("✅ Test runner initialized successfully")
                return True
            else:
                logger.error("❌ Test runner initialization failed")
                return False'''
        
        if old_test_runner in content:
            content = content.replace(old_test_runner, new_test_runner)
        else:
            # اگر پیدا نشد، بخش دیگری را جستجو کن
            old_test_runner2 = '''            if hasattr(self.test_runner, 'run_test_suite'):'''
            new_test_runner2 = '''            if hasattr(self.test_runner, 'run_startup_tests'):'''
            
            if old_test_runner2 in content:
                content = content.replace(old_test_runner2, new_test_runner2)
        
        # رفع مشکل run_system_tests
        old_system_tests = '''            # Run basic tests
            test_results = self.test_runner.run_test_suite()'''
        
        new_system_tests = '''            # Run basic tests
            test_results = self.test_runner.run_startup_tests()'''
        
        if old_system_tests in content:
            content = content.replace(old_system_tests, new_system_tests)
        
        # ذخیره فایل
        with open('main_new.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("✅ Test Runner fixed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing Test Runner: {e}")
        return False

def test_fixes():
    """تست رفع مشکلات"""
    logger.info("🧪 Testing fixes...")
    
    try:
        import sys
        sys.path.insert(0, '.')
        
        # تست Multi-Exchange Manager
        from core.multi_exchange import MultiExchangeManager, ExchangeType
        from core.shared_types import ExchangeConfig
        
        manager = MultiExchangeManager()
        
        # تست اضافه کردن exchange
        forex_config = ExchangeConfig(
            exchange_id="test_forex",
            name="Test Forex",
            exchange_type=ExchangeType.FOREX,
            api_url="https://test.com",
            timeout=30,
            enabled=True,
            sandbox=True,
            supported_symbols=[]
        )
        
        result = manager.add_exchange(forex_config)
        logger.info(f"Multi-Exchange test: {'✅ Success' if result else '❌ Failed'}")
        
        # تست Test Runner
        from utils.test_runner import OperationalTestRunner
        
        runner = OperationalTestRunner()
        has_method = hasattr(runner, 'run_startup_tests')
        logger.info(f"Test Runner test: {'✅ Success' if has_method else '❌ Failed'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

def main():
    """اجرای رفع مشکلات"""
    print('🔧 Fix Multi-Exchange & Test Runner Script')
    print('=' * 50)
    print(f'⏰ شروع: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    try:
        # 1. رفع مشکل Test Runner
        if not fix_test_runner_main():
            return 1
        
        # 2. رفع مشکل Multi-Exchange Manager
        if not fix_multi_exchange_main():
            return 1
        
        # 3. تست رفع مشکلات
        if not test_fixes():
            return 1
        
        print(f'\n🎉 همه مشکلات رفع شدند!')
        print(f'⏰ پایان: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        return 0
        
    except Exception as e:
        logger.error(f"❌ خطای کلی: {e}")
        return 1

if __name__ == "__main__":
    exit(main()) 