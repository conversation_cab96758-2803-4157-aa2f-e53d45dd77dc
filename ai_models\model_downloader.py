"""
🤗 HuggingFace Model Downloader with Proxy Support
دانلودکننده مدل‌های HuggingFace با پشتیبانی پروکسی

این فایل شامل:
1. تنظیم پروکسی برای HuggingFace
2. دانلود محلی مدل‌ها
3. مدیریت cache و storage
4. بهینه‌سازی حافظه
"""

import os
import sys
import json
import logging
import requests
import time
from typing import Dict, Any, Optional, List
from pathlib import Path
import shutil
from dataclasses import dataclass

# HuggingFace imports
from huggingface_hub import snapshot_download, login, HfApi
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logger = logging.getLogger(__name__)

@dataclass
class ModelInfo:
    """اطلاعات مدل"""
    name: str
    repo_id: str
    description: str
    size_mb: float
    performance: float
    labels: List[str]
    downloaded: bool = False
    local_path: Optional[str] = None

class ProxyManager:
    """مدیر پروکسی"""
    
    def __init__(self, proxy_config_path: str = "PROXY.json"):
        self.proxy_config_path = proxy_config_path
        self.proxy_config = None
        self.proxies = None
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def load_proxy_config(self) -> bool:
        """بارگذاری تنظیمات پروکسی"""
        try:
            if os.path.exists(self.proxy_config_path):
                with open(self.proxy_config_path, 'r', encoding='utf-8') as f:
                    self.proxy_config = json.load(f)
                
                # Extract proxy settings
                http_port = None
                socks_port = None
                
                for inbound in self.proxy_config.get('inbounds', []):
                    if inbound.get('protocol') == 'http':
                        http_port = inbound.get('port')
                    elif inbound.get('protocol') == 'socks':
                        socks_port = inbound.get('port')
                
                # Set up proxies for requests
                if http_port:
                    self.proxies = {
                        'http': f'http://127.0.0.1:{http_port}',
                        'https': f'http://127.0.0.1:{http_port}'
                    }
                elif socks_port:
                    self.proxies = {
                        'http': f'socks5://127.0.0.1:{socks_port}',
                        'https': f'socks5://127.0.0.1:{socks_port}'
                    }
                
                self.logger.info(f"✅ Proxy config loaded: {self.proxies}")
                return True
            else:
                self.logger.warning("⚠️ Proxy config file not found, using direct connection")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Failed to load proxy config: {e}")
            return False
    
    def setup_environment_proxy(self):
        """تنظیم پروکسی در environment variables"""
        if self.proxies:
            os.environ['HTTP_PROXY'] = self.proxies['http']
            os.environ['HTTPS_PROXY'] = self.proxies['https']
            os.environ['http_proxy'] = self.proxies['http']
            os.environ['https_proxy'] = self.proxies['https']
            self.logger.info("🔧 Environment proxy variables set")

class HuggingFaceModelDownloader:
    """دانلودکننده مدل‌های HuggingFace"""
    
    def __init__(self, cache_dir: str = "./models_cache", use_proxy: bool = True):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.use_proxy = use_proxy
        self.proxy_manager = ProxyManager() if use_proxy else None
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Available models
        self.available_models = {
            "finbert": ModelInfo(
                name="FinBERT",
                repo_id="ProsusAI/finbert",
                description="BERT fine-tuned for financial sentiment analysis",
                size_mb=440,
                performance=0.89,
                labels=["positive", "negative", "neutral"]
            ),
            "financial_roberta": ModelInfo(
                name="Financial RoBERTa",
                repo_id="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
                description="DistilRoBERTa fine-tuned for financial news sentiment",
                size_mb=320,
                performance=0.87,
                labels=["LABEL_0", "LABEL_1", "LABEL_2"]
            ),
            "cardiffnlp_roberta": ModelInfo(
                name="Cardiff RoBERTa",
                repo_id="cardiffnlp/twitter-roberta-base-sentiment-latest",
                description="RoBERTa trained on Twitter sentiment (general purpose)",
                size_mb=500,
                performance=0.85,
                labels=["LABEL_0", "LABEL_1", "LABEL_2"]
            )
        }
        
        # Setup proxy if needed
        if self.use_proxy and self.proxy_manager:
            self.proxy_manager.load_proxy_config()
            self.proxy_manager.setup_environment_proxy()
    
    def check_model_availability(self, model_key: str) -> bool:
        """بررسی در دسترس بودن مدل"""
        if model_key not in self.available_models:
            return False
        
        model_info = self.available_models[model_key]
        local_path = self.cache_dir / model_key
        
        if local_path.exists() and any(local_path.iterdir()):
            model_info.downloaded = True
            model_info.local_path = str(local_path)
            self.logger.info(f"✅ Model {model_key} already downloaded at {local_path}")
            return True
        
        return False
    
    def download_model(self, model_key: str, force_download: bool = False) -> bool:
        """دانلود مدل"""
        if model_key not in self.available_models:
            self.logger.error(f"❌ Unknown model: {model_key}")
            return False
        
        model_info = self.available_models[model_key]
        local_path = self.cache_dir / model_key
        
        # Check if already downloaded
        if not force_download and self.check_model_availability(model_key):
            return True
        
        try:
            self.logger.info(f"🔄 Downloading {model_info.name} ({model_info.size_mb}MB)...")
            self.logger.info(f"📂 Saving to: {local_path}")
            
            # Create directory
            local_path.mkdir(exist_ok=True)
            
            # Download model files
            start_time = time.time()
            
            snapshot_download(
                repo_id=model_info.repo_id,
                cache_dir=str(local_path),
                local_dir=str(local_path),
                local_dir_use_symlinks=False,
                resume_download=True
            )
            
            download_time = time.time() - start_time
            
            # Update model info
            model_info.downloaded = True
            model_info.local_path = str(local_path)
            
            self.logger.info(f"✅ {model_info.name} downloaded successfully in {download_time:.1f}s")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to download {model_info.name}: {e}")
            # Clean up partial download
            if local_path.exists():
                shutil.rmtree(local_path, ignore_errors=True)
            return False
    
    def download_lightweight_models(self) -> Dict[str, bool]:
        """دانلود مدل‌های سبک"""
        lightweight_models = ["financial_roberta", "cardiffnlp_roberta"]
        results = {}
        
        self.logger.info("🚀 Downloading lightweight models...")
        
        for model_key in lightweight_models:
            model_info = self.available_models[model_key]
            self.logger.info(f"📦 Attempting to download {model_info.name} ({model_info.size_mb}MB)")
            results[model_key] = self.download_model(model_key)
        
        return results
    
    def get_model_path(self, model_key: str) -> Optional[str]:
        """دریافت مسیر محلی مدل"""
        if model_key in self.available_models:
            model_info = self.available_models[model_key]
            if model_info.downloaded and model_info.local_path:
                return model_info.local_path
        return None
    
    def get_download_status(self) -> Dict[str, Any]:
        """دریافت وضعیت دانلود"""
        status = {
            "cache_directory": str(self.cache_dir),
            "proxy_enabled": self.use_proxy,
            "models": {}
        }
        
        for model_key, model_info in self.available_models.items():
            status["models"][model_key] = {
                "name": model_info.name,
                "downloaded": model_info.downloaded,
                "local_path": model_info.local_path,
                "size_mb": model_info.size_mb,
                "performance": model_info.performance
            }
        
        return status
    
    def cleanup_cache(self, model_key: Optional[str] = None):
        """پاک‌سازی cache"""
        if model_key:
            # Clean specific model
            model_path = self.cache_dir / model_key
            if model_path.exists():
                shutil.rmtree(model_path)
                self.available_models[model_key].downloaded = False
                self.available_models[model_key].local_path = None
                self.logger.info(f"🗑️ Cleaned cache for {model_key}")
        else:
            # Clean all cache
            if self.cache_dir.exists():
                shutil.rmtree(self.cache_dir)
                self.cache_dir.mkdir(exist_ok=True)
                for model_info in self.available_models.values():
                    model_info.downloaded = False
                    model_info.local_path = None
                self.logger.info("🗑️ All cache cleaned")

def test_proxy_connection() -> bool:
    """تست اتصال پروکسی"""
    proxy_manager = ProxyManager()
    
    if proxy_manager.load_proxy_config():
        proxy_manager.setup_environment_proxy()
        
        try:
            # Test connection to HuggingFace
            response = requests.get("https://huggingface.co", timeout=10)
            if response.status_code == 200:
                logger.info("✅ Proxy connection successful")
                return True
            else:
                logger.warning(f"⚠️ Proxy connection issue: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ Proxy connection failed: {e}")
            return False
    else:
        logger.info("🔄 Testing direct connection...")
        try:
            response = requests.get("https://huggingface.co", timeout=10)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"❌ Direct connection failed: {e}")
            return False

if __name__ == "__main__":
    # Test the downloader
    print("🤗 Testing HuggingFace Model Downloader...")
    
    # Test proxy
    print("🔄 Testing proxy connection...")
    proxy_works = test_proxy_connection()
    print(f"Proxy status: {'✅ Working' if proxy_works else '❌ Failed'}")
    
    # Test downloader
    downloader = HuggingFaceModelDownloader(use_proxy=proxy_works)
    
    print("\n📊 Available models:")
    for key, model in downloader.available_models.items():
        print(f"  {key}: {model.name} ({model.size_mb}MB)")
    
    print("\n🚀 Attempting to download lightweight models...")
    results = downloader.download_lightweight_models()
    
    print("\n📊 Download results:")
    for model_key, success in results.items():
        status = "✅ Success" if success else "❌ Failed"
        print(f"  {model_key}: {status}")
    
    print("\n📋 Final status:")
    status = downloader.get_download_status()
    for model_key, model_status in status["models"].items():
        downloaded = "✅" if model_status["downloaded"] else "❌"
        print(f"  {model_key}: {downloaded} {model_status['name']}")
