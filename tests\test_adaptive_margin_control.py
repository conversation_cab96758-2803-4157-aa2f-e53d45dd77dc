import unittest
import sys
import os
import numpy as np
import pandas as pd
import torch
import json
from datetime import datetime, timedel<PERSON>

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.adaptive_margin_control import AdaptiveMarginControl, MarginRiskModel

class TestAdaptiveMarginControl(unittest.TestCase):
    """
    Test cases for the AdaptiveMarginControl class
    """
    
    def setUp(self):
        """
        Set up test environment before each test
        """
        # Initialize with test configuration
        self.margin_control = AdaptiveMarginControl(
            base_margin_rates={
                'EURUSD': 0.02,
                'BTCUSD': 0.50
            },
            volatility_window=10,
            max_margin_multiplier=2.0,
            min_margin_multiplier=0.5,
            use_ml_model=False  # Use rule-based for faster tests
        )
        
        # Sample price data
        self.price_data = {
            'EURUSD': [1.1000, 1.1005, 1.1010, 1.1008, 1.1012, 
                       1.1020, 1.1015, 1.1025, 1.1030, 1.1028],
            'BTCUSD': [50000, 50500, 51000, 50800, 50600, 
                      51200, 51500, 52000, 51800, 51500]
        }
        
        # Sample market data
        self.market_data = {
            'sentiment': 0.2,  # Slightly positive sentiment
            'volume': 0.5      # Medium volume
        }
    
    def test_initialization(self):
        """Test that the margin control initializes correctly"""
        self.assertEqual(self.margin_control.volatility_window, 10)
        self.assertEqual(self.margin_control.max_margin_multiplier, 2.0)
        self.assertEqual(self.margin_control.min_margin_multiplier, 0.5)
        self.assertEqual(self.margin_control.base_margin_rates['EURUSD'], 0.02)
        self.assertEqual(self.margin_control.base_margin_rates['BTCUSD'], 0.50)
    
    def test_price_history_update(self):
        """Test price history update"""
        symbol = 'EURUSD'
        
        # Update with a few prices
        for price in self.price_data[symbol]:
            self.margin_control.update_price_history(symbol, price)
        
        # Check history
        self.assertEqual(len(self.margin_control.price_history[symbol]), len(self.price_data[symbol]))
        self.assertEqual(self.margin_control.price_history[symbol][-1], self.price_data[symbol][-1])
    
    def test_position_update(self):
        """Test position update"""
        symbol = 'EURUSD'
        
        # Update position
        self.margin_control.update_position(symbol, 10000, 5.0)
        self.margin_control.update_position(symbol, 15000, 3.0)
        
        # Check history
        self.assertEqual(len(self.margin_control.position_history[symbol]), 2)
        self.assertEqual(self.margin_control.position_history[symbol][-1]['size'], 15000)
        self.assertEqual(self.margin_control.position_history[symbol][-1]['leverage'], 3.0)
    
    def test_volatility_calculation(self):
        """Test volatility calculation"""
        symbol = 'EURUSD'
        
        # Update with price data
        for price in self.price_data[symbol]:
            self.margin_control.update_price_history(symbol, price)
        
        # Calculate volatility
        volatility = self.margin_control.calculate_volatility(symbol)
        
        # Check that volatility is a reasonable value
        self.assertGreaterEqual(volatility, 0.0)
        self.assertLessEqual(volatility, 0.01)  # EURUSD volatility is typically low
    
    def test_position_risk_calculation(self):
        """Test position risk calculation"""
        symbol = 'EURUSD'
        
        # No position initially
        risk = self.margin_control.calculate_position_risk(symbol)
        self.assertEqual(risk, 0.0)
        
        # Update with a position
        self.margin_control.update_position(symbol, 10000, 5.0)
        
        # Calculate risk
        risk = self.margin_control.calculate_position_risk(symbol)
        
        # Check risk value
        self.assertGreater(risk, 0.0)
        self.assertLessEqual(risk, 1.0)
    
    def test_margin_requirement_calculation(self):
        """Test margin requirement calculation"""
        symbol = 'EURUSD'
        
        # Update with price data
        for price in self.price_data[symbol]:
            self.margin_control.update_price_history(symbol, price)
        
        # Update with a position
        self.margin_control.update_position(symbol, 10000, 5.0)
        
        # Calculate margin requirement
        margin_info = self.margin_control.calculate_margin_requirement(
            symbol, price=1.1035, position_size=10000, leverage=5.0, market_data=self.market_data
        )
        
        # Check margin info
        self.assertEqual(margin_info['symbol'], symbol)
        self.assertEqual(margin_info['base_margin_rate'], 0.02)
        self.assertGreaterEqual(margin_info['margin_multiplier'], self.margin_control.min_margin_multiplier)
        self.assertLessEqual(margin_info['margin_multiplier'], self.margin_control.max_margin_multiplier)
        self.assertGreaterEqual(margin_info['margin_requirement'], 0.01)  # At least 1%
        self.assertLessEqual(margin_info['margin_requirement'], 0.04)  # At most 4% (2% * 2.0)
    
    def test_margin_history(self):
        """Test margin history tracking"""
        symbol = 'EURUSD'
        
        # Calculate margin requirements multiple times
        for i, price in enumerate(self.price_data[symbol]):
            self.margin_control.calculate_margin_requirement(
                symbol, price=price, position_size=10000 + i * 1000, leverage=5.0
            )
        
        # Get history
        history = self.margin_control.get_margin_history(symbol)
        
        # Check history
        self.assertEqual(len(history), len(self.price_data[symbol]))
        for entry in history:
            self.assertIn('timestamp', entry)
            self.assertIn('base_margin', entry)
            self.assertIn('multiplier', entry)
            self.assertIn('requirement', entry)
    
    def test_margin_stats(self):
        """Test margin statistics calculation"""
        symbol = 'EURUSD'
        
        # Calculate margin requirements multiple times
        for i, price in enumerate(self.price_data[symbol]):
            self.margin_control.calculate_margin_requirement(
                symbol, price=price, position_size=10000 + i * 1000, leverage=5.0
            )
        
        # Get stats
        stats = self.margin_control.get_margin_stats(symbol)
        
        # Check stats
        self.assertIn('mean', stats)
        self.assertIn('max', stats)
        self.assertIn('min', stats)
        self.assertIn('current', stats)
        self.assertIn('volatility', stats)
    
    def test_config_save_load(self):
        """Test configuration save and load"""
        # Save configuration
        config_path = 'test_margin_config.json'
        self.margin_control.save_config(config_path)
        
        # Create a new instance and load config
        new_control = AdaptiveMarginControl()
        new_control.load_config(config_path)
        
        # Check loaded configuration
        self.assertEqual(new_control.volatility_window, self.margin_control.volatility_window)
        self.assertEqual(new_control.max_margin_multiplier, self.margin_control.max_margin_multiplier)
        self.assertEqual(new_control.min_margin_multiplier, self.margin_control.min_margin_multiplier)
        self.assertEqual(new_control.base_margin_rates['EURUSD'], self.margin_control.base_margin_rates['EURUSD'])
        
        # Clean up
        if os.path.exists(config_path):
            os.remove(config_path)
    
    def test_ml_model(self):
        """Test ML model initialization and prediction"""
        # Create margin control with ML model
        ml_control = AdaptiveMarginControl(use_ml_model=True)
        
        # Check model initialization
        self.assertIsInstance(ml_control.model, MarginRiskModel)
        self.assertIsInstance(ml_control.target_model, MarginRiskModel)
        
        # Update with some data
        symbol = 'EURUSD'
        for price in self.price_data[symbol]:
            ml_control.update_price_history(symbol, price)
        
        ml_control.update_position(symbol, 10000, 5.0)
        
        # Calculate margin requirement
        margin_info = ml_control.calculate_margin_requirement(
            symbol, price=1.1035, position_size=10000, leverage=5.0, market_data=self.market_data
        )
        
        # Check margin info
        self.assertEqual(margin_info['symbol'], symbol)
        self.assertGreaterEqual(margin_info['margin_multiplier'], ml_control.min_margin_multiplier)
        self.assertLessEqual(margin_info['margin_multiplier'], ml_control.max_margin_multiplier)
    
    def test_explain_margin_decision(self):
        """Test the explain_margin_decision method."""
        # Get explanation for a symbol
        explanation = self.margin_control.explain_margin_decision('EURUSD')
        
        # Check explanation structure
        self.assertIsInstance(explanation, dict)
        self.assertIn('symbol', explanation)
        self.assertIn('base_margin_rate', explanation)
        self.assertIn('margin_multiplier', explanation)
        self.assertIn('margin_requirement', explanation)
        self.assertIn('margin_requirement_pct', explanation)
        self.assertIn('risk_assessment', explanation)
        self.assertIn('primary_factors', explanation)
        self.assertIn('historical_context', explanation)
        self.assertIn('model_confidence', explanation)
        self.assertIn('recommendation', explanation)
        self.assertIn('timestamp', explanation)
        
        # Check that base margin rate matches expected value
        self.assertEqual(explanation['base_margin_rate'], 0.02)
        
        # Check primary factors
        self.assertIsInstance(explanation['primary_factors'], list)
        
        # Test with market data
        market_data = {
            'sentiment': -0.7,  # Negative sentiment
            'volatility': 0.03  # High volatility
        }
        
        explanation_with_data = self.margin_control.explain_margin_decision('EURUSD', market_data)
        
        # Check that market data is reflected in the explanation
        has_sentiment_factor = False
        for factor in explanation_with_data['primary_factors']:
            if factor['factor'] == 'negative_sentiment':
                has_sentiment_factor = True
                self.assertEqual(factor['impact'], 'increase')
                break
                
        self.assertTrue(has_sentiment_factor, "Negative sentiment should be reflected in factors")
        
        # Test with position updates to trigger position risk factor
        self.margin_control.update_position('EURUSD', 100.0, 5.0)  # Large position with high leverage
        explanation_with_position = self.margin_control.explain_margin_decision('EURUSD')
        
        has_position_risk = False
        for factor in explanation_with_position['primary_factors']:
            if 'position_risk' in factor['factor']:
                has_position_risk = True
                break
                
        self.assertTrue(has_position_risk, "Position risk should be reflected in factors")
        
        # Test margin trend analysis
        # Add some history first
        for i in range(5):
            if 'EURUSD' not in self.margin_control.margin_history:
                self.margin_control.margin_history['EURUSD'] = []
                
            self.margin_control.margin_history['EURUSD'].append({
                'timestamp': datetime.now().timestamp() - (i * 3600),
                'margin_requirement': 0.02 * (1.0 + i * 0.1),
                'margin_multiplier': 1.0 + i * 0.1
            })
            
        trend_explanation = self.margin_control.explain_margin_decision('EURUSD')
        self.assertIn('recent_trend', trend_explanation['historical_context'])


if __name__ == '__main__':
    unittest.main() 