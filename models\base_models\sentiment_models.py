"""
Real Sentiment Models for Financial Analysis
مدل‌های واقعی تحلیل احساسات مالی
"""

import logging
from typing import Dict, Any, Optional
from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline
import torch

logger = logging.getLogger(__name__)

class FinBERTModel:
    """FinBERT Model for financial sentiment analysis"""
    
    def __init__(self, model_name: str = "ProsusAI/finbert"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            self.pipeline = pipeline(
                'sentiment-analysis',
                model=self.model_name,
                device=0 if torch.cuda.is_available() else -1
            )
            self.logger.info(f"✅ FinBERT model loaded: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to load FinBERT model: {e}")
            return False
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """تحلیل احساسات"""
        if not self.pipeline:
            return {'error': 'Model not loaded'}
        
        try:
            result = self.pipeline(text)
            return {
                'model_name': self.model_name,
                'text': text,
                'sentiment': result[0]['label'],
                'confidence': result[0]['score'],
                'label': result[0]['label'],
                'probabilities': {result[0]['label']: result[0]['score']}
            }
        except Exception as e:
            self.logger.error(f"Error in sentiment analysis: {e}")
            return {'error': str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.pipeline is not None,
            'status': 'healthy' if self.pipeline else 'not_loaded'
        }

class CryptoBERTModel:
    """CryptoBERT Model for cryptocurrency sentiment analysis"""
    
    def __init__(self, model_name: str = "ElKulako/cryptobert"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            self.pipeline = pipeline(
                'sentiment-analysis',
                model=self.model_name,
                device=0 if torch.cuda.is_available() else -1
            )
            self.logger.info(f"✅ CryptoBERT model loaded: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to load CryptoBERT model: {e}")
            return False
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """تحلیل احساسات"""
        if not self.pipeline:
            return {'error': 'Model not loaded'}
        
        try:
            result = self.pipeline(text)
            return {
                'model_name': self.model_name,
                'text': text,
                'sentiment': result[0]['label'],
                'confidence': result[0]['score'],
                'label': result[0]['label'],
                'probabilities': {result[0]['label']: result[0]['score']}
            }
        except Exception as e:
            self.logger.error(f"Error in sentiment analysis: {e}")
            return {'error': str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.pipeline is not None,
            'status': 'healthy' if self.pipeline else 'not_loaded'
        }

class FinancialSentimentModel:
    """Financial Sentiment Model for general financial sentiment analysis"""
    
    def __init__(self, model_name: str = "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            self.pipeline = pipeline(
                'sentiment-analysis',
                model=self.model_name,
                device=0 if torch.cuda.is_available() else -1
            )
            self.logger.info(f"✅ Financial Sentiment model loaded: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to load Financial Sentiment model: {e}")
            return False
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """تحلیل احساسات"""
        if not self.pipeline:
            return {'error': 'Model not loaded'}
        
        try:
            result = self.pipeline(text)
            return {
                'model_name': self.model_name,
                'text': text,
                'sentiment': result[0]['label'],
                'confidence': result[0]['score'],
                'label': result[0]['label'],
                'probabilities': {result[0]['label']: result[0]['score']}
            }
        except Exception as e:
            self.logger.error(f"Error in sentiment analysis: {e}")
            return {'error': str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.pipeline is not None,
            'status': 'healthy' if self.pipeline else 'not_loaded'
        } 