#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ Multi-Exchange Support System
سیستم پشتیبانی چند صرافی با API یکپارچه و همگام‌سازی داده
"""

import os
import sys
import time
import json
import asyncio
import aiohttp
import threading
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from decimal import Decimal
from abc import ABC, abstractmethod
import logging
from contextlib import asynccontextmanager

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .exceptions import TradingSystemError, ConnectionError, ValidationError
from .shared_types import Order, OrderType, OrderSide, OrderStatus, Fill, ExchangeType, ExchangeConfig

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class ExchangeStatus(Enum):
    """وضعیت صرافی"""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    ERROR = "error"
    MAINTENANCE = "maintenance"

class DataType(Enum):
    """نوع داده"""
    TICK = "tick"
    QUOTE = "quote"
    TRADE = "trade"
    ORDERBOOK = "orderbook"
    CANDLE = "candle"
    NEWS = "news"

@dataclass
class MarketData:
    """داده بازار"""
    symbol: str
    exchange_id: str
    data_type: DataType
    timestamp: datetime
    bid: Optional[Decimal] = None
    ask: Optional[Decimal] = None
    last: Optional[Decimal] = None
    volume: Optional[Decimal] = None
    high: Optional[Decimal] = None
    low: Optional[Decimal] = None
    open: Optional[Decimal] = None
    close: Optional[Decimal] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        # Ensure decimal types
        for field_name in ['bid', 'ask', 'last', 'volume', 'high', 'low', 'open', 'close']:
            value = getattr(self, field_name)
            if value is not None:
                setattr(self, field_name, Decimal(str(value)))

@dataclass
class ExchangeInfo:
    """اطلاعات صرافی"""
    exchange_id: str
    name: str
    status: ExchangeStatus
    last_update: datetime
    latency: float = 0.0
    symbols_count: int = 0
    orders_count: int = 0
    volume_24h: Decimal = Decimal('0')
    uptime: float = 0.0
    error_message: str = ""

class BaseExchange(ABC):
    """کلاس پایه صرافی"""
    
    def __init__(self, config: ExchangeConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{config.exchange_id}")
        self.status = ExchangeStatus.DISCONNECTED
        self.last_update = datetime.now()
        self.session: Optional[aiohttp.ClientSession] = None
        self.websocket: Optional[Any] = None
        self.rate_limiter = {}
        self.latency_history = []
        self.max_latency_history = 100
        
        # Callbacks
        self.data_callbacks: Dict[DataType, List[Callable]] = {
            data_type: [] for data_type in DataType
        }
        self.order_callbacks: List[Callable] = []
        self.connection_callbacks: List[Callable] = []
        
        # Statistics
        self.stats = {
            "requests_sent": 0,
            "responses_received": 0,
            "errors_count": 0,
            "data_packets_received": 0,
            "orders_sent": 0,
            "orders_filled": 0,
            "uptime_start": datetime.now(),
            "last_error": None
        }
    
    @abstractmethod
    async def connect(self) -> bool:
        """اتصال به صرافی"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """قطع اتصال"""
        pass
    
    @abstractmethod
    async def get_symbols(self) -> List[str]:
        """دریافت نمادهای موجود"""
        pass
    
    @abstractmethod
    async def get_market_data(self, symbol: str, data_type: DataType) -> Optional[MarketData]:
        """دریافت داده بازار"""
        pass
    
    @abstractmethod
    async def submit_order(self, order: Order) -> bool:
        """ارسال سفارش"""
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str) -> bool:
        """لغو سفارش"""
        pass
    
    @abstractmethod
    async def get_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """وضعیت سفارش"""
        pass
    
    async def check_connection(self) -> bool:
        """بررسی اتصال"""
        try:
            # Simple connectivity check
            if self.session and not self.session.closed:
                start_time = time.time()
                async with self.session.get(f"{self.config.api_url}/ping") as response:
                    if response.status == 200:
                        latency = (time.time() - start_time) * 1000  # ms
                        self.update_latency(latency)
                        return True
        except Exception as e:
            self.logger.error(f"Connection check failed: {e}")
        return False
    
    def update_latency(self, latency: float):
        """بروزرسانی latency"""
        self.latency_history.append(latency)
        if len(self.latency_history) > self.max_latency_history:
            self.latency_history.pop(0)
    
    def get_average_latency(self) -> float:
        """میانگین latency"""
        if not self.latency_history:
            return 0.0
        return sum(self.latency_history) / len(self.latency_history)
    
    def get_info(self) -> ExchangeInfo:
        """اطلاعات صرافی"""
        uptime = 0.0
        if self.stats["uptime_start"]:
            uptime = (datetime.now() - self.stats["uptime_start"]).total_seconds()
        
        return ExchangeInfo(
            exchange_id=self.config.exchange_id,
            name=self.config.name,
            status=self.status,
            last_update=self.last_update,
            latency=self.get_average_latency(),
            symbols_count=len(self.config.supported_symbols),
            orders_count=self.stats["orders_sent"],
            uptime=uptime
        )
    
    def add_data_callback(self, data_type: DataType, callback: Callable):
        """اضافه کردن callback داده"""
        self.data_callbacks[data_type].append(callback)
    
    def add_order_callback(self, callback: Callable):
        """اضافه کردن callback سفارش"""
        self.order_callbacks.append(callback)
    
    def add_connection_callback(self, callback: Callable):
        """اضافه کردن callback اتصال"""
        self.connection_callbacks.append(callback)
    
    async def _notify_data_callbacks(self, data: MarketData):
        """اطلاع‌رسانی callbacks داده"""
        for callback in self.data_callbacks[data.data_type]:
            try:
                await callback(data)
            except Exception as e:
                self.logger.error(f"Data callback error: {e}")
    
    async def _notify_order_callbacks(self, order: Order, fill: Optional[Fill] = None):
        """اطلاع‌رسانی callbacks سفارش"""
        for callback in self.order_callbacks:
            try:
                await callback(order, fill)
            except Exception as e:
                self.logger.error(f"Order callback error: {e}")
    
    async def _notify_connection_callbacks(self, status: ExchangeStatus):
        """اطلاع‌رسانی callbacks اتصال"""
        for callback in self.connection_callbacks:
            try:
                await callback(self.config.exchange_id, status)
            except Exception as e:
                self.logger.error(f"Connection callback error: {e}")

class ForexExchange(BaseExchange):
    """صرافی فارکس"""
    
    def __init__(self, config: ExchangeConfig):
        super().__init__(config)
        self.major_pairs = [
            'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD'
        ]
        self.config.supported_symbols.extend(self.major_pairs)
    
    async def connect(self) -> bool:
        """اتصال به صرافی فارکس"""
        try:
            self.status = ExchangeStatus.CONNECTING
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            )
            
            # Test connection
            if await self.check_connection():
                self.status = ExchangeStatus.CONNECTED
                self.last_update = datetime.now()
                await self._notify_connection_callbacks(self.status)
                self.logger.info(f"Connected to {self.config.name}")
                return True
            else:
                self.status = ExchangeStatus.ERROR
                return False
                
        except Exception as e:
            self.status = ExchangeStatus.ERROR
            self.logger.error(f"Connection failed: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """قطع اتصال"""
        try:
            if self.session:
                await self.session.close()
            self.status = ExchangeStatus.DISCONNECTED
            await self._notify_connection_callbacks(self.status)
            self.logger.info(f"Disconnected from {self.config.name}")
            return True
        except Exception as e:
            self.logger.error(f"Disconnect error: {e}")
            return False
    
    async def get_symbols(self) -> List[str]:
        """دریافت نمادهای فارکس"""
        return self.config.supported_symbols
    
    async def get_market_data(self, symbol: str, data_type: DataType) -> Optional[MarketData]:
        """دریافت داده بازار فارکس"""
        try:
            # Simulate market data
            import random
            
            base_prices = {
                'EURUSD': 1.0950, 'GBPUSD': 1.2500, 'USDJPY': 110.50,
                'USDCHF': 0.9200, 'AUDUSD': 0.7300, 'USDCAD': 1.2600,
                'NZDUSD': 0.6800
            }
            
            if symbol not in base_prices:
                return None
            
            base_price = base_prices[symbol]
            spread = base_price * 0.0001  # 1 pip spread
            
            bid = Decimal(str(base_price - spread/2 + random.uniform(-0.001, 0.001)))
            ask = Decimal(str(base_price + spread/2 + random.uniform(-0.001, 0.001)))
            
            data = MarketData(
                symbol=symbol,
                exchange_id=self.config.exchange_id,
                data_type=data_type,
                timestamp=datetime.now(),
                bid=bid,
                ask=ask,
                last=(bid + ask) / 2,
                volume=Decimal(str(random.randint(1000, 10000)))
            )
            
            await self._notify_data_callbacks(data)
            return data
            
        except Exception as e:
            self.logger.error(f"Market data error: {e}")
            return None
    
    async def submit_order(self, order: Order) -> bool:
        """ارسال سفارش فارکس"""
        try:
            # Simulate order submission
            self.stats["orders_sent"] += 1
            
            # Simulate order processing delay
            await asyncio.sleep(0.1)
            
            # Simulate order fill
            if order.order_type == OrderType.MARKET:
                # Market orders fill immediately
                market_data = await self.get_market_data(order.symbol, DataType.QUOTE)
                if market_data:
                    fill_price = market_data.ask if order.side == OrderSide.BUY else market_data.bid
                    fill = Fill(
                        fill_id=f"fill_{order.order_id}",
                        order_id=order.order_id,
                        price=fill_price,
                        quantity=order.quantity,
                        timestamp=datetime.now(),
                        exchange=self.config.exchange_id
                    )
                    await self._notify_order_callbacks(order, fill)
                    self.stats["orders_filled"] += 1
            
            self.logger.info(f"Order submitted: {order.order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Order submission error: {e}")
            return False
    
    async def cancel_order(self, order_id: str) -> bool:
        """لغو سفارش"""
        try:
            # Simulate order cancellation
            await asyncio.sleep(0.05)
            self.logger.info(f"Order cancelled: {order_id}")
            return True
        except Exception as e:
            self.logger.error(f"Order cancellation error: {e}")
            return False
    
    async def get_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """وضعیت سفارش"""
        # Simulate order status lookup
        return OrderStatus.FILLED  # Simplified

class CryptoExchange(BaseExchange):
    """صرافی رمزارز"""
    
    def __init__(self, config: ExchangeConfig):
        super().__init__(config)
        self.crypto_pairs = [
            'BTCUSD', 'ETHUSD', 'XRPUSD', 'ADAUSD', 'DOTUSD', 'LINKUSD'
        ]
        self.config.supported_symbols.extend(self.crypto_pairs)
    
    async def connect(self) -> bool:
        """اتصال به صرافی رمزارز"""
        try:
            self.status = ExchangeStatus.CONNECTING
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            )
            
            # Test connection (simulate)
            await asyncio.sleep(0.1)
            self.status = ExchangeStatus.CONNECTED
            self.last_update = datetime.now()
            await self._notify_connection_callbacks(self.status)
            self.logger.info(f"Connected to {self.config.name}")
            return True
            
        except Exception as e:
            self.status = ExchangeStatus.ERROR
            self.logger.error(f"Connection failed: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """قطع اتصال"""
        try:
            if self.session:
                await self.session.close()
            self.status = ExchangeStatus.DISCONNECTED
            await self._notify_connection_callbacks(self.status)
            self.logger.info(f"Disconnected from {self.config.name}")
            return True
        except Exception as e:
            self.logger.error(f"Disconnect error: {e}")
            return False
    
    async def get_symbols(self) -> List[str]:
        """دریافت نمادهای رمزارز"""
        return self.config.supported_symbols
    
    async def get_market_data(self, symbol: str, data_type: DataType) -> Optional[MarketData]:
        """دریافت داده بازار رمزارز"""
        try:
            # Simulate crypto market data
            import random
            
            base_prices = {
                'BTCUSD': 45000, 'ETHUSD': 3000, 'XRPUSD': 0.60,
                'ADAUSD': 0.45, 'DOTUSD': 25.0, 'LINKUSD': 15.0
            }
            
            if symbol not in base_prices:
                return None
            
            base_price = base_prices[symbol]
            spread = base_price * 0.001  # 0.1% spread
            
            bid = Decimal(str(base_price - spread/2 + random.uniform(-base_price*0.01, base_price*0.01)))
            ask = Decimal(str(base_price + spread/2 + random.uniform(-base_price*0.01, base_price*0.01)))
            
            data = MarketData(
                symbol=symbol,
                exchange_id=self.config.exchange_id,
                data_type=data_type,
                timestamp=datetime.now(),
                bid=bid,
                ask=ask,
                last=(bid + ask) / 2,
                volume=Decimal(str(random.randint(100, 1000)))
            )
            
            await self._notify_data_callbacks(data)
            return data
            
        except Exception as e:
            self.logger.error(f"Market data error: {e}")
            return None
    
    async def submit_order(self, order: Order) -> bool:
        """ارسال سفارش رمزارز"""
        try:
            # Simulate order submission
            self.stats["orders_sent"] += 1
            
            # Simulate network delay
            await asyncio.sleep(0.2)
            
            # Simulate order processing
            if order.order_type == OrderType.MARKET:
                market_data = await self.get_market_data(order.symbol, DataType.QUOTE)
                if market_data:
                    fill_price = market_data.ask if order.side == OrderSide.BUY else market_data.bid
                    fill = Fill(
                        fill_id=f"fill_{order.order_id}",
                        order_id=order.order_id,
                        price=fill_price,
                        quantity=order.quantity,
                        timestamp=datetime.now(),
                        exchange=self.config.exchange_id
                    )
                    await self._notify_order_callbacks(order, fill)
                    self.stats["orders_filled"] += 1
            
            self.logger.info(f"Crypto order submitted: {order.order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Order submission error: {e}")
            return False
    
    async def cancel_order(self, order_id: str) -> bool:
        """لغو سفارش"""
        try:
            await asyncio.sleep(0.1)
            self.logger.info(f"Crypto order cancelled: {order_id}")
            return True
        except Exception as e:
            self.logger.error(f"Order cancellation error: {e}")
            return False
    
    async def get_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """وضعیت سفارش"""
        return OrderStatus.FILLED  # Simplified

class MultiExchangeManager:
    """مدیر چند صرافی"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.exchanges: Dict[str, BaseExchange] = {}
        self.exchange_configs: Dict[str, ExchangeConfig] = {}
        self.data_aggregator = DataAggregator()
        self.order_router = OrderRouter()
        self.connection_monitor = ConnectionMonitor()
        
        # Global callbacks
        self.global_data_callbacks: Dict[DataType, List[Callable]] = {
            data_type: [] for data_type in DataType
        }
        self.global_order_callbacks: List[Callable] = []
        
        # Statistics
        self.stats = {
            "total_exchanges": 0,
            "connected_exchanges": 0,
            "total_symbols": 0,
            "total_orders": 0,
            "data_packets_processed": 0,
            "arbitrage_opportunities": 0
        }
        
        # Running flag
        self.running = False
    
    def add_exchange(self, config: ExchangeConfig) -> bool:
        """اضافه کردن صرافی"""
        try:
            # Create exchange instance based on type
            if config.exchange_type == ExchangeType.FOREX:
                exchange = ForexExchange(config)
            elif config.exchange_type == ExchangeType.CRYPTO:
                exchange = CryptoExchange(config)
            else:
                self.logger.error(f"Unsupported exchange type: {config.exchange_type}")
                return False
            
            # Add callbacks
            exchange.add_data_callback(DataType.QUOTE, self._handle_market_data)
            exchange.add_order_callback(self._handle_order_update)
            exchange.add_connection_callback(self._handle_connection_update)
            
            # Store exchange
            self.exchanges[config.exchange_id] = exchange
            self.exchange_configs[config.exchange_id] = config
            self.stats["total_exchanges"] += 1
            
            self.logger.info(f"Exchange added: {config.name} ({config.exchange_id})")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add exchange: {e}")
            return False
    
    async def connect_all(self) -> Dict[str, bool]:
        """اتصال به همه صرافی‌ها"""
        results = {}
        
        for exchange_id, exchange in self.exchanges.items():
            if self.exchange_configs[exchange_id].enabled:
                try:
                    result = await exchange.connect()
                    results[exchange_id] = result
                    if result:
                        self.stats["connected_exchanges"] += 1
                except Exception as e:
                    self.logger.error(f"Connection failed for {exchange_id}: {e}")
                    results[exchange_id] = False
        
        return results
    
    async def disconnect_all(self) -> Dict[str, bool]:
        """قطع اتصال همه صرافی‌ها"""
        results = {}
        
        for exchange_id, exchange in self.exchanges.items():
            try:
                result = await exchange.disconnect()
                results[exchange_id] = result
                if result:
                    self.stats["connected_exchanges"] -= 1
            except Exception as e:
                self.logger.error(f"Disconnect failed for {exchange_id}: {e}")
                results[exchange_id] = False
        
        return results
    
    async def get_market_data(self, symbol: str, data_type: DataType, 
                            exchange_id: Optional[str] = None) -> Dict[str, Optional[MarketData]]:
        """دریافت داده بازار"""
        results = {}
        
        if exchange_id:
            # Specific exchange
            exchange = self.exchanges.get(exchange_id)
            if exchange:
                data = await exchange.get_market_data(symbol, data_type)
                results[exchange_id] = data
        else:
            # All exchanges
            for exchange_id, exchange in self.exchanges.items():
                if exchange.status == ExchangeStatus.CONNECTED:
                    if symbol in exchange.config.supported_symbols:
                        data = await exchange.get_market_data(symbol, data_type)
                        results[exchange_id] = data
        
        return results
    
    async def submit_order(self, order: Order, exchange_id: Optional[str] = None) -> Dict[str, bool]:
        """ارسال سفارش"""
        results = {}
        
        if exchange_id:
            # Specific exchange
            exchange = self.exchanges.get(exchange_id)
            if exchange and exchange.status == ExchangeStatus.CONNECTED:
                result = await exchange.submit_order(order)
                results[exchange_id] = result
                if result:
                    self.stats["total_orders"] += 1
        else:
            # Route to best exchange
            best_exchange_id = await self.order_router.route_order(order, self.exchanges)
            if best_exchange_id:
                exchange = self.exchanges[best_exchange_id]
                result = await exchange.submit_order(order)
                results[best_exchange_id] = result
                if result:
                    self.stats["total_orders"] += 1
        
        return results
    
    async def cancel_order(self, order_id: str, exchange_id: str) -> bool:
        """لغو سفارش"""
        exchange = self.exchanges.get(exchange_id)
        if exchange:
            return await exchange.cancel_order(order_id)
        return False
    
    def get_exchange_info(self, exchange_id: Optional[str] = None) -> Dict[str, ExchangeInfo]:
        """اطلاعات صرافی"""
        results = {}
        
        if exchange_id:
            exchange = self.exchanges.get(exchange_id)
            if exchange:
                results[exchange_id] = exchange.get_info()
        else:
            for exchange_id, exchange in self.exchanges.items():
                results[exchange_id] = exchange.get_info()
        
        return results
    
    def get_all_symbols(self) -> Dict[str, List[str]]:
        """همه نمادهای موجود"""
        results = {}
        
        for exchange_id, exchange in self.exchanges.items():
            results[exchange_id] = exchange.config.supported_symbols
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """آمار کلی"""
        # Update connected exchanges count
        connected = sum(1 for ex in self.exchanges.values() if ex.status == ExchangeStatus.CONNECTED)
        self.stats["connected_exchanges"] = connected
        
        # Update total symbols
        all_symbols = set()
        for exchange in self.exchanges.values():
            all_symbols.update(exchange.config.supported_symbols)
        self.stats["total_symbols"] = len(all_symbols)
        
        return self.stats.copy()
    
    def add_global_data_callback(self, data_type: DataType, callback: Callable):
        """اضافه کردن callback کلی داده"""
        self.global_data_callbacks[data_type].append(callback)
    
    def add_global_order_callback(self, callback: Callable):
        """اضافه کردن callback کلی سفارش"""
        self.global_order_callbacks.append(callback)
    
    async def _handle_market_data(self, data: MarketData):
        """مدیریت داده بازار"""
        self.stats["data_packets_processed"] += 1
        
        # Add to aggregator
        self.data_aggregator.add_data(data)
        
        # Notify global callbacks
        for callback in self.global_data_callbacks[data.data_type]:
            try:
                await callback(data)
            except Exception as e:
                self.logger.error(f"Global data callback error: {e}")
    
    async def _handle_order_update(self, order: Order, fill: Optional[Fill] = None):
        """مدیریت بروزرسانی سفارش"""
        # Notify global callbacks
        for callback in self.global_order_callbacks:
            try:
                await callback(order, fill)
            except Exception as e:
                self.logger.error(f"Global order callback error: {e}")
    
    async def _handle_connection_update(self, exchange_id: str, status: ExchangeStatus):
        """مدیریت بروزرسانی اتصال"""
        self.logger.info(f"Exchange {exchange_id} status changed to {status.value}")
        
        # Update connection monitor
        self.connection_monitor.update_status(exchange_id, status)

class DataAggregator:
    """تجمیع‌کننده داده"""
    
    def __init__(self):
        self.data_buffer: Dict[str, List[MarketData]] = {}
        self.max_buffer_size = 1000
        
    def add_data(self, data: MarketData):
        """اضافه کردن داده"""
        key = f"{data.symbol}_{data.exchange_id}"
        
        if key not in self.data_buffer:
            self.data_buffer[key] = []
        
        self.data_buffer[key].append(data)
        
        # Keep buffer size under control
        if len(self.data_buffer[key]) > self.max_buffer_size:
            self.data_buffer[key].pop(0)
    
    def get_latest_data(self, symbol: str, exchange_id: str) -> Optional[MarketData]:
        """آخرین داده"""
        key = f"{symbol}_{exchange_id}"
        buffer = self.data_buffer.get(key, [])
        return buffer[-1] if buffer else None
    
    def get_best_quote(self, symbol: str) -> Optional[Dict[str, MarketData]]:
        """بهترین قیمت"""
        best_bid = None
        best_ask = None
        
        for key, buffer in self.data_buffer.items():
            if symbol in key and buffer:
                data = buffer[-1]
                if data.bid and (not best_bid or data.bid > best_bid.bid):
                    best_bid = data
                if data.ask and (not best_ask or data.ask < best_ask.ask):
                    best_ask = data
        
        return {"best_bid": best_bid, "best_ask": best_ask} if best_bid or best_ask else None

class OrderRouter:
    """مسیریاب سفارش"""
    
    def __init__(self):
        self.routing_rules = {
            # Default routing based on exchange type
            ExchangeType.FOREX: ["forex_exchange"],
            ExchangeType.CRYPTO: ["crypto_exchange"],
        }
    
    async def route_order(self, order: Order, exchanges: Dict[str, BaseExchange]) -> Optional[str]:
        """مسیریابی سفارش"""
        # Find suitable exchanges
        suitable_exchanges = []
        
        for exchange_id, exchange in exchanges.items():
            if (exchange.status == ExchangeStatus.CONNECTED and 
                order.symbol in exchange.config.supported_symbols):
                suitable_exchanges.append((exchange_id, exchange))
        
        if not suitable_exchanges:
            return None
        
        # Select best exchange (simple logic: lowest latency)
        best_exchange = min(suitable_exchanges, key=lambda x: x[1].get_average_latency())
        return best_exchange[0]

class ConnectionMonitor:
    """نظارت اتصال"""
    
    def __init__(self):
        self.status_history: Dict[str, List[tuple]] = {}
        self.max_history_size = 100
        
    def update_status(self, exchange_id: str, status: ExchangeStatus):
        """بروزرسانی وضعیت"""
        if exchange_id not in self.status_history:
            self.status_history[exchange_id] = []
        
        self.status_history[exchange_id].append((datetime.now(), status))
        
        # Keep history size under control
        if len(self.status_history[exchange_id]) > self.max_history_size:
            self.status_history[exchange_id].pop(0)
    
    def get_uptime(self, exchange_id: str) -> float:
        """محاسبه uptime"""
        history = self.status_history.get(exchange_id, [])
        if not history:
            return 0.0
        
        total_time = 0
        connected_time = 0
        
        for i in range(1, len(history)):
            prev_time, prev_status = history[i-1]
            curr_time, _ = history[i]
            
            duration = (curr_time - prev_time).total_seconds()
            total_time += duration
            
            if prev_status == ExchangeStatus.CONNECTED:
                connected_time += duration
        
        return (connected_time / total_time * 100) if total_time > 0 else 0.0

# Global instance
multi_exchange_manager = MultiExchangeManager()

@asynccontextmanager
async def exchange_session():
    """Context manager برای جلسه صرافی"""
    try:
        await multi_exchange_manager.connect_all()
        yield multi_exchange_manager
    finally:
        await multi_exchange_manager.disconnect_all()

# Test and examples
async def test_multi_exchange():
    """تست سیستم چند صرافی"""
    print("🌐 Testing Multi-Exchange System...")
    
    # Test 1: Add exchanges
    forex_config = ExchangeConfig(
        exchange_id="forex_exchange",
        name="Forex Exchange",
        exchange_type=ExchangeType.FOREX,
        api_url="https://api-demo.fxcm.com"
    )
    
    crypto_config = ExchangeConfig(
        exchange_id="crypto_exchange",
        name="Crypto Exchange",
        exchange_type=ExchangeType.CRYPTO,
        api_url="https://api.crypto.com"
    )
    
    manager = MultiExchangeManager()
    
    # Add exchanges
    result1 = manager.add_exchange(forex_config)
    result2 = manager.add_exchange(crypto_config)
    
    print(f"✓ Forex exchange added: {result1}")
    print(f"✓ Crypto exchange added: {result2}")
    
    # Test 2: Connect to exchanges
    print("\n2. Connecting to exchanges...")
    connection_results = await manager.connect_all()
    
    for exchange_id, result in connection_results.items():
        print(f"   ✓ {exchange_id}: {result}")
    
    # Test 3: Get market data
    print("\n3. Getting market data...")
    
    forex_data = await manager.get_market_data("EURUSD", DataType.QUOTE)
    crypto_data = await manager.get_market_data("BTCUSD", DataType.QUOTE)
    
    for exchange_id, data in forex_data.items():
        if data:
            print(f"   ✓ {exchange_id} EURUSD: {data.bid}/{data.ask}")
    
    for exchange_id, data in crypto_data.items():
        if data:
            print(f"   ✓ {exchange_id} BTCUSD: {data.bid}/{data.ask}")
    
    # Test 4: Exchange info
    print("\n4. Exchange information...")
    
    exchange_info = manager.get_exchange_info()
    for exchange_id, info in exchange_info.items():
        print(f"   ✓ {exchange_id}: {info.status.value}, latency: {info.latency:.2f}ms")
    
    # Test 5: Statistics
    print("\n5. System statistics...")
    
    stats = manager.get_statistics()
    print(f"   ✓ Total exchanges: {stats['total_exchanges']}")
    print(f"   ✓ Connected exchanges: {stats['connected_exchanges']}")
    print(f"   ✓ Total symbols: {stats['total_symbols']}")
    print(f"   ✓ Data packets processed: {stats['data_packets_processed']}")
    
    # Test 6: Disconnect
    print("\n6. Disconnecting...")
    
    disconnect_results = await manager.disconnect_all()
    for exchange_id, result in disconnect_results.items():
        print(f"   ✓ {exchange_id} disconnected: {result}")
    
    print("\n✅ Multi-Exchange System test completed!")

if __name__ == "__main__":
    asyncio.run(test_multi_exchange()) 