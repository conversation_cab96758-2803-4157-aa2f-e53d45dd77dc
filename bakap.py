import os
import shutil
import logging
import re

# تنظیم لاگینگ
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


def copy_project_files(source_dir, target_dir):
    """کپی کردن فایل‌های مهم پروژه به دایرکتوری جدید"""
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)

    # فایل‌ها و دایرکتوری‌هایی که می‌خواهیم کپی کنیم
    extensions = [".py", ".csv"]
    ignored_dirs = ["__pycache__", "venv", ".git"]

    for root, dirs, files in os.walk(source_dir):
        dirs[:] = [d for d in dirs if d not in ignored_dirs]
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                source_path = os.path.join(root, file)
                target_path = os.path.join(
                    target_dir, os.path.relpath(source_path, source_dir)
                )
                os.makedirs(os.path.dirname(target_path), exist_ok=True)
                try:
                    shutil.copy2(source_path, target_path)
                    logging.info(f"Copied {source_path} to {target_path}")
                except Exception as e:
                    logging.error(f"Failed to copy {source_path}: {e}")


def analyze_project(source_dir, summary_file):
    """تحلیل پروژه و ذخیره اطلاعات در فایل خلاصه"""
    with open(summary_file, "w", encoding="utf-8") as f:
        f.write("### Project Summary\n\n")

        # ساختار پوشه‌ها
        f.write("#### Directory Structure\n")
        for root, dirs, files in os.walk(source_dir):
            level = root.replace(source_dir, "").count(os.sep)
            indent = " " * 4 * level
            f.write(f"{indent}{os.path.basename(root)}/\n")
            subindent = " " * 4 * (level + 1)
            for file in files:
                if any(file.endswith(ext) for ext in [".py", ".csv"]):
                    f.write(f"{subindent}{file}\n")
        f.write("\n")

        # استخراج پارامترها و مدل‌ها از فایل‌ها
        f.write("#### Extracted Parameters and Models\n")
        for root, _, files in os.walk(source_dir):
            for file in files:
                if file.endswith(".py"):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, "r", encoding="utf-8") as content_file:
                            content = content_file.read()
                            # جستجوی پارامترها (مثل dictها)
                            params = re.findall(
                                r'\{[\'"]\w+[\'"]\s*:\s*[\'"]?\w+[\'"]?\s*(?:,[\s]*[\'"]\w+[\'"]\s*:\s*[\'"]?\w+[\'"]?\s*)*\}',
                                content,
                            )
                            if params:
                                f.write(f"From {file_path}:\n")
                                for param in params:
                                    f.write(f"  - Parameter: {param}\n")

                            # جستجوی مدل‌ها (مثل PPO یا نام کلاس‌ها)
                            models = re.findall(
                                r"(PPO|TradingEnv|PH3Optimizer|Model)", content
                            )
                            if models:
                                f.write(f"From {file_path}:\n")
                                for model in set(models):
                                    f.write(f"  - Model: {model}\n")
                    except UnicodeDecodeError:
                        logging.warning(
                            f"Skipped {file_path} due to encoding error (not UTF-8)"
                        )
                    except Exception as e:
                        logging.error(f"Error reading {file_path}: {e}")
        f.write("\n")

        # هدف پروژه (بر اساس حدس از کد)
        f.write("#### Project Goal\n")
        f.write(
            "Based on the code structure, the project appears to be a trading optimization system using reinforcement learning.\n"
        )
        f.write(
            "It processes financial data (e.g., GBPUSD, AUDUSD, USDCAD) from CSV files, applies indicators (RSI, MA, Stochastic),\n"
        )
        f.write(
            "and trains models (e.g., PPO) to optimize trading strategies (swing, scalping) with parameters like lot_size, stop_loss, and take_profit.\n"
        )
        f.write(
            "The goal seems to be to find the best trading models and evaluate their performance.\n\n"
        )

        # وضعیت آموزش (بر اساس فایل‌ها)
        f.write("#### Training Status\n")
        f.write(
            "Training status cannot be determined automatically without execution logs.\n"
        )
        f.write(
            "Check for existing model files (e.g., .zip) or logs in the project directory to assess training progress.\n"
        )


# تنظیم مسیرها
project_dir = r"C:\Users\<USER>\Documents\project"
backup_dir = r"C:\Users\<USER>\Documents\project_backup"
summary_file = os.path.join(backup_dir, "project_summary.txt")

# اجرای کپی و تحلیل
copy_project_files(project_dir, backup_dir)
analyze_project(project_dir, summary_file)
logging.info(f"Backup created at {backup_dir} and summary saved to {summary_file}")
