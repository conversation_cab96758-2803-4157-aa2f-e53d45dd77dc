"""
تست واقعی معاملاتی مدل‌های Plutus
Real Trading Scenario Test for Plutus Models
"""

import pandas as pd
import numpy as np
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
import logging

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from test_plutus_models_comprehensive import PlutusModelTester

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealTradingScenarioTester:
    """
    تست‌کننده سناریوهای واقعی معاملاتی
    Real trading scenario tester
    """
    
    def __init__(self):
        self.model_tester = PlutusModelTester()
        self.trading_results = {}
        
        # Trading parameters
        self.initial_balance = 10000  # $10,000 starting balance
        self.risk_per_trade = 0.02    # 2% risk per trade
        self.stop_loss_pct = 0.01     # 1% stop loss
        self.take_profit_pct = 0.02   # 2% take profit
        
    def simulate_trading_period(self, 
                               symbol: str, 
                               timeframe: str = "H1", 
                               periods: int = 100) -> dict:
        """
        شبیه‌سازی دوره معاملاتی
        Simulate trading period
        """
        try:
            logger.info(f"Simulating trading for {symbol} {timeframe} over {periods} periods")
            
            # Load historical data
            price_data = self.model_tester.load_real_project_data(symbol, timeframe)
            
            if price_data.empty or len(price_data) < periods + 50:
                return {"error": f"Insufficient data for {symbol}"}
            
            # Initialize trading accounts
            accounts = {
                "chronos": {
                    "balance": self.initial_balance,
                    "trades": [],
                    "open_positions": [],
                    "total_trades": 0,
                    "winning_trades": 0,
                    "losing_trades": 0
                },
                "fingpt": {
                    "balance": self.initial_balance,
                    "trades": [],
                    "open_positions": [],
                    "total_trades": 0,
                    "winning_trades": 0,
                    "losing_trades": 0
                },
                "combined": {
                    "balance": self.initial_balance,
                    "trades": [],
                    "open_positions": [],
                    "total_trades": 0,
                    "winning_trades": 0,
                    "losing_trades": 0
                }
            }
            
            # Simulate trading over time
            for i in range(50, len(price_data) - 50, 24):  # Every 24 hours
                if len(accounts["chronos"]["trades"]) >= periods:
                    break
                
                # Get training data up to current point
                train_data = price_data.iloc[:i]
                current_price = price_data.iloc[i]['close']
                
                # Get predictions from both models
                chronos_pred = self.model_tester.test_chronos_model_offline(train_data, symbol)
                fingpt_pred = self.model_tester.test_fingpt_model_offline(train_data, symbol)
                
                # Execute trades based on predictions
                if not chronos_pred.get("error"):
                    self.execute_trade(accounts["chronos"], chronos_pred, current_price, 
                                     price_data.iloc[i:i+24], "chronos")
                
                if not fingpt_pred.get("error"):
                    self.execute_trade(accounts["fingpt"], fingpt_pred, current_price, 
                                     price_data.iloc[i:i+24], "fingpt")
                
                # Combined strategy (both models must agree)
                if (not chronos_pred.get("error") and not fingpt_pred.get("error")):
                    combined_pred = self.combine_predictions(chronos_pred, fingpt_pred)
                    if combined_pred:
                        self.execute_trade(accounts["combined"], combined_pred, current_price, 
                                         price_data.iloc[i:i+24], "combined")
            
            # Calculate final performance metrics
            results = {}
            for strategy, account in accounts.items():
                results[strategy] = self.calculate_performance_metrics(account)
            
            return {
                "symbol": symbol,
                "timeframe": timeframe,
                "periods_tested": periods,
                "results": results,
                "test_completed": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in trading simulation: {str(e)}")
            return {"error": str(e)}
    
    def execute_trade(self, account: dict, prediction: dict, entry_price: float, 
                     future_data: pd.DataFrame, strategy: str):
        """
        اجرای معامله بر اساس پیش‌بینی
        Execute trade based on prediction
        """
        try:
            # Determine trade direction
            if strategy == "chronos":
                signal = prediction["signals"]["trend"]
                confidence = prediction["signals"]["confidence"]
            elif strategy == "fingpt":
                signal = "bullish" if prediction["prediction"] == "up" else \
                        "bearish" if prediction["prediction"] == "down" else "neutral"
                confidence = prediction["confidence"]
            else:  # combined
                signal = prediction["trend"]
                confidence = prediction["confidence"]
            
            # Skip neutral signals
            if signal == "neutral" or signal == "sideways":
                return
            
            # Skip low confidence trades
            if confidence < 0.6:
                return
            
            # Calculate position size based on risk
            risk_amount = account["balance"] * self.risk_per_trade
            position_size = risk_amount / (entry_price * self.stop_loss_pct)
            
            # Skip if position size is too small
            if position_size * entry_price < 100:  # Minimum $100 trade
                return
            
            # Create trade
            trade = {
                "entry_time": future_data.index[0] if len(future_data) > 0 else datetime.now(),
                "entry_price": entry_price,
                "direction": signal,
                "position_size": position_size,
                "confidence": confidence,
                "stop_loss": entry_price * (1 - self.stop_loss_pct) if signal == "bullish" else \
                           entry_price * (1 + self.stop_loss_pct),
                "take_profit": entry_price * (1 + self.take_profit_pct) if signal == "bullish" else \
                             entry_price * (1 - self.take_profit_pct),
                "status": "open"
            }
            
            # Simulate trade outcome over future periods
            exit_price = entry_price
            exit_reason = "time_exit"
            
            for j, (timestamp, row) in enumerate(future_data.iterrows()):
                if j >= 24:  # Maximum 24 periods
                    break
                
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']
                
                # Check stop loss and take profit
                if signal == "bullish":
                    if low_price <= trade["stop_loss"]:
                        exit_price = trade["stop_loss"]
                        exit_reason = "stop_loss"
                        break
                    elif high_price >= trade["take_profit"]:
                        exit_price = trade["take_profit"]
                        exit_reason = "take_profit"
                        break
                else:  # bearish
                    if high_price >= trade["stop_loss"]:
                        exit_price = trade["stop_loss"]
                        exit_reason = "stop_loss"
                        break
                    elif low_price <= trade["take_profit"]:
                        exit_price = trade["take_profit"]
                        exit_reason = "take_profit"
                        break
                
                exit_price = close_price
            
            # Calculate P&L
            if signal == "bullish":
                pnl = (exit_price - entry_price) * position_size
            else:
                pnl = (entry_price - exit_price) * position_size
            
            # Update trade
            trade.update({
                "exit_price": exit_price,
                "exit_reason": exit_reason,
                "pnl": pnl,
                "status": "closed"
            })
            
            # Update account
            account["balance"] += pnl
            account["trades"].append(trade)
            account["total_trades"] += 1
            
            if pnl > 0:
                account["winning_trades"] += 1
            else:
                account["losing_trades"] += 1
            
            logger.info(f"{strategy}: {signal} trade at {entry_price:.5f}, "
                       f"exit at {exit_price:.5f}, P&L: ${pnl:.2f}")
            
        except Exception as e:
            logger.error(f"Error executing trade: {str(e)}")
    
    def combine_predictions(self, chronos_pred: dict, fingpt_pred: dict) -> dict:
        """
        ترکیب پیش‌بینی‌های دو مدل
        Combine predictions from both models
        """
        try:
            chronos_trend = chronos_pred["signals"]["trend"]
            chronos_conf = chronos_pred["signals"]["confidence"]
            
            fingpt_prediction = fingpt_pred["prediction"]
            fingpt_conf = fingpt_pred["confidence"]
            
            # Convert FinGPT prediction to trend
            fingpt_trend = "bullish" if fingpt_prediction == "up" else \
                          "bearish" if fingpt_prediction == "down" else "neutral"
            
            # Only trade if both models agree
            if chronos_trend == fingpt_trend and chronos_trend != "neutral":
                # Average confidence
                avg_confidence = (chronos_conf + fingpt_conf) / 2
                
                return {
                    "trend": chronos_trend,
                    "confidence": avg_confidence
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error combining predictions: {str(e)}")
            return None
    
    def calculate_performance_metrics(self, account: dict) -> dict:
        """
        محاسبه معیارهای عملکرد
        Calculate performance metrics
        """
        try:
            trades = account["trades"]
            
            if not trades:
                return {
                    "total_return": 0,
                    "total_trades": 0,
                    "win_rate": 0,
                    "profit_factor": 0,
                    "max_drawdown": 0,
                    "sharpe_ratio": 0,
                    "final_balance": account["balance"]
                }
            
            # Basic metrics
            total_return = (account["balance"] - self.initial_balance) / self.initial_balance
            win_rate = account["winning_trades"] / account["total_trades"] if account["total_trades"] > 0 else 0
            
            # P&L analysis
            pnls = [trade["pnl"] for trade in trades]
            winning_trades_pnl = [pnl for pnl in pnls if pnl > 0]
            losing_trades_pnl = [abs(pnl) for pnl in pnls if pnl < 0]
            
            avg_win = np.mean(winning_trades_pnl) if winning_trades_pnl else 0
            avg_loss = np.mean(losing_trades_pnl) if losing_trades_pnl else 0
            
            profit_factor = (sum(winning_trades_pnl) / sum(losing_trades_pnl)) if losing_trades_pnl else float('inf')
            
            # Drawdown calculation
            balance_curve = [self.initial_balance]
            for trade in trades:
                balance_curve.append(balance_curve[-1] + trade["pnl"])
            
            peak = balance_curve[0]
            max_drawdown = 0
            
            for balance in balance_curve:
                if balance > peak:
                    peak = balance
                drawdown = (peak - balance) / peak
                max_drawdown = max(max_drawdown, drawdown)
            
            # Sharpe ratio (simplified)
            if len(pnls) > 1:
                avg_return = np.mean(pnls)
                std_return = np.std(pnls)
                sharpe_ratio = avg_return / std_return if std_return > 0 else 0
            else:
                sharpe_ratio = 0
            
            return {
                "total_return": total_return,
                "total_trades": account["total_trades"],
                "winning_trades": account["winning_trades"],
                "losing_trades": account["losing_trades"],
                "win_rate": win_rate,
                "avg_win": avg_win,
                "avg_loss": avg_loss,
                "profit_factor": profit_factor,
                "max_drawdown": max_drawdown,
                "sharpe_ratio": sharpe_ratio,
                "final_balance": account["balance"]
            }
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {str(e)}")
            return {"error": str(e)}
    
    def run_comprehensive_trading_test(self) -> dict:
        """
        اجرای تست جامع معاملاتی
        Run comprehensive trading test
        """
        logger.info("Starting comprehensive trading test")
        
        results = {
            "test_start": datetime.now().isoformat(),
            "trading_results": {},
            "summary": {}
        }
        
        # Test major currency pairs
        test_pairs = ["EURUSD", "GBPUSD", "USDJPY"]
        
        all_results = {"chronos": [], "fingpt": [], "combined": []}
        
        for symbol in test_pairs:
            logger.info(f"\n{'='*50}")
            logger.info(f"Testing {symbol}")
            logger.info(f"{'='*50}")
            
            try:
                trading_result = self.simulate_trading_period(symbol, "H1", 50)
                
                if not trading_result.get("error"):
                    results["trading_results"][symbol] = trading_result
                    
                    # Collect results for summary
                    for strategy in ["chronos", "fingpt", "combined"]:
                        if strategy in trading_result["results"]:
                            strategy_result = trading_result["results"][strategy]
                            if not strategy_result.get("error"):
                                all_results[strategy].append(strategy_result)
                
            except Exception as e:
                logger.error(f"Error testing {symbol}: {str(e)}")
                results["trading_results"][symbol] = {"error": str(e)}
        
        # Calculate summary statistics
        summary = {}
        for strategy, strategy_results in all_results.items():
            if strategy_results:
                summary[strategy] = {
                    "avg_return": np.mean([r["total_return"] for r in strategy_results]),
                    "avg_win_rate": np.mean([r["win_rate"] for r in strategy_results]),
                    "avg_profit_factor": np.mean([r["profit_factor"] for r in strategy_results if r["profit_factor"] != float('inf')]),
                    "avg_max_drawdown": np.mean([r["max_drawdown"] for r in strategy_results]),
                    "total_trades": sum([r["total_trades"] for r in strategy_results]),
                    "avg_sharpe_ratio": np.mean([r["sharpe_ratio"] for r in strategy_results])
                }
        
        results["summary"] = summary
        results["test_end"] = datetime.now().isoformat()
        
        return results
    
    def generate_trading_report(self, results: dict) -> str:
        """
        تولید گزارش معاملاتی
        Generate trading report
        """
        report = []
        report.append("=" * 80)
        report.append("PLUTUS MODELS TRADING PERFORMANCE REPORT")
        report.append("=" * 80)
        report.append(f"Test Period: {results.get('test_start', 'Unknown')} to {results.get('test_end', 'Unknown')}")
        report.append("")
        
        # Summary section
        summary = results.get("summary", {})
        
        report.append("OVERALL PERFORMANCE SUMMARY:")
        report.append("-" * 40)
        
        for strategy, metrics in summary.items():
            report.append(f"\n{strategy.upper()} STRATEGY:")
            report.append(f"  Average Return: {metrics.get('avg_return', 0):.2%}")
            report.append(f"  Average Win Rate: {metrics.get('avg_win_rate', 0):.1%}")
            report.append(f"  Average Profit Factor: {metrics.get('avg_profit_factor', 0):.2f}")
            report.append(f"  Average Max Drawdown: {metrics.get('avg_max_drawdown', 0):.1%}")
            report.append(f"  Total Trades: {metrics.get('total_trades', 0)}")
            report.append(f"  Average Sharpe Ratio: {metrics.get('avg_sharpe_ratio', 0):.2f}")
        
        # Detailed results by currency pair
        trading_results = results.get("trading_results", {})
        
        report.append("\n" + "=" * 80)
        report.append("DETAILED RESULTS BY CURRENCY PAIR:")
        report.append("=" * 80)
        
        for symbol, result in trading_results.items():
            if result.get("error"):
                report.append(f"\n{symbol}: ERROR - {result['error']}")
                continue
            
            report.append(f"\n{symbol}:")
            report.append("-" * 20)
            
            for strategy, metrics in result.get("results", {}).items():
                if metrics.get("error"):
                    report.append(f"  {strategy}: ERROR")
                    continue
                
                report.append(f"  {strategy.upper()}:")
                report.append(f"    Return: {metrics.get('total_return', 0):.2%}")
                report.append(f"    Trades: {metrics.get('total_trades', 0)}")
                report.append(f"    Win Rate: {metrics.get('win_rate', 0):.1%}")
                report.append(f"    Profit Factor: {metrics.get('profit_factor', 0):.2f}")
                report.append(f"    Max Drawdown: {metrics.get('max_drawdown', 0):.1%}")
                report.append(f"    Final Balance: ${metrics.get('final_balance', 0):.2f}")
        
        # Performance ranking
        report.append("\n" + "=" * 80)
        report.append("STRATEGY RANKING:")
        report.append("=" * 80)
        
        if summary:
            strategies = list(summary.keys())
            strategies.sort(key=lambda x: summary[x].get('avg_return', 0), reverse=True)
            
            for i, strategy in enumerate(strategies, 1):
                metrics = summary[strategy]
                report.append(f"{i}. {strategy.upper()}: {metrics.get('avg_return', 0):.2%} return, "
                             f"{metrics.get('avg_win_rate', 0):.1%} win rate")
        
        report.append("\n" + "=" * 80)
        report.append("END OF TRADING REPORT")
        report.append("=" * 80)
        
        return "\n".join(report)

def main():
    """
    اجرای تست اصلی
    Run main test
    """
    print("Starting Real Trading Scenario Test for Plutus Models")
    print("=" * 60)
    
    tester = RealTradingScenarioTester()
    
    # Run comprehensive trading test
    results = tester.run_comprehensive_trading_test()
    
    # Generate report
    report = tester.generate_trading_report(results)
    
    # Save results
    results_file = Path(__file__).parent / "plutus_trading_results.json"
    report_file = Path(__file__).parent / "plutus_trading_report.txt"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    with open(report_file, 'w') as f:
        f.write(report)
    
    print(report)
    print(f"\nResults saved to: {results_file}")
    print(f"Report saved to: {report_file}")
    
    # Print key conclusions
    print("\n" + "=" * 60)
    print("KEY CONCLUSIONS:")
    print("=" * 60)
    
    summary = results.get("summary", {})
    
    if "chronos" in summary and "fingpt" in summary:
        chronos_return = summary["chronos"].get("avg_return", 0)
        fingpt_return = summary["fingpt"].get("avg_return", 0)
        
        if chronos_return > fingpt_return:
            print(f"🏆 Chronos performed better: {chronos_return:.2%} vs {fingpt_return:.2%}")
        elif fingpt_return > chronos_return:
            print(f"🏆 FinGPT performed better: {fingpt_return:.2%} vs {chronos_return:.2%}")
        else:
            print("🤝 Both models performed similarly")
    
    if "combined" in summary:
        combined_return = summary["combined"].get("avg_return", 0)
        print(f"🤝 Combined strategy return: {combined_return:.2%}")
    
    print("\nTrading simulation completed!")

if __name__ == "__main__":
    main() 