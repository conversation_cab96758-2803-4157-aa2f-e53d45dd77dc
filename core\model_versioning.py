#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ Model Versioning System
سیستم مدیریت نسخه‌بندی مدل‌ها با MLflow Integration
"""

import os
import sys
import json
import pickle
import joblib
import hashlib
import threading
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from enum import Enum
from pathlib import Path
import logging
from contextlib import contextmanager

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.exceptions import TradingSystemError, ValidationError, ModelLoadError

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class ModelStatus(Enum):
    """وضعیت مدل"""
    TRAINING = "training"
    TRAINED = "trained"
    TESTING = "testing"
    VALIDATED = "validated"
    DEPLOYED = "deployed"
    RETIRED = "retired"
    FAILED = "failed"

class ModelStage(Enum):
    """مرحله مدل"""
    STAGING = "staging"
    PRODUCTION = "production"
    ARCHIVED = "archived"

class ModelType(Enum):
    """نوع مدل"""
    REGRESSION = "regression"
    CLASSIFICATION = "classification"
    CLUSTERING = "clustering"
    REINFORCEMENT_LEARNING = "reinforcement_learning"
    DEEP_LEARNING = "deep_learning"
    ENSEMBLE = "ensemble"
    CUSTOM = "custom"

class SerializationFormat(Enum):
    """فرمت سریال‌سازی"""
    PICKLE = "pickle"
    JOBLIB = "joblib"
    JSON = "json"
    ONNX = "onnx"
    TORCH = "torch"
    TENSORFLOW = "tensorflow"

@dataclass
class ModelMetrics:
    """معیارهای مدل"""
    accuracy: Optional[float] = None
    precision: Optional[float] = None
    recall: Optional[float] = None
    f1_score: Optional[float] = None
    auc_roc: Optional[float] = None
    mse: Optional[float] = None
    rmse: Optional[float] = None
    mae: Optional[float] = None
    r2_score: Optional[float] = None
    custom_metrics: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        result = asdict(self)
        # Remove None values
        return {k: v for k, v in result.items() if v is not None}

@dataclass
class ModelMetadata:
    """متادیتای مدل"""
    name: str
    version: str
    model_type: ModelType
    description: str = ""
    author: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    status: ModelStatus = ModelStatus.TRAINING
    stage: ModelStage = ModelStage.STAGING
    tags: List[str] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)
    metrics: ModelMetrics = field(default_factory=ModelMetrics)
    dependencies: List[str] = field(default_factory=list)
    training_data_hash: str = ""
    model_hash: str = ""
    file_path: str = ""
    file_size: int = 0
    serialization_format: SerializationFormat = SerializationFormat.PICKLE
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        result = asdict(self)
        # Convert datetime objects to strings
        result['created_at'] = self.created_at.isoformat()
        result['updated_at'] = self.updated_at.isoformat()
        result['status'] = self.status.value
        result['stage'] = self.stage.value
        result['model_type'] = self.model_type.value
        result['serialization_format'] = self.serialization_format.value
        return result

@dataclass
class ModelVersion:
    """نسخه مدل"""
    model_id: str
    version: str
    metadata: ModelMetadata
    model_object: Optional[Any] = None
    is_loaded: bool = False
    
    def get_full_name(self) -> str:
        """نام کامل مدل"""
        return f"{self.metadata.name}:v{self.version}"
    
    def get_model_path(self) -> str:
        """مسیر فایل مدل"""
        return self.metadata.file_path
    
    def calculate_hash(self) -> str:
        """محاسبه hash مدل"""
        if self.model_object is not None:
            # Calculate hash based on model object
            model_bytes = pickle.dumps(self.model_object)
            return hashlib.md5(model_bytes).hexdigest()
        elif self.metadata.file_path and os.path.exists(self.metadata.file_path):
            # Calculate hash based on file
            with open(self.metadata.file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        return ""

class ModelSerializer:
    """سریال‌ساز مدل"""
    
    @staticmethod
    def serialize(model: Any, file_path: str, format: SerializationFormat = SerializationFormat.PICKLE) -> bool:
        """سریال‌سازی مدل"""
        try:
            # Create directory if it doesn't exist
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            if format == SerializationFormat.PICKLE:
                with open(file_path, 'wb') as f:
                    pickle.dump(model, f)
            elif format == SerializationFormat.JOBLIB:
                joblib.dump(model, file_path)
            elif format == SerializationFormat.JSON:
                # For simple models that can be serialized to JSON
                with open(file_path, 'w') as f:
                    json.dump(model, f, indent=2)
            else:
                logger.warning(f"Unsupported serialization format: {format}")
                return False
            
            logger.info(f"Model serialized to {file_path} in {format.value} format")
            return True
            
        except Exception as e:
            logger.error(f"Model serialization failed: {e}")
            return False
    
    @staticmethod
    def deserialize(file_path: str, format: SerializationFormat = SerializationFormat.PICKLE) -> Optional[Any]:
        """دسریال‌سازی مدل"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"Model file not found: {file_path}")
                return None
            
            if format == SerializationFormat.PICKLE:
                with open(file_path, 'rb') as f:
                    return pickle.load(f)
            elif format == SerializationFormat.JOBLIB:
                return joblib.load(file_path)
            elif format == SerializationFormat.JSON:
                with open(file_path, 'r') as f:
                    return json.load(f)
            else:
                logger.warning(f"Unsupported deserialization format: {format}")
                return None
                
        except Exception as e:
            logger.error(f"Model deserialization failed: {e}")
            return None

class ModelRegistry:
    """رجیستری مدل"""
    
    def __init__(self, base_path: str = "models"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(exist_ok=True)
        
        self.models: Dict[str, Dict[str, ModelVersion]] = {}  # {model_name: {version: ModelVersion}}
        self.model_index: Dict[str, ModelMetadata] = {}  # {model_id: ModelMetadata}
        self.lock = threading.RLock()
        
        # Load existing models
        self._load_registry()
    
    def _generate_model_id(self, name: str, version: str) -> str:
        """تولید ID مدل"""
        return f"{name}_{version}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    def _get_model_path(self, name: str, version: str) -> str:
        """مسیر فایل مدل"""
        return str(self.base_path / name / f"v{version}" / "model.pkl")
    
    def _get_metadata_path(self, name: str, version: str) -> str:
        """مسیر فایل متادیتا"""
        return str(self.base_path / name / f"v{version}" / "metadata.json")
    
    def _save_metadata(self, metadata: ModelMetadata, name: str, version: str):
        """ذخیره متادیتا"""
        metadata_path = self._get_metadata_path(name, version)
        Path(metadata_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(metadata_path, 'w') as f:
            json.dump(metadata.to_dict(), f, indent=2)
    
    def _load_metadata(self, name: str, version: str) -> Optional[ModelMetadata]:
        """بارگذاری متادیتا"""
        metadata_path = self._get_metadata_path(name, version)
        
        if not os.path.exists(metadata_path):
            return None
        
        try:
            with open(metadata_path, 'r') as f:
                data = json.load(f)
            
            # Convert strings back to datetime objects
            data['created_at'] = datetime.fromisoformat(data['created_at'])
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
            
            # Convert enums
            data['status'] = ModelStatus(data['status'])
            data['stage'] = ModelStage(data['stage'])
            data['model_type'] = ModelType(data['model_type'])
            data['serialization_format'] = SerializationFormat(data['serialization_format'])
            
            # Convert metrics
            if 'metrics' in data:
                data['metrics'] = ModelMetrics(**data['metrics'])
            
            return ModelMetadata(**data)
            
        except Exception as e:
            logger.error(f"Failed to load metadata for {name}:v{version}: {e}")
            return None
    
    def _load_registry(self):
        """بارگذاری رجیستری"""
        try:
            for model_dir in self.base_path.iterdir():
                if model_dir.is_dir():
                    model_name = model_dir.name
                    self.models[model_name] = {}
                    
                    for version_dir in model_dir.iterdir():
                        if version_dir.is_dir() and version_dir.name.startswith('v'):
                            version = version_dir.name[1:]  # Remove 'v' prefix
                            
                            metadata = self._load_metadata(model_name, version)
                            if metadata:
                                model_id = self._generate_model_id(model_name, version)
                                model_version = ModelVersion(
                                    model_id=model_id,
                                    version=version,
                                    metadata=metadata
                                )
                                
                                self.models[model_name][version] = model_version
                                self.model_index[model_id] = metadata
                
            logger.info(f"Loaded {len(self.model_index)} models from registry")
            
        except Exception as e:
            logger.error(f"Failed to load registry: {e}")
    
    def register_model(self, name: str, version: str, model: Any, metadata: ModelMetadata,
                      serialize: bool = True) -> str:
        """ثبت مدل"""
        with self.lock:
            try:
                # Generate model ID
                model_id = self._generate_model_id(name, version)
                
                # Update metadata
                metadata.name = name
                metadata.version = version
                metadata.updated_at = datetime.now()
                
                # Serialize model if requested
                if serialize:
                    model_path = self._get_model_path(name, version)
                    if ModelSerializer.serialize(model, model_path, metadata.serialization_format):
                        metadata.file_path = model_path
                        metadata.file_size = os.path.getsize(model_path)
                        metadata.model_hash = hashlib.md5(open(model_path, 'rb').read()).hexdigest()
                
                # Save metadata
                self._save_metadata(metadata, name, version)
                
                # Create model version
                model_version = ModelVersion(
                    model_id=model_id,
                    version=version,
                    metadata=metadata,
                    model_object=model if not serialize else None,
                    is_loaded=not serialize
                )
                
                # Add to registry
                if name not in self.models:
                    self.models[name] = {}
                
                self.models[name][version] = model_version
                self.model_index[model_id] = metadata
                
                logger.info(f"Model registered: {name}:v{version} ({model_id})")
                return model_id
                
            except Exception as e:
                logger.error(f"Failed to register model: {e}")
                raise ModelLoadError(f"Failed to register model: {e}")
    
    def get_model(self, name: str, version: Optional[str] = None, 
                  stage: Optional[ModelStage] = None) -> Optional[ModelVersion]:
        """دریافت مدل"""
        with self.lock:
            if name not in self.models:
                return None
            
            model_versions = self.models[name]
            
            if version:
                # Get specific version
                return model_versions.get(version)
            elif stage:
                # Get model by stage
                for v in model_versions.values():
                    if v.metadata.stage == stage:
                        return v
                return None
            else:
                # Get latest version
                if not model_versions:
                    return None
                
                latest_version = max(model_versions.keys(), key=lambda x: model_versions[x].metadata.updated_at)
                return model_versions[latest_version]
    
    def load_model(self, name: str, version: Optional[str] = None) -> Optional[Any]:
        """بارگذاری مدل"""
        model_version = self.get_model(name, version)
        if not model_version:
            return None
        
        if model_version.is_loaded and model_version.model_object is not None:
            return model_version.model_object
        
        # Load from file
        if model_version.metadata.file_path:
            model_object = ModelSerializer.deserialize(
                model_version.metadata.file_path,
                model_version.metadata.serialization_format
            )
            
            if model_object is not None:
                model_version.model_object = model_object
                model_version.is_loaded = True
                return model_object
        
        return None
    
    def list_models(self, name_filter: Optional[str] = None, 
                   stage_filter: Optional[ModelStage] = None) -> List[ModelMetadata]:
        """لیست مدل‌ها"""
        results = []
        
        for name, versions in self.models.items():
            if name_filter and name_filter not in name:
                continue
            
            for version, model_version in versions.items():
                if stage_filter and model_version.metadata.stage != stage_filter:
                    continue
                
                results.append(model_version.metadata)
        
        return results
    
    def update_model_stage(self, name: str, version: str, stage: ModelStage) -> bool:
        """بروزرسانی مرحله مدل"""
        with self.lock:
            model_version = self.get_model(name, version)
            if not model_version:
                return False
            
            model_version.metadata.stage = stage
            model_version.metadata.updated_at = datetime.now()
            
            # Save metadata
            self._save_metadata(model_version.metadata, name, version)
            
            logger.info(f"Model stage updated: {name}:v{version} -> {stage.value}")
            return True
    
    def update_model_metrics(self, name: str, version: str, metrics: ModelMetrics) -> bool:
        """بروزرسانی معیارهای مدل"""
        with self.lock:
            model_version = self.get_model(name, version)
            if not model_version:
                return False
            
            model_version.metadata.metrics = metrics
            model_version.metadata.updated_at = datetime.now()
            
            # Save metadata
            self._save_metadata(model_version.metadata, name, version)
            
            logger.info(f"Model metrics updated: {name}:v{version}")
            return True
    
    def delete_model(self, name: str, version: str) -> bool:
        """حذف مدل"""
        with self.lock:
            model_version = self.get_model(name, version)
            if not model_version:
                return False
            
            try:
                # Delete files
                model_dir = self.base_path / name / f"v{version}"
                if model_dir.exists():
                    import shutil
                    shutil.rmtree(model_dir)
                
                # Remove from registry
                del self.models[name][version]
                if not self.models[name]:
                    del self.models[name]
                
                del self.model_index[model_version.model_id]
                
                logger.info(f"Model deleted: {name}:v{version}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to delete model: {e}")
                return False
    
    def get_model_lineage(self, name: str) -> List[ModelMetadata]:
        """تاریخچه نسخه‌های مدل"""
        if name not in self.models:
            return []
        
        versions = list(self.models[name].values())
        versions.sort(key=lambda x: x.metadata.created_at)
        
        return [v.metadata for v in versions]
    
    def compare_models(self, name1: str, version1: str, name2: str, version2: str) -> Dict[str, Any]:
        """مقایسه مدل‌ها"""
        model1 = self.get_model(name1, version1)
        model2 = self.get_model(name2, version2)
        
        if not model1 or not model2:
            return {"error": "One or both models not found"}
        
        comparison = {
            "model1": {
                "name": model1.metadata.name,
                "version": model1.metadata.version,
                "metrics": model1.metadata.metrics.to_dict(),
                "created_at": model1.metadata.created_at.isoformat(),
                "stage": model1.metadata.stage.value
            },
            "model2": {
                "name": model2.metadata.name,
                "version": model2.metadata.version,
                "metrics": model2.metadata.metrics.to_dict(),
                "created_at": model2.metadata.created_at.isoformat(),
                "stage": model2.metadata.stage.value
            },
            "differences": {}
        }
        
        # Compare metrics
        metrics1 = model1.metadata.metrics.to_dict()
        metrics2 = model2.metadata.metrics.to_dict()
        
        for metric in set(metrics1.keys()).union(set(metrics2.keys())):
            val1 = metrics1.get(metric)
            val2 = metrics2.get(metric)
            
            if val1 != val2:
                comparison["differences"][metric] = {
                    "model1": val1,
                    "model2": val2,
                    "difference": (val2 - val1) if (val1 and val2) else None
                }
        
        return comparison
    
    def export_model(self, name: str, version: str, export_path: str) -> bool:
        """صادرات مدل"""
        model_version = self.get_model(name, version)
        if not model_version:
            return False
        
        try:
            # Create export directory
            export_dir = Path(export_path)
            export_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy model file
            if model_version.metadata.file_path:
                import shutil
                shutil.copy2(model_version.metadata.file_path, export_dir / "model.pkl")
            
            # Save metadata
            with open(export_dir / "metadata.json", 'w') as f:
                json.dump(model_version.metadata.to_dict(), f, indent=2)
            
            logger.info(f"Model exported to {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export model: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """آمار رجیستری"""
        total_models = len(self.model_index)
        
        # Count by stage
        stage_counts = {}
        for metadata in self.model_index.values():
            stage = metadata.stage.value
            stage_counts[stage] = stage_counts.get(stage, 0) + 1
        
        # Count by type
        type_counts = {}
        for metadata in self.model_index.values():
            model_type = metadata.model_type.value
            type_counts[model_type] = type_counts.get(model_type, 0) + 1
        
        # Calculate total size
        total_size = sum(metadata.file_size for metadata in self.model_index.values())
        
        return {
            "total_models": total_models,
            "total_size_bytes": total_size,
            "total_size_mb": total_size / (1024 * 1024),
            "stage_distribution": stage_counts,
            "type_distribution": type_counts,
            "unique_names": len(self.models)
        }

# Global registry instance
model_registry = ModelRegistry()

@contextmanager
def model_context(name: str, version: str):
    """Context manager برای مدل"""
    model = model_registry.load_model(name, version)
    if model is None:
        raise ModelLoadError(f"Failed to load model {name}:v{version}")
    
    try:
        yield model
    finally:
        # Cleanup if needed
        pass

# Mock MLflow integration (simplified)
class MLflowIntegration:
    """ادغام MLflow"""
    
    def __init__(self, tracking_uri: str = "sqlite:///mlflow.db"):
        self.tracking_uri = tracking_uri
        self.experiment_name = "trading_models"
        self.active_run = None
        
    def start_run(self, run_name: str = None):
        """شروع اجرا"""
        # Mock MLflow run
        self.active_run = {
            "run_id": f"run_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "run_name": run_name or "unnamed_run",
            "start_time": datetime.now(),
            "metrics": {},
            "params": {},
            "tags": {}
        }
        logger.info(f"MLflow run started: {self.active_run['run_id']}")
    
    def log_metric(self, key: str, value: float, step: int = 0):
        """ثبت معیار"""
        if self.active_run:
            if key not in self.active_run["metrics"]:
                self.active_run["metrics"][key] = []
            self.active_run["metrics"][key].append({"value": value, "step": step})
    
    def log_param(self, key: str, value: Any):
        """ثبت پارامتر"""
        if self.active_run:
            self.active_run["params"][key] = value
    
    def log_model(self, model: Any, artifact_path: str, registered_model_name: str = None):
        """ثبت مدل"""
        if self.active_run:
            # Mock model logging
            model_path = f"models/{artifact_path}"
            self.active_run["model_path"] = model_path
            
            if registered_model_name:
                # Register model in our registry
                metadata = ModelMetadata(
                    name=registered_model_name,
                    version="1.0",
                    model_type=ModelType.CUSTOM,
                    description="Model from MLflow",
                    author="MLflow"
                )
                model_registry.register_model(registered_model_name, "1.0", model, metadata)
    
    def end_run(self):
        """پایان اجرا"""
        if self.active_run:
            self.active_run["end_time"] = datetime.now()
            logger.info(f"MLflow run ended: {self.active_run['run_id']}")
            self.active_run = None

# Global MLflow integration
mlflow_integration = MLflowIntegration()

# Test and examples
def test_model_versioning():
    """تست سیستم versioning"""
    print("📦 Testing Model Versioning System...")
    
    # Create a simple test model
    class TestModel:
        def __init__(self, param1=1.0, param2=2.0):
            self.param1 = param1
            self.param2 = param2
            self.trained = False
        
        def train(self, data):
            self.trained = True
            return {"loss": 0.1, "accuracy": 0.95}
        
        def predict(self, data):
            return [x * self.param1 + self.param2 for x in data]
    
    # Test 1: Register model
    print("\n1. Registering model...")
    model = TestModel(param1=1.5, param2=0.5)
    model.train([1, 2, 3, 4, 5])
    
    metadata = ModelMetadata(
        name="test_model",
        version="1.0",
        model_type=ModelType.REGRESSION,
        description="Test regression model",
        author="Test System",
        metrics=ModelMetrics(accuracy=0.95, mse=0.1)
    )
    
    model_id = model_registry.register_model("test_model", "1.0", model, metadata)
    print(f"✓ Model registered: {model_id}")
    
    # Test 2: Load model
    print("\n2. Loading model...")
    loaded_model = model_registry.load_model("test_model", "1.0")
    if loaded_model:
        print(f"✓ Model loaded successfully")
        predictions = loaded_model.predict([1, 2, 3])
        print(f"✓ Model predictions: {predictions}")
    
    # Test 3: Update model stage
    print("\n3. Updating model stage...")
    stage_updated = model_registry.update_model_stage("test_model", "1.0", ModelStage.PRODUCTION)
    print(f"✓ Stage updated: {stage_updated}")
    
    # Test 4: Register new version
    print("\n4. Registering new version...")
    model_v2 = TestModel(param1=2.0, param2=1.0)
    model_v2.train([1, 2, 3, 4, 5])
    
    metadata_v2 = ModelMetadata(
        name="test_model",
        version="2.0",
        model_type=ModelType.REGRESSION,
        description="Improved test regression model",
        author="Test System",
        metrics=ModelMetrics(accuracy=0.97, mse=0.08)
    )
    
    model_id_v2 = model_registry.register_model("test_model", "2.0", model_v2, metadata_v2)
    print(f"✓ Model v2.0 registered: {model_id_v2}")
    
    # Test 5: List models
    print("\n5. Listing models...")
    models = model_registry.list_models()
    for model_meta in models:
        print(f"✓ {model_meta.name}:v{model_meta.version} ({model_meta.stage.value})")
    
    # Test 6: Compare models
    print("\n6. Comparing models...")
    comparison = model_registry.compare_models("test_model", "1.0", "test_model", "2.0")
    print(f"✓ Model comparison: {comparison}")
    
    # Test 7: Model lineage
    print("\n7. Model lineage...")
    lineage = model_registry.get_model_lineage("test_model")
    print(f"✓ Model lineage: {len(lineage)} versions")
    
    # Test 8: Statistics
    print("\n8. Registry statistics...")
    stats = model_registry.get_statistics()
    print(f"✓ Registry stats: {stats}")
    
    print("\n✅ Model Versioning System test completed!")

if __name__ == "__main__":
    test_model_versioning() 