# مستند جامع: SentimentIntegrator

## مسئولیت
ادغام و ترکیب نتایج تحلیل احساسات از منابع مختلف (اخبار، شبکه اجتماعی، تحلیل بازار) و تبدیل آن به سیگنال معاملاتی.

## پارامترها
- sentiment_analyzer: نمونه‌ای از SentimentAnalyzer یا AdvancedSentimentAnalyzer
- languages: لیست زبان‌های فعال

## متدهای کلیدی
- analyze_market_sentiment: تحلیل احساسات بازار با ترکیب منابع
- sentiment_to_trading_signal: تبدیل احساسات به سیگنال معامله
- _apply_sentiment_adjustment: تنظیم سیگنال نهایی با توجه به احساسات

## نمونه کد
```python
from utils.sentiment_integrator import SentimentIntegrator
integrator = SentimentIntegrator(languages=['en','fa'])
result = integrator.analyze_market_sentiment('BTCUSD', news_texts=['خبر مثبت'])
```

## مدیریت خطا
در صورت خطا یا نبود داده، خروجی پیش‌فرض یا سیگنال خنثی برمی‌گرداند.

## بهترین شیوه
- همیشه از منابع متنوع برای تحلیل احساسات استفاده کنید.
- خروجی را با سایر سیگنال‌های بازار ترکیب کنید.

## نمودار
- نمودار همبستگی احساسات و سیگنال‌های معاملاتی قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول در سیستم معاملاتی یکپارچه (models/unified_trading_system.py) به صورت عملیاتی برای ادغام احساسات و تولید سیگنال نهایی استفاده می‌شود.

## وضعیت عملیاتی
✅ عملیاتی و در جریان اصلی پروژه فعال است. 