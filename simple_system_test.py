"""
Simple System Performance Test
تست ساده عملکرد سیستم‌ها بدون مشکلات Unicode
"""

import sys
import os
import json
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_individual_systems():
    """تست سیستم‌های منفرد"""
    results = {}
    
    print("=== SYSTEM PERFORMANCE TEST ===")
    print("Testing individual systems...")
    
    # Test 1: Advanced RL Agent
    print("\n1. Testing Advanced RL Agent...")
    try:
        from utils.advanced_rl_agent import AdvancedRLAgent
        
        agent = AdvancedRLAgent()
        
        # Test basic functionality
        state = [1.1000, 0.02, 1.0, 0.5, 2, 0.8, 0.0]
        action = agent.act(state)
        agent.remember(state, action, 0.01, state, False)
        
        results['advanced_rl'] = {
            'status': 'PASSED',
            'action': action,
            'memory_size': len(agent.memory)
        }
        print("   PASSED - RL Agent working correctly")
        
    except Exception as e:
        results['advanced_rl'] = {'status': 'FAILED', 'error': str(e)}
        print(f"   FAILED - {e}")
    
    # Test 2: Multi-Step Prediction
    print("\n2. Testing Multi-Step Prediction...")
    try:
        from utils.multi_step_prediction_fixed import MultiStepPredictor
        
        predictor = MultiStepPredictor()
        
        # Test training
        training_data = []
        for i in range(50):
            training_data.append({
                'timestamp': datetime.now(),
                'price': 1.1000 + i * 0.0001,
                'volume': 1000
            })
        
        train_results = predictor.train(training_data)
        
        # Test prediction
        current_data = {
            'timestamp': datetime.now(),
            'price': 1.1050,
            'volume': 1200
        }
        predictions = predictor.predict(current_data)
        
        results['multi_step_prediction'] = {
            'status': 'PASSED',
            'models_trained': len(predictor.models),
            'predictions': len(predictions)
        }
        print("   PASSED - Multi-Step Prediction working correctly")
        
    except Exception as e:
        results['multi_step_prediction'] = {'status': 'FAILED', 'error': str(e)}
        print(f"   FAILED - {e}")
    
    # Test 3: Market Regime Detection
    print("\n3. Testing Market Regime Detection...")
    try:
        from utils.market_regime_detector import MarketRegimeDetector
        import numpy as np
        
        detector = MarketRegimeDetector()
        
        # Test with simple features
        features = np.array([[1.1234, 1500, 0.025, 0.003, 1.1234]])
        detector.fit(features)
        
        result = detector.predict_regime(features)
        
        results['market_regime'] = {
            'status': 'PASSED',
            'regime': result['regime'],
            'confidence': result['confidence']
        }
        print("   PASSED - Market Regime Detection working correctly")
        
    except Exception as e:
        results['market_regime'] = {'status': 'FAILED', 'error': str(e)}
        print(f"   FAILED - {e}")
    
    # Test 4: Memory System
    print("\n4. Testing Memory System...")
    try:
        from utils.intelligent_memory_system import IntelligentMemorySystem
        
        memory = IntelligentMemorySystem("test_memory.db")
        
        # Store and retrieve memory
        memory_id = memory.store_memory(
            content={'test': 'data'},
            memory_type='test',
            importance=0.8,
            tags=['test']
        )
        
        retrieved = memory.retrieve_memory(memory_id)
        
        results['memory_system'] = {
            'status': 'PASSED',
            'memory_stored': memory_id is not None,
            'memory_retrieved': retrieved is not None
        }
        print("   PASSED - Memory System working correctly")
        
    except Exception as e:
        results['memory_system'] = {'status': 'FAILED', 'error': str(e)}
        print(f"   FAILED - {e}")
    
    # Test 5: Federated Learning
    print("\n5. Testing Federated Learning...")
    try:
        from utils.federated_learning_system import FederatedLearningServer, FederatedLearningClient
        
        server = FederatedLearningServer("test_fed.db")
        client = FederatedLearningClient("test_client", "test_client.db")
        
        # Test update
        training_data = {'data_size': 100, 'accuracy': 0.85}
        update = client.train_local_model("TestModel", training_data)
        success = server.receive_update(update)
        
        results['federated_learning'] = {
            'status': 'PASSED',
            'update_success': success,
            'server_active': True
        }
        print("   PASSED - Federated Learning working correctly")
        
    except Exception as e:
        results['federated_learning'] = {'status': 'FAILED', 'error': str(e)}
        print(f"   FAILED - {e}")
    
    return results

def test_system_integration():
    """تست یکپارچه‌سازی سیستم"""
    print("\n6. Testing System Integration...")
    
    try:
        # Test basic integration workflow
        from utils.advanced_rl_agent import AdvancedRLAgent
        from utils.intelligent_memory_system import IntelligentMemorySystem
        
        # Initialize components
        agent = AdvancedRLAgent()
        memory = IntelligentMemorySystem("integration_test.db")
        
        # Simulate trading decision
        market_state = [1.1234, 0.025, 1.2, 0.6, 1, 0.75, 0.005]
        action = agent.act(market_state)
        
        # Store decision in memory
        memory_id = memory.store_memory(
            content={
                'market_state': market_state,
                'action': action,
                'timestamp': datetime.now().isoformat()
            },
            memory_type='trading_decision',
            importance=0.7,
            tags=['integration', 'test']
        )
        
        integration_success = memory_id is not None and action is not None
        
        print("   PASSED - System Integration working correctly")
        return {'status': 'PASSED', 'integration_success': integration_success}
        
    except Exception as e:
        print(f"   FAILED - {e}")
        return {'status': 'FAILED', 'error': str(e)}

def generate_performance_report(results, integration_result):
    """تولید گزارش عملکرد"""
    
    # Count successes
    total_tests = len(results) + 1  # +1 for integration test
    passed_tests = sum(1 for r in results.values() if r['status'] == 'PASSED')
    if integration_result['status'] == 'PASSED':
        passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100
    
    print("\n" + "="*50)
    print("PERFORMANCE REPORT SUMMARY")
    print("="*50)
    print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    print("\nDETAILED RESULTS:")
    for system, result in results.items():
        status = "PASSED" if result['status'] == 'PASSED' else "FAILED"
        print(f"  {status} - {system}")
    
    status = "PASSED" if integration_result['status'] == 'PASSED' else "FAILED"
    print(f"  {status} - system_integration")
    
    print("\nSYSTEM READINESS:")
    if success_rate >= 90:
        print("  EXCELLENT - System ready for production")
    elif success_rate >= 75:
        print("  GOOD - System mostly ready")
    elif success_rate >= 50:
        print("  FAIR - System needs some attention")
    else:
        print("  POOR - System needs significant work")
    
    # Save detailed report
    report = {
        'timestamp': datetime.now().isoformat(),
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'success_rate': success_rate,
        'individual_results': results,
        'integration_result': integration_result
    }
    
    with open('performance_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\nDetailed report saved to: performance_report.json")
    
    return success_rate >= 75

def main():
    """اجرای تست اصلی"""
    print("Starting Simple System Performance Test...")
    
    start_time = time.time()
    
    # Run individual system tests
    results = test_individual_systems()
    
    # Run integration test
    integration_result = test_system_integration()
    
    # Generate report
    system_ready = generate_performance_report(results, integration_result)
    
    end_time = time.time()
    execution_time = end_time - start_time
    
    print(f"\nTotal execution time: {execution_time:.2f} seconds")
    
    # Clean up test files
    test_files = ["test_memory.db", "test_fed.db", "test_client.db", "integration_test.db"]
    for file in test_files:
        try:
            if os.path.exists(file):
                os.remove(file)
        except:
            pass
    
    return system_ready

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 