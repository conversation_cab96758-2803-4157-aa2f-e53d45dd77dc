"""
Genetic Strategy Evolution System
سیستم تکامل استراتژی با الگوریتم ژنتیک

این سیستم استراتژی‌های معاملاتی را با استفاده از الگوریتم ژنتیک تکامل می‌دهد.
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import logging
import random
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from copy import deepcopy
import threading
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TradingStrategy:
    """استراتژی معاملاتی"""
    strategy_id: str
    name: str
    parameters: Dict[str, float]
    rules: List[Dict[str, Any]]
    fitness_score: float
    generation: int
    parent_ids: List[str]
    performance_metrics: Dict[str, float]
    created_at: datetime

@dataclass
class EvolutionConfig:
    """تنظیمات تکامل"""
    population_size: int = 50
    generations: int = 100
    mutation_rate: float = 0.1
    crossover_rate: float = 0.8
    elite_size: int = 5
    tournament_size: int = 3
    fitness_weights: Dict[str, float] = None

class StrategyGenePool:
    """مجموعه ژن‌های استراتژی"""
    
    def __init__(self):
        self.parameter_ranges = {
            # پارامترهای تکنیکال
            'sma_period_short': (5, 50),
            'sma_period_long': (20, 200),
            'rsi_period': (10, 30),
            'rsi_overbought': (70, 90),
            'rsi_oversold': (10, 30),
            'bollinger_period': (10, 30),
            'bollinger_std': (1.5, 2.5),
            'macd_fast': (8, 15),
            'macd_slow': (20, 30),
            'macd_signal': (5, 15),
            
            # پارامترهای ریسک
            'stop_loss': (0.005, 0.05),
            'take_profit': (0.01, 0.1),
            'position_size': (0.01, 0.1),
            'max_positions': (1, 10),
            'risk_per_trade': (0.01, 0.05),
            
            # پارامترهای زمانی
            'entry_timeout': (1, 24),
            'exit_timeout': (1, 48),
            'trade_frequency': (0.1, 2.0),
            
            # پارامترهای بازار
            'volatility_threshold': (0.01, 0.1),
            'volume_threshold': (0.5, 3.0),
            'trend_strength': (0.3, 0.9),
            'correlation_threshold': (0.5, 0.95)
        }
        
        self.rule_templates = [
            {
                'type': 'trend_following',
                'conditions': ['price_above_sma', 'rsi_not_overbought'],
                'action': 'buy',
                'weight': 1.0
            },
            {
                'type': 'mean_reversion',
                'conditions': ['price_below_bollinger_lower', 'rsi_oversold'],
                'action': 'buy',
                'weight': 1.0
            },
            {
                'type': 'momentum',
                'conditions': ['macd_bullish_crossover', 'volume_above_average'],
                'action': 'buy',
                'weight': 1.0
            },
            {
                'type': 'breakout',
                'conditions': ['price_break_resistance', 'volume_spike'],
                'action': 'buy',
                'weight': 1.0
            },
            {
                'type': 'risk_management',
                'conditions': ['position_size_exceeded', 'max_drawdown_reached'],
                'action': 'close_positions',
                'weight': 1.0
            }
        ]
    
    def generate_random_strategy(self, generation: int = 0) -> TradingStrategy:
        """تولید استراتژی تصادفی"""
        strategy_id = f"strategy_{int(time.time() * 1000000) % 1000000}"
        
        # تولید پارامترهای تصادفی
        parameters = {}
        for param, (min_val, max_val) in self.parameter_ranges.items():
            parameters[param] = random.uniform(min_val, max_val)
        
        # تولید قوانین تصادفی
        rules = []
        num_rules = random.randint(3, 7)
        
        for i in range(num_rules):
            rule_template = random.choice(self.rule_templates)
            rule = deepcopy(rule_template)
            rule['weight'] = random.uniform(0.5, 2.0)
            rule['priority'] = i + 1
            rules.append(rule)
        
        return TradingStrategy(
            strategy_id=strategy_id,
            name=f"Strategy_{strategy_id}",
            parameters=parameters,
            rules=rules,
            fitness_score=0.0,
            generation=generation,
            parent_ids=[],
            performance_metrics={},
            created_at=datetime.now()
        )

class StrategyEvaluator:
    """ارزیابی‌کننده استراتژی"""
    
    def __init__(self, historical_data: List[Dict]):
        self.historical_data = historical_data
        self.evaluation_period = 252  # یک سال
        
    def evaluate_strategy(self, strategy: TradingStrategy) -> float:
        """ارزیابی استراتژی"""
        try:
            # شبیه‌سازی معاملات
            trades = self._simulate_trades(strategy)
            
            # محاسبه متریک‌های عملکرد
            performance_metrics = self._calculate_performance_metrics(trades)
            
            # محاسبه امتیاز fitness
            fitness_score = self._calculate_fitness_score(performance_metrics)
            
            # به‌روزرسانی استراتژی
            strategy.performance_metrics = performance_metrics
            strategy.fitness_score = fitness_score
            
            return fitness_score
            
        except Exception as e:
            logger.error(f"Error evaluating strategy {strategy.strategy_id}: {e}")
            return 0.0
    
    def _simulate_trades(self, strategy: TradingStrategy) -> List[Dict]:
        """شبیه‌سازی معاملات"""
        trades = []
        balance = 10000.0
        positions = []
        
        # استفاده از داده‌های تاریخی
        for i, data_point in enumerate(self.historical_data[-self.evaluation_period:]):
            # چک کردن موقعیت‌های باز برای بستن (قبل از تولید سیگنال‌های جدید)
            for pos in positions[:]:
                if self._should_close_position(strategy, pos, data_point):
                    close_trade = self._execute_sell(pos, data_point)
                    if close_trade:
                        trades.append(close_trade)
                        positions.remove(pos)
                        balance += close_trade['profit']
            
            # تولید سیگنال‌های معاملاتی
            signals = self._generate_signals(strategy, data_point, i)
            
            # اجرای معاملات
            for signal in signals:
                if signal['action'] == 'buy' and len(positions) < strategy.parameters.get('max_positions', 5):
                    trade = self._execute_buy(strategy, signal, balance, data_point)
                    if trade:
                        trades.append(trade)
                        positions.append(trade)
                        balance -= trade['cost']
                
                elif signal['action'] == 'sell':
                    # بستن موقعیت‌ها
                    for pos in positions[:]:
                        if self._should_close_position(strategy, pos, data_point):
                            close_trade = self._execute_sell(pos, data_point)
                            if close_trade:
                                trades.append(close_trade)
                                positions.remove(pos)
                                balance += close_trade['profit']
        
        # بستن تمام موقعیت‌های باقی‌مانده در انتها
        for pos in positions:
            close_trade = self._execute_sell(pos, self.historical_data[-1])
            if close_trade:
                trades.append(close_trade)
        
        return trades
    
    def _generate_signals(self, strategy: TradingStrategy, data_point: Dict, index: int) -> List[Dict]:
        """تولید سیگنال‌های معاملاتی"""
        signals = []
        
        # محاسبه اندیکاتورها
        indicators = self._calculate_indicators(strategy, data_point, index)
        
        # بررسی قوانین
        for rule in strategy.rules:
            if self._evaluate_rule_conditions(rule, indicators):
                # تعیین action بر اساس نوع قانون
                action = rule.get('action', self._get_default_action(rule))
                
                signals.append({
                    'action': action,
                    'weight': rule.get('weight', 1.0),
                    'rule_type': rule.get('type', 'unknown')
                })
        
        return signals
    
    def _calculate_indicators(self, strategy: TradingStrategy, data_point: Dict, index: int) -> Dict:
        """محاسبه اندیکاتورها"""
        # شبیه‌سازی اندیکاتورهای تکنیکال
        price = data_point.get('price', 1.0)
        volume = data_point.get('volume', 1000)
        
        # SMA
        sma_short = price * (1 + random.uniform(-0.01, 0.01))
        sma_long = price * (1 + random.uniform(-0.02, 0.02))
        
        # RSI
        rsi = random.uniform(20, 80)
        
        # Bollinger Bands
        bollinger_upper = price * 1.02
        bollinger_lower = price * 0.98
        
        # MACD
        macd_line = random.uniform(-0.01, 0.01)
        macd_signal = random.uniform(-0.01, 0.01)
        
        return {
            'price': price,
            'volume': volume,
            'sma_short': sma_short,
            'sma_long': sma_long,
            'rsi': rsi,
            'bollinger_upper': bollinger_upper,
            'bollinger_lower': bollinger_lower,
            'macd_line': macd_line,
            'macd_signal': macd_signal,
            'volatility': random.uniform(0.01, 0.05)
        }
    
    def _evaluate_rule_conditions(self, rule: Dict, indicators: Dict) -> bool:
        """ارزیابی شرایط قانون"""
        conditions = rule.get('conditions', [])
        
        for condition in conditions:
            if condition == 'price_above_sma':
                if indicators['price'] <= indicators['sma_short']:
                    return False
            elif condition == 'rsi_not_overbought':
                if indicators['rsi'] >= 70:
                    return False
            elif condition == 'price_below_bollinger_lower':
                if indicators['price'] >= indicators['bollinger_lower']:
                    return False
            elif condition == 'rsi_oversold':
                if indicators['rsi'] >= 30:
                    return False
            elif condition == 'macd_bullish_crossover':
                if indicators['macd_line'] <= indicators['macd_signal']:
                    return False
            elif condition == 'volume_above_average':
                if indicators['volume'] <= 1000:
                    return False
        
        return True
    
    def _get_default_action(self, rule: Dict) -> str:
        """تعیین action پیش‌فرض بر اساس نوع قانون"""
        rule_type = rule.get('type', 'unknown')
        
        if rule_type in ['trend_following', 'momentum', 'breakout', 'mean_reversion']:
            return 'buy'
        elif rule_type == 'risk_management':
            return 'sell'
        else:
            return 'hold'
    
    def _execute_buy(self, strategy: TradingStrategy, signal: Dict, balance: float, data_point: Dict) -> Optional[Dict]:
        """اجرای معامله خرید"""
        price = data_point.get('price', 1.0)
        position_size = strategy.parameters.get('position_size', 0.02)
        
        cost = balance * position_size
        
        if cost > balance:
            return None
        
        return {
            'type': 'buy',
            'price': price,
            'size': cost / price,
            'cost': cost,
            'timestamp': datetime.now(),
            'stop_loss': price * (1 - strategy.parameters.get('stop_loss', 0.02)),
            'take_profit': price * (1 + strategy.parameters.get('take_profit', 0.04))
        }
    
    def _should_close_position(self, strategy: TradingStrategy, position: Dict, data_point: Dict) -> bool:
        """تعیین اینکه آیا موقعیت باید بسته شود"""
        current_price = data_point.get('price', 1.0)
        
        # Stop Loss
        if current_price <= position['stop_loss']:
            return True
        
        # Take Profit
        if current_price >= position['take_profit']:
            return True
        
        # Timeout
        time_diff = datetime.now() - position['timestamp']
        if time_diff.total_seconds() > strategy.parameters.get('exit_timeout', 24) * 3600:
            return True
        
        return False
    
    def _execute_sell(self, position: Dict, data_point: Dict) -> Dict:
        """اجرای معامله فروش"""
        current_price = data_point.get('price', 1.0)
        profit = position['size'] * current_price - position['cost']
        
        return {
            'type': 'sell',
            'price': current_price,
            'size': position['size'],
            'profit': profit,
            'timestamp': datetime.now(),
            'return': profit / position['cost']
        }
    
    def _calculate_performance_metrics(self, trades: List[Dict]) -> Dict[str, float]:
        """محاسبه متریک‌های عملکرد"""
        if not trades:
            return {
                'total_return': 0.0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'total_trades': 0
            }
        
        # جداسازی معاملات خرید و فروش
        buy_trades = [t for t in trades if t['type'] == 'buy']
        sell_trades = [t for t in trades if t['type'] == 'sell']
        
        if not sell_trades:
            return {
                'total_return': 0.0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'total_trades': len(buy_trades)
            }
        
        # محاسبه بازده کل
        total_return = sum(t['profit'] for t in sell_trades)
        
        # محاسبه نرخ برد
        winning_trades = [t for t in sell_trades if t['profit'] > 0]
        win_rate = len(winning_trades) / len(sell_trades) if sell_trades else 0
        
        # محاسبه Profit Factor
        gross_profit = sum(t['profit'] for t in sell_trades if t['profit'] > 0)
        gross_loss = abs(sum(t['profit'] for t in sell_trades if t['profit'] < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else gross_profit
        
        # محاسبه Max Drawdown
        cumulative_returns = np.cumsum([t['profit'] for t in sell_trades])
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / (running_max + 1)
        max_drawdown = abs(np.min(drawdown))
        
        # محاسبه Sharpe Ratio
        returns = [t['return'] for t in sell_trades]
        if returns:
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
        else:
            sharpe_ratio = 0
        
        return {
            'total_return': total_return,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'total_trades': len(sell_trades)
        }
    
    def _calculate_fitness_score(self, performance_metrics: Dict[str, float]) -> float:
        """محاسبه امتیاز fitness"""
        # وزن‌های مختلف برای متریک‌ها
        weights = {
            'total_return': 0.3,
            'win_rate': 0.2,
            'profit_factor': 0.2,
            'sharpe_ratio': 0.2,
            'max_drawdown': -0.1  # منفی چون کمتر بهتر است
        }
        
        fitness = 0.0
        
        for metric, weight in weights.items():
            value = performance_metrics.get(metric, 0.0)
            
            # نرمال‌سازی مقادیر
            if metric == 'total_return':
                normalized_value = min(1.0, max(-1.0, value / 1000.0))
            elif metric == 'win_rate':
                normalized_value = value
            elif metric == 'profit_factor':
                normalized_value = min(1.0, value / 3.0)
            elif metric == 'sharpe_ratio':
                normalized_value = min(1.0, max(-1.0, value / 2.0))
            elif metric == 'max_drawdown':
                normalized_value = min(1.0, value)
            else:
                normalized_value = value
            
            fitness += weight * normalized_value
        
        return max(0.0, fitness)

class GeneticAlgorithm:
    """الگوریتم ژنتیک"""
    
    def __init__(self, config: EvolutionConfig, evaluator: StrategyEvaluator):
        self.config = config
        self.evaluator = evaluator
        self.gene_pool = StrategyGenePool()
        self.population = []
        self.generation = 0
        self.best_strategies = []
        
    def initialize_population(self):
        """راه‌اندازی جمعیت اولیه"""
        self.population = []
        
        for i in range(self.config.population_size):
            strategy = self.gene_pool.generate_random_strategy(self.generation)
            self.population.append(strategy)
        
        logger.info(f"Initialized population with {len(self.population)} strategies")
    
    def evolve(self) -> List[TradingStrategy]:
        """تکامل جمعیت"""
        self.initialize_population()
        
        for generation in range(self.config.generations):
            self.generation = generation
            logger.info(f"Generation {generation + 1}/{self.config.generations}")
            
            # ارزیابی جمعیت
            self._evaluate_population()
            
            # مرتب‌سازی بر اساس fitness
            self.population.sort(key=lambda x: x.fitness_score, reverse=True)
            
            # ذخیره بهترین استراتژی‌ها
            self.best_strategies.append(deepcopy(self.population[0]))
            
            # نمایش پیشرفت
            best_fitness = self.population[0].fitness_score
            avg_fitness = np.mean([s.fitness_score for s in self.population])
            logger.info(f"  Best fitness: {best_fitness:.4f}, Average: {avg_fitness:.4f}")
            
            # تولید نسل جدید
            if generation < self.config.generations - 1:
                new_population = self._create_new_generation()
                self.population = new_population
        
        return self.best_strategies
    
    def _evaluate_population(self):
        """ارزیابی جمعیت"""
        for strategy in self.population:
            if strategy.fitness_score == 0.0:  # فقط استراتژی‌های ارزیابی نشده
                fitness = self.evaluator.evaluate_strategy(strategy)
                strategy.fitness_score = fitness
    
    def _create_new_generation(self) -> List[TradingStrategy]:
        """ایجاد نسل جدید"""
        new_population = []
        
        # Elite Selection - نگه‌داشتن بهترین‌ها
        elite_size = min(self.config.elite_size, len(self.population))
        elites = self.population[:elite_size]
        
        for elite in elites:
            new_strategy = deepcopy(elite)
            new_strategy.generation = self.generation + 1
            new_population.append(new_strategy)
        
        # تولید بقیه جمعیت
        while len(new_population) < self.config.population_size:
            # انتخاب والدین
            parent1 = self._tournament_selection()
            parent2 = self._tournament_selection()
            
            # تولید فرزند
            if random.random() < self.config.crossover_rate:
                child = self._crossover(parent1, parent2)
            else:
                child = deepcopy(parent1)
            
            # جهش
            if random.random() < self.config.mutation_rate:
                child = self._mutate(child)
            
            child.generation = self.generation + 1
            child.fitness_score = 0.0  # نیاز به ارزیابی مجدد
            new_population.append(child)
        
        return new_population
    
    def _tournament_selection(self) -> TradingStrategy:
        """انتخاب تورنمنت"""
        tournament_size = min(self.config.tournament_size, len(self.population))
        tournament = random.sample(self.population, tournament_size)
        return max(tournament, key=lambda x: x.fitness_score)
    
    def _crossover(self, parent1: TradingStrategy, parent2: TradingStrategy) -> TradingStrategy:
        """تقاطع دو والد"""
        child = deepcopy(parent1)
        
        # تقاطع پارامترها
        for param in child.parameters:
            if random.random() < 0.5:
                child.parameters[param] = parent2.parameters.get(param, child.parameters[param])
        
        # تقاطع قوانین
        child.rules = []
        all_rules = parent1.rules + parent2.rules
        
        # انتخاب تصادفی از قوانین والدین
        num_rules = random.randint(3, min(7, len(all_rules)))
        selected_rules = random.sample(all_rules, min(num_rules, len(all_rules)))
        
        for rule in selected_rules:
            child.rules.append(deepcopy(rule))
        
        # به‌روزرسانی metadata
        child.strategy_id = f"strategy_{int(time.time() * 1000000) % 1000000}"
        child.parent_ids = [parent1.strategy_id, parent2.strategy_id]
        child.created_at = datetime.now()
        
        return child
    
    def _mutate(self, strategy: TradingStrategy) -> TradingStrategy:
        """جهش استراتژی"""
        mutated = deepcopy(strategy)
        
        # جهش پارامترها
        for param in mutated.parameters:
            if random.random() < 0.3:  # احتمال جهش هر پارامتر
                min_val, max_val = self.gene_pool.parameter_ranges[param]
                
                # جهش گاوسی
                current_value = mutated.parameters[param]
                mutation_strength = (max_val - min_val) * 0.1
                new_value = current_value + random.gauss(0, mutation_strength)
                
                # محدود کردن به بازه مجاز
                mutated.parameters[param] = max(min_val, min(max_val, new_value))
        
        # جهش قوانین
        if random.random() < 0.2:  # احتمال جهش قوانین
            if mutated.rules:
                # تغییر وزن یک قانون
                rule_index = random.randint(0, len(mutated.rules) - 1)
                mutated.rules[rule_index]['weight'] *= random.uniform(0.8, 1.2)
        
        if random.random() < 0.1:  # احتمال اضافه کردن قانون جدید
            new_rule = random.choice(self.gene_pool.rule_templates)
            mutated.rules.append(deepcopy(new_rule))
        
        if random.random() < 0.1 and len(mutated.rules) > 3:  # احتمال حذف قانون
            mutated.rules.pop(random.randint(0, len(mutated.rules) - 1))
        
        return mutated

class GeneticStrategyEvolution:
    """سیستم جامع تکامل استراتژی ژنتیک"""
    
    def __init__(self, db_path: str = "genetic_evolution.db"):
        self.db_path = db_path
        self.historical_data = []
        self.evolution_history = []
        self.best_strategies = []
        
        self._init_database()
        logger.info("Genetic Strategy Evolution System initialized")
    
    def _init_database(self):
        """راه‌اندازی پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS strategies (
                strategy_id TEXT PRIMARY KEY,
                name TEXT,
                parameters TEXT,
                rules TEXT,
                fitness_score REAL,
                generation INTEGER,
                parent_ids TEXT,
                performance_metrics TEXT,
                created_at TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS evolution_runs (
                run_id TEXT PRIMARY KEY,
                config TEXT,
                start_time TEXT,
                end_time TEXT,
                generations INTEGER,
                best_fitness REAL,
                final_population TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def load_historical_data(self, data: List[Dict]):
        """بارگذاری داده‌های تاریخی"""
        self.historical_data = data
        logger.info(f"Loaded {len(data)} historical data points")
    
    def run_evolution(self, config: EvolutionConfig = None) -> List[TradingStrategy]:
        """اجرای فرآیند تکامل"""
        if not self.historical_data:
            logger.error("No historical data available")
            return []
        
        if config is None:
            config = EvolutionConfig()
        
        # ایجاد ارزیابی‌کننده
        evaluator = StrategyEvaluator(self.historical_data)
        
        # ایجاد الگوریتم ژنتیک
        genetic_algorithm = GeneticAlgorithm(config, evaluator)
        
        # اجرای تکامل
        start_time = datetime.now()
        logger.info("Starting genetic evolution...")
        
        best_strategies = genetic_algorithm.evolve()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"Evolution completed in {duration:.2f} seconds")
        
        # ذخیره نتایج
        self._save_evolution_results(genetic_algorithm, config, start_time, end_time)
        
        self.best_strategies = best_strategies
        return best_strategies
    
    def _save_evolution_results(self, genetic_algorithm: GeneticAlgorithm, config: EvolutionConfig, 
                               start_time: datetime, end_time: datetime):
        """ذخیره نتایج تکامل"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # ذخیره استراتژی‌ها
        for strategy in genetic_algorithm.population:
            cursor.execute('''
                INSERT OR REPLACE INTO strategies 
                (strategy_id, name, parameters, rules, fitness_score, generation, parent_ids, performance_metrics, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                strategy.strategy_id,
                strategy.name,
                json.dumps(strategy.parameters),
                json.dumps(strategy.rules),
                strategy.fitness_score,
                strategy.generation,
                json.dumps(strategy.parent_ids),
                json.dumps(strategy.performance_metrics),
                strategy.created_at.isoformat()
            ))
        
        # ذخیره اجرای تکامل
        run_id = f"evolution_{int(time.time())}"
        cursor.execute('''
            INSERT INTO evolution_runs 
            (run_id, config, start_time, end_time, generations, best_fitness, final_population)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            run_id,
            json.dumps(asdict(config)),
            start_time.isoformat(),
            end_time.isoformat(),
            config.generations,
            genetic_algorithm.population[0].fitness_score,
            json.dumps([s.strategy_id for s in genetic_algorithm.population])
        ))
        
        conn.commit()
        conn.close()
    
    def get_best_strategy(self) -> Optional[TradingStrategy]:
        """دریافت بهترین استراتژی"""
        if not self.best_strategies:
            return None
        
        return max(self.best_strategies, key=lambda x: x.fitness_score)
    
    def get_evolution_summary(self) -> Dict:
        """خلاصه تکامل"""
        if not self.best_strategies:
            return {}
        
        fitness_history = [s.fitness_score for s in self.best_strategies]
        
        return {
            'total_generations': len(self.best_strategies),
            'best_fitness': max(fitness_history),
            'final_fitness': fitness_history[-1],
            'improvement': fitness_history[-1] - fitness_history[0],
            'fitness_history': fitness_history
        }
    
    def _calculate_fitness(self, strategy: TradingStrategy, data: List[Dict]) -> float:
        """محاسبه fitness برای یک استراتژی"""
        try:
            # ایجاد ارزیابی‌کننده
            evaluator = StrategyEvaluator(data)
            
            # ارزیابی استراتژی
            fitness_score = evaluator.evaluate_strategy(strategy)
            
            return fitness_score
            
        except Exception as e:
            logger.error(f"Error calculating fitness for strategy {strategy.strategy_id}: {e}")
            return 0.0

def main():
    """تست سیستم تکامل ژنتیک"""
    print("Genetic Strategy Evolution System Test")
    print("=" * 50)
    
    # ایجاد سیستم
    evolution_system = GeneticStrategyEvolution("test_genetic_evolution.db")
    
    # شبیه‌سازی داده‌های تاریخی
    print("Generating historical market data...")
    historical_data = []
    
    base_price = 1.1000
    for i in range(1000):
        # شبیه‌سازی حرکت قیمت
        price_change = random.gauss(0, 0.001)
        base_price += price_change
        
        data_point = {
            'timestamp': datetime.now() - timedelta(hours=1000-i),
            'price': base_price,
            'volume': random.randint(800, 1200),
            'high': base_price + random.uniform(0, 0.002),
            'low': base_price - random.uniform(0, 0.002),
            'volatility': random.uniform(0.01, 0.05)
        }
        historical_data.append(data_point)
    
    evolution_system.load_historical_data(historical_data)
    
    # تنظیمات تکامل
    config = EvolutionConfig(
        population_size=20,  # کم برای تست سریع
        generations=10,      # کم برای تست سریع
        mutation_rate=0.15,
        crossover_rate=0.8,
        elite_size=3,
        tournament_size=3
    )
    
    print(f"Starting evolution with {config.population_size} strategies for {config.generations} generations...")
    
    # اجرای تکامل
    best_strategies = evolution_system.run_evolution(config)
    
    # نمایش نتایج
    print(f"\n--- Evolution Results ---")
    
    if best_strategies:
        best_strategy = evolution_system.get_best_strategy()
        
        print(f"Best Strategy: {best_strategy.strategy_id}")
        print(f"  Fitness Score: {best_strategy.fitness_score:.4f}")
        print(f"  Generation: {best_strategy.generation}")
        print(f"  Performance Metrics:")
        for metric, value in best_strategy.performance_metrics.items():
            print(f"    {metric}: {value:.4f}")
        
        print(f"  Key Parameters:")
        key_params = ['sma_period_short', 'sma_period_long', 'rsi_period', 'stop_loss', 'take_profit']
        for param in key_params:
            if param in best_strategy.parameters:
                print(f"    {param}: {best_strategy.parameters[param]:.4f}")
        
        print(f"  Rules: {len(best_strategy.rules)}")
        for i, rule in enumerate(best_strategy.rules[:3]):  # نمایش 3 قانون اول
            print(f"    Rule {i+1}: {rule['type']} (weight: {rule['weight']:.2f})")
    
    # خلاصه تکامل
    summary = evolution_system.get_evolution_summary()
    if summary:
        print(f"\n--- Evolution Summary ---")
        print(f"Total Generations: {summary['total_generations']}")
        print(f"Best Fitness: {summary['best_fitness']:.4f}")
        print(f"Final Fitness: {summary['final_fitness']:.4f}")
        print(f"Improvement: {summary['improvement']:.4f}")
        
        # نمایش تاریخچه fitness
        print(f"\nFitness History:")
        for i, fitness in enumerate(summary['fitness_history'][:10]):  # نمایش 10 نسل اول
            print(f"  Generation {i+1}: {fitness:.4f}")
    
    print(f"\n✅ Genetic Strategy Evolution System test completed!")

if __name__ == "__main__":
    main() 