from utils.risk_manager import RiskManager

def test_risk_manager_limits():
    # تست ریسک پوزیشن (فقط این شرط فعال شود)
    rm = RiskManager(max_position_risk=0.1, max_total_risk=0.2, max_positions=3)
    assert rm.check_position(balance=10000, position_risk=100, open_positions=1) is None
    msg = rm.check_position(balance=10000, position_risk=1500, open_positions=1)  # 15% > 10%
    assert msg is not None and 'ریسک پوزیشن' in msg
    # تست ریسک کل (فقط این شرط فعال شود)
    rm = RiskManager(max_position_risk=0.2, max_total_risk=0.1, max_positions=3)
    msg = rm.check_position(balance=10000, position_risk=1500, open_positions=1)  # 15% > 10%
    assert msg is not None and 'ریسک کل' in msg
    # تعداد پوزیشن بیشتر از حد مجاز
    msg = rm.check_position(balance=10000, position_risk=100, open_positions=3)
    assert msg is not None and 'تعداد پوزیشن' in msg
