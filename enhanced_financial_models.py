#!/usr/bin/env python3
"""
🚀 سیستم تحلیل مالی پیشرفته
شامل تمام پیشنهادات توسعه پلوتوس + مدل‌های محلی + API

ویژگی‌ها:
- تحلیل احساس آفلاین/آنلاین
- تحلیل تکنیکال
- تشخیص رژیم بازار
- پیش‌بینی قیمت
- مدیریت ریسک
- سیگنال‌های پیشرفته
- نظارت و کنترل
"""

import numpy as np
import pandas as pd
import json
import time
import hashlib
import pickle
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import warnings
warnings.filterwarnings("ignore")

# === مدل آفلاین ساده ===
class SimpleOfflineSentiment:
    """مدل احساس آفلاین ساده"""
    
    def __init__(self):
        # کلمات کلیدی مثبت/منفی مالی
        self.positive_words = {
            'profit', 'profits', 'gain', 'gains', 'growth', 'rise', 'rises', 'rising',
            'surge', 'surges', 'jump', 'jumps', 'soar', 'soars', 'rally', 'rallies',
            'bullish', 'bull', 'strong', 'strength', 'beat', 'beats', 'exceed',
            'exceeds', 'outperform', 'outperforms', 'record', 'high', 'up', 'positive',
            'optimistic', 'confidence', 'boost', 'boosts', 'breakthrough', 'success',
            'earnings', 'revenue', 'sales', 'buy', 'recommend', 'upgrade', 'upward',
            'momentum', 'breakout', 'recovery', 'improvement', 'expansion'
        }
        
        self.negative_words = {
            'loss', 'losses', 'decline', 'declines', 'fall', 'falls', 'falling',
            'drop', 'drops', 'crash', 'crashes', 'plunge', 'plunges', 'slump',
            'bearish', 'bear', 'weak', 'weakness', 'miss', 'misses', 'disappointing',
            'disappointed', 'concern', 'concerns', 'fear', 'fears', 'uncertainty',
            'risk', 'risks', 'pressure', 'pressures', 'challenge', 'challenges',
            'problem', 'problems', 'crisis', 'recession', 'inflation', 'volatile',
            'volatility', 'sell', 'selling', 'downgrade', 'downward', 'correction',
            'bankruptcy', 'debt', 'struggle', 'struggles', 'warning', 'alert'
        }
        
        # وزن‌های مختلف برای کلمات
        self.strong_positive = {'surge', 'soar', 'record', 'breakthrough', 'rally'}
        self.strong_negative = {'crash', 'plunge', 'crisis', 'bankruptcy', 'collapse'}
    
    def analyze(self, text: str) -> Dict[str, Any]:
        """تحلیل احساس آفلاین"""
        text_lower = text.lower()
        words = text_lower.split()
        
        positive_score = 0
        negative_score = 0
        
        for word in words:
            # حذف نشانه‌های نگارشی
            clean_word = word.strip('.,!?;:()[]{}"\'-')
            
            # امتیاز مثبت
            if clean_word in self.positive_words:
                if clean_word in self.strong_positive:
                    positive_score += 2
                else:
                    positive_score += 1
            
            # امتیاز منفی
            if clean_word in self.negative_words:
                if clean_word in self.strong_negative:
                    negative_score += 2
                else:
                    negative_score += 1
        
        total_score = positive_score - negative_score
        
        # تشخیص احساس
        if total_score > 0:
            sentiment = "positive"
            confidence = min(0.9, 0.5 + (total_score * 0.1))
        elif total_score < 0:
            sentiment = "negative"
            confidence = min(0.9, 0.5 + (abs(total_score) * 0.1))
        else:
            sentiment = "neutral"
            confidence = 0.5
        
        return {
            "method": "OFFLINE",
            "sentiment": sentiment,
            "confidence": round(confidence, 3),
            "positive_score": positive_score,
            "negative_score": negative_score,
            "total_score": total_score,
            "success": True
        }


# === تحلیل تکنیکال ===
class TechnicalAnalyzer:
    """تحلیل‌گر تکنیکال"""
    
    @staticmethod
    def calculate_sma(prices: List[float], period: int = 14) -> Optional[float]:
        """میانگین متحرک ساده"""
        if len(prices) < period:
            return None
        return sum(prices[-period:]) / period
    
    @staticmethod
    def calculate_ema(prices: List[float], period: int = 14) -> Optional[float]:
        """میانگین متحرک نمایی"""
        if len(prices) < period:
            return None
        
        sma = sum(prices[:period]) / period
        multiplier = 2 / (period + 1)
        ema = sma
        
        for price in prices[period:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))
        
        return ema
    
    @staticmethod
    def calculate_rsi(prices: List[float], period: int = 14) -> Optional[float]:
        """شاخص قدرت نسبی"""
        if len(prices) < period + 1:
            return None
        
        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        gains = [d if d > 0 else 0 for d in deltas]
        losses = [-d if d < 0 else 0 for d in deltas]
        
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def calculate_macd(prices: List[float], fast: int = 12, slow: int = 26, signal: int = 9) -> Optional[Dict]:
        """MACD"""
        if len(prices) < slow:
            return None
        
        ema_fast = TechnicalAnalyzer.calculate_ema(prices, fast)
        ema_slow = TechnicalAnalyzer.calculate_ema(prices, slow)
        
        if ema_fast is None or ema_slow is None:
            return None
        
        macd_line = ema_fast - ema_slow
        
        return {
            "macd": macd_line,
            "ema_fast": ema_fast,
            "ema_slow": ema_slow,
            "signal": "buy" if macd_line > 0 else "sell"
        }
    
    def analyze_trend(self, prices: List[float]) -> Dict[str, Any]:
        """تحلیل ترند"""
        if len(prices) < 14:
            return {"error": "داده کافی نیست"}
        
        # محاسبه اندیکاتورها
        sma_short = self.calculate_sma(prices, 5)
        sma_long = self.calculate_sma(prices, 14)
        rsi = self.calculate_rsi(prices)
        macd = self.calculate_macd(prices)
        
        # تحلیل ترند
        current_price = prices[-1]
        trend_direction = "neutral"
        
        if sma_short and sma_long:
            if sma_short > sma_long and current_price > sma_short:
                trend_direction = "upward"
            elif sma_short < sma_long and current_price < sma_short:
                trend_direction = "downward"
        
        # قدرت ترند
        trend_strength = "weak"
        if rsi:
            if rsi > 70:
                trend_strength = "strong_overbought"
            elif rsi < 30:
                trend_strength = "strong_oversold"
            elif 40 < rsi < 60:
                trend_strength = "moderate"
        
        return {
            "trend_direction": trend_direction,
            "trend_strength": trend_strength,
            "current_price": current_price,
            "sma_short": sma_short,
            "sma_long": sma_long,
            "rsi": rsi,
            "macd": macd,
            "support_level": min(prices[-10:]) if len(prices) >= 10 else None,
            "resistance_level": max(prices[-10:]) if len(prices) >= 10 else None
        }


# === تشخیص رژیم بازار ===
class MarketRegimeDetector:
    """تشخیص رژیم بازار"""
    
    def __init__(self):
        self.regimes = {
            "bull_market": {"volatility": "low", "trend": "upward", "sentiment": "positive"},
            "bear_market": {"volatility": "high", "trend": "downward", "sentiment": "negative"},
            "sideways": {"volatility": "low", "trend": "neutral", "sentiment": "neutral"},
            "crisis": {"volatility": "very_high", "trend": "downward", "sentiment": "negative"},
            "recovery": {"volatility": "medium", "trend": "upward", "sentiment": "positive"}
        }
    
    def calculate_volatility(self, prices: List[float], period: int = 20) -> float:
        """محاسبه نوسانات"""
        if len(prices) < period:
            return 0
        
        returns = []
        for i in range(1, len(prices)):
            ret = (prices[i] - prices[i-1]) / prices[i-1]
            returns.append(ret)
        
        if not returns:
            return 0
        
        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        
        return variance ** 0.5
    
    def detect_regime(self, prices: List[float], sentiment_score: float = 0) -> Dict[str, Any]:
        """تشخیص رژیم فعلی"""
        if len(prices) < 20:
            return {"regime": "unknown", "confidence": 0.0}
        
        # محاسبه شاخص‌ها
        volatility = self.calculate_volatility(prices)
        
        # ترند (30 روز گذشته)
        recent_trend = (prices[-1] - prices[-min(30, len(prices))]) / prices[-min(30, len(prices))]
        
        # تشخیص رژیم
        if volatility > 0.03:  # نوسانات بالا
            if recent_trend < -0.1:  # ترند نزولی قوی
                regime = "crisis"
            else:
                regime = "bear_market"
        elif volatility < 0.01:  # نوسانات کم
            if recent_trend > 0.05:  # ترند صعودی
                regime = "bull_market"
            elif recent_trend < -0.05:  # ترند نزولی
                regime = "bear_market"
            else:
                regime = "sideways"
        else:  # نوسانات متوسط
            if recent_trend > 0.02:
                regime = "recovery"
            else:
                regime = "sideways"
        
        # محاسبه اطمینان
        confidence = min(0.9, max(0.3, abs(recent_trend) * 5 + (volatility * 10)))
        
        return {
            "regime": regime,
            "confidence": round(confidence, 3),
            "volatility": round(volatility, 4),
            "trend_strength": round(recent_trend, 4),
            "characteristics": self.regimes.get(regime, {}),
            "recommendation": self._get_regime_recommendation(regime)
        }
    
    def _get_regime_recommendation(self, regime: str) -> str:
        """توصیه بر اساس رژیم"""
        recommendations = {
            "bull_market": "AGGRESSIVE_BUY",
            "bear_market": "DEFENSIVE_SELL",
            "sideways": "RANGE_TRADING",
            "crisis": "CASH_POSITION",
            "recovery": "GRADUAL_BUY"
        }
        return recommendations.get(regime, "HOLD")


# === پیش‌بینی قیمت ساده ===
class SimplePricePredictor:
    """پیش‌بینی قیمت ساده"""
    
    def __init__(self):
        self.models = ["linear_trend", "moving_average", "momentum"]
    
    def predict_linear_trend(self, prices: List[float], periods: int = 5) -> List[float]:
        """پیش‌بینی با خط روند"""
        if len(prices) < 3:
            return [prices[-1]] * periods
        
        # محاسبه شیب
        x = list(range(len(prices)))
        n = len(prices)
        
        sum_x = sum(x)
        sum_y = sum(prices)
        sum_xy = sum(x[i] * prices[i] for i in range(n))
        sum_x2 = sum(xi * xi for xi in x)
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        intercept = (sum_y - slope * sum_x) / n
        
        # پیش‌بینی
        predictions = []
        for i in range(periods):
            pred = slope * (len(prices) + i) + intercept
            predictions.append(pred)
        
        return predictions
    
    def predict_moving_average(self, prices: List[float], periods: int = 5, ma_period: int = 10) -> List[float]:
        """پیش‌بینی با میانگین متحرک"""
        if len(prices) < ma_period:
            return [prices[-1]] * periods
        
        ma = sum(prices[-ma_period:]) / ma_period
        return [ma] * periods
    
    def predict_momentum(self, prices: List[float], periods: int = 5) -> List[float]:
        """پیش‌بینی با مومنتوم"""
        if len(prices) < 3:
            return [prices[-1]] * periods
        
        # محاسبه مومنتوم
        momentum = prices[-1] - prices[-min(5, len(prices))]
        
        predictions = []
        last_price = prices[-1]
        
        for i in range(periods):
            pred = last_price + (momentum * (i + 1) * 0.1)  # کاهش تدریجی مومنتوم
            predictions.append(pred)
            last_price = pred
        
        return predictions
    
    def ensemble_predict(self, prices: List[float], periods: int = 5) -> Dict[str, Any]:
        """پیش‌بینی ترکیبی"""
        predictions = {
            "linear_trend": self.predict_linear_trend(prices, periods),
            "moving_average": self.predict_moving_average(prices, periods),
            "momentum": self.predict_momentum(prices, periods)
        }
        
        # میانگین‌گیری
        ensemble_pred = []
        for i in range(periods):
            avg_pred = sum(predictions[model][i] for model in predictions) / len(predictions)
            ensemble_pred.append(avg_pred)
        
        # محاسبه اطمینان
        current_price = prices[-1]
        price_change = abs(ensemble_pred[0] - current_price) / current_price
        confidence = max(0.3, min(0.8, 1 - price_change))
        
        return {
            "ensemble_prediction": ensemble_pred,
            "individual_predictions": predictions,
            "confidence": round(confidence, 3),
            "expected_direction": "up" if ensemble_pred[0] > current_price else "down",
            "expected_change": round((ensemble_pred[0] - current_price) / current_price * 100, 2)
        }


# === مدیریت ریسک ===
class RiskManager:
    """مدیر ریسک"""
    
    def __init__(self):
        self.max_position_size = 0.1  # حداکثر 10% پورتفولیو
        self.max_daily_loss = 0.02    # حداکثر 2% ضرر روزانه
        self.stop_loss_pct = 0.03     # 3% stop loss
        self.take_profit_pct = 0.06   # 6% take profit
    
    def calculate_position_size(self, account_balance: float, current_price: float, 
                              volatility: float, confidence: float) -> Dict[str, Any]:
        """محاسبه اندازه پوزیشن"""
        
        # اندازه پایه
        base_size = account_balance * self.max_position_size
        
        # تعدیل بر اساس نوسانات
        volatility_factor = max(0.3, min(1.0, 1 - (volatility * 20)))
        
        # تعدیل بر اساس اطمینان
        confidence_factor = max(0.5, confidence)
        
        # محاسبه نهایی
        position_value = base_size * volatility_factor * confidence_factor
        position_size = position_value / current_price
        
        # محاسبه stop loss و take profit
        stop_loss_price = current_price * (1 - self.stop_loss_pct)
        take_profit_price = current_price * (1 + self.take_profit_pct)
        
        return {
            "position_size": round(position_size, 4),
            "position_value": round(position_value, 2),
            "stop_loss_price": round(stop_loss_price, 4),
            "take_profit_price": round(take_profit_price, 4),
            "risk_amount": round(position_value * self.stop_loss_pct, 2),
            "reward_amount": round(position_value * self.take_profit_pct, 2),
            "risk_reward_ratio": round(self.take_profit_pct / self.stop_loss_pct, 2)
        }
    
    def evaluate_portfolio_risk(self, positions: List[Dict], current_prices: Dict) -> Dict[str, Any]:
        """ارزیابی ریسک پورتفولیو"""
        total_value = 0
        total_risk = 0
        
        for pos in positions:
            symbol = pos["symbol"]
            size = pos["size"]
            entry_price = pos["entry_price"]
            current_price = current_prices.get(symbol, entry_price)
            
            position_value = size * current_price
            position_risk = position_value * self.stop_loss_pct
            
            total_value += position_value
            total_risk += position_risk
        
        risk_percentage = (total_risk / total_value * 100) if total_value > 0 else 0
        
        return {
            "total_portfolio_value": round(total_value, 2),
            "total_risk_amount": round(total_risk, 2),
            "risk_percentage": round(risk_percentage, 2),
            "risk_status": "LOW" if risk_percentage < 5 else "MEDIUM" if risk_percentage < 10 else "HIGH",
            "recommendations": self._get_risk_recommendations(risk_percentage)
        }
    
    def _get_risk_recommendations(self, risk_pct: float) -> List[str]:
        """توصیه‌های مدیریت ریسک"""
        recommendations = []
        
        if risk_pct > 15:
            recommendations.append("فوری: کاهش اندازه پوزیشن‌ها")
            recommendations.append("بستن پوزیشن‌های پر ریسک")
        elif risk_pct > 10:
            recommendations.append("کاهش تدریجی پوزیشن‌ها")
            recommendations.append("افزایش نقدینگی")
        elif risk_pct > 5:
            recommendations.append("نظارت دقیق بر پوزیشن‌ها")
            recommendations.append("آماده‌باش برای کاهش ریسک")
        else:
            recommendations.append("وضعیت ریسک مناسب")
            recommendations.append("امکان افزایش محدود پوزیشن‌ها")
        
        return recommendations


# === سیستم پیشرفته ===
class AdvancedFinancialSystem:
    """سیستم تحلیل مالی پیشرفته"""
    
    def __init__(self, hf_token: Optional[str] = None):
        self.hf_token = hf_token
        self.cache = {}
        
        # اجزاء سیستم
        self.sentiment_analyzer = SimpleOfflineSentiment()
        self.technical_analyzer = TechnicalAnalyzer()
        self.regime_detector = MarketRegimeDetector()
        self.price_predictor = SimplePricePredictor()
        self.risk_manager = RiskManager()
        
        print("🚀 سیستم تحلیل مالی پیشرفته آماده شد")
    
    def comprehensive_analysis(self, symbol: str, price_data: List[float], 
                             news_data: List[str], account_balance: float = 10000) -> Dict[str, Any]:
        """تحلیل جامع"""
        
        print(f"🔍 تحلیل جامع {symbol}...")
        
        # تحلیل احساس
        sentiment_results = []
        for news in news_data:
            sentiment = self.sentiment_analyzer.analyze(news)
            sentiment_results.append(sentiment)
        
        # میانگین احساس
        avg_sentiment = sum(s["total_score"] for s in sentiment_results) / len(sentiment_results)
        
        # تحلیل تکنیکال
        technical_analysis = self.technical_analyzer.analyze_trend(price_data)
        
        # تشخیص رژیم
        regime_analysis = self.regime_detector.detect_regime(price_data, avg_sentiment)
        
        # پیش‌بینی قیمت
        price_prediction = self.price_predictor.ensemble_predict(price_data)
        
        # مدیریت ریسک
        current_price = price_data[-1]
        volatility = self.regime_detector.calculate_volatility(price_data)
        confidence = (sentiment_results[0]["confidence"] + 
                     technical_analysis.get("rsi", 50)/100 + 
                     regime_analysis["confidence"]) / 3
        
        risk_analysis = self.risk_manager.calculate_position_size(
            account_balance, current_price, volatility, confidence
        )
        
        # سیگنال نهایی
        final_signal = self._generate_advanced_signal(
            sentiment_results, technical_analysis, regime_analysis, 
            price_prediction, risk_analysis
        )
        
        return {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "sentiment_analysis": {
                "individual_results": sentiment_results,
                "average_sentiment": avg_sentiment,
                "sentiment_strength": "strong" if abs(avg_sentiment) > 2 else "moderate" if abs(avg_sentiment) > 1 else "weak"
            },
            "technical_analysis": technical_analysis,
            "regime_analysis": regime_analysis,
            "price_prediction": price_prediction,
            "risk_analysis": risk_analysis,
            "final_signal": final_signal,
            "confidence_score": round(confidence, 3),
            "recommendations": self._get_comprehensive_recommendations(final_signal, risk_analysis, regime_analysis)
        }
    
    def _generate_advanced_signal(self, sentiment_results: List[Dict], 
                                technical_analysis: Dict, regime_analysis: Dict,
                                price_prediction: Dict, risk_analysis: Dict) -> str:
        """تولید سیگنال پیشرفته"""
        
        # وزن‌دهی عوامل
        sentiment_weight = 0.3
        technical_weight = 0.3
        regime_weight = 0.2
        prediction_weight = 0.2
        
        # امتیاز احساس
        avg_sentiment = sum(s["total_score"] for s in sentiment_results) / len(sentiment_results)
        sentiment_score = max(-1, min(1, avg_sentiment / 3))
        
        # امتیاز تکنیکال
        technical_score = 0
        if technical_analysis.get("trend_direction") == "upward":
            technical_score += 0.5
        elif technical_analysis.get("trend_direction") == "downward":
            technical_score -= 0.5
        
        rsi = technical_analysis.get("rsi", 50)
        if rsi < 30:
            technical_score += 0.3  # oversold
        elif rsi > 70:
            technical_score -= 0.3  # overbought
        
        # امتیاز رژیم
        regime_score = 0
        regime = regime_analysis.get("regime", "unknown")
        if regime in ["bull_market", "recovery"]:
            regime_score = 0.5
        elif regime in ["bear_market", "crisis"]:
            regime_score = -0.5
        
        # امتیاز پیش‌بینی
        prediction_score = 0
        if price_prediction.get("expected_direction") == "up":
            prediction_score = 0.5
        elif price_prediction.get("expected_direction") == "down":
            prediction_score = -0.5
        
        # محاسبه امتیاز کل
        total_score = (sentiment_score * sentiment_weight + 
                      technical_score * technical_weight +
                      regime_score * regime_weight +
                      prediction_score * prediction_weight)
        
        # تشخیص سیگنال
        if total_score > 0.3:
            if total_score > 0.6:
                return "STRONG_BUY"
            else:
                return "BUY"
        elif total_score < -0.3:
            if total_score < -0.6:
                return "STRONG_SELL"
            else:
                return "SELL"
        else:
            return "HOLD"
    
    def _get_comprehensive_recommendations(self, signal: str, risk_analysis: Dict, 
                                         regime_analysis: Dict) -> List[str]:
        """توصیه‌های جامع"""
        recommendations = []
        
        # توصیه‌های سیگنال
        if signal == "STRONG_BUY":
            recommendations.append("فرصت خرید قوی - افزایش پوزیشن")
            recommendations.append(f"هدف سود: {risk_analysis['take_profit_price']}")
        elif signal == "BUY":
            recommendations.append("فرصت خرید متوسط - ورود تدریجی")
        elif signal == "STRONG_SELL":
            recommendations.append("هشدار فروش قوی - کاهش فوری پوزیشن")
        elif signal == "SELL":
            recommendations.append("توصیه فروش - کاهش تدریجی پوزیشن")
        else:
            recommendations.append("انتظار و نظارت دقیق")
        
        # توصیه‌های ریسک
        risk_reward = risk_analysis.get("risk_reward_ratio", 1)
        if risk_reward < 1.5:
            recommendations.append("نسبت ریسک/پاداش نامناسب - احتیاط")
        
        # توصیه‌های رژیم
        regime = regime_analysis.get("regime", "unknown")
        if regime == "crisis":
            recommendations.append("وضعیت بحرانی - حفظ نقدینگی")
        elif regime == "bull_market":
            recommendations.append("بازار صعودی - فرصت رشد")
        
        return recommendations
    
    def monitor_and_alert(self, symbol: str, current_price: float, 
                         target_price: float, stop_loss: float) -> Dict[str, Any]:
        """نظارت و هشدار"""
        
        price_change_pct = (current_price - target_price) / target_price * 100
        
        alerts = []
        status = "NORMAL"
        
        if current_price <= stop_loss:
            alerts.append("🚨 STOP LOSS HIT - فروش فوری")
            status = "CRITICAL"
        elif current_price >= target_price:
            alerts.append("🎯 TARGET REACHED - در نظر گیری سود")
            status = "TARGET_HIT"
        elif abs(price_change_pct) > 5:
            alerts.append(f"⚠️ تغییر قیمت قابل توجه: {price_change_pct:.1f}%")
            status = "SIGNIFICANT_MOVE"
        
        return {
            "symbol": symbol,
            "current_price": current_price,
            "target_price": target_price,
            "stop_loss": stop_loss,
            "price_change_pct": round(price_change_pct, 2),
            "status": status,
            "alerts": alerts,
            "timestamp": datetime.now().isoformat()
        }


# === تست سیستم ===
def test_advanced_system():
    """تست سیستم پیشرفته"""
    print("🧪 تست سیستم پیشرفته")
    print("=" * 40)
    
    # راه‌اندازی
    system = AdvancedFinancialSystem()
    
    # داده‌های نمونه
    symbol = "AAPL"
    price_data = [150, 152, 151, 153, 155, 154, 156, 158, 157, 159, 161, 160, 162, 164, 163, 165, 167, 166, 168, 170]
    news_data = [
        "Apple reports record quarterly earnings beating expectations",
        "iPhone sales surge in emerging markets driving growth",
        "Strong services revenue boosts Apple's financial performance",
        "Market optimism grows around Apple's innovation pipeline",
        "Apple stock rallies on positive analyst upgrades"
    ]
    
    # تحلیل جامع
    analysis = system.comprehensive_analysis(symbol, price_data, news_data)
    
    # نمایش نتایج
    print(f"\n📊 تحلیل {symbol}:")
    print(f"🎯 سیگنال نهایی: {analysis['final_signal']}")
    print(f"🎲 امتیاز اطمینان: {analysis['confidence_score']}")
    print(f"📈 رژیم بازار: {analysis['regime_analysis']['regime']}")
    print(f"💹 پیش‌بینی قیمت: {analysis['price_prediction']['expected_direction']}")
    print(f"⚖️ نسبت ریسک/پاداش: {analysis['risk_analysis']['risk_reward_ratio']}")
    
    print(f"\n📋 توصیه‌ها:")
    for i, rec in enumerate(analysis['recommendations'], 1):
        print(f"{i}. {rec}")
    
    # تست نظارت
    print(f"\n🔍 تست نظارت:")
    monitor_result = system.monitor_and_alert(
        symbol, 172, 175, 160
    )
    print(f"وضعیت: {monitor_result['status']}")
    for alert in monitor_result['alerts']:
        print(f"هشدار: {alert}")
    
    print(f"\n✅ تست سیستم پیشرفته کامل شد!")
    
    return analysis


if __name__ == "__main__":
    test_advanced_system() 