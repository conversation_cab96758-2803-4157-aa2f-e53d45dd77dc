#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Advanced tests for Hierarchical Reinforcement Learning

Tests cover:
1. Policy networks (Strategic, Tactical, Execution)
2. Hierarchical decision making
3. Training and optimization
4. Strategy analysis and performance
5. Model saving/loading
6. Error handling and edge cases
"""

import pytest
import numpy as np
import torch
import pandas as pd
from unittest.mock import Mock, patch
import tempfile
import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.hierarchical_rl import HierarchicalRL, StrategicPolicy, TacticalPolicy, ExecutionPolicy

class TestStrategicPolicy:
    """Test Strategic Policy Network"""
    
    def test_strategic_policy_initialization(self):
        """Test strategic policy initialization"""
        obs_dim = 10
        n_strategies = 4
        policy = StrategicPolicy(obs_dim, n_strategies)
        
        assert policy.n_strategies == n_strategies
        assert isinstance(policy.strategy_net, torch.nn.Sequential)
        assert isinstance(policy.value_net, torch.nn.Sequential)
    
    def test_strategic_policy_forward(self):
        """Test strategic policy forward pass"""
        obs_dim = 10
        policy = StrategicPolicy(obs_dim)
        
        obs = torch.randn(1, obs_dim)
        strategy_logits, value = policy.forward(obs)
        
        assert strategy_logits.shape == (1, 4)  # 4 strategies
        assert value.shape == (1, 1)
        assert torch.is_tensor(strategy_logits)
        assert torch.is_tensor(value)
    
    def test_strategic_policy_probs(self):
        """Test strategic policy probability computation"""
        obs_dim = 10
        policy = StrategicPolicy(obs_dim)
        
        obs = torch.randn(1, obs_dim)
        probs = policy.get_strategy_probs(obs)
        
        assert probs.shape == (1, 4)
        assert torch.allclose(probs.sum(dim=1), torch.ones(1), atol=1e-6)
        assert torch.all(probs >= 0) and torch.all(probs <= 1)
    
    def test_strategic_policy_different_obs_dim(self):
        """Test strategic policy with different observation dimensions"""
        for obs_dim in [5, 15, 30]:
            policy = StrategicPolicy(obs_dim)
            obs = torch.randn(1, obs_dim)
            strategy_logits, value = policy.forward(obs)
            
            assert strategy_logits.shape == (1, 4)
            assert value.shape == (1, 1)

class TestTacticalPolicy:
    """Test Tactical Policy Network"""
    
    def test_tactical_policy_initialization(self):
        """Test tactical policy initialization"""
        obs_dim = 10
        n_actions = 3
        policy = TacticalPolicy(obs_dim, n_actions)
        
        assert policy.n_actions == n_actions
        assert isinstance(policy.tactical_net, torch.nn.Sequential)
        assert isinstance(policy.value_net, torch.nn.Sequential)
    
    def test_tactical_policy_forward(self):
        """Test tactical policy forward pass"""
        obs_dim = 10
        policy = TacticalPolicy(obs_dim)
        
        obs = torch.randn(1, obs_dim)
        tactical_logits, value = policy.forward(obs)
        
        assert tactical_logits.shape == (1, 3)  # 3 actions: hold, enter, exit
        assert value.shape == (1, 1)
        assert torch.is_tensor(tactical_logits)
        assert torch.is_tensor(value)
    
    def test_tactical_policy_probs(self):
        """Test tactical policy probability computation"""
        obs_dim = 10
        policy = TacticalPolicy(obs_dim)
        
        obs = torch.randn(1, obs_dim)
        probs = policy.get_action_probs(obs)
        
        assert probs.shape == (1, 3)
        assert torch.allclose(probs.sum(dim=1), torch.ones(1), atol=1e-6)
        assert torch.all(probs >= 0) and torch.all(probs <= 1)
    
    def test_tactical_policy_different_actions(self):
        """Test tactical policy with different action spaces"""
        obs_dim = 10
        for n_actions in [2, 4, 5]:
            policy = TacticalPolicy(obs_dim, n_actions)
            obs = torch.randn(1, obs_dim)
            tactical_logits, value = policy.forward(obs)
            
            assert tactical_logits.shape == (1, n_actions)
            assert value.shape == (1, 1)

class TestExecutionPolicy:
    """Test Execution Policy Network"""
    
    def test_execution_policy_initialization(self):
        """Test execution policy initialization"""
        obs_dim = 10
        action_dim = 1
        policy = ExecutionPolicy(obs_dim, action_dim)
        
        assert policy.action_dim == action_dim
        assert isinstance(policy.execution_net, torch.nn.Sequential)
        assert isinstance(policy.value_net, torch.nn.Sequential)
    
    def test_execution_policy_forward(self):
        """Test execution policy forward pass"""
        obs_dim = 10
        policy = ExecutionPolicy(obs_dim)
        
        obs = torch.randn(1, obs_dim)
        action, value = policy.forward(obs)
        
        assert action.shape == (1, 1)  # Continuous action
        assert value.shape == (1, 1)
        assert torch.is_tensor(action)
        assert torch.is_tensor(value)
    
    def test_execution_policy_different_action_dim(self):
        """Test execution policy with different action dimensions"""
        obs_dim = 10
        for action_dim in [1, 2, 3]:
            policy = ExecutionPolicy(obs_dim, action_dim)
            obs = torch.randn(1, obs_dim)
            action, value = policy.forward(obs)
            
            assert action.shape == (1, action_dim)
            assert value.shape == (1, 1)

class TestHierarchicalRL:
    """Test Hierarchical RL System"""
    
    @pytest.fixture
    def hierarchical_rl(self):
        """Create hierarchical RL instance for testing"""
        obs_dim = 20
        return HierarchicalRL(obs_dim=obs_dim, device='cpu')
    
    def test_hierarchical_rl_initialization(self, hierarchical_rl):
        """Test hierarchical RL initialization"""
        assert hierarchical_rl.obs_dim == 20
        assert hierarchical_rl.device == 'cpu'
        assert isinstance(hierarchical_rl.strategic_policy, StrategicPolicy)
        assert isinstance(hierarchical_rl.tactical_policy, TacticalPolicy)
        assert isinstance(hierarchical_rl.execution_policy, ExecutionPolicy)
        assert len(hierarchical_rl.strategies) == 4
    
    def test_strategic_observation_extraction(self, hierarchical_rl):
        """Test strategic observation feature extraction"""
        obs = np.random.randn(20)
        strategic_obs = hierarchical_rl.get_strategic_observation(obs)
        
        assert strategic_obs.shape == (1, 10)  # First 10 features
        assert torch.is_tensor(strategic_obs)
        assert strategic_obs.device.type == hierarchical_rl.device
    
    def test_tactical_observation_extraction(self, hierarchical_rl):
        """Test tactical observation feature extraction"""
        obs = np.random.randn(20)
        strategy = 1
        tactical_obs = hierarchical_rl.get_tactical_observation(obs, strategy)
        
        assert tactical_obs.shape == (1, 11)  # Features 10-20 + strategy
        assert torch.is_tensor(tactical_obs)
        assert tactical_obs.device.type == hierarchical_rl.device
    
    def test_execution_observation_extraction(self, hierarchical_rl):
        """Test execution observation feature extraction"""
        obs = np.random.randn(20)
        strategy = 1
        tactical_action = 2
        execution_obs = hierarchical_rl.get_execution_observation(obs, strategy, tactical_action)
        
        assert execution_obs.shape == (1, 12)  # Features 20+ + strategy + tactical
        assert torch.is_tensor(execution_obs)
        assert execution_obs.device.type == hierarchical_rl.device
    
    def test_strategy_selection(self, hierarchical_rl):
        """Test strategy selection"""
        obs = np.random.randn(20)
        strategy = hierarchical_rl.select_strategy(obs)
        
        assert isinstance(strategy, int)
        assert 0 <= strategy < 4
        assert hierarchical_rl.current_strategy == strategy
    
    def test_tactical_action_selection(self, hierarchical_rl):
        """Test tactical action selection"""
        obs = np.random.randn(20)
        strategy = 1
        tactical_action = hierarchical_rl.select_tactical_action(obs, strategy)
        
        assert isinstance(tactical_action, int)
        assert 0 <= tactical_action < 3
        assert hierarchical_rl.current_tactical_action == tactical_action
    
    def test_execution_action_selection(self, hierarchical_rl):
        """Test execution action selection"""
        obs = np.random.randn(20)
        strategy = 1
        tactical_action = 2
        execution_action = hierarchical_rl.select_execution_action(obs, strategy, tactical_action)
        
        assert isinstance(execution_action, float)
        assert -1 <= execution_action <= 1  # Bounded by tanh
        assert hierarchical_rl.current_execution_action == execution_action
    
    def test_complete_hierarchical_decision(self, hierarchical_rl):
        """Test complete hierarchical decision making"""
        obs = np.random.randn(20)
        strategy, tactical_action, execution_action = hierarchical_rl.act(obs)
        
        assert isinstance(strategy, int)
        assert isinstance(tactical_action, int)
        assert isinstance(execution_action, float)
        assert 0 <= strategy < 4
        assert 0 <= tactical_action < 3
        assert -1 <= execution_action <= 1
    
    def test_gae_computation(self, hierarchical_rl):
        """Test Generalized Advantage Estimation computation"""
        rewards = [1.0, 0.5, -0.5, 1.0]
        values = [0.8, 0.6, 0.4, 0.2]
        dones = [False, False, False, True]
        
        advantages = hierarchical_rl.compute_gae(rewards, values, dones)
        
        assert len(advantages) == len(rewards)
        assert all(isinstance(adv, float) for adv in advantages)
    
    def test_strategy_usage_tracking(self, hierarchical_rl):
        """Test strategy usage tracking"""
        # Simulate some strategy selections
        for _ in range(10):
            obs = np.random.randn(20)
            hierarchical_rl.select_strategy(obs)
        
        # Check that usage is tracked
        total_usage = sum(hierarchical_rl.training_history['strategy_usage'].values())
        assert total_usage == 10
    
    def test_model_saving_and_loading(self, hierarchical_rl):
        """Test model saving and loading"""
        with tempfile.NamedTemporaryFile(suffix='.pth', delete=False) as tmp_file:
            save_path = tmp_file.name
        
        try:
            # Save model
            hierarchical_rl.save(save_path)
            assert os.path.exists(save_path)
            
            # Create new model and load
            new_model = HierarchicalRL(obs_dim=20)
            new_model.load(save_path)
            
            # Test that loaded model works
            obs = np.random.randn(20)
            strategy, tactical, execution = new_model.act(obs)
            
            assert isinstance(strategy, int)
            assert isinstance(tactical, int)
            assert isinstance(execution, float)
            
        finally:
            if os.path.exists(save_path):
                os.unlink(save_path)
    
    def test_strategy_analysis(self, hierarchical_rl):
        """Test strategy analysis functionality"""
        # Simulate some training
        for _ in range(20):
            obs = np.random.randn(20)
            hierarchical_rl.select_strategy(obs)
        
        analysis = hierarchical_rl.get_strategy_analysis()
        
        assert 'strategy_usage' in analysis
        assert 'training_losses' in analysis
        assert 'mean_reward' in analysis
        assert len(analysis['strategy_usage']) == 4
    
    def test_current_state_tracking(self, hierarchical_rl):
        """Test current state tracking"""
        obs = np.random.randn(20)
        hierarchical_rl.act(obs)
        
        state = hierarchical_rl.get_current_state()
        
        assert 'current_strategy' in state
        assert 'current_tactical_action' in state
        assert 'current_execution_action' in state
        assert 'strategy_name' in state
        assert state['current_strategy'] is not None
        assert state['current_tactical_action'] is not None
        assert state['current_execution_action'] is not None
    
    def test_error_handling_invalid_obs_dim(self):
        """Test error handling for invalid observation dimensions"""
        with pytest.raises(Exception):
            HierarchicalRL(obs_dim=0)
    
    def test_error_handling_invalid_device(self):
        """Test error handling for invalid device"""
        with pytest.raises(Exception):
            HierarchicalRL(obs_dim=20, device='invalid_device')
    
    def test_training_step_with_empty_batch(self, hierarchical_rl):
        """Test training step with empty batch data"""
        empty_batch = {
            'observations': [],
            'strategies': [],
            'tactical_actions': [],
            'execution_actions': [],
            'rewards': [],
            'dones': []
        }
        
        # Should not raise exception
        result = hierarchical_rl.train_step(empty_batch)
        assert isinstance(result, dict)
    
    def test_training_step_with_valid_batch(self, hierarchical_rl):
        """Test training step with valid batch data"""
        batch_data = {
            'observations': [np.random.randn(20) for _ in range(5)],
            'strategies': [0, 1, 2, 1, 0],
            'tactical_actions': [0, 1, 2, 1, 0],
            'execution_actions': [0.5, -0.3, 0.8, -0.1, 0.2],
            'rewards': [1.0, 0.5, -0.5, 1.0, 0.8],
            'dones': [False, False, False, False, True]
        }
        
        result = hierarchical_rl.train_step(batch_data)
        
        assert 'strategic_loss' in result
        assert 'tactical_loss' in result
        assert 'execution_loss' in result
        assert 'mean_reward' in result
        assert all(isinstance(v, float) for v in result.values())
    
    def test_different_obs_dimensions(self):
        """Test hierarchical RL with different observation dimensions"""
        for obs_dim in [10, 30, 50]:
            model = HierarchicalRL(obs_dim=obs_dim)
            obs = np.random.randn(obs_dim)
            strategy, tactical, execution = model.act(obs)
            
            assert isinstance(strategy, int)
            assert isinstance(tactical, int)
            assert isinstance(execution, float)
    
    def test_different_learning_rates(self):
        """Test hierarchical RL with different learning rates"""
        for lr in [1e-4, 3e-4, 1e-3]:
            model = HierarchicalRL(obs_dim=20, learning_rate=lr)
            assert model.learning_rate == lr
    
    def test_different_gamma_values(self):
        """Test hierarchical RL with different gamma values"""
        for gamma in [0.9, 0.95, 0.99]:
            model = HierarchicalRL(obs_dim=20, gamma=gamma)
            assert model.gamma == gamma
    
    def test_strategy_names(self, hierarchical_rl):
        """Test strategy name mapping"""
        expected_strategies = {
            0: 'trend_following',
            1: 'mean_reversion',
            2: 'breakout',
            3: 'momentum'
        }
        
        assert hierarchical_rl.strategies == expected_strategies
    
    def test_training_history_initialization(self, hierarchical_rl):
        """Test training history initialization"""
        history = hierarchical_rl.training_history
        
        assert 'strategic_loss' in history
        assert 'tactical_loss' in history
        assert 'execution_loss' in history
        assert 'total_reward' in history
        assert 'strategy_usage' in history
        
        assert len(history['strategy_usage']) == 4
        assert all(count == 0 for count in history['strategy_usage'].values())
    
    def test_optimizer_initialization(self, hierarchical_rl):
        """Test optimizer initialization"""
        assert isinstance(hierarchical_rl.strategic_optimizer, torch.optim.Adam)
        assert isinstance(hierarchical_rl.tactical_optimizer, torch.optim.Adam)
        assert isinstance(hierarchical_rl.execution_optimizer, torch.optim.Adam)
    
    def test_policy_device_assignment(self, hierarchical_rl):
        """Test that policies are assigned to correct device"""
        device = hierarchical_rl.device
        assert next(hierarchical_rl.strategic_policy.parameters()).device.type == device
        assert next(hierarchical_rl.tactical_policy.parameters()).device.type == device
        assert next(hierarchical_rl.execution_policy.parameters()).device.type == device
    
    def test_continuous_execution_actions(self, hierarchical_rl):
        """Test that execution actions are continuous and bounded"""
        obs = np.random.randn(20)
        strategy, tactical, execution = hierarchical_rl.act(obs)
        
        assert isinstance(execution, float)
        assert -1 <= execution <= 1  # Bounded by tanh
    
    def test_discrete_strategy_actions(self, hierarchical_rl):
        """Test that strategy actions are discrete"""
        obs = np.random.randn(20)
        strategy, tactical, execution = hierarchical_rl.act(obs)
        
        assert isinstance(strategy, int)
        assert strategy in [0, 1, 2, 3]
    
    def test_discrete_tactical_actions(self, hierarchical_rl):
        """Test that tactical actions are discrete"""
        obs = np.random.randn(20)
        strategy, tactical, execution = hierarchical_rl.act(obs)
        
        assert isinstance(tactical, int)
        assert tactical in [0, 1, 2]  # hold, enter, exit
    
    def test_hierarchical_decision_consistency(self, hierarchical_rl):
        """Test consistency of hierarchical decision making"""
        obs = np.random.randn(20)
        
        # Multiple calls should produce consistent results for same obs
        strategy1, tactical1, execution1 = hierarchical_rl.act(obs)
        strategy2, tactical2, execution2 = hierarchical_rl.act(obs)
        
        # Note: Due to stochasticity, we don't expect exact same results
        # But we expect valid ranges
        assert 0 <= strategy1 < 4 and 0 <= strategy2 < 4
        assert 0 <= tactical1 < 3 and 0 <= tactical2 < 3
        assert -1 <= execution1 <= 1 and -1 <= execution2 <= 1

if __name__ == "__main__":
    pytest.main([__file__]) 