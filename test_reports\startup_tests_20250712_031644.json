{"type": "startup_tests", "timestamp": "2025-07-12T03:16:44.772384", "summary": {"total_tests": 4, "passed": 0, "failed": 4, "success_rate": 0.0, "total_duration": 4.568551063537598}, "results": [{"name": "test_advanced_risk_manager.py", "passed": false, "duration": 1.0409517288208008, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 391\nE       await asyncio.to_thread(env.stop)\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:16:41.230626"}, {"name": "test_smart_portfolio_manager.py", "passed": false, "duration": 0.9388391971588135, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 391\nE       await asyncio.to_thread(env.stop)\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:16:42.171473"}, {"name": "test_integrated_system.py", "passed": false, "duration": 0.9986941814422607, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 391\nE       await asyncio.to_thread(env.stop)\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:16:43.172168"}, {"name": "test_integration_*.py", "passed": false, "duration": 1.5900659561157227, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 391\nE       await asyncio.to_thread(env.stop)\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:16:44.769384"}]}