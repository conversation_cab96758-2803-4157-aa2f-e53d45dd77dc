import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import random
from collections import deque
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DeepRLMarketMaker(nn.Module):
    """
    Deep RL model for market making decisions
    """
    def __init__(self, input_dim, output_dim):
        super(DeepRLMarketMaker, self).__init__()
        self.fc1 = nn.Linear(input_dim, 128)
        self.fc2 = nn.Linear(128, 64)
        self.fc3 = nn.Linear(64, output_dim)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.relu(self.fc2(x))
        return self.fc3(x)

class AutoMarketMaker:
    """
    Advanced RL-based market maker with adaptive spread, inventory management,
    and risk control features.
    """
    def __init__(self, 
                 spread_range=(0.0001, 0.001), 
                 inventory_limit=10000,
                 learning_rate=0.001,
                 gamma=0.99,
                 epsilon=0.1,
                 epsilon_decay=0.995,
                 min_epsilon=0.01,
                 memory_size=10000,
                 batch_size=64,
                 update_target_every=100,
                 risk_aversion=0.5,
                 use_deep_rl=True):
        """
        Initialize the AutoMarketMaker with configurable parameters.
        
        Args:
            spread_range (tuple): Min and max spread values
            inventory_limit (int): Maximum allowed inventory
            learning_rate (float): Learning rate for RL model
            gamma (float): Discount factor for future rewards
            epsilon (float): Exploration rate
            epsilon_decay (float): Decay rate for epsilon
            min_epsilon (float): Minimum exploration rate
            memory_size (int): Size of replay memory
            batch_size (int): Batch size for training
            update_target_every (int): Steps between target network updates
            risk_aversion (float): Risk aversion parameter (0-1)
            use_deep_rl (bool): Whether to use deep RL or simple RL
        """
        self.spread_range = spread_range
        self.inventory = 0
        self.inventory_limit = inventory_limit
        self.history = []
        self.quote_history = []
        self.pnl_history = []
        self.total_pnl = 0
        self.risk_aversion = risk_aversion
        self.use_deep_rl = use_deep_rl
        
        # RL parameters
        self.learning_rate = learning_rate
        self.gamma = gamma
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.min_epsilon = min_epsilon
        self.memory_size = memory_size
        self.batch_size = batch_size
        self.update_target_every = update_target_every
        
        # Initialize memory
        self.memory = deque(maxlen=memory_size)
        self.step_count = 0
        
        # Define state and action dimensions
        self.state_dim = 7  # [bid, ask, mid, spread, inventory, volatility, volume]
        self.action_dim = 9  # 9 different spread levels
        
        # Initialize models if using deep RL
        if self.use_deep_rl:
            self.model = DeepRLMarketMaker(self.state_dim, self.action_dim)
            self.target_model = DeepRLMarketMaker(self.state_dim, self.action_dim)
            self.target_model.load_state_dict(self.model.state_dict())
            self.optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
            self.criterion = nn.MSELoss()
        else:
            # Simple Q-table for discrete states
            self.q_table = {}
    
    def _get_state(self, order_book, symbol, market_features=None):
        """
        Extract state features from order book and market data.
        
        Args:
            order_book (dict): Order book data
            symbol (str): Trading symbol
            market_features (dict): Additional market features
            
        Returns:
            list: State features
        """
        if symbol not in order_book:
            return np.zeros(self.state_dim)
            
        bid = order_book[symbol].get('bid', 0)
        ask = order_book[symbol].get('ask', 0)
        
        if bid == 0 or ask == 0:
            return np.zeros(self.state_dim)
            
        mid = (bid + ask) / 2
        spread = ask - bid
        
        # Normalized inventory position (-1 to 1)
        norm_inventory = self.inventory / self.inventory_limit if self.inventory_limit > 0 else 0
        
        # Default values for volatility and volume
        volatility = 0.0
        volume = 0.0
        
        # Use provided market features if available
        if market_features:
            volatility = market_features.get('volatility', 0.0)
            volume = market_features.get('volume', 0.0)
        
        return np.array([bid, ask, mid, spread, norm_inventory, volatility, volume])
    
    def _get_discrete_state(self, state):
        """
        Convert continuous state to discrete state for Q-table.
        
        Args:
            state (np.array): Continuous state
            
        Returns:
            tuple: Discretized state
        """
        # Simple discretization for Q-table
        spread_idx = int(min(9, max(0, (state[3] - self.spread_range[0]) / 
                                     (self.spread_range[1] - self.spread_range[0]) * 10)))
        inventory_idx = int(min(9, max(0, (state[4] + 1) * 5)))  # -1 to 1 -> 0 to 10
        volatility_idx = int(min(4, max(0, state[5] * 5)))
        volume_idx = int(min(4, max(0, state[6] * 5)))
        
        return (spread_idx, inventory_idx, volatility_idx, volume_idx)
    
    def _get_action_spread(self, action_idx):
        """
        Convert action index to actual spread value.
        
        Args:
            action_idx (int): Action index
            
        Returns:
            float: Spread value
        """
        min_spread, max_spread = self.spread_range
        return min_spread + (action_idx / (self.action_dim - 1)) * (max_spread - min_spread)
    
    def decide_spread(self, order_book, symbol, market_features=None):
        """
        Choose spread based on current state using RL model.
        
        Args:
            order_book (dict): Order book data
            symbol (str): Trading symbol
            market_features (dict): Additional market features
            
        Returns:
            float: Selected spread
        """
        state = self._get_state(order_book, symbol, market_features)
        
        # Exploration: random action
        if random.random() < self.epsilon:
            action_idx = random.randint(0, self.action_dim - 1)
        # Exploitation: best action from model
        else:
            if self.use_deep_rl:
                with torch.no_grad():
                    state_tensor = torch.FloatTensor(state).unsqueeze(0)
                    q_values = self.model(state_tensor)
                    action_idx = torch.argmax(q_values).item()
            else:
                discrete_state = self._get_discrete_state(state)
                if discrete_state not in self.q_table:
                    self.q_table[discrete_state] = np.zeros(self.action_dim)
                action_idx = np.argmax(self.q_table[discrete_state])
        
        # Decay epsilon
        self.epsilon = max(self.min_epsilon, self.epsilon * self.epsilon_decay)
        
        # Convert action to spread
        return self._get_action_spread(action_idx)
    
    def quote(self, order_book, symbol, market_features=None):
        """
        Generate bid and ask quotes based on market conditions.
        
        Args:
            order_book (dict): Order book data
            symbol (str): Trading symbol
            market_features (dict): Additional market features
            
        Returns:
            dict: Quote with bid, ask and spread
        """
        if symbol not in order_book:
            return {'bid': 0, 'ask': 0, 'spread': 0}
            
        spread = self.decide_spread(order_book, symbol, market_features)
        mid = (order_book[symbol]['bid'] + order_book[symbol]['ask']) / 2
        
        # Adjust quotes based on inventory
        inventory_skew = self.inventory / self.inventory_limit if self.inventory_limit > 0 else 0
        skew_factor = self.risk_aversion * inventory_skew * spread / 2
        
        bid = mid - spread / 2 - skew_factor
        ask = mid + spread / 2 - skew_factor
        
        quote = {'bid': bid, 'ask': ask, 'spread': spread}
        self.quote_history.append(quote)
        return quote
    
    def update_inventory(self, trade_side, amount, price=None):
        """
        Update inventory after a trade.
        
        Args:
            trade_side (str): 'buy' or 'sell'
            amount (float): Trade amount
            price (float): Trade price (optional)
        """
        old_inventory = self.inventory
        
        if trade_side == 'buy':
            self.inventory += amount
        else:
            self.inventory -= amount
        
        # Record inventory history
        self.history.append(self.inventory)
        
        # Calculate PnL if price is provided
        if price is not None and len(self.quote_history) > 0:
            last_quote = self.quote_history[-1]
            if trade_side == 'buy':
                # We bought at bid, so PnL is the difference between mid price and bid
                mid = (last_quote['bid'] + last_quote['ask']) / 2
                pnl = (mid - price) * amount
            else:
                # We sold at ask, so PnL is the difference between ask and mid price
                mid = (last_quote['bid'] + last_quote['ask']) / 2
                pnl = (price - mid) * amount
            
            self.pnl_history.append(pnl)
            self.total_pnl += pnl
    
    def remember(self, state, action, reward, next_state, done):
        """
        Store experience in replay memory.
        
        Args:
            state (np.array): Current state
            action (int): Action taken
            reward (float): Reward received
            next_state (np.array): Next state
            done (bool): Whether episode is done
        """
        self.memory.append((state, action, reward, next_state, done))
    
    def replay(self):
        """
        Train model using experience replay.
        """
        if len(self.memory) < self.batch_size:
            return
        
        # Sample batch from memory
        minibatch = random.sample(self.memory, self.batch_size)
        
        if self.use_deep_rl:
            # Deep RL training
            states = torch.FloatTensor(np.array([experience[0] for experience in minibatch]))
            actions = torch.LongTensor(np.array([experience[1] for experience in minibatch]))
            rewards = torch.FloatTensor(np.array([experience[2] for experience in minibatch]))
            next_states = torch.FloatTensor(np.array([experience[3] for experience in minibatch]))
            dones = torch.FloatTensor(np.array([experience[4] for experience in minibatch]))
            
            # Current Q values
            current_q = self.model(states).gather(1, actions.unsqueeze(1)).squeeze(1)
            
            # Target Q values
            with torch.no_grad():
                next_q = self.target_model(next_states).max(1)[0]
            target_q = rewards + (1 - dones) * self.gamma * next_q
            
            # Compute loss and optimize
            loss = self.criterion(current_q, target_q)
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
        else:
            # Simple Q-learning update
            for state, action, reward, next_state, done in minibatch:
                discrete_state = self._get_discrete_state(state)
                discrete_next_state = self._get_discrete_state(next_state)
                
                if discrete_state not in self.q_table:
                    self.q_table[discrete_state] = np.zeros(self.action_dim)
                if discrete_next_state not in self.q_table:
                    self.q_table[discrete_next_state] = np.zeros(self.action_dim)
                
                if done:
                    target = reward
                else:
                    target = reward + self.gamma * np.max(self.q_table[discrete_next_state])
                
                self.q_table[discrete_state][action] += self.learning_rate * (target - self.q_table[discrete_state][action])
        
        # Update target network if needed
        self.step_count += 1
        if self.step_count % self.update_target_every == 0 and self.use_deep_rl:
            self.target_model.load_state_dict(self.model.state_dict())
    
    def simulate_market_making(self, order_books, symbol, actions=None, market_features=None):
        """
        Simulate a sequence of market making actions offline.
        
        Args:
            order_books (list): List of order book snapshots
            symbol (str): Trading symbol
            actions (list): Optional list of actions (if None, model decides)
            market_features (list): Optional list of additional market features
            
        Returns:
            list: Simulation results
        """
        results = []
        states = []
        action_indices = []
        rewards = []
        next_states = []
        dones = []
        
        for i, ob in enumerate(order_books):
            # Get current state
            state = self._get_state(ob, symbol, market_features[i] if market_features else None)
            states.append(state)
            
            # Generate quote
            quote = self.quote(ob, symbol, market_features[i] if market_features else None)
            
            # Use provided action or generate one
            if actions and i < len(actions):
                act = actions[i]
                trade_side = act.get('side', None)
                amount = act.get('amount', 0)
                price = act.get('price', None)
            else:
                # Simple simulation: alternate buy/sell with random amounts
                trade_side = 'buy' if random.random() < 0.5 else 'sell'
                amount = random.uniform(100, 1000)
                price = quote['bid'] if trade_side == 'sell' else quote['ask']
            
            # Update inventory
            old_inventory = self.inventory
            self.update_inventory(trade_side, amount, price)
            
            # Calculate reward
            # Reward components:
            # 1. Spread capture (positive)
            # 2. Inventory risk penalty (negative)
            # 3. PnL from last trade (if available)
            spread_reward = quote['spread'] * amount * 0.5  # Half the spread as reward
            inventory_penalty = -abs(self.inventory) / self.inventory_limit * 0.01 * amount
            pnl_reward = self.pnl_history[-1] if len(self.pnl_history) > 0 else 0
            
            reward = spread_reward + inventory_penalty + pnl_reward
            rewards.append(reward)
            
            # Get next state
            next_state = state  # Default to current state
            if i < len(order_books) - 1:
                next_state = self._get_state(
                    order_books[i+1], 
                    symbol, 
                    market_features[i+1] if market_features else None
                )
            next_states.append(next_state)
            
            # Done flag (True for last step)
            done = (i == len(order_books) - 1)
            dones.append(done)
            
            # Store experience for RL training
            action_idx = int((quote['spread'] - self.spread_range[0]) / 
                           (self.spread_range[1] - self.spread_range[0]) * (self.action_dim - 1))
            action_idx = max(0, min(self.action_dim - 1, action_idx))
            action_indices.append(action_idx)
            
            # Store result
            results.append({
                'quote': quote, 
                'inventory': self.inventory,
                'trade': {'side': trade_side, 'amount': amount, 'price': price},
                'pnl': self.pnl_history[-1] if len(self.pnl_history) > 0 else 0,
                'total_pnl': self.total_pnl
            })
        
        # Store experiences in replay memory
        for i in range(len(states)):
            self.remember(states[i], action_indices[i], rewards[i], next_states[i], dones[i])
        
        # Train model
        self.replay()
        
        return results
    
    def save_model(self, path):
        """
        Save the model to disk.
        
        Args:
            path (str): Path to save the model
        """
        if self.use_deep_rl:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'target_model_state_dict': self.target_model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'epsilon': self.epsilon,
                'step_count': self.step_count
            }, path)
        else:
            import pickle
            with open(path, 'wb') as f:
                pickle.dump(self.q_table, f)
        
        logger.info(f"Model saved to {path}")
    
    def load_model(self, path):
        """
        Load the model from disk.
        
        Args:
            path (str): Path to load the model from
        """
        try:
            if self.use_deep_rl:
                checkpoint = torch.load(path)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                self.target_model.load_state_dict(checkpoint['target_model_state_dict'])
                self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                self.epsilon = checkpoint['epsilon']
                self.step_count = checkpoint['step_count']
            else:
                import pickle
                with open(path, 'rb') as f:
                    self.q_table = pickle.load(f)
            
            logger.info(f"Model loaded from {path}")
            return True
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False
    
    def get_inventory_stats(self):
        """
        Get statistics about inventory history.
        
        Returns:
            dict: Inventory statistics
        """
        if not self.history:
            return {'mean': 0, 'max': 0, 'min': 0, 'current': 0}
        
        return {
            'mean': np.mean(self.history),
            'max': np.max(self.history),
            'min': np.min(self.history),
            'current': self.inventory,
            'std': np.std(self.history)
        }
    
    def get_pnl_stats(self):
        """
        Get statistics about PnL history.
        
        Returns:
            dict: PnL statistics
        """
        if not self.pnl_history:
            return {'total': 0, 'mean': 0, 'positive_ratio': 0}
        
        positive_trades = sum(1 for pnl in self.pnl_history if pnl > 0)
        
        return {
            'total': self.total_pnl,
            'mean': np.mean(self.pnl_history),
            'positive_ratio': positive_trades / len(self.pnl_history),
            'sharpe': np.mean(self.pnl_history) / (np.std(self.pnl_history) + 1e-10)
        }
    
    def reset(self):
        """
        Reset the market maker state.
        """
        self.inventory = 0
        self.history = []
        self.quote_history = []
        self.pnl_history = []
        self.total_pnl = 0
    
    def explain_decision(self, order_book, symbol, market_features=None):
        """
        Provide detailed explanation for market making decisions.
        
        Args:
            order_book (dict): Order book data
            symbol (str): Trading symbol
            market_features (dict): Additional market features
            
        Returns:
            dict: Explanation of the market making decision
        """
        if symbol not in order_book:
            return {
                "decision": "no_quote",
                "reason": "Symbol not found in order book",
                "details": {},
                "recommendation": "Wait for valid order book data"
            }
            
        state = self._get_state(order_book, symbol, market_features)
        spread = self.decide_spread(order_book, symbol, market_features)
        mid = (order_book[symbol]['bid'] + order_book[symbol]['ask']) / 2
        
        # Inventory skew calculation
        inventory_skew = self.inventory / self.inventory_limit if self.inventory_limit > 0 else 0
        skew_factor = self.risk_aversion * inventory_skew * spread / 2
        
        bid = mid - spread / 2 - skew_factor
        ask = mid + spread / 2 - skew_factor
        
        # Extract important features for explanation
        volatility = state[5] if len(state) > 5 else 0
        volume = state[6] if len(state) > 6 else 0
        
        # Determine primary factors for the decision
        factors = []
        
        # Check inventory imbalance
        if abs(inventory_skew) > 0.3:
            factors.append({
                "factor": "inventory_imbalance",
                "importance": 0.8,
                "description": f"{'Excessive long' if inventory_skew > 0 else 'Excessive short'} inventory position ({inventory_skew:.2f})"
            })
            
        # Check volatility
        if volatility > 0.02:
            factors.append({
                "factor": "high_volatility",
                "importance": 0.7,
                "description": f"High market volatility ({volatility:.4f})"
            })
        elif volatility < 0.005:
            factors.append({
                "factor": "low_volatility",
                "importance": 0.5,
                "description": f"Low market volatility ({volatility:.4f})"
            })
            
        # Check volume
        if volume > 0.7:
            factors.append({
                "factor": "high_volume",
                "importance": 0.6,
                "description": f"High trading volume ({volume:.2f})"
            })
        elif volume < 0.3:
            factors.append({
                "factor": "low_volume",
                "importance": 0.4,
                "description": f"Low trading volume ({volume:.2f})"
            })
            
        # If no specific factors were identified, add a general market condition factor
        if not factors:
            factors.append({
                "factor": "normal_market",
                "importance": 0.5,
                "description": "Normal market conditions"
            })
            
        # Sort factors by importance
        factors.sort(key=lambda x: x["importance"], reverse=True)
        
        # Generate overall explanation
        if inventory_skew > 0.5:
            strategy = "Conservative ask-side quoting to reduce inventory"
        elif inventory_skew < -0.5:
            strategy = "Conservative bid-side quoting to increase inventory"
        elif volatility > 0.03:
            strategy = "Wide spread due to high volatility"
        elif volume > 0.8:
            strategy = "Tighter spread to capture high volume"
        else:
            strategy = "Balanced quoting with standard spread"
            
        # Calculate expected P&L
        expected_edge = spread / 2
        expected_fill_rate = max(0.1, min(0.9, 0.5 - volatility + volume))
        expected_pnl = expected_edge * expected_fill_rate * (1 - abs(inventory_skew))
        
        # Create explanation object
        explanation = {
            "decision": "quote",
            "bid": bid,
            "ask": ask,
            "spread": spread,
            "mid_price": mid,
            "inventory_position": self.inventory,
            "inventory_skew": inventory_skew,
            "skew_factor": skew_factor,
            "primary_factors": factors,
            "strategy": strategy,
            "expected": {
                "fill_rate": expected_fill_rate,
                "edge": expected_edge,
                "pnl": expected_pnl
            },
            "recommendation": "Continue with current strategy" if abs(inventory_skew) < 0.7 else 
                             "Consider reducing inventory risk"
        }
        
        return explanation 