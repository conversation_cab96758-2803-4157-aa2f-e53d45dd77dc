{"analysis_date": "2025-07-08T06:35:21.933569", "project_path": ".", "summary": {"total_python_files": 199, "files_analyzed": 196, "total_lines_of_code": 49581, "total_classes": 285, "total_functions": 1739, "total_database_files": 21}, "file_analysis": {".\\bakap.py": {"file": ".\\bakap.py", "lines": 123, "classes": 0, "functions": 2, "imports": 4, "class_names": [], "function_names": ["copy_project_files", "analyze_project"], "import_names": ["re", "shutil", "os", "logging"]}, ".\\clean_cache.py": {"file": ".\\clean_cache.py", "lines": 21, "classes": 0, "functions": 1, "imports": 3, "class_names": [], "function_names": ["clean_pycache"], "import_names": ["shutil", "os", "sys"]}, ".\\debug_fitness_issue.py": {"file": ".\\debug_fitness_issue.py", "lines": 124, "classes": 0, "functions": 1, "imports": 4, "class_names": [], "function_names": ["debug_fitness_calculation"], "import_names": ["utils.genetic_strategy_evolution", "datetime", "json", "sys"]}, ".\\final_system_analysis.py": {"file": ".\\final_system_analysis.py", "lines": 262, "classes": 0, "functions": 4, "imports": 4, "class_names": [], "function_names": ["analyze_test_logs", "generate_system_readiness_report", "generate_recommendations", "main"], "import_names": ["pathlib", "os", "json", "datetime"]}, ".\\fix_cursor_proxy.py": {"file": ".\\fix_cursor_proxy.py", "lines": 239, "classes": 0, "functions": 9, "imports": 9, "class_names": [], "function_names": ["set_system_proxy", "disable_system_proxy", "set_environment_variables", "load_proxy_config", "test_internet_connection", "restart_cursor", "flush_dns", "reset_winsock", "main"], "import_names": ["subprocess", "time", "winreg", "ctypes", "json", "requests", "pathlib", "os", "sys"]}, ".\\llma.py": {"file": ".\\llma.py", "lines": 9, "classes": 0, "functions": 0, "imports": 1, "class_names": [], "function_names": [], "import_names": ["llama_cpp"]}, ".\\main.py": {"file": ".\\main.py", "error": "unexpected indent (<unknown>, line 83)"}, ".\\ph3_optimizer.py": {"file": ".\\ph3_optimizer.py", "lines": 564, "classes": 2, "functions": 17, "imports": 13, "class_names": ["Model<PERSON><PERSON>er", "PH3Optimizer"], "function_names": ["__init__", "calculate_max_tasks", "_create_env", "_setup_checkpoint_paths", "_load_checkpoint", "_resume_or_create_model", "_train_model", "_evaluate_model", "_save_checkpoint", "_calculate_metrics", "train_and_evaluate", "__init__", "train_task", "evaluate_combination", "run_self_improving_system", "load_data", "make_env"], "import_names": ["pickle", "torch", "models.rl_models", "time", "os", "env.trading_env", "stable_baselines3.common.vec_env", "gc", "sklearn.model_selection", "multiprocessing", "stable_baselines3", "numpy", "pandas"]}, ".\\project_analyzer.py": {"file": ".\\project_analyzer.py", "lines": 285, "classes": 1, "functions": 8, "imports": 6, "class_names": ["ProjectAnalyzer"], "function_names": ["main", "__init__", "analyze_file", "find_python_files", "analyze_database_files", "analyze_project_structure", "run_complete_analysis", "generate_detailed_report"], "import_names": ["ast", "json", "typing", "os", "sqlite3", "datetime"]}, ".\\run_comprehensive_tests.py": {"file": ".\\run_comprehensive_tests.py", "lines": 206, "classes": 0, "functions": 2, "imports": 6, "class_names": [], "function_names": ["run_command_and_log", "main"], "import_names": ["subprocess", "time", "json", "pathlib", "os", "datetime"]}, ".\\run_sentiment_tests.py": {"file": ".\\run_sentiment_tests.py", "lines": 105, "classes": 0, "functions": 1, "imports": 6, "class_names": [], "function_names": ["run_test"], "import_names": ["logging", "time", "subprocess", "os", "sys", "datetime"]}, ".\\run_tests.py": {"file": ".\\run_tests.py", "lines": 38, "classes": 0, "functions": 1, "imports": 3, "class_names": [], "function_names": ["run_tests"], "import_names": ["subprocess", "os", "sys"]}, ".\\simple_system_test.py": {"file": ".\\simple_system_test.py", "lines": 287, "classes": 0, "functions": 4, "imports": 11, "class_names": [], "function_names": ["test_individual_systems", "test_system_integration", "generate_performance_report", "main"], "import_names": ["utils.advanced_rl_agent", "time", "utils.market_regime_detector", "json", "utils.intelligent_memory_system", "utils.multi_step_prediction_fixed", "utils.federated_learning_system", "os", "sys", "datetime", "numpy"]}, ".\\test_fetcher.py": {"file": ".\\test_fetcher.py", "lines": 4, "classes": 0, "functions": 0, "imports": 1, "class_names": [], "function_names": [], "import_names": ["cvxpy"]}, ".\\test_final_integration.py": {"file": ".\\test_final_integration.py", "lines": 656, "classes": 1, "functions": 8, "imports": 17, "class_names": ["FinalIntegrationTest"], "function_names": ["main", "__init__", "test_individual_systems", "test_system_integration", "test_performance_stress", "generate_final_report", "run_complete_test", "objective_function"], "import_names": ["logging", "utils.advanced_rl_agent", "time", "utils.market_regime_detector", "json", "utils.federated_learning_system", "utils.multi_step_prediction_fixed", "utils.intelligent_memory_system", "utils.genetic_strategy_evolution", "sqlite3", "os", "sys", "random", "datetime", "utils.anomaly_detection_system", "numpy", "pandas"]}, ".\\test_genetic_algorithm_deep.py": {"file": ".\\test_genetic_algorithm_deep.py", "lines": 319, "classes": 0, "functions": 4, "imports": 7, "class_names": [], "function_names": ["test_genetic_algorithm_fitness", "test_fitness_components", "main", "convert_numpy_types"], "import_names": ["json", "utils.genetic_strategy_evolution", "os", "sys", "datetime", "numpy", "pandas"]}, ".\\test_hierarchical_simple.py": {"file": ".\\test_hierarchical_simple.py", "lines": 43, "classes": 0, "functions": 1, "imports": 3, "class_names": [], "function_names": ["test_hierarchical_rl"], "import_names": ["torch", "models.hierarchical_rl", "numpy"]}, ".\\test_ray.py": {"file": ".\\test_ray.py", "lines": 10, "classes": 0, "functions": 0, "imports": 3, "class_names": [], "function_names": [], "import_names": ["torch", "<PERSON><PERSON><PERSON>", "torchvision"]}, ".\\api\\endpoints.py": {"file": ".\\api\\endpoints.py", "lines": 9, "classes": 0, "functions": 1, "imports": 1, "class_names": [], "function_names": ["health_check"], "import_names": ["<PERSON><PERSON><PERSON>"]}, ".\\api\\realtime_dashboard.py": {"file": ".\\api\\realtime_dashboard.py", "lines": 1007, "classes": 8, "functions": 17, "imports": 23, "class_names": ["Alert<PERSON>everity", "DashboardTheme", "<PERSON><PERSON>", "DashboardConfig", "ConnectionManager", "DataAggregator", "ReportGenerator", "RealTimeDashboard"], "function_names": ["__init__", "disconnect", "get_connected_clients", "__init__", "_simulate_portfolio_value", "_simulate_market_data", "_get_hft_metrics", "_get_routing_status", "_calculate_volatility", "_calculate_sharpe_ratio", "add_alert", "__init__", "__init__", "setup_middleware", "setup_routes", "setup_background_tasks", "get_dashboard_html"], "import_names": ["logging", "utils.auto_drawdown_control", "fastapi.responses", "u<PERSON><PERSON>", "utils.hft_modeling", "typing", "utils.sentiment_analyzer", "dataclasses", "<PERSON><PERSON><PERSON>", "datetime", "utils.multi_exchange_routing", "utils.reward_redistribution", "json", "enum", "asyncio", "numpy", "pandas", "fastapi.staticfiles", "uuid", "utils.alpha_beta_attribution", "os", "sys", "fastapi.middleware.cors"]}, ".\\api\\server.py": {"file": ".\\api\\server.py", "lines": 11, "classes": 0, "functions": 0, "imports": 3, "class_names": [], "function_names": [], "import_names": ["u<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "endpoints"]}, ".\\api\\__init__.py": {"file": ".\\api\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\data\\fetcher.py": {"file": ".\\data\\fetcher.py", "lines": 39, "classes": 1, "functions": 4, "imports": 2, "class_names": ["MT5DataFetcher"], "function_names": ["read_large_csv", "__init__", "fetch_data", "__del__"], "import_names": ["os", "pandas"]}, ".\\data\\import sys.py": {"file": ".\\data\\import sys.py", "lines": 13, "classes": 0, "functions": 0, "imports": 3, "class_names": [], "function_names": [], "import_names": ["os", "data.fetcher", "sys"]}, ".\\data\\preprocessor.py": {"file": ".\\data\\preprocessor.py", "lines": 77, "classes": 1, "functions": 2, "imports": 6, "class_names": ["DataPreprocessor"], "function_names": ["__init__", "preprocess"], "import_names": ["ta.momentum", "ta", "ta.volatility", "ta.trend", "numpy", "pandas"]}, ".\\data\\test_fetcher.py": {"file": ".\\data\\test_fetcher.py", "lines": 13, "classes": 0, "functions": 0, "imports": 3, "class_names": [], "function_names": [], "import_names": ["os", "data.fetcher", "sys"]}, ".\\data\\__init__.py": {"file": ".\\data\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\evaluation\\comparison.py": {"file": ".\\evaluation\\comparison.py", "lines": 13, "classes": 0, "functions": 1, "imports": 1, "class_names": [], "function_names": ["compare_models"], "import_names": ["topsis"]}, ".\\evaluation\\metrics.py": {"file": ".\\evaluation\\metrics.py", "lines": 71, "classes": 0, "functions": 1, "imports": 1, "class_names": [], "function_names": ["calculate_metrics"], "import_names": ["numpy"]}, ".\\evaluation\\scenario_backtesting.py": {"file": ".\\evaluation\\scenario_backtesting.py", "lines": 714, "classes": 1, "functions": 19, "imports": 11, "class_names": ["ScenarioBacktesting"], "function_names": ["__init__", "_create_vec_env", "historical_crisis_scenario", "parameter_sensitivity_analysis", "_create_market_scenario", "execution_delay_slippage_scenario", "regime_switching_scenario", "_run_backtest", "compare_scenarios", "visualize_scenario_results", "monte_carlo_stress_scenarios", "multi_market_backtest", "news_event_impact_scenario", "worst_case_robust_optimization", "liquidity_shock_scenario", "multi_agent_behavior_backtest", "market_manipulation_scenario", "whale_behavior_scenario", "run_scenario_with_xai"], "import_names": ["copy", "models.rl_models", "evaluation.metrics", "gym", "evaluation.explainable_ai", "typing", "matplotlib.pyplot", "portfolio.portfolio_manager", "stable_baselines3.common.vec_env", "numpy", "pandas"]}, ".\\evaluation\\tests.py": {"file": ".\\evaluation\\tests.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\evaluation\\__init__.py": {"file": ".\\evaluation\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\examples\\adaptive_plutus_demo.py": {"file": ".\\examples\\adaptive_plutus_demo.py", "lines": 306, "classes": 0, "functions": 5, "imports": 9, "class_names": [], "function_names": ["demo_adaptive_learning", "demo_weight_optimization", "demo_backtest_with_learning", "demo_real_time_adaptation", "main"], "import_names": ["logging", "time", "json", "traceback", "pathlib", "os", "sys", "utils.adaptive_plutus_system", "datetime"]}, ".\\examples\\adaptive_plutus_live_demo.py": {"file": ".\\examples\\adaptive_plutus_live_demo.py", "lines": 232, "classes": 0, "functions": 3, "imports": 9, "class_names": [], "function_names": ["live_adaptive_demo", "quick_performance_test", "main"], "import_names": ["logging", "time", "json", "traceback", "pathlib", "os", "sys", "utils.adaptive_plutus_system", "datetime"]}, ".\\examples\\advanced_regime_test_with_logging.py": {"file": ".\\examples\\advanced_regime_test_with_logging.py", "lines": 589, "classes": 1, "functions": 11, "imports": 13, "class_names": ["AdvancedRegimeTestSystem"], "function_names": ["setup_logging", "main", "__init__", "phase_1_data_loading", "phase_2_regime_detection_setup", "phase_3_signal_generation_test", "phase_4_trading_simulation", "_calculate_max_drawdown", "phase_5_learning_analysis", "generate_comprehensive_report", "_generate_summary"], "import_names": ["logging", "time", "utils.market_regime_detector", "json", "utils.enhanced_adaptive_plutus", "traceback", "pathlib", "tests.test_plutus_models_comprehensive", "os", "sys", "datetime", "numpy", "pandas"]}, ".\\examples\\alpha_beta_attribution_example.py": {"file": ".\\examples\\alpha_beta_attribution_example.py", "lines": 445, "classes": 0, "functions": 9, "imports": 8, "class_names": [], "function_names": ["generate_sample_data", "demonstrate_basic_alpha_beta", "demonstrate_realtime_updates", "demonstrate_multi_factor_analysis", "demonstrate_alpha_decomposition", "demonstrate_alpha_prediction", "demonstrate_comprehensive_report", "create_visualization", "main"], "import_names": ["utils.alpha_beta_attribution", "sklearn.linear_model", "matplotlib.pyplot", "os", "sys", "datetime", "numpy", "pandas"]}, ".\\examples\\continual_learning_example.py": {"file": ".\\examples\\continual_learning_example.py", "lines": 349, "classes": 0, "functions": 6, "imports": 12, "class_names": [], "function_names": ["create_task_environments", "create_data_loader", "train_model", "evaluate_model", "main", "plot_results"], "import_names": ["logging", "utils.data_utils", "torch", "models.rl_models", "matplotlib.pyplot", "os", "env.trading_env", "sys", "datetime", "models.continual_learning", "numpy", "pandas"]}, ".\\examples\\download_models.py": {"file": ".\\examples\\download_models.py", "lines": 60, "classes": 0, "functions": 1, "imports": 5, "class_names": [], "function_names": ["download_models"], "import_names": ["utils.proxy_setup", "torch", "transformers", "os", "sys"]}, ".\\examples\\ensemble_example.py": {"file": ".\\examples\\ensemble_example.py", "lines": 271, "classes": 0, "functions": 5, "imports": 9, "class_names": [], "function_names": ["create_test_env", "train_individual_models", "test_ensemble", "compare_with_individual_models", "main"], "import_names": ["models.ensemble_model", "models.rl_models", "matplotlib.pyplot", "os", "env.trading_env", "utils.config_override", "sys", "datetime", "numpy"]}, ".\\examples\\fix_encoding.py": {"file": ".\\examples\\fix_encoding.py", "lines": 64, "classes": 0, "functions": 0, "imports": 1, "class_names": [], "function_names": [], "import_names": ["os"]}, ".\\examples\\hft_modeling_example.py": {"file": ".\\examples\\hft_modeling_example.py", "lines": 301, "classes": 0, "functions": 7, "imports": 4, "class_names": [], "function_names": ["generate_sample_market_data", "demonstrate_order_book_analysis", "demonstrate_latency_optimization", "demonstrate_short_term_prediction", "demonstrate_hft_strategy", "main", "sample_calculation"], "import_names": ["time", "logging", "utils.hft_modeling", "numpy"]}, ".\\examples\\hierarchical_rl_example.py": {"file": ".\\examples\\hierarchical_rl_example.py", "lines": 370, "classes": 0, "functions": 7, "imports": 11, "class_names": [], "function_names": ["create_hierarchical_env", "generate_hierarchical_observation", "train_hierarchical_model", "evaluate_hierarchical_model", "plot_hierarchical_results", "plot_strategy_analysis", "main"], "import_names": ["logging", "utils.data_utils", "matplotlib.pyplot", "os", "models.hierarchical_rl", "env.trading_env", "sys", "random", "datetime", "numpy", "pandas"]}, ".\\examples\\multi_exchange_routing_example.py": {"file": ".\\examples\\multi_exchange_routing_example.py", "lines": 420, "classes": 0, "functions": 1, "imports": 6, "class_names": [], "function_names": ["create_sample_exchanges"], "import_names": ["utils.multi_exchange_routing", "logging", "time", "typing", "asyncio", "numpy"]}, ".\\examples\\plutus_api_example.py": {"file": ".\\examples\\plutus_api_example.py", "lines": 449, "classes": 2, "functions": 10, "imports": 8, "class_names": ["FinancialModelAPI", "FinancialAnalysisIntegration"], "function_names": ["main_example", "__init__", "use_huggingface_financial_sentiment", "use_fingpt_forecaster", "use_alpha_vantage_api", "use_yahoo_finance_api", "__init__", "comprehensive_analysis", "calculate_technical_indicators", "generate_combined_recommendation"], "import_names": ["logging", "time", "json", "requests", "typing", "datetime", "numpy", "pandas"]}, ".\\examples\\plutus_integration_final.py": {"file": ".\\examples\\plutus_integration_final.py", "lines": 399, "classes": 1, "functions": 8, "imports": 10, "class_names": ["PlutusIntegratedTradingSystem"], "function_names": ["main", "__init__", "get_real_time_signal", "generate_combined_signal", "get_market_context", "generate_recommendation", "run_live_analysis", "save_analysis_results"], "import_names": ["logging", "json", "utils.plutus_integration", "pathlib", "tests.test_plutus_models_comprehensive", "os", "sys", "datetime", "numpy", "pandas"]}, ".\\examples\\practical_regime_trading.py": {"file": ".\\examples\\practical_regime_trading.py", "lines": 463, "classes": 1, "functions": 10, "imports": 12, "class_names": ["RegimeBasedTradingSystem"], "function_names": ["main", "__init__", "initialize_system", "analyze_trading_opportunity", "calculate_position_size", "calculate_risk_levels", "assess_risk_level", "execute_trade_simulation", "analyze_portfolio_performance", "run_trading_simulation"], "import_names": ["logging", "time", "utils.market_regime_detector", "json", "utils.enhanced_adaptive_plutus", "pathlib", "tests.test_plutus_models_comprehensive", "os", "sys", "datetime", "numpy", "pandas"]}, ".\\examples\\realtime_dashboard_example.py": {"file": ".\\examples\\realtime_dashboard_example.py", "lines": 525, "classes": 1, "functions": 10, "imports": 11, "class_names": ["DashboardDemo"], "function_names": ["main", "__init__", "start_server", "demonstrate_api_endpoints", "demonstrate_alert_system", "demonstrate_data_flow", "demonstrate_reports", "demonstrate_dashboard_features", "run_browser_demo", "run_server"], "import_names": ["api.realtime_dashboard", "time", "websockets", "u<PERSON><PERSON>", "requests", "json", "os", "threading", "sys", "datetime", "asyncio"]}, ".\\examples\\reward_redistribution_example.py": {"file": ".\\examples\\reward_redistribution_example.py", "lines": 349, "classes": 0, "functions": 4, "imports": 3, "class_names": [], "function_names": ["simulate_trading_session", "create_visualizations", "test_different_strategies", "optimize_hyperparameters"], "import_names": ["matplotlib.pyplot", "utils.reward_redistribution", "numpy"]}, ".\\examples\\run_with_proxy.py": {"file": ".\\examples\\run_with_proxy.py", "lines": 178, "classes": 0, "functions": 2, "imports": 12, "class_names": [], "function_names": ["setup_proxy", "main"], "import_names": ["urllib3", "utils.source_credibility", "utils.sentiment_analyzer", "subprocess", "time", "json", "requests", "traceback", "utils.news_volume_analyzer", "os", "sys", "utils.sentiment_integrator"]}, ".\\examples\\sentiment_integration_example.py": {"file": ".\\examples\\sentiment_integration_example.py", "lines": 48, "classes": 0, "functions": 0, "imports": 11, "class_names": [], "function_names": [], "import_names": ["utils.source_credibility", "utils.sentiment_analyzer", "utils.sentiment_integrator", "utils.news_volume_analyzer", "typing", "os", "matplotlib.pyplot", "sys", "datetime", "numpy", "pandas"]}, ".\\examples\\simple_example.py": {"file": ".\\examples\\simple_example.py", "error": "EOL while scanning string literal (<unknown>, line 4)"}, ".\\examples\\simple_hyperparameter_test.py": {"file": ".\\examples\\simple_hyperparameter_test.py", "lines": 182, "classes": 1, "functions": 6, "imports": 5, "class_names": ["SimpleHyperparameterTuner"], "function_names": ["evaluate_rl_params", "evaluate_prediction_params", "main", "__init__", "optimize_rl_parameters", "optimize_prediction_parameters"], "import_names": ["json", "typing", "os", "sys", "random"]}, ".\\examples\\simple_regime_demo.py": {"file": ".\\examples\\simple_regime_demo.py", "error": "unexpected indent (<unknown>, line 64)"}, ".\\examples\\test_rl_system.py": {"file": ".\\examples\\test_rl_system.py", "lines": 92, "classes": 0, "functions": 1, "imports": 4, "class_names": [], "function_names": ["main"], "import_names": ["os", "numpy", "utils.advanced_rl_agent", "sys"]}, ".\\examples\\zero_shot_learning_example.py": {"file": ".\\examples\\zero_shot_learning_example.py", "lines": 388, "classes": 0, "functions": 6, "imports": 11, "class_names": [], "function_names": ["prepare_markets_data", "create_environments", "train_base_models", "evaluate_model", "main", "plot_results"], "import_names": ["logging", "utils.data_utils", "models.rl_models", "models.zero_shot_learning", "matplotlib.pyplot", "os", "env.trading_env", "sys", "datetime", "numpy", "pandas"]}, ".\\models\\continual_learning.py": {"file": ".\\models\\continual_learning.py", "lines": 654, "classes": 3, "functions": 25, "imports": 13, "class_names": ["EWC<PERSON><PERSON>er", "<PERSON>layBuffer", "ContinualLearning"], "function_names": ["__init__", "compute_fisher_information", "ewc_loss", "update_model_loss", "__init__", "add", "sample", "_uniform_sampling", "_importance_sampling", "update_priorities", "__len__", "__init__", "prepare_for_new_task", "set_data_loader", "add_experience", "distillation_loss", "update_model_loss", "train_with_replay", "evaluate_forgetting", "plot_forgetting", "plot_performance", "save", "load", "new_loss_fn", "new_loss_fn"], "import_names": ["copy", "logging", "torch", "rl_models", "json", "collections", "typing", "os", "matplotlib.pyplot", "torch.nn", "datetime", "numpy", "torch.optim"]}, ".\\models\\ensemble_model.py": {"file": ".\\models\\ensemble_model.py", "lines": 572, "classes": 1, "functions": 11, "imports": 10, "class_names": ["EnsembleModel"], "function_names": ["__init__", "_load_models", "predict", "_combine_actions", "_update_weights", "_calculate_diversity", "update_reward", "save", "load", "plot_weights_history", "plot_performance"], "import_names": ["logging", "torch", "rl_models", "json", "typing", "os", "matplotlib.pyplot", "datetime", "numpy", "pandas"]}, ".\\models\\hierarchical_rl.py": {"file": ".\\models\\hierarchical_rl.py", "lines": 557, "classes": 4, "functions": 25, "imports": 12, "class_names": ["StrategicPolicy", "TacticalPolicy", "ExecutionPolicy", "HierarchicalRL"], "function_names": ["__init__", "forward", "get_strategy_probs", "__init__", "forward", "get_action_probs", "__init__", "forward", "__init__", "get_strategic_observation", "get_tactical_observation", "get_execution_observation", "select_strategy", "select_tactical_action", "select_execution_action", "act", "compute_gae", "update_strategic_policy", "update_tactical_policy", "update_execution_policy", "train_step", "get_strategy_analysis", "save", "load", "get_current_state"], "import_names": ["logging", "stable_baselines3.common.policies", "torch", "json", "typing", "os", "stable_baselines3.common.vec_env", "torch.nn", "datetime", "stable_baselines3", "numpy", "torch.optim"]}, ".\\models\\meta_learner.py": {"file": ".\\models\\meta_learner.py", "lines": 432, "classes": 1, "functions": 9, "imports": 9, "class_names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "function_names": ["__init__", "_load_history", "_save_history", "extract_market_features", "record_performance", "select_best_model", "_find_similar_markets", "optimize_hyperparameters", "get_model_insights"], "import_names": ["pickle", "torch", "json", "typing", "os", "sklearn.model_selection", "datetime", "numpy", "pandas"]}, ".\\models\\rl_models.py": {"file": ".\\models\\rl_models.py", "lines": 347, "classes": 1, "functions": 18, "imports": 7, "class_names": ["RLModelFactory"], "function_names": ["resume_training", "auto_checkpoint", "__init__", "save_checkpoint", "load_checkpoint", "_create_ddpg", "_create_qrdqn", "_create_tqc", "_create_maskable_ppo", "_create_bc", "_create_sac", "_create_td3", "_create_ppo_lstm", "create_model", "_create_ppo", "_create_a2c", "_create_dqn", "file_hash"], "import_names": ["sb3_contrib", "torch", "json", "os", "<PERSON><PERSON><PERSON>", "stable_baselines3", "sb3_contrib.common.wrappers"]}, ".\\models\\zero_shot_learning.py": {"file": ".\\models\\zero_shot_learning.py", "lines": 815, "classes": 4, "functions": 23, "imports": 13, "class_names": ["MarketEmbedding", "MarketPolicyAdapter", "ZeroShotLearning", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "function_names": ["__init__", "forward", "__init__", "forward", "__init__", "_default_feature_extractor", "compute_market_embedding", "compute_market_similarity", "find_similar_markets", "adapt_to_new_market", "_apply_policy_parameters", "_adapt_with_prototypes", "_apply_weighted_prototype", "_extract_few_shot_samples", "_fine_tune_on_samples", "create_market_prototype", "_extract_prototype_from_model", "record_performance", "plot_market_similarity", "plot_performance_transfer", "save", "load", "predict"], "import_names": ["logging", "torch", "rl_models", "json", "collections", "typing", "os", "matplotlib.pyplot", "continual_learning", "torch.nn", "datetime", "numpy", "torch.optim"]}, ".\\models\\__init__.py": {"file": ".\\models\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\optimization\\bayesian.py": {"file": ".\\optimization\\bayesian.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\optimization\\genetic.py": {"file": ".\\optimization\\genetic.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\optimization\\pso.py": {"file": ".\\optimization\\pso.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\optimization\\__init__.py": {"file": ".\\optimization\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\portfolio\\portfolio_manager.py": {"file": ".\\portfolio\\portfolio_manager.py", "lines": 102, "classes": 1, "functions": 8, "imports": 1, "class_names": ["PortfolioManager"], "function_names": ["__init__", "open_position", "close_position", "get_trade_history", "get_total_pnl", "get_trade_report", "get_balance", "get_positions"], "import_names": ["typing"]}, ".\\portfolio\\__init__.py": {"file": ".\\portfolio\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\bakap.py": {"file": ".\\project_backup\\bakap.py", "lines": 96, "classes": 0, "functions": 2, "imports": 4, "class_names": [], "function_names": ["copy_project_files", "analyze_project"], "import_names": ["re", "shutil", "os", "logging"]}, ".\\project_backup\\main.py": {"file": ".\\project_backup\\main.py", "lines": 96, "classes": 0, "functions": 1, "imports": 6, "class_names": [], "function_names": ["main"], "import_names": ["logging", "ph3_optimizer", "os", "env.trading_env", "data.preprocessor", "pandas"]}, ".\\project_backup\\ph3_optimizer.py": {"file": ".\\project_backup\\ph3_optimizer.py", "lines": 362, "classes": 2, "functions": 9, "imports": 13, "class_names": ["Model<PERSON><PERSON>er", "PH3Optimizer"], "function_names": ["__init__", "calculate_max_tasks", "train_and_evaluate", "__init__", "train_task", "evaluate_combination", "run_self_improving_system", "load_data", "make_env"], "import_names": ["pickle", "torch", "models.rl_models", "time", "os", "env.trading_env", "stable_baselines3.common.vec_env", "gc", "sklearn.model_selection", "multiprocessing", "stable_baselines3", "numpy", "pandas"]}, ".\\project_backup\\test_fetcher.py": {"file": ".\\project_backup\\test_fetcher.py", "lines": 2, "classes": 0, "functions": 0, "imports": 1, "class_names": [], "function_names": [], "import_names": ["cvxpy"]}, ".\\project_backup\\test_ray.py": {"file": ".\\project_backup\\test_ray.py", "lines": 8, "classes": 0, "functions": 0, "imports": 3, "class_names": [], "function_names": [], "import_names": ["torch", "<PERSON><PERSON><PERSON>", "torchvision"]}, ".\\project_backup\\api\\endpoints.py": {"file": ".\\project_backup\\api\\endpoints.py", "lines": 7, "classes": 0, "functions": 1, "imports": 1, "class_names": [], "function_names": ["health_check"], "import_names": ["<PERSON><PERSON><PERSON>"]}, ".\\project_backup\\api\\server.py": {"file": ".\\project_backup\\api\\server.py", "lines": 9, "classes": 0, "functions": 0, "imports": 3, "class_names": [], "function_names": [], "import_names": ["u<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "endpoints"]}, ".\\project_backup\\api\\__init__.py": {"file": ".\\project_backup\\api\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\data\\fetcher.py": {"file": ".\\project_backup\\data\\fetcher.py", "lines": 24, "classes": 1, "functions": 3, "imports": 2, "class_names": ["MT5DataFetcher"], "function_names": ["__init__", "fetch_data", "__del__"], "import_names": ["os", "pandas"]}, ".\\project_backup\\data\\import sys.py": {"file": ".\\project_backup\\data\\import sys.py", "lines": 9, "classes": 0, "functions": 0, "imports": 3, "class_names": [], "function_names": [], "import_names": ["os", "data.fetcher", "sys"]}, ".\\project_backup\\data\\preprocessor.py": {"file": ".\\project_backup\\data\\preprocessor.py", "lines": 59, "classes": 1, "functions": 2, "imports": 6, "class_names": ["DataPreprocessor"], "function_names": ["__init__", "preprocess"], "import_names": ["ta.momentum", "ta", "ta.volatility", "ta.trend", "numpy", "pandas"]}, ".\\project_backup\\data\\test_fetcher.py": {"file": ".\\project_backup\\data\\test_fetcher.py", "lines": 11, "classes": 0, "functions": 0, "imports": 3, "class_names": [], "function_names": [], "import_names": ["os", "data.fetcher", "sys"]}, ".\\project_backup\\data\\__init__.py": {"file": ".\\project_backup\\data\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\evaluation\\comparison.py": {"file": ".\\project_backup\\evaluation\\comparison.py", "lines": 11, "classes": 0, "functions": 1, "imports": 1, "class_names": [], "function_names": ["compare_models"], "import_names": ["topsis"]}, ".\\project_backup\\evaluation\\metrics.py": {"file": ".\\project_backup\\evaluation\\metrics.py", "lines": 64, "classes": 0, "functions": 1, "imports": 1, "class_names": [], "function_names": ["calculate_metrics"], "import_names": ["numpy"]}, ".\\project_backup\\evaluation\\tests.py": {"file": ".\\project_backup\\evaluation\\tests.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\evaluation\\__init__.py": {"file": ".\\project_backup\\evaluation\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\models\\meta_learner.py": {"file": ".\\project_backup\\models\\meta_learner.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\models\\rl_models.py": {"file": ".\\project_backup\\models\\rl_models.py", "lines": 58, "classes": 1, "functions": 5, "imports": 1, "class_names": ["RLModelFactory"], "function_names": ["__init__", "create_model", "_create_ppo", "_create_a2c", "_create_dqn"], "import_names": ["stable_baselines3"]}, ".\\project_backup\\models\\__init__.py": {"file": ".\\project_backup\\models\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\optimization\\bayesian.py": {"file": ".\\project_backup\\optimization\\bayesian.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\optimization\\genetic.py": {"file": ".\\project_backup\\optimization\\genetic.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\optimization\\pso.py": {"file": ".\\project_backup\\optimization\\pso.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\optimization\\__init__.py": {"file": ".\\project_backup\\optimization\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\portfolio\\portfolio_manager.py": {"file": ".\\project_backup\\portfolio\\portfolio_manager.py", "lines": 66, "classes": 1, "functions": 5, "imports": 0, "class_names": ["PortfolioManager"], "function_names": ["__init__", "open_position", "close_position", "get_balance", "get_positions"], "import_names": []}, ".\\project_backup\\portfolio\\__init__.py": {"file": ".\\project_backup\\portfolio\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\tests\\test_env.py": {"file": ".\\project_backup\\tests\\test_env.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\tests\\test_fetcher.py": {"file": ".\\project_backup\\tests\\test_fetcher.py", "lines": 12, "classes": 0, "functions": 2, "imports": 3, "class_names": [], "function_names": ["fetcher", "test_fetch_data"], "import_names": ["pytest", "data.fetcher", "pandas"]}, ".\\project_backup\\tests\\test_indicators.py": {"file": ".\\project_backup\\tests\\test_indicators.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\tests\\test_models.py": {"file": ".\\project_backup\\tests\\test_models.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\project_backup\\utils\\config.py": {"file": ".\\project_backup\\utils\\config.py", "lines": 46, "classes": 1, "functions": 1, "imports": 0, "class_names": ["Config"], "function_names": ["__init__"], "import_names": []}, ".\\project_backup\\utils\\logger.py": {"file": ".\\project_backup\\utils\\logger.py", "lines": 32, "classes": 1, "functions": 4, "imports": 3, "class_names": ["<PERSON><PERSON>"], "function_names": ["__init__", "log_message", "log_metrics", "_save_logs"], "import_names": ["os", "json", "datetime"]}, ".\\project_backup\\utils\\security.py": {"file": ".\\project_backup\\utils\\security.py", "lines": 9, "classes": 1, "functions": 3, "imports": 0, "class_names": ["SecureStorage"], "function_names": ["__init__", "encrypt", "decrypt"], "import_names": []}, ".\\project_backup\\utils\\test_fetcher.py": {"file": ".\\project_backup\\utils\\test_fetcher.py", "lines": 17, "classes": 0, "functions": 2, "imports": 3, "class_names": [], "function_names": ["fetcher", "test_fetch_data"], "import_names": ["pytest", "data.fetcher", "pandas"]}, ".\\project_backup\\utils\\__init__.py": {"file": ".\\project_backup\\utils\\__init__.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\tests\\conftest.py": {"file": ".\\tests\\conftest.py", "lines": 77, "classes": 0, "functions": 10, "imports": 7, "class_names": [], "function_names": ["rl_factory", "gym_env", "dummy_vec_env", "test_data", "test_rewards", "test_checkpoint_path", "reset_env_safely", "predict_safely", "_reset", "_predict"], "import_names": ["models.rl_models", "gym", "os", "pytest", "stable_baselines3.common.vec_env", "sys", "numpy"]}, ".\\tests\\test_adaptive_margin_control.py": {"file": ".\\tests\\test_adaptive_margin_control.py", "lines": 299, "classes": 1, "functions": 12, "imports": 9, "class_names": ["TestAdaptiveMarginControl"], "function_names": ["setUp", "test_initialization", "test_price_history_update", "test_position_update", "test_volatility_calculation", "test_position_risk_calculation", "test_margin_requirement_calculation", "test_margin_history", "test_margin_stats", "test_config_save_load", "test_ml_model", "test_explain_margin_decision"], "import_names": ["utils.adaptive_margin_control", "unittest", "torch", "json", "os", "sys", "datetime", "numpy", "pandas"]}, ".\\tests\\test_adaptive_plutus_simple.py": {"file": ".\\tests\\test_adaptive_plutus_simple.py", "lines": 312, "classes": 0, "functions": 5, "imports": 7, "class_names": [], "function_names": ["test_database_operations", "test_weight_optimization", "test_signal_generation", "test_performance_analysis", "main"], "import_names": ["json", "traceback", "pathlib", "os", "sys", "utils.adaptive_plutus_system", "datetime"]}, ".\\tests\\test_alpha_beta_attribution.py": {"file": ".\\tests\\test_alpha_beta_attribution.py", "lines": 456, "classes": 5, "functions": 20, "imports": 8, "class_names": ["TestKalmanFilterAlphaBeta", "TestMultiFactorModel", "TestAlphaMLPredictor", "TestAlphaBetaAttributionEngine", "TestIntegration"], "function_names": ["test_initialization", "test_reset", "test_first_update", "test_multiple_updates", "test_initialization", "test_custom_factors", "test_fit_model", "test_predict_alpha", "test_initialization", "test_prepare_features", "test_rsi_calculation", "test_train_and_predict", "test_initialization", "test_calculate_alpha_beta", "test_realtime_alpha_beta", "test_decompose_alpha_sources", "test_generate_attribution_report", "test_get_realtime_metrics", "test_full_attribution_workflow", "test_real_time_updates"], "import_names": ["utils.alpha_beta_attribution", "unittest.mock", "os", "pytest", "sys", "datetime", "numpy", "pandas"]}, ".\\tests\\test_auto_market_maker.py": {"file": ".\\tests\\test_auto_market_maker.py", "lines": 241, "classes": 1, "functions": 10, "imports": 7, "class_names": ["TestAutoMarketMaker"], "function_names": ["setUp", "test_initialization", "test_quote_generation", "test_inventory_update", "test_market_making_simulation", "test_state_extraction", "test_reset", "test_deep_rl_model", "test_explanation", "test_explain_decision"], "import_names": ["unittest", "torch", "utils.auto_market_maker", "os", "sys", "numpy", "pandas"]}, ".\\tests\\test_cleanup.py": {"file": ".\\tests\\test_cleanup.py", "lines": 17, "classes": 0, "functions": 1, "imports": 3, "class_names": [], "function_names": ["test_cleanup_old_models"], "import_names": ["tempfile", "os", "utils.cleanup"]}, ".\\tests\\test_complete_advanced_system.py": {"file": ".\\tests\\test_complete_advanced_system.py", "lines": 775, "classes": 1, "functions": 8, "imports": 17, "class_names": ["CompleteAdvancedTradingSystem"], "function_names": ["main", "__init__", "load_market_data", "initialize_all_systems", "run_complete_analysis", "run_federated_learning_round", "run_genetic_evolution", "get_system_status"], "import_names": ["logging", "utils.advanced_rl_agent", "time", "utils.market_regime_detector", "json", "utils.federated_learning_system", "utils.multi_step_prediction_fixed", "utils.intelligent_memory_system", "utils.genetic_strategy_evolution", "typing", "os", "sys", "random", "datetime", "utils.anomaly_detection_system", "numpy", "pandas"]}, ".\\tests\\test_config.py": {"file": ".\\tests\\test_config.py", "lines": 26, "classes": 0, "functions": 2, "imports": 2, "class_names": [], "function_names": ["test_default_config", "test_config_yaml"], "import_names": ["pytest", "utils.config"]}, ".\\tests\\test_config_override.py": {"file": ".\\tests\\test_config_override.py", "lines": 31, "classes": 0, "functions": 3, "imports": 5, "class_names": [], "function_names": ["test_override_config_with_env", "test_override_config_with_cli", "test_load_config_with_override"], "import_names": ["tempfile", "yaml", "os", "utils.config_override", "sys"]}, ".\\tests\\test_config_validation.py": {"file": ".\\tests\\test_config_validation.py", "lines": 10, "classes": 0, "functions": 1, "imports": 2, "class_names": [], "function_names": ["test_config_validation"], "import_names": ["pytest", "utils.config"]}, ".\\tests\\test_config_validation_module.py": {"file": ".\\tests\\test_config_validation_module.py", "lines": 37, "classes": 0, "functions": 4, "imports": 2, "class_names": [], "function_names": ["test_validate_config_success", "test_validate_config_missing_key", "test_validate_config_invalid_lot_size", "test_validate_config_invalid_type"], "import_names": ["utils.config_validation", "pytest"]}, ".\\tests\\test_data_cleaning.py": {"file": ".\\tests\\test_data_cleaning.py", "lines": 64, "classes": 0, "functions": 10, "imports": 4, "class_names": [], "function_names": ["test_drop_duplicates", "test_fix_inconsistent_data", "test_remove_time_anomalies", "test_remove_sudden_spikes", "test_impute_missing_statistical", "test_remove_zero_abnormal_volume", "test_normalize_categorical", "test_filter_market_hours", "test_filter_unrealistic_financial_ratios", "test_remove_unrealistic_price_change"], "import_names": ["utils", "pytest", "numpy", "pandas"]}, ".\\tests\\test_data_cleaning_pipeline.py": {"file": ".\\tests\\test_data_cleaning_pipeline.py", "lines": 39, "classes": 0, "functions": 1, "imports": 4, "class_names": [], "function_names": ["test_advanced_data_cleaning_on_eurusd_h1"], "import_names": ["os", "utils.data_cleaning_pipeline", "pandas", "sys"]}, ".\\tests\\test_data_cleaning_pipeline_btcusd.py": {"file": ".\\tests\\test_data_cleaning_pipeline_btcusd.py", "lines": 42, "classes": 0, "functions": 1, "imports": 4, "class_names": [], "function_names": ["test_advanced_data_cleaning_on_btcusd_m15"], "import_names": ["os", "utils.data_cleaning_pipeline", "pandas", "sys"]}, ".\\tests\\test_data_cleaning_pipeline_btcusd_full.py": {"file": ".\\tests\\test_data_cleaning_pipeline_btcusd_full.py", "lines": 41, "classes": 0, "functions": 1, "imports": 4, "class_names": [], "function_names": ["test_advanced_data_cleaning_on_btcusd_m15_full"], "import_names": ["os", "utils.data_cleaning_pipeline", "pandas", "sys"]}, ".\\tests\\test_data_pipeline.py": {"file": ".\\tests\\test_data_pipeline.py", "lines": 23, "classes": 0, "functions": 2, "imports": 3, "class_names": [], "function_names": ["test_normalize_features", "test_pipeline_run"], "import_names": ["utils.data_pipeline", "numpy", "pandas"]}, ".\\tests\\test_data_structure.py": {"file": ".\\tests\\test_data_structure.py", "lines": 7, "classes": 0, "functions": 1, "imports": 2, "class_names": [], "function_names": ["test_data_folders_exist"], "import_names": ["os", "pytest"]}, ".\\tests\\test_docstring.py": {"file": ".\\tests\\test_docstring.py", "lines": 7, "classes": 0, "functions": 1, "imports": 2, "class_names": [], "function_names": ["test_logger_docstring"], "import_names": ["inspect", "utils.logger"]}, ".\\tests\\test_env.py": {"file": ".\\tests\\test_env.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\tests\\test_error_handling.py": {"file": ".\\tests\\test_error_handling.py", "lines": 97, "classes": 0, "functions": 8, "imports": 5, "class_names": [], "function_names": ["mock_analyzer", "test_language_detection_error", "test_none_input", "test_empty_string", "test_empty_list", "test_list_with_error", "test_get_sentiment_score_error_handling", "mock_detect_language"], "import_names": ["utils.source_credibility", "utils.sentiment_analyzer", "os", "pytest", "sys"]}, ".\\tests\\test_explainable_ai.py": {"file": ".\\tests\\test_explainable_ai.py", "lines": 453, "classes": 2, "functions": 35, "imports": 11, "class_names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "TestExplainableAI"], "function_names": ["__init__", "predict", "explainable_ai", "sample_observations", "sample_actions", "sample_returns", "sample_market_data", "sample_time_data", "test_feature_importance", "test_decision_explanation", "test_visualize_feature_importance", "test_visualize_decision_process", "test_compare_decisions", "test_generate_explanation_report", "test_analyze_market_regime", "test_analyze_decision_risk", "test_identify_critical_decision_points", "test_create_decision_heatmap", "test_counterfactual_analysis", "test_sensitivity_analysis", "test_detect_bias", "test_advanced_pnl_attribution", "test_stress_scenario_analysis", "test_decision_memory_analysis", "test_behavioral_anomaly_detection", "test_generate_nlg_explanation", "test_visualize_decision_flow", "test_interactive_decision_space", "test_expert_comparison", "test_news_impact_analysis", "test_market_impact_analysis", "test_decision_lag_analysis", "test_explanation_quality_assessment", "test_auto_feature_engineering_basic", "test_auto_feature_engineering_active_learning"], "import_names": ["json", "sklearn.linear_model", "unittest.mock", "os", "matplotlib.pyplot", "pytest", "sys", "utils.explainable_ai", "utils.auto_feature_engineering", "numpy", "pandas"]}, ".\\tests\\test_export.py": {"file": ".\\tests\\test_export.py", "lines": 15, "classes": 0, "functions": 1, "imports": 2, "class_names": [], "function_names": ["test_export_results"], "import_names": ["tempfile", "json"]}, ".\\tests\\test_export_module.py": {"file": ".\\tests\\test_export_module.py", "lines": 39, "classes": 0, "functions": 3, "imports": 4, "class_names": [], "function_names": ["test_export_to_csv", "test_export_to_json", "test_export_to_excel"], "import_names": ["utils.export", "os", "json", "pandas"]}, ".\\tests\\test_fetcher.py": {"file": ".\\tests\\test_fetcher.py", "lines": 20, "classes": 0, "functions": 2, "imports": 3, "class_names": [], "function_names": ["fetcher", "test_fetch_data"], "import_names": ["pytest", "data.fetcher", "pandas"]}, ".\\tests\\test_first_batch.py": {"file": ".\\tests\\test_first_batch.py", "lines": 329, "classes": 4, "functions": 17, "imports": 10, "class_names": ["TestRewardRedistribution", "TestAlphaBetaAttribution", "TestAutoDrawdownControl", "TestRealtimeDashboard"], "function_names": ["test_basic_redistribution", "test_drawdown_redistribution", "test_module_function", "test_calculate_alpha_beta", "test_rolling_alpha_beta", "test_decompose_returns", "test_kalman_filter", "test_update_equity", "test_position_multiplier", "test_adaptive_position_size", "test_predict_drawdown_risk", "test_recovery_strategy", "test_dashboard_server_init", "test_update_metrics", "test_send_alert", "test_get_chart_data", "test_connection_manager"], "import_names": ["api.realtime_dashboard", "utils.auto_drawdown_control", "utils.reward_redistribution", "utils.alpha_beta_attribution", "os", "pytest", "sys", "datetime", "asyncio", "numpy"]}, ".\\tests\\test_hft_modeling.py": {"file": ".\\tests\\test_hft_modeling.py", "lines": 391, "classes": 6, "functions": 28, "imports": 5, "class_names": ["TestOrderBookAnalyzer", "TestLatencyOptimizer", "TestShortTermPredictor", "TestHFTStrategy", "TestHFTModelingSystem", "TestIntegration"], "function_names": ["setUp", "test_initialization", "test_update_order_book", "test_calculate_price_impact", "test_get_market_microstructure", "setUp", "test_measure_execution_time", "test_get_latency_stats", "test_optimize_data_structures", "setUp", "test_initialization", "test_extract_features", "test_train_model", "test_predict_direction", "setUp", "test_initialization", "test_should_enter_position", "test_execute_trade", "test_get_performance_stats", "setUp", "test_initialization", "test_start_stop", "test_process_market_data", "test_get_system_stats", "test_full_hft_workflow", "test_latency_measurement", "dummy_function", "test_func"], "import_names": ["unittest", "time", "utils.hft_modeling", "unittest.mock", "numpy"]}, ".\\tests\\test_hierarchical_rl.py": {"file": ".\\tests\\test_hierarchical_rl.py", "lines": 474, "classes": 4, "functions": 40, "imports": 9, "class_names": ["TestStrategicPolicy", "TestTacticalPolicy", "TestExecutionPolicy", "TestHierarchicalRL"], "function_names": ["test_strategic_policy_initialization", "test_strategic_policy_forward", "test_strategic_policy_probs", "test_strategic_policy_different_obs_dim", "test_tactical_policy_initialization", "test_tactical_policy_forward", "test_tactical_policy_probs", "test_tactical_policy_different_actions", "test_execution_policy_initialization", "test_execution_policy_forward", "test_execution_policy_different_action_dim", "hierarchical_rl", "test_hierarchical_rl_initialization", "test_strategic_observation_extraction", "test_tactical_observation_extraction", "test_execution_observation_extraction", "test_strategy_selection", "test_tactical_action_selection", "test_execution_action_selection", "test_complete_hierarchical_decision", "test_gae_computation", "test_strategy_usage_tracking", "test_model_saving_and_loading", "test_strategy_analysis", "test_current_state_tracking", "test_error_handling_invalid_obs_dim", "test_error_handling_invalid_device", "test_training_step_with_empty_batch", "test_training_step_with_valid_batch", "test_different_obs_dimensions", "test_different_learning_rates", "test_different_gamma_values", "test_strategy_names", "test_training_history_initialization", "test_optimizer_initialization", "test_policy_device_assignment", "test_continuous_execution_actions", "test_discrete_strategy_actions", "test_discrete_tactical_actions", "test_hierarchical_decision_consistency"], "import_names": ["tempfile", "torch", "unittest.mock", "os", "pytest", "models.hierarchical_rl", "sys", "numpy", "pandas"]}, ".\\tests\\test_indicators.py": {"file": ".\\tests\\test_indicators.py", "lines": 1, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}, ".\\tests\\test_logger.py": {"file": ".\\tests\\test_logger.py", "lines": 15, "classes": 0, "functions": 2, "imports": 2, "class_names": [], "function_names": ["test_logger_info_level", "test_logger_error_level"], "import_names": ["logging", "utils.logger"]}, ".\\tests\\test_models.py": {"file": ".\\tests\\test_models.py", "lines": 380, "classes": 5, "functions": 43, "imports": 3, "class_names": ["Du<PERSON><PERSON><PERSON><PERSON>", "Du<PERSON><PERSON><PERSON><PERSON>", "Dummy", "Du<PERSON><PERSON><PERSON><PERSON>", "Du<PERSON><PERSON><PERSON><PERSON>"], "function_names": ["test_adaptive_trade_executor_learns_slippage", "test_adaptive_trade_executor_action_selection", "test_adaptive_trade_executor_execute_and_online_update", "test_deep_rl_execution_model_learns_order_type", "test_order_book_depth_and_feature_extraction", "test_high_frequency_execution", "test_advanced_order_types_execution", "test_market_impact_simulation", "test_multi_agent_learning", "test_liquidity_shock_detection", "test_adapt_to_abnormal_conditions", "test_order_book_snapshot", "test_execute_dual_mode", "test_real_slippage_modeling", "test_parameter_sensitivity", "test_explain_execution", "test_transfer_learning_q_table", "test_block_trade_splitting", "test_online_update_q_table", "test_optimize_commission", "test_safe_execute_live_handles_error", "test_sentiment_analyzer_basic", "test_sentiment_aware_execute", "test_meta_learner_selection", "test_auto_ensemble_voting", "test_continual_learner_memory_and_rehearsal", "test_advanced_sentiment_analyzer", "test_meta_learner_transfer_and_explain", "test_auto_ensemble_weighted", "test_continual_learner_regularization", "test_auto_market_maker", "__init__", "predict", "__init__", "learn_from_experience", "get_weights", "set_weights", "__init__", "predict", "__init__", "get_weights", "set_weights", "learn_from_experience"], "import_names": ["torch", "utils.rl_training_utils", "pytest"]}, ".\\tests\\test_multi_exchange_routing.py": {"file": ".\\tests\\test_multi_exchange_routing.py", "lines": 407, "classes": 7, "functions": 24, "imports": 5, "class_names": ["TestExchangeConnector", "TestArbitrageEngine", "TestExecutionCostOptimizer", "TestLiquidityManager", "TestFailoverSystem", "TestMultiExchangeRouter", "TestAsyncIntegration"], "function_names": ["setUp", "test_initialization", "setUp", "test_initialization", "test_find_arbitrage_opportunities", "test_rank_opportunities", "setUp", "test_calculate_total_cost", "test_estimate_slippage", "test_select_optimal_exchange", "setUp", "test_split_large_order", "test_calculate_market_impact", "setUp", "test_add_backup_exchanges", "test_get_healthy_exchanges", "test_get_failover_exchange", "setUp", "test_initialization", "test_get_statistics", "setUp", "tearDown", "test_full_arbitrage_workflow", "test_order_execution_workflow"], "import_names": ["unittest", "utils.multi_exchange_routing", "time", "unittest.mock", "asyncio"]}, ".\\tests\\test_news_volume_analyzer.py": {"file": ".\\tests\\test_news_volume_analyzer.py", "lines": 184, "classes": 0, "functions": 11, "imports": 3, "class_names": [], "function_names": ["analyzer", "test_initialization", "test_initialization_invalid_windows", "test_add_news", "test_clean_old_news", "test_get_volume", "test_no_spike_with_insufficient_news", "test_no_spike_with_normal_distribution", "test_spike_detection", "test_get_all_assets_with_spikes", "test_volume_cache"], "import_names": ["datetime", "pytest", "utils.news_volume_analyzer"]}, ".\\tests\\test_plutus_models_comprehensive.py": {"file": ".\\tests\\test_plutus_models_comprehensive.py", "lines": 674, "classes": 1, "functions": 10, "imports": 15, "class_names": ["PlutusModelTester"], "function_names": ["test_plutus_models_comprehensive", "test_model_backtesting", "__init__", "load_real_project_data", "test_chronos_model_offline", "test_fingpt_model_offline", "compare_models_performance", "backtest_predictions", "run_comprehensive_test", "generate_detailed_report"], "import_names": ["logging", "torch", "time", "examples.plutus_api_example", "requests", "json", "utils.plutus_integration", "pathlib", "typing", "os", "pytest", "sys", "datetime", "numpy", "pandas"]}, ".\\tests\\test_plutus_real_trading_scenario.py": {"file": ".\\tests\\test_plutus_real_trading_scenario.py", "lines": 536, "classes": 1, "functions": 8, "imports": 9, "class_names": ["RealTradingScenarioTester"], "function_names": ["main", "__init__", "simulate_trading_period", "execute_trade", "combine_predictions", "calculate_performance_metrics", "run_comprehensive_trading_test", "generate_trading_report"], "import_names": ["logging", "test_plutus_models_comprehensive", "json", "pathlib", "os", "sys", "datetime", "numpy", "pandas"]}, ".\\tests\\test_portfolio_manager.py": {"file": ".\\tests\\test_portfolio_manager.py", "lines": 335, "classes": 0, "functions": 35, "imports": 6, "class_names": [], "function_names": ["test_open_and_close_long_position", "test_open_and_close_short_position", "test_close_nonexistent_position", "test_multiple_positions_and_balances", "test_reopen_closed_symbol", "test_get_positions_and_balance", "test_floating_point_precision", "test_massive_batch_operations", "test_randomized_stress", "test_nan_inf_handling", "test_symbol_case_sensitivity", "test_extreme_quantity_and_price", "test_reopen_with_different_type_and_quantity", "test_balance_never_negative", "test_open_same_symbol_twice_should_override", "test_close_all_positions_in_loop", "test_balance_precision_after_many_operations", "test_zero_quantity_and_zero_price", "test_large_number_of_symbols_and_cleanup", "test_unicode_and_special_symbol_names", "test_extreme_symbol_length", "test_open_close_with_minimal_and_maximal_float", "test_open_close_with_nan_symbol", "test_open_close_with_empty_string_symbol", "test_balance_type_consistency", "test_multi_account_transfer", "test_risk_limit_enforcement", "test_margin_call_liquidation", "test_state_save_and_load", "test_cumulative_pnl", "test_real_data_backtest", "test_commission_and_slippage", "test_invalid_data_handling", "test_performance_small", "test_equity_curve_generation"], "import_names": ["pickle", "math", "pytest", "portfolio.portfolio_manager", "sys", "random"]}, ".\\tests\\test_portfolio_reporting.py": {"file": ".\\tests\\test_portfolio_reporting.py", "lines": 24, "classes": 0, "functions": 2, "imports": 1, "class_names": [], "function_names": ["test_trade_history_and_total_pnl", "test_trade_report_format"], "import_names": ["portfolio.portfolio_manager"]}, ".\\tests\\test_portfolio_visualizer.py": {"file": ".\\tests\\test_portfolio_visualizer.py", "lines": 31, "classes": 0, "functions": 4, "imports": 3, "class_names": [], "function_names": ["sample_trades", "test_plot_pnl_curve", "test_plot_trade_distribution", "test_plot_drawdown"], "import_names": ["utils.portfolio_visualizer", "typing", "pytest"]}, ".\\tests\\test_realtime_dashboard.py": {"file": ".\\tests\\test_realtime_dashboard.py", "lines": 583, "classes": 7, "functions": 24, "imports": 10, "class_names": ["TestConnectionManager", "TestDataAggregator", "TestReportGenerator", "TestDashboardConfig", "TestRealTimeDashboard", "TestAPIEndpoints", "TestIntegration"], "function_names": ["test_initialization", "test_disconnect", "test_get_connected_clients", "test_initialization", "test_simulate_portfolio_value", "test_simulate_market_data", "test_get_hft_metrics", "test_get_routing_status", "test_calculate_volatility", "test_calculate_sharpe_ratio", "test_add_alert", "test_initialization", "test_default_config", "test_initialization", "test_get_dashboard_html", "setup_method", "test_health_endpoint", "test_config_endpoints", "test_alerts_endpoints", "test_reports_endpoints", "test_data_endpoint", "test_dashboard_html_endpoint", "test_dashboard_performance", "test_concurrent_connections"], "import_names": ["api.realtime_dashboard", "time", "json", "os", "fastapi.websockets", "pytest", "sys", "datetime", "asyncio", "fastapi.testclient"]}, ".\\tests\\test_resume_only.py": {"file": ".\\tests\\test_resume_only.py", "lines": 46, "classes": 0, "functions": 2, "imports": 5, "class_names": [], "function_names": ["test_ppo_cross_market_resume", "test_ppo_curriculum_resume"], "import_names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "models.rl_models", "gym", "os", "stable_baselines3.common.vec_env"]}, ".\\tests\\test_reward_redistribution.py": {"file": ".\\tests\\test_reward_redistribution.py", "lines": 256, "classes": 7, "functions": 19, "imports": 5, "class_names": ["TestRewardStrategies", "TestRewardMemorySystem", "TestRewardPredictor", "TestRewardQualityMonitor", "TestAdvancedRewardRedistributor", "TestBackwardCompatibility", "TestFactoryFunction"], "function_names": ["test_linear_strategy", "test_exponential_strategy", "test_adaptive_strategy", "test_add_record", "test_memory_size_limit", "test_weighted_average_performance", "test_drawdown_statistics", "test_model_training", "test_insufficient_data", "test_quality_calculation", "test_quality_trend", "redistributor", "test_single_redistribution", "test_batch_redistribution", "test_performance_summary", "test_simple_redistributor", "test_module_function", "test_create_advanced_redistributor", "test_invalid_strategy"], "import_names": ["utils.reward_redistribution", "unittest.mock", "typing", "pytest", "numpy"]}, ".\\tests\\test_risk_manager.py": {"file": ".\\tests\\test_risk_manager.py", "lines": 16, "classes": 0, "functions": 1, "imports": 1, "class_names": [], "function_names": ["test_risk_manager_limits"], "import_names": ["utils.risk_manager"]}, ".\\tests\\test_rl_models.py": {"file": ".\\tests\\test_rl_models.py", "lines": 1296, "classes": 1, "functions": 32, "imports": 18, "class_names": ["Du<PERSON><PERSON><PERSON><PERSON>"], "function_names": ["test_ppo_cross_market_resume", "test_ppo_curriculum_resume", "test_set12_anomaly_reward_imitation", "test_set11_explainable_auto_scenario", "test_set10_federated_news_scenario", "test_set9_anomaly_selfplay_whale", "test_set8_scenario_auto_multimarket", "test_set7_adversarial_lookahead_comparative", "test_set6_overlapping_lookahead_featureimportance", "test_set5_uncertainty_augmentation_stability", "test_set4_heatmap_rolling_drawdown", "test_set3_scenario_robust_regularization", "fake_sharpe_curve", "nested_cv", "test_set2_nestedcv_combo_earlystopping", "test_set1_earlystopping_and_cv", "test_stress_and_adversarial_models", "make_env", "make_env_continuous", "test_factory_all_models", "test_ppo_training_step", "test_ppo_checkpoint_resume", "test_ppo_auto_checkpoint", "test_sac_training_step", "test_lstm_ppo_training_step", "test_ppo_resume_training", "test_ppo_resume_network_change", "test_ppo_resume_optimizer_change", "test_ppo_resume_reward_change", "scenario_func", "new_reward_fn", "score"], "import_names": ["seaborn", "scipy.stats", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "models.rl_models", "random", "torch", "json", "gym", "os", "pytest", "matplotlib.pyplot", "stable_baselines3.common.vec_env", "sys", "pandas", "utils.rl_training_utils", "warnings", "numpy", "gymnasium"]}, ".\\tests\\test_scenario_backtesting.py": {"file": ".\\tests\\test_scenario_backtesting.py", "lines": 434, "classes": 2, "functions": 21, "imports": 8, "class_names": ["MockEnv", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "function_names": ["setup_scenario_backtesting", "test_historical_crisis_scenario", "test_parameter_sensitivity_analysis", "test_execution_delay_slippage_scenario", "test_regime_switching_scenario", "test_compare_scenarios", "test_visualize_scenario_results", "test_create_market_scenario", "test_monte_carlo_stress_scenarios", "test_multi_market_backtest", "test_news_event_impact_scenario", "test_worst_case_robust_optimization", "test_liquidity_shock_scenario", "test_multi_agent_behavior_backtest", "test_market_manipulation_scenario", "test_whale_behavior_scenario", "__init__", "reset", "step", "__init__", "predict"], "import_names": ["gym", "evaluation.scenario_backtesting", "unittest.mock", "matplotlib.pyplot", "pytest", "gym.spaces", "numpy", "pandas"]}, ".\\tests\\test_sentiment_analyzer.py": {"file": ".\\tests\\test_sentiment_analyzer.py", "lines": 566, "classes": 12, "functions": 29, "imports": 6, "class_names": ["TestSentimentTypes", "TestSentimentResult", "TestLanguageModel", "TestAspectBasedSentimentAnalyzer", "TestTemporalSentimentAnalyzer", "TestSocialMediaSentimentAnalyzer", "TestMarketSentimentAggregator", "TestSentimentCache", "TestSentimentImpactPredictor", "TestEnsembleSentimentAnalyzer", "TestAdvancedSentimentAnalyzer", "TestIntegration"], "function_names": ["test_sentiment_types", "test_aspect_categories", "test_sentiment_result_creation", "test_language_model_init", "test_analyze", "test_extract_aspects", "test_extract_aspects_persian", "test_add_sentiment", "test_get_trend", "test_get_momentum", "test_get_volatility", "test_predict_next_sentiment", "test_emoji_sentiment", "test_platform_weights", "test_clean_social_text", "test_add_sentiment", "test_calculate_market_sentiment", "test_time_decay", "test_cache_operations", "test_cache_expiry", "test_cache_stats", "test_train", "test_predict_impact", "test_analyze_ensemble", "test_initialization", "test_detect_language", "test_backward_compatibility", "test_full_analysis_workflow", "test_batch_analysis"], "import_names": ["utils.sentiment_analyzer", "unittest.mock", "pytest", "datetime", "numpy", "pandas"]}, ".\\tests\\test_sentiment_integrator.py": {"file": ".\\tests\\test_sentiment_integrator.py", "lines": 259, "classes": 1, "functions": 10, "imports": 8, "class_names": ["TestSentimentIntegrator"], "function_names": ["setUp", "test_initialization", "test_weight_normalization", "test_set_weights", "test_process_news", "test_get_volume_shock_signal", "test_get_technical_signal", "test_get_fundamental_signal", "test_get_sentiment_signal", "test_get_integrated_signal"], "import_names": ["utils.source_credibility", "unittest", "utils.sentiment_analyzer", "utils.sentiment_integrator", "utils.news_volume_analyzer", "unittest.mock", "datetime", "numpy"]}, ".\\tests\\test_source_credibility.py": {"file": ".\\tests\\test_source_credibility.py", "lines": 158, "classes": 0, "functions": 11, "imports": 5, "class_names": [], "function_names": ["temp_storage_path", "credibility_manager", "test_initialization", "test_initialization_with_sources", "test_get_score", "test_set_score", "test_adjust_score", "test_get_top_sources", "test_get_bottom_sources", "test_apply_credibility_weight", "test_persistence"], "import_names": ["tempfile", "utils.source_credibility", "json", "os", "pytest"]}, ".\\tests\\test_typehint.py": {"file": ".\\tests\\test_typehint.py", "lines": 8, "classes": 0, "functions": 1, "imports": 2, "class_names": [], "function_names": ["test_logger_typehint"], "import_names": ["typing", "utils.logger"]}, ".\\tests\\test_zero_shot_learning.py": {"file": ".\\tests\\test_zero_shot_learning.py", "lines": 371, "classes": 3, "functions": 26, "imports": 7, "class_names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "function_names": ["sample_market_data", "zero_shot_model", "test_market_embedding_network", "test_policy_adapter_network", "test_zero_shot_initialization", "test_default_feature_extractor", "test_compute_market_embedding", "test_compute_market_similarity", "test_find_similar_markets", "test_adapt_to_new_market_zero_shot", "test_adapt_to_new_market_few_shot", "test_create_market_prototype", "test_record_performance", "test_plot_market_similarity", "test_plot_performance_transfer", "test_save_and_load", "test_error_handling", "test_embedding_network_training", "test_feature_extractor_edge_cases", "test_market_embedding_consistency", "test_similarity_matrix_storage", "__init__", "predict", "__init__", "predict", "__init__"], "import_names": ["torch", "models.zero_shot_learning", "os", "pytest", "sys", "numpy", "pandas"]}, ".\\utils\\adaptive_margin_control.py": {"file": ".\\utils\\adaptive_margin_control.py", "lines": 798, "classes": 2, "functions": 23, "imports": 10, "class_names": ["MarginRiskModel", "AdaptiveMarginControl"], "function_names": ["__init__", "forward", "__init__", "_init_ml_model", "update_price_history", "update_position", "calculate_volatility", "calculate_position_risk", "get_market_features", "_get_time_features", "_get_symbol_features", "_prepare_model_input", "calculate_margin_requirement", "store_experience", "train_model", "get_margin_history", "get_margin_stats", "explain_margin_decision", "_get_margin_trend", "save_config", "load_config", "save_model", "load_model"], "import_names": ["logging", "torch", "collections", "json", "os", "pandas", "torch.nn", "datetime", "numpy", "torch.optim"]}, ".\\utils\\adaptive_plutus_system.py": {"file": ".\\utils\\adaptive_plutus_system.py", "lines": 940, "classes": 6, "functions": 26, "imports": 17, "class_names": ["ModelPerformanceMetrics", "AdaptiveWeights", "PerformanceDatabase", "AdaptiveLearningEngine", "BacktestingEngine", "AdaptivePlutusSystem"], "function_names": ["main", "__init__", "init_database", "save_performance", "get_recent_performance", "save_adaptive_weights", "get_latest_weights", "__init__", "analyze_model_performance", "analyze_market_conditions_impact", "analyze_performance_trend", "optimize_model_weights", "__init__", "run_adaptive_backtest", "combine_predictions_with_weights", "simulate_trade", "save_backtest_performance", "calculate_backtest_metrics", "calculate_max_drawdown", "__init__", "start_continuous_learning", "stop_continuous_learning", "_continuous_learning_loop", "get_adaptive_signal", "run_comprehensive_optimization", "generate_learning_report"], "import_names": ["logging", "pickle", "time", "json", "dataclasses", "pathlib", "tests.test_plutus_models_comprehensive", "typing", "os", "sqlite3", "threading", "sys", "examples.plutus_integration_final", "datetime", "concurrent.futures", "numpy", "pandas"]}, ".\\utils\\advanced_reward_system.py": {"file": ".\\utils\\advanced_reward_system.py", "lines": 362, "classes": 4, "functions": 9, "imports": 6, "class_names": ["MarketRegime", "DrawdownMemory", "RewardAdjustment", "AdaptiveRewardSystem"], "function_names": ["__init__", "detect_market_regime", "calculate_dynamic_multiplier", "_calculate_recovery_progress", "learn_from_experience", "_update_adaptive_parameters", "get_multilevel_reward", "update_state", "get_system_status"], "import_names": ["logging", "collections", "dataclasses", "enum", "typing", "numpy"]}, ".\\utils\\advanced_rl_agent.py": {"file": ".\\utils\\advanced_rl_agent.py", "lines": 732, "classes": 7, "functions": 38, "imports": 13, "class_names": ["MarketState", "TradingAction", "Experience", "<PERSON>layBuffer", "AdvancedRLAgent", "QLearningAgent", "AdvancedRLTradingSystem"], "function_names": ["main", "to_vector", "to_index", "from_index", "__init__", "add", "sample", "size", "save", "load", "__init__", "q_table", "remember", "act", "train_episode", "get_action_probabilities", "save_model", "load_model", "replay", "get_confidence", "__init__", "_state_to_key", "get_q_value", "set_q_value", "choose_action", "update_q_value", "__init__", "_init_database", "create_market_state", "calculate_reward", "get_optimal_action", "add_experience", "_save_experience_to_db", "train_agent", "_save_performance_to_db", "get_agent_statistics", "save_system", "generate_trading_recommendation"], "import_names": ["logging", "pickle", "json", "collections", "dataclasses", "pathlib", "typing", "sqlite3", "os", "random", "datetime", "numpy", "pandas"]}, ".\\utils\\alpha_beta_attribution.py": {"file": ".\\utils\\alpha_beta_attribution.py", "lines": 647, "classes": 10, "functions": 24, "imports": 12, "class_names": ["RiskFactorType", "AttributionPeriod", "AlphaBetaResult", "FactorExposure", "AttributionResult", "KalmanFilterAlphaBeta", "MultiFactorModel", "AlphaMLPredictor", "AlphaBetaAttributionEngine", "AlphaBetaAnalyzer"], "function_names": ["__init__", "reset", "update", "__init__", "fit", "predict_alpha", "__init__", "prepare_features", "_calculate_rsi", "train", "predict", "__init__", "calculate_alpha_beta", "realtime_alpha_beta", "multi_factor_attribution", "decompose_alpha_sources", "_calculate_security_selection", "_calculate_timing_effect", "_calculate_sector_allocation", "_calculate_momentum_alpha", "_calculate_mean_reversion_alpha", "predict_future_alpha", "generate_attribution_report", "get_realtime_metrics"], "import_names": ["sklearn.preprocessing", "logging", "dataclasses", "sklearn.ensemble", "warnings", "typing", "enum", "sklearn.linear_model", "sklearn.metrics", "datetime", "numpy", "pandas"]}, ".\\utils\\anomaly_detection_system.py": {"file": ".\\utils\\anomaly_detection_system.py", "lines": 858, "classes": 6, "functions": 31, "imports": 12, "class_names": ["AnomalyEvent", "AdaptationRule", "MarketFeatureExtractor", "AnomalyDetector", "AdaptationEngine", "AnomalyDetectionSystem"], "function_names": ["main", "__init__", "extract_features", "_extract_price_features", "_extract_volume_features", "_extract_volatility_features", "_extract_correlation_features", "_extract_economic_features", "_calculate_skewness", "_calculate_kurtosis", "_calculate_max_drawdown", "_calculate_volatility_clustering", "_calculate_persistence", "_detect_volatility_jumps", "__init__", "fit", "detect_anomalies", "_classify_anomaly_type", "__init__", "_init_database", "_load_default_rules", "process_anomaly", "_find_applicable_rules", "_check_trigger_conditions", "_scale_actions_by_severity", "_save_anomaly_event", "_save_adaptation_history", "__init__", "initialize", "analyze_market_data", "get_system_status"], "import_names": ["sklearn.preprocessing", "logging", "json", "dataclasses", "sklearn.ensemble", "sklearn.cluster", "typing", "sqlite3", "warnings", "datetime", "numpy", "pandas"]}, ".\\utils\\auto_drawdown_control.py": {"file": ".\\utils\\auto_drawdown_control.py", "lines": 856, "classes": 12, "functions": 26, "imports": 9, "class_names": ["DrawdownSeverity", "DrawdownAction", "RecoveryStrategy", "DrawdownEvent", "DrawdownMetrics", "ControlParameters", "DrawdownDetector", "AdaptiveBrakeSystem", "DrawdownPredictor", "RecoveryManager", "AutoDrawdownController", "DrawdownController"], "function_names": ["__init__", "update", "_classify_severity", "get_current_metrics", "_calculate_recovery_factor", "_estimate_drawdown_probability", "_estimate_recovery_time", "__init__", "calculate_position_adjustment", "get_recommended_action", "__init__", "prepare_features", "_calculate_drawdown_duration", "_calculate_rsi", "_calculate_bollinger_position", "predict_drawdown_probability", "__init__", "start_recovery", "_select_recovery_strategy", "get_recovery_position_size", "check_recovery_completion", "__init__", "update", "_generate_alerts", "get_dashboard_data", "simulate_scenarios"], "import_names": ["logging", "abc", "dataclasses", "warnings", "typing", "enum", "datetime", "numpy", "pandas"]}, ".\\utils\\auto_feature_engineering.py": {"file": ".\\utils\\auto_feature_engineering.py", "lines": 72, "classes": 1, "functions": 7, "imports": 4, "class_names": ["AutoFeatureEngineering"], "function_names": ["__init__", "fit", "transform", "fit_transform", "_generate_features", "suggest_new_features", "select_samples_for_labeling"], "import_names": ["sklearn.base", "sklearn.feature_selection", "numpy", "pandas"]}, ".\\utils\\auto_hyperparameter_tuning.py": {"file": ".\\utils\\auto_hyperparameter_tuning.py", "lines": 613, "classes": 4, "functions": 24, "imports": 14, "class_names": ["HyperparameterSpace", "OptimizationResult", "BayesianOptimizer", "AutoHyperparameterTuner"], "function_names": ["main", "__init__", "_encode_parameters", "_decode_parameters", "_acquisition_function", "_norm_cdf", "_norm_pdf", "_suggest_next_point", "_generate_random_params", "optimize", "_check_convergence", "_calculate_improvement_rate", "__init__", "_init_database", "define_rl_hyperparameters", "define_prediction_hyperparameters", "optimize_rl_parameters", "optimize_prediction_parameters", "_save_optimization_result", "get_best_parameters", "get_optimization_summary", "evaluate_rl_params", "evaluate_prediction_params", "neg_acquisition"], "import_names": ["sklearn.preprocessing", "logging", "pickle", "sklearn.gaussian_process", "json", "dataclasses", "warnings", "typing", "sqlite3", "scipy.optimize", "sklearn.gaussian_process.kernels", "datetime", "numpy", "pandas"]}, ".\\utils\\auto_hyperparameter_tuning_fixed.py": {"file": ".\\utils\\auto_hyperparameter_tuning_fixed.py", "lines": 461, "classes": 4, "functions": 18, "imports": 9, "class_names": ["HyperparameterSpace", "OptimizationResult", "SimpleBayesianOptimizer", "AutoHyperparameterTuner"], "function_names": ["main", "__init__", "_generate_random_params", "_mutate_params", "optimize", "_check_convergence", "_calculate_improvement_rate", "__init__", "_init_database", "define_rl_hyperparameters", "define_prediction_hyperparameters", "optimize_rl_parameters", "optimize_prediction_parameters", "_save_optimization_result", "get_best_parameters", "get_optimization_summary", "evaluate_rl_params", "evaluate_prediction_params"], "import_names": ["logging", "json", "dataclasses", "typing", "sqlite3", "random", "datetime", "numpy", "pandas"]}, ".\\utils\\auto_market_maker.py": {"file": ".\\utils\\auto_market_maker.py", "lines": 652, "classes": 2, "functions": 18, "imports": 9, "class_names": ["DeepRLMarketMaker", "AutoMarketMaker"], "function_names": ["__init__", "forward", "__init__", "_get_state", "_get_discrete_state", "_get_action_spread", "decide_spread", "quote", "update_inventory", "remember", "replay", "simulate_market_making", "save_model", "load_model", "get_inventory_stats", "get_pnl_stats", "reset", "explain_decision"], "import_names": ["logging", "pickle", "torch", "collections", "pandas", "torch.nn", "random", "numpy", "torch.optim"]}, ".\\utils\\cleanup.py": {"file": ".\\utils\\cleanup.py", "lines": 24, "classes": 0, "functions": 1, "imports": 2, "class_names": [], "function_names": ["cleanup_old_models"], "import_names": ["glob", "os"]}, ".\\utils\\config.py": {"file": ".\\utils\\config.py", "lines": 28, "classes": 0, "functions": 1, "imports": 2, "class_names": [], "function_names": ["get_config"], "import_names": ["os", "yaml"]}, ".\\utils\\config_override.py": {"file": ".\\utils\\config_override.py", "lines": 36, "classes": 0, "functions": 3, "imports": 4, "class_names": [], "function_names": ["override_config_with_env", "override_config_with_cli", "load_config_with_override"], "import_names": ["typing", "os", "yaml", "<PERSON><PERSON><PERSON><PERSON>"]}, ".\\utils\\config_validation.py": {"file": ".\\utils\\config_validation.py", "lines": 17, "classes": 0, "functions": 1, "imports": 1, "class_names": [], "function_names": ["validate_config"], "import_names": ["typing"]}, ".\\utils\\data_cleaning.py": {"file": ".\\utils\\data_cleaning.py", "lines": 361, "classes": 0, "functions": 29, "imports": 6, "class_names": [], "function_names": ["microstructure_noise_cleaning", "drop_missing", "fill_missing", "remove_outliers", "filter_invalid", "drop_duplicates", "fix_inconsistent_data", "remove_time_anomalies", "remove_sudden_spikes", "impute_missing_statistical", "remove_zero_abnormal_volume", "normalize_categorical", "filter_market_hours", "filter_unrealistic_financial_ratios", "remove_unrealistic_price_change", "spike_correction", "contextual_outlier_cleaning", "anomaly_detection_cleaning", "adaptive_outlier_cleaning", "remove_holiday_illiquid", "corporate_action_cleaning", "ml_based_cleaning", "adaptive_cleaning_by_reward", "explainable_cleaning", "testable_cleaning", "scenario_based_cleaning", "feature_driven_cleaning", "imitation_cleaning", "cross_validation_cleaning"], "import_names": ["shap", "sklearn.ensemble", "typing", "datetime", "numpy", "pandas"]}, ".\\utils\\data_cleaning_pipeline.py": {"file": ".\\utils\\data_cleaning_pipeline.py", "lines": 153, "classes": 0, "functions": 1, "imports": 3, "class_names": [], "function_names": ["advanced_data_cleaning"], "import_names": ["data_cleaning", "typing", "pandas"]}, ".\\utils\\data_pipeline.py": {"file": ".\\utils\\data_pipeline.py", "lines": 30, "classes": 1, "functions": 4, "imports": 2, "class_names": ["DataPipeline"], "function_names": ["normalize_features", "__init__", "add_step", "run"], "import_names": ["typing", "pandas"]}, ".\\utils\\data_utils.py": {"file": ".\\utils\\data_utils.py", "lines": 268, "classes": 0, "functions": 5, "imports": 5, "class_names": [], "function_names": ["load_data", "preprocess_data", "add_technical_indicators", "split_data", "prepare_data_for_env"], "import_names": ["logging", "typing", "os", "numpy", "pandas"]}, ".\\utils\\enhanced_adaptive_plutus.py": {"file": ".\\utils\\enhanced_adaptive_plutus.py", "lines": 444, "classes": 1, "functions": 9, "imports": 11, "class_names": ["EnhancedAdaptivePlutusSystem"], "function_names": ["main", "__init__", "initialize_regime_detection", "get_regime_aware_signal", "_combine_adaptive_and_regime_weights", "_get_regime_name_from_weights", "_adjust_recommendation_for_regime", "analyze_regime_performance", "generate_enhanced_learning_report"], "import_names": ["logging", "utils.market_regime_detector", "json", "tests.test_plutus_models_comprehensive", "typing", "os", "sys", "utils.adaptive_plutus_system", "datetime", "numpy", "pandas"]}, ".\\utils\\explainable_ai.py": {"file": ".\\utils\\explainable_ai.py", "lines": 1877, "classes": 2, "functions": 33, "imports": 15, "class_names": ["ExplainableAI", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "function_names": ["__init__", "set_model", "set_feature_names", "feature_importance", "_permutation_importance", "_shap_importance", "_gradient_importance", "decision_explanation", "visualize_feature_importance", "visualize_decision_process", "compare_decisions", "generate_explanation_report", "analyze_market_regime", "analyze_decision_risk", "identify_critical_decision_points", "create_decision_heatmap", "counterfactual_analysis", "sensitivity_analysis", "detect_bias", "advanced_pnl_attribution", "stress_scenario_analysis", "decision_memory_analysis", "behavioral_anomaly_detection", "generate_nlg_explanation", "visualize_decision_flow", "interactive_decision_space", "expert_comparison", "news_impact_analysis", "market_impact_analysis", "decision_lag_analysis", "explanation_quality_assessment", "predict_fn", "default"], "import_names": ["seaborn", "shap", "sklearn.preprocessing", "matplotlib.patches", "stable_baselines3.common.policies", "torch", "matplotlib.lines", "plotly.express", "collections", "json", "typing", "matplotlib.pyplot", "sklearn.cluster", "numpy", "pandas"]}, ".\\utils\\export.py": {"file": ".\\utils\\export.py", "lines": 19, "classes": 0, "functions": 3, "imports": 3, "class_names": [], "function_names": ["export_to_csv", "export_to_json", "export_to_excel"], "import_names": ["typing", "json", "pandas"]}, ".\\utils\\federated_learning_system.py": {"file": ".\\utils\\federated_learning_system.py", "lines": 523, "classes": 5, "functions": 22, "imports": 13, "class_names": ["ModelUpdate", "FederatedModel", "PrivacyPreservingAggregator", "FederatedLearningClient", "FederatedLearningServer"], "function_names": ["main", "__init__", "aggregate_weights", "validate_update", "_calculate_privacy_hash", "__init__", "_init_database", "train_local_model", "_train_rl_model", "_train_prediction_model", "_train_generic_model", "_evaluate_local_model", "_save_local_model", "apply_global_update", "__init__", "_init_database", "receive_update", "_save_client_update", "aggregate_updates", "_save_federated_model", "get_latest_model", "get_server_status"], "import_names": ["logging", "pickle", "time", "json", "dataclasses", "typing", "sqlite3", "threading", "<PERSON><PERSON><PERSON>", "random", "datetime", "numpy", "pandas"]}, ".\\utils\\genetic_strategy_evolution.py": {"file": ".\\utils\\genetic_strategy_evolution.py", "lines": 870, "classes": 6, "functions": 31, "imports": 12, "class_names": ["TradingStrategy", "EvolutionConfig", "StrategyGenePool", "StrategyEvaluator", "GeneticAlgorithm", "GeneticStrategyEvolution"], "function_names": ["main", "__init__", "generate_random_strategy", "__init__", "evaluate_strategy", "_simulate_trades", "_generate_signals", "_calculate_indicators", "_evaluate_rule_conditions", "_get_default_action", "_execute_buy", "_should_close_position", "_execute_sell", "_calculate_performance_metrics", "_calculate_fitness_score", "__init__", "initialize_population", "evolve", "_evaluate_population", "_create_new_generation", "_tournament_selection", "_crossover", "_mutate", "__init__", "_init_database", "load_historical_data", "run_evolution", "_save_evolution_results", "get_best_strategy", "get_evolution_summary", "_calculate_fitness"], "import_names": ["copy", "logging", "time", "json", "dataclasses", "typing", "sqlite3", "threading", "random", "datetime", "numpy", "pandas"]}, ".\\utils\\hft_modeling.py": {"file": ".\\utils\\hft_modeling.py", "lines": 672, "classes": 8, "functions": 24, "imports": 18, "class_names": ["OrderBookLevel", "Trade", "MarketMicrostructure", "OrderBookAnalyzer", "LatencyOptimizer", "ShortTermPredictor", "HFTStrategy", "HFTModelingSystem"], "function_names": ["__init__", "update_order_book", "_calculate_current_stats", "calculate_price_impact", "get_market_microstructure", "__init__", "measure_execution_time", "measure_network_delay", "get_latency_stats", "optimize_data_structures", "__init__", "extract_features", "train_model", "predict_direction", "__init__", "should_enter_position", "should_exit_position", "execute_trade", "get_performance_stats", "__init__", "start", "stop", "process_market_data", "get_system_stats"], "import_names": ["sklearn.preprocessing", "logging", "concurrent.futures", "time", "torch", "subprocess", "platform", "collections", "dataclasses", "sklearn.ensemble", "warnings", "typing", "threading", "torch.nn", "datetime", "asyncio", "numpy", "pandas"]}, ".\\utils\\integrated_advanced_system.py": {"file": ".\\utils\\integrated_advanced_system.py", "lines": 534, "classes": 2, "functions": 12, "imports": 13, "class_names": ["IntegratedSignal", "IntegratedAdvancedTradingSystem"], "function_names": ["main", "__init__", "initialize_with_data", "_initialize_rl_with_samples", "get_integrated_signal", "_get_rl_recommendation", "_combine_signals", "_assess_risk_level", "_calculate_position_size", "_calculate_risk_levels", "get_system_status", "save_all_systems"], "import_names": ["logging", "utils.advanced_rl_agent", "json", "utils.enhanced_adaptive_plutus", "utils.multi_step_prediction_fixed", "dataclasses", "tests.test_plutus_models_comprehensive", "typing", "os", "sys", "datetime", "numpy", "pandas"]}, ".\\utils\\intelligent_memory_system.py": {"file": ".\\utils\\intelligent_memory_system.py", "lines": 970, "classes": 7, "functions": 40, "imports": 15, "class_names": ["MemoryItem", "Pattern", "ShortTermMemory", "MediumTermMemory", "LongTermMemory", "PatternDetector", "IntelligentMemorySystem"], "function_names": ["main", "__init__", "store", "retrieve", "search", "get_frequent_patterns", "_matches_query", "_cleanup_expired", "_remove_memory", "get_stats", "__init__", "_init_database", "consolidate_from_short_term", "_consolidate_related_memories", "_group_similar_memories", "_calculate_similarity", "_calculate_content_similarity", "_create_consolidated_memory", "_store_memory", "_store_pattern", "retrieve_patterns", "__init__", "_init_database", "consolidate_from_medium_term", "_pattern_to_knowledge", "_memory_to_knowledge", "_store_knowledge", "query_knowledge", "__init__", "detect_patterns", "_detect_temporal_patterns", "_detect_content_patterns", "_detect_behavioral_patterns", "__init__", "store_memory", "retrieve_memory", "search_memories", "get_patterns", "get_system_stats", "_auto_consolidation"], "import_names": ["sklearn.preprocessing", "logging", "time", "json", "collections", "dataclasses", "sklearn.cluster", "warnings", "typing", "sqlite3", "threading", "<PERSON><PERSON><PERSON>", "datetime", "numpy", "pandas"]}, ".\\utils\\logger.py": {"file": ".\\utils\\logger.py", "lines": 26, "classes": 0, "functions": 1, "imports": 1, "class_names": [], "function_names": ["get_logger"], "import_names": ["logging"]}, ".\\utils\\log_config.py": {"file": ".\\utils\\log_config.py", "lines": 30, "classes": 0, "functions": 1, "imports": 3, "class_names": [], "function_names": ["setup_logging"], "import_names": ["logging.handlers", "logging", "os"]}, ".\\utils\\market_regime_detector.py": {"file": ".\\utils\\market_regime_detector.py", "lines": 630, "classes": 3, "functions": 16, "imports": 11, "class_names": ["MarketRegime", "RegimeDetectionResult", "MarketRegimeDetector"], "function_names": ["main", "__init__", "fit", "predict_regime", "_define_market_regimes", "extract_market_features", "_calculate_rsi", "_calculate_support_resistance_strength", "_get_default_features", "train_regime_model", "detect_regime", "_rule_based_detection", "_get_default_regime_result", "get_regime_optimal_weights", "analyze_regime_stability", "get_regime_transition_probability"], "import_names": ["sklearn.mixture", "sklearn.preprocessing", "logging", "json", "collections", "dataclasses", "warnings", "typing", "datetime", "numpy", "pandas"]}, ".\\utils\\multi_exchange_routing.py": {"file": ".\\utils\\multi_exchange_routing.py", "lines": 938, "classes": 12, "functions": 23, "imports": 13, "class_names": ["ExchangeStatus", "OrderType", "ExchangeInfo", "OrderBookData", "ArbitrageOpportunity", "ExecutionOrder", "ExchangeConnector", "ArbitrageEngine", "ExecutionCostOptimizer", "LiquidityManager", "FailoverSystem", "MultiExchangeRouter"], "function_names": ["__init__", "get_balance", "update_balance", "__init__", "find_arbitrage_opportunities", "rank_opportunities", "__init__", "calculate_total_cost", "estimate_slippage", "select_optimal_exchange", "__init__", "split_large_order", "calculate_market_impact", "__init__", "add_backup_exchanges", "_record_failure", "get_healthy_exchanges", "get_failover_exchange", "stop_monitoring", "__init__", "get_aggregated_order_book", "_aggregate_levels", "get_statistics"], "import_names": ["logging", "concurrent.futures", "time", "collections", "json", "dataclasses", "typing", "enum", "threading", "datetime", "asyncio", "numpy", "pandas"]}, ".\\utils\\multi_step_prediction.py": {"file": ".\\utils\\multi_step_prediction.py", "lines": 692, "classes": 5, "functions": 20, "imports": 13, "class_names": ["PredictionTarget", "MultiStepPrediction", "FeatureExtractor", "MultiStepPredictor", "MultiStepPredictionSystem"], "function_names": ["main", "__init__", "extract_features", "_calculate_rsi", "__init__", "prepare_training_data", "train_model", "predict", "_calculate_ensemble_agreement", "save_models", "load_models", "__init__", "_init_database", "add_symbol", "train_models", "_save_model_performance", "get_prediction", "_save_prediction", "get_prediction_summary", "generate_trading_signals"], "import_names": ["sklearn.preprocessing", "logging", "pickle", "json", "dataclasses", "pathlib", "sklearn.ensemble", "typing", "sqlite3", "sklearn.metrics", "datetime", "numpy", "pandas"]}, ".\\utils\\multi_step_prediction_fixed.py": {"file": ".\\utils\\multi_step_prediction_fixed.py", "lines": 474, "classes": 5, "functions": 18, "imports": 13, "class_names": ["PredictionTarget", "MultiStepPrediction", "FeatureExtractor", "MultiStepPredictor", "MultiStepPredictionSystem"], "function_names": ["main", "__init__", "extract_features", "_calculate_rsi", "__init__", "train", "predict", "predict_with_dataframe", "prepare_training_data", "train_model", "_calculate_ensemble_agreement", "__init__", "_init_database", "add_symbol", "train_models", "get_prediction", "_save_prediction", "generate_trading_signals"], "import_names": ["sklearn.preprocessing", "logging", "pickle", "json", "dataclasses", "pathlib", "sklearn.ensemble", "typing", "sqlite3", "sklearn.metrics", "datetime", "numpy", "pandas"]}, ".\\utils\\news_volume_analyzer.py": {"file": ".\\utils\\news_volume_analyzer.py", "lines": 269, "classes": 1, "functions": 6, "imports": 6, "class_names": ["NewsVolumeAnalyzer"], "function_names": ["__init__", "add_news", "_clean_old_news", "get_volume", "detect_volume_spike", "get_all_assets_with_spikes"], "import_names": ["logging", "collections", "typing", "datetime", "numpy", "pandas"]}, ".\\utils\\plutus_integration.py": {"file": ".\\utils\\plutus_integration.py", "lines": 551, "classes": 3, "functions": 19, "imports": 10, "class_names": ["PlutusConfig", "PlutusFinancialForecaster", "PlutusIntegrationExample"], "function_names": ["create_plutus_integration_example", "__init__", "prepare_time_series_data", "make_prediction_request", "predict_price_movement", "process_prediction_response", "calculate_confidence", "__init__", "get_enhanced_predictions", "combine_signals", "assess_risk", "calculate_volatility_risk", "calculate_directional_risk", "calculate_overall_recommendation", "generate_recommendations", "calculate_position_size", "calculate_stop_loss", "calculate_take_profit", "generate_reasoning"], "import_names": ["logging", "json", "requests", "dataclasses", "typing", "aiohttp", "datetime", "asyncio", "numpy", "pandas"]}, ".\\utils\\portfolio_visualizer.py": {"file": ".\\utils\\portfolio_visualizer.py", "lines": 62, "classes": 1, "functions": 4, "imports": 3, "class_names": ["PortfolioVisualizer"], "function_names": ["__init__", "plot_pnl_curve", "plot_trade_distribution", "plot_drawdown"], "import_names": ["seaborn", "typing", "matplotlib.pyplot"]}, ".\\utils\\proxy_manager.py": {"file": ".\\utils\\proxy_manager.py", "lines": 152, "classes": 1, "functions": 8, "imports": 7, "class_names": ["ProxyManager"], "function_names": ["setup_proxy_env", "get_proxy_manager", "__init__", "_load_proxy_config", "_setup_session", "test_connection", "get_stable_session", "download_with_retry"], "import_names": ["urllib3", "logging", "time", "requests", "json", "typing", "os"]}, ".\\utils\\proxy_setup.py": {"file": ".\\utils\\proxy_setup.py", "lines": 41, "classes": 0, "functions": 2, "imports": 2, "class_names": [], "function_names": ["setup_proxy", "patched_init"], "import_names": ["os", "requests"]}, ".\\utils\\reward_redistribution.py": {"file": ".\\utils\\reward_redistribution.py", "lines": 843, "classes": 13, "functions": 47, "imports": 12, "class_names": ["RedistributionStrategy", "RewardStrategy", "LinearRewardStrategy", "ExponentialRewardStrategy", "AdaptiveRewardStrategy", "RewardMemorySystem", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RewardQualityMonitor", "DynamicRewardWeighting", "RiskAdjustedRewardSystem", "AdvancedRewardRedistributor", "RewardOptimizer", "RewardRedistributor"], "function_names": ["redistribute", "create_advanced_redistributor", "calculate_multiplier", "get_strategy_name", "__init__", "calculate_multiplier", "get_strategy_name", "__init__", "calculate_multiplier", "get_strategy_name", "__init__", "calculate_multiplier", "get_strategy_name", "__post_init__", "add_record", "get_weighted_average_performance", "get_drawdown_statistics", "__init__", "_prepare_features", "train", "predict_next_reward", "__init__", "calculate_reward_quality", "is_quality_acceptable", "get_quality_trend", "__init__", "update_action_performance", "calculate_action_weight", "get_weighted_reward", "__init__", "calculate_risk_adjusted_reward", "__post_init__", "_calculate_drawdown", "_prepare_context", "redistribute_single", "redistribute_batch", "get_performance_summary", "predict_next_reward", "optimize_strategy", "__init__", "optimize_hyperparameters", "_grid_search_optimization", "_random_search_optimization", "__post_init__", "_calculate_drawdown", "_get_multiplier", "redistribute"], "import_names": ["sklearn.preprocessing", "__future__", "abc", "dataclasses", "sklearn.ensemble", "enum", "typing", "scipy", "sklearn.linear_model", "warnings", "numpy", "pandas"]}, ".\\utils\\risk_manager.py": {"file": ".\\utils\\risk_manager.py", "lines": 521, "classes": 1, "functions": 9, "imports": 4, "class_names": ["RiskManager"], "function_names": ["__init__", "update_balance", "get_risk_status", "calculate_position_size", "can_open_position", "open_position", "close_position", "check_stop_levels", "get_daily_summary"], "import_names": ["typing", "datetime", "numpy", "pandas"]}, ".\\utils\\rl_training_utils.py": {"file": ".\\utils\\rl_training_utils.py", "lines": 1297, "classes": 15, "functions": 103, "imports": 9, "class_names": ["EarlyStoppingLookahead", "EarlyStoppingCombo", "EarlyStoppingRegularization", "EarlyStoppingMovingAverage", "EarlyStoppingDynamicPatience", "EarlyStoppingDrawdown", "EarlyStopping", "DeepRLExecutionModel", "AdvancedSentimentAnalyzer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AutoEnsemble", "Continual<PERSON><PERSON><PERSON>", "AutoMarketMaker", "AdaptiveTradeExecutor", "SentimentAnalyzer"], "function_names": ["explainable_ai_feature_importance", "advanced_auto_feature_engineering", "realistic_scenario_based_backtesting", "advanced_anomaly_detection", "adaptive_reward_shaping", "imitation_learning_expert_policy", "federated_learning_cv", "auto_news_event_detection", "auto_scenario_generation", "anomaly_detection_cv", "self_play_cv", "whale_tracking_reward_shaping", "scenario_backtesting_cv", "auto_feature_engineering", "multi_market_cv", "adversarial_cv", "lookahead_cv", "comparative_earlystopping_curve", "overlapping_cv", "feature_importance_cv", "adaptive_window_cv", "overlapping_window_cv", "scenario_based_cv", "uncertainty_cv", "augment_data", "data_augmentation_cv", "stability_cv", "rolling_window_cv", "adversarial_training", "whale_tracking", "federated_learning_train", "multi_account_portfolio_manager", "auto_trade_execution", "real_time_anomaly_alerting", "__init__", "step", "__init__", "step", "__init__", "step", "__init__", "step", "__init__", "step", "__init__", "step", "__init__", "step", "__init__", "forward", "__init__", "score_text", "analyze_batch", "aggregate_score", "news_volume_shock", "weighted_source_score", "__init__", "record_performance", "select_best", "get_model", "transfer_knowledge", "explain_selection", "__init__", "predict", "update_weights", "__init__", "store_experience", "rehearse", "regularize", "update_prev_weights", "__init__", "decide_spread", "quote", "update_inventory", "simulate_market_making", "__init__", "_init_deep_model", "_state_from_order_book", "_extract_features", "fit", "select_action", "execute", "online_update", "_simulate_market_impact", "fit_deep_rl", "predict_order_type", "fit_multi_agent", "detect_liquidity_shock", "adapt_to_abnormal_conditions", "get_order_book_snapshot", "execute_dual_mode", "compute_real_slippage", "parameter_sensitivity", "explain_execution", "transfer_q_table", "split_block_trade", "optimize_commission", "safe_execute_live", "sentiment_aware_execute", "__init__", "score_text", "analyze_batch", "aggregate_score"], "import_names": ["rl_training_utils", "torch", "collections", "re", "itertools", "torch.nn", "random", "numpy", "torch.optim"]}, ".\\utils\\security.py": {"file": ".\\utils\\security.py", "lines": 28, "classes": 1, "functions": 3, "imports": 0, "class_names": ["SecureStorage"], "function_names": ["__init__", "encrypt", "decrypt"], "import_names": []}, ".\\utils\\sentiment_analyzer.py": {"file": ".\\utils\\sentiment_analyzer.py", "lines": 1145, "classes": 14, "functions": 48, "imports": 19, "class_names": ["SentimentType", "AspectCategory", "SentimentResult", "MarketSentiment", "LanguageModel", "AspectBasedSentimentAnalyzer", "TemporalSentimentAnalyzer", "SocialMediaSentimentAnalyzer", "MarketSentimentAggregator", "SentimentCache", "SentimentImpactPredictor", "EnsembleSentimentAnalyzer", "AdvancedSentimentAnalyzer", "SentimentAnalyzer"], "function_names": ["sentiment", "__init__", "_setup_proxy", "_load_model", "analyze", "__init__", "extract_aspects", "analyze_aspect_sentiment", "extract_entities", "__init__", "add_sentiment", "get_trend", "get_momentum", "get_volatility", "predict_next_sentiment", "__init__", "analyze_social_text", "_calculate_emoji_sentiment", "_clean_social_text", "__init__", "add_sentiment", "calculate_market_sentiment", "_empty_market_sentiment", "__init__", "_get_cache_key", "get", "put", "get_stats", "__init__", "train", "predict_impact", "__init__", "analyze_ensemble", "_label_to_score", "update_weights", "__init__", "_load_language_models", "detect_language", "analyze", "_analyze_entity_sentiment", "analyze_social_media", "get_market_sentiment", "get_sentiment_trend", "train_impact_predictor", "get_cache_stats", "batch_analyze", "__init__", "get_sentiment_score"], "import_names": ["sklearn.preprocessing", "logging", "collections", "langdetect", "typing", "<PERSON><PERSON><PERSON>", "torch", "dataclasses", "sklearn.ensemble", "datetime", "spacy", "json", "enum", "transformers", "numpy", "pandas", "re", "os", "vaderSentiment.vaderSentiment"]}, ".\\utils\\sentiment_analyzer_old.py": {"file": ".\\utils\\sentiment_analyzer_old.py", "lines": 253, "classes": 1, "functions": 5, "imports": 6, "class_names": ["SentimentAnalyzer"], "function_names": ["__init__", "_load_models", "detect_language", "analyze", "get_sentiment_score"], "import_names": ["utils.source_credibility", "logging", "torch", "langdetect", "transformers", "os"]}, ".\\utils\\sentiment_integrator.py": {"file": ".\\utils\\sentiment_integrator.py", "lines": 414, "classes": 1, "functions": 9, "imports": 8, "class_names": ["SentimentIntegrator"], "function_names": ["__init__", "_normalize_weights", "set_weights", "process_news", "get_sentiment_signal", "get_volume_shock_signal", "get_technical_signal", "get_fundamental_signal", "get_integrated_signal"], "import_names": ["logging", "sentiment_analyzer", "source_credibility", "typing", "news_volume_analyzer", "datetime", "numpy", "pandas"]}, ".\\utils\\source_credibility.py": {"file": ".\\utils\\source_credibility.py", "lines": 230, "classes": 1, "functions": 9, "imports": 5, "class_names": ["SourceCredibility"], "function_names": ["__init__", "_load_data", "_save_data", "get_score", "set_score", "adjust_score", "get_top_sources", "get_bottom_sources", "apply_credibility_weight"], "import_names": ["logging", "json", "typing", "os", "datetime"]}, ".\\utils\\test_fetcher.py": {"file": ".\\utils\\test_fetcher.py", "lines": 19, "classes": 0, "functions": 2, "imports": 3, "class_names": [], "function_names": ["fetcher", "test_fetch_data"], "import_names": ["pytest", "data.fetcher", "pandas"]}, ".\\utils\\__init__.py": {"file": ".\\utils\\__init__.py", "lines": 2, "classes": 0, "functions": 0, "imports": 0, "class_names": [], "function_names": [], "import_names": []}}, "database_analysis": {".\\advanced_test.db": {"tables": 4, "table_info": {"model_performance": 0, "sqlite_sequence": 0, "adaptive_weights": 0, "market_conditions": 0}}, ".\\enhanced_demo.db": {"tables": 4, "table_info": {"model_performance": 0, "sqlite_sequence": 0, "adaptive_weights": 0, "market_conditions": 0}}, ".\\regime_trading_system.db": {"tables": 4, "table_info": {"model_performance": 0, "sqlite_sequence": 0, "adaptive_weights": 0, "market_conditions": 0}}, ".\\test_adaptive.db": {"tables": 4, "table_info": {"model_performance": 1, "sqlite_sequence": 2, "adaptive_weights": 1, "market_conditions": 0}}, ".\\test_analysis.db": {"tables": 4, "table_info": {"model_performance": 20, "sqlite_sequence": 1, "adaptive_weights": 0, "market_conditions": 0}}, ".\\test_anomaly_system.db": {"tables": 4, "table_info": {"anomaly_events": 1, "sqlite_sequence": 2, "adaptation_rules": 0, "adaptation_history": 1}}, ".\\test_federated_client_1.db": {"tables": 3, "table_info": {"local_models": 12, "sqlite_sequence": 2, "federated_updates": 12}}, ".\\test_federated_client_2.db": {"tables": 3, "table_info": {"local_models": 12, "sqlite_sequence": 2, "federated_updates": 12}}, ".\\test_federated_client_3.db": {"tables": 3, "table_info": {"local_models": 12, "sqlite_sequence": 2, "federated_updates": 12}}, ".\\test_federated_client_4.db": {"tables": 3, "table_info": {"local_models": 12, "sqlite_sequence": 2, "federated_updates": 12}}, ".\\test_federated_server.db": {"tables": 3, "table_info": {"federated_models": 12, "sqlite_sequence": 2, "client_updates": 48}}, ".\\test_genetic_evolution.db": {"tables": 2, "table_info": {"strategies": 21, "evolution_runs": 3}}, ".\\test_hyperparameter_tuning.db": {"tables": 3, "table_info": {"optimization_results": 0, "sqlite_sequence": 0, "optimization_history": 0}}, ".\\test_integrated.db_adaptive.db": {"tables": 4, "table_info": {"model_performance": 0, "sqlite_sequence": 0, "adaptive_weights": 0, "market_conditions": 0}}, ".\\test_integrated.db_predictions.db": {"tables": 2, "table_info": {"predictions": 16, "sqlite_sequence": 1}}, ".\\test_integrated.db_rl.db": {"tables": 3, "table_info": {"rl_experiences": 300, "sqlite_sequence": 2, "rl_performance": 6}}, ".\\test_intelligent_memory.db": {"tables": 4, "table_info": {"medium_term_memories": 0, "detected_patterns": 0, "long_term_knowledge": 0, "semantic_relationships": 0}}, ".\\test_learning.db": {"tables": 4, "table_info": {"model_performance": 20, "sqlite_sequence": 2, "adaptive_weights": 1, "market_conditions": 0}}, ".\\test_predictions.db": {"tables": 3, "table_info": {"predictions": 4, "sqlite_sequence": 1, "model_performance": 0}}, ".\\test_rl_system.db": {"tables": 3, "table_info": {"rl_experiences": 2, "sqlite_sequence": 2, "rl_performance": 2}}, ".\\test_signals.db": {"tables": 4, "table_info": {"model_performance": 0, "sqlite_sequence": 0, "adaptive_weights": 0, "market_conditions": 0}}}, "structure_analysis": {"root": {"directories": 23, "files": 112, "python_files": 18, "config_files": 7, "data_files": 24, "image_files": 33, "text_files": 28}, "api": {"directories": 1, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "api\\__pycache__": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "best_models": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "checkpoints": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 1, "image_files": 0, "text_files": 0}, "claude-html-chat": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "data": {"directories": 21, "files": 7, "python_files": 5, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "data\\AUDJPY": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data\\AUDUSD": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data\\BTCUSD": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data\\chunks_head": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "data\\DJ30.spot": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data\\ETHUSD": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data\\EURJPY": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data\\EURUSD": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data\\GBPJPY": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data\\GBPUSD": {"directories": 0, "files": 8, "python_files": 0, "config_files": 0, "data_files": 8, "image_files": 0, "text_files": 0}, "data\\NDQ100.spot": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data\\NZDUSD": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 7, "image_files": 0, "text_files": 0}, "data\\storage": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 2, "image_files": 0, "text_files": 0}, "data\\USDCAD": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data\\USDCHF": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data\\USDJPY": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 7, "image_files": 0, "text_files": 0}, "data\\wal": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "data\\WTI.spot": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data\\XAGUSD": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data\\XAUUSD": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data\\XRPUSD": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data_new": {"directories": 18, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "data_new\\AUDJPY": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data_new\\AUDUSD": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data_new\\BTCUSD": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data_new\\DJ30.spot": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data_new\\ETHUSD": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data_new\\EURJPY": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data_new\\EURUSD": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data_new\\GBPJPY": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 5, "image_files": 0, "text_files": 0}, "data_new\\GBPUSD": {"directories": 0, "files": 8, "python_files": 0, "config_files": 0, "data_files": 8, "image_files": 0, "text_files": 0}, "data_new\\NDQ100.spot": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data_new\\NZDUSD": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 7, "image_files": 0, "text_files": 0}, "data_new\\USDCAD": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data_new\\USDCHF": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data_new\\USDJPY": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 7, "image_files": 0, "text_files": 0}, "data_new\\WTI.spot": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data_new\\XAGUSD": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data_new\\XAUUSD": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "data_new\\XRPUSD": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "docs": {"directories": 0, "files": 8, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 8}, "env": {"directories": 1, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "env\\__pycache__": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "evaluation": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "examples": {"directories": 1, "files": 23, "python_files": 23, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "examples\\__pycache__": {"directories": 0, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "logs": {"directories": 0, "files": 22, "python_files": 0, "config_files": 12, "data_files": 0, "image_files": 0, "text_files": 10}, "models": {"directories": 1, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "models\\__pycache__": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "optimization": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "portfolio": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "project_backup": {"directories": 9, "files": 6, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "project_backup\\api": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "project_backup\\data": {"directories": 1, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "project_backup\\data\\storage": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 2, "image_files": 0, "text_files": 0}, "project_backup\\env": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "project_backup\\evaluation": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "project_backup\\models": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "project_backup\\optimization": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "project_backup\\portfolio": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "project_backup\\tests": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "project_backup\\utils": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "reports": {"directories": 0, "files": 1, "python_files": 0, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "storage": {"directories": 0, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "temp": {"directories": 0, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "temp_models": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "tests": {"directories": 1, "files": 52, "python_files": 48, "config_files": 2, "data_files": 0, "image_files": 0, "text_files": 2}, "tests\\__pycache__": {"directories": 0, "files": 18, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "test_logs": {"directories": 0, "files": 9, "python_files": 0, "config_files": 9, "data_files": 0, "image_files": 0, "text_files": 0}, "utils": {"directories": 1, "files": 48, "python_files": 48, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "utils\\__pycache__": {"directories": 0, "files": 26, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv": {"directories": 2, "files": 1, "python_files": 0, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Include": {"directories": 0, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib": {"directories": 1, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages": {"directories": 134, "files": 12, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\adodbapi": {"directories": 2, "files": 10, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\adodbapi\\examples": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\adodbapi\\test": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\annotated_types": {"directories": 0, "files": 3, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\annotated_types-0.7.0.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\annotated_types-0.7.0.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\anyio": {"directories": 4, "files": 8, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\anyio\\abc": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\anyio\\streams": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\anyio\\_backends": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\anyio\\_core": {"directories": 0, "files": 15, "python_files": 15, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\anyio-4.9.0.dist-info": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\asyncer": {"directories": 0, "files": 4, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\asyncer-0.0.8.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\asyncer-0.0.8.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\attr": {"directories": 0, "files": 23, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\attrs": {"directories": 0, "files": 8, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\attrs-25.3.0.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\attrs-25.3.0.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen": {"directories": 15, "files": 17, "python_files": 17, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat": {"directories": 4, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\contrib": {"directories": 6, "files": 15, "python_files": 15, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\contrib\\agent_eval": {"directories": 0, "files": 7, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\autogen\\agentchat\\contrib\\capabilities": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\contrib\\captainagent": {"directories": 1, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\contrib\\captainagent\\tools": {"directories": 3, "files": 4, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\autogen\\agentchat\\contrib\\captainagent\\tools\\data_analysis": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\contrib\\captainagent\\tools\\information_retrieval": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\contrib\\captainagent\\tools\\math": {"directories": 0, "files": 15, "python_files": 15, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\contrib\\graph_rag": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\contrib\\rag": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\contrib\\vectordb": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\group": {"directories": 2, "files": 15, "python_files": 15, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\group\\patterns": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\group\\targets": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\realtime": {"directories": 1, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\realtime\\experimental": {"directories": 2, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\realtime\\experimental\\audio_adapters": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\realtime\\experimental\\clients": {"directories": 2, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\realtime\\experimental\\clients\\gemini": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\realtime\\experimental\\clients\\oai": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agentchat\\realtime_agent": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agents": {"directories": 2, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agents\\contrib": {"directories": 1, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agents\\contrib\\time": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agents\\experimental": {"directories": 8, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agents\\experimental\\deep_research": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agents\\experimental\\discord": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agents\\experimental\\document_agent": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agents\\experimental\\reasoning": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agents\\experimental\\slack": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agents\\experimental\\telegram": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agents\\experimental\\websurfer": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\agents\\experimental\\wikipedia": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\cache": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\coding": {"directories": 1, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\coding\\jupyter": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\events": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\extensions": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\fast_depends": {"directories": 3, "files": 6, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\fast_depends\\core": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\fast_depends\\dependencies": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\fast_depends\\library": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\interop": {"directories": 4, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\interop\\crewai": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\interop\\langchain": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\interop\\litellm": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\interop\\pydantic_ai": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\io": {"directories": 1, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\io\\processors": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\logger": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\mcp": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\messages": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\oai": {"directories": 1, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\oai\\oai_models": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools": {"directories": 2, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\contrib": {"directories": 1, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\contrib\\time": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental": {"directories": 9, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\browser_use": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\crawl4ai": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\deep_research": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\google": {"directories": 2, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\google\\authentication": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\google\\drive": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\google_search": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\messageplatform": {"directories": 3, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\messageplatform\\discord": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\messageplatform\\slack": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\messageplatform\\telegram": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\perplexity": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\tavily": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\tools\\experimental\\wikipedia": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\autogen\\_website": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\certifi": {"directories": 0, "files": 5, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\certifi-2025.4.26.dist-info": {"directories": 1, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\certifi-2025.4.26.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\charset_normalizer": {"directories": 1, "files": 13, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\charset_normalizer\\cli": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\charset_normalizer-3.4.1.dist-info": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\click": {"directories": 0, "files": 17, "python_files": 16, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\click-8.1.8.dist-info": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\cloudpickle": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\cloudpickle-3.1.1.dist-info": {"directories": 0, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\colorama": {"directories": 1, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\colorama\\tests": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\colorama-0.4.6.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\colorama-0.4.6.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\contourpy": {"directories": 1, "files": 13, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\contourpy\\util": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\contourpy-1.3.0.dist-info": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\cycler": {"directories": 0, "files": 2, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\cycler-0.12.1.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\dateutil": {"directories": 3, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\dateutil\\parser": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\dateutil\\tz": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\dateutil\\zoneinfo": {"directories": 0, "files": 3, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\diskcache": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\diskcache-5.6.3.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\docker": {"directories": 7, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\docker\\api": {"directories": 0, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\docker\\context": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\docker\\credentials": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\docker\\models": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\docker\\transport": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\docker\\types": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\docker\\utils": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\docker-7.1.0.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\docker-7.1.0.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\dotenv": {"directories": 0, "files": 9, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\exceptiongroup": {"directories": 0, "files": 7, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\exceptiongroup-1.2.2.dist-info": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\farama_notifications": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\Farama_Notifications-0.0.4.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\filelock": {"directories": 0, "files": 10, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\filelock-3.18.0.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\filelock-3.18.0.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools": {"directories": 21, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\cffLib": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\colorLib": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\config": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\cu2qu": {"directories": 0, "files": 9, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\designspaceLib": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\encodings": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\feaLib": {"directories": 0, "files": 12, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\merge": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\misc": {"directories": 1, "files": 37, "python_files": 35, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\misc\\plistlib": {"directories": 0, "files": 2, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\mtiLib": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\otlLib": {"directories": 1, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\otlLib\\optimize": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\pens": {"directories": 0, "files": 31, "python_files": 29, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\qu2cu": {"directories": 0, "files": 7, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\subset": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\svgLib": {"directories": 1, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\svgLib\\path": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\t1Lib": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\ttLib": {"directories": 1, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\ttLib\\tables": {"directories": 0, "files": 99, "python_files": 98, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\fontTools\\ufoLib": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\unicodedata": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\varLib": {"directories": 1, "files": 26, "python_files": 24, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\varLib\\instancer": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fontTools\\voltLib": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fonttools-4.57.0.dist-info": {"directories": 1, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\fonttools-4.57.0.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fsspec": {"directories": 2, "files": 22, "python_files": 22, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fsspec\\implementations": {"directories": 0, "files": 25, "python_files": 25, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fsspec\\tests": {"directories": 1, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fsspec\\tests\\abstract": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fsspec-2025.3.2.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\fsspec-2025.3.2.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\functorch": {"directories": 5, "files": 2, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\functorch\\compile": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\functorch\\dim": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\functorch\\einops": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\functorch\\experimental": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\functorch\\_src": {"directories": 4, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\functorch\\_src\\aot_autograd": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\functorch\\_src\\eager_transforms": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\functorch\\_src\\make_functional": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\functorch\\_src\\vmap": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\google": {"directories": 2, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\google\\protobuf": {"directories": 5, "files": 32, "python_files": 32, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\google\\protobuf\\compiler": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\google\\protobuf\\internal": {"directories": 0, "files": 16, "python_files": 16, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\google\\protobuf\\pyext": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\google\\protobuf\\testdata": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\google\\protobuf\\util": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\google\\_upb": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym": {"directories": 5, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs": {"directories": 6, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\box2d": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\classic_control": {"directories": 1, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\classic_control\\assets": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 1, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\mujoco": {"directories": 1, "files": 21, "python_files": 21, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\mujoco\\assets": {"directories": 0, "files": 14, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\robotics": {"directories": 3, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\robotics\\assets": {"directories": 4, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\gym\\envs\\robotics\\assets\\fetch": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\robotics\\assets\\hand": {"directories": 0, "files": 12, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\robotics\\assets\\stls": {"directories": 2, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\robotics\\assets\\stls\\fetch": {"directories": 0, "files": 18, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\robotics\\assets\\stls\\hand": {"directories": 0, "files": 12, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\robotics\\assets\\textures": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 2, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\robotics\\fetch": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\robotics\\hand": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\toy_text": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\envs\\unittest": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\spaces": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\utils": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\vector": {"directories": 1, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\vector\\utils": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\wrappers": {"directories": 1, "files": 19, "python_files": 19, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym\\wrappers\\monitoring": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym-0.21.0-py3.9.egg-info": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 5}, "venv\\Lib\\site-packages\\gymnasium": {"directories": 6, "files": 5, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\envs": {"directories": 6, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\envs\\box2d": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\envs\\classic_control": {"directories": 1, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\envs\\classic_control\\assets": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 1, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\envs\\mujoco": {"directories": 1, "files": 44, "python_files": 44, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\envs\\mujoco\\assets": {"directories": 0, "files": 14, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\envs\\phys2d": {"directories": 1, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\envs\\phys2d\\assets": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 1, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\envs\\tabular": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\envs\\toy_text": {"directories": 2, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\envs\\toy_text\\font": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\envs\\toy_text\\img": {"directories": 0, "files": 81, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 81, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\experimental": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\spaces": {"directories": 0, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\utils": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\vector": {"directories": 1, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\vector\\utils": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\wrappers": {"directories": 1, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium\\wrappers\\vector": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gymnasium-1.1.1.dist-info": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\gym_notices": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\gym_notices-0.0.8.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\h11": {"directories": 0, "files": 12, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\h11-0.16.0.dist-info": {"directories": 1, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\h11-0.16.0.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\httpcore": {"directories": 3, "files": 9, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\httpcore\\_async": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\httpcore\\_backends": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\httpcore\\_sync": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\httpcore-1.0.9.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\httpcore-1.0.9.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\httpx": {"directories": 1, "files": 18, "python_files": 17, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\httpx\\_transports": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\httpx-0.28.1.dist-info": {"directories": 1, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\httpx-0.28.1.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\idna": {"directories": 0, "files": 9, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\idna-3.10.dist-info": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\importlib_metadata": {"directories": 0, "files": 10, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\importlib_metadata-4.13.0.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\importlib_resources": {"directories": 3, "files": 9, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\importlib_resources\\compat": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\importlib_resources\\future": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\importlib_resources\\tests": {"directories": 1, "files": 15, "python_files": 15, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\importlib_resources\\tests\\compat": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\importlib_resources-6.5.2.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\isapi": {"directories": 3, "files": 7, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\isapi\\doc": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\isapi\\samples": {"directories": 0, "files": 6, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\isapi\\test": {"directories": 0, "files": 2, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\jinja2": {"directories": 0, "files": 26, "python_files": 25, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jinja2-3.1.6.dist-info": {"directories": 1, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\jinja2-3.1.6.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\joblib": {"directories": 2, "files": 22, "python_files": 22, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\joblib\\externals": {"directories": 2, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\joblib\\externals\\cloudpickle": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\joblib\\externals\\loky": {"directories": 1, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\joblib\\externals\\loky\\backend": {"directories": 0, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\joblib\\test": {"directories": 1, "files": 25, "python_files": 25, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\joblib\\test\\data": {"directories": 0, "files": 67, "python_files": 2, "config_files": 0, "data_files": 10, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\joblib-1.4.2.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\jsonschema": {"directories": 2, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema\\benchmarks": {"directories": 1, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema\\benchmarks\\issue232": {"directories": 0, "files": 1, "python_files": 0, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema\\tests": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema-4.23.0.dist-info": {"directories": 1, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\jsonschema-4.23.0.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications": {"directories": 2, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications\\schemas": {"directories": 6, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications\\schemas\\draft201909": {"directories": 1, "files": 1, "python_files": 0, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications\\schemas\\draft201909\\vocabularies": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications\\schemas\\draft202012": {"directories": 1, "files": 1, "python_files": 0, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications\\schemas\\draft202012\\vocabularies": {"directories": 0, "files": 9, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications\\schemas\\draft3": {"directories": 0, "files": 1, "python_files": 0, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications\\schemas\\draft4": {"directories": 0, "files": 1, "python_files": 0, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications\\schemas\\draft6": {"directories": 0, "files": 1, "python_files": 0, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications\\schemas\\draft7": {"directories": 0, "files": 1, "python_files": 0, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications\\tests": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications-2025.4.1.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\jsonschema_specifications-2025.4.1.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\kiwisolver": {"directories": 0, "files": 5, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\kiwisolver-1.4.7.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\markupsafe": {"directories": 0, "files": 6, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\matplotlib": {"directories": 10, "files": 137, "python_files": 72, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\axes": {"directories": 0, "files": 8, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\backends": {"directories": 2, "files": 40, "python_files": 35, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend": {"directories": 2, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\css": {"directories": 0, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\backends\\web_backend\\js": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\mpl-data": {"directories": 5, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts": {"directories": 3, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm": {"directories": 0, "files": 46, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts": {"directories": 0, "files": 15, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf": {"directories": 0, "files": 40, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images": {"directories": 0, "files": 51, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 21, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\mpl-data\\plot_directive": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data": {"directories": 1, "files": 14, "python_files": 0, "config_files": 0, "data_files": 3, "image_files": 3, "text_files": 1}, "venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib": {"directories": 0, "files": 28, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\projections": {"directories": 0, "files": 6, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\sphinxext": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\style": {"directories": 0, "files": 3, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\testing": {"directories": 1, "files": 12, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\testing\\jpl_units": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\tests": {"directories": 0, "files": 90, "python_files": 90, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\tri": {"directories": 0, "files": 17, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib\\_api": {"directories": 0, "files": 4, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\matplotlib-3.9.4.dist-info": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\MetaTrader5": {"directories": 0, "files": 2, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\MetaTrader5-5.0.4993.dist-info": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\mpl_toolkits": {"directories": 3, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpl_toolkits\\axes_grid1": {"directories": 1, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpl_toolkits\\axes_grid1\\tests": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpl_toolkits\\axisartist": {"directories": 1, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpl_toolkits\\axisartist\\tests": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d": {"directories": 1, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\tests": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpmath": {"directories": 5, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpmath\\calculus": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpmath\\functions": {"directories": 0, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpmath\\libmp": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpmath\\matrices": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpmath\\tests": {"directories": 0, "files": 36, "python_files": 36, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\mpmath-1.3.0.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\msgpack": {"directories": 0, "files": 5, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\msgpack-1.1.0.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\networkx": {"directories": 8, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms": {"directories": 17, "files": 55, "python_files": 55, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\approximation": {"directories": 1, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests": {"directories": 0, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\assortativity": {"directories": 1, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\assortativity\\tests": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\bipartite": {"directories": 1, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests": {"directories": 0, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\centrality": {"directories": 1, "files": 22, "python_files": 22, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests": {"directories": 0, "files": 21, "python_files": 21, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\coloring": {"directories": 1, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\coloring\\tests": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\community": {"directories": 1, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\community\\tests": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\components": {"directories": 1, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\components\\tests": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\connectivity": {"directories": 1, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\connectivity\\tests": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\flow": {"directories": 1, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\flow\\tests": {"directories": 0, "files": 10, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\isomorphism": {"directories": 1, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\isomorphism\\tests": {"directories": 0, "files": 14, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\link_analysis": {"directories": 1, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\link_analysis\\tests": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\minors": {"directories": 1, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\minors\\tests": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\operators": {"directories": 1, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\operators\\tests": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\shortest_paths": {"directories": 1, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\shortest_paths\\tests": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\tests": {"directories": 0, "files": 56, "python_files": 56, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\traversal": {"directories": 1, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\traversal\\tests": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\tree": {"directories": 1, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\algorithms\\tree\\tests": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\classes": {"directories": 1, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\classes\\tests": {"directories": 0, "files": 17, "python_files": 17, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\drawing": {"directories": 1, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\drawing\\tests": {"directories": 1, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\drawing\\tests\\baseline": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 1, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\generators": {"directories": 1, "files": 31, "python_files": 30, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\generators\\tests": {"directories": 0, "files": 29, "python_files": 29, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\linalg": {"directories": 1, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\linalg\\tests": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\readwrite": {"directories": 2, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\readwrite\\json_graph": {"directories": 1, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\readwrite\\json_graph\\tests": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\readwrite\\tests": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\tests": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\utils": {"directories": 1, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx\\utils\\tests": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\networkx-3.2.1.dist-info": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 3}, "venv\\Lib\\site-packages\\numpy": {"directories": 22, "files": 22, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\char": {"directories": 0, "files": 2, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\compat": {"directories": 1, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\compat\\tests": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\core": {"directories": 0, "files": 20, "python_files": 19, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\distutils": {"directories": 5, "files": 26, "python_files": 25, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\distutils\\checks": {"directories": 0, "files": 44, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\distutils\\command": {"directories": 0, "files": 18, "python_files": 18, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\distutils\\fcompiler": {"directories": 0, "files": 20, "python_files": 20, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\distutils\\mingw": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\distutils\\tests": {"directories": 0, "files": 17, "python_files": 17, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\doc": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py": {"directories": 3, "files": 20, "python_files": 18, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\src": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests": {"directories": 1, "files": 32, "python_files": 32, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src": {"directories": 25, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\abstract_interface": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\array_from_pyobj": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\assumed_shape": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\block_docstring": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\callback": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\cli": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\common": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\crackfortran": {"directories": 0, "files": 19, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\f2cmap": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\isocintrin": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\kind": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\mixed": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\modules": {"directories": 1, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\modules\\gh25337": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\negative_bounds": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\parameter": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\quoted_character": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\regression": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\return_character": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\return_complex": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\return_integer": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\return_logical": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\return_real": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\size": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\string": {"directories": 0, "files": 9, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\tests\\src\\value_attrspec": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\f2py\\_backends": {"directories": 0, "files": 5, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\fft": {"directories": 1, "files": 9, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\fft\\tests": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\lib": {"directories": 1, "files": 56, "python_files": 31, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\lib\\tests": {"directories": 1, "files": 26, "python_files": 26, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\lib\\tests\\data": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\linalg": {"directories": 1, "files": 9, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\linalg\\tests": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\ma": {"directories": 1, "files": 13, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\numpy\\ma\\tests": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\matrixlib": {"directories": 1, "files": 4, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\matrixlib\\tests": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\polynomial": {"directories": 1, "files": 18, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\polynomial\\tests": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\random": {"directories": 3, "files": 34, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\numpy\\random\\lib": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\random\\tests": {"directories": 1, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\random\\tests\\data": {"directories": 0, "files": 14, "python_files": 1, "config_files": 0, "data_files": 10, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\random\\_examples": {"directories": 3, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\random\\_examples\\cffi": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\random\\_examples\\cython": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\random\\_examples\\numba": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\rec": {"directories": 0, "files": 2, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\strings": {"directories": 0, "files": 2, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\testing": {"directories": 2, "files": 4, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\testing\\tests": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\testing\\_private": {"directories": 0, "files": 4, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\tests": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\typing": {"directories": 1, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\typing\\tests": {"directories": 1, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\typing\\tests\\data": {"directories": 4, "files": 1, "python_files": 0, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\typing\\tests\\data\\fail": {"directories": 0, "files": 46, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\typing\\tests\\data\\misc": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\typing\\tests\\data\\pass": {"directories": 0, "files": 32, "python_files": 32, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\typing\\tests\\data\\reveal": {"directories": 0, "files": 55, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_core": {"directories": 3, "files": 61, "python_files": 29, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_core\\include": {"directories": 1, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_core\\include\\numpy": {"directories": 1, "files": 26, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_core\\include\\numpy\\random": {"directories": 0, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\numpy\\_core\\lib": {"directories": 2, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_core\\lib\\npy-pkg-config": {"directories": 0, "files": 2, "python_files": 0, "config_files": 2, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_core\\lib\\pkgconfig": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_core\\tests": {"directories": 2, "files": 66, "python_files": 66, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_core\\tests\\data": {"directories": 0, "files": 24, "python_files": 0, "config_files": 0, "data_files": 21, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\numpy\\_core\\tests\\examples": {"directories": 2, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_core\\tests\\examples\\cython": {"directories": 0, "files": 3, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_core\\tests\\examples\\limited_api": {"directories": 0, "files": 5, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_pyinstaller": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_typing": {"directories": 0, "files": 12, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy\\_utils": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\numpy-2.0.2.dist-info": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\numpy.libs": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\packaging": {"directories": 1, "files": 15, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\packaging\\licenses": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\packaging-25.0.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\packaging-25.0.dist-info\\licenses": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas": {"directories": 13, "files": 7, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\api": {"directories": 5, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\api\\extensions": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\api\\indexers": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\api\\interchange": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\api\\types": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\api\\typing": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\arrays": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\compat": {"directories": 1, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\compat\\numpy": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core": {"directories": 18, "files": 22, "python_files": 22, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\arrays": {"directories": 2, "files": 20, "python_files": 20, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\array_algos": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\computation": {"directories": 0, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\dtypes": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\groupby": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\indexers": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\indexes": {"directories": 0, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\interchange": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\internals": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\methods": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\ops": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\reshape": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\sparse": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\strings": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\tools": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\util": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\window": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\_numba": {"directories": 1, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\errors": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\io": {"directories": 6, "files": 16, "python_files": 16, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\io\\clipboard": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\io\\excel": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\io\\formats": {"directories": 1, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\io\\formats\\templates": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\io\\json": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\io\\parsers": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\io\\sas": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\plotting": {"directories": 1, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests": {"directories": 32, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\api": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\apply": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arithmetic": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays": {"directories": 12, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical": {"directories": 0, "files": 17, "python_files": 17, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays\\numpy_": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays\\period": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays\\string_": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\base": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\computation": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\config": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\construction": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\copy_view": {"directories": 1, "files": 16, "python_files": 16, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\index": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\dtypes": {"directories": 1, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\extension": {"directories": 6, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\extension\\array_with_attr": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\extension\\base": {"directories": 0, "files": 19, "python_files": 19, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\extension\\date": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\extension\\decimal": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\extension\\json": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\extension\\list": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\frame": {"directories": 3, "files": 22, "python_files": 22, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\frame\\constructors": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing": {"directories": 0, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods": {"directories": 0, "files": 82, "python_files": 82, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\generic": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\groupby": {"directories": 3, "files": 25, "python_files": 25, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods": {"directories": 0, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\groupby\\transform": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes": {"directories": 11, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes": {"directories": 1, "files": 18, "python_files": 18, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods": {"directories": 0, "files": 23, "python_files": 23, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi": {"directories": 0, "files": 29, "python_files": 29, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\object": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period": {"directories": 1, "files": 16, "python_files": 16, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas": {"directories": 1, "files": 15, "python_files": 15, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexing": {"directories": 2, "files": 18, "python_files": 18, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexing\\interval": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\interchange": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\internals": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\io": {"directories": 7, "files": 19, "python_files": 19, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\io\\excel": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\io\\formats": {"directories": 1, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\io\\json": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\io\\parser": {"directories": 3, "files": 23, "python_files": 23, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common": {"directories": 0, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables": {"directories": 0, "files": 20, "python_files": 20, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\io\\sas": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\io\\xml": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\libs": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\plotting": {"directories": 1, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\reductions": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\resample": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\reshape": {"directories": 2, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat": {"directories": 0, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\scalar": {"directories": 4, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\scalar\\period": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta": {"directories": 1, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\methods": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp": {"directories": 1, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\series": {"directories": 3, "files": 15, "python_files": 15, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\series\\accessors": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\series\\methods": {"directories": 0, "files": 71, "python_files": 71, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\strings": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\tools": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\tseries": {"directories": 3, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets": {"directories": 0, "files": 22, "python_files": 22, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\tslibs": {"directories": 0, "files": 19, "python_files": 19, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\util": {"directories": 0, "files": 26, "python_files": 26, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\window": {"directories": 1, "files": 19, "python_files": 19, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tests\\window\\moments": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\tseries": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\util": {"directories": 1, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\util\\version": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\_config": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\_libs": {"directories": 2, "files": 77, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\_libs\\tslibs": {"directories": 0, "files": 48, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\_libs\\window": {"directories": 0, "files": 7, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas\\_testing": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pandas-2.2.3.dist-info": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\pandas.libs": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\PIL": {"directories": 0, "files": 112, "python_files": 96, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pillow-11.2.1.dist-info": {"directories": 1, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\pillow-11.2.1.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip": {"directories": 2, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal": {"directories": 11, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\cli": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\commands": {"directories": 0, "files": 16, "python_files": 16, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\distributions": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\index": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\models": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\network": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\operations": {"directories": 2, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\operations\\build": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\operations\\install": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\req": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\resolution": {"directories": 2, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\resolution\\legacy": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\resolution\\resolvelib": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\utils": {"directories": 0, "files": 29, "python_files": 29, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_internal\\vcs": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor": {"directories": 17, "files": 9, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol": {"directories": 1, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\certifi": {"directories": 0, "files": 4, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\chardet": {"directories": 1, "files": 39, "python_files": 39, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\chardet\\cli": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\colorama": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\distlib": {"directories": 1, "files": 17, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\_backport": {"directories": 0, "files": 6, "python_files": 5, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\html5lib": {"directories": 5, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\html5lib\\filters": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\html5lib\\treeadapters": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\html5lib\\treebuilders": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\html5lib\\_trie": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\idna": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\msgpack": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\packaging": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\pep517": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\pkg_resources": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\progress": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\requests": {"directories": 0, "files": 18, "python_files": 18, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib": {"directories": 1, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\resolvelib\\compat": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\toml": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\urllib3": {"directories": 3, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib": {"directories": 1, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages": {"directories": 2, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\ssl_match_hostname": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip\\_vendor\\webencodings": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pip-20.2.3.dist-info": {"directories": 0, "files": 8, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 3}, "venv\\Lib\\site-packages\\pkg_resources": {"directories": 2, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pkg_resources\\extern": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pkg_resources\\_vendor": {"directories": 1, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\protobuf-6.30.2.dist-info": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\psutil": {"directories": 1, "files": 10, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\psutil\\tests": {"directories": 0, "files": 19, "python_files": 19, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\psutil-7.0.0.dist-info": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\pyautogen-0.9.dist-info": {"directories": 1, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pyautogen-0.9.dist-info\\licenses": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\pydantic": {"directories": 5, "files": 36, "python_files": 35, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pydantic\\deprecated": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pydantic\\experimental": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pydantic\\plugin": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pydantic\\v1": {"directories": 0, "files": 27, "python_files": 26, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pydantic\\_internal": {"directories": 0, "files": 29, "python_files": 29, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pydantic-2.11.4.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pydantic-2.11.4.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pydantic_core": {"directories": 0, "files": 5, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pydantic_core-2.33.2.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pydantic_core-2.33.2.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pyparsing": {"directories": 2, "files": 11, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pyparsing\\diagram": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pyparsing\\tools": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pyparsing-3.2.3.dist-info": {"directories": 0, "files": 5, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin": {"directories": 1, "files": 8, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\pythonwin\\pywin": {"directories": 9, "files": 3, "python_files": 1, "config_files": 2, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\debugger": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\Demos": {"directories": 2, "files": 17, "python_files": 17, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\Demos\\app": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\Demos\\ocx": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\dialogs": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\docking": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\framework": {"directories": 1, "files": 17, "python_files": 17, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\framework\\editor": {"directories": 1, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\framework\\editor\\color": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\idle": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\mfc": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\scintilla": {"directories": 0, "files": 12, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pythonwin\\pywin\\tools": {"directories": 0, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\python_dateutil-2.9.0.post0.dist-info": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\python_dotenv-1.1.0.dist-info": {"directories": 1, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\python_dotenv-1.1.0.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz": {"directories": 1, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo": {"directories": 16, "files": 51, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa": {"directories": 0, "files": 54, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\America": {"directories": 4, "files": 143, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina": {"directories": 0, "files": 13, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana": {"directories": 0, "files": 8, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica": {"directories": 0, "files": 12, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia": {"directories": 0, "files": 99, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic": {"directories": 0, "files": 12, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia": {"directories": 0, "files": 23, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil": {"directories": 0, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada": {"directories": 0, "files": 8, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Chile": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc": {"directories": 0, "files": 35, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe": {"directories": 0, "files": 64, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian": {"directories": 0, "files": 11, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific": {"directories": 0, "files": 44, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz\\zoneinfo\\US": {"directories": 0, "files": 12, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\pytz-2025.2.dist-info": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\pywin32-310.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\pywin32_system32": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\PyYAML-6.0.2.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\referencing": {"directories": 1, "files": 9, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\referencing\\tests": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\referencing-0.36.2.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\referencing-0.36.2.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\regex": {"directories": 0, "files": 5, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\regex-2024.11.6.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 2}, "venv\\Lib\\site-packages\\requests": {"directories": 0, "files": 18, "python_files": 18, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\requests-2.32.3.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\rpds": {"directories": 0, "files": 4, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\rpds_py-0.24.0.dist-info": {"directories": 1, "files": 4, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\rpds_py-0.24.0.dist-info\\licenses": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scikit_learn-1.6.1.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy": {"directories": 19, "files": 8, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\cluster": {"directories": 1, "files": 9, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\cluster\\tests": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\constants": {"directories": 1, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\constants\\tests": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\datasets": {"directories": 1, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\datasets\\tests": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\fft": {"directories": 2, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\fft\\tests": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\fft\\_pocketfft": {"directories": 1, "files": 7, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\tests": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\fftpack": {"directories": 1, "files": 11, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\fftpack\\tests": {"directories": 0, "files": 10, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\integrate": {"directories": 2, "files": 27, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\integrate\\tests": {"directories": 0, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\integrate\\_ivp": {"directories": 1, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\integrate\\_ivp\\tests": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\interpolate": {"directories": 1, "files": 34, "python_files": 20, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\interpolate\\tests": {"directories": 1, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\interpolate\\tests\\data": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\io": {"directories": 5, "files": 12, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\io\\arff": {"directories": 1, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\io\\arff\\tests": {"directories": 1, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\io\\arff\\tests\\data": {"directories": 0, "files": 16, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\io\\matlab": {"directories": 1, "files": 22, "python_files": 16, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\io\\matlab\\tests": {"directories": 1, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\io\\matlab\\tests\\data": {"directories": 0, "files": 110, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\scipy\\io\\tests": {"directories": 1, "files": 7, "python_files": 7, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\io\\tests\\data": {"directories": 0, "files": 85, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\io\\_fast_matrix_market": {"directories": 0, "files": 3, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\io\\_harwell_boeing": {"directories": 1, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\io\\_harwell_boeing\\tests": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\linalg": {"directories": 1, "files": 68, "python_files": 36, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\linalg\\tests": {"directories": 1, "files": 24, "python_files": 24, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\linalg\\tests\\data": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\misc": {"directories": 1, "files": 7, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\misc\\tests": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\ndimage": {"directories": 1, "files": 21, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\ndimage\\tests": {"directories": 1, "files": 11, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 1, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\ndimage\\tests\\data": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 3}, "venv\\Lib\\site-packages\\scipy\\odr": {"directories": 1, "files": 8, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\odr\\tests": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\optimize": {"directories": 7, "files": 82, "python_files": 56, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\optimize\\cython_optimize": {"directories": 0, "files": 5, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\optimize\\tests": {"directories": 0, "files": 43, "python_files": 43, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\optimize\\_highs": {"directories": 1, "files": 5, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\optimize\\_highs\\src": {"directories": 1, "files": 0, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\optimize\\_highs\\src\\cython": {"directories": 0, "files": 12, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\optimize\\_lsq": {"directories": 0, "files": 10, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\optimize\\_trlib": {"directories": 0, "files": 3, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr": {"directories": 1, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tests": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\signal": {"directories": 2, "files": 41, "python_files": 27, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\signal\\tests": {"directories": 0, "files": 22, "python_files": 22, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\signal\\windows": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse": {"directories": 3, "files": 37, "python_files": 33, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\csgraph": {"directories": 1, "files": 17, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\tests": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg": {"directories": 5, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\tests": {"directories": 0, "files": 10, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve": {"directories": 1, "files": 5, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\tests": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen": {"directories": 3, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack": {"directories": 1, "files": 5, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\tests": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg": {"directories": 1, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\tests": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\tests": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve": {"directories": 1, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tests": {"directories": 0, "files": 8, "python_files": 8, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack": {"directories": 0, "files": 8, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\tests": {"directories": 1, "files": 16, "python_files": 16, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\sparse\\tests\\data": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\spatial": {"directories": 3, "files": 26, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\spatial\\qhull_src": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\scipy\\spatial\\tests": {"directories": 1, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\spatial\\tests\\data": {"directories": 0, "files": 31, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 30}, "venv\\Lib\\site-packages\\scipy\\spatial\\transform": {"directories": 1, "files": 7, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\spatial\\transform\\tests": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\special": {"directories": 3, "files": 46, "python_files": 19, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\special\\special": {"directories": 1, "files": 9, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\special\\special\\cephes": {"directories": 0, "files": 7, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\special\\tests": {"directories": 1, "files": 51, "python_files": 51, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\special\\tests\\data": {"directories": 0, "files": 4, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\special\\_precompute": {"directories": 0, "files": 13, "python_files": 13, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\stats": {"directories": 5, "files": 74, "python_files": 55, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\stats\\tests": {"directories": 1, "files": 34, "python_files": 34, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\stats\\tests\\data": {"directories": 3, "files": 5, "python_files": 2, "config_files": 1, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\stats\\tests\\data\\levy_stable": {"directories": 0, "files": 3, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\stats\\tests\\data\\nist_anova": {"directories": 0, "files": 11, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\stats\\tests\\data\\nist_linregress": {"directories": 0, "files": 1, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\stats\\_boost": {"directories": 0, "files": 19, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\stats\\_levy_stable": {"directories": 0, "files": 3, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\stats\\_rcont": {"directories": 0, "files": 3, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\stats\\_unuran": {"directories": 0, "files": 4, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\_lib": {"directories": 3, "files": 30, "python_files": 18, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat": {"directories": 4, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy": {"directories": 0, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\_lib\\tests": {"directories": 0, "files": 15, "python_files": 15, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy\\_lib\\_uarray": {"directories": 0, "files": 5, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\scipy-1.13.1.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\scipy.libs": {"directories": 0, "files": 2, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\setuptools": {"directories": 4, "files": 38, "python_files": 30, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\setuptools\\command": {"directories": 0, "files": 26, "python_files": 25, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\setuptools\\extern": {"directories": 0, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\setuptools\\_distutils": {"directories": 1, "files": 28, "python_files": 28, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\setuptools\\_distutils\\command": {"directories": 0, "files": 23, "python_files": 23, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\setuptools\\_vendor": {"directories": 1, "files": 4, "python_files": 4, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\setuptools-49.2.1.dist-info": {"directories": 0, "files": 10, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 3}, "venv\\Lib\\site-packages\\shimmy": {"directories": 1, "files": 9, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\shimmy\\utils": {"directories": 0, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\Shimmy-2.0.0.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\six-1.17.0.dist-info": {"directories": 0, "files": 6, "python_files": 0, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 1}, "venv\\Lib\\site-packages\\sklearn": {"directories": 7, "files": 23, "python_files": 19, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\cluster": {"directories": 2, "files": 33, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\cluster\\tests": {"directories": 0, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan": {"directories": 1, "files": 13, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\tests": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\compose": {"directories": 1, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\compose\\tests": {"directories": 0, "files": 3, "python_files": 3, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\covariance": {"directories": 1, "files": 6, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\covariance\\tests": {"directories": 0, "files": 5, "python_files": 5, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\cross_decomposition": {"directories": 1, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\cross_decomposition\\tests": {"directories": 0, "files": 2, "python_files": 2, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets": {"directories": 4, "files": 18, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\data": {"directories": 0, "files": 10, "python_files": 1, "config_files": 0, "data_files": 6, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\descr": {"directories": 0, "files": 15, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\images": {"directories": 0, "files": 4, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 2, "text_files": 1}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests": {"directories": 1, "files": 14, "python_files": 14, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data": {"directories": 1, "files": 5, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 4}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml": {"directories": 15, "files": 1, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1": {"directories": 0, "files": 5, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119": {"directories": 0, "files": 7, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590": {"directories": 0, "files": 5, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2": {"directories": 0, "files": 7, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292": {"directories": 0, "files": 9, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3": {"directories": 0, "files": 5, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589": {"directories": 0, "files": 7, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675": {"directories": 0, "files": 8, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945": {"directories": 0, "files": 5, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966": {"directories": 0, "files": 7, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074": {"directories": 0, "files": 5, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585": {"directories": 0, "files": 5, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561": {"directories": 0, "files": 7, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61": {"directories": 0, "files": 7, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62": {"directories": 0, "files": 5, "python_files": 1, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\decomposition": {"directories": 1, "files": 19, "python_files": 12, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\decomposition\\tests": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\ensemble": {"directories": 2, "files": 13, "python_files": 9, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\ensemble\\tests": {"directories": 0, "files": 10, "python_files": 10, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting": {"directories": 1, "files": 30, "python_files": 6, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}, "venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests": {"directories": 0, "files": 11, "python_files": 11, "config_files": 0, "data_files": 0, "image_files": 0, "text_files": 0}}}