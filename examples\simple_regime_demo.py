"""
مثال ساده: تست سیستم تشخیص رژیم
Simple Demo: Regime Detection System Test
"""

import os
import sys
import time

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))


def main():
    """اجرای نمایش ساده سیستم"""
    print("🎭 SIMPLE REGIME DETECTION DEMO")
    print("=" * 50)
    
    try:
        # وارد کردن کتابخانه‌ها
        print("📦 Loading libraries...")
        from utils.enhanced_adaptive_plutus import EnhancedAdaptivePlutusSystem
        from tests.test_plutus_models_comprehensive import PlutusModelTester
        
        # ایجاد سیستم
        print("🚀 Creating Enhanced Adaptive System...")
        system = EnhancedAdaptivePlutusSystem("demo_regime.db")
        
        # بارگذاری داده‌ها
        print("📊 Loading test data...")
        model_tester = PlutusModelTester()
        
        symbols = ["EURUSD", "GBPUSD"]
        historical_data = {}
        
        for symbol in symbols:
            data = model_tester.load_real_project_data(symbol, "H1")
            if not data.empty:
                historical_data[symbol] = data
                print(f"  ✅ {symbol}: {len(data)} records")
        
        if not historical_data:
            print("❌ No data available")
            return
        
        # راه‌اندازی تشخیص رژیم
        print("🧠 Initializing regime detection...")
        success = system.initialize_regime_detection(historical_data)
        
        if not success:
            print("❌ Failed to initialize regime detection")
            return
        
        print("✅ Regime detection initialized successfully")
        
        # تست تشخیص رژیم برای هر نماد
        print("\n🎯 Testing regime detection:")
        print("-" * 40)
        
        for symbol in symbols:
            print(f"\n📈 {symbol} Analysis:")
            
                         # دریافت سیگنال
    signal = system.get_regime_aware_signal(symbol, "H1")
             
             if signal.get("error"):
                 print("  ❌ Error: " + str(signal['error']))
                 continue
            
            regime_info = signal.get("regime_info", {})
            recommendation = signal.get("recommendation", {})
            combined_signal = signal.get("combined_signal", {})
            
            # نمایش نتایج
            print(f"  🎭 Regime: {regime_info.get('current_regime', 'unknown')}")
            print(f"  📊 Regime Confidence: {regime_info.get('regime_confidence', 0):.1%}")
            print(f"  🎯 Signal Confidence: {combined_signal.get('confidence', 0):.1%}")
            print(f"  🚦 Action: {recommendation.get('action', 'HOLD')}")
            
            if recommendation.get("reason"):
                print(f"  💭 Reason: {recommendation['reason']}")
            
            if recommendation.get("regime_adjustment"):
                print(f"  ⚖️ Adjustment: {recommendation['regime_adjustment']}")
            
            time.sleep(1)  # وقفه کوتاه
        
        # تست گزارش یادگیری
        print(f"\n📋 Generating learning reports:")
        print("-" * 40)
        
        for symbol in symbols[:1]:  # فقط یکی برای نمایش
            print(f"\n📄 {symbol} Enhanced Learning Report:")
            
            report = system.get_enhanced_learning_report(symbol, "H1")
            
            if report.get("error"):
                print(f"  ❌ Error: {report['error']}")
                continue
            
            # نمایش بخش‌هایی از گزارش
            lines = report.split('\n')
            for line in lines[:15]:  # اول 15 خط
                if line.strip():
                    print(f"  {line}")
            
            if len(lines) > 15:
                print(f"  ... ({len(lines)-15} more lines)")
        
        print(f"\n🎉 Demo completed successfully!")
        print(f"✅ Regime detection working properly")
        print(f"✅ Signal generation functional")
        print(f"✅ Learning reports generated")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 