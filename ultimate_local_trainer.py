"""
🔥 Pearl-3x7B Ultimate Local Trainer
مربی نهایی برای دیتاهای محلی شما

استفاده از دیتاهای واقعی شما در:
- XAUUSD (طلا)
- BTCUSD (بیت کوین)
- EURUSD (یورو دلار)
- و سایر جفت ارزها

ویژگی‌های پیشرفته:
- 50+ اندیکاتور تخصصی
- 20+ استراتژی معاملاتی
- تشخیص الگوهای پنهان
- مقابله با حرکات فیک
- آموزش تخصصی برای "پدر بازار در آوردن" 😄
"""

import os
import sys
import time
import json
import warnings
warnings.filterwarnings('ignore')

import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional

# Install required packages if needed
def install_packages():
    """نصب پکیج‌های مورد نیاز"""
    try:
        import torch
        import sklearn
        print("✅ All required packages available")
    except ImportError as e:
        missing = str(e).split("'")[1]
        print(f"📦 Installing {missing}...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", missing])

install_packages()

import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error

def ultimate_local_training():
    """🔥 آموزش نهایی با دیتاهای محلی شما"""
    print("🔥 PEARL-3X7B ULTIMATE LOCAL TRAINING")
    print("=" * 80)
    print("👑 MISSION: پدر بازار در آوردن با دیتاهای واقعی شما!")
    print("📊 DATA SOURCE: Local data_new directory")
    print()
    
    # Step 1: Load and analyze your data
    print("📋 STEP 1: LOADING YOUR REAL DATA")
    print("=" * 50)
    
    try:
        data_analysis = load_and_analyze_your_data()
        print("✅ Your real data loaded and analyzed!")
        print(f"📊 Found {len(data_analysis['symbols'])} trading symbols")
        print(f"📈 Total data points: {data_analysis['total_records']:,}")
        
    except Exception as e:
        print(f"❌ Data loading error: {e}")
        return {"success": False, "error": str(e)}
    
    # Step 2: Select best symbol for training
    print(f"\n📋 STEP 2: SELECTING BEST SYMBOL FOR TRAINING")
    print("=" * 50)
    
    best_symbol = select_best_symbol(data_analysis)
    print(f"🏆 Selected symbol: {best_symbol['symbol']}")
    print(f"📊 Records: {best_symbol['records']:,}")
    print(f"📅 Date range: {best_symbol['date_range']}")
    
    # Step 3: Enhance data with advanced indicators
    print(f"\n📋 STEP 3: ENHANCING DATA WITH ADVANCED INDICATORS")
    print("=" * 50)
    
    enhanced_data = enhance_with_advanced_indicators(best_symbol['data'])
    print(f"✅ Enhanced with {len(enhanced_data.columns)} total features")
    
    # Step 4: Train market-dominating models
    print(f"\n📋 STEP 4: TRAINING MARKET-DOMINATING MODELS")
    print("=" * 50)
    
    try:
        training_results = {}
        
        # Train Advanced LSTM
        print("\n📈 Training Market-Dominating LSTM...")
        training_results['lstm'] = train_ultimate_lstm(enhanced_data, best_symbol['symbol'])
        
        # Train Advanced DQN
        print("\n🤖 Training Market-Dominating DQN...")
        training_results['dqn'] = train_ultimate_dqn(enhanced_data, best_symbol['symbol'])
        
        print("✅ All market-dominating models trained!")
        
    except Exception as e:
        print(f"❌ Training error: {e}")
        return {"success": False, "error": str(e)}
    
    # Step 5: Package results
    print(f"\n📋 STEP 5: PACKAGING ULTIMATE MODELS")
    print("=" * 50)
    
    try:
        package_results = package_ultimate_models(training_results, best_symbol)
        print("✅ Ultimate models packaged!")
        
    except Exception as e:
        print(f"❌ Packaging error: {e}")
        package_results = {"success": False}
    
    # Final ultimate summary
    print(f"\n👑 ULTIMATE LOCAL TRAINING COMPLETED!")
    print("=" * 80)
    
    successful = sum(1 for r in training_results.values() if r.get('success', False))
    print(f"✅ Successfully trained: {successful}/{len(training_results)} models")
    
    for name, result in training_results.items():
        if result.get('success'):
            print(f"   👑 {name.upper()}: {result.get('performance', 'Trained')}")
    
    if package_results.get('success'):
        print(f"\n📦 MODELS SAVED TO: {package_results['package_dir']}")
        print(f"🎉 Ready to dominate the market with {best_symbol['symbol']}!")
    
    return {"success": True, "results": training_results, "symbol": best_symbol['symbol']}

def load_and_analyze_your_data():
    """بارگذاری و تحلیل دیتاهای شما"""
    print("📊 Analyzing your trading data...")
    
    data_dir = "data_new"
    if not os.path.exists(data_dir):
        raise FileNotFoundError(f"Data directory not found: {data_dir}")
    
    symbols_data = {}
    
    # Scan all symbol directories
    for symbol_dir in os.listdir(data_dir):
        symbol_path = os.path.join(data_dir, symbol_dir)
        
        if os.path.isdir(symbol_path):
            print(f"🔍 Analyzing {symbol_dir}...")
            
            # Look for H1.csv (hourly data)
            h1_file = os.path.join(symbol_path, "H1.csv")
            
            if os.path.exists(h1_file):
                try:
                    df = pd.read_csv(h1_file)
                    
                    if len(df) > 1000:  # Minimum data requirement
                        # Convert datetime
                        df['datetime'] = pd.to_datetime(df['datetime'])
                        df = df.sort_values('datetime')
                        
                        symbols_data[symbol_dir] = {
                            'data': df,
                            'records': len(df),
                            'date_range': f"{df['datetime'].min()} to {df['datetime'].max()}",
                            'columns': list(df.columns),
                            'price_range': f"${df['close'].min():.2f} - ${df['close'].max():.2f}"
                        }
                        
                        print(f"   ✅ {symbol_dir}: {len(df):,} records")
                    else:
                        print(f"   ⚠️ {symbol_dir}: Not enough data ({len(df)} records)")
                        
                except Exception as e:
                    print(f"   ❌ {symbol_dir}: Error loading - {e}")
    
    if not symbols_data:
        raise ValueError("No valid symbol data found")
    
    total_records = sum(data['records'] for data in symbols_data.values())
    
    return {
        'symbols': list(symbols_data.keys()),
        'symbols_data': symbols_data,
        'total_records': total_records
    }

def select_best_symbol(data_analysis):
    """انتخاب بهترین نماد برای آموزش"""
    print("🏆 Selecting best symbol for training...")
    
    symbols_data = data_analysis['symbols_data']
    
    # Score each symbol
    symbol_scores = {}
    
    for symbol, data_info in symbols_data.items():
        score = 0
        
        # More data is better
        score += min(data_info['records'] / 10000, 2.0)  # Max 2 points
        
        # Prefer major symbols
        major_symbols = ['XAUUSD', 'BTCUSD', 'EURUSD', 'GBPUSD', 'USDJPY']
        if symbol in major_symbols:
            score += 1.0
        
        # Check data quality
        df = data_info['data']
        
        # No missing values in OHLC
        if not df[['open', 'high', 'low', 'close']].isnull().any().any():
            score += 0.5
        
        # Good volume data
        if 'volume' in df.columns and df['volume'].sum() > 0:
            score += 0.5
        
        symbol_scores[symbol] = score
        print(f"   📊 {symbol}: Score {score:.2f}")
    
    # Select best symbol
    best_symbol = max(symbol_scores.items(), key=lambda x: x[1])[0]
    
    return {
        'symbol': best_symbol,
        'data': symbols_data[best_symbol]['data'],
        'records': symbols_data[best_symbol]['records'],
        'date_range': symbols_data[best_symbol]['date_range'],
        'score': symbol_scores[best_symbol]
    }

def enhance_with_advanced_indicators(df):
    """اضافه کردن 50+ اندیکاتور پیشرفته"""
    print("🔧 Adding 50+ advanced indicators...")
    
    enhanced_df = df.copy()
    
    # Basic price features
    enhanced_df['hl_pct'] = (enhanced_df['high'] - enhanced_df['low']) / enhanced_df['close']
    enhanced_df['oc_pct'] = (enhanced_df['close'] - enhanced_df['open']) / enhanced_df['open']
    enhanced_df['body_size'] = abs(enhanced_df['close'] - enhanced_df['open'])
    enhanced_df['upper_shadow'] = enhanced_df['high'] - enhanced_df[['open', 'close']].max(axis=1)
    enhanced_df['lower_shadow'] = enhanced_df[['open', 'close']].min(axis=1) - enhanced_df['low']
    
    # Moving averages
    for period in [5, 10, 20, 50, 100, 200]:
        enhanced_df[f'sma_{period}'] = enhanced_df['close'].rolling(period).mean()
        enhanced_df[f'ema_{period}'] = enhanced_df['close'].ewm(span=period).mean()
    
    # Price ratios
    enhanced_df['price_sma20_ratio'] = enhanced_df['close'] / enhanced_df['sma_20']
    enhanced_df['sma20_sma50_ratio'] = enhanced_df['sma_20'] / enhanced_df['sma_50']
    enhanced_df['ema12_ema26_ratio'] = enhanced_df['ema_12'] / enhanced_df['ema_26']
    
    # Returns and volatility
    enhanced_df['returns'] = enhanced_df['close'].pct_change()
    enhanced_df['log_returns'] = np.log(enhanced_df['close'] / enhanced_df['close'].shift(1))
    enhanced_df['volatility_10'] = enhanced_df['returns'].rolling(10).std()
    enhanced_df['volatility_20'] = enhanced_df['returns'].rolling(20).std()
    enhanced_df['volatility_50'] = enhanced_df['returns'].rolling(50).std()
    
    # RSI
    delta = enhanced_df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    enhanced_df['rsi'] = 100 - (100 / (1 + rs))
    enhanced_df['rsi_sma'] = enhanced_df['rsi'].rolling(10).mean()
    
    # MACD
    ema12 = enhanced_df['close'].ewm(span=12).mean()
    ema26 = enhanced_df['close'].ewm(span=26).mean()
    enhanced_df['macd'] = ema12 - ema26
    enhanced_df['macd_signal'] = enhanced_df['macd'].ewm(span=9).mean()
    enhanced_df['macd_histogram'] = enhanced_df['macd'] - enhanced_df['macd_signal']
    
    # Bollinger Bands
    sma20 = enhanced_df['close'].rolling(20).mean()
    std20 = enhanced_df['close'].rolling(20).std()
    enhanced_df['bb_upper'] = sma20 + (std20 * 2)
    enhanced_df['bb_lower'] = sma20 - (std20 * 2)
    enhanced_df['bb_width'] = enhanced_df['bb_upper'] - enhanced_df['bb_lower']
    enhanced_df['bb_position'] = (enhanced_df['close'] - enhanced_df['bb_lower']) / enhanced_df['bb_width']
    
    # Volume indicators
    if 'volume' in enhanced_df.columns:
        enhanced_df['volume_sma'] = enhanced_df['volume'].rolling(20).mean()
        enhanced_df['volume_ratio'] = enhanced_df['volume'] / enhanced_df['volume_sma']
        enhanced_df['price_volume'] = enhanced_df['returns'] * enhanced_df['volume']
        enhanced_df['volume_roc'] = enhanced_df['volume'].pct_change(10)
    
    # ATR
    high_low = enhanced_df['high'] - enhanced_df['low']
    high_close = np.abs(enhanced_df['high'] - enhanced_df['close'].shift())
    low_close = np.abs(enhanced_df['low'] - enhanced_df['close'].shift())
    true_range = np.maximum(high_low, np.maximum(high_close, low_close))
    enhanced_df['atr'] = true_range.rolling(14).mean()
    enhanced_df['atr_ratio'] = enhanced_df['atr'] / enhanced_df['close']
    
    # Stochastic
    lowest_low = enhanced_df['low'].rolling(14).min()
    highest_high = enhanced_df['high'].rolling(14).max()
    enhanced_df['stoch_k'] = 100 * (enhanced_df['close'] - lowest_low) / (highest_high - lowest_low)
    enhanced_df['stoch_d'] = enhanced_df['stoch_k'].rolling(3).mean()
    
    # Williams %R
    enhanced_df['williams_r'] = -100 * (highest_high - enhanced_df['close']) / (highest_high - lowest_low)
    
    # ROC (Rate of Change)
    for period in [5, 10, 20]:
        enhanced_df[f'roc_{period}'] = enhanced_df['close'].pct_change(period) * 100
    
    # Momentum
    for period in [5, 10, 20]:
        enhanced_df[f'momentum_{period}'] = enhanced_df['close'] / enhanced_df['close'].shift(period)
    
    # Candlestick patterns
    enhanced_df['doji'] = (enhanced_df['body_size'] / (enhanced_df['high'] - enhanced_df['low']) < 0.1).astype(int)
    enhanced_df['hammer'] = ((enhanced_df['lower_shadow'] > 2 * enhanced_df['body_size']) & 
                            (enhanced_df['upper_shadow'] < enhanced_df['body_size'])).astype(int)
    enhanced_df['shooting_star'] = ((enhanced_df['upper_shadow'] > 2 * enhanced_df['body_size']) & 
                                   (enhanced_df['lower_shadow'] < enhanced_df['body_size'])).astype(int)
    
    # Market structure
    enhanced_df['local_high'] = ((enhanced_df['close'] > enhanced_df['close'].shift(1)) & 
                                (enhanced_df['close'] > enhanced_df['close'].shift(-1))).astype(int)
    enhanced_df['local_low'] = ((enhanced_df['close'] < enhanced_df['close'].shift(1)) & 
                               (enhanced_df['close'] < enhanced_df['close'].shift(-1))).astype(int)
    
    # Time-based features
    enhanced_df['hour'] = enhanced_df['datetime'].dt.hour
    enhanced_df['day_of_week'] = enhanced_df['datetime'].dt.dayofweek
    enhanced_df['is_weekend'] = (enhanced_df['day_of_week'] >= 5).astype(int)
    enhanced_df['is_asian_session'] = ((enhanced_df['hour'] >= 0) & (enhanced_df['hour'] < 8)).astype(int)
    enhanced_df['is_european_session'] = ((enhanced_df['hour'] >= 8) & (enhanced_df['hour'] < 16)).astype(int)
    enhanced_df['is_us_session'] = ((enhanced_df['hour'] >= 16) & (enhanced_df['hour'] < 24)).astype(int)
    
    # Advanced patterns
    enhanced_df['gap'] = enhanced_df['open'] - enhanced_df['close'].shift(1)
    enhanced_df['gap_pct'] = enhanced_df['gap'] / enhanced_df['close'].shift(1)
    enhanced_df['sudden_move'] = (abs(enhanced_df['returns']) > enhanced_df['volatility_20'] * 2).astype(int)
    
    # Trend indicators
    enhanced_df['trend_5'] = (enhanced_df['close'] > enhanced_df['sma_5']).astype(int)
    enhanced_df['trend_20'] = (enhanced_df['close'] > enhanced_df['sma_20']).astype(int)
    enhanced_df['trend_50'] = (enhanced_df['close'] > enhanced_df['sma_50']).astype(int)
    
    print(f"✅ Added {len(enhanced_df.columns) - len(df.columns)} advanced indicators")
    
    return enhanced_df
