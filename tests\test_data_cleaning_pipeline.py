
import sys
import os
import pandas as pd
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from utils.data_cleaning_pipeline import advanced_data_cleaning

def test_advanced_data_cleaning_on_eurusd_h1():
    # داده واقعی EURUSD H1
    df = pd.read_csv(r'data/EURUSD/H1.csv')
    # اجرای cleaning pipeline با ست‌های پایه و پیشرفته
    df_clean = advanced_data_cleaning(
        df,
        cleaning_sets=['basic', 'advanced', 'smart'],
        dropna_cols=['close'],
        fillna_value=0.0,
        duplicate_subset=['datetime'],
        filter_cols=['close'],
        min_val=1.10,
        max_val=1.20,
        outlier_cols=['close'],
        outlier_z=3.0,
        spike_cols=['close'],
        spike_threshold=5.0,
        contextual_outlier_cols=['close'],
        contextual_window=10,
        anomaly_cols=['close'],
        anomaly_method='isolation_forest',
        adaptive_vol_cols=['close'],
        adaptive_z=3.0
    )
    # تست: تعداد ردیف‌ها باید کمتر یا مساوی ورودی باشد و هیچ NaN در close نباشد
    assert len(df_clean) <= len(df)
    assert df_clean['close'].isna().sum() == 0
    print('Test passed: advanced_data_cleaning on EURUSD H1')

if __name__ == '__main__':
    test_advanced_data_cleaning_on_eurusd_h1()
