"""
مثال کاربردی عملی: سیستم معاملاتی با تشخیص رژیم بازار
Practical Example: Trading System with Market Regime Detection
"""

import os
import sys
import time
import json
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.enhanced_adaptive_plutus import EnhancedAdaptivePlutusSystem
from utils.market_regime_detector import MarketRegimeDetector
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RegimeBasedTradingSystem:
    """سیستم معاملاتی مبتنی بر رژیم بازار"""
    
    def __init__(self):
        self.system = EnhancedAdaptivePlutusSystem("regime_trading_system.db")
        self.portfolio = {
            "balance": 10000.0,
            "positions": {},
            "trade_history": [],
            "performance_metrics": {}
        }
        
        # تنظیمات ریسک
        self.max_risk_per_trade = 0.02  # 2% ریسک هر معامله
        self.max_portfolio_risk = 0.20  # 20% ریسک کل پرتفوی
        
        logger.info("Regime-Based Trading System initialized")
    
    def initialize_system(self, symbols: list):
        """راه‌اندازی سیستم"""
        try:
            print("🚀 Initializing Regime-Based Trading System")
            print("=" * 60)
            
            # بارگذاری داده‌های تاریخی
            historical_data = {}
            from tests.test_plutus_models_comprehensive import PlutusModelTester
            model_tester = PlutusModelTester()
            
            for symbol in symbols:
                data = model_tester.load_real_project_data(symbol, "H1")
                if not data.empty:
                    historical_data[symbol] = data
                    print(f"✅ Loaded {len(data)} records for {symbol}")
            
            if not historical_data:
                print("❌ No historical data available")
                return False
            
            # راه‌اندازی تشخیص رژیم
            success = self.system.initialize_regime_detection(historical_data)
            
            if success:
                print("✅ Regime detection system initialized")
                return True
            else:
                print("❌ Failed to initialize regime detection")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing system: {str(e)}")
            return False
    
    def analyze_trading_opportunity(self, symbol: str) -> dict:
        """تحلیل فرصت معاملاتی"""
        try:
            # دریافت سیگنال آگاه از رژیم
            signal = self.system.get_regime_aware_signal(symbol, "H1")
            
            if signal.get("error"):
                return {"error": signal["error"]}
            
            # استخراج اطلاعات کلیدی
            regime_info = signal.get("regime_info", {})
            recommendation = signal.get("recommendation", {})
            combined_signal = signal.get("combined_signal", {})
            
            # محاسبه اندازه موقعیت بر اساس رژیم
            position_size = self.calculate_position_size(
                regime_info.get("current_regime"),
                combined_signal.get("confidence", 0),
                recommendation.get("position_size_multiplier", 1.0)
            )
            
            # تعیین سطوح stop loss و take profit
            stop_loss, take_profit = self.calculate_risk_levels(
                regime_info.get("current_regime"),
                recommendation.get("action", "HOLD")
            )
            
            opportunity = {
                "symbol": symbol,
                "timestamp": datetime.now(),
                "regime": regime_info.get("current_regime", "unknown"),
                "regime_confidence": regime_info.get("regime_confidence", 0),
                "signal_confidence": combined_signal.get("confidence", 0),
                "action": recommendation.get("action", "HOLD"),
                "reason": recommendation.get("reason", ""),
                "position_size": position_size,
                "stop_loss_pct": stop_loss,
                "take_profit_pct": take_profit,
                "risk_level": self.assess_risk_level(regime_info.get("current_regime")),
                "regime_adjustment": recommendation.get("regime_adjustment", "")
            }
            
            return opportunity
            
        except Exception as e:
            logger.error(f"Error analyzing opportunity for {symbol}: {str(e)}")
            return {"error": str(e)}
    
    def calculate_position_size(self, regime: str, confidence: float, regime_multiplier: float) -> float:
        """محاسبه اندازه موقعیت"""
        try:
            # اندازه پایه بر اساس ریسک
            base_size = self.portfolio["balance"] * self.max_risk_per_trade
            
            # تنظیم بر اساس رژیم
            regime_adjustments = {
                "bull_market": 1.2,
                "bear_market": 0.8,
                "sideways_market": 1.0,
                "high_volatility": 0.6,
                "low_volatility": 1.1
            }
            
            regime_factor = regime_adjustments.get(regime, 1.0)
            
            # تنظیم بر اساس اعتماد
            confidence_factor = min(confidence / 0.7, 1.2)  # حداکثر 120%
            
            # محاسبه نهایی
            final_size = base_size * regime_factor * confidence_factor * regime_multiplier
            
            # محدودیت حداکثر
            max_position = self.portfolio["balance"] * 0.25  # حداکثر 25% بالانس
            
            return min(final_size, max_position)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            return self.portfolio["balance"] * 0.01  # 1% پیش‌فرض
    
    def calculate_risk_levels(self, regime: str, action: str) -> tuple:
        """محاسبه سطوح stop loss و take profit"""
        try:
            # تنظیمات پیش‌فرض
            base_stop_loss = 0.01  # 1%
            base_take_profit = 0.02  # 2%
            
            # تنظیم بر اساس رژیم
            if regime == "high_volatility":
                stop_loss = base_stop_loss * 1.5  # 1.5%
                take_profit = base_take_profit * 2.0  # 4%
            elif regime == "low_volatility":
                stop_loss = base_stop_loss * 0.7  # 0.7%
                take_profit = base_take_profit * 0.8  # 1.6%
            elif regime == "bull_market":
                stop_loss = base_stop_loss * 0.8  # 0.8%
                take_profit = base_take_profit * 1.5  # 3%
            elif regime == "bear_market":
                stop_loss = base_stop_loss * 1.2  # 1.2%
                take_profit = base_take_profit * 1.3  # 2.6%
            else:  # sideways_market
                stop_loss = base_stop_loss  # 1%
                take_profit = base_take_profit  # 2%
            
            return stop_loss, take_profit
            
        except Exception as e:
            logger.error(f"Error calculating risk levels: {str(e)}")
            return 0.01, 0.02  # پیش‌فرض
    
    def assess_risk_level(self, regime: str) -> str:
        """ارزیابی سطح ریسک"""
        risk_levels = {
            "bull_market": "medium",
            "bear_market": "high",
            "sideways_market": "low",
            "high_volatility": "very_high",
            "low_volatility": "very_low"
        }
        
        return risk_levels.get(regime, "medium")
    
    def execute_trade_simulation(self, opportunity: dict) -> dict:
        """شبیه‌سازی اجرای معامله"""
        try:
            if opportunity.get("error") or opportunity["action"] == "HOLD":
                return {"status": "no_trade", "reason": "Hold signal or error"}
            
            # اطلاعات معامله
            symbol = opportunity["symbol"]
            action = opportunity["action"]
            position_size = opportunity["position_size"]
            
            # شبیه‌سازی قیمت ورود
            entry_price = 1.0 + np.random.normal(0, 0.001)  # قیمت تصادفی
            
            # محاسبه P&L شبیه‌سازی شده
            if action == "BUY":
                # شبیه‌سازی حرکت قیمت
                price_movement = np.random.normal(0.0005, 0.01)  # حرکت تصادفی
                exit_price = entry_price * (1 + price_movement)
                pnl = (exit_price - entry_price) * position_size
            else:  # SELL
                price_movement = np.random.normal(-0.0005, 0.01)
                exit_price = entry_price * (1 + price_movement)
                pnl = (entry_price - exit_price) * position_size
            
            # بررسی stop loss و take profit
            if action == "BUY":
                stop_loss_price = entry_price * (1 - opportunity["stop_loss_pct"])
                take_profit_price = entry_price * (1 + opportunity["take_profit_pct"])
                
                if exit_price <= stop_loss_price:
                    pnl = -position_size * opportunity["stop_loss_pct"]
                    exit_reason = "stop_loss"
                elif exit_price >= take_profit_price:
                    pnl = position_size * opportunity["take_profit_pct"]
                    exit_reason = "take_profit"
                else:
                    exit_reason = "time_exit"
            else:  # SELL
                stop_loss_price = entry_price * (1 + opportunity["stop_loss_pct"])
                take_profit_price = entry_price * (1 - opportunity["take_profit_pct"])
                
                if exit_price >= stop_loss_price:
                    pnl = -position_size * opportunity["stop_loss_pct"]
                    exit_reason = "stop_loss"
                elif exit_price <= take_profit_price:
                    pnl = position_size * opportunity["take_profit_pct"]
                    exit_reason = "take_profit"
                else:
                    exit_reason = "time_exit"
            
            # ثبت معامله
            trade = {
                "timestamp": datetime.now(),
                "symbol": symbol,
                "action": action,
                "entry_price": entry_price,
                "exit_price": exit_price,
                "position_size": position_size,
                "pnl": pnl,
                "exit_reason": exit_reason,
                "regime": opportunity["regime"],
                "regime_confidence": opportunity["regime_confidence"],
                "signal_confidence": opportunity["signal_confidence"]
            }
            
            # به‌روزرسانی پرتفوی
            self.portfolio["balance"] += pnl
            self.portfolio["trade_history"].append(trade)
            
            return {"status": "executed", "trade": trade}
            
        except Exception as e:
            logger.error(f"Error executing trade simulation: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    def analyze_portfolio_performance(self) -> dict:
        """تحلیل عملکرد پرتفوی"""
        try:
            trades = self.portfolio["trade_history"]
            
            if not trades:
                return {"error": "No trades to analyze"}
            
            # محاسبه معیارهای عملکرد
            pnls = [trade["pnl"] for trade in trades]
            total_pnl = sum(pnls)
            
            winning_trades = [pnl for pnl in pnls if pnl > 0]
            losing_trades = [pnl for pnl in pnls if pnl < 0]
            
            win_rate = len(winning_trades) / len(trades)
            avg_win = np.mean(winning_trades) if winning_trades else 0
            avg_loss = np.mean(losing_trades) if losing_trades else 0
            
            profit_factor = (sum(winning_trades) / abs(sum(losing_trades))) if losing_trades else float('inf')
            
            # تحلیل بر اساس رژیم
            regime_performance = {}
            for trade in trades:
                regime = trade["regime"]
                if regime not in regime_performance:
                    regime_performance[regime] = {"trades": [], "total_pnl": 0}
                
                regime_performance[regime]["trades"].append(trade)
                regime_performance[regime]["total_pnl"] += trade["pnl"]
            
            # محاسبه آمار هر رژیم
            for regime, data in regime_performance.items():
                regime_trades = data["trades"]
                regime_pnls = [t["pnl"] for t in regime_trades]
                regime_wins = [p for p in regime_pnls if p > 0]
                
                data.update({
                    "trade_count": len(regime_trades),
                    "win_rate": len(regime_wins) / len(regime_trades) if regime_trades else 0,
                    "avg_pnl": np.mean(regime_pnls) if regime_pnls else 0
                })
            
            return {
                "total_trades": len(trades),
                "total_pnl": total_pnl,
                "current_balance": self.portfolio["balance"],
                "return_pct": (total_pnl / 10000) * 100,
                "win_rate": win_rate,
                "avg_win": avg_win,
                "avg_loss": avg_loss,
                "profit_factor": profit_factor,
                "regime_performance": regime_performance,
                "best_regime": max(regime_performance.keys(), 
                                 key=lambda x: regime_performance[x]["total_pnl"]) if regime_performance else None,
                "worst_regime": min(regime_performance.keys(), 
                                  key=lambda x: regime_performance[x]["total_pnl"]) if regime_performance else None
            }
            
        except Exception as e:
            logger.error(f"Error analyzing portfolio performance: {str(e)}")
            return {"error": str(e)}
    
    def run_trading_simulation(self, symbols: list, iterations: int = 20):
        """اجرای شبیه‌سازی معاملات"""
        try:
            print(f"\n🎯 Running Trading Simulation")
            print(f"Symbols: {symbols}")
            print(f"Iterations: {iterations}")
            print(f"Initial Balance: ${self.portfolio['balance']:,.2f}")
            print("-" * 60)
            
            for i in range(iterations):
                print(f"\n📊 Iteration {i+1}/{iterations}")
                
                for symbol in symbols:
                    # تحلیل فرصت
                    opportunity = self.analyze_trading_opportunity(symbol)
                    
                    if not opportunity.get("error"):
                        print(f"  {symbol}:")
                        print(f"    Regime: {opportunity['regime']} ({opportunity['regime_confidence']:.1%})")
                        print(f"    Action: {opportunity['action']}")
                        print(f"    Signal Confidence: {opportunity['signal_confidence']:.1%}")
                        print(f"    Risk Level: {opportunity['risk_level']}")
                        
                        if opportunity.get("regime_adjustment"):
                            print(f"    Regime Adjustment: {opportunity['regime_adjustment']}")
                        
                        # اجرای معامله
                        trade_result = self.execute_trade_simulation(opportunity)
                        
                        if trade_result["status"] == "executed":
                            trade = trade_result["trade"]
                            print(f"    Trade Executed: {trade['action']} ${trade['position_size']:.2f}")
                            print(f"    P&L: ${trade['pnl']:.2f} ({trade['exit_reason']})")
                        elif trade_result["status"] == "no_trade":
                            print(f"    No Trade: {trade_result['reason']}")
                    else:
                        print(f"  {symbol}: Error - {opportunity['error']}")
                
                # نمایش بالانس فعلی
                print(f"  Current Balance: ${self.portfolio['balance']:.2f}")
                
                # وقفه کوتاه
                time.sleep(0.5)
            
            # تحلیل عملکرد نهایی
            print(f"\n📈 Final Performance Analysis")
            print("=" * 60)
            
            performance = self.analyze_portfolio_performance()
            
            if not performance.get("error"):
                print(f"Total Trades: {performance['total_trades']}")
                print(f"Final Balance: ${performance['current_balance']:.2f}")
                print(f"Total P&L: ${performance['total_pnl']:.2f}")
                print(f"Return: {performance['return_pct']:.2f}%")
                print(f"Win Rate: {performance['win_rate']:.1%}")
                print(f"Profit Factor: {performance['profit_factor']:.2f}")
                
                if performance.get("best_regime"):
                    print(f"Best Regime: {performance['best_regime']}")
                if performance.get("worst_regime"):
                    print(f"Worst Regime: {performance['worst_regime']}")
                
                # عملکرد هر رژیم
                print(f"\n📊 Performance by Regime:")
                for regime, data in performance["regime_performance"].items():
                    print(f"  {regime}:")
                    print(f"    Trades: {data['trade_count']}")
                    print(f"    Win Rate: {data['win_rate']:.1%}")
                    print(f"    Avg P&L: ${data['avg_pnl']:.2f}")
                    print(f"    Total P&L: ${data['total_pnl']:.2f}")
            
            return performance
            
        except Exception as e:
            logger.error(f"Error in trading simulation: {str(e)}")
            return {"error": str(e)}

def main():
    """اجرای مثال کاربردی"""
    print("💼 PRACTICAL REGIME-BASED TRADING SYSTEM")
    print("=" * 70)
    
    # ایجاد سیستم معاملاتی
    trading_system = RegimeBasedTradingSystem()
    
    # نمادهای تست
    symbols = ["EURUSD", "GBPUSD"]
    
    # راه‌اندازی سیستم
    success = trading_system.initialize_system(symbols)
    
    if success:
        # اجرای شبیه‌سازی معاملات
        performance = trading_system.run_trading_simulation(symbols, iterations=15)
        
        # ذخیره نتایج
        if not performance.get("error"):
            results_file = Path(__file__).parent.parent / "logs" / "regime_trading_results.json"
            results_file.parent.mkdir(exist_ok=True)
            
            results = {
                "simulation_date": datetime.now().isoformat(),
                "symbols": symbols,
                "performance": performance,
                "trade_history": trading_system.portfolio["trade_history"][-10:]  # آخرین 10 معامله
            }
            
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            print(f"\n💾 Results saved to: {results_file}")
            
            # نمایش خلاصه
            print(f"\n🎉 SIMULATION COMPLETED SUCCESSFULLY!")
            print(f"✅ System effectively used regime detection")
            print(f"✅ Risk management applied based on market conditions")
            print(f"✅ Adaptive position sizing working")
            print(f"✅ Performance tracking functional")
        
    else:
        print("❌ Failed to initialize trading system")

if __name__ == "__main__":
    main() 