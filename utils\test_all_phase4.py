#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Phase 4 Complete Test Suite (Items 16-21)
"""

import os
import sys
import time
import asyncio

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def run_all_tests():
    """اجرای تمام تست‌های Phase 4"""
    print("🚀 Starting Phase 4 Tests (Items 16-21)...")
    print("=" * 60)
    
    test_results = {}
    
    # Test 16: Database Transaction Management
    print("\n📊 Item 16: Database Transaction Management")
    print("-" * 40)
    try:
        from utils.simple_db_test import test_database_transaction_manager
        result = test_database_transaction_manager()
        test_results["16_database_transaction"] = result
        print(f"✅ Database Transaction Management: {'PASSED' if result else 'FAILED'}")
    except Exception as e:
        test_results["16_database_transaction"] = False
        print(f"❌ Database Transaction Management: FAILED - {e}")
    
    # Test 17: Order Management System
    print("\n💼 Item 17: Order Management System")
    print("-" * 40)
    try:
        from utils.advanced_order_test import test_advanced_order_management
        result = test_advanced_order_management()
        test_results["17_order_management"] = result
        print(f"✅ Order Management System: {'PASSED' if result else 'FAILED'}")
    except Exception as e:
        test_results["17_order_management"] = False
        print(f"❌ Order Management System: FAILED - {e}")
    
    # Test 18: Multi-Exchange Integration
    print("\n🌐 Item 18: Multi-Exchange Integration")
    print("-" * 40)
    try:
        from utils.multi_exchange_test import test_multi_exchange_system
        result = await test_multi_exchange_system()
        test_results["18_multi_exchange"] = result
        print(f"✅ Multi-Exchange Integration: {'PASSED' if result else 'FAILED'}")
    except Exception as e:
        test_results["18_multi_exchange"] = False
        print(f"❌ Multi-Exchange Integration: FAILED - {e}")
    
    # Test 19: Real-time Data Processing
    print("\n⚡ Item 19: Real-time Data Processing")
    print("-" * 40)
    try:
        from utils.realtime_data_test import test_realtime_data_processing
        result = await test_realtime_data_processing()
        test_results["19_realtime_data"] = result
        print(f"✅ Real-time Data Processing: {'PASSED' if result else 'FAILED'}")
    except Exception as e:
        test_results["19_realtime_data"] = False
        print(f"❌ Real-time Data Processing: FAILED - {e}")
    
    # Test 20: Circuit Breaker System
    print("\n🔌 Item 20: Circuit Breaker System")
    print("-" * 40)
    try:
        from utils.circuit_breaker_test import test_circuit_breaker_system, test_async_circuit
        result1 = test_circuit_breaker_system()
        result2 = await test_async_circuit()
        result = result1 and result2
        test_results["20_circuit_breaker"] = result
        print(f"✅ Circuit Breaker System: {'PASSED' if result else 'FAILED'}")
    except Exception as e:
        test_results["20_circuit_breaker"] = False
        print(f"❌ Circuit Breaker System: FAILED - {e}")
    
    # Test 21: Model Versioning & MLOps
    print("\n📦 Item 21: Model Versioning & MLOps")
    print("-" * 40)
    try:
        from utils.mlops_versioning_test import test_mlops_versioning
        result = test_mlops_versioning()
        test_results["21_mlops_versioning"] = result
        print(f"✅ Model Versioning & MLOps: {'PASSED' if result else 'FAILED'}")
    except Exception as e:
        test_results["21_mlops_versioning"] = False
        print(f"❌ Model Versioning & MLOps: FAILED - {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 PHASE 4 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for v in test_results.values() if v)
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print("-" * 60)
    print(f"Total: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL PHASE 4 TESTS PASSED! 🎉")
        print("✅ Items 16-21 are fully operational and ready for production!")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the implementation.")
    
    return passed == total

# Test integration with main system
def test_main_integration():
    """بررسی integration با سیستم اصلی"""
    print("\n🔗 Testing Main System Integration...")
    print("=" * 60)
    
    try:
        # Test core imports
        print("1️⃣ Testing core imports...")
        from core import (
            DATABASE_TRANSACTION_MANAGER_AVAILABLE,
            ADVANCED_ORDER_MANAGEMENT_AVAILABLE,
            MULTI_EXCHANGE_INTEGRATION_AVAILABLE,
            REALTIME_DATA_PROCESSING_AVAILABLE,
            CIRCUIT_BREAKER_SYSTEM_AVAILABLE,
            MLOPS_VERSIONING_AVAILABLE
        )
        
        availability = {
            "Database Transaction": DATABASE_TRANSACTION_MANAGER_AVAILABLE,
            "Order Management": ADVANCED_ORDER_MANAGEMENT_AVAILABLE,
            "Multi-Exchange": MULTI_EXCHANGE_INTEGRATION_AVAILABLE,
            "Real-time Data": REALTIME_DATA_PROCESSING_AVAILABLE,
            "Circuit Breaker": CIRCUIT_BREAKER_SYSTEM_AVAILABLE,
            "MLOps Versioning": MLOPS_VERSIONING_AVAILABLE
        }
        
        print("\n📦 Component Availability:")
        for component, available in availability.items():
            status = "✅ Available" if available else "❌ Not Available"
            print(f"   {component}: {status}")
        
        # Test main_new.py integration
        print("\n2️⃣ Testing main_new.py integration...")
        from main_new import TradingSystemManager
        
        manager = TradingSystemManager()
        components_initialized = manager.initialize_advanced_core_components()
        
        if components_initialized:
            print("✅ Advanced components initialized in main system")
        else:
            print("❌ Failed to initialize advanced components")
        
        # Check if all components are available
        all_available = all(availability.values())
        
        if all_available and components_initialized:
            print("\n✅ FULL INTEGRATION SUCCESS!")
            print("All Phase 4 components are integrated and operational in the main system.")
            return True
        else:
            print("\n⚠️  PARTIAL INTEGRATION")
            print("Some components are not fully integrated.")
            return False
            
    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Run all tests
    all_tests_passed = asyncio.run(run_all_tests())
    
    # Test main integration
    integration_success = test_main_integration()
    
    # Final verdict
    print("\n" + "=" * 60)
    print("🏁 FINAL VERDICT")
    print("=" * 60)
    
    if all_tests_passed and integration_success:
        print("✅ Phase 4 (Items 16-21) is COMPLETE and FULLY INTEGRATED!")
        print("🚀 The trading system is ready with all advanced features!")
    else:
        print("❌ Phase 4 implementation needs attention.")
        print("Please review the failed tests and fix the issues.") 