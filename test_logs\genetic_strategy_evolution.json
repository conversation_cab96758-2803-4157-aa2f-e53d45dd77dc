{"command": "python utils/genetic_strategy_evolution.py", "timestamp": "2025-07-08T05:52:23.948530", "execution_time": 1.5786266326904297, "return_code": 1, "stdout": "Genetic Strategy Evolution System Test\n==================================================\nGenerating historical market data...\nStarting evolution with 20 strategies for 10 generations...\n\n--- Evolution Results ---\nBest Strategy: strategy_338773\n  Fitness Score: 0.0000\n  Generation: 0\n  Performance Metrics:\n    total_return: 0.0000\n    win_rate: 0.0000\n    profit_factor: 0.0000\n    max_drawdown: 0.0000\n    sharpe_ratio: 0.0000\n    total_trades: 8.0000\n  Key Parameters:\n    sma_period_short: 6.9368\n    sma_period_long: 169.7497\n    rsi_period: 24.4152\n    stop_loss: 0.0270\n    take_profit: 0.0652\n  Rules: 5\n    Rule 1: trend_following (weight: 0.81)\n    Rule 2: risk_management (weight: 1.15)\n    Rule 3: breakout (weight: 1.55)\n\n--- Evolution Summary ---\nTotal Generations: 10\nBest Fitness: 0.0000\nFinal Fitness: 0.0000\nImprovement: 0.0000\n\nFitness History:\n  Generation 1: 0.0000\n  Generation 2: 0.0000\n  Generation 3: 0.0000\n  Generation 4: 0.0000\n  Generation 5: 0.0000\n  Generation 6: 0.0000\n  Generation 7: 0.0000\n  Generation 8: 0.0000\n  Generation 9: 0.0000\n  Generation 10: 0.0000\n", "stderr": "INFO:__main__:Genetic Strategy Evolution System initialized\nINFO:__main__:Loaded 1000 historical data points\nINFO:__main__:Starting genetic evolution...\nINFO:__main__:Initialized population with 20 strategies\nINFO:__main__:Generation 1/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 2/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 3/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 4/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 5/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 6/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 7/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 8/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 9/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 10/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Evolution completed in 0.32 seconds\nTraceback (most recent call last):\n  File \"D:\\project\\utils\\genetic_strategy_evolution.py\", line 826, in <module>\n    main() \n  File \"D:\\project\\utils\\genetic_strategy_evolution.py\", line 823, in main\n    print(f\"\\n\\u2705 Genetic Strategy Evolution System test completed!\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u2705' in position 2: character maps to <undefined>\n", "success": false}