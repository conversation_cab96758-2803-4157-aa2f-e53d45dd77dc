{"command": "python utils/anomaly_detection_system.py", "timestamp": "2025-07-08T05:52:22.364912", "execution_time": 3.391171455383301, "return_code": 1, "stdout": "Anomaly Detection & Adaptation System Test\n==================================================\nGenerating historical data...\nInitializing system...\n", "stderr": "INFO:__main__:Adaptation Engine initialized\nINFO:__main__:Anomaly Detection System initialized\nINFO:__main__:Anomaly detector fitted with 50 samples\nINFO:__main__:System initialized with 50 feature samples\nTraceback (most recent call last):\n  File \"D:\\project\\utils\\anomaly_detection_system.py\", line 858, in <module>\n    main() \n  File \"D:\\project\\utils\\anomaly_detection_system.py\", line 706, in main\n    print(\"\\u2705 System initialized successfully\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u2705' in position 0: character maps to <undefined>\n", "success": false}