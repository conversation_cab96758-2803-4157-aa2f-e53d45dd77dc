#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Quick Fix and Test for Multi-Brain System
اصلاح سریع و تست سیستم Multi-Brain

This script:
1. Fixes the immediate import and key errors
2. Tests the Multi-Brain System
3. Provides a working solution
"""

import sys
import os
from pathlib import Path

def fix_imports_in_main_file():
    """Fix missing imports in fixed_ultimate_main.py"""
    print("🔧 Fixing imports in fixed_ultimate_main.py...")
    
    try:
        # Read the file
        with open('fixed_ultimate_main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if imports are already there
        if 'from pathlib import Path' not in content:
            # Find the import section and add missing imports
            import_section = """# Now import everything
import time
import json
import zipfile
import shutil
import warnings
warnings.filterwarnings('ignore')
import os
import gc
import hashlib
from pathlib import Path

import torch"""
            
            # Replace the existing import section
            content = content.replace(
                """# Now import everything
import time
import json
import zipfile
import shutil
import warnings
warnings.filterwarnings('ignore')

import torch""",
                import_section
            )
            
            # Write back
            with open('fixed_ultimate_main.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Imports fixed successfully")
        else:
            print("✅ Imports already correct")
            
        return True
        
    except Exception as e:
        print(f"❌ Error fixing imports: {e}")
        return False

def test_multi_brain_system():
    """Test the Multi-Brain System"""
    print("\n🧪 Testing Multi-Brain System...")
    
    try:
        # Import the fixed file
        sys.path.append('.')
        
        # Test basic imports
        import torch
        import pandas as pd
        import numpy as np
        from pathlib import Path
        
        print("✅ Basic imports working")
        
        # Create sample data
        sample_data = pd.DataFrame({
            'close': np.random.randn(100).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, 100),
            'high': np.random.randn(100).cumsum() + 102,
            'low': np.random.randn(100).cumsum() + 98,
            'open': np.random.randn(100).cumsum() + 99
        })
        
        print("✅ Sample data created")
        
        # Test if we can import from the main file
        try:
            exec(open('fixed_ultimate_main.py').read())
            print("✅ Main file executed successfully")
            
            # Test Multi-Brain System
            multi_brain = MultiBrainSystem()
            print("✅ Multi-Brain System initialized")
            
            # Test analysis
            analysis = multi_brain.analyze_training_situation(sample_data, 'LSTM', 'TEST')
            print(f"✅ Analysis completed: {analysis.get('action', 'unknown')}")
            
            return True
            
        except Exception as e:
            print(f"❌ Main file execution error: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Multi-Brain test error: {e}")
        return False

def create_simple_training_script():
    """Create a simple training script that works"""
    print("\n📝 Creating simple training script...")
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Simple Training Script - Fixed Version
اسکریپت آموزش ساده - نسخه اصلاح شده
"""

# Add missing imports at the top
import os
import gc
import hashlib
from pathlib import Path

# Execute the main file
try:
    exec(open('fixed_ultimate_main.py').read())
    print("✅ Main file loaded successfully")
    
    # Run the training
    print("🚀 Starting ultimate market domination training...")
    result = ultimate_market_domination_training()
    
    if result and result.get('success'):
        print("🎉 Training completed successfully!")
    else:
        print("⚠️ Training completed with issues")
        
except Exception as e:
    print(f"❌ Training error: {e}")
    print("🔧 Try running the fixes first:")
    print("   python quick_fix_and_test.py")
'''
    
    try:
        with open('simple_training.py', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print("✅ Simple training script created: simple_training.py")
        return True
        
    except Exception as e:
        print(f"❌ Error creating training script: {e}")
        return False

def main():
    """Main function"""
    print("🔧 QUICK FIX AND TEST SYSTEM")
    print("=" * 50)
    print("Fixing immediate issues and testing the system...")
    print()
    
    # Step 1: Fix imports
    fix_success = fix_imports_in_main_file()
    
    # Step 2: Test system
    if fix_success:
        test_success = test_multi_brain_system()
    else:
        test_success = False
    
    # Step 3: Create simple script
    script_success = create_simple_training_script()
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 QUICK FIX SUMMARY")
    print("=" * 50)
    
    print(f"✅ Import fixes: {'Success' if fix_success else 'Failed'}")
    print(f"✅ System test: {'Success' if test_success else 'Failed'}")
    print(f"✅ Simple script: {'Success' if script_success else 'Failed'}")
    
    if fix_success and script_success:
        print("\n🎉 FIXES APPLIED SUCCESSFULLY!")
        print("🚀 You can now run:")
        print("   python simple_training.py")
        print("\n💡 Or in Google Colab:")
        print("   exec(open('/content/fixed_ultimate_main.py').read())")
        print("   ultimate_market_domination_training()")
    else:
        print("\n⚠️ Some fixes failed. Manual intervention may be needed.")
    
    return fix_success and script_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
