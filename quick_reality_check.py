#!/usr/bin/env python3
"""
🔍 چک سریع واقعیت - کدوم مدل‌ها واقعاً کار می‌کنن؟
"""

def test_finbert():
    """تست FinBERT"""
    print("💰 Testing FinBERT...")
    try:
        from transformers import pipeline
        classifier = pipeline("sentiment-analysis", model="ProsusAI/finbert")
        
        result = classifier("Bitcoin price is rising significantly")[0]
        print(f"   ✅ FinBERT works! Sentiment: {result['label']} ({result['score']:.2f})")
        return True
    except Exception as e:
        print(f"   ❌ FinBERT failed: {e}")
        return False

def test_cryptobert():
    """تست CryptoBERT"""
    print("\n🪙 Testing CryptoBERT...")
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch
        
        tokenizer = AutoTokenizer.from_pretrained("ElKulako/cryptobert")
        model = AutoModel.from_pretrained("ElKulako/cryptobert")
        
        inputs = tokenizer("Bitcoin is going to the moon", return_tensors="pt")
        outputs = model(**inputs)
        
        print(f"   ✅ CryptoBERT works! Output: {outputs.last_hidden_state.shape}")
        return True
    except Exception as e:
        print(f"   ❌ CryptoBERT failed: {e}")
        return False

def test_sentence_transformer():
    """تست Sentence Transformer"""
    print("\n🔤 Testing Sentence Transformer...")
    try:
        from sentence_transformers import SentenceTransformer
        
        model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")
        
        sentences = ["Bitcoin is rising", "Market is bullish"]
        embeddings = model.encode(sentences)
        
        print(f"   ✅ Sentence Transformer works! Embeddings: {embeddings.shape}")
        return True
    except Exception as e:
        print(f"   ❌ Sentence Transformer failed: {e}")
        return False

def test_financial_bert():
    """تست Financial BERT"""
    print("\n📄 Testing Financial BERT...")
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch
        
        tokenizer = AutoTokenizer.from_pretrained("nlpaueb/sec-bert-base")
        model = AutoModel.from_pretrained("nlpaueb/sec-bert-base")
        
        inputs = tokenizer("The company reported strong earnings", return_tensors="pt")
        outputs = model(**inputs)
        
        print(f"   ✅ Financial BERT works! Output: {outputs.last_hidden_state.shape}")
        return True
    except Exception as e:
        print(f"   ❌ Financial BERT failed: {e}")
        return False

def test_distilbert():
    """تست DistilBERT"""
    print("\n🤖 Testing DistilBERT...")
    try:
        from transformers import AutoTokenizer, AutoModel
        
        tokenizer = AutoTokenizer.from_pretrained("distilbert-base-uncased")
        model = AutoModel.from_pretrained("distilbert-base-uncased")
        
        inputs = tokenizer("This is a test", return_tensors="pt")
        outputs = model(**inputs)
        
        print(f"   ✅ DistilBERT works! Output: {outputs.last_hidden_state.shape}")
        return True
    except Exception as e:
        print(f"   ❌ DistilBERT failed: {e}")
        return False

def test_bert_tiny():
    """تست BERT Tiny"""
    print("\n🐣 Testing BERT Tiny...")
    try:
        from transformers import AutoTokenizer, AutoModel
        
        tokenizer = AutoTokenizer.from_pretrained("prajjwal1/bert-tiny")
        model = AutoModel.from_pretrained("prajjwal1/bert-tiny")
        
        inputs = tokenizer("Fast test", return_tensors="pt")
        outputs = model(**inputs)
        
        print(f"   ✅ BERT Tiny works! Output: {outputs.last_hidden_state.shape}")
        return True
    except Exception as e:
        print(f"   ❌ BERT Tiny failed: {e}")
        return False

def main():
    print("🔍 Quick Reality Check - Which Models Actually Work?")
    print("=" * 50)
    
    tests = [
        ("FinBERT", test_finbert),
        ("CryptoBERT", test_cryptobert),
        ("Sentence Transformer", test_sentence_transformer),
        ("Financial BERT", test_financial_bert),
        ("DistilBERT", test_distilbert),
        ("BERT Tiny", test_bert_tiny)
    ]
    
    working = 0
    total = len(tests)
    
    for name, test_func in tests:
        if test_func():
            working += 1
    
    print(f"\n📊 Summary: {working}/{total} models are working")
    
    if working >= 4:
        print("🎉 GREAT! Most models are working - your downloads were useful!")
    elif working >= 2:
        print("👍 GOOD! Some models are working - partially useful downloads")
    else:
        print("😞 BAD! Few models working - mostly wasted bandwidth")

if __name__ == "__main__":
    main() 