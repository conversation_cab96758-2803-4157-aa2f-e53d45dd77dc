#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 بررسی جامع ماژول‌ها
تحلیل کامل همه ماژول‌ها و اسکریپت‌ها برای تشخیص اسکلت بودن
"""

import os
import sys
import importlib.util
import ast
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import json

# تنظیم logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

sys.path.insert(0, '.')

class تحلیلگر_ماژول:
    """تحلیلگر ماژول‌ها برای تشخیص اسکلت بودن"""
    
    def __init__(self):
        self.نتایج = {
            'کامل': [],
            'نیمه_کامل': [],
            'اسکلت': [],
            'خراب': []
        }
        
        # دایرکتوری‌های مهم
        self.دایرکتوری_های_مهم = [
            'core', 'utils', 'models', 'api', 'env', 'portfolio',
            'evaluation', 'optimization', 'examples', 'tests'
        ]
        
        # معیارهای تشخیص
        self.معیارهای_کاملی = {
            'حداقل_خطوط_کد': 50,
            'حداقل_کلاس_ها': 1,
            'حداقل_متدها': 3,
            'حداقل_docstring': 1,
            'حداقل_error_handling': 1
        }
    
    def تحلیل_فایل(self, مسیر_فایل: str) -> Dict[str, Any]:
        """تحلیل یک فایل Python"""
        try:
            with open(مسیر_فایل, 'r', encoding='utf-8') as f:
                محتوا = f.read()
            
            # Parse AST
            tree = ast.parse(محتوا)
            
            # آمارگیری
            آمار = {
                'خطوط_کد': len([line for line in محتوا.split('\n') if line.strip() and not line.strip().startswith('#')]),
                'کل_خطوط': len(محتوا.split('\n')),
                'کلاس_ها': [],
                'متدها': [],
                'توابع': [],
                'imports': [],
                'docstrings': [],
                'try_except_blocks': 0,
                'comments': len([line for line in محتوا.split('\n') if line.strip().startswith('#')]),
                'todo_comments': len([line for line in محتوا.split('\n') if 'TODO' in line.upper() or 'FIXME' in line.upper()]),
                'pass_statements': محتوا.count('pass'),
                'not_implemented': محتوا.count('NotImplementedError') + محتوا.count('not implemented')
            }
            
            # تحلیل AST
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    آمار['کلاس_ها'].append({
                        'نام': node.name,
                        'متدها': [n.name for n in node.body if isinstance(n, ast.FunctionDef)],
                        'docstring': ast.get_docstring(node) is not None
                    })
                
                elif isinstance(node, ast.FunctionDef):
                    if not any(node.name in cls['متدها'] for cls in آمار['کلاس_ها']):
                        آمار['توابع'].append({
                            'نام': node.name,
                            'docstring': ast.get_docstring(node) is not None
                        })
                    
                    آمار['متدها'].append(node.name)
                
                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        آمار['imports'].append(alias.name)
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        آمار['imports'].append(node.module)
                
                elif isinstance(node, ast.Try):
                    آمار['try_except_blocks'] += 1
                
                elif isinstance(node, ast.Expr) and isinstance(node.value, ast.Constant):
                    if isinstance(node.value.value, str) and len(node.value.value) > 10:
                        آمار['docstrings'].append(node.value.value)
            
            return آمار
            
        except Exception as e:
            logger.error(f"خطا در تحلیل {مسیر_فایل}: {e}")
            return {'خطا': str(e)}
    
    def تشخیص_نوع_فایل(self, آمار: Dict[str, Any]) -> str:
        """تشخیص نوع فایل بر اساس آمار"""
        if 'خطا' in آمار:
            return 'خراب'
        
        امتیاز = 0
        
        # بررسی خطوط کد
        if آمار['خطوط_کد'] >= self.معیارهای_کاملی['حداقل_خطوط_کد']:
            امتیاز += 2
        elif آمار['خطوط_کد'] >= 20:
            امتیاز += 1
        
        # بررسی کلاس‌ها
        if len(آمار['کلاس_ها']) >= self.معیارهای_کاملی['حداقل_کلاس_ها']:
            امتیاز += 2
            
            # بررسی متدهای کلاس‌ها
            کل_متدهای_کلاس = sum(len(cls['متدها']) for cls in آمار['کلاس_ها'])
            if کل_متدهای_کلاس >= self.معیارهای_کاملی['حداقل_متدها']:
                امتیاز += 2
        
        # بررسی docstring
        if len(آمار['docstrings']) >= self.معیارهای_کاملی['حداقل_docstring']:
            امتیاز += 1
        
        # بررسی error handling
        if آمار['try_except_blocks'] >= self.معیارهای_کاملی['حداقل_error_handling']:
            امتیاز += 1
        
        # بررسی علائم اسکلت بودن
        if آمار['pass_statements'] > 3:
            امتیاز -= 2
        
        if آمار['not_implemented'] > 0:
            امتیاز -= 1
        
        if آمار['todo_comments'] > 5:
            امتیاز -= 1
        
        # تشخیص نهایی
        if امتیاز >= 6:
            return 'کامل'
        elif امتیاز >= 3:
            return 'نیمه_کامل'
        elif امتیاز >= 1:
            return 'اسکلت'
        else:
            return 'خراب'
    
    def بررسی_دایرکتوری(self, مسیر_دایرکتوری: str) -> Dict[str, Any]:
        """بررسی یک دایرکتوری کامل"""
        نتایج_دایرکتوری = {
            'فایل_های_python': [],
            'آمار_کلی': defaultdict(int),
            'فایل_های_مهم': [],
            'فایل_های_مشکوک': []
        }
        
        if not os.path.exists(مسیر_دایرکتوری):
            return نتایج_دایرکتوری
        
        # پیدا کردن همه فایل‌های Python
        for root, dirs, files in os.walk(مسیر_دایرکتوری):
            for file in files:
                if file.endswith('.py') and file != '__init__.py':
                    مسیر_کامل = os.path.join(root, file)
                    
                    # تحلیل فایل
                    آمار = self.تحلیل_فایل(مسیر_کامل)
                    نوع = self.تشخیص_نوع_فایل(آمار)
                    
                    فایل_info = {
                        'مسیر': مسیر_کامل,
                        'نام': file,
                        'نوع': نوع,
                        'آمار': آمار
                    }
                    
                    نتایج_دایرکتوری['فایل_های_python'].append(فایل_info)
                    نتایج_دایرکتوری['آمار_کلی'][نوع] += 1
                    
                    # شناسایی فایل‌های مهم
                    if any(keyword in file.lower() for keyword in ['main', 'core', 'manager', 'system']):
                        نتایج_دایرکتوری['فایل_های_مهم'].append(فایل_info)
                    
                    # شناسایی فایل‌های مشکوک
                    if نوع in ['اسکلت', 'خراب'] or آمار.get('pass_statements', 0) > 5:
                        نتایج_دایرکتوری['فایل_های_مشکوک'].append(فایل_info)
        
        return نتایج_دایرکتوری
    
    def بررسی_کامل_پروژه(self) -> Dict[str, Any]:
        """بررسی کامل پروژه"""
        print("🔍 شروع بررسی جامع پروژه...")
        print("=" * 60)
        
        نتایج_کامل = {
            'دایرکتوری_ها': {},
            'آمار_کلی': {
                'کل_فایل_ها': 0,
                'کامل': 0,
                'نیمه_کامل': 0,
                'اسکلت': 0,
                'خراب': 0
            },
            'فایل_های_بحرانی': [],
            'توصیه_های_بهبود': []
        }
        
        # بررسی هر دایرکتوری
        for دایرکتوری in self.دایرکتوری_های_مهم:
            print(f"\n📁 بررسی دایرکتوری: {دایرکتوری}")
            
            نتایج_دایرکتوری = self.بررسی_دایرکتوری(دایرکتوری)
            نتایج_کامل['دایرکتوری_ها'][دایرکتوری] = نتایج_دایرکتوری
            
            # به‌روزرسانی آمار کلی
            for نوع, تعداد in نتایج_دایرکتوری['آمار_کلی'].items():
                نتایج_کامل['آمار_کلی'][نوع] += تعداد
                نتایج_کامل['آمار_کلی']['کل_فایل_ها'] += تعداد
            
            # گزارش دایرکتوری
            کل_فایل_ها = len(نتایج_دایرکتوری['فایل_های_python'])
            if کل_فایل_ها > 0:
                کامل_ها = نتایج_دایرکتوری['آمار_کلی']['کامل']
                درصد_کاملی = (کامل_ها / کل_فایل_ها) * 100
                
                print(f"   📊 {کل_فایل_ها} فایل Python")
                print(f"   ✅ کامل: {کامل_ها} ({درصد_کاملی:.1f}%)")
                print(f"   ⚠️ نیمه‌کامل: {نتایج_دایرکتوری['آمار_کلی']['نیمه_کامل']}")
                print(f"   🔴 اسکلت: {نتایج_دایرکتوری['آمار_کلی']['اسکلت']}")
                print(f"   ❌ خراب: {نتایج_دایرکتوری['آمار_کلی']['خراب']}")
                
                # فایل‌های مشکوک
                if نتایج_دایرکتوری['فایل_های_مشکوک']:
                    print(f"   🚨 فایل‌های مشکوک:")
                    for فایل in نتایج_دایرکتوری['فایل_های_مشکوک'][:3]:
                        print(f"      - {فایل['نام']} ({فایل['نوع']})")
            else:
                print(f"   📂 دایرکتوری خالی یا موجود نیست")
        
        # تحلیل فایل‌های root
        print(f"\n📁 بررسی فایل‌های root...")
        root_files = [f for f in os.listdir('.') if f.endswith('.py')]
        for فایل in root_files:
            آمار = self.تحلیل_فایل(فایل)
            نوع = self.تشخیص_نوع_فایل(آمار)
            
            if نوع in ['اسکلت', 'خراب']:
                نتایج_کامل['فایل_های_بحرانی'].append({
                    'مسیر': فایل,
                    'نوع': نوع,
                    'آمار': آمار
                })
        
        # تولید توصیه‌ها
        self._تولید_توصیه_ها(نتایج_کامل)
        
        return نتایج_کامل
    
    def _تولید_توصیه_ها(self, نتایج: Dict[str, Any]):
        """تولید توصیه‌های بهبود"""
        آمار = نتایج['آمار_کلی']
        
        if آمار['کل_فایل_ها'] == 0:
            return
        
        درصد_اسکلت = (آمار['اسکلت'] / آمار['کل_فایل_ها']) * 100
        درصد_خراب = (آمار['خراب'] / آمار['کل_فایل_ها']) * 100
        درصد_کامل = (آمار['کامل'] / آمار['کل_فایل_ها']) * 100
        
        توصیه_ها = []
        
        if درصد_اسکلت > 30:
            توصیه_ها.append("🔧 اولویت بالا: بیش از 30% فایل‌ها اسکلت هستند - نیاز به پیاده‌سازی کامل")
        
        if درصد_خراب > 10:
            توصیه_ها.append("🚨 اولویت فوری: بیش از 10% فایل‌ها خراب هستند - نیاز به رفع فوری")
        
        if درصد_کامل < 50:
            توصیه_ها.append("⚠️ اولویت متوسط: کمتر از 50% فایل‌ها کامل هستند - نیاز به تکمیل")
        
        # توصیه‌های خاص دایرکتوری
        for دایرکتوری, اطلاعات in نتایج['دایرکتوری_ها'].items():
            if اطلاعات['آمار_کلی']['اسکلت'] > 2:
                توصیه_ها.append(f"📁 {دایرکتوری}: {اطلاعات['آمار_کلی']['اسکلت']} فایل اسکلت دارد")
        
        نتایج['توصیه_های_بهبود'] = توصیه_ها
    
    def تولید_گزارش_نهایی(self, نتایج: Dict[str, Any]):
        """تولید گزارش نهایی"""
        print("\n" + "="*60)
        print("📊 گزارش نهایی بررسی جامع پروژه")
        print("="*60)
        
        آمار = نتایج['آمار_کلی']
        
        print(f"\n📈 آمار کلی:")
        print(f"   کل فایل‌های Python: {آمار['کل_فایل_ها']}")
        print(f"   ✅ کامل: {آمار['کامل']} ({آمار['کامل']/آمار['کل_فایل_ها']*100:.1f}%)")
        print(f"   ⚠️ نیمه‌کامل: {آمار['نیمه_کامل']} ({آمار['نیمه_کامل']/آمار['کل_فایل_ها']*100:.1f}%)")
        print(f"   🔴 اسکلت: {آمار['اسکلت']} ({آمار['اسکلت']/آمار['کل_فایل_ها']*100:.1f}%)")
        print(f"   ❌ خراب: {آمار['خراب']} ({آمار['خراب']/آمار['کل_فایل_ها']*100:.1f}%)")
        
        # وضعیت کلی
        درصد_سالم = ((آمار['کامل'] + آمار['نیمه_کامل']) / آمار['کل_فایل_ها']) * 100
        
        print(f"\n🎯 وضعیت کلی پروژه:")
        if درصد_سالم >= 80:
            print(f"   🟢 عالی: {درصد_سالم:.1f}% فایل‌ها سالم هستند")
        elif درصد_سالم >= 60:
            print(f"   🟡 خوب: {درصد_سالم:.1f}% فایل‌ها سالم هستند")
        elif درصد_سالم >= 40:
            print(f"   🟠 متوسط: {درصد_سالم:.1f}% فایل‌ها سالم هستند")
        else:
            print(f"   🔴 ضعیف: {درصد_سالم:.1f}% فایل‌ها سالم هستند")
        
        # توصیه‌های بهبود
        if نتایج['توصیه_های_بهبود']:
            print(f"\n🔧 توصیه‌های بهبود:")
            for توصیه in نتایج['توصیه_های_بهبود']:
                print(f"   {توصیه}")
        
        # فایل‌های بحرانی
        if نتایج['فایل_های_بحرانی']:
            print(f"\n🚨 فایل‌های بحرانی (root):")
            for فایل in نتایج['فایل_های_بحرانی']:
                print(f"   - {فایل['مسیر']} ({فایل['نوع']})")
        
        # ذخیره گزارش
        with open('گزارش_جامع_ماژول_ها.json', 'w', encoding='utf-8') as f:
            json.dump(نتایج, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 گزارش کامل ذخیره شد: گزارش_جامع_ماژول_ها.json")
        
        return درصد_سالم

def main():
    """اجرای اصلی"""
    تحلیلگر = تحلیلگر_ماژول()
    
    # بررسی کامل پروژه
    نتایج = تحلیلگر.بررسی_کامل_پروژه()
    
    # تولید گزارش نهایی
    درصد_سالم = تحلیلگر.تولید_گزارش_نهایی(نتایج)
    
    # نتیجه‌گیری
    if درصد_سالم >= 70:
        print(f"\n🎉 پروژه در وضعیت نسبتاً خوبی است!")
        return 0
    else:
        print(f"\n⚠️ پروژه نیاز به بازسازی جدی دارد!")
        return 1

if __name__ == "__main__":
    exit(main()) 