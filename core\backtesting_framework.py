#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 Advanced Backtesting Framework
فریم‌ورک پیشرفته بک‌تستینگ
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import logging

# Configure logging
logger = logging.getLogger(__name__)

class BacktestMode(Enum):
    """حالت بک‌تست"""
    SIMPLE = "simple"
    ADVANCED = "advanced"
    WALK_FORWARD = "walk_forward"

class PositionSizing(Enum):
    """اندازه‌گیری پوزیشن"""
    FIXED = "fixed"
    KELLY = "kelly"
    VOLATILITY = "volatility"

class BacktestOrderType(Enum):
    """نوع سفارش بک‌تست"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"

@dataclass
class Trade:
    """معامله"""
    timestamp: datetime
    symbol: str
    side: str
    quantity: float
    price: float
    pnl: float = 0.0

@dataclass
class BacktestResult:
    """نتیجه بک‌تست"""
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    num_trades: int
    final_balance: float
    trades: List[Trade]

class Strategy:
    """استراتژی پایه"""
    
    def __init__(self, name: str):
        self.name = name
        self.parameters = {}
    
    def generate_signal(self, data: pd.Series) -> float:
        """تولید سیگنال"""
        return 0.0

class SimpleMovingAverageStrategy(Strategy):
    """استراتژی میانگین متحرک ساده"""
    
    def __init__(self, short_window: int = 10, long_window: int = 30):
        super().__init__("SMA_Strategy")
        self.short_window = short_window
        self.long_window = long_window
    
    def generate_signal(self, data: pd.Series) -> float:
        """تولید سیگنال بر اساس SMA"""
        if len(data) < self.long_window:
            return 0.0
        
        short_sma = data.rolling(self.short_window).mean().iloc[-1]
        long_sma = data.rolling(self.long_window).mean().iloc[-1]
        
        if short_sma > long_sma:
            return 1.0  # Buy signal
        elif short_sma < long_sma:
            return -1.0  # Sell signal
        return 0.0

class PortfolioManager:
    """مدیر پرتفوی"""
    
    def __init__(self, initial_balance: float = 10000):
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.positions = {}
    
    def execute_trade(self, trade: Trade) -> bool:
        """اجرای معامله"""
        # Simple execution logic
        if trade.side == "buy":
            cost = trade.quantity * trade.price
            if cost <= self.balance:
                self.balance -= cost
                return True
        elif trade.side == "sell":
            revenue = trade.quantity * trade.price
            self.balance += revenue
            return True
        return False

class ExecutionModel:
    """مدل اجرا"""
    
    def __init__(self, slippage: float = 0.0):
        self.slippage = slippage
    
    def execute_order(self, order_type: BacktestOrderType, 
                     price: float, quantity: float) -> float:
        """اجرای سفارش"""
        if order_type == BacktestOrderType.MARKET:
            return price * (1 + self.slippage)
        return price

class AdvancedBacktestingEngine:
    """موتور پیشرفته بک‌تستینگ"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.results = {}
        self.trades = []
        self.balance = 10000
        self.initial_balance = 10000
        self.portfolio_manager = PortfolioManager()
        self.execution_model = ExecutionModel()
        
        self.logger.info("Advanced Backtesting Engine initialized")
        
    def load_data(self, file_path: str) -> pd.DataFrame:
        """بارگذاری داده‌ها"""
        try:
            data = pd.read_csv(file_path)
            return data
        except Exception as e:
            self.logger.error(f"Error loading data: {e}")
            return pd.DataFrame()
    
    def run_backtest(self, strategy: Strategy, data: pd.DataFrame, 
                    start_date: Optional[str] = None, 
                    end_date: Optional[str] = None) -> BacktestResult:
        """اجرای بک‌تست"""
        
        if data.empty:
            raise ValueError("No data provided")
        
        # فیلتر داده‌ها بر اساس تاریخ
        if start_date:
            data = data[data['timestamp'] >= start_date]
        if end_date:
            data = data[data['timestamp'] <= end_date]
        
        # شبیه‌سازی معاملات
        for i, row in data.iterrows():
            signal = strategy.generate_signal(data['close'].iloc[:i+1])
            if signal != 0:
                self._execute_trade(signal, row['close'], row['timestamp'])
        
        # محاسبه نتایج
        return self._calculate_results()
    
    def _execute_trade(self, signal: float, price: float, timestamp: str):
        """اجرای معامله"""
        trade = Trade(
            timestamp=pd.to_datetime(timestamp),
            symbol="EURUSD",
            side="buy" if signal > 0 else "sell",
            quantity=abs(signal) * 1000,  # Fixed position size
            price=price
        )
        
        # شبیه‌سازی ساده معامله
        trade_amount = self.balance * 0.1 * signal  # 10% of balance
        self.balance += trade_amount
        trade.pnl = trade_amount
        
        self.trades.append(trade)
    
    def _calculate_results(self) -> BacktestResult:
        """محاسبه نتایج"""
        if not self.trades:
            return BacktestResult(
                total_return=0.0,
                sharpe_ratio=0.0,
                max_drawdown=0.0,
                win_rate=0.0,
                num_trades=0,
                final_balance=self.balance,
                trades=[]
            )
        
        trades_df = pd.DataFrame([vars(t) for t in self.trades])
        
        total_return = (self.balance - self.initial_balance) / self.initial_balance
        num_trades = len(self.trades)
        winning_trades = len([t for t in self.trades if t.pnl > 0])
        win_rate = winning_trades / num_trades if num_trades > 0 else 0
        
        # محاسبه Sharpe ratio
        returns = trades_df['pnl'].pct_change().dropna()
        sharpe_ratio = returns.mean() / returns.std() if returns.std() > 0 else 0
        
        # محاسبه Max Drawdown
        balance_series = pd.Series([t.pnl for t in self.trades]).cumsum() + self.initial_balance
        running_max = balance_series.expanding().max()
        drawdown = (balance_series - running_max) / running_max
        max_drawdown = drawdown.min()
        
        return BacktestResult(
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            num_trades=num_trades,
            final_balance=self.balance,
            trades=self.trades
        )
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """دریافت متریک‌های عملکرد"""
        results = self._calculate_results()
        return {
            'total_return': results.total_return,
            'sharpe_ratio': results.sharpe_ratio,
            'max_drawdown': results.max_drawdown,
            'win_rate': results.win_rate,
            'num_trades': results.num_trades,
            'final_balance': results.final_balance
        }

# Global instance
advanced_backtesting_engine = AdvancedBacktestingEngine()

# Helper functions
def run_simple_backtest(strategy: Strategy, data: pd.DataFrame) -> BacktestResult:
    """اجرای بک‌تست ساده"""
    return advanced_backtesting_engine.run_backtest(strategy, data)

def get_backtesting_statistics() -> Dict[str, Any]:
    """آمار سیستم بک‌تستینگ"""
    return {
        'total_backtests': len(advanced_backtesting_engine.results),
        'engine_status': 'active',
        'last_backtest': datetime.now().isoformat()
    }

# Test
if __name__ == "__main__":
    print("📊 Testing Advanced Backtesting Framework...")
    
    # Create sample data
    dates = pd.date_range("2023-01-01", periods=100, freq="D")
    prices = np.random.randn(100).cumsum() + 100
    data = pd.DataFrame({
        'timestamp': dates,
        'close': prices
    })
    
    try:
        # Create strategy
        strategy = SimpleMovingAverageStrategy(5, 20)
        
        # Run backtest
        result = run_simple_backtest(strategy, data)
        print(f"✅ Backtest completed successfully!")
        print(f"📊 Total Return: {result.total_return:.4f}")
        print(f"📊 Sharpe Ratio: {result.sharpe_ratio:.4f}")
        print(f"📊 Max Drawdown: {result.max_drawdown:.4f}")
        print(f"📊 Win Rate: {result.win_rate:.4f}")
        print(f"📊 Number of Trades: {result.num_trades}")
        
        print("🎯 All tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
