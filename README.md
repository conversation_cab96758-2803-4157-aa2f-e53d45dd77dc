# 🚀 Trading System v2.0 - Professional Edition

یک سیستم معاملاتی پیشرفته و یکپارچه با قابلیت‌های هوش مصنوعی و پردازش داده‌های مالی

## ✨ ویژگی‌های کلیدی

### 🤖 پردازش هوش مصنوعی
- **تحلیل احساسات مالی**: مدل‌های FinBERT و CryptoBERT
- **پیش‌بینی Time Series**: مدل‌های Chronos Amazon
- **پردازش متن**: مدل‌های transformer پیشرفته
- **Ensemble Learning**: ترکیب چندین مدل برای دقت بالاتر

### 📊 تحلیل داده‌های مالی
- **بازارهای مختلف**: Forex, Crypto, Stocks, Commodities
- **تایم فریم‌های متنوع**: M1, M5, M15, H1, H4, D1
- **تکنیکال آنالیز**: بیش از 100 اندیکاتور
- **پردازش real-time**: داده‌های زنده و آپدیت لحظه‌ای

### 🏗️ معماری پیشرفته
- **Microservices**: معماری مدولار و مقیاس‌پذیر
- **Async Processing**: پردازش غیرهمزمان برای عملکرد بهتر
- **Caching System**: سیستم کش چندسطحی
- **Monitoring**: مانیتورینگ کامل سیستم و عملکرد

### 🔧 مدیریت پیکربندی
- **Configuration Management**: مدیریت متمرکز تنظیمات
- **Environment Support**: پشتیبانی محیط‌های مختلف
- **Hot Reload**: بارگذاری مجدد تنظیمات بدون توقف
- **Validation**: اعتبارسنجی پیکربندی

## 🛠️ نصب و راه‌اندازی

### پیش‌نیازها
```bash
# Python 3.8+
python --version

# Git
git --version

# حافظه رم حداقل 8GB
# فضای دیسک حداقل 10GB
```

### نصب
```bash
# کلون کردن پروژه
git clone https://github.com/your-repo/trading-system-v2.git
cd trading-system-v2

# ایجاد محیط مجازی
python -m venv venv
source venv/bin/activate  # Linux/Mac
# یا
venv\Scripts\activate  # Windows

# نصب وابستگی‌ها
pip install -r requirements.txt

# نصب کتابخانه‌های اضافی (اختیاری)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### پیکربندی
```bash
# کپی کردن فایل پیکربندی
cp config.yaml.example config.yaml

# ویرایش تنظیمات
nano config.yaml
```

## 🎯 راه‌اندازی سریع

### 1. اجرای بررسی سیستم
```bash
python main_new.py --health-check
```

### 2. اعتبارسنجی پیکربندی
```bash
python main_new.py --validate-config
```

### 3. اجرای دمو
```bash
python main_new.py --demo
```

### 4. اجرای تعاملی
```bash
python main_new.py --interactive
```

### 5. اجرای عادی
```bash
python main_new.py
```

## 📋 نحوه استفاده

### حالت خط فرمان
```bash
# اجرای سیستم با پیکربندی مشخص
python main_new.py --config custom_config.yaml

# اجرای در حالت debug
python main_new.py --debug

# اجرای بدون پروکسی
python main_new.py --no-proxy
```

### استفاده از API
```python
from core.config import get_config
from ai_models import initialize_models, model_registry

# مقداردهی اولیه
config = get_config()
model_manager = initialize_models()

# استفاده از مدل
sentiment_model = model_registry.get_model("finbert")
result = sentiment_model.predict("Bitcoin is showing strong bullish momentum")

print(f"Sentiment: {result.prediction['sentiment']}")
print(f"Confidence: {result.confidence:.2f}")
```

### تحلیل احساسات
```python
from ai_models import create_sentiment_ensemble

# ایجاد ensemble
ensemble = create_sentiment_ensemble()

# تحلیل متن
text = "The market is experiencing significant volatility"
result = ensemble.predict_ensemble(text)

print(f"Overall sentiment: {result['sentiment']}")
print(f"Trading action: {result['action']}")
print(f"Confidence: {result['confidence']:.2f}")
```

### پیش‌بینی Time Series
```python
from ai_models import create_timeseries_ensemble
import numpy as np

# ایجاد ensemble
ensemble = create_timeseries_ensemble()

# داده‌های نمونه
data = np.random.randn(100).cumsum()

# پیش‌بینی
predictions = ensemble.predict_ensemble(data)
```

## 🔧 پیکربندی

### فایل config.yaml
```yaml
# تنظیمات پروکسی
proxy:
  enabled: true
  host: "127.0.0.1"
  port: 10809
  protocol: "http"

# تنظیمات مدل‌ها
models:
  finbert:
    enabled: true
    max_memory: 512
    timeout: 30
    batch_size: 16
  
  chronos_small:
    enabled: true
    max_memory: 1024
    context_length: 512
    prediction_length: 24

# تنظیمات لاگینگ
logging:
  level: "INFO"
  file_path: "logs/trading_system.log"
  console_enabled: true
  json_enabled: true
```

### متغیرهای محیطی
```bash
# در فایل .env
TRADING_SYSTEM_CONFIG=config.yaml
TRADING_SYSTEM_LOG_LEVEL=INFO
TRADING_SYSTEM_PROXY_HOST=127.0.0.1
TRADING_SYSTEM_PROXY_PORT=10809
HUGGINGFACE_HUB_CACHE=./models_cache
```

## 📊 مانیتورینگ و لاگینگ

### مشاهده لاگ‌ها
```bash
# لاگ‌های real-time
tail -f logs/trading_system.log

# لاگ‌های JSON
cat logs/trading_system.json | jq '.'

# لاگ‌های خطا
grep "ERROR" logs/trading_system.log
```

### وضعیت سیستم
```bash
# بررسی سلامت
python main_new.py --health-check

# وضعیت مدل‌ها
python -c "
from ai_models import model_registry
for name, info in model_registry.get_all_info().items():
    print(f'{name}: {info.status}')
"
```

### مانیتورینگ عملکرد
```python
from core.utils import performance_monitor

# دریافت معیارهای فعلی
metrics = performance_monitor.get_current_metrics()
print(f"CPU: {metrics.cpu_percent}%")
print(f"Memory: {metrics.memory_percent}%")

# تاریخچه عملکرد
history = performance_monitor.get_metrics_history(minutes=60)
```

## 🔍 عیب‌یابی

### مشکلات رایج

#### 1. خطای import
```bash
# بررسی نصب کتابخانه‌ها
pip list | grep transformers
pip list | grep torch

# نصب مجدد
pip install --upgrade transformers huggingface_hub
```

#### 2. مشکل پروکسی
```bash
# تست پروکسی
curl --proxy http://127.0.0.1:10809 https://httpbin.org/ip

# چک کردن پیکربندی
python -c "
from core.config import get_config
print(get_config().proxy)
"
```

#### 3. کمبود حافظه
```bash
# بررسی حافظه
python -c "
from core.utils import memory_manager
print(memory_manager.get_memory_info())
"

# پاک کردن کش
python -c "
from core.utils import cache_manager
cache_manager.clear()
"
```

### لاگ‌های مفید
```python
# فعال کردن debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# دریافت لاگ‌های مدل
from ai_models import model_registry
for name, model in model_registry.get_all_models().items():
    print(f"{name}: {model.get_model_info()}")
```

## 🧪 تست

### اجرای تست‌ها
```bash
# تمام تست‌ها
pytest tests/

# تست‌های خاص
pytest tests/test_models.py
pytest tests/test_core.py

# تست با coverage
pytest --cov=core --cov=ai_models tests/
```

### تست دستی
```python
# تست مدل sentiment
from ai_models import FinBERTModel

model = FinBERTModel()
model.load_model("ProsusAI/finbert")

result = model.predict("Bitcoin price is surging")
assert result.prediction['sentiment'] in ['positive', 'negative', 'neutral']
```

## 🚀 استقرار

### استقرار محلی
```bash
# اجرای در پس‌زمینه
nohup python main_new.py > system.log 2>&1 &

# استفاده از systemd
sudo cp trading-system.service /etc/systemd/system/
sudo systemctl enable trading-system
sudo systemctl start trading-system
```

### استقرار با Docker
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "main_new.py"]
```

```bash
# ساخت image
docker build -t trading-system:v2.0 .

# اجرای container
docker run -d --name trading-system -p 8080:8080 trading-system:v2.0
```

## 📈 بهینه‌سازی عملکرد

### تنظیمات حافظه
```yaml
# در config.yaml
advanced:
  memory_management:
    max_memory_usage: 8192  # 8GB
    cleanup_threshold: 70
    gc_interval: 30
```

### تنظیمات GPU
```yaml
models:
  finbert:
    parameters:
      device: "cuda"
      torch_dtype: "float16"
```

### کش بهینه
```yaml
advanced:
  caching:
    max_size: 2000
    ttl: 1800
    disk_cache: true
    compression: true
```

## 🔒 امنیت

### تنظیمات امنیتی
```yaml
advanced:
  security:
    enable_ssl_verification: true
    max_request_size: 10485760
    rate_limit: 100
```

### مدیریت secrets
```bash
# استفاده از environment variables
export HUGGINGFACE_TOKEN=your_token_here
export DATABASE_PASSWORD=your_password_here
```

## 📚 API مستندات

### Core API
```python
from core.config import get_config, ConfigManager
from core.logger import get_logger
from core.base import registry
from core.utils import performance_monitor
```

### AI Models API
```python
from ai_models import (
    ModelManager,
    model_registry,
    initialize_models,
    create_sentiment_ensemble
)
```

## 🤝 مشارکت

### راهنمای مشارکت
1. Fork کردن پروژه
2. ایجاد branch جدید (`git checkout -b feature/amazing-feature`)
3. Commit کردن تغییرات (`git commit -m 'Add amazing feature'`)
4. Push کردن به branch (`git push origin feature/amazing-feature`)
5. باز کردن Pull Request

### استانداردهای کد
```bash
# Format کردن کد
black .
isort .

# بررسی کیفیت
flake8 .
mypy .
```

## 📄 لایسنس

این پروژه تحت لایسنس MIT منتشر شده است. برای جزئیات بیشتر فایل [LICENSE](LICENSE) را مطالعه کنید.

## 🙏 تشکر

- [Hugging Face](https://huggingface.co/) برای مدل‌های transformer
- [PyTorch](https://pytorch.org/) برای فریمورک deep learning
- [FastAPI](https://fastapi.tiangolo.com/) برای فریمورک API
- تمام کمک‌کنندگان و توسعه‌دهندگان

## 📞 پشتیبانی

- 📧 Email: <EMAIL>
- 💬 Discord: [Trading System Community](https://discord.gg/trading-system)
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/trading-system-v2/issues)
- 📖 Documentation: [Wiki](https://github.com/your-repo/trading-system-v2/wiki)

---

<p align="center">
  <strong>🚀 Trading System v2.0 - The Future of Automated Trading</strong>
</p>

<p align="center">
  Made with ❤️ by the Trading System Team
</p>