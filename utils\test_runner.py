#!/usr/bin/env python3
"""
Test Runner عملیاتی برای سیستم معاملاتی
=======================================

این ماژول مسئول اجرای خودکار تست‌ها در محیط عملیاتی است.
"""

import os
import sys
import subprocess
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import time
import asyncio

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger
from utils.proxy_manager import get_proxy_manager
from core.config import get_config


@dataclass
class TestResult:
    """نتیجه تست"""
    name: str
    passed: bool
    duration: float
    output: str
    error: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'passed': self.passed,
            'duration': self.duration,
            'output': self.output,
            'error': self.error,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class TestSuite:
    """مجموعه تست"""
    name: str
    tests: List[str]
    critical: bool = True
    timeout: int = 300  # seconds
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'tests': self.tests,
            'critical': self.critical,
            'timeout': self.timeout
        }


class OperationalTestRunner:
    """اجراکننده تست‌های عملیاتی"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = get_logger(__name__)
        self.results = []
        self.test_suites = []
        
        # Test settings
        self.test_dir = Path("tests")
        self.report_dir = Path("test_reports")
        self.coverage_threshold = self.config.get("coverage_threshold", 80)
        self.fail_fast = self.config.get("fail_fast", False)
        self.verbose = self.config.get("verbose", True)
        
        # Proxy settings
        self.use_proxy = self.config.get("use_proxy", True)
        
        # Initialize test suites
        self._setup_test_suites()
        
        # Create report directory
        self.report_dir.mkdir(exist_ok=True)
    
    def _setup_test_suites(self):
        """تنظیم مجموعه تست‌ها"""
        self.test_suites = [
            TestSuite(
                name="core_tests",
                tests=[
                    "tests/test_advanced_risk_manager.py",
                    "tests/test_smart_portfolio_manager.py",
                    "tests/test_integrated_system.py"
                ],
                critical=True,
                timeout=300
            ),
            TestSuite(
                name="unit_tests",
                tests=[
                    "tests/test_*.py"
                ],
                critical=False,
                timeout=600
            ),
            TestSuite(
                name="integration_tests",
                tests=[
                    "tests/test_integration_basic.py",
                    "tests/test_integration_complete.py"
                ],
                critical=True,
                timeout=900
            )
        ]
    
    def run_startup_tests(self) -> bool:
        """اجرای تست‌های شروع سیستم"""
        self.logger.info("🧪 Starting system startup tests...")
        
        # فقط تست‌های critical
        critical_suites = [suite for suite in self.test_suites if suite.critical]
        
        for suite in critical_suites:
            self.logger.info(f"Running critical test suite: {suite.name}")
            
            success = self._run_test_suite(suite)
            if not success and self.fail_fast:
                self.logger.error(f"❌ Critical test suite failed: {suite.name}")
                return False
        
        # Generate startup report
        self._generate_startup_report()
        
        all_passed = all(result.passed for result in self.results)
        if all_passed:
            self.logger.info("✅ All startup tests passed!")
        else:
            self.logger.warning("⚠️ Some startup tests failed")
        
        return all_passed
    
    def run_all_tests(self) -> bool:
        """اجرای تمام تست‌ها"""
        self.logger.info("🧪 Starting comprehensive test suite...")
        
        start_time = time.time()
        
        for suite in self.test_suites:
            self.logger.info(f"Running test suite: {suite.name}")
            
            success = self._run_test_suite(suite)
            if not success and suite.critical and self.fail_fast:
                self.logger.error(f"❌ Critical test suite failed: {suite.name}")
                return False
        
        # Generate comprehensive report
        total_time = time.time() - start_time
        self._generate_comprehensive_report(total_time)
        
        all_passed = all(result.passed for result in self.results)
        if all_passed:
            self.logger.info("✅ All tests passed!")
        else:
            self.logger.warning("⚠️ Some tests failed")
        
        return all_passed
    
    def _run_test_suite(self, suite: TestSuite) -> bool:
        """اجرای یک مجموعه تست"""
        start_time = time.time()
        
        for test_path in suite.tests:
            result = self._run_single_test(test_path, suite.timeout)
            self.results.append(result)
            
            if not result.passed:
                self.logger.error(f"❌ Test failed: {result.name}")
                if result.error:
                    self.logger.error(f"Error: {result.error}")
                
                if suite.critical and self.fail_fast:
                    return False
        
        duration = time.time() - start_time
        suite_passed = all(r.passed for r in self.results if r.name.startswith(suite.name))
        
        self.logger.info(f"Test suite {suite.name} completed in {duration:.2f}s - {'PASSED' if suite_passed else 'FAILED'}")
        return suite_passed
    
    def _run_single_test(self, test_path: str, timeout: int) -> TestResult:
        """اجرای یک تست"""
        test_name = os.path.basename(test_path)
        self.logger.info(f"Running test: {test_name}")
        
        start_time = time.time()
        
        try:
            # Build pytest command
            cmd = [
                sys.executable, "-m", "pytest",
                test_path,
                "-v",
                "--tb=short",
                "--disable-warnings"
            ]
            
            # Add coverage if needed
            if self.coverage_threshold > 0:
                cmd.extend([
                    "--cov=.",
                    f"--cov-fail-under={self.coverage_threshold}",
                    "--cov-report=term-missing"
                ])
            
            # Set proxy environment if needed
            env = os.environ.copy()
            if self.use_proxy:
                proxy_config = get_config().proxy
                if proxy_config.enabled:
                    env["HTTP_PROXY"] = proxy_config.url
                    env["HTTPS_PROXY"] = proxy_config.url
            
            # Run test
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout,
                env=env,
                encoding='utf-8',
                errors='replace'  # Replace problematic characters instead of crashing
            )
            
            duration = time.time() - start_time
            
            # Parse result
            passed = result.returncode == 0
            output = result.stdout
            error = result.stderr if result.stderr else None
            
            return TestResult(
                name=test_name,
                passed=passed,
                duration=duration,
                output=output,
                error=error
            )
            
        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            return TestResult(
                name=test_name,
                passed=False,
                duration=duration,
                output="",
                error=f"Test timed out after {timeout} seconds"
            )
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                name=test_name,
                passed=False,
                duration=duration,
                output="",
                error=str(e)
            )
    
    def _generate_startup_report(self):
        """تولید گزارش تست‌های شروع"""
        report = {
            "type": "startup_tests",
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": len(self.results),
                "passed": len([r for r in self.results if r.passed]),
                "failed": len([r for r in self.results if not r.passed]),
                "success_rate": len([r for r in self.results if r.passed]) / len(self.results) * 100 if self.results else 0,
                "total_duration": sum(r.duration for r in self.results)
            },
            "results": [r.to_dict() for r in self.results]
        }
        
        # Save to file
        report_file = self.report_dir / f"startup_tests_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📊 Startup test report saved: {report_file}")
    
    def _generate_comprehensive_report(self, total_time: float):
        """تولید گزارش کامل"""
        report = {
            "type": "comprehensive_tests",
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": len(self.results),
                "passed": len([r for r in self.results if r.passed]),
                "failed": len([r for r in self.results if not r.passed]),
                "success_rate": len([r for r in self.results if r.passed]) / len(self.results) * 100 if self.results else 0,
                "total_duration": total_time,
                "average_test_duration": sum(r.duration for r in self.results) / len(self.results) if self.results else 0
            },
            "test_suites": [s.to_dict() for s in self.test_suites],
            "results": [r.to_dict() for r in self.results],
            "failed_tests": [r.to_dict() for r in self.results if not r.passed]
        }
        
        # Save to file
        report_file = self.report_dir / f"comprehensive_tests_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📊 Comprehensive test report saved: {report_file}")
        
        # Print summary
        self._print_test_summary(report)
    
    def _print_test_summary(self, report: Dict[str, Any]):
        """چاپ خلاصه تست‌ها"""
        summary = report["summary"]
        
        print("\n" + "="*60)
        print("🧪 TEST EXECUTION SUMMARY")
        print("="*60)
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed']} ✅")
        print(f"Failed: {summary['failed']} ❌")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        print(f"Total Duration: {summary['total_duration']:.2f}s")
        print("="*60)
        
        if report["failed_tests"]:
            print("\n❌ FAILED TESTS:")
            for failed_test in report["failed_tests"]:
                print(f"  - {failed_test['name']}: {failed_test['error']}")
        
        print()
    
    def run_health_check_tests(self) -> bool:
        """اجرای تست‌های بررسی سلامت"""
        self.logger.info("🏥 Running health check tests...")
        
        health_tests = [
            "tests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_initialization",
            "tests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_initialization",
            "tests/test_integrated_system.py::TestIntegratedSystem::test_system_initialization"
        ]
        
        for test in health_tests:
            result = self._run_single_test(test, 60)  # 60 second timeout
            self.results.append(result)
            
            if not result.passed:
                self.logger.error(f"❌ Health check failed: {result.name}")
                return False
        
        self.logger.info("✅ All health checks passed!")
        return True
    
    def get_test_status(self) -> Dict[str, Any]:
        """وضعیت تست‌ها"""
        if not self.results:
            return {"status": "no_tests_run"}
        
        return {
            "status": "completed",
            "total_tests": len(self.results),
            "passed": len([r for r in self.results if r.passed]),
            "failed": len([r for r in self.results if not r.passed]),
            "success_rate": len([r for r in self.results if r.passed]) / len(self.results) * 100,
            "last_run": max(r.timestamp for r in self.results).isoformat()
        }


# Global test runner instance
test_runner = OperationalTestRunner()

# Quick test functions for integration
def run_startup_tests() -> bool:
    """اجرای سریع تست‌های شروع"""
    return test_runner.run_startup_tests()

def run_health_checks() -> bool:
    """اجرای سریع تست‌های سلامت"""
    return test_runner.run_health_check_tests()

def run_all_tests() -> bool:
    """اجرای تمام تست‌ها"""
    return test_runner.run_all_tests()


if __name__ == "__main__":
    """اجرای مستقل"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Operational Test Runner")
    parser.add_argument("--startup", action="store_true", help="Run startup tests only")
    parser.add_argument("--health", action="store_true", help="Run health checks only")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO if args.verbose else logging.WARNING,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run tests based on arguments
    if args.startup:
        success = run_startup_tests()
    elif args.health:
        success = run_health_checks()
    elif args.all:
        success = run_all_tests()
    else:
        print("Please specify test type: --startup, --health, or --all")
        sys.exit(1)
    
    sys.exit(0 if success else 1) 