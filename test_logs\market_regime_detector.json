{"command": "python utils/market_regime_detector.py", "timestamp": "2025-07-08T05:52:06.740733", "execution_time": 4.240382671356201, "return_code": 1, "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"D:\\project\\utils\\market_regime_detector.py\", line 630, in <module>\n    main() \n  File \"D:\\project\\utils\\market_regime_detector.py\", line 566, in main\n    print(\"\\U0001f3ad Market Regime Detection System Demo\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f3ad' in position 0: character maps to <undefined>\n", "success": false}