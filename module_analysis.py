#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Module Analysis - Complete System Review
تحلیل کامل ماژول‌ها برای تشخیص فایل‌های اسکلتی
"""

import os
import sys
import ast
import json
import logging
from pathlib import Path
from typing import Dict, List, Any
from collections import defaultdict

# تنظیم logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

sys.path.insert(0, '.')

class ModuleAnalyzer:
    """تحلیلگر ماژول‌ها"""
    
    def __init__(self):
        self.results = {
            'complete': [],
            'partial': [],
            'skeleton': [],
            'broken': []
        }
        
        # دایرکتوری‌های مهم
        self.important_dirs = [
            'core', 'utils', 'models', 'api', 'env', 'portfolio',
            'evaluation', 'optimization', 'examples', 'tests'
        ]
        
        # معیارهای کاملی
        self.completeness_criteria = {
            'min_lines': 50,
            'min_classes': 1,
            'min_methods': 3,
            'min_docstrings': 1,
            'min_error_handling': 1
        }
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """تحلیل یک فایل Python"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST
            tree = ast.parse(content)
            
            # آمارگیری
            stats = {
                'code_lines': len([line for line in content.split('\n') 
                                 if line.strip() and not line.strip().startswith('#')]),
                'total_lines': len(content.split('\n')),
                'classes': [],
                'methods': [],
                'functions': [],
                'imports': [],
                'docstrings': [],
                'try_except_blocks': 0,
                'comments': len([line for line in content.split('\n') 
                               if line.strip().startswith('#')]),
                'todo_comments': len([line for line in content.split('\n') 
                                    if 'TODO' in line.upper() or 'FIXME' in line.upper()]),
                'pass_statements': content.count('pass'),
                'not_implemented': content.count('NotImplementedError') + content.count('not implemented'),
                'empty_methods': 0
            }
            
            # تحلیل AST
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_methods = [n.name for n in node.body if isinstance(n, ast.FunctionDef)]
                    stats['classes'].append({
                        'name': node.name,
                        'methods': class_methods,
                        'docstring': ast.get_docstring(node) is not None
                    })
                
                elif isinstance(node, ast.FunctionDef):
                    # بررسی متد خالی
                    if len(node.body) == 1 and isinstance(node.body[0], ast.Pass):
                        stats['empty_methods'] += 1
                    
                    if not any(node.name in cls['methods'] for cls in stats['classes']):
                        stats['functions'].append({
                            'name': node.name,
                            'docstring': ast.get_docstring(node) is not None
                        })
                    
                    stats['methods'].append(node.name)
                
                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        stats['imports'].append(alias.name)
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        stats['imports'].append(node.module)
                
                elif isinstance(node, ast.Try):
                    stats['try_except_blocks'] += 1
                
                elif isinstance(node, ast.Expr) and isinstance(node.value, ast.Constant):
                    if isinstance(node.value.value, str) and len(node.value.value) > 10:
                        stats['docstrings'].append(node.value.value)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error analyzing {file_path}: {e}")
            return {'error': str(e)}
    
    def classify_file(self, stats: Dict[str, Any]) -> str:
        """تشخیص نوع فایل"""
        if 'error' in stats:
            return 'broken'
        
        score = 0
        
        # بررسی خطوط کد
        if stats['code_lines'] >= self.completeness_criteria['min_lines']:
            score += 3
        elif stats['code_lines'] >= 20:
            score += 1
        
        # بررسی کلاس‌ها
        if len(stats['classes']) >= self.completeness_criteria['min_classes']:
            score += 2
            
            # بررسی متدهای کلاس‌ها
            total_class_methods = sum(len(cls['methods']) for cls in stats['classes'])
            if total_class_methods >= self.completeness_criteria['min_methods']:
                score += 2
        
        # بررسی docstring
        if len(stats['docstrings']) >= self.completeness_criteria['min_docstrings']:
            score += 1
        
        # بررسی error handling
        if stats['try_except_blocks'] >= self.completeness_criteria['min_error_handling']:
            score += 1
        
        # کسر امتیاز برای علائم اسکلت
        if stats['pass_statements'] > 3:
            score -= 2
        
        if stats['not_implemented'] > 0:
            score -= 1
        
        if stats['empty_methods'] > 2:
            score -= 1
        
        if stats['todo_comments'] > 5:
            score -= 1
        
        # تشخیص نهایی
        if score >= 6:
            return 'complete'
        elif score >= 3:
            return 'partial'
        elif score >= 1:
            return 'skeleton'
        else:
            return 'broken'
    
    def analyze_directory(self, dir_path: str) -> Dict[str, Any]:
        """بررسی یک دایرکتوری"""
        dir_results = {
            'python_files': [],
            'stats': defaultdict(int),
            'important_files': [],
            'problematic_files': []
        }
        
        if not os.path.exists(dir_path):
            return dir_results
        
        # پیدا کردن همه فایل‌های Python
        for root, dirs, files in os.walk(dir_path):
            for file in files:
                if file.endswith('.py') and file != '__init__.py':
                    full_path = os.path.join(root, file)
                    
                    # تحلیل فایل
                    stats = self.analyze_file(full_path)
                    file_type = self.classify_file(stats)
                    
                    file_info = {
                        'path': full_path,
                        'name': file,
                        'type': file_type,
                        'stats': stats
                    }
                    
                    dir_results['python_files'].append(file_info)
                    dir_results['stats'][file_type] += 1
                    
                    # شناسایی فایل‌های مهم
                    if any(keyword in file.lower() for keyword in ['main', 'core', 'manager', 'system']):
                        dir_results['important_files'].append(file_info)
                    
                    # شناسایی فایل‌های مشکوک
                    if file_type in ['skeleton', 'broken'] or stats.get('pass_statements', 0) > 5:
                        dir_results['problematic_files'].append(file_info)
        
        return dir_results
    
    def analyze_complete_project(self) -> Dict[str, Any]:
        """بررسی کامل پروژه"""
        print("🔍 Starting Complete Project Analysis...")
        print("=" * 60)
        
        complete_results = {
            'directories': {},
            'overall_stats': {
                'total_files': 0,
                'complete': 0,
                'partial': 0,
                'skeleton': 0,
                'broken': 0
            },
            'critical_files': [],
            'improvement_recommendations': []
        }
        
        # بررسی هر دایرکتوری
        for directory in self.important_dirs:
            print(f"\n📁 Analyzing directory: {directory}")
            
            dir_results = self.analyze_directory(directory)
            complete_results['directories'][directory] = dir_results
            
            # به‌روزرسانی آمار کلی
            for file_type, count in dir_results['stats'].items():
                complete_results['overall_stats'][file_type] += count
                complete_results['overall_stats']['total_files'] += count
            
            # گزارش دایرکتوری
            total_files = len(dir_results['python_files'])
            if total_files > 0:
                complete_files = dir_results['stats']['complete']
                completion_rate = (complete_files / total_files) * 100
                
                print(f"   📊 {total_files} Python files")
                print(f"   ✅ Complete: {complete_files} ({completion_rate:.1f}%)")
                print(f"   ⚠️ Partial: {dir_results['stats']['partial']}")
                print(f"   🔴 Skeleton: {dir_results['stats']['skeleton']}")
                print(f"   ❌ Broken: {dir_results['stats']['broken']}")
                
                # فایل‌های مشکوک
                if dir_results['problematic_files']:
                    print(f"   🚨 Problematic files:")
                    for file_info in dir_results['problematic_files'][:3]:
                        print(f"      - {file_info['name']} ({file_info['type']})")
            else:
                print(f"   📂 Empty or non-existent directory")
        
        # تحلیل فایل‌های root
        print(f"\n📁 Analyzing root files...")
        root_files = [f for f in os.listdir('.') if f.endswith('.py')]
        for file in root_files:
            stats = self.analyze_file(file)
            file_type = self.classify_file(stats)
            
            if file_type in ['skeleton', 'broken']:
                complete_results['critical_files'].append({
                    'path': file,
                    'type': file_type,
                    'stats': stats
                })
        
        # تولید توصیه‌ها
        self._generate_recommendations(complete_results)
        
        return complete_results
    
    def _generate_recommendations(self, results: Dict[str, Any]):
        """تولید توصیه‌های بهبود"""
        stats = results['overall_stats']
        
        if stats['total_files'] == 0:
            return
        
        skeleton_rate = (stats['skeleton'] / stats['total_files']) * 100
        broken_rate = (stats['broken'] / stats['total_files']) * 100
        complete_rate = (stats['complete'] / stats['total_files']) * 100
        
        recommendations = []
        
        if skeleton_rate > 30:
            recommendations.append("🔧 HIGH PRIORITY: >30% files are skeletons - need complete implementation")
        
        if broken_rate > 10:
            recommendations.append("🚨 URGENT: >10% files are broken - need immediate fix")
        
        if complete_rate < 50:
            recommendations.append("⚠️ MEDIUM PRIORITY: <50% files complete - need completion")
        
        # دایرکتوری‌های خاص
        for directory, info in results['directories'].items():
            if info['stats']['skeleton'] > 2:
                recommendations.append(f"📁 {directory}: {info['stats']['skeleton']} skeleton files")
        
        results['improvement_recommendations'] = recommendations
    
    def generate_final_report(self, results: Dict[str, Any]):
        """تولید گزارش نهایی"""
        print("\n" + "="*60)
        print("📊 FINAL PROJECT ANALYSIS REPORT")
        print("="*60)
        
        stats = results['overall_stats']
        
        print(f"\n📈 Overall Statistics:")
        print(f"   Total Python files: {stats['total_files']}")
        print(f"   ✅ Complete: {stats['complete']} ({stats['complete']/stats['total_files']*100:.1f}%)")
        print(f"   ⚠️ Partial: {stats['partial']} ({stats['partial']/stats['total_files']*100:.1f}%)")
        print(f"   🔴 Skeleton: {stats['skeleton']} ({stats['skeleton']/stats['total_files']*100:.1f}%)")
        print(f"   ❌ Broken: {stats['broken']} ({stats['broken']/stats['total_files']*100:.1f}%)")
        
        # وضعیت کلی
        health_rate = ((stats['complete'] + stats['partial']) / stats['total_files']) * 100
        
        print(f"\n🎯 Project Health Status:")
        if health_rate >= 80:
            print(f"   🟢 EXCELLENT: {health_rate:.1f}% files are healthy")
        elif health_rate >= 60:
            print(f"   🟡 GOOD: {health_rate:.1f}% files are healthy")
        elif health_rate >= 40:
            print(f"   🟠 FAIR: {health_rate:.1f}% files are healthy")
        else:
            print(f"   🔴 POOR: {health_rate:.1f}% files are healthy")
        
        # توصیه‌های بهبود
        if results['improvement_recommendations']:
            print(f"\n🔧 Improvement Recommendations:")
            for recommendation in results['improvement_recommendations']:
                print(f"   {recommendation}")
        
        # فایل‌های بحرانی
        if results['critical_files']:
            print(f"\n🚨 Critical Files (root):")
            for file_info in results['critical_files']:
                print(f"   - {file_info['path']} ({file_info['type']})")
        
        # تحلیل تفصیلی دایرکتوری‌ها
        print(f"\n📁 Directory Analysis:")
        for directory, info in results['directories'].items():
            total = len(info['python_files'])
            if total > 0:
                complete = info['stats']['complete']
                skeleton = info['stats']['skeleton']
                print(f"   {directory}: {complete}/{total} complete ({complete/total*100:.1f}%), {skeleton} skeleton")
        
        # ذخیره گزارش
        with open('complete_module_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 Complete report saved: complete_module_analysis.json")
        
        return health_rate

def main():
    """اجرای اصلی"""
    analyzer = ModuleAnalyzer()
    
    # بررسی کامل پروژه
    results = analyzer.analyze_complete_project()
    
    # تولید گزارش نهایی
    health_rate = analyzer.generate_final_report(results)
    
    # نتیجه‌گیری
    print(f"\n🎯 CONCLUSION:")
    if health_rate >= 70:
        print(f"   🎉 Project is in relatively good condition!")
        print(f"   📈 Health Rate: {health_rate:.1f}%")
        return 0
    else:
        print(f"   ⚠️ Project needs serious reconstruction!")
        print(f"   📉 Health Rate: {health_rate:.1f}%")
        return 1

if __name__ == "__main__":
    exit(main()) 