"""
🏗️ Base Classes - کلاس‌های پایه
کلاس‌های پایه و رجیستری کامپوننت‌ها
"""

import os
import sys
import abc
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@dataclass
class TradingSignal:
    """سیگنال معاملاتی"""
    symbol: str
    action: str  # buy, sell, hold
    confidence: float
    price: float
    timestamp: datetime
    reasoning: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        return {
            'symbol': self.symbol,
            'action': self.action,
            'confidence': self.confidence,
            'price': self.price,
            'timestamp': self.timestamp.isoformat(),
            'reasoning': self.reasoning
        }

@dataclass
class MarketData:
    """داده‌های بازار"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume
        }

class BaseComponent(abc.ABC):
    """کلاس پایه برای تمام کامپوننت‌ها"""
    
    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.logger = logging.getLogger(self.__class__.__name__)
        self.started = False
        self.initialized = False
        self.start_time = None
        self.status = "created"
    
    @abc.abstractmethod
    def initialize(self) -> bool:
        """مقداردهی اولیه کامپوننت"""
        pass
    
    @abc.abstractmethod
    def start(self) -> bool:
        """شروع کامپوننت"""
        pass
    
    @abc.abstractmethod
    def stop(self) -> bool:
        """توقف کامپوننت"""
        pass
    
    @abc.abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت کامپوننت"""
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """دریافت وضعیت کامپوننت"""
        return {
            'name': self.name,
            'status': self.status,
            'started': self.started,
            'initialized': self.initialized,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'uptime': str(datetime.now() - self.start_time) if self.start_time else None
        }
    
    def _set_status(self, status: str):
        """تنظیم وضعیت"""
        self.status = status
        self.logger.info(f"Status changed to: {status}")

class ComponentRegistry:
    """رجیستری کامپوننت‌ها"""
    
    def __init__(self):
        self.components: Dict[str, BaseComponent] = {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def register(self, name: str, component: Any) -> bool:
        """ثبت کامپوننت"""
        try:
            # If it's not a BaseComponent, wrap it
            if not isinstance(component, BaseComponent):
                component = ComponentWrapper(name, component)
            
            self.components[name] = component
            self.logger.info(f"✅ Component registered: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to register component {name}: {e}")
            return False
    
    def get(self, name: str) -> Optional[BaseComponent]:
        """دریافت کامپوننت"""
        return self.components.get(name)
    
    def get_all(self) -> Dict[str, BaseComponent]:
        """دریافت تمام کامپوننت‌ها"""
        return self.components.copy()
    
    def start_all(self) -> bool:
        """شروع تمام کامپوننت‌ها"""
        success = True
        
        for name, component in self.components.items():
            try:
                if not component.initialized:
                    component.initialize()
                
                if not component.started:
                    component.start()
                    
                self.logger.info(f"✅ Started component: {name}")
                
            except Exception as e:
                self.logger.error(f"❌ Failed to start component {name}: {e}")
                success = False
        
        return success
    
    def stop_all(self) -> bool:
        """توقف تمام کامپوننت‌ها"""
        success = True
        
        for name, component in self.components.items():
            try:
                if component.started:
                    component.stop()
                    
                self.logger.info(f"✅ Stopped component: {name}")
                
            except Exception as e:
                self.logger.error(f"❌ Failed to stop component {name}: {e}")
                success = False
        
        return success
    
    def health_check_all(self) -> Dict[str, Any]:
        """بررسی سلامت تمام کامپوننت‌ها"""
        results = {}
        
        for name, component in self.components.items():
            try:
                results[name] = component.health_check()
            except Exception as e:
                results[name] = {
                    'status': 'error',
                    'error': str(e)
                }
        
        return results
    
    def get_status_all(self) -> Dict[str, Any]:
        """دریافت وضعیت تمام کامپوننت‌ها"""
        results = {}
        
        for name, component in self.components.items():
            try:
                results[name] = component.get_status()
            except Exception as e:
                results[name] = {
                    'name': name,
                    'status': 'error',
                    'error': str(e)
                }
        
        return results

class ComponentWrapper(BaseComponent):
    """Wrapper برای کامپوننت‌هایی که BaseComponent نیستند"""
    
    def __init__(self, name: str, component: Any):
        super().__init__(name)
        self.wrapped_component = component
        self.initialized = True
    
    def initialize(self) -> bool:
        """مقداردهی اولیه"""
        try:
            if hasattr(self.wrapped_component, 'initialize'):
                result = self.wrapped_component.initialize()
                self.initialized = result if isinstance(result, bool) else True
            else:
                self.initialized = True
            
            self._set_status("initialized")
            return self.initialized
            
        except Exception as e:
            self.logger.error(f"Initialization failed: {e}")
            self._set_status("failed")
            return False
    
    def start(self) -> bool:
        """شروع کامپوننت"""
        try:
            if hasattr(self.wrapped_component, 'start'):
                result = self.wrapped_component.start()
                self.started = result if isinstance(result, bool) else True
            else:
                self.started = True
            
            if self.started:
                self.start_time = datetime.now()
                self._set_status("running")
            
            return self.started
            
        except Exception as e:
            self.logger.error(f"Start failed: {e}")
            self._set_status("failed")
            return False
    
    def stop(self) -> bool:
        """توقف کامپوننت"""
        try:
            if hasattr(self.wrapped_component, 'stop'):
                result = self.wrapped_component.stop()
                stopped = result if isinstance(result, bool) else True
            else:
                stopped = True
            
            if stopped:
                self.started = False
                self._set_status("stopped")
            
            return stopped
            
        except Exception as e:
            self.logger.error(f"Stop failed: {e}")
            self._set_status("failed")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        try:
            if hasattr(self.wrapped_component, 'health_check'):
                return self.wrapped_component.health_check()
            else:
                return {
                    'status': 'healthy' if self.started else 'stopped',
                    'initialized': self.initialized,
                    'started': self.started
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }

@dataclass
class ModelPrediction:
    """نتیجه پیش‌بینی مدل"""
    model_name: str
    prediction: Any
    confidence: float
    timestamp: datetime
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        return {
            'model_name': self.model_name,
            'prediction': self.prediction,
            'confidence': self.confidence,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata
        }

class BaseModel(abc.ABC):
    """کلاس پایه برای مدل‌های ML"""
    
    def __init__(self, name: str, model_type: str = "general", config: Dict[str, Any] = None):
        self.name = name
        self.model_type = model_type
        self.config = config or {}
        self.logger = logging.getLogger(self.__class__.__name__)
        self.model = None
        self.loaded = False
        self.metrics = {}
        self.training_history = []
        self.created_at = datetime.now()
        self.last_updated = datetime.now()
    
    @abc.abstractmethod
    def load_model(self, model_path: str = None) -> bool:
        """بارگذاری مدل"""
        pass
    
    @abc.abstractmethod
    def predict(self, input_data: Any) -> ModelPrediction:
        """پیش‌بینی"""
        pass
    
    def save_model(self, model_path: str) -> bool:
        """ذخیره مدل"""
        try:
            # Default implementation - can be overridden
            if self.model is None:
                self.logger.error("No model to save")
                return False
            
            # In a real implementation, you would save the model
            # For now, just log it
            self.logger.info(f"Model {self.name} saved to {model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save model: {e}")
            return False
    
    def train(self, training_data: Any, validation_data: Any = None) -> bool:
        """آموزش مدل"""
        try:
            # Default implementation - can be overridden
            self.logger.info(f"Training model {self.name}")
            
            # Mock training process
            import time
            time.sleep(0.1)  # Simulate training time
            
            # Update metrics
            self.metrics.update({
                'accuracy': 0.85,
                'loss': 0.15,
                'training_time': 0.1
            })
            
            # Update history
            self.training_history.append({
                'timestamp': datetime.now(),
                'metrics': self.metrics.copy()
            })
            
            self.last_updated = datetime.now()
            self.logger.info(f"Model {self.name} training completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Training failed: {e}")
            return False
    
    def evaluate(self, test_data: Any) -> Dict[str, float]:
        """ارزیابی مدل"""
        try:
            if not self.loaded:
                self.logger.error("Model not loaded")
                return {}
            
            # Default implementation - can be overridden
            evaluation_metrics = {
                'accuracy': 0.85,
                'precision': 0.80,
                'recall': 0.90,
                'f1_score': 0.85
            }
            
            self.logger.info(f"Model {self.name} evaluated")
            return evaluation_metrics
            
        except Exception as e:
            self.logger.error(f"Evaluation failed: {e}")
            return {}
    
    def get_info(self) -> Dict[str, Any]:
        """دریافت اطلاعات مدل"""
        return {
            'name': self.name,
            'model_type': self.model_type,
            'loaded': self.loaded,
            'metrics': self.metrics,
            'created_at': self.created_at.isoformat(),
            'last_updated': self.last_updated.isoformat(),
            'training_history_count': len(self.training_history)
        }
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت مدل"""
        return {
            'name': self.name,
            'model_type': self.model_type,
            'loaded': self.loaded,
            'status': 'healthy' if self.loaded else 'not_loaded',
            'last_updated': self.last_updated.isoformat()
        }

# Global component registry
registry = ComponentRegistry()

# Export main classes and functions
__all__ = [
    'BaseComponent',
    'ComponentRegistry',
    'ComponentWrapper',
    'TradingSignal',
    'MarketData',
    'BaseModel',
    'ModelPrediction',
    'registry'
] 