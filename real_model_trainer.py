"""
🔥 Pearl-3x7B REAL Model Trainer
مربی واقعی مدل‌های Pearl-3x7B

این بار با دیتاست‌های واقعی و آموزش اصولی!
"""

import os
import sys
import time
import json
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, f1_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

class RealFinBERTTrainer:
    """🤗 مربی واقعی FinBERT"""
    
    def __init__(self, data_path: str):
        self.data_path = data_path
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔥 FinBERT Trainer initialized on {self.device}")
    
    def train_real_finbert(self) -> Dict[str, Any]:
        """آموزش واقعی FinBERT با داده‌های اخبار واقعی"""
        print("🤗 TRAINING REAL FINBERT")
        print("=" * 40)
        
        try:
            # Install transformers
            os.system("pip install transformers torch-audio")
            
            from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments
            from transformers import DataCollatorWithPadding
            import torch.utils.data as data_utils
            
            # Load real news data
            print("📰 Loading real news sentiment data...")
            news_data = pd.read_pickle(self.data_path)
            
            print(f"   📊 Total articles: {len(news_data)}")
            
            # Prepare data for training
            texts = news_data['text'].tolist()
            
            # Create sentiment labels from VADER scores
            labels = []
            for _, row in news_data.iterrows():
                compound = row['vader_compound']
                if compound >= 0.05:
                    labels.append(2)  # Positive
                elif compound <= -0.05:
                    labels.append(0)  # Negative
                else:
                    labels.append(1)  # Neutral
            
            print(f"   📊 Label distribution:")
            label_counts = pd.Series(labels).value_counts()
            print(f"      Negative (0): {label_counts.get(0, 0)}")
            print(f"      Neutral (1): {label_counts.get(1, 0)}")
            print(f"      Positive (2): {label_counts.get(2, 0)}")
            
            # Split data
            train_texts, val_texts, train_labels, val_labels = train_test_split(
                texts, labels, test_size=0.2, random_state=42, stratify=labels
            )
            
            print(f"   📊 Train samples: {len(train_texts)}")
            print(f"   📊 Validation samples: {len(val_texts)}")
            
            # Load pre-trained model
            model_name = "ProsusAI/finbert"
            print(f"   🤗 Loading {model_name}...")
            
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForSequenceClassification.from_pretrained(
                model_name, 
                num_labels=3,
                ignore_mismatched_sizes=True
            ).to(self.device)
            
            # Tokenize data
            print("   🔤 Tokenizing texts...")
            train_encodings = tokenizer(train_texts, truncation=True, padding=True, max_length=512, return_tensors='pt')
            val_encodings = tokenizer(val_texts, truncation=True, padding=True, max_length=512, return_tensors='pt')
            
            # Create datasets
            class SentimentDataset(torch.utils.data.Dataset):
                def __init__(self, encodings, labels):
                    self.encodings = encodings
                    self.labels = labels
                
                def __getitem__(self, idx):
                    item = {key: val[idx] for key, val in self.encodings.items()}
                    item['labels'] = torch.tensor(self.labels[idx], dtype=torch.long)
                    return item
                
                def __len__(self):
                    return len(self.labels)
            
            train_dataset = SentimentDataset(train_encodings, train_labels)
            val_dataset = SentimentDataset(val_encodings, val_labels)
            
            # Training arguments
            training_args = TrainingArguments(
                output_dir='/content/finbert_results',
                num_train_epochs=3,
                per_device_train_batch_size=8,
                per_device_eval_batch_size=8,
                warmup_steps=500,
                weight_decay=0.01,
                logging_dir='/content/finbert_logs',
                logging_steps=100,
                evaluation_strategy="steps",
                eval_steps=500,
                save_steps=1000,
                load_best_model_at_end=True,
                metric_for_best_model="eval_loss",
                greater_is_better=False,
            )
            
            # Initialize trainer
            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset,
                tokenizer=tokenizer,
            )
            
            # Start training
            print("🔥 Starting FinBERT training...")
            start_time = time.time()
            
            trainer.train()
            
            training_time = time.time() - start_time
            
            # Evaluate
            print("📊 Evaluating model...")
            eval_results = trainer.evaluate()
            
            # Test predictions
            model.eval()
            predictions = []
            true_labels = []
            
            with torch.no_grad():
                for i in range(0, len(val_texts), 8):
                    batch_texts = val_texts[i:i+8]
                    batch_labels = val_labels[i:i+8]
                    
                    inputs = tokenizer(batch_texts, truncation=True, padding=True, max_length=512, return_tensors='pt').to(self.device)
                    outputs = model(**inputs)
                    
                    batch_predictions = torch.argmax(outputs.logits, dim=-1).cpu().numpy()
                    predictions.extend(batch_predictions)
                    true_labels.extend(batch_labels)
            
            # Calculate metrics
            accuracy = accuracy_score(true_labels, predictions)
            f1 = f1_score(true_labels, predictions, average='weighted')
            
            # Save model
            model_save_path = f"/content/models/finbert_real_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(model_save_path, exist_ok=True)
            
            model.save_pretrained(model_save_path)
            tokenizer.save_pretrained(model_save_path)
            
            print(f"✅ FinBERT training completed!")
            print(f"   Training time: {training_time/3600:.2f} hours")
            print(f"   Accuracy: {accuracy:.4f}")
            print(f"   F1 Score: {f1:.4f}")
            print(f"   Model saved: {model_save_path}")
            
            return {
                "success": True,
                "model_name": "FinBERT_Real",
                "training_time_hours": training_time / 3600,
                "accuracy": accuracy,
                "f1_score": f1,
                "eval_loss": eval_results.get('eval_loss', 0),
                "model_path": model_save_path,
                "train_samples": len(train_texts),
                "val_samples": len(val_texts)
            }
            
        except Exception as e:
            print(f"❌ FinBERT training failed: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}

class RealLSTMTrainer:
    """📈 مربی واقعی LSTM"""
    
    def __init__(self, data_path: str):
        self.data_path = data_path
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"📈 LSTM Trainer initialized on {self.device}")
    
    def train_real_lstm(self) -> Dict[str, Any]:
        """آموزش واقعی LSTM با داده‌های قیمت واقعی"""
        print("📈 TRAINING REAL LSTM")
        print("=" * 30)
        
        try:
            # Load real financial data
            print("📊 Loading real financial data...")
            financial_data = pd.read_pickle(self.data_path)
            
            # Use Bitcoin data for training
            if 'BTC-USD' not in financial_data:
                print("❌ Bitcoin data not found")
                return {"success": False, "error": "Bitcoin data not available"}
            
            btc_data = financial_data['BTC-USD'].copy()
            print(f"   📊 Bitcoin data: {len(btc_data)} records")
            
            # Prepare features
            features = ['Close', 'Volume', 'SMA_20', 'SMA_50', 'RSI', 'MACD', 'Volatility']
            data = btc_data[features].dropna()
            
            print(f"   📊 Features: {features}")
            print(f"   📊 Clean data: {len(data)} records")
            
            # Normalize data
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(data)
            
            # Create sequences
            sequence_length = 60  # 60 hours lookback
            X, y = [], []
            
            for i in range(sequence_length, len(scaled_data)):
                X.append(scaled_data[i-sequence_length:i])
                y.append(scaled_data[i, 0])  # Predict Close price
            
            X, y = np.array(X), np.array(y)
            
            print(f"   📊 Sequences: {X.shape}")
            print(f"   📊 Targets: {y.shape}")
            
            # Split data
            split_idx = int(0.8 * len(X))
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # Convert to tensors
            X_train = torch.FloatTensor(X_train).to(self.device)
            y_train = torch.FloatTensor(y_train).to(self.device)
            X_test = torch.FloatTensor(X_test).to(self.device)
            y_test = torch.FloatTensor(y_test).to(self.device)
            
            # Define LSTM model
            class RealLSTM(nn.Module):
                def __init__(self, input_size, hidden_size=128, num_layers=3, dropout=0.2):
                    super(RealLSTM, self).__init__()
                    self.hidden_size = hidden_size
                    self.num_layers = num_layers
                    
                    self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                                       batch_first=True, dropout=dropout)
                    self.attention = nn.MultiheadAttention(hidden_size, num_heads=8, batch_first=True)
                    self.fc1 = nn.Linear(hidden_size, 64)
                    self.fc2 = nn.Linear(64, 1)
                    self.dropout = nn.Dropout(dropout)
                    self.relu = nn.ReLU()
                
                def forward(self, x):
                    lstm_out, _ = self.lstm(x)
                    
                    # Apply attention
                    attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
                    
                    # Use last time step
                    out = attn_out[:, -1, :]
                    out = self.dropout(out)
                    out = self.relu(self.fc1(out))
                    out = self.dropout(out)
                    out = self.fc2(out)
                    
                    return out
            
            # Initialize model
            model = RealLSTM(input_size=len(features)).to(self.device)
            criterion = nn.MSELoss()
            optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
            
            print(f"   🧠 Model parameters: {sum(p.numel() for p in model.parameters()):,}")
            
            # Training loop
            print("🔥 Starting LSTM training...")
            start_time = time.time()
            
            batch_size = 32
            num_epochs = 100
            best_loss = float('inf')
            patience_counter = 0
            
            for epoch in range(num_epochs):
                model.train()
                total_loss = 0
                
                # Mini-batch training
                for i in range(0, len(X_train), batch_size):
                    batch_X = X_train[i:i+batch_size]
                    batch_y = y_train[i:i+batch_size]
                    
                    optimizer.zero_grad()
                    outputs = model(batch_X)
                    loss = criterion(outputs.squeeze(), batch_y)
                    loss.backward()
                    
                    # Gradient clipping
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    
                    optimizer.step()
                    total_loss += loss.item()
                
                avg_loss = total_loss / (len(X_train) // batch_size)
                
                # Validation
                model.eval()
                with torch.no_grad():
                    val_outputs = model(X_test)
                    val_loss = criterion(val_outputs.squeeze(), y_test).item()
                
                scheduler.step(val_loss)
                
                # Early stopping
                if val_loss < best_loss:
                    best_loss = val_loss
                    patience_counter = 0
                    # Save best model
                    torch.save(model.state_dict(), '/content/best_lstm_model.pth')
                else:
                    patience_counter += 1
                
                if epoch % 10 == 0:
                    print(f"   Epoch {epoch:3d}: Train Loss: {avg_loss:.6f}, Val Loss: {val_loss:.6f}")
                
                if patience_counter >= 20:
                    print(f"   Early stopping at epoch {epoch}")
                    break
            
            training_time = time.time() - start_time
            
            # Load best model and evaluate
            model.load_state_dict(torch.load('/content/best_lstm_model.pth'))
            model.eval()
            
            with torch.no_grad():
                train_pred = model(X_train)
                test_pred = model(X_test)
                
                train_rmse = torch.sqrt(criterion(train_pred.squeeze(), y_train)).item()
                test_rmse = torch.sqrt(criterion(test_pred.squeeze(), y_test)).item()
            
            # Save model
            model_save_path = f"/content/models/lstm_real_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(model_save_path, exist_ok=True)
            
            torch.save({
                'model_state_dict': model.state_dict(),
                'scaler': scaler,
                'features': features,
                'sequence_length': sequence_length
            }, f"{model_save_path}/lstm_model.pth")
            
            print(f"✅ LSTM training completed!")
            print(f"   Training time: {training_time/3600:.2f} hours")
            print(f"   Train RMSE: {train_rmse:.6f}")
            print(f"   Test RMSE: {test_rmse:.6f}")
            print(f"   Model saved: {model_save_path}")
            
            return {
                "success": True,
                "model_name": "LSTM_Real",
                "training_time_hours": training_time / 3600,
                "train_rmse": train_rmse,
                "test_rmse": test_rmse,
                "model_path": model_save_path,
                "train_samples": len(X_train),
                "test_samples": len(X_test),
                "features": features
            }
            
        except Exception as e:
            print(f"❌ LSTM training failed: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}

class RealRLTrainer:
    """🤖 مربی واقعی عامل تقویتی"""

    def __init__(self, data_path: str):
        self.data_path = data_path
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🤖 RL Trainer initialized on {self.device}")

    def train_real_dqn(self) -> Dict[str, Any]:
        """آموزش واقعی DQN با محیط معاملاتی واقعی"""
        print("🤖 TRAINING REAL DQN AGENT")
        print("=" * 35)

        try:
            # Load real trading environment data
            print("📊 Loading real trading environment data...")
            trading_data = pd.read_pickle(self.data_path)

            # Use Bitcoin data for RL training
            if 'BTC-USD' not in trading_data:
                print("❌ Bitcoin trading data not found")
                return {"success": False, "error": "Bitcoin trading data not available"}

            btc_trading = trading_data['BTC-USD']
            features = btc_trading['features']
            rewards = btc_trading['rewards']
            actions = btc_trading['actions']

            print(f"   📊 Trading samples: {len(features)}")
            print(f"   📊 Feature dimension: {features.shape[1]}")
            print(f"   📊 Action space: {len(np.unique(actions))} actions")

            # Define DQN network
            class RealDQN(nn.Module):
                def __init__(self, state_size, action_size, hidden_size=256):
                    super(RealDQN, self).__init__()
                    self.network = nn.Sequential(
                        nn.Linear(state_size, hidden_size),
                        nn.ReLU(),
                        nn.Dropout(0.2),
                        nn.Linear(hidden_size, hidden_size),
                        nn.ReLU(),
                        nn.Dropout(0.2),
                        nn.Linear(hidden_size, hidden_size // 2),
                        nn.ReLU(),
                        nn.Linear(hidden_size // 2, action_size)
                    )

                def forward(self, x):
                    return self.network(x)

            # Initialize networks
            state_size = features.shape[1]
            action_size = 3  # Hold, Buy, Sell

            main_network = RealDQN(state_size, action_size).to(self.device)
            target_network = RealDQN(state_size, action_size).to(self.device)
            target_network.load_state_dict(main_network.state_dict())

            optimizer = optim.Adam(main_network.parameters(), lr=0.001)
            criterion = nn.MSELoss()

            print(f"   🧠 Network parameters: {sum(p.numel() for p in main_network.parameters()):,}")

            # Experience replay buffer
            class ReplayBuffer:
                def __init__(self, capacity=10000):
                    self.capacity = capacity
                    self.buffer = []
                    self.position = 0

                def push(self, state, action, reward, next_state, done):
                    if len(self.buffer) < self.capacity:
                        self.buffer.append(None)
                    self.buffer[self.position] = (state, action, reward, next_state, done)
                    self.position = (self.position + 1) % self.capacity

                def sample(self, batch_size):
                    return np.random.choice(self.buffer, batch_size, replace=False)

                def __len__(self):
                    return len(self.buffer)

            replay_buffer = ReplayBuffer(capacity=50000)

            # Training parameters
            epsilon = 1.0
            epsilon_decay = 0.995
            epsilon_min = 0.01
            gamma = 0.95
            batch_size = 64
            target_update_freq = 1000

            # Fill replay buffer with real data
            print("   📚 Filling replay buffer with real trading data...")
            for i in range(len(features) - 1):
                state = features[i]
                action = actions[i]
                reward = rewards[i]
                next_state = features[i + 1]
                done = (i == len(features) - 2)

                replay_buffer.push(state, action, reward, next_state, done)

            print(f"   📚 Replay buffer size: {len(replay_buffer)}")

            # Training loop
            print("🔥 Starting DQN training...")
            start_time = time.time()

            num_episodes = 1000
            episode_rewards = []
            losses = []

            for episode in range(num_episodes):
                # Sample batch from replay buffer
                if len(replay_buffer) < batch_size:
                    continue

                batch = replay_buffer.sample(batch_size)
                states = torch.FloatTensor([e[0] for e in batch]).to(self.device)
                actions_batch = torch.LongTensor([e[1] for e in batch]).to(self.device)
                rewards_batch = torch.FloatTensor([e[2] for e in batch]).to(self.device)
                next_states = torch.FloatTensor([e[3] for e in batch]).to(self.device)
                dones = torch.BoolTensor([e[4] for e in batch]).to(self.device)

                # Current Q values
                current_q_values = main_network(states).gather(1, actions_batch.unsqueeze(1))

                # Next Q values from target network
                next_q_values = target_network(next_states).max(1)[0].detach()
                target_q_values = rewards_batch + (gamma * next_q_values * ~dones)

                # Compute loss
                loss = criterion(current_q_values.squeeze(), target_q_values)

                # Optimize
                optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(main_network.parameters(), max_norm=1.0)
                optimizer.step()

                losses.append(loss.item())

                # Update target network
                if episode % target_update_freq == 0:
                    target_network.load_state_dict(main_network.state_dict())

                # Decay epsilon
                epsilon = max(epsilon_min, epsilon * epsilon_decay)

                # Calculate episode reward (simulate trading)
                episode_reward = 0
                portfolio_value = 10000  # Start with $10,000
                position = 0  # 0: no position, 1: long, -1: short

                for i in range(min(100, len(features) - 1)):  # Simulate 100 steps
                    state = torch.FloatTensor(features[i]).unsqueeze(0).to(self.device)

                    if np.random.random() > epsilon:
                        with torch.no_grad():
                            q_values = main_network(state)
                            action = q_values.max(1)[1].item()
                    else:
                        action = np.random.randint(0, action_size)

                    # Execute action and calculate reward
                    price_change = rewards[i]

                    if action == 1 and position <= 0:  # Buy
                        position = 1
                        episode_reward += price_change * portfolio_value
                    elif action == 2 and position >= 0:  # Sell
                        position = -1
                        episode_reward -= price_change * portfolio_value
                    # action == 0 is hold

                episode_rewards.append(episode_reward)

                if episode % 100 == 0:
                    avg_reward = np.mean(episode_rewards[-100:])
                    avg_loss = np.mean(losses[-100:]) if losses else 0
                    print(f"   Episode {episode:4d}: Avg Reward: {avg_reward:8.2f}, Avg Loss: {avg_loss:.6f}, Epsilon: {epsilon:.3f}")

            training_time = time.time() - start_time

            # Evaluate final performance
            final_avg_reward = np.mean(episode_rewards[-100:])
            max_reward = max(episode_rewards)

            # Save model
            model_save_path = f"/content/models/dqn_real_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(model_save_path, exist_ok=True)

            torch.save({
                'main_network_state_dict': main_network.state_dict(),
                'target_network_state_dict': target_network.state_dict(),
                'state_size': state_size,
                'action_size': action_size,
                'episode_rewards': episode_rewards
            }, f"{model_save_path}/dqn_model.pth")

            print(f"✅ DQN training completed!")
            print(f"   Training time: {training_time/3600:.2f} hours")
            print(f"   Final avg reward: {final_avg_reward:.2f}")
            print(f"   Max reward: {max_reward:.2f}")
            print(f"   Model saved: {model_save_path}")

            return {
                "success": True,
                "model_name": "DQN_Real",
                "training_time_hours": training_time / 3600,
                "final_avg_reward": final_avg_reward,
                "max_reward": max_reward,
                "total_episodes": num_episodes,
                "model_path": model_save_path
            }

        except Exception as e:
            print(f"❌ DQN training failed: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}

def train_all_real_models(datasets_dir: str) -> Dict[str, Any]:
    """آموزش همه مدل‌ها با دیتاست‌های واقعی"""
    print("🔥 TRAINING ALL MODELS WITH REAL DATASETS")
    print("=" * 60)

    results = {}

    # 1. Train FinBERT with real news data
    news_data_path = f"{datasets_dir}/news_sentiment_data.pkl"
    if os.path.exists(news_data_path):
        print("\n🤗 Training FinBERT...")
        finbert_trainer = RealFinBERTTrainer(news_data_path)
        results['finbert'] = finbert_trainer.train_real_finbert()
    else:
        print("❌ News sentiment data not found")
        results['finbert'] = {"success": False, "error": "Data not found"}

    # 2. Train LSTM with real financial data
    financial_data_path = f"{datasets_dir}/financial_data.pkl"
    if os.path.exists(financial_data_path):
        print("\n📈 Training LSTM...")
        lstm_trainer = RealLSTMTrainer(financial_data_path)
        results['lstm'] = lstm_trainer.train_real_lstm()
    else:
        print("❌ Financial data not found")
        results['lstm'] = {"success": False, "error": "Data not found"}

    # 3. Train DQN with real trading environment data
    trading_data_path = f"{datasets_dir}/trading_environment_data.pkl"
    if os.path.exists(trading_data_path):
        print("\n🤖 Training DQN...")
        rl_trainer = RealRLTrainer(trading_data_path)
        results['dqn'] = rl_trainer.train_real_dqn()
    else:
        print("❌ Trading environment data not found")
        results['dqn'] = {"success": False, "error": "Data not found"}

    # Summary
    successful = sum(1 for r in results.values() if r.get('success', False))
    total = len(results)

    print(f"\n🎉 TRAINING SUMMARY:")
    print(f"   ✅ Successful: {successful}/{total}")

    for name, result in results.items():
        if result.get('success'):
            training_time = result.get('training_time_hours', 0)
            print(f"   ✅ {name.upper()}: {training_time:.2f}h")
        else:
            print(f"   ❌ {name.upper()}: {result.get('error', 'Failed')}")

    return results

# Main execution
if __name__ == "__main__":
    print("🔥 REAL MODEL TRAINER READY")
    print("First run: download_real_datasets()")
    print("Then run: train_all_real_models('/content/real_datasets')")
