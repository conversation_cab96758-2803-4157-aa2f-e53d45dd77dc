import pytest
import pandas as pd

import pytest
import pandas as pd
from data.fetcher import MT5DataFetcher

@pytest.fixture
def fetcher():
    return MT5DataFetcher("data/storage/")

def test_fetch_data(fetcher):
    try:
        df = fetcher.fetch_data("EURUSD", period="1d", interval="1h")
        assert isinstance(df, pd.DataFrame)
        assert not df.empty
    except FileNotFoundError:
        pass
