"""
🔥 Pearl-3x7B REAL Colab Trainer
مربی واقعی Pearl-3x7B برای Google Colab

این بار با دیتاست‌های واقعی و آموزش اصولی!
"""

import os
import sys
import time
import json
import torch
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# Check if running in Colab
try:
    import google.colab
    IN_COLAB = True
    print("🚀 Running in Google Colab")
except ImportError:
    IN_COLAB = False
    print("⚠️ Not running in Google Colab")

class RealDatasetManager:
    """📊 مدیر دیتاست‌های واقعی"""
    
    def __init__(self):
        self.data_dir = "/content/real_datasets"
        os.makedirs(self.data_dir, exist_ok=True)
        
    def download_financial_data(self) -> Dict[str, Any]:
        """دانلود داده‌های مالی واقعی"""
        print("📊 Downloading REAL financial data...")
        
        try:
            # Install required packages
            os.system("pip install yfinance pandas-datareader alpha_vantage")
            
            import yfinance as yf
            
            # Download real crypto and stock data
            symbols = ['BTC-USD', 'ETH-USD', 'AAPL', 'GOOGL', 'TSLA', 'MSFT']
            
            all_data = {}
            for symbol in symbols:
                print(f"   📈 Downloading {symbol}...")
                ticker = yf.Ticker(symbol)
                
                # Get 2 years of data
                data = ticker.history(period="2y", interval="1h")
                
                if not data.empty:
                    # Add technical indicators
                    data['Returns'] = data['Close'].pct_change()
                    data['SMA_20'] = data['Close'].rolling(20).mean()
                    data['SMA_50'] = data['Close'].rolling(50).mean()
                    data['RSI'] = self._calculate_rsi(data['Close'])
                    data['MACD'] = self._calculate_macd(data['Close'])
                    data['Volatility'] = data['Returns'].rolling(24).std()
                    
                    # Clean data
                    data = data.dropna()
                    
                    all_data[symbol] = data
                    print(f"   ✅ {symbol}: {len(data)} records")
                else:
                    print(f"   ❌ {symbol}: No data")
            
            # Save to files
            dataset_path = f"{self.data_dir}/financial_data.pkl"
            pd.to_pickle(all_data, dataset_path)
            
            print(f"✅ Financial data saved: {len(all_data)} symbols")
            return {
                "success": True,
                "path": dataset_path,
                "symbols": list(all_data.keys()),
                "total_records": sum(len(data) for data in all_data.values())
            }
            
        except Exception as e:
            print(f"❌ Financial data download failed: {e}")
            return {"success": False, "error": str(e)}
    
    def download_news_sentiment_data(self) -> Dict[str, Any]:
        """دانلود داده‌های اخبار و احساسات واقعی"""
        print("📰 Downloading REAL news sentiment data...")
        
        try:
            # Install required packages
            os.system("pip install newsapi-python textblob vaderSentiment")
            
            from newsapi import NewsApiClient
            from textblob import TextBlob
            from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
            
            # Use free news sources
            print("   📰 Fetching financial news...")
            
            # Alternative: Use RSS feeds and web scraping
            import feedparser
            import requests
            from bs4 import BeautifulSoup
            
            news_data = []
            
            # Financial RSS feeds
            rss_feeds = [
                "https://feeds.finance.yahoo.com/rss/2.0/headline",
                "https://www.cnbc.com/id/100003114/device/rss/rss.html",
                "https://www.marketwatch.com/rss/topstories",
            ]
            
            analyzer = SentimentIntensityAnalyzer()
            
            for feed_url in rss_feeds:
                try:
                    print(f"   📡 Fetching from {feed_url}")
                    feed = feedparser.parse(feed_url)
                    
                    for entry in feed.entries[:50]:  # Limit to 50 per feed
                        title = entry.title
                        description = getattr(entry, 'description', '')
                        
                        # Sentiment analysis
                        text = f"{title} {description}"
                        
                        # VADER sentiment
                        vader_scores = analyzer.polarity_scores(text)
                        
                        # TextBlob sentiment
                        blob = TextBlob(text)
                        
                        news_data.append({
                            'title': title,
                            'description': description,
                            'text': text,
                            'vader_positive': vader_scores['pos'],
                            'vader_negative': vader_scores['neg'],
                            'vader_neutral': vader_scores['neu'],
                            'vader_compound': vader_scores['compound'],
                            'textblob_polarity': blob.sentiment.polarity,
                            'textblob_subjectivity': blob.sentiment.subjectivity,
                            'timestamp': datetime.now().isoformat(),
                            'source': feed_url
                        })
                    
                    print(f"   ✅ Fetched {len(feed.entries[:50])} articles")
                    
                except Exception as e:
                    print(f"   ⚠️ Feed error: {e}")
                    continue
            
            # Convert to DataFrame
            news_df = pd.DataFrame(news_data)
            
            # Save to file
            dataset_path = f"{self.data_dir}/news_sentiment_data.pkl"
            news_df.to_pickle(dataset_path)
            
            print(f"✅ News sentiment data saved: {len(news_df)} articles")
            return {
                "success": True,
                "path": dataset_path,
                "total_articles": len(news_df),
                "sources": len(rss_feeds)
            }
            
        except Exception as e:
            print(f"❌ News sentiment data download failed: {e}")
            return {"success": False, "error": str(e)}
    
    def prepare_trading_environment_data(self) -> Dict[str, Any]:
        """آماده‌سازی داده‌های محیط معاملاتی واقعی"""
        print("🤖 Preparing REAL trading environment data...")
        
        try:
            # Load financial data
            financial_path = f"{self.data_dir}/financial_data.pkl"
            if not os.path.exists(financial_path):
                print("   📊 Financial data not found, downloading...")
                self.download_financial_data()
            
            financial_data = pd.read_pickle(financial_path)
            
            # Create trading environment dataset
            trading_data = {}
            
            for symbol, data in financial_data.items():
                print(f"   🔧 Processing {symbol}...")
                
                # Create features for RL
                features = []
                rewards = []
                actions = []
                
                for i in range(50, len(data) - 1):  # Skip first 50 for indicators
                    # State features (last 50 periods)
                    state_data = data.iloc[i-50:i]
                    
                    # Normalize features
                    features_vector = [
                        (state_data['Close'].iloc[-1] - state_data['Close'].mean()) / state_data['Close'].std(),
                        (state_data['Volume'].iloc[-1] - state_data['Volume'].mean()) / state_data['Volume'].std(),
                        state_data['RSI'].iloc[-1] / 100.0,
                        state_data['MACD'].iloc[-1],
                        state_data['Volatility'].iloc[-1],
                        (state_data['SMA_20'].iloc[-1] - state_data['SMA_50'].iloc[-1]) / state_data['Close'].iloc[-1]
                    ]
                    
                    features.append(features_vector)
                    
                    # Calculate reward (next period return)
                    current_price = data['Close'].iloc[i]
                    next_price = data['Close'].iloc[i + 1]
                    reward = (next_price - current_price) / current_price
                    rewards.append(reward)
                    
                    # Simple action based on price movement
                    if reward > 0.001:  # Buy
                        action = 1
                    elif reward < -0.001:  # Sell
                        action = 2
                    else:  # Hold
                        action = 0
                    
                    actions.append(action)
                
                trading_data[symbol] = {
                    'features': np.array(features),
                    'rewards': np.array(rewards),
                    'actions': np.array(actions)
                }
                
                print(f"   ✅ {symbol}: {len(features)} trading samples")
            
            # Save trading data
            dataset_path = f"{self.data_dir}/trading_environment_data.pkl"
            pd.to_pickle(trading_data, dataset_path)
            
            total_samples = sum(len(data['features']) for data in trading_data.values())
            
            print(f"✅ Trading environment data saved: {total_samples} samples")
            return {
                "success": True,
                "path": dataset_path,
                "symbols": list(trading_data.keys()),
                "total_samples": total_samples
            }
            
        except Exception as e:
            print(f"❌ Trading environment data preparation failed: {e}")
            return {"success": False, "error": str(e)}
    
    def _calculate_rsi(self, prices, period=14):
        """محاسبه RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices, fast=12, slow=26):
        """محاسبه MACD"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        return macd
    
    def download_all_datasets(self) -> Dict[str, Any]:
        """دانلود همه دیتاست‌ها"""
        print("🔥 DOWNLOADING ALL REAL DATASETS")
        print("=" * 50)
        
        results = {}
        
        # 1. Financial data
        results['financial'] = self.download_financial_data()
        
        # 2. News sentiment data
        results['news_sentiment'] = self.download_news_sentiment_data()
        
        # 3. Trading environment data
        results['trading_environment'] = self.prepare_trading_environment_data()
        
        # Summary
        successful = sum(1 for r in results.values() if r.get('success', False))
        total = len(results)
        
        print(f"\n📊 DATASET DOWNLOAD SUMMARY:")
        print(f"   ✅ Successful: {successful}/{total}")
        
        for name, result in results.items():
            if result.get('success'):
                print(f"   ✅ {name}: Ready")
            else:
                print(f"   ❌ {name}: Failed - {result.get('error', 'Unknown')}")
        
        return {
            "success": successful == total,
            "results": results,
            "data_dir": self.data_dir
        }

def download_real_datasets():
    """تابع اصلی دانلود دیتاست‌های واقعی"""
    print("🔥 PEARL-3X7B REAL DATASET DOWNLOADER")
    print("=" * 60)
    print("📊 Downloading REAL datasets for proper training")
    print()
    
    manager = RealDatasetManager()
    return manager.download_all_datasets()

# اجرای خودکار
if __name__ == "__main__":
    if IN_COLAB:
        print("🚀 Ready to download real datasets!")
        print("Run: download_real_datasets()")
    else:
        print("⚠️ This script is designed for Google Colab")
        download_real_datasets()
