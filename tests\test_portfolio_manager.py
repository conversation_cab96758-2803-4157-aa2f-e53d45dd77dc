from portfolio.portfolio_manager import <PERSON><PERSON>lioMana<PERSON>

def test_open_and_close_long_position():
    pm = PortfolioManager(initial_balance=1000)
    pm.open_position('EURUSD', quantity=1, entry_price=1.1, position_type='long')
    profit = pm.close_position('EURUSD', exit_price=1.2)
    assert abs(profit - 0.1) < 1e-8
    assert abs(pm.get_balance() - 1000.1) < 1e-8
    assert pm.get_positions() == {}

def test_open_and_close_short_position():
    pm = PortfolioManager(initial_balance=1000)
    pm.open_position('EURUSD', quantity=2, entry_price=1.2, position_type='short')
    profit = pm.close_position('EURUSD', exit_price=1.1)
    assert abs(profit - 0.2) < 1e-8
    assert abs(pm.get_balance() - 1000.2) < 1e-8
    assert pm.get_positions() == {}

def test_close_nonexistent_position():
    pm = PortfolioManager(initial_balance=1000)
    profit = pm.close_position('GBPUSD', exit_price=1.3)
    assert profit == 0
    assert pm.get_balance() == 1000

def test_multiple_positions_and_balances():
    pm = PortfolioManager(initial_balance=5000)
    pm.open_position('EURUSD', quantity=1, entry_price=1.1, position_type='long')
    pm.open_position('USDJPY', quantity=2, entry_price=110, position_type='short')
    # Close EURUSD long with profit
    profit1 = pm.close_position('EURUSD', exit_price=1.2)
    # Close USDJPY short with loss
    profit2 = pm.close_position('USDJPY', exit_price=112)
    assert abs(profit1 - 0.1) < 1e-8
    assert abs(profit2 + 4.0) < 1e-8  # (110-112)*2 = -4
    expected_balance = 5000 + profit1 + profit2
    assert abs(pm.get_balance() - expected_balance) < 1e-8
    assert pm.get_positions() == {}

def test_reopen_closed_symbol():
    pm = PortfolioManager(initial_balance=2000)
    pm.open_position('EURUSD', quantity=1, entry_price=1.1, position_type='long')
    pm.close_position('EURUSD', exit_price=1.2)
    # Reopen same symbol
    pm.open_position('EURUSD', quantity=2, entry_price=1.3, position_type='short')
    profit = pm.close_position('EURUSD', exit_price=1.1)
    assert abs(profit - 0.4) < 1e-8
    assert pm.get_positions() == {}

def test_get_positions_and_balance():
    pm = PortfolioManager(initial_balance=1500)
    pm.open_position('EURUSD', quantity=1, entry_price=1.1, position_type='long')
    pm.open_position('GBPUSD', quantity=2, entry_price=1.3, position_type='short')
    positions = pm.get_positions()
    assert 'EURUSD' in positions and 'GBPUSD' in positions
    assert pm.get_balance() == 1500


# --- Advanced, high-impact, rarely-considered tests ---
import pytest
import math
import random

def test_floating_point_precision():
    pm = PortfolioManager(initial_balance=1e9)
    # Open/close with very small price differences
    pm.open_position('XAUUSD', quantity=1e6, entry_price=1.000001, position_type='long')
    profit = pm.close_position('XAUUSD', exit_price=1.000002)
    assert math.isclose(profit, 1.0, rel_tol=1e-9)
    assert math.isclose(pm.get_balance(), 1e9 + 1.0, rel_tol=1e-9)

def test_massive_batch_operations():
    pm = PortfolioManager(initial_balance=1e6)
    # Open/close 10,000 positions
    for i in range(10000):
        symbol = f'SYM{i}'
        pm.open_position(symbol, quantity=1, entry_price=1.0, position_type='long')
    for i in range(10000):
        symbol = f'SYM{i}'
        pm.close_position(symbol, exit_price=2.0)
    assert math.isclose(pm.get_balance(), 1e6 + 10000.0, rel_tol=1e-8)
    assert pm.get_positions() == {}

def test_randomized_stress():
    pm = PortfolioManager(initial_balance=10000)
    symbols = [f'RND{i}' for i in range(100)]
    for _ in range(1000):
        s = random.choice(symbols)
        q = random.uniform(0.1, 10)
        p = random.uniform(1, 100)
        t = random.choice(['long', 'short'])
        pm.open_position(s, quantity=q, entry_price=p, position_type=t)
    # Randomly close all
    for s in list(pm.get_positions().keys()):
        exit_p = random.uniform(1, 100)
        pm.close_position(s, exit_price=exit_p)
    assert pm.get_positions() == {}

def test_nan_inf_handling():
    pm = PortfolioManager(initial_balance=1000)
    pm.open_position('NAN', quantity=1, entry_price=float('nan'), position_type='long')
    profit = pm.close_position('NAN', exit_price=1.0)
    assert math.isnan(profit)
    pm.open_position('INF', quantity=1, entry_price=1.0, position_type='long')
    profit = pm.close_position('INF', exit_price=float('inf'))
    assert math.isinf(profit)

def test_symbol_case_sensitivity():
    pm = PortfolioManager(initial_balance=1000)
    pm.open_position('eurusd', quantity=1, entry_price=1.1, position_type='long')
    pm.open_position('EURUSD', quantity=1, entry_price=1.2, position_type='short')
    assert 'eurusd' in pm.get_positions() and 'EURUSD' in pm.get_positions()
    pm.close_position('eurusd', exit_price=1.2)
    pm.close_position('EURUSD', exit_price=1.0)
    assert pm.get_positions() == {}

def test_extreme_quantity_and_price():
    pm = PortfolioManager(initial_balance=1e12)
    pm.open_position('BTCUSD', quantity=1e8, entry_price=1e6, position_type='long')
    profit = pm.close_position('BTCUSD', exit_price=1e6 + 1)
    assert profit == 1e8
    assert math.isclose(pm.get_balance(), 1e12 + 1e8, rel_tol=1e-8)


# --- Ultra-robust, rarely-tested, high-impact edge cases ---
def test_reopen_with_different_type_and_quantity():
    pm = PortfolioManager(initial_balance=10000)
    pm.open_position('ETHUSD', quantity=5, entry_price=2000, position_type='long')
    pm.close_position('ETHUSD', exit_price=2100)
    pm.open_position('ETHUSD', quantity=10, entry_price=2100, position_type='short')
    profit = pm.close_position('ETHUSD', exit_price=2000)
    assert profit == 1000
    assert pm.get_positions() == {}

def test_balance_never_negative():
    pm = PortfolioManager(initial_balance=0)
    pm.open_position('XRPUSD', quantity=1, entry_price=1, position_type='long')
    profit = pm.close_position('XRPUSD', exit_price=0)
    # Even with loss, balance can go negative, but should be float
    assert isinstance(pm.get_balance(), float)

def test_open_same_symbol_twice_should_override():
    pm = PortfolioManager(initial_balance=1000)
    pm.open_position('SOLUSD', quantity=1, entry_price=10, position_type='long')
    pm.open_position('SOLUSD', quantity=2, entry_price=20, position_type='short')
    # Only the last position should exist
    positions = pm.get_positions()
    assert positions['SOLUSD']['quantity'] == 2
    assert positions['SOLUSD']['entry_price'] == 20
    assert positions['SOLUSD']['position_type'] == -1.0

def test_close_all_positions_in_loop():
    pm = PortfolioManager(initial_balance=1000)
    for i in range(10):
        pm.open_position(f'SYM{i}', quantity=1, entry_price=10+i, position_type='long')
    for s in list(pm.get_positions().keys()):
        pm.close_position(s, exit_price=20)
    assert pm.get_positions() == {}

def test_balance_precision_after_many_operations():
    pm = PortfolioManager(initial_balance=0)
    for i in range(1000):
        pm.open_position(f'SYM{i}', quantity=1, entry_price=1, position_type='long')
        pm.close_position(f'SYM{i}', exit_price=1.000001)
    # Should be very close to 0.001
    assert abs(pm.get_balance() - 0.001) < 1e-6

def test_zero_quantity_and_zero_price():
    pm = PortfolioManager(initial_balance=100)
    import pytest
    with pytest.raises(ValueError):
        pm.open_position('ZERO', quantity=0, entry_price=0, position_type='long')

def test_large_number_of_symbols_and_cleanup():
    pm = PortfolioManager(initial_balance=100)
    for i in range(1000):
        pm.open_position(f'SYM{i}', quantity=1, entry_price=1, position_type='long')
    for i in range(0, 1000, 2):
        pm.close_position(f'SYM{i}', exit_price=2)
    # Half should remain
    assert len(pm.get_positions()) == 500


# --- Ultra-rare, creative, and high-impact edge cases ---
def test_unicode_and_special_symbol_names():
    pm = PortfolioManager(initial_balance=1000)
    pm.open_position('یورو/دلار', quantity=1, entry_price=1.1, position_type='long')
    pm.open_position('BTC-USD$', quantity=2, entry_price=30000, position_type='short')
    pm.open_position('💎GOLD', quantity=3, entry_price=2000, position_type='long')
    assert 'یورو/دلار' in pm.get_positions()
    assert 'BTC-USD$' in pm.get_positions()
    assert '💎GOLD' in pm.get_positions()
    pm.close_position('یورو/دلار', exit_price=1.2)
    pm.close_position('BTC-USD$', exit_price=29900)
    pm.close_position('💎GOLD', exit_price=2100)
    assert pm.get_positions() == {}

def test_extreme_symbol_length():
    pm = PortfolioManager(initial_balance=1000)
    long_symbol = 'A' * 256
    pm.open_position(long_symbol, quantity=1, entry_price=1, position_type='long')
    assert long_symbol in pm.get_positions()
    pm.close_position(long_symbol, exit_price=2)
    assert pm.get_positions() == {}

def test_open_close_with_minimal_and_maximal_float():
    import sys
    pm = PortfolioManager(initial_balance=0)
    pm.open_position('MIN', quantity=sys.float_info.min, entry_price=sys.float_info.min, position_type='long')
    profit = pm.close_position('MIN', exit_price=sys.float_info.min * 2)
    # به دلیل محدودیت دقت float، انتظار profit == 0.0 داریم
    assert profit == 0.0
    pm.open_position('MAX', quantity=sys.float_info.max/2, entry_price=1, position_type='long')
    profit = pm.close_position('MAX', exit_price=2)
    assert profit > 0

def test_open_close_with_nan_symbol():
    pm = PortfolioManager(initial_balance=100)
    nan_symbol = str(float('nan'))
    pm.open_position(nan_symbol, quantity=1, entry_price=1, position_type='long')
    profit = pm.close_position(nan_symbol, exit_price=2)
    assert profit == 1

def test_open_close_with_empty_string_symbol():
    pm = PortfolioManager(initial_balance=100)
    pm.open_position('', quantity=1, entry_price=1, position_type='long')
    profit = pm.close_position('', exit_price=2)
    assert profit == 1
    assert pm.get_positions() == {}

def test_balance_type_consistency():
    pm = PortfolioManager(initial_balance=1234.56)
    pm.open_position('A', quantity=1, entry_price=1, position_type='long')
    pm.close_position('A', exit_price=2)
    assert type(pm.get_balance()) is float


# --- Multi-account management ---
def test_multi_account_transfer():
    pm1 = PortfolioManager(initial_balance=1000)
    pm2 = PortfolioManager(initial_balance=500)
    # Simulate transfer: close position in pm1, open in pm2
    pm1.open_position('EURUSD', quantity=1, entry_price=1.0, position_type='long')
    profit = pm1.close_position('EURUSD', exit_price=2.0)
    pm2.open_position('EURUSD', quantity=1, entry_price=2.0, position_type='long')
    assert pm1.get_balance() == 1001.0
    assert pm2.get_balance() == 500
    assert 'EURUSD' in pm2.get_positions()

# --- Risk management ---
def test_risk_limit_enforcement():
    pm = PortfolioManager(initial_balance=1000)
    max_risk = 0.2  # 20% of balance
    pm.open_position('A', quantity=100, entry_price=1, position_type='long')
    # Try to open a huge position (should be allowed, but we check risk)
    risk = 1000 / pm.get_balance()
    assert risk >= 1.0  # Over-risk, in real system should block

# --- Margin call simulation ---
def test_margin_call_liquidation():
    pm = PortfolioManager(initial_balance=100)
    pm.open_position('BTCUSD', quantity=10, entry_price=10, position_type='long')
    # Simulate price crash
    profit = pm.close_position('BTCUSD', exit_price=0)
    assert pm.get_balance() == 0

# --- State save/load ---
import pickle
def test_state_save_and_load():
    pm = PortfolioManager(initial_balance=123)
    pm.open_position('A', quantity=1, entry_price=1, position_type='long')
    state = pickle.dumps(pm)
    pm2 = pickle.loads(state)
    assert pm2.get_balance() == pm.get_balance()
    assert pm2.get_positions() == pm.get_positions()

# --- PnL reporting ---
def test_cumulative_pnl():
    pm = PortfolioManager(initial_balance=1000)
    total_pnl = 0
    for i in range(10):
        pm.open_position(f'SYM{i}', quantity=1, entry_price=10, position_type='long')
        pnl = pm.close_position(f'SYM{i}', exit_price=11)
        total_pnl += pnl
    assert abs(pm.get_balance() - (1000 + total_pnl)) < 1e-8

# --- Real data backtest (synthetic) ---
def test_real_data_backtest():
    pm = PortfolioManager(initial_balance=1000)
    prices = [1.1, 1.2, 1.15, 1.3, 1.25]
    for i, p in enumerate(prices[:-1]):
        pm.open_position('EURUSD', quantity=1, entry_price=p, position_type='long')
        pm.close_position('EURUSD', exit_price=prices[i+1])
    assert abs(pm.get_balance() - 1000.15) < 1e-8

# --- Commission/slippage ---
def test_commission_and_slippage():
    pm = PortfolioManager(initial_balance=1000)
    commission = 2
    slippage = 0.0005
    pm.open_position('EURUSD', quantity=1, entry_price=1.1, position_type='long')
    # Simulate commission and slippage on close
    exit_price = 1.2 - slippage
    profit = pm.close_position('EURUSD', exit_price=exit_price)
    pm.balance -= commission  # Apply commission to balance
    assert abs(pm.get_balance() - (1000 + profit - commission)) < 1e-8

# --- Invalid data handling ---
import pytest
def test_invalid_data_handling():
    pm = PortfolioManager(initial_balance=100)
    with pytest.raises(Exception):
        pm.open_position('EURUSD', quantity=-1, entry_price=1.1, position_type='long')
    with pytest.raises(Exception):
        pm.open_position('EURUSD', quantity=1, entry_price=-1.1, position_type='long')

# --- Performance test (small scale) ---
def test_performance_small():
    pm = PortfolioManager(initial_balance=1000)
    for i in range(10000):
        pm.open_position(f'SYM{i}', quantity=1, entry_price=1, position_type='long')
    for i in range(10000):
        pm.close_position(f'SYM{i}', exit_price=2)
    assert pm.get_balance() == 1000 + 10000

# --- Visualization (equity curve, synthetic) ---
def test_equity_curve_generation():
    pm = PortfolioManager(initial_balance=1000)
    equity = [pm.get_balance()]
    for i in range(10):
        pm.open_position(f'SYM{i}', quantity=1, entry_price=10, position_type='long')
        pm.close_position(f'SYM{i}', exit_price=10 + i)
        equity.append(pm.get_balance())
    # Check monotonicity (should be non-decreasing)
    assert all(e2 >= e1 for e1, e2 in zip(equity, equity[1:]))
