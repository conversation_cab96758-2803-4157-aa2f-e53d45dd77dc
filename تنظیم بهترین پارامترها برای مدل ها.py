"""
تنظیم بهترین پارامترها برای مدل‌های هوش مصنوعی در ترید
این فایل شامل بهترین تنظیمات و پارامترهای بهینه برای مدل‌های مختلف هوش مصنوعی
در زمینه ترید و پیش‌بینی بازارهای مالی است.
"""

# 1. LSTM (Long Short-Term Memory)
LSTM_OPTIMAL_PARAMS = {
    # Architecture
    "hidden_size": 64,           # بهینه برای financial data
    "num_layers": 2,             # 2-3 لایه بهینه است
    "dropout": 0.2,              # جلوگیری از overfitting
    "bidirectional": False,      # برای real-time trading
    
    # Training
    "learning_rate": 0.001,      # کاهش یافته از 0.005
    "batch_size": 32,            # بهینه برای memory و accuracy
    "sequence_length": 60,       # 60 time steps برای financial data
    "epochs": 100,               # با early stopping
    
    # Optimization
    "optimizer": "Adam",         # بهترین برای LSTM
    "weight_decay": 1e-5,        # L2 regularization
    "gradient_clipping": 1.0,    # جلوگیری از gradient explosion
    
    # Loss Function
    "loss_function": "MSE",      # برای regression
    "scheduler": "ReduceLROnPlateau",  # adaptive learning rate
}

# 2. GRU (Gated Recurrent Unit)
GRU_OPTIMAL_PARAMS = {
    # Architecture
    "hidden_size": 128,          # کمی بیشتر از LSTM
    "num_layers": 2,             # 2 لایه بهینه
    "dropout": 0.3,              # کمی بیشتر از LSTM
    "bidirectional": False,      # برای real-time
    
    # Training
    "learning_rate": 0.0005,     # کمتر از LSTM
    "batch_size": 64,            # بزرگتر از LSTM
    "sequence_length": 50,       # کمتر از LSTM
    "epochs": 150,               # بیشتر از LSTM
    
    # Optimization
    "optimizer": "AdamW",        # بهتر از Adam برای GRU
    "weight_decay": 1e-4,        # کمی بیشتر
    "gradient_clipping": 0.5,    # محافظه‌کارانه‌تر
}

# 3. DQN (Deep Q-Network)
DQN_OPTIMAL_PARAMS = {
    # Network Architecture
    "hidden_layers": [256, 128, 64],  # 3-layer network
    "activation": "ReLU",             # بهترین برای DQN
    "dueling": True,                  # Dueling DQN architecture
    
    # Training Parameters
    "learning_rate": 0.0001,          # کم برای stability
    "batch_size": 32,                 # استاندارد
    "memory_size": 100000,            # replay buffer size
    "target_update_freq": 1000,       # target network update
    
    # Exploration
    "epsilon_start": 1.0,             # شروع exploration
    "epsilon_end": 0.01,              # حداقل exploration
    "epsilon_decay": 0.995,           # نرخ کاهش
    
    # Reward Engineering
    "reward_scaling": 10,             # کاهش یافته از 100
    "reward_clipping": [-1.0, 1.0],   # جلوگیری از extreme rewards
    "gamma": 0.99,                    # discount factor
}

# 4. PPO (Proximal Policy Optimization)
PPO_OPTIMAL_PARAMS = {
    # Network Architecture
    "policy_layers": [256, 128],      # policy network
    "value_layers": [256, 128],       # value network
    "activation": "Tanh",             # بهتر از ReLU برای PPO
    
    # Training Parameters
    "learning_rate": 3e-4,            # استاندارد PPO
    "batch_size": 64,                 # بزرگتر از DQN
    "n_steps": 2048,                  # steps per update
    "n_epochs": 10,                   # optimization epochs
    
    # PPO Specific
    "clip_range": 0.2,                # clipping parameter
    "vf_coef": 0.5,                   # value function coefficient
    "ent_coef": 0.01,                 # entropy coefficient
    "max_grad_norm": 0.5,             # gradient clipping
    
    # Environment
    "gamma": 0.99,                    # discount factor
    "gae_lambda": 0.95,               # GAE parameter
}

# 5. TD3 (Twin Delayed Deep Deterministic)
TD3_OPTIMAL_PARAMS = {
    # Network Architecture
    "actor_layers": [400, 300],       # actor network
    "critic_layers": [400, 300],      # critic networks (twin)
    "activation": "ReLU",             # استاندارد
    
    # Training Parameters
    "learning_rate_actor": 1e-3,      # actor learning rate
    "learning_rate_critic": 1e-3,     # critic learning rate
    "batch_size": 256,                # بزرگ برای stability
    "memory_size": 1000000,           # بزرگ replay buffer
    
    # TD3 Specific
    "policy_delay": 2,                # delayed policy updates
    "target_policy_noise": 0.2,       # target policy smoothing
    "noise_clip": 0.5,                # noise clipping
    "tau": 0.005,                     # soft update coefficient
    
    # Exploration
    "exploration_noise": 0.1,         # action noise
    "gamma": 0.99,                    # discount factor
}

# 6. FinBERT
FINBERT_OPTIMAL_PARAMS = {
    # Model Configuration
    "model_name": "ProsusAI/finbert",
    "max_length": 512,                # maximum sequence length
    "num_labels": 3,                  # positive, negative, neutral
    
    # Training Parameters
    "learning_rate": 2e-5,            # کم برای pre-trained model
    "batch_size": 16,                 # کم به دلیل memory
    "epochs": 3,                      # کم برای fine-tuning
    "warmup_steps": 500,              # learning rate warmup
    
    # Optimization
    "optimizer": "AdamW",             # بهترین برای BERT
    "weight_decay": 0.01,             # regularization
    "adam_epsilon": 1e-8,             # Adam parameter
    
    # Fine-tuning
    "freeze_embeddings": False,       # fine-tune embeddings
    "gradient_accumulation": 2,       # effective batch size
}

# 7. Chronos (Time Series Transformer)
CHRONOS_OPTIMAL_PARAMS = {
    # Model Selection
    "model_size": "chronos-t5-small", # balance between speed and accuracy
    "context_length": 512,            # input sequence length
    "prediction_length": 24,          # forecast horizon
    
    # Inference Parameters
    "num_samples": 20,                # number of forecast samples
    "temperature": 1.0,               # sampling temperature
    "top_k": 50,                      # top-k sampling
    "top_p": 1.0,                     # nucleus sampling
    
    # Processing
    "batch_size": 32,                 # inference batch size
    "device": "cuda",                 # GPU acceleration
}

# 8. AutoGluon
AUTOGLUON_OPTIMAL_PARAMS = {
    # Predictor Configuration
    "prediction_length": 24,          # forecast horizon
    "eval_metric": "MASE",            # evaluation metric
    "quantile_levels": [0.1, 0.5, 0.9], # prediction intervals
    
    # Training Configuration
    "presets": "high_quality",        # balance quality and time
    "time_limit": 3600,               # 1 hour training limit
    "num_val_windows": 3,             # validation windows
    
    # Model Selection
    "excluded_model_types": ["AutoETS"], # exclude problematic models
    "hyperparameters": {
        "DeepAR": {
            "epochs": 100,
            "learning_rate": 1e-3,
            "hidden_size": 40,
        },
        "TemporalFusionTransformer": {
            "hidden_size": 64,
            "attention_head_size": 4,
            "dropout": 0.1,
        }
    }
}

# 9. PyCaret
PYCARET_OPTIMAL_PARAMS = {
    # Setup Parameters
    "fold_strategy": "timeseries",    # time series cross-validation
    "fold": 3,                        # number of folds
    "fh": 24,                         # forecast horizon
    
    # Model Configuration
    "seasonal_periods": [24, 168],    # daily and weekly seasonality
    "numeric_imputation": "mean",     # handle missing values
    "transformation": True,           # apply transformations
    
    # Hyperparameter Tuning
    "optimize": "MASE",               # optimization metric
    "n_iter": 50,                     # tuning iterations
    "search_library": "optuna",       # optimization library
    
    # Ensemble Configuration
    "ensemble": True,                 # enable ensemble
    "method": "Bagging",              # ensemble method
}

# 10. Optuna (Hyperparameter Optimization)
OPTUNA_OPTIMAL_PARAMS = {
    # Study Configuration
    "direction": "minimize",          # minimize loss
    "sampler": "TPESampler",          # Tree-structured Parzen Estimator
    "pruner": "MedianPruner",         # early stopping
    
    # Optimization Parameters
    "n_trials": 100,                  # number of trials
    "timeout": 3600,                  # 1 hour timeout
    "n_jobs": -1,                     # parallel execution
    
    # Search Space Examples
    "search_spaces": {
        "learning_rate": ["float", 1e-5, 1e-2, "log"],
        "batch_size": ["categorical", [16, 32, 64, 128]],
        "hidden_size": ["int", 32, 256],
        "dropout": ["float", 0.1, 0.5],
    }
}

# نکات مهم برای استفاده از مدل‌ها
IMPORTANT_NOTES = """
🔥 نکات مهم برای Trading Bot:

1. Learning Rate: همیشه کم شروع کنید (1e-4 تا 1e-3)
2. Batch Size: 32-64 برای اکثر مدل‌ها بهینه است
3. Dropout: 0.2-0.3 برای جلوگیری از overfitting
4. Sequence Length: 50-60 برای financial time series
5. Early Stopping: همیشه فعال کنید
6. Gradient Clipping: برای stability ضروری است
7. Reward Clipping: در RL models حتمی است

⚠️ هشدارهای مهم:
- هرگز learning rate بالای 0.01 استفاده نکنید
- همیشه validation set جداگانه داشته باشید
- از cross-validation برای time series استفاده نکنید
- reward function را ساده نگه دارید
- memory usage را مانیتور کنید
"""

print(IMPORTANT_NOTES)
