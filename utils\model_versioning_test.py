#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 Model Versioning System Test
تست سیستم مدیریت نسخه‌بندی مدل‌ها
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_model_versioning_system():
    """تست سیستم versioning مدل‌ها"""
    print("📦 Testing Model Versioning System")
    print("=" * 50)
    
    try:
        # Import the model versioning system
        from core.model_versioning import (
            ModelRegistry, 
            ModelMetadata,
            ModelMetrics,
            ModelType,
            ModelStatus,
            ModelStage,
            SerializationFormat,
            ModelSerializer,
            model_context,
            MLflowIntegration
        )
        
        # Create temporary directory for testing
        test_dir = tempfile.mkdtemp()
        print(f"   Using test directory: {test_dir}")
        
        # Test 1: Model Registry Creation
        print("\n1️⃣ Testing Model Registry Creation...")
        
        registry = ModelRegistry(base_path=test_dir)
        print(f"   ✓ Registry created at: {registry.base_path}")
        
        # Test 2: Model Metadata Creation
        print("\n2️⃣ Testing Model Metadata...")
        
        metrics = ModelMetrics(
            accuracy=0.95,
            precision=0.92,
            recall=0.88,
            f1_score=0.90,
            mse=0.1,
            custom_metrics={"custom_score": 0.85}
        )
        
        metadata = ModelMetadata(
            name="test_regression_model",
            version="1.0",
            model_type=ModelType.REGRESSION,
            description="Test regression model for versioning",
            author="Test System",
            status=ModelStatus.TRAINED,
            stage=ModelStage.STAGING,
            tags=["regression", "test", "v1"],
            parameters={"learning_rate": 0.01, "max_depth": 5},
            metrics=metrics,
            dependencies=["numpy", "pandas", "scikit-learn"]
        )
        
        print(f"   ✓ Metadata created: {metadata.name}")
        print(f"   ✓ Model type: {metadata.model_type.value}")
        print(f"   ✓ Metrics: {metadata.metrics.to_dict()}")
        
        # Test 3: Simple Model Creation
        print("\n3️⃣ Testing Simple Model Creation...")
        
        class SimpleRegressionModel:
            def __init__(self, slope=1.0, intercept=0.0):
                self.slope = slope
                self.intercept = intercept
                self.is_trained = False
                
            def train(self, X, y):
                # Simple training simulation
                self.is_trained = True
                return {"loss": 0.1, "accuracy": 0.95}
                
            def predict(self, X):
                if not self.is_trained:
                    raise ValueError("Model not trained")
                return [self.slope * x + self.intercept for x in X]
            
            def __str__(self):
                return f"SimpleRegressionModel(slope={self.slope}, intercept={self.intercept})"
        
        model = SimpleRegressionModel(slope=2.0, intercept=1.0)
        model.train([1, 2, 3, 4, 5], [3, 5, 7, 9, 11])
        
        print(f"   ✓ Model created: {model}")
        print(f"   ✓ Model trained: {model.is_trained}")
        
        # Test 4: Model Registration
        print("\n4️⃣ Testing Model Registration...")
        
        model_id = registry.register_model("test_regression_model", "1.0", model, metadata)
        print(f"   ✓ Model registered with ID: {model_id}")
        
        # Test 5: Model Loading
        print("\n5️⃣ Testing Model Loading...")
        
        loaded_model = registry.load_model("test_regression_model", "1.0")
        if loaded_model:
            print(f"   ✓ Model loaded successfully: {loaded_model}")
            
            # Test prediction
            test_data = [1, 2, 3]
            predictions = loaded_model.predict(test_data)
            print(f"   ✓ Model predictions: {predictions}")
        else:
            print("   ❌ Failed to load model")
        
        # Test 6: Model Retrieval
        print("\n6️⃣ Testing Model Retrieval...")
        
        # Get by name and version
        model_version = registry.get_model("test_regression_model", "1.0")
        if model_version:
            print(f"   ✓ Model retrieved: {model_version.get_full_name()}")
            print(f"   ✓ Model stage: {model_version.metadata.stage.value}")
        
        # Get latest version
        latest_model = registry.get_model("test_regression_model")
        if latest_model:
            print(f"   ✓ Latest model: {latest_model.get_full_name()}")
        
        # Test 7: Model Stage Update
        print("\n7️⃣ Testing Model Stage Update...")
        
        stage_updated = registry.update_model_stage("test_regression_model", "1.0", ModelStage.PRODUCTION)
        print(f"   ✓ Stage updated to production: {stage_updated}")
        
        # Verify stage update
        updated_model = registry.get_model("test_regression_model", "1.0")
        if updated_model:
            print(f"   ✓ Current stage: {updated_model.metadata.stage.value}")
        
        # Test 8: Multiple Model Versions
        print("\n8️⃣ Testing Multiple Model Versions...")
        
        # Create version 2.0
        model_v2 = SimpleRegressionModel(slope=2.5, intercept=0.5)
        model_v2.train([1, 2, 3, 4, 5], [3, 5.5, 8, 10.5, 13])
        
        metadata_v2 = ModelMetadata(
            name="test_regression_model",
            version="2.0",
            model_type=ModelType.REGRESSION,
            description="Improved test regression model",
            author="Test System",
            status=ModelStatus.TRAINED,
            stage=ModelStage.STAGING,
            metrics=ModelMetrics(accuracy=0.97, mse=0.08)
        )
        
        model_id_v2 = registry.register_model("test_regression_model", "2.0", model_v2, metadata_v2)
        print(f"   ✓ Version 2.0 registered: {model_id_v2}")
        
        # Create version 3.0
        model_v3 = SimpleRegressionModel(slope=3.0, intercept=0.0)
        model_v3.train([1, 2, 3, 4, 5], [3, 6, 9, 12, 15])
        
        metadata_v3 = ModelMetadata(
            name="test_regression_model",
            version="3.0",
            model_type=ModelType.REGRESSION,
            description="Latest test regression model",
            author="Test System",
            status=ModelStatus.TRAINED,
            stage=ModelStage.STAGING,
            metrics=ModelMetrics(accuracy=0.98, mse=0.05)
        )
        
        model_id_v3 = registry.register_model("test_regression_model", "3.0", model_v3, metadata_v3)
        print(f"   ✓ Version 3.0 registered: {model_id_v3}")
        
        # Test 9: Model Listing
        print("\n9️⃣ Testing Model Listing...")
        
        all_models = registry.list_models()
        print(f"   ✓ Total models: {len(all_models)}")
        
        for model_meta in all_models:
            print(f"     - {model_meta.name}:v{model_meta.version} ({model_meta.stage.value})")
        
        # Filter by stage
        production_models = registry.list_models(stage_filter=ModelStage.PRODUCTION)
        print(f"   ✓ Production models: {len(production_models)}")
        
        # Test 10: Model Comparison
        print("\n🔟 Testing Model Comparison...")
        
        comparison = registry.compare_models("test_regression_model", "1.0", "test_regression_model", "3.0")
        print(f"   ✓ Comparison completed")
        
        if "differences" in comparison:
            print(f"   ✓ Differences found: {len(comparison['differences'])}")
            for metric, diff in comparison["differences"].items():
                print(f"     - {metric}: {diff}")
        
        # Test 11: Model Lineage
        print("\n1️⃣1️⃣ Testing Model Lineage...")
        
        lineage = registry.get_model_lineage("test_regression_model")
        print(f"   ✓ Model lineage: {len(lineage)} versions")
        
        for i, version_meta in enumerate(lineage):
            print(f"     {i+1}. v{version_meta.version} - {version_meta.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Test 12: Model Serialization
        print("\n1️⃣2️⃣ Testing Model Serialization...")
        
        # Test different serialization formats
        test_model = SimpleRegressionModel(slope=1.5, intercept=2.0)
        test_model.train([1, 2, 3], [3.5, 5.0, 6.5])
        
        # Test pickle serialization
        pickle_path = os.path.join(test_dir, "test_model.pkl")
        pickle_success = ModelSerializer.serialize(test_model, pickle_path, SerializationFormat.PICKLE)
        print(f"   ✓ Pickle serialization: {pickle_success}")
        
        if pickle_success:
            loaded_pickle = ModelSerializer.deserialize(pickle_path, SerializationFormat.PICKLE)
            if loaded_pickle:
                print(f"   ✓ Pickle deserialization successful")
                test_pred = loaded_pickle.predict([1, 2, 3])
                print(f"   ✓ Loaded model predictions: {test_pred}")
        
        # Test 13: Model Context Manager
        print("\n1️⃣3️⃣ Testing Model Context Manager...")
        
        try:
            with model_context("test_regression_model", "1.0") as ctx_model:
                print(f"   ✓ Model context created: {ctx_model}")
                ctx_predictions = ctx_model.predict([1, 2, 3])
                print(f"   ✓ Context predictions: {ctx_predictions}")
        except Exception as e:
            print(f"   ❌ Model context error: {e}")
        
        # Test 14: MLflow Integration
        print("\n1️⃣4️⃣ Testing MLflow Integration...")
        
        mlflow = MLflowIntegration()
        mlflow.start_run(run_name="test_run")
        
        # Log metrics and parameters
        mlflow.log_metric("accuracy", 0.95)
        mlflow.log_metric("loss", 0.1)
        mlflow.log_param("learning_rate", 0.01)
        mlflow.log_param("batch_size", 32)
        
        # Log model
        mlflow.log_model(test_model, "model", "mlflow_test_model")
        
        mlflow.end_run()
        print(f"   ✓ MLflow integration test completed")
        
        # Test 15: Model Export
        print("\n1️⃣5️⃣ Testing Model Export...")
        
        export_path = os.path.join(test_dir, "exported_model")
        export_success = registry.export_model("test_regression_model", "1.0", export_path)
        print(f"   ✓ Model export: {export_success}")
        
        if export_success and os.path.exists(export_path):
            exported_files = os.listdir(export_path)
            print(f"   ✓ Exported files: {exported_files}")
        
        # Test 16: Registry Statistics
        print("\n1️⃣6️⃣ Testing Registry Statistics...")
        
        stats = registry.get_statistics()
        print(f"   ✓ Registry statistics:")
        print(f"     - Total models: {stats['total_models']}")
        print(f"     - Total size: {stats['total_size_mb']:.2f} MB")
        print(f"     - Unique names: {stats['unique_names']}")
        print(f"     - Stage distribution: {stats['stage_distribution']}")
        print(f"     - Type distribution: {stats['type_distribution']}")
        
        # Test 17: Model Deletion
        print("\n1️⃣7️⃣ Testing Model Deletion...")
        
        # Delete version 2.0
        delete_success = registry.delete_model("test_regression_model", "2.0")
        print(f"   ✓ Model deletion: {delete_success}")
        
        # Verify deletion
        deleted_model = registry.get_model("test_regression_model", "2.0")
        print(f"   ✓ Model deleted verification: {deleted_model is None}")
        
        # Final model count
        final_models = registry.list_models()
        print(f"   ✓ Final model count: {len(final_models)}")
        
        print("\n✅ All Model Versioning System tests passed!")
        
        # Cleanup
        shutil.rmtree(test_dir)
        print(f"   ✓ Test directory cleaned up")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_serialization():
    """تست سریال‌سازی مدل"""
    print("\n🔧 Testing Model Serialization...")
    
    try:
        from core.model_versioning import ModelSerializer, SerializationFormat
        
        # Create test model
        class TestModel:
            def __init__(self, value=42):
                self.value = value
                
            def predict(self, x):
                return x * self.value
        
        model = TestModel(value=10)
        
        # Test different serialization formats
        test_dir = tempfile.mkdtemp()
        
        # Test Pickle
        pickle_path = os.path.join(test_dir, "model.pkl")
        pickle_result = ModelSerializer.serialize(model, pickle_path, SerializationFormat.PICKLE)
        print(f"   ✓ Pickle serialization: {pickle_result}")
        
        if pickle_result:
            loaded_model = ModelSerializer.deserialize(pickle_path, SerializationFormat.PICKLE)
            if loaded_model:
                print(f"   ✓ Pickle deserialization: {loaded_model.predict(5)}")
        
        # Test Joblib
        joblib_path = os.path.join(test_dir, "model.joblib")
        joblib_result = ModelSerializer.serialize(model, joblib_path, SerializationFormat.JOBLIB)
        print(f"   ✓ Joblib serialization: {joblib_result}")
        
        if joblib_result:
            loaded_joblib = ModelSerializer.deserialize(joblib_path, SerializationFormat.JOBLIB)
            if loaded_joblib:
                print(f"   ✓ Joblib deserialization: {loaded_joblib.predict(5)}")
        
        # Cleanup
        shutil.rmtree(test_dir)
        print("✅ Model serialization test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Model serialization test error: {e}")
        return False

def test_model_performance():
    """تست عملکرد مدل"""
    print("\n⚡ Testing Model Performance...")
    
    try:
        from core.model_versioning import ModelRegistry, ModelMetadata, ModelType, ModelMetrics
        import time
        
        # Create test registry
        test_dir = tempfile.mkdtemp()
        registry = ModelRegistry(base_path=test_dir)
        
        # Create multiple models
        num_models = 10
        start_time = time.time()
        
        for i in range(num_models):
            model = {"type": "test", "id": i, "data": list(range(100))}
            
            metadata = ModelMetadata(
                name=f"perf_test_model_{i}",
                version="1.0",
                model_type=ModelType.CUSTOM,
                description=f"Performance test model {i}",
                metrics=ModelMetrics(accuracy=0.9 + i * 0.01)
            )
            
            registry.register_model(f"perf_test_model_{i}", "1.0", model, metadata)
        
        registration_time = time.time() - start_time
        print(f"   ✓ Registered {num_models} models in {registration_time:.2f}s")
        
        # Test bulk loading
        start_time = time.time()
        
        loaded_models = []
        for i in range(num_models):
            model = registry.load_model(f"perf_test_model_{i}", "1.0")
            if model:
                loaded_models.append(model)
        
        loading_time = time.time() - start_time
        print(f"   ✓ Loaded {len(loaded_models)} models in {loading_time:.2f}s")
        
        # Test listing performance
        start_time = time.time()
        all_models = registry.list_models()
        listing_time = time.time() - start_time
        print(f"   ✓ Listed {len(all_models)} models in {listing_time:.3f}s")
        
        # Cleanup
        shutil.rmtree(test_dir)
        print("✅ Model performance test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Model performance test error: {e}")
        return False

def main():
    """تست اصلی"""
    print("📦 Model Versioning System Test Suite")
    print("=" * 60)
    
    # Run tests
    test_results = []
    
    # Test 1: Basic versioning functionality
    test_results.append(test_model_versioning_system())
    
    # Test 2: Model serialization
    test_results.append(test_model_serialization())
    
    # Test 3: Performance
    test_results.append(test_model_performance())
    
    # Results summary
    print("\n📊 Test Results Summary:")
    print("=" * 30)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ Tests passed: {passed}/{total}")
    print(f"📈 Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! Model Versioning System is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 