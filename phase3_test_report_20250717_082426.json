{"phase3_test_summary": {"total_duration": 16.84864044189453, "overall_success_rate": 0.6551724137931034, "test_suites_completed": 4, "test_suites_failed": 0, "timestamp": "2025-07-17T08:24:26.717494"}, "detailed_results": {"sentiment_training": {"config_creation": true, "trainer_initialization": true, "data_preparation": true, "model_initialization": true, "training_simulation": true, "evaluation_system": true, "model_saving": true, "integration_test": true, "duration": 1.794511318206787, "status": "completed", "success_rate": 1.0}, "timeseries_training": {"config_creation": true, "trainer_initialization": true, "environment_preparation": true, "model_architectures": true, "data_processing": true, "training_simulation": false, "evaluation_metrics": false, "model_saving": false, "error": "__init__() got an unexpected keyword argument 'verbose'", "duration": 8.000039339065552, "status": "completed", "success_rate": 0.625}, "rl_training": {"config_creation": true, "trainer_initialization": true, "environment_creation": true, "agent_architectures": true, "trading_environment": true, "training_simulation": false, "evaluation_system": false, "model_saving": false, "error": "mat1 and mat2 shapes cannot be multiplied (16x20 and 10x256)", "duration": 7.023178339004517, "status": "completed", "success_rate": 0.625}, "training_integration": {"cross_module_imports": true, "memory_management_integration": false, "logging_integration": false, "concurrent_training": false, "resource_cleanup": false, "error": "__init__() missing 1 required positional argument: 'algorithm'", "duration": 0.011687040328979492, "status": "completed", "success_rate": 0.2}}, "recommendations": ["⚠️ timeseries_training needs attention (success rate: 62.5%)", "⚠️ rl_training needs attention (success rate: 62.5%)", "⚠️ training_integration needs attention (success rate: 20.0%)"]}