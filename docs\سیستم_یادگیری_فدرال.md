# مستندات سیستم یادگیری فدرال (FederatedLearningSystem)

## 📋 معرفی کلی

سیستم **FederatedLearningSystem** یکی از ماژول‌های پیشرفته پروژه است که امکان اشتراک یادگیری بین چندین instance را فراهم می‌کند بدون اشتراک داده‌های خام. این سیستم از تکنیک‌های حفظ حریم خصوصی برای تجمیع مدل‌ها استفاده می‌کند.

## 🎯 مسئولیت‌های اصلی

### 1. یادگیری توزیع شده
- آموزش مدل‌ها در کلاینت‌های مختلف
- تجمیع وزن‌های مدل‌ها
- حفظ حریم خصوصی داده‌ها

### 2. مدیریت کلاینت‌ها
- ثبت و مدیریت کلاینت‌ها
- هماهنگی دورهای آموزش
- اعتبارسنجی به‌روزرسانی‌ها

### 3. تجمیع مدل‌ها
- تجمیع وزن‌ها با حفظ حریم خصوصی
- محاسبه مدل سراسری
- توزیع مدل به‌روزرسانی شده

## 🔧 کلاس‌های اصلی

### 1. ModelUpdate
```python
@dataclass
class ModelUpdate:
    client_id: str
    model_type: str
    weights: Dict[str, float]
    performance_metrics: Dict[str, float]
    timestamp: datetime
    data_size: int
    privacy_hash: str
```

### 2. FederatedModel
```python
@dataclass
class FederatedModel:
    model_type: str
    global_weights: Dict[str, float]
    version: int
    participating_clients: List[str]
    last_update: datetime
    performance_history: List[Dict]
```

### 3. PrivacyPreservingAggregator
```python
class PrivacyPreservingAggregator:
    def __init__(self, min_clients: int = 3):
        self.min_clients = min_clients
        self.noise_level = 0.01
    
    def aggregate_weights(self, updates: List[ModelUpdate]) -> Dict[str, float]:
        """تجمیع وزن‌ها با حفظ حریم خصوصی"""
```

### 4. FederatedLearningClient
```python
class FederatedLearningClient:
    def __init__(self, client_id: str, db_path: str = "federated_client.db"):
        self.client_id = client_id
        self.db_path = db_path
        self.local_models = {}
        self.performance_history = []
```

### 5. FederatedLearningServer
```python
class FederatedLearningServer:
    def __init__(self, db_path: str = "federated_server.db"):
        self.db_path = db_path
        self.aggregator = PrivacyPreservingAggregator()
        self.federated_models = {}
        self.pending_updates = {}
```

## 📊 متدهای اصلی

### 1. train_local_model()
```python
def train_local_model(self, model_type: str, training_data: Dict) -> ModelUpdate:
    """
    آموزش مدل محلی
    
    پارامترها:
    - model_type: نوع مدل ('RL_Agent', 'Prediction_Model')
    - training_data: داده‌های آموزش
    
    خروجی:
    - ModelUpdate: به‌روزرسانی مدل
    """
```

### 2. aggregate_weights()
```python
def aggregate_weights(self, updates: List[ModelUpdate]) -> Dict[str, float]:
    """
    تجمیع وزن‌ها با حفظ حریم خصوصی
    
    پارامترها:
    - updates: لیست به‌روزرسانی‌ها
    
    خروجی:
    - Dict[str, float]: وزن‌های تجمیعی
    """
```

### 3. receive_update()
```python
def receive_update(self, update: ModelUpdate) -> bool:
    """
    دریافت به‌روزرسانی از کلاینت
    
    پارامترها:
    - update: به‌روزرسانی مدل
    
    خروجی:
    - bool: موفقیت دریافت
    """
```

### 4. apply_global_update()
```python
def apply_global_update(self, global_weights: Dict[str, float], model_type: str, version: int):
    """
    اعمال به‌روزرسانی سراسری
    
    پارامترها:
    - global_weights: وزن‌های سراسری
    - model_type: نوع مدل
    - version: نسخه مدل
    """
```

## 🔒 حفظ حریم خصوصی

### 1. تجمیع با نویز
```python
def aggregate_weights(self, updates: List[ModelUpdate]) -> Dict[str, float]:
    for update in updates:
        client_weight = update.data_size / total_data_size
        for param_name, param_value in update.weights.items():
            # اضافه کردن نویز
            noisy_value = param_value + np.random.normal(0, self.noise_level)
            aggregated_weights[param_name] += client_weight * noisy_value
```

### 2. اعتبارسنجی Hash
```python
def validate_update(self, update: ModelUpdate) -> bool:
    expected_hash = self._calculate_privacy_hash(update)
    if update.privacy_hash != expected_hash:
        return False
    return True
```

### 3. محدودیت وزن‌ها
```python
for param_name, param_value in update.weights.items():
    if abs(param_value) > 10.0:  # محدوده معقول
        return False
```

## 🎨 انواع مدل‌های پشتیبانی شده

### 1. مدل RL
```python
def _train_rl_model(self, training_data: Dict) -> Dict[str, float]:
    return {
        'q_learning_rate': 0.01 + noise * 0.001,
        'exploration_rate': max(0.1, 0.9 - base_performance + noise * 0.1),
        'discount_factor': 0.95 + noise * 0.01,
        'policy_weight': base_performance + noise * 0.1
    }
```

### 2. مدل پیش‌بینی
```python
def _train_prediction_model(self, training_data: Dict) -> Dict[str, float]:
    return {
        'feature_weight_1': random.uniform(0.1, 0.9),
        'feature_weight_2': random.uniform(0.1, 0.9),
        'bias_term': random.uniform(-0.1, 0.1),
        'volatility_adjustment': market_volatility * 0.5,
        'trend_sensitivity': random.uniform(0.3, 0.7)
    }
```

## 📈 نمونه کد استفاده

```python
from utils.federated_learning_system import FederatedLearningServer, FederatedLearningClient

# ایجاد سرور
server = FederatedLearningServer("federated_server.db")

# ایجاد کلاینت‌ها
clients = []
for i in range(4):
    client = FederatedLearningClient(f"client_{i+1}", f"federated_client_{i+1}.db")
    clients.append(client)

# دور یادگیری فدرال
for round_num in range(3):
    print(f"دور {round_num + 1}")
    
    # آموزش مدل‌های محلی
    for client in clients:
        training_data = {
            'data_size': 1000,
            'base_performance': 0.7,
            'market_volatility': 0.5
        }
        
        # آموزش مدل RL
        update = client.train_local_model("RL_Agent", training_data)
        success = server.receive_update(update)
        
        if success:
            print(f"کلاینت {client.client_id} به‌روزرسانی ارسال کرد")
    
    # تجمیع به‌روزرسانی‌ها
    federated_model = server.aggregate_updates("RL_Agent")
    
    if federated_model:
        print(f"مدل فدرال نسخه {federated_model.version} ایجاد شد")
        
        # ارسال به کلاینت‌ها
        for client in clients:
            client.apply_global_update(
                federated_model.global_weights,
                "RL_Agent",
                federated_model.version
            )

# وضعیت سرور
status = server.get_server_status()
print("وضعیت سرور:", status)
```

## 🔧 تنظیمات و پیکربندی

### پارامترهای مهم:
- `min_clients`: حداقل کلاینت‌ها برای تجمیع (3)
- `noise_level`: سطح نویز برای حریم خصوصی (0.01)
- `contamination`: آستانه تشخیص مقادیر مشکوک
- `round_number`: شماره دور فعلی

### تنظیمات پایگاه داده:
- `federated_models`: مدل‌های فدرال
- `client_updates`: به‌روزرسانی‌های کلاینت‌ها
- `adaptation_history`: تاریخچه تطبیق

## 🚨 مدیریت خطا

```python
try:
    update = client.train_local_model("RL_Agent", training_data)
    success = server.receive_update(update)
    if not success:
        print("خطا در اعتبارسنجی به‌روزرسانی")
except Exception as e:
    print(f"خطا در آموزش مدل محلی: {e}")
```

## 📊 متریک‌های عملکرد

### 1. کیفیت مدل
- دقت مدل فدرال
- بهبود نسبت به مدل‌های محلی
- همگرایی آموزش

### 2. کارایی سیستم
- زمان تجمیع
- تعداد کلاینت‌های شرکت‌کننده
- اندازه داده‌های منتقل شده

### 3. حریم خصوصی
- سطح نویز اضافه شده
- تشخیص تلاش‌های مخرب
- محفوظیت داده‌ها

## 🔗 اتصال به سیستم اصلی

### فایل‌های مرتبط:
- `utils/federated_learning_system.py`: پیاده‌سازی اصلی
- `tests/test_complete_advanced_system.py`: تست‌های پیشرفته
- `utils/rl_training_utils.py`: ابزارهای آموزش
- `test_final_integration.py`: تست یکپارچه‌سازی

### وضعیت عملیاتی:
⚠️ **نیمه‌فعال** - این ماژول در تست‌های پیشرفته و آزمایشی استفاده می‌شود

## 🎯 بهترین شیوه‌های استفاده

### 1. تعداد کلاینت‌ها
```python
# حداقل 3 کلاینت برای تجمیع
if len(clients) >= 3:
    federated_model = server.aggregate_updates(model_type)
else:
    print("تعداد کلاینت‌ها کافی نیست")
```

### 2. اعتبارسنجی
```python
# بررسی صحت به‌روزرسانی
if server.aggregator.validate_update(update):
    server.receive_update(update)
else:
    print("به‌روزرسانی نامعتبر")
```

### 3. مدیریت دور
```python
# محدود کردن تعداد دورها
MAX_ROUNDS = 10
for round_num in range(MAX_ROUNDS):
    # انجام دور یادگیری
    pass
```

## 🔮 نمودار معماری

```mermaid
graph TD
    A[FederatedLearningServer] --> B[PrivacyPreservingAggregator]
    A --> C[FederatedModel Storage]
    A --> D[Client Management]
    
    E[FederatedLearningClient 1] --> F[Local Model Training]
    G[FederatedLearningClient 2] --> H[Local Model Training]
    I[FederatedLearningClient 3] --> J[Local Model Training]
    
    F --> K[ModelUpdate 1]
    H --> L[ModelUpdate 2]
    J --> M[ModelUpdate 3]
    
    K --> B
    L --> B
    M --> B
    
    B --> N[Global Model]
    N --> O[Distribution to Clients]
    
    O --> E
    O --> G
    O --> I
    
    B --> P[Privacy Noise]
    B --> Q[Weight Validation]
    B --> R[Hash Verification]
```

## 📚 مزایا و کاربردها

### مزایا:
1. **حفظ حریم خصوصی**: عدم اشتراک داده‌های خام
2. **مقیاس‌پذیری**: قابلیت اضافه کردن کلاینت‌های جدید
3. **مقاومت**: عدم وابستگی به یک نقطه شکست
4. **کیفیت**: بهبود مدل با داده‌های متنوع

### کاربردها:
1. **آموزش توزیع شده**: بین چندین سرور
2. **تجمیع استراتژی‌ها**: از منابع مختلف
3. **یادگیری مشترک**: بدون افشای داده‌ها
4. **بهبود عملکرد**: با تنوع داده‌ها

---

**نکته**: این سیستم برای یادگیری توزیع شده و حفظ حریم خصوصی طراحی شده و می‌تواند برای تجمیع دانش از منابع مختلف استفاده شود. 