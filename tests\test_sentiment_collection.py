#!/usr/bin/env python3
"""
تست جامع عملکرد سیستم جمع‌آوری sentiment data
"""

import unittest
import pandas as pd
import json
from pathlib import Path
import sys
import os
import time
from datetime import datetime

# اضافه کردن مسیر پروژه
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.sentiment_data_collector import RealSentimentDataCollector
from utils.sentiment_dataset_manager import SentimentDatasetManager

class TestSentimentCollection(unittest.TestCase):
    """تست کامل سیستم sentiment"""
    
    def setUp(self):
        """راه‌اندازی تست"""
        self.collector = RealSentimentDataCollector()
        self.manager = SentimentDatasetManager()
        self.test_symbol = 'EURUSD'
        
    def test_01_collector_initialization(self):
        """تست راه‌اندازی collector"""
        print("\n🧪 Test 1: Collector Initialization")
        
        self.assertIsNotNone(self.collector)
        self.assertTrue(hasattr(self.collector, 'symbol_keywords'))
        self.assertIn('EURUSD', self.collector.symbol_keywords)
        
        print("✅ Collector initialized successfully")
    
    def test_02_news_data_collection(self):
        """تست جمع‌آوری اخبار"""
        print("\n🧪 Test 2: News Data Collection")
        
        start_time = time.time()
        news_data = self.collector.collect_real_news_data(self.test_symbol, days_back=7)
        collection_time = time.time() - start_time
        
        self.assertIsInstance(news_data, list)
        self.assertGreater(len(news_data), 0, "No news data collected")
        
        # بررسی ساختار داده‌ها
        if news_data:
            sample_item = news_data[0]
            required_fields = ['title', 'summary', 'symbol', 'sentiment_score']
            
            for field in required_fields:
                self.assertIn(field, sample_item, f"Missing field: {field}")
        
        print(f"✅ Collected {len(news_data)} news items in {collection_time:.2f}s")
        
        # ذخیره برای تست‌های بعدی
        self.test_news_data = news_data
    
    def test_03_sentiment_analysis_quality(self):
        """تست کیفیت تحلیل sentiment"""
        print("\n🧪 Test 3: Sentiment Analysis Quality")
        
        if not hasattr(self, 'test_news_data'):
            self.test_02_news_data_collection()
        
        sentiment_scores = [item['sentiment_score'] for item in self.test_news_data]
        
        # بررسی محدوده sentiment scores
        for score in sentiment_scores:
            self.assertGreaterEqual(score, -1.0, f"Sentiment score {score} below -1")
            self.assertLessEqual(score, 1.0, f"Sentiment score {score} above 1")
        
        # بررسی توزیع
        positive_count = sum(1 for score in sentiment_scores if score > 0)
        negative_count = sum(1 for score in sentiment_scores if score < 0)
        neutral_count = sum(1 for score in sentiment_scores if score == 0)
        
        print(f"✅ Sentiment distribution: +{positive_count}, -{negative_count}, ={neutral_count}")
        
        # حداقل باید تنوع داشته باشد
        unique_scores = len(set(sentiment_scores))
        self.assertGreater(unique_scores, 1, "All sentiment scores are identical")
    
    def test_04_dataset_manager_functionality(self):
        """تست عملکرد dataset manager"""
        print("\n🧪 Test 4: Dataset Manager Functionality")
        
        # ایجاد داده‌های تست
        test_data = [
            {
                'title': 'EUR/USD rises on ECB policy',
                'summary': 'European Central Bank maintains dovish stance',
                'sentiment_score': 0.3,
                'published': datetime.now().isoformat(),
                'symbol': self.test_symbol,
                'impact': 'medium'
            }
        ]
        
        # ذخیره داده‌های تست
        filepath = self.collector.save_sentiment_dataset(self.test_symbol, test_data)
        self.assertTrue(os.path.exists(filepath), "Test dataset file not created")
        
        # بارگذاری داده‌ها
        df = self.manager.load_sentiment_data(self.test_symbol)
        self.assertFalse(df.empty, "Failed to load sentiment data")
        self.assertIn('sentiment_score', df.columns)
        
        print(f"✅ Dataset manager loaded {len(df)} records")
    
    def test_05_feature_preparation(self):
        """تست آماده‌سازی ویژگی‌ها"""
        print("\n🧪 Test 5: Feature Preparation")
        
        # بارگذاری داده‌ها
        df = self.manager.load_sentiment_data(self.test_symbol)
        
        if not df.empty:
            # آماده‌سازی ویژگی‌ها
            features_df = self.manager.prepare_sentiment_features(df, 'H1')
            
            if not features_df.empty:
                expected_columns = ['sentiment_mean', 'sentiment_std', 'news_count', 
                                  'sentiment_momentum', 'sentiment_volatility', 'impact_score']
                
                for col in expected_columns:
                    self.assertIn(col, features_df.columns, f"Missing feature column: {col}")
                
                print(f"✅ Features prepared: {list(features_df.columns)}")
            else:
                print("⚠️ No features prepared (insufficient data)")
        else:
            print("⚠️ No data available for feature preparation")
    
    def test_06_data_validation(self):
        """تست اعتبارسنجی داده‌ها"""
        print("\n🧪 Test 6: Data Validation")
        
        df = self.manager.load_sentiment_data(self.test_symbol)
        
        if not df.empty:
            validation_result = self.manager.validate_sentiment_data(df)
            
            self.assertIsInstance(validation_result, dict)
            self.assertIn('is_valid', validation_result)
            self.assertIn('stats', validation_result)
            
            if validation_result['is_valid']:
                print("✅ Data validation passed")
            else:
                print(f"⚠️ Validation issues: {validation_result['errors']}")
                
            print(f"📊 Stats: {validation_result['stats']}")
        else:
            print("⚠️ No data available for validation")
    
    def test_07_performance_benchmarks(self):
        """تست معیارهای عملکرد"""
        print("\n🧪 Test 7: Performance Benchmarks")
        
        # تست سرعت جمع‌آوری
        start_time = time.time()
        news_data = self.collector.collect_real_news_data(self.test_symbol, days_back=3)
        collection_time = time.time() - start_time
        
        # تست سرعت پردازش
        if news_data:
            start_time = time.time()
            filepath = self.collector.save_sentiment_dataset(self.test_symbol, news_data)
            save_time = time.time() - start_time
            
            # تست سرعت بارگذاری
            start_time = time.time()
            df = self.manager.load_sentiment_data(self.test_symbol)
            load_time = time.time() - start_time
            
            print(f"⏱️ Performance Metrics:")
            print(f"   Collection: {collection_time:.2f}s for {len(news_data)} items")
            print(f"   Save: {save_time:.2f}s")
            print(f"   Load: {load_time:.2f}s for {len(df)} records")
            
            # معیارهای قابل قبول
            self.assertLess(collection_time, 30, "Collection too slow")
            self.assertLess(save_time, 5, "Save operation too slow")
            self.assertLess(load_time, 5, "Load operation too slow")
            
            print("✅ Performance benchmarks passed")
        else:
            print("⚠️ No data for performance testing")
    
    def test_08_integration_test(self):
        """تست یکپارچگی کامل"""
        print("\n🧪 Test 8: Full Integration Test")
        
        symbols = ['EURUSD', 'GBPUSD']
        results = {}
        
        for symbol in symbols:
            try:
                # جمع‌آوری
                news_data = self.collector.collect_real_news_data(symbol, days_back=5)
                
                if news_data:
                    # ذخیره
                    filepath = self.collector.save_sentiment_dataset(symbol, news_data)
                    
                    # بارگذاری و پردازش
                    df = self.manager.load_sentiment_data(symbol)
                    features_df = self.manager.prepare_sentiment_features(df, 'H1')
                    
                    results[symbol] = {
                        'raw_count': len(news_data),
                        'processed_count': len(features_df) if not features_df.empty else 0,
                        'status': 'success'
                    }
                else:
                    results[symbol] = {'status': 'no_data'}
                    
            except Exception as e:
                results[symbol] = {'status': 'error', 'error': str(e)}
        
        print(f"🔗 Integration Results:")
        for symbol, result in results.items():
            print(f"   {symbol}: {result}")
        
        # حداقل یک نماد باید موفق باشد
        successful_symbols = [s for s, r in results.items() if r.get('status') == 'success']
        self.assertGreater(len(successful_symbols), 0, "No symbols processed successfully")
        
        print("✅ Integration test completed")

def run_performance_test():
    """اجرای تست عملکرد کامل"""
    print("🚀 SENTIMENT COLLECTION PERFORMANCE TEST")
    print("=" * 60)
    
    # اجرای تست‌ها
    suite = unittest.TestLoader().loadTestsFromTestCase(TestSentimentCollection)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # خلاصه نتایج
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY:")
    print(f"✅ Tests run: {result.testsRun}")
    print(f"❌ Failures: {len(result.failures)}")
    print(f"⚠️ Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"   {test}: {traceback}")
    
    if result.errors:
        print("\n⚠️ ERRORS:")
        for test, traceback in result.errors:
            print(f"   {test}: {traceback}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
    print(f"\n🎯 SUCCESS RATE: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 SENTIMENT COLLECTION SYSTEM READY!")
        return True
    else:
        print("❌ SENTIMENT COLLECTION NEEDS FIXES!")
        return False

if __name__ == "__main__":
    success = run_performance_test()
    sys.exit(0 if success else 1)