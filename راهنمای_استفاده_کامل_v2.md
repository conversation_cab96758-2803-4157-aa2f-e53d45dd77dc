# 🚀 راهنمای استفاده کامل سیستم معاملاتی v2.0

## 📋 فهرست مطالب

- [نصب و راه‌اندازی](#نصب-و-راه‌اندازی)
- [معماری جدید](#معماری-جدید)
- [ماژول‌های بازسازی شده](#ماژول‌های-بازسازی-شده)
- [نحوه استفاده](#نحوه-استفاده)
- [مثال‌های کاربردی](#مثال‌های-کاربردی)
- [API و WebSocket](#api-و-websocket)
- [مانیتورینگ و لاگ](#مانیتورینگ-و-لاگ)
- [تست و اعتبارسنجی](#تست-و-اعتبارسنجی)
- [عیب‌یابی](#عیب‌یاب<PERSON>)

## 🛠️ نصب و راه‌اندازی

### 1. نصب وابستگی‌ها

```bash
# نصب پکیج‌های اصلی
pip install -r requirements.txt

# نصب پکیج‌های اضافی (اختیاری)
pip install fastapi uvicorn websockets
```

### 2. پیکربندی

```yaml
# config.yaml
system:
  debug: true
  environment: "development"

proxy:
  http_proxy: "http://127.0.0.1:10809"
  https_proxy: "http://127.0.0.1:10809"
  enabled: true

trading:
  initial_balance: 10000.0
  max_position_size: 0.1
  risk_per_trade: 0.02

models:
  finbert:
    name: "ProsusAI/finbert"
    model_type: "sentiment"
    device: "auto"
  
  chronos:
    name: "amazon/chronos-t5-small"
    model_type: "timeseries"
    device: "auto"
```

### 3. راه‌اندازی اولیه

```python
# راه‌اندازی سریع
from main_new import system_manager

# مقداردهی اولیه
system_manager.initialize()

# شروع سیستم
system_manager.start()

# وضعیت سیستم
status = system_manager.get_system_status()
print(status)
```

## 🏗️ معماری جدید

### ساختار کلی

```
project/
├── core/                    # هسته سیستم
│   ├── __init__.py         # اجزای اصلی
│   ├── base.py             # کلاس‌های پایه
│   ├── config.py           # مدیریت پیکربندی
│   ├── logger.py           # سیستم لاگ
│   ├── exceptions.py       # مدیریت خطا
│   └── utils.py            # ابزارهای کمکی
├── ai_models/              # مدل‌های AI
│   ├── __init__.py         # رجیستری مدل‌ها
│   ├── model_manager.py    # مدیریت مدل‌ها
│   ├── huggingface_models.py # مدل‌های HuggingFace
│   └── sentiment_models.py # مدل‌های تحلیل احساسات
├── utils/                  # ابزارهای بازسازی شده
├── models/                 # مدل‌های بازسازی شده
├── env/                    # محیط‌های معاملاتی
├── portfolio/              # مدیریت پورتفولیو
├── evaluation/             # سیستم ارزیابی
├── optimization/           # بهینه‌سازی
├── api/                    # API و WebSocket
├── main_new.py             # نقطه ورود جدید
└── execution_pipeline.py   # پایپ‌لاین اجرایی
```

### کلاس‌های پایه

```python
from core.base import BaseComponent, BaseModel, BaseStrategy

class MyComponent(BaseComponent):
    def initialize(self) -> bool:
        # مقداردهی اولیه
        return True
    
    def start(self) -> bool:
        # شروع component
        return True
    
    def stop(self) -> bool:
        # توقف component
        return True
    
    def health_check(self) -> dict:
        # بررسی سلامت
        return {"status": "healthy"}
```

## 🔧 ماژول‌های بازسازی شده

### 1. Utils Module

```python
# استفاده از utils جدید
from utils import (
    performance_monitor,
    memory_manager,
    cache_manager,
    retry_on_failure,
    timeout_after
)

# مثال استفاده
@retry_on_failure(max_attempts=3)
@timeout_after(30)
def risky_operation():
    # عملیات پرخطر
    pass

# مانیتورینگ عملکرد
with performance_monitor.measure("operation"):
    result = risky_operation()
```

### 2. Models Module

```python
# استفاده از models جدید
from models import (
    create_model,
    load_model,
    predict,
    legacy_factory
)

# ایجاد مدل
sentiment_model = create_model("sentiment")

# پیش‌بینی
result = predict("finbert", "Bitcoin is showing strong bullish momentum")

# استفاده از factory
model = legacy_factory.create_sentiment_model("finbert")
```

### 3. Environment Module

```python
# استفاده از env جدید
from env import env_factory, TradingEnvironmentV2

# ایجاد محیط جدید
env = env_factory.create_trading_env("EURUSD", "H1", "v2")
env.initialize()
env.start()

# استفاده از محیط
state = env.get_state()
reward = env.step(action=1)  # خرید
```

### 4. Portfolio Module

```python
# استفاده از portfolio جدید
from portfolio import portfolio_factory, PortfolioManagerV2

# ایجاد پورتفولیو
portfolio = portfolio_factory.create_portfolio_manager(10000.0, "v2")
portfolio.initialize()

# عملیات پورتفولیو
portfolio.update_price("EURUSD", 1.1000)
portfolio.open_position("EURUSD", 0.1, "long", 1.1000)
performance = portfolio.get_performance_summary()
```

## 📖 نحوه استفاده

### 1. راه‌اندازی سریع

```python
#!/usr/bin/env python3
"""
مثال راه‌اندازی سریع
"""

from main_new import system_manager
import asyncio

async def main():
    # مقداردهی اولیه
    if not system_manager.initialize():
        print("❌ خطا در مقداردهی اولیه")
        return
    
    # شروع سیستم
    if not system_manager.start():
        print("❌ خطا در شروع سیستم")
        return
    
    print("✅ سیستم آماده است")
    
    # نمایش وضعیت
    status = system_manager.get_system_status()
    print(f"وضعیت سیستم: {status}")
    
    # اجرای دمو
    await system_manager.run_demo()

if __name__ == "__main__":
    asyncio.run(main())
```

### 2. استفاده از Pipeline

```python
#!/usr/bin/env python3
"""
استفاده از pipeline اجرایی
"""

from execution_pipeline import execution_pipeline, run_pipeline
import asyncio

async def main():
    # اجرای pipeline کامل
    result = await run_pipeline()
    
    if result:
        print("✅ Pipeline اجرا شد")
        
        # دریافت گزارش
        report = execution_pipeline.get_pipeline_report()
        print(f"نرخ موفقیت: {report['success_rate']:.1%}")
        print(f"زمان کل: {report['total_duration']:.2f}s")
    else:
        print("❌ Pipeline ناموفق")

if __name__ == "__main__":
    asyncio.run(main())
```

### 3. استفاده پیشرفته

```python
#!/usr/bin/env python3
"""
استفاده پیشرفته از سیستم
"""

from ai_models import initialize_models, get_model
from portfolio import PortfolioManagerV2
from evaluation import evaluation_engine
from optimization import optimization_engine

class TradingBot:
    def __init__(self):
        self.portfolio = PortfolioManagerV2(initial_balance=10000.0)
        self.models = {}
        
    async def initialize(self):
        # مقداردهی اولیه مدل‌ها
        model_manager = initialize_models()
        
        # بارگذاری مدل‌های مورد نیاز
        self.models["sentiment"] = get_model("finbert")
        self.models["timeseries"] = get_model("chronos")
        
        # مقداردهی اولیه پورتفولیو
        self.portfolio.initialize()
        
        # شروع موتورهای ارزیابی و بهینه‌سازی
        evaluation_engine.initialize()
        optimization_engine.initialize()
    
    async def analyze_market(self, symbol: str):
        # تحلیل احساسات
        news_sentiment = self.models["sentiment"].predict(
            f"Market analysis for {symbol}"
        )
        
        # تحلیل سری زمانی
        price_prediction = self.models["timeseries"].predict(
            {"symbol": symbol, "timeframe": "H1"}
        )
        
        return {
            "sentiment": news_sentiment,
            "price_prediction": price_prediction
        }
    
    async def execute_trade(self, symbol: str, analysis: dict):
        # تصمیم‌گیری بر اساس تحلیل
        if analysis["sentiment"]["confidence"] > 0.7:
            # باز کردن پوزیشن
            position_size = self.portfolio.get_position_size(symbol)
            
            if analysis["sentiment"]["label"] == "positive":
                self.portfolio.open_position(
                    symbol, position_size, "long", 1.1000
                )
            else:
                self.portfolio.open_position(
                    symbol, position_size, "short", 1.1000
                )
    
    async def run(self):
        symbols = ["EURUSD", "GBPUSD", "USDJPY"]
        
        for symbol in symbols:
            # تحلیل بازار
            analysis = await self.analyze_market(symbol)
            
            # اجرای معامله
            await self.execute_trade(symbol, analysis)
            
            # به‌روزرسانی پورتفولیو
            self.portfolio.update_price(symbol, 1.1000)
        
        # نمایش عملکرد
        performance = self.portfolio.get_performance_summary()
        print(f"عملکرد: {performance}")

# اجرای ربات
async def main():
    bot = TradingBot()
    await bot.initialize()
    await bot.run()

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔗 API و WebSocket

### 1. استفاده از REST API

```python
# راه‌اندازی API
from api import api_manager

# شروع API server
api_manager.initialize()
api_manager.start()

# API endpoints:
# GET /health - بررسی سلامت
# GET /status - وضعیت سیستم
# GET /models - مدل‌های موجود
# POST /predict - پیش‌بینی
# GET /portfolio - وضعیت پورتفولیو
# GET /signals - سیگنال‌های معاملاتی
```

### 2. WebSocket Connection

```javascript
// اتصال WebSocket
const ws = new WebSocket('ws://localhost:8000/ws/trading');

ws.onopen = function() {
    console.log('متصل شد');
    
    // اشتراک در کانال
    ws.send(JSON.stringify({
        type: 'subscribe',
        channel: 'trading'
    }));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('پیام دریافت شد:', data);
};
```

### 3. Custom API Endpoint

```python
# افزودن endpoint سفارشی
from api import api_manager

async def custom_endpoint(request_data):
    return {
        "success": True,
        "data": {"message": "Custom endpoint"},
        "timestamp": datetime.now().isoformat()
    }

# ثبت endpoint
api_manager.register_endpoint("/custom", "POST", custom_endpoint)
```

## 📊 مانیتورینگ و لاگ

### 1. سیستم لاگ پیشرفته

```python
from core.logger import get_logger, configure_logging

# پیکربندی لاگ
configure_logging({
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "handlers": ["console", "file", "database"]
})

# استفاده از لاگ
logger = get_logger(__name__)

logger.info("پیام اطلاعاتی")
logger.warning("پیام هشدار")
logger.error("پیام خطا")

# لاگ با metadata
logger.info("معامله انجام شد", extra={
    "symbol": "EURUSD",
    "action": "buy",
    "amount": 0.1
})
```

### 2. مانیتورینگ عملکرد

```python
from core.utils import performance_monitor, memory_manager

# مانیتورینگ عملکرد
with performance_monitor.measure("trading_operation"):
    # عملیات معاملاتی
    result = execute_trade()

# مانیتورینگ حافظه
memory_info = memory_manager.get_memory_info()
print(f"استفاده حافظه: {memory_info['used_mb']:.1f}MB")

# هشدار در صورت استفاده بالای حافظه
if memory_info['used_mb'] > 1000:
    logger.warning("استفاده بالای حافظه")
```

### 3. داشبورد Realtime

```python
# راه‌اندازی داشبورد
from api.realtime_dashboard import DashboardServer

dashboard = DashboardServer()
dashboard.initialize()
dashboard.start()

# دسترسی به داشبورد: http://localhost:8000/dashboard
```

## 🧪 تست و اعتبارسنجی

### 1. تست کامل سیستم

```bash
# اجرای تست یکپارچه‌سازی
python test_integration_v2.py

# اجرای تست‌های خاص
python -m pytest tests/ -v
```

### 2. تست عملکرد

```python
# تست عملکرد
from test_integration_v2 import IntegrationTester

tester = IntegrationTester()
result = tester.run_all_tests()

if result:
    print("✅ همه تست‌ها موفق")
else:
    print("❌ برخی تست‌ها ناموفق")
```

### 3. تست مدل‌ها

```python
from evaluation import evaluation_engine

# تست مدل
model = get_model("finbert")
result = evaluation_engine.evaluate(
    model, 
    "model_performance",
    test_data=sample_data
)

print(f"نتیجه ارزیابی: {result.score:.2f}")
```

## 🐛 عیب‌یابی

### 1. مشکلات رایج

#### خطای Import
```python
# اگر خطای import دارید
try:
    from ai_models import initialize_models
except ImportError:
    print("مدل‌های AI در دسترس نیست")
    # استفاده از حالت fallback
```

#### مشکل Proxy
```python
# بررسی وضعیت proxy
from core.utils import proxy_manager

status = proxy_manager.get_status()
if not status["connected"]:
    print("مشکل در اتصال proxy")
```

#### مشکل حافظه
```python
# بررسی حافظه
from core.utils import memory_manager

memory_info = memory_manager.get_memory_info()
if memory_info["used_mb"] > 2000:
    # پاکسازی حافظه
    memory_manager.cleanup()
```

### 2. Debug Mode

```python
# فعال‌سازی حالت debug
from core.config import get_config

config = get_config()
config.system.debug = True

# لاگ‌های تفصیلی
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

### 3. Health Check

```python
# بررسی سلامت تمام اجزا
from main_new import system_manager

health_report = system_manager.get_comprehensive_health_report()
print(health_report)
```

## ⚙️ پیکربندی پیشرفته

### 1. تنظیمات مدل‌ها

```yaml
# config.yaml
models:
  finbert:
    name: "ProsusAI/finbert"
    model_type: "sentiment"
    device: "cuda"  # یا "cpu"
    max_length: 512
    batch_size: 32
    cache_size: 1000
  
  chronos:
    name: "amazon/chronos-t5-small"
    model_type: "timeseries"
    device: "auto"
    prediction_length: 24
    context_length: 512
```

### 2. تنظیمات Performance

```yaml
# config.yaml
performance:
  max_workers: 4
  batch_processing: true
  async_execution: true
  cache_enabled: true
  memory_limit_mb: 2048
```

### 3. تنظیمات Security

```yaml
# config.yaml
security:
  api_key_required: true
  rate_limiting: true
  cors_enabled: true
  encryption_enabled: false
```

## 🚀 بهینه‌سازی عملکرد

### 1. استفاده از Cache

```python
from core.utils import cache_manager

# Cache کردن نتایج
@cache_manager.cached(ttl=3600)
def expensive_calculation(data):
    # محاسبه پرهزینه
    return result
```

### 2. Async Processing

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def parallel_processing():
    with ThreadPoolExecutor(max_workers=4) as executor:
        tasks = [
            executor.submit(process_symbol, symbol)
            for symbol in ["EURUSD", "GBPUSD", "USDJPY"]
        ]
        
        results = await asyncio.gather(*tasks)
    return results
```

### 3. Memory Management

```python
from core.utils import memory_manager

# نظارت بر حافظه
memory_manager.set_memory_limit(2048)  # 2GB
memory_manager.enable_auto_cleanup()

# پاکسازی دستی
memory_manager.cleanup()
```

## 📝 نتیجه‌گیری

سیستم معاملاتی v2.0 با معماری جدید و ماژول‌های بازسازی شده، قابلیت‌های پیشرفته‌ای را ارائه می‌دهد:

✅ **معماری مدرن**: ساختار یکپارچه و قابل نگهداری
✅ **AI Integration**: ادغام کامل مدل‌های HuggingFace
✅ **Backward Compatibility**: سازگاری با کدهای قدیمی
✅ **Performance**: بهینه‌سازی عملکرد و مدیریت حافظه
✅ **Monitoring**: سیستم مانیتورینگ و لاگ پیشرفته
✅ **API**: REST API و WebSocket برای دسترسی realtime
✅ **Testing**: فریمورک تست جامع
✅ **Documentation**: مستندات کامل و مثال‌های کاربردی

برای اطلاعات بیشتر، فایل‌های مثال در پوشه `examples/` را مطالعه کنید. 