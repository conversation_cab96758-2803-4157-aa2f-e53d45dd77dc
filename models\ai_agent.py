"""
🤖 AI Agent - مغز اصلی سیستم ترید
AI Agent - Main Brain of Trading System

این Agent مسئول انتخاب، نظارت و مدیریت بهترین مدل‌های ترید است
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from pathlib import Path
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
import os

# Local imports
from .__init__ import (
    ModelManager, ModelRegistry, model_registry,
    FinBERTModel, CryptoBERTModel, FinancialSentimentModel,
    ChronosModel, TimeSeriesModel, TimeSeriesEnsemble,
    ModelEnsemble, WeightedEnsemble, VotingEnsemble
)
from utils.advanced_technical_indicators import AdvancedTechnicalIndicators
from evaluation.metrics import calculate_metrics
from optimization.bayesian import BayesianOptimizer

class ModelType(Enum):
    """انواع مدل‌های موجود"""
    SENTIMENT = "sentiment"
    TIMESERIES = "timeseries"
    TECHNICAL = "technical"
    ENSEMBLE = "ensemble"
    HYBRID = "hybrid"

class MarketCondition(Enum):
    """شرایط بازار"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    BREAKOUT = "breakout"

@dataclass
class ModelPerformance:
    """اطلاعات عملکرد مدل"""
    model_name: str
    model_type: ModelType
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    training_time: float
    inference_time: float
    last_updated: datetime
    market_conditions: List[MarketCondition]
    confidence_score: float

@dataclass
class TradingDecision:
    """تصمیم ترید"""
    action: str  # "buy", "sell", "hold"
    confidence: float
    model_used: str
    reasoning: str
    risk_level: str
    expected_return: float
    stop_loss: float
    take_profit: float

class AIAgent:
    """
    🤖 AI Agent - مغز اصلی سیستم ترید
    
    مسئولیت‌ها:
    1. انتخاب بهترین مدل‌ها بر اساس شرایط بازار
    2. نظارت بر عملکرد مدل‌ها
    3. تصمیم‌گیری نهایی ترید
    4. مدیریت ریسک
    5. بهینه‌سازی مداوم
    6. نظارت کامل بر تمام ماژول‌ها
    7. مدیریت فرآیند آموزش
    8. تصمیم‌گیری استراتژیک
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config(config_path)
        
        # Initialize components
        self.model_registry = model_registry
        self.model_manager = ModelManager()
        self.technical_indicators = AdvancedTechnicalIndicators()
        self.bayesian_optimizer = BayesianOptimizer()
        
        # Performance tracking
        self.model_performances: Dict[str, ModelPerformance] = {}
        self.trading_history: List[TradingDecision] = []
        self.market_conditions: List[MarketCondition] = []
        
        # Best models for each market condition
        self.best_models = {
            MarketCondition.TRENDING_UP: [],
            MarketCondition.TRENDING_DOWN: [],
            MarketCondition.SIDEWAYS: [],
            MarketCondition.VOLATILE: [],
            MarketCondition.BREAKOUT: []
        }
        
        # System supervision
        self.system_status = {
            'overall_health': 'healthy',
            'active_models': [],
            'learning_status': 'active',
            'risk_level': 'medium',
            'performance_score': 0.0,
            'last_optimization': None,
            'system_uptime': datetime.now()
        }
        
        # Learning management
        self.learning_schedule = {
            'continuous_learning': True,
            'retraining_frequency': 'daily',
            'optimization_frequency': 'weekly',
            'performance_threshold': 0.65,
            'last_training': None,
            'next_training': None
        }
        
        # Initialize models
        self._initialize_models()
        self._initialize_hf_model()
        
        # Start supervision
        self._start_system_supervision()
        
    def _load_config(self, config_path: str) -> Dict:
        """بارگذاری تنظیمات"""
        try:
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.warning(f"Could not load config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """تنظیمات پیش‌فرض"""
        return {
            "models": {
                "sentiment": ["finbert", "cryptobert"],
                "timeseries": ["chronos", "lstm"],
                "technical": ["ensemble"],
                "ensemble": ["weighted", "voting"]
            },
            "performance_thresholds": {
                "min_accuracy": 0.65,
                "min_sharpe": 1.0,
                "max_drawdown": 0.15,
                "min_win_rate": 0.55
            },
            "risk_management": {
                "max_position_size": 0.02,
                "stop_loss_pct": 0.02,
                "take_profit_pct": 0.04
            },
            "supervision": {
                "health_check_interval": 300,  # 5 minutes
                "performance_evaluation_interval": 3600,  # 1 hour
                "optimization_interval": 86400,  # 24 hours
                "emergency_threshold": 0.3
            }
        }
    
    def _initialize_models(self):
        """راه‌اندازی مدل‌های اولیه"""
        try:
            # Initialize sentiment models
            self.sentiment_models = {
                "finbert": FinBERTModel(),
                "cryptobert": CryptoBERTModel()
            }
            
            # Initialize time series models
            self.timeseries_models = {
                "chronos": ChronosModel(),
                "lstm": TimeSeriesModel(model_name="lstm")
            }
            
            # Initialize ensemble models
            self.ensemble_models = {
                "weighted": WeightedEnsemble(),
                "voting": VotingEnsemble()
            }
            
            self.logger.info("✅ AI Agent models initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Error initializing models: {e}")
    
    def _initialize_hf_model(self):
        """راه‌اندازی مدل Hugging Face"""
        try:
            # Check if model path exists
            model_path = 'temp_models/pearl-3x7b'
            if not os.path.exists(model_path):
                self.logger.warning(f"⚠️ Hugging Face model path not found: {model_path}")
                self.logger.info("📝 Using fallback model configuration")
                return
            
            # Try to load bitsandbytes
            try:
                from transformers import BitsAndBytesConfig
                bnb_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_quant_type="nf4",
                    bnb_4bit_compute_dtype=torch.float16,
                )
            except ImportError:
                self.logger.warning("⚠️ bitsandbytes not available, using default config")
                bnb_config = None
            
            self.hf_model = AutoModelForCausalLM.from_pretrained(
                model_path,
                quantization_config=bnb_config,
                device_map="auto",
            )
            self.hf_tokenizer = AutoTokenizer.from_pretrained(model_path)
            self.logger.info("✅ Hugging Face Pearl-3x7B model loaded successfully")
        except Exception as e:
            self.logger.warning(f"⚠️ Hugging Face model not available: {e}")
            self.logger.info("📝 System will continue without Hugging Face model")
    
    def _start_system_supervision(self):
        """شروع نظارت بر سیستم"""
        self.logger.info("🚀 Starting AI Agent system supervision...")
        
        # Note: Tasks will be started manually when needed
        # asyncio.create_task(self._periodic_health_check())
        # asyncio.create_task(self._periodic_performance_evaluation())
        # asyncio.create_task(self._periodic_optimization())
        
        self.logger.info("✅ AI Agent supervision started successfully")
    
    async def _periodic_health_check(self):
        """بررسی سلامت دوره‌ای سیستم"""
        while True:
            try:
                await self._check_system_health()
                await asyncio.sleep(self.config['supervision']['health_check_interval'])
            except Exception as e:
                self.logger.error(f"Error in health check: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retry
    
    async def _periodic_performance_evaluation(self):
        """ارزیابی عملکرد دوره‌ای"""
        while True:
            try:
                await self._evaluate_system_performance()
                await asyncio.sleep(self.config['supervision']['performance_evaluation_interval'])
            except Exception as e:
                self.logger.error(f"Error in performance evaluation: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retry
    
    async def _periodic_optimization(self):
        """بهینه‌سازی دوره‌ای"""
        while True:
            try:
                await self._optimize_system()
                await asyncio.sleep(self.config['supervision']['optimization_interval'])
            except Exception as e:
                self.logger.error(f"Error in optimization: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour before retry
    
    async def _check_system_health(self):
        """بررسی سلامت سیستم"""
        try:
            health_score = 0.0
            issues = []
            
            # Check model availability
            if self.model_performances:
                health_score += 0.3
            else:
                issues.append("No models available")
            
            # Check recent performance
            if self.trading_history:
                recent_decisions = self.trading_history[-10:]
                avg_confidence = np.mean([d.confidence for d in recent_decisions])
                if avg_confidence > 0.5:
                    health_score += 0.3
                else:
                    issues.append("Low confidence in recent decisions")
            
            # Check market condition analysis
            if self.market_conditions:
                health_score += 0.2
            else:
                issues.append("No market condition analysis")
            
            # Check learning status
            if self.learning_schedule['continuous_learning']:
                health_score += 0.2
            else:
                issues.append("Continuous learning disabled")
            
            # Update system status
            if health_score >= 0.8:
                self.system_status['overall_health'] = 'excellent'
            elif health_score >= 0.6:
                self.system_status['overall_health'] = 'good'
            elif health_score >= 0.4:
                self.system_status['overall_health'] = 'fair'
            else:
                self.system_status['overall_health'] = 'poor'
            
            self.system_status['performance_score'] = health_score
            
            if issues:
                self.logger.warning(f"System health issues: {issues}")
            else:
                self.logger.info(f"System health: {self.system_status['overall_health']} (score: {health_score:.2f})")
                
        except Exception as e:
            self.logger.error(f"Error checking system health: {e}")
    
    async def _evaluate_system_performance(self):
        """ارزیابی عملکرد سیستم"""
        try:
            if not self.model_performances:
                return
            
            # Calculate overall performance metrics
            total_models = len(self.model_performances)
            avg_accuracy = np.mean([p.accuracy for p in self.model_performances.values()])
            avg_sharpe = np.mean([p.sharpe_ratio for p in self.model_performances.values()])
            avg_win_rate = np.mean([p.win_rate for p in self.model_performances.values()])
            
            # Update system status
            self.system_status['active_models'] = list(self.model_performances.keys())
            
            # Determine if retraining is needed
            if avg_accuracy < self.learning_schedule['performance_threshold']:
                self.logger.warning(f"Performance below threshold ({avg_accuracy:.3f} < {self.learning_schedule['performance_threshold']})")
                await self._trigger_retraining()
            
            self.logger.info(f"System performance - Accuracy: {avg_accuracy:.3f}, Sharpe: {avg_sharpe:.3f}, Win Rate: {avg_win_rate:.3f}")
            
        except Exception as e:
            self.logger.error(f"Error evaluating system performance: {e}")
    
    async def _optimize_system(self):
        """بهینه‌سازی سیستم"""
        try:
            self.logger.info("🔄 Starting system optimization...")
            
            # Optimize model hyperparameters
            await self._optimize_model_hyperparameters()
            
            # Optimize ensemble weights
            await self._optimize_ensemble_weights()
            
            # Update learning schedule
            self.learning_schedule['last_training'] = datetime.now()
            self.learning_schedule['next_training'] = datetime.now() + timedelta(days=1)
            
            self.system_status['last_optimization'] = datetime.now()
            
            self.logger.info("✅ System optimization completed")
            
        except Exception as e:
            self.logger.error(f"Error optimizing system: {e}")
    
    async def _optimize_model_hyperparameters(self):
        """بهینه‌سازی هایپرپارامترهای مدل"""
        try:
            # Define objective function for optimization
            def objective_function(params):
                # Simulate model performance with given parameters
                learning_rate = params.get('learning_rate', 0.001)
                batch_size = params.get('batch_size', 32)
                hidden_size = params.get('hidden_size', 128)
                
                # Simple performance simulation
                performance = 0.0
                
                # Learning rate effect
                lr_score = 1.0 - abs(learning_rate - 0.001) / 0.001
                performance += lr_score * 0.4
                
                # Batch size effect
                batch_score = 1.0 - abs(batch_size - 64) / 64
                performance += batch_score * 0.3
                
                # Hidden size effect
                hidden_score = 1.0 - abs(hidden_size - 128) / 128
                performance += hidden_score * 0.3
                
                return max(0.0, min(1.0, performance))
            
            # Define parameter bounds
            parameter_bounds = {
                'learning_rate': (1e-5, 1e-2),
                'batch_size': (16, 256),
                'hidden_size': (32, 512),
                'dropout_rate': (0.0, 0.5),
                'weight_decay': (1e-6, 1e-3)
            }
            
            # Run optimization
            result = self.bayesian_optimizer.optimize(
                objective_function=objective_function,
                parameter_bounds=parameter_bounds,
                n_iterations=50
            )
            
            self.logger.info(f"Hyperparameter optimization completed. Best score: {result['best_score']:.4f}")
            
        except Exception as e:
            self.logger.error(f"Error optimizing hyperparameters: {e}")
    
    async def _optimize_ensemble_weights(self):
        """بهینه‌سازی وزن‌های ensemble"""
        try:
            if not self.model_performances:
                return
            
            # Calculate optimal weights based on recent performance
            model_names = list(self.model_performances.keys())
            performances = [self.model_performances[name].confidence_score for name in model_names]
            
            # Normalize weights
            total_performance = sum(performances)
            if total_performance > 0:
                optimal_weights = [p / total_performance for p in performances]
            else:
                optimal_weights = [1.0 / len(model_names)] * len(model_names)
            
            # Update ensemble weights
            for i, model_name in enumerate(model_names):
                if hasattr(self, 'ensemble_models') and 'weighted' in self.ensemble_models:
                    # Update weighted ensemble
                    pass  # Implementation depends on ensemble structure
            
            self.logger.info(f"Ensemble weights optimized: {dict(zip(model_names, optimal_weights))}")
            
        except Exception as e:
            self.logger.error(f"Error optimizing ensemble weights: {e}")
    
    async def _trigger_retraining(self):
        """شروع مجدد آموزش"""
        try:
            self.logger.info("🔄 Triggering model retraining...")
            
            # Update learning schedule
            self.learning_schedule['last_training'] = datetime.now()
            
            # Trigger retraining for underperforming models
            for model_name, performance in self.model_performances.items():
                if performance.accuracy < self.learning_schedule['performance_threshold']:
                    self.logger.info(f"Retraining model: {model_name}")
                    # Here you would trigger actual retraining
                    # await self._retrain_model(model_name)
            
            self.logger.info("✅ Retraining triggered successfully")
            
        except Exception as e:
            self.logger.error(f"Error triggering retraining: {e}")
    
    async def analyze_market_conditions(self, market_data: pd.DataFrame) -> MarketCondition:
        """
        تحلیل شرایط بازار
        """
        try:
            # Calculate technical indicators
            indicators = self.technical_indicators.calculate_all(market_data)
            
            # Analyze trend
            sma_20 = indicators.get('sma_20', [])
            sma_50 = indicators.get('sma_50', [])
            rsi = indicators.get('rsi', [])
            volatility = indicators.get('atr', [])
            
            if len(sma_20) < 2 or len(sma_50) < 2:
                return MarketCondition.SIDEWAYS
            
            # Determine market condition
            current_price = market_data['close'].iloc[-1]
            sma_20_current = sma_20[-1]
            sma_50_current = sma_50[-1]
            rsi_current = rsi[-1] if len(rsi) > 0 else 50
            volatility_current = volatility[-1] if len(volatility) > 0 else 0
            
            # Trending conditions
            if sma_20_current > sma_50_current and current_price > sma_20_current:
                if rsi_current < 70:
                    return MarketCondition.TRENDING_UP
                else:
                    return MarketCondition.BREAKOUT
            elif sma_20_current < sma_50_current and current_price < sma_20_current:
                if rsi_current > 30:
                    return MarketCondition.TRENDING_DOWN
                else:
                    return MarketCondition.BREAKOUT
            
            # Volatile condition
            if volatility_current > np.mean(volatility) * 1.5:
                return MarketCondition.VOLATILE
            
            return MarketCondition.SIDEWAYS
            
        except Exception as e:
            self.logger.error(f"Error analyzing market conditions: {e}")
            return MarketCondition.SIDEWAYS
    
    async def evaluate_model_performance(self, model_name: str, model_type: ModelType, 
                                       test_data: pd.DataFrame, predictions: np.ndarray) -> ModelPerformance:
        """
        ارزیابی عملکرد مدل
        """
        try:
            # Calculate basic metrics
            actual = test_data['target'].values if 'target' in test_data.columns else test_data['close'].pct_change().shift(-1).dropna().values
            actual = actual[:len(predictions)]
            
            # Calculate metrics
            metrics = calculate_metrics(actual, predictions)
            
            # Calculate trading metrics
            returns = self._calculate_returns(actual, predictions)
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
            max_drawdown = self._calculate_max_drawdown(returns)
            win_rate = np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0
            profit_factor = np.sum(returns[returns > 0]) / abs(np.sum(returns[returns < 0])) if np.sum(returns[returns < 0]) != 0 else float('inf')
            
            # Determine market conditions where model performs well
            market_conditions = await self._determine_model_market_conditions(model_name, test_data)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(metrics, sharpe_ratio, win_rate)
            
            performance = ModelPerformance(
                model_name=model_name,
                model_type=model_type,
                accuracy=metrics.get('accuracy', 0),
                precision=metrics.get('precision', 0),
                recall=metrics.get('recall', 0),
                f1_score=metrics.get('f1_score', 0),
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor,
                training_time=0,  # Will be updated during training
                inference_time=0,  # Will be updated during inference
                last_updated=datetime.now(),
                market_conditions=market_conditions,
                confidence_score=confidence_score
            )
            
            self.model_performances[model_name] = performance
            return performance
            
        except Exception as e:
            self.logger.error(f"Error evaluating model performance: {e}")
            return None
    
    def _calculate_returns(self, actual: np.ndarray, predictions: np.ndarray) -> np.ndarray:
        """محاسبه بازدهی"""
        try:
            # Simple strategy: buy when prediction > 0, sell when < 0
            positions = np.where(predictions > 0, 1, -1)
            returns = positions * actual
            return returns
        except Exception as e:
            self.logger.error(f"Error calculating returns: {e}")
            return np.zeros_like(actual)
    
    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """محاسبه حداکثر drawdown"""
        try:
            cumulative = np.cumprod(1 + returns)
            running_max = np.maximum.accumulate(cumulative)
            drawdown = (cumulative - running_max) / running_max
            return np.min(drawdown)
        except Exception as e:
            self.logger.error(f"Error calculating max drawdown: {e}")
            return 0.0
    
    def _calculate_confidence_score(self, metrics: Dict, sharpe_ratio: float, win_rate: float) -> float:
        """محاسبه امتیاز اطمینان"""
        try:
            # Weighted combination of metrics
            accuracy_weight = 0.3
            sharpe_weight = 0.3
            win_rate_weight = 0.2
            f1_weight = 0.2
            
            accuracy_score = metrics.get('accuracy', 0)
            f1_score = metrics.get('f1_score', 0)
            
            # Normalize sharpe ratio (assume good range is 0-3)
            sharpe_score = min(sharpe_ratio / 3.0, 1.0) if sharpe_ratio > 0 else 0
            
            confidence = (accuracy_score * accuracy_weight + 
                         sharpe_score * sharpe_weight + 
                         win_rate * win_rate_weight + 
                         f1_score * f1_weight)
            
            return max(0.0, min(1.0, confidence))
            
        except Exception as e:
            self.logger.error(f"Error calculating confidence score: {e}")
            return 0.5
    
    async def _determine_model_market_conditions(self, model_name: str, test_data: pd.DataFrame) -> List[MarketCondition]:
        """تعیین شرایط بازار مناسب برای مدل"""
        try:
            # This is a simplified version - in practice, you'd analyze performance across different market conditions
            conditions = []
            
            # Analyze different time periods to determine market conditions
            for i in range(0, len(test_data), len(test_data) // 5):
                period_data = test_data.iloc[i:i+len(test_data)//5]
                if len(period_data) > 10:
                    condition = await self.analyze_market_conditions(period_data)
                    conditions.append(condition)
            
            return list(set(conditions))  # Remove duplicates
            
        except Exception as e:
            self.logger.error(f"Error determining model market conditions: {e}")
            return [MarketCondition.SIDEWAYS]
    
    async def select_best_models(self, market_condition: MarketCondition) -> List[str]:
        """
        انتخاب بهترین مدل‌ها برای شرایط بازار فعلی
        """
        try:
            # Filter models by performance and market condition
            suitable_models = []
            
            for model_name, performance in self.model_performances.items():
                # Check if model performs well in current market condition
                if market_condition in performance.market_conditions:
                    # Check performance thresholds
                    if (performance.accuracy >= self.config['performance_thresholds']['min_accuracy'] and
                        performance.sharpe_ratio >= self.config['performance_thresholds']['min_sharpe'] and
                        performance.max_drawdown >= -self.config['performance_thresholds']['max_drawdown'] and
                        performance.win_rate >= self.config['performance_thresholds']['min_win_rate']):
                        
                        suitable_models.append((model_name, performance.confidence_score))
            
            # Sort by confidence score and return top models
            suitable_models.sort(key=lambda x: x[1], reverse=True)
            best_models = [model[0] for model in suitable_models[:3]]  # Top 3 models
            
            self.best_models[market_condition] = best_models
            return best_models
            
        except Exception as e:
            self.logger.error(f"Error selecting best models: {e}")
            return []
    
    async def make_trading_decision(self, market_data: pd.DataFrame, 
                                  sentiment_data: Optional[Dict] = None) -> TradingDecision:
        """
        تصمیم‌گیری نهایی ترید
        """
        try:
            # Analyze current market conditions
            market_condition = await self.analyze_market_conditions(market_data)
            self.market_conditions.append(market_condition)
            
            # Select best models for current condition
            best_models = await self.select_best_models(market_condition)
            
            if not best_models:
                return TradingDecision(
                    action="hold",
                    confidence=0.0,
                    model_used="none",
                    reasoning="No suitable models available",
                    risk_level="high",
                    expected_return=0.0,
                    stop_loss=0.0,
                    take_profit=0.0
                )
            
            # Get predictions from best models
            predictions = []
            model_weights = []
            
            for model_name in best_models:
                if model_name in self.model_performances:
                    performance = self.model_performances[model_name]
                    prediction = await self._get_model_prediction(model_name, market_data, sentiment_data)
                    if prediction is not None:
                        predictions.append(prediction)
                        model_weights.append(performance.confidence_score)
            
            if not predictions:
                return TradingDecision(
                    action="hold",
                    confidence=0.0,
                    model_used="none",
                    reasoning="No predictions available",
                    risk_level="high",
                    expected_return=0.0,
                    stop_loss=0.0,
                    take_profit=0.0
                )
            
            # Weighted ensemble prediction
            weights = np.array(model_weights) / np.sum(model_weights)
            final_prediction = np.average(predictions, weights=weights)
            
            # Use HF model for final decision
            if hasattr(self, 'hf_model') and self.hf_model is not None:
                prompt = f"Analyze market data and make trading decision. Current condition: {market_condition.value}, Predictions: {predictions}"
                inputs = self.hf_tokenizer(prompt, return_tensors="pt").to(self.hf_model.device)
                outputs = self.hf_model.generate(**inputs, max_new_tokens=100)
                hf_decision = self.hf_tokenizer.decode(outputs[0], skip_special_tokens=True)
                # Parse hf_decision to update final_prediction or action
                # For simplicity, assume it outputs action and confidence
                # Example parsing (adjust based on actual output)
                if 'buy' in hf_decision.lower():
                    action = 'buy'
                    confidence = 0.8  # Extract from output
                elif 'sell' in hf_decision.lower():
                    action = 'sell'
                    confidence = 0.8
                else:
                    action = 'hold'
                    confidence = 0.5
            else:
                # Fallback to traditional decision logic if HF model is not loaded
                current_price = market_data['close'].iloc[-1]
                volatility = market_data['close'].pct_change().std()
                
                if final_prediction > 0.1:  # Strong buy signal
                    action = "buy"
                    confidence = min(abs(final_prediction), 0.95)
                elif final_prediction < -0.1:  # Strong sell signal
                    action = "sell"
                    confidence = min(abs(final_prediction), 0.95)
                else:
                    action = "hold"
                    confidence = 0.5
            
            # Calculate risk management levels
            stop_loss_pct = self.config['risk_management']['stop_loss_pct']
            take_profit_pct = self.config['risk_management']['take_profit_pct']
            
            if action == "buy":
                stop_loss = current_price * (1 - stop_loss_pct)
                take_profit = current_price * (1 + take_profit_pct)
                expected_return = take_profit_pct * confidence
            elif action == "sell":
                stop_loss = current_price * (1 + stop_loss_pct)
                take_profit = current_price * (1 - take_profit_pct)
                expected_return = take_profit_pct * confidence
            else:
                stop_loss = take_profit = expected_return = 0.0
            
            # Determine risk level
            if confidence > 0.8:
                risk_level = "low"
            elif confidence > 0.6:
                risk_level = "medium"
            else:
                risk_level = "high"
            
            decision = TradingDecision(
                action=action,
                confidence=confidence,
                model_used=", ".join(best_models),
                reasoning=f"Market condition: {market_condition.value}, Models: {len(best_models)}",
                risk_level=risk_level,
                expected_return=expected_return,
                stop_loss=stop_loss,
                take_profit=take_profit
            )
            
            self.trading_history.append(decision)
            return decision
            
        except Exception as e:
            self.logger.error(f"Error making trading decision: {e}")
            return TradingDecision(
                action="hold",
                confidence=0.0,
                model_used="error",
                reasoning=f"Error: {str(e)}",
                risk_level="high",
                expected_return=0.0,
                stop_loss=0.0,
                take_profit=0.0
            )
    
    async def _get_model_prediction(self, model_name: str, market_data: pd.DataFrame, 
                                  sentiment_data: Optional[Dict] = None) -> Optional[float]:
        """دریافت پیش‌بینی از مدل"""
        try:
            # This is a simplified version - in practice, you'd call the actual model
            if "sentiment" in model_name.lower():
                # Sentiment model prediction
                if sentiment_data:
                    return sentiment_data.get('sentiment_score', 0.0)
                else:
                    return 0.0
            elif "timeseries" in model_name.lower():
                # Time series model prediction
                returns = market_data['close'].pct_change().dropna()
                if len(returns) > 0:
                    return returns.iloc[-1]  # Simple momentum
                else:
                    return 0.0
            else:
                # Technical model prediction
                indicators = self.technical_indicators.calculate_all(market_data)
                rsi = indicators.get('rsi', [])
                if len(rsi) > 0:
                    rsi_current = rsi[-1]
                    if rsi_current < 30:
                        return 0.1  # Buy signal
                    elif rsi_current > 70:
                        return -0.1  # Sell signal
                    else:
                        return 0.0  # Hold
                else:
                    return 0.0
                    
        except Exception as e:
            self.logger.error(f"Error getting model prediction: {e}")
            return None
    
    async def update_model_performance(self, model_name: str, actual_result: float, 
                                     prediction: float, market_condition: MarketCondition):
        """به‌روزرسانی عملکرد مدل"""
        try:
            if model_name in self.model_performances:
                performance = self.model_performances[model_name]
                
                # Update metrics
                # This is a simplified update - in practice, you'd recalculate all metrics
                performance.last_updated = datetime.now()
                
                # Update market conditions if needed
                if market_condition not in performance.market_conditions:
                    performance.market_conditions.append(market_condition)
                
                self.logger.info(f"Updated performance for {model_name}")
                
        except Exception as e:
            self.logger.error(f"Error updating model performance: {e}")
    
    def get_system_status(self) -> Dict:
        """دریافت وضعیت سیستم"""
        try:
            return {
                "total_models": len(self.model_performances),
                "best_models_by_condition": {
                    condition.value: models for condition, models in self.best_models.items()
                },
                "recent_decisions": len(self.trading_history[-10:]) if self.trading_history else 0,
                "current_market_condition": self.market_conditions[-1].value if self.market_conditions else "unknown",
                "system_health": self.system_status['overall_health'],
                "performance_score": self.system_status['performance_score'],
                "learning_status": self.learning_schedule['continuous_learning'],
                "last_optimization": self.system_status['last_optimization'].isoformat() if self.system_status['last_optimization'] else None,
                "system_uptime": (datetime.now() - self.system_status['system_uptime']).total_seconds()
            }
        except Exception as e:
            self.logger.error(f"Error getting system status: {e}")
            return {"error": str(e)}
    
    def save_state(self, filepath: str = "ai_agent_state.json"):
        """ذخیره وضعیت Agent"""
        try:
            state = {
                "model_performances": {
                    name: asdict(perf) for name, perf in self.model_performances.items()
                },
                "best_models": {
                    condition.value: models for condition, models in self.best_models.items()
                },
                "trading_history": [
                    {
                        "action": decision.action,
                        "confidence": decision.confidence,
                        "model_used": decision.model_used,
                        "reasoning": decision.reasoning,
                        "risk_level": decision.risk_level,
                        "expected_return": decision.expected_return,
                        "stop_loss": decision.stop_loss,
                        "take_profit": decision.take_profit
                    }
                    for decision in self.trading_history
                ],
                "market_conditions": [condition.value for condition in self.market_conditions],
                "system_status": self.system_status,
                "learning_schedule": self.learning_schedule,
                "last_updated": datetime.now().isoformat()
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"AI Agent state saved to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error saving AI Agent state: {e}")
    
    def load_state(self, filepath: str = "ai_agent_state.json"):
        """بارگذاری وضعیت Agent"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            # Load model performances
            self.model_performances = {}
            for name, perf_data in state.get("model_performances", {}).items():
                perf_data['last_updated'] = datetime.fromisoformat(perf_data['last_updated'])
                perf_data['market_conditions'] = [MarketCondition(c) for c in perf_data['market_conditions']]
                self.model_performances[name] = ModelPerformance(**perf_data)
            
            # Load best models
            self.best_models = {}
            for condition_str, models in state.get("best_models", {}).items():
                condition = MarketCondition(condition_str)
                self.best_models[condition] = models
            
            # Load trading history
            self.trading_history = []
            for decision_data in state.get("trading_history", []):
                self.trading_history.append(TradingDecision(**decision_data))
            
            # Load market conditions
            self.market_conditions = [MarketCondition(c) for c in state.get("market_conditions", [])]
            
            # Load system status and learning schedule
            self.system_status = state.get("system_status", self.system_status)
            self.learning_schedule = state.get("learning_schedule", self.learning_schedule)
            
            self.logger.info(f"AI Agent state loaded from {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error loading AI Agent state: {e}")

    async def start_model_training(self):
        """شروع آموزش مدل‌ها"""
        try:
            self.logger.info("🎓 Starting comprehensive model training process...")
            
            # Update learning schedule
            self.learning_schedule['last_training'] = datetime.now()
            self.learning_schedule['next_training'] = datetime.now() + timedelta(days=1)
            
            # 1. Train sentiment models
            await self._train_sentiment_models()
            
            # 2. Train time series models
            await self._train_timeseries_models()
            
            # 3. Train reinforcement learning models
            await self._train_rl_models()
            
            # 4. Train ensemble models
            await self._train_ensemble_models()
            
            # 5. Train continual learning models
            await self._train_continual_learning_models()
            
            # 6. Train deep learning models
            await self._train_deep_learning_models()
            
            # 7. Optimize hyperparameters
            await self._optimize_model_hyperparameters()
            
            # 8. Update ensemble weights
            await self._optimize_ensemble_weights()
            
            # Update system status
            self.system_status['last_training'] = datetime.now()
            self.system_status['training_completed'] = True
            
            self.logger.info("✅ Comprehensive model training completed successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Error in model training: {e}")
            self.system_status['training_completed'] = False
    
    async def _train_sentiment_models(self):
        """آموزش مدل‌های sentiment"""
        try:
            self.logger.info("📊 Training sentiment models...")
            
            # Mock training data
            training_data = [
                {"text": "Bitcoin price is rising", "label": "positive"},
                {"text": "Market is bearish", "label": "negative"},
                {"text": "Stable market conditions", "label": "neutral"}
            ]
            
            for model_name, model in self.sentiment_models.items():
                try:
                    # Mock training process
                    self.logger.info(f"🔄 Training {model_name}...")
                    await asyncio.sleep(1)  # Simulate training time
                    
                    # Update model performance
                    performance = ModelPerformance(
                        model_name=model_name,
                        model_type=ModelType.SENTIMENT,
                        accuracy=0.85,
                        precision=0.82,
                        recall=0.88,
                        f1_score=0.85,
                        sharpe_ratio=1.2,
                        max_drawdown=0.08,
                        win_rate=0.68,
                        profit_factor=1.8,
                        training_time=1.0,
                        inference_time=0.05,
                        last_updated=datetime.now(),
                        market_conditions=[MarketCondition.TRENDING_UP, MarketCondition.VOLATILE],
                        confidence_score=0.85
                    )
                    
                    self.model_performances[model_name] = performance
                    self.logger.info(f"✅ {model_name} training completed")
                    
                except Exception as e:
                    self.logger.error(f"❌ {model_name} training failed: {e}")
            
        except Exception as e:
            self.logger.error(f"❌ Sentiment models training failed: {e}")
    
    async def _train_timeseries_models(self):
        """آموزش مدل‌های time series"""
        try:
            self.logger.info("📈 Training time series models...")
            
            # Mock training data
            training_data = np.random.normal(0, 1, 1000)
            
            for model_name, model in self.timeseries_models.items():
                try:
                    # Mock training process
                    self.logger.info(f"🔄 Training {model_name}...")
                    await asyncio.sleep(1)  # Simulate training time
                    
                    # Update model performance
                    performance = ModelPerformance(
                        model_name=model_name,
                        model_type=ModelType.TIMESERIES,
                        accuracy=0.78,
                        precision=0.75,
                        recall=0.80,
                        f1_score=0.77,
                        sharpe_ratio=1.1,
                        max_drawdown=0.12,
                        win_rate=0.62,
                        profit_factor=1.5,
                        training_time=2.0,
                        inference_time=0.08,
                        last_updated=datetime.now(),
                        market_conditions=[MarketCondition.SIDEWAYS, MarketCondition.TRENDING_UP],
                        confidence_score=0.78
                    )
                    
                    self.model_performances[model_name] = performance
                    self.logger.info(f"✅ {model_name} training completed")
                    
                except Exception as e:
                    self.logger.error(f"❌ {model_name} training failed: {e}")
            
        except Exception as e:
            self.logger.error(f"❌ Time series models training failed: {e}")
    
    async def _train_rl_models(self):
        """آموزش مدل‌های یادگیری تقویتی"""
        try:
            self.logger.info("🤖 Training Reinforcement Learning models...")
            
            # Import RL components
            from .rl_models import RLModelFactory
            from env.trading_env import TradingEnv
            
            rl_factory = RLModelFactory()
            
            # Create trading environment
            env = TradingEnv(
                df=pd.DataFrame({
                    'open': [1.1] * 100,
                    'high': [1.11] * 100,
                    'low': [1.09] * 100,
                    'close': [1.105] * 100,
                    'volume': [1000] * 100
                }),
                symbol="EURUSD",
                style="default",
                timeframe="H1",
                initial_balance=10000,
                commission=0.001
            )
            
            # Define RL model configurations
            rl_configs = [
                {"model_type": "ppo", "params": {"n_steps": 2048}},
                {"model_type": "a2c", "params": {}},
                {"model_type": "dqn", "params": {}},
                {"model_type": "sac", "params": {}},
                {"model_type": "td3", "params": {}},
                {"model_type": "ddpg", "params": {}},
            ]
            
            for config in rl_configs:
                try:
                    model_type = config["model_type"]
                    params = config["params"]
                    
                    self.logger.info(f"Training {model_type.upper()} model...")
                    
                    # Create model
                    model = rl_factory.create_model(model_type, env, **params)
                    
                    # Train model
                    model.learn(total_timesteps=10000)
                    
                    # Save model
                    model_path = f"models/rl_{model_type}_trained.zip"
                    model.save(model_path)
                    
                    # Register in model registry
                    self.model_registry.register(
                        f"rl_{model_type}",
                        model,
                        "reinforcement_learning",
                        {"model_type": model_type, "params": params}
                    )
                    
                    self.logger.info(f"✅ {model_type.upper()} model trained and saved")
                    
                except Exception as e:
                    self.logger.error(f"❌ Error training {model_type}: {e}")
            
            self.logger.info("✅ RL models training completed")
            
        except Exception as e:
            self.logger.error(f"❌ Error in RL training: {e}")
    
    async def _train_continual_learning_models(self):
        """آموزش مدل‌های یادگیری مداوم"""
        try:
            self.logger.info("🔄 Training Continual Learning models...")
            
            from .continual_learning import ContinualLearning, ContinualLearningSystem
            from .rl_models import RLModelFactory
            from env.trading_env import TradingEnv
            
            # Create continual learning system
            cl_system = ContinualLearningSystem()
            
            # Create RL factory and environment
            rl_factory = RLModelFactory()
            env = TradingEnv(
                df=pd.DataFrame({
                    'open': [1.1] * 100,
                    'high': [1.11] * 100,
                    'low': [1.09] * 100,
                    'close': [1.105] * 100,
                    'volume': [1000] * 100
                }),
                symbol="EURUSD",
                style="default",
                timeframe="H1",
                initial_balance=10000,
                commission=0.001
            )
            
            # Create base PPO model for continual learning
            base_model = rl_factory.create_model("ppo", env)
            
            # Initialize continual learning
            cl_model = ContinualLearning(
                model=base_model,
                ewc_lambda=0.4,
                replay_buffer_capacity=10000,
                use_ewc=True,
                use_replay=True,
                use_distillation=True
            )
            
            # Prepare for first task
            cl_model.prepare_for_new_task("market_regime_1")
            
            # Train on first task
            cl_model.train_with_replay(batch_size=32, replay_ratio=0.5)
            
            # Save continual learning model
            cl_model.save("models/continual_learning_model.pkl")
            
            # Register in model registry
            self.model_registry.register(
                "continual_learning",
                cl_model,
                "continual_learning",
                {"ewc_lambda": 0.4, "replay_capacity": 10000}
            )
            
            self.logger.info("✅ Continual Learning model trained and saved")
            
        except Exception as e:
            self.logger.error(f"❌ Error in Continual Learning training: {e}")
    
    async def _train_deep_learning_models(self):
        """آموزش مدل‌های یادگیری عمیق"""
        try:
            self.logger.info("🧠 Training Deep Learning models...")
            
            # Import deep learning models
            from ai_models import (
                LayoutLMModel, T5Model, BERTModel, BARTModel,
                DocumentAnalyzer
            )
            
            # Train LayoutLM for document analysis
            try:
                layout_model = LayoutLMModel("microsoft/layoutlm-base-uncased")
                if layout_model.load():
                    self.model_registry.register(
                        "layoutlm",
                        layout_model,
                        "document_analysis",
                        {"model_name": "microsoft/layoutlm-base-uncased"}
                    )
                    self.logger.info("✅ LayoutLM model loaded")
            except Exception as e:
                self.logger.warning(f"⚠️ LayoutLM training failed: {e}")
            
            # Train T5 for text generation
            try:
                t5_model = T5Model("t5-base")
                if t5_model.load():
                    self.model_registry.register(
                        "t5",
                        t5_model,
                        "text_generation",
                        {"model_name": "t5-base"}
                    )
                    self.logger.info("✅ T5 model loaded")
            except Exception as e:
                self.logger.warning(f"⚠️ T5 training failed: {e}")
            
            # Train BERT for text encoding
            try:
                bert_model = BERTModel("bert-base-uncased")
                if bert_model.load():
                    self.model_registry.register(
                        "bert",
                        bert_model,
                        "text_encoding",
                        {"model_name": "bert-base-uncased"}
                    )
                    self.logger.info("✅ BERT model loaded")
            except Exception as e:
                self.logger.warning(f"⚠️ BERT training failed: {e}")
            
            # Train BART for summarization
            try:
                bart_model = BARTModel("facebook/bart-large-cnn")
                if bart_model.load():
                    self.model_registry.register(
                        "bart",
                        bart_model,
                        "text_summarization",
                        {"model_name": "facebook/bart-large-cnn"}
                    )
                    self.logger.info("✅ BART model loaded")
            except Exception as e:
                self.logger.warning(f"⚠️ BART training failed: {e}")
            
            # Train Document Analyzer
            try:
                doc_analyzer = DocumentAnalyzer("document_analyzer")
                if doc_analyzer.load():
                    self.model_registry.register(
                        "document_analyzer",
                        doc_analyzer,
                        "document_analysis",
                        {"model_name": "document_analyzer"}
                    )
                    self.logger.info("✅ Document Analyzer loaded")
            except Exception as e:
                self.logger.warning(f"⚠️ Document Analyzer training failed: {e}")
            
            self.logger.info("✅ Deep Learning models training completed")
            
        except Exception as e:
            self.logger.error(f"❌ Error in Deep Learning training: {e}")
    
    async def _train_ensemble_models(self):
        """آموزش مدل‌های ensemble"""
        try:
            self.logger.info("🎯 Training Ensemble models...")
            
            from .ensemble_model import EnsembleModel
            from .rl_models import RLModelFactory
            from env.trading_env import TradingEnv
            
            # Create RL factory and environment
            rl_factory = RLModelFactory()
            env = TradingEnv(
                df=pd.DataFrame({
                    'open': [1.1] * 100,
                    'high': [1.11] * 100,
                    'low': [1.09] * 100,
                    'close': [1.105] * 100,
                    'volume': [1000] * 100
                }),
                symbol="EURUSD",
                style="default",
                timeframe="H1",
                initial_balance=10000,
                commission=0.001
            )
            
            # Define ensemble configurations
            ensemble_configs = [
                {
                    "model_type": "ppo",
                    "params": {"n_steps": 2048}
                },
                {
                    "model_type": "a2c", 
                    "params": {}
                },
                {
                    "model_type": "sac",
                    "params": {}
                }
            ]
            
            # Create ensemble model
            ensemble = EnsembleModel(
                model_configs=ensemble_configs,
                env=env,
                voting_method='weighted',
                weight_update_freq=100,
                confidence_threshold=0.6
            )
            
            # Train ensemble (this will train individual models)
            # The ensemble combines predictions from trained models
            
            # Save ensemble model
            ensemble.save("models/ensemble_model.pkl")
            
            # Register in model registry
            self.model_registry.register(
                "rl_ensemble",
                ensemble,
                "ensemble",
                {"voting_method": "weighted", "num_models": len(ensemble_configs)}
            )
            
            self.logger.info("✅ Ensemble model trained and saved")
            
        except Exception as e:
            self.logger.error(f"❌ Error in Ensemble training: {e}")

# Convenience function
def create_ai_agent(config_path: str = "config.yaml") -> AIAgent:
    """ایجاد AI Agent"""
    return AIAgent(config_path) 