import sys
import os

# Add project root to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    print("Attempting to import core.order_manager...")
    from core import order_manager
    print("✅ Successfully imported core.order_manager")
    print(f"OrderManager object: {order_manager.AdvancedOrderManager}")
except ImportError as e:
    print(f"❌ Failed to import core.order_manager.")
    print(f"Error: {e}")
    import traceback
    traceback.print_exc() 