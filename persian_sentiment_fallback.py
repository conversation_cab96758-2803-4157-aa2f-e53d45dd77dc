# -*- coding: utf-8 -*-
"""
Persian Sentiment Fallback
پیاده‌سازی جایگزین برای sentiment فارسی
"""

import re
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class PersianSentimentResult:
    """نتیجه تحلیل احساسات فارسی"""
    label: str
    score: float
    confidence: float
    market_impact: float = 0.0

class PersianSentimentAnalyzer:
    """تحلیل احساسات فارسی بدون وابستگی به مدل"""
    
    def __init__(self):
        self.positive_words = {
            'خوب', 'عالی', 'بهتر', 'بهترین', 'موفق', 'موفقیت', 'پیشرفت', 'رشد', 'بالا', 'صعود',
            'سود', 'برنده', 'قوی', 'پربازده', 'مثبت', 'خوشبین', 'امیدوار', 'روشن', 'پیروز',
            'افزایش', 'بهبود', 'توسعه', 'گسترش', 'تقویت', 'بازگشت', 'احیا', 'نوسان مثبت'
        }
        
        self.negative_words = {
            'بد', 'ضعیف', 'افت', 'کاهش', 'ریزش', 'سقوط', 'نزول', 'کم', 'پایین', 'منفی',
            'زیان', 'ضرر', 'شکست', 'بدبین', 'نگران', 'تاریک', 'بحران', 'رکود', 'تنزل',
            'کسری', 'کاستی', 'تضعیف', 'فروپاشی', 'عقبگرد', 'تراجع', 'نوسان منفی'
        }
        
        self.neutral_words = {
            'عادی', 'معمولی', 'متوسط', 'طبیعی', 'ثابت', 'بدون تغییر', 'همان', 'مشابه',
            'معادل', 'برابر', 'مساوی', 'استاندارد', 'پایه', 'اصلی', 'اولیه'
        }
        
        self.financial_terms = {
            'قیمت', 'نرخ', 'ارز', 'دلار', 'یورو', 'طلا', 'سهام', 'بورس', 'بازار', 'سرمایه',
            'سود', 'زیان', 'درآمد', 'هزینه', 'فروش', 'خرید', 'سرمایه‌گذاری', 'تجارت'
        }
    
    def analyze(self, text: str) -> PersianSentimentResult:
        """تحلیل احساسات متن فارسی"""
        if not text:
            return PersianSentimentResult("neutral", 0.0, 0.0)
        
        # Clean text
        text = text.strip()
        words = re.findall(r'[\u0600-\u06FF]+', text)
        
        positive_score = 0
        negative_score = 0
        financial_relevance = 0
        
        # Calculate scores
        for word in words:
            if word in self.positive_words:
                positive_score += 1
            elif word in self.negative_words:
                negative_score += 1
            elif word in self.financial_terms:
                financial_relevance += 1
        
        # Calculate final sentiment
        total_sentiment_words = positive_score + negative_score
        
        if total_sentiment_words == 0:
            return PersianSentimentResult("neutral", 0.0, 0.5)
        
        # Calculate sentiment score
        sentiment_score = (positive_score - negative_score) / total_sentiment_words
        
        # Determine label
        if sentiment_score > 0.1:
            label = "positive"
        elif sentiment_score < -0.1:
            label = "negative"
        else:
            label = "neutral"
        
        # Calculate confidence
        confidence = min(abs(sentiment_score) + 0.3, 1.0)
        
        # Calculate market impact
        market_impact = sentiment_score * (1 + financial_relevance * 0.1)
        
        return PersianSentimentResult(
            label=label,
            score=sentiment_score,
            confidence=confidence,
            market_impact=market_impact
        )
    
    def test_analyzer(self):
        """تست تحلیل‌گر"""
        test_texts = [
            "بازار امروز خوب بود و قیمت طلا بالا رفت",
            "وضعیت اقتصادی بد است و بورس ریزش کرد",
            "نرخ دلار ثابت مانده و تغییری نداشته",
            "سرمایه‌گذاری در این شرکت موفق خواهد بود",
            "بحران مالی باعث کاهش سود شد"
        ]
        
        print("🧪 Testing Persian Sentiment Analyzer:")
        for text in test_texts:
            result = self.analyze(text)
            print(f"Text: {text}")
            print(f"Result: {result.label} (score: {result.score:.2f}, confidence: {result.confidence:.2f})")
            print("-" * 50)
        
        return True

# Global analyzer instance
persian_analyzer = PersianSentimentAnalyzer()

def analyze_persian_text(text: str) -> Dict[str, Any]:
    """تحلیل متن فارسی"""
    result = persian_analyzer.analyze(text)
    return {
        'label': result.label,
        'score': result.score,
        'confidence': result.confidence,
        'market_impact': result.market_impact
    }

if __name__ == "__main__":
    persian_analyzer.test_analyzer()
