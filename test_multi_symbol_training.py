"""
🌍 Test Multi-Symbol Training Implementation
تست پیاده‌سازی آموزش چند نمادی

این تست شامل:
1. تست MultiSymbolDataManager
2. تست DynamicRewardSystem
3. تست Multi-Symbol Training
4. تست Symbol Rotation
5. تست Generalization Score
"""

import os
import sys
import time
import json
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_multi_symbol_data_manager():
    """تست مدیر داده‌های چند نمادی"""
    print("🌍 Testing MultiSymbolDataManager...")
    
    try:
        from training.train_rl import MultiSymbolDataManager
        
        # Initialize with test symbols
        symbols = ["EURUSD", "GBPUSD", "USDJPY"]
        manager = MultiSymbolDataManager(symbols, universal_features=True)
        
        print(f"✅ Manager initialized with {len(symbols)} symbols")
        
        # Test data generation
        for symbol in symbols:
            data = manager.generate_symbol_data(symbol)
            print(f"  📊 {symbol}: {len(data)} data points generated")
            
            # Test normalized features
            normalized_data = manager.get_normalized_features(symbol)
            print(f"    ✅ Normalized features: {normalized_data.shape}")
        
        # Test symbol rotation
        rotation_sequence = manager.get_symbol_rotation_sequence("random", 100, 25)
        print(f"✅ Rotation sequence generated: {len(rotation_sequence)} episodes")
        
        # Check rotation distribution
        symbol_counts = {symbol: rotation_sequence.count(symbol) for symbol in symbols}
        print(f"  📈 Symbol distribution: {symbol_counts}")
        
        return True
        
    except Exception as e:
        print(f"❌ MultiSymbolDataManager test failed: {e}")
        return False

def test_dynamic_reward_system():
    """تست سیستم پاداش پویا"""
    print("\n🎯 Testing DynamicRewardSystem...")
    
    try:
        from training.train_rl import DynamicRewardSystem
        
        # Initialize reward system
        reward_components = {
            "profit": 0.4,
            "risk_adjusted": 0.25,
            "drawdown_penalty": 0.2,
            "consistency": 0.15
        }
        
        reward_system = DynamicRewardSystem(reward_components, market_adaptation=True)
        print("✅ DynamicRewardSystem initialized")
        
        # Test reward calculation
        test_scenarios = [
            {"action": 1, "prev_balance": 10000, "current_balance": 10100, "position": 1, "price_change": 0.01},
            {"action": 2, "prev_balance": 10100, "current_balance": 10050, "position": -1, "price_change": -0.005},
            {"action": 0, "prev_balance": 10050, "current_balance": 10050, "position": 0, "price_change": 0.0}
        ]
        
        for i, scenario in enumerate(test_scenarios):
            reward = reward_system.calculate_reward(
                action=scenario["action"],
                prev_balance=scenario["prev_balance"],
                current_balance=scenario["current_balance"],
                position=scenario["position"],
                price_change=scenario["price_change"],
                step=i,
                max_steps=100
            )
            print(f"  📊 Scenario {i+1}: Reward = {reward:.4f}")
        
        print("✅ Dynamic reward calculation working")
        return True
        
    except Exception as e:
        print(f"❌ DynamicRewardSystem test failed: {e}")
        return False

def test_multi_symbol_training():
    """تست آموزش چند نمادی"""
    print("\n🚀 Testing Multi-Symbol Training...")
    
    try:
        from training.train_rl import RLTrainingConfig, PearlRLTrainer
        
        # Create multi-symbol configuration
        config = RLTrainingConfig(
            model_name="Test_PPO_MultiSymbol",
            algorithm="ppo",
            num_episodes=50,  # Short test
            symbols=["EURUSD", "GBPUSD"],
            symbol_rotation_strategy="sequential",
            episodes_per_symbol=25,
            dynamic_rewards=True,
            universal_features=True
        )
        
        print(f"✅ Multi-symbol config created: {len(config.symbols)} symbols")
        
        # Initialize trainer
        trainer = PearlRLTrainer(config)
        print("✅ Multi-symbol trainer initialized")
        
        # Test symbol switching
        print("🔄 Testing symbol switching...")
        trainer.prepare_environment("EURUSD")
        print(f"  📊 Current symbol: {trainer.current_symbol}")
        
        trainer.switch_symbol("GBPUSD")
        print(f"  🔄 Switched to: {trainer.current_symbol}")
        
        # Test symbol rotation sequence
        rotation_sequence = trainer.symbol_rotation_sequence[:10]
        print(f"  📈 First 10 episodes rotation: {rotation_sequence}")
        
        print("✅ Multi-symbol training components working")
        return True
        
    except Exception as e:
        print(f"❌ Multi-symbol training test failed: {e}")
        return False

def test_trading_environment_enhancements():
    """تست بهبودهای محیط معاملاتی"""
    print("\n🏪 Testing Trading Environment Enhancements...")
    
    try:
        from training.train_rl import TradingEnvironment, DynamicRewardSystem, MultiSymbolDataManager
        import pandas as pd
        import numpy as np
        
        # Create test data
        manager = MultiSymbolDataManager(["EURUSD"])
        data = manager.generate_symbol_data("EURUSD")
        
        # Create dynamic reward system
        reward_system = DynamicRewardSystem({
            "profit": 0.4,
            "risk_adjusted": 0.25,
            "drawdown_penalty": 0.2,
            "consistency": 0.15
        })
        
        # Create enhanced trading environment
        env = TradingEnvironment(
            data=data,
            symbol="EURUSD",
            dynamic_reward_system=reward_system
        )
        
        print("✅ Enhanced trading environment created")
        
        # Test environment steps
        state = env.reset()
        print(f"  📊 Initial state shape: {state.shape}")
        
        total_reward = 0
        for step in range(10):
            action = np.random.choice([0, 1, 2])  # Random action
            state, reward, done, info = env.step(action)
            total_reward += reward
            
            if done:
                break
        
        print(f"  🎯 Test run completed: Total reward = {total_reward:.4f}")
        print(f"  💰 Final balance: {env.balance:.2f}")
        print(f"  📈 Trade history: {len(env.trade_history)} trades")
        
        return True
        
    except Exception as e:
        print(f"❌ Trading environment test failed: {e}")
        return False

def test_generalization_score():
    """تست امتیاز تعمیم‌پذیری"""
    print("\n📊 Testing Generalization Score...")
    
    try:
        from training.train_rl import PearlRLTrainer, RLTrainingConfig
        
        # Create trainer with mock data
        config = RLTrainingConfig(
            model_name="Test_Generalization",
            algorithm="ppo",
            symbols=["EURUSD", "GBPUSD", "USDJPY"]
        )
        
        trainer = PearlRLTrainer(config)
        
        # Mock symbol performance data
        trainer.symbol_performance = {
            "EURUSD": [100, 110, 105, 115, 120],
            "GBPUSD": [95, 105, 100, 110, 115],
            "USDJPY": [90, 100, 95, 105, 110]
        }
        
        # Calculate generalization score
        score = trainer._calculate_generalization_score()
        print(f"✅ Generalization score calculated: {score:.4f}")
        
        # Test with perfect consistency
        trainer.symbol_performance = {
            "EURUSD": [100, 100, 100],
            "GBPUSD": [100, 100, 100],
            "USDJPY": [100, 100, 100]
        }
        
        perfect_score = trainer._calculate_generalization_score()
        print(f"  🎯 Perfect consistency score: {perfect_score:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Generalization score test failed: {e}")
        return False

def run_quick_training_demo():
    """اجرای نمایش سریع آموزش"""
    print("\n🚀 Running Quick Multi-Symbol Training Demo...")
    
    try:
        from training.train_rl import RLTrainingConfig, PearlRLTrainer
        
        # Create quick demo configuration
        config = RLTrainingConfig(
            model_name="Demo_PPO_MultiSymbol",
            algorithm="ppo",
            num_episodes=20,  # Very short demo
            symbols=["EURUSD", "GBPUSD"],
            symbol_rotation_strategy="random",
            episodes_per_symbol=10,
            dynamic_rewards=True,
            universal_features=True,
            max_steps_per_episode=50  # Short episodes
        )
        
        print(f"🎯 Starting demo training: {config.num_episodes} episodes")
        
        # Initialize and run training
        trainer = PearlRLTrainer(config)
        
        # Prepare environment
        trainer.prepare_environment()
        print(f"✅ Environment prepared for {trainer.current_symbol}")
        
        # Run a few training episodes manually
        for episode in range(5):
            # Check symbol rotation
            target_symbol = trainer.symbol_rotation_sequence[episode]
            if target_symbol != trainer.current_symbol:
                trainer.switch_symbol(target_symbol)
            
            print(f"  Episode {episode+1} on {trainer.current_symbol}")
            
            # Simulate episode (simplified)
            time.sleep(0.1)  # Small delay for demo
        
        print("✅ Quick training demo completed successfully!")
        
        # Show symbol performance
        print(f"📊 Symbol switches: {trainer.symbol_switch_count}")
        for symbol, rewards in trainer.symbol_performance.items():
            if rewards:
                avg_reward = sum(rewards) / len(rewards)
                print(f"  {symbol}: {len(rewards)} episodes, avg reward: {avg_reward:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Quick training demo failed: {e}")
        return False

def main():
    """اجرای تمام تست‌ها"""
    print("🌍 MULTI-SYMBOL TRAINING TESTS")
    print("=" * 60)
    
    tests = [
        ("MultiSymbolDataManager", test_multi_symbol_data_manager),
        ("DynamicRewardSystem", test_dynamic_reward_system),
        ("Multi-Symbol Training", test_multi_symbol_training),
        ("Trading Environment Enhancements", test_trading_environment_enhancements),
        ("Generalization Score", test_generalization_score),
        ("Quick Training Demo", run_quick_training_demo)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 MULTI-SYMBOL TRAINING TEST RESULTS")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    success_rate = (passed / total) * 100
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Success Rate: {success_rate:.1f}% ({passed}/{total})")
    
    if success_rate >= 80:
        print("🎉 Multi-Symbol Training implementation is working excellently!")
        print("🚀 Ready for full-scale multi-symbol training!")
    elif success_rate >= 60:
        print("⚠️ Multi-Symbol Training mostly working, minor issues remain")
    else:
        print("❌ Multi-Symbol Training needs significant improvements")
    
    # Save results
    results_file = f"multi_symbol_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            "test_results": results,
            "success_rate": success_rate,
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total,
                "passed_tests": passed,
                "failed_tests": total - passed
            }
        }, f, indent=2, ensure_ascii=False)
    
    print(f"📄 Detailed results saved to: {results_file}")
    
    return success_rate

if __name__ == "__main__":
    success_rate = main()
    exit(0 if success_rate >= 80 else 1)
