import matplotlib
matplotlib.use('Agg')
import os
import gym
from models.rl_models import RLModelFactory
from stable_baselines3.common.vec_env import DummyVecEnv

def test_ppo_cross_market_resume():
    factory = RLModelFactory()
    env1 = gym.make('CartPole-v1')
    model = factory.create_model('ppo', env1)
    model.learn(total_timesteps=50)
    ckpt_path = 'ppo_cross_market_ckpt.zip'
    factory.save_checkpoint(model, ckpt_path)
    assert os.path.exists(ckpt_path)
    env2 = gym.make('CartPole-v0')
    resumed_model = factory.resume_training('ppo', env2, ckpt_path, new_env=env2)
    env2_vec = DummyVecEnv([lambda: env2])
    resumed_model.set_env(env2_vec)
    obs = env2_vec.reset()
    resumed_model.learn(total_timesteps=1, reset_num_timesteps=False)
    resumed_model.learn(total_timesteps=10, reset_num_timesteps=False)
    obs = env2_vec.reset()
    action, _ = resumed_model.predict(obs)
    assert action is not None
    os.remove(ckpt_path)

def test_ppo_curriculum_resume():
    factory = RLModelFactory()
    env1 = gym.make('CartPole-v1')
    env2 = gym.make('CartPole-v0')
    model = factory.create_model('ppo', env1)
    model.learn(total_timesteps=20)
    ckpt_path = 'ppo_curriculum_ckpt.zip'
    factory.save_checkpoint(model, ckpt_path)
    assert os.path.exists(ckpt_path)
    curriculum_steps = [(env1, 10), (env2, 10)]
    resumed_model = factory.resume_training('ppo', env1, ckpt_path, curriculum_steps=curriculum_steps)
    env2_vec = DummyVecEnv([lambda: env2])
    resumed_model.set_env(env2_vec)
    obs = env2_vec.reset()
    resumed_model.learn(total_timesteps=1, reset_num_timesteps=False)
    action, _ = resumed_model.predict(obs)
    assert action is not None
    os.remove(ckpt_path)
