#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗄️ Simple Database Transaction Management
سیستم ساده مدیریت تراکنش‌های دیتابیس
"""

import os
import sys
import time
import sqlite3
import threading
import logging
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from contextlib import contextmanager
from dataclasses import dataclass, field
from enum import Enum
from functools import wraps

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logger = logging.getLogger(__name__)

class TransactionStatus(str, Enum):
    """وضعیت تراکنش"""
    PENDING = "pending"
    COMMITTED = "committed"
    ROLLED_BACK = "rolled_back"
    FAILED = "failed"

class TransactionType(str, Enum):
    """نوع تراکنش"""
    READ = "read"
    WRITE = "write"
    BULK_INSERT = "bulk_insert"
    BULK_UPDATE = "bulk_update"
    BULK_DELETE = "bulk_delete"

@dataclass
class TransactionMetrics:
    """متریک‌های تراکنش"""
    transaction_id: str
    transaction_type: TransactionType
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    status: TransactionStatus = TransactionStatus.PENDING
    affected_rows: int = 0
    error_message: Optional[str] = None
    retry_count: int = 0

class SimpleConnectionPool:
    """مجموعه ساده اتصالات دیتابیس"""
    
    def __init__(self, database_path: str, max_connections: int = 10):
        self.database_path = database_path
        self.max_connections = max_connections
        self._connections = []
        self._lock = threading.Lock()
        self._is_initialized = False
        
    def initialize(self) -> bool:
        """راه‌اندازی pool اتصالات"""
        try:
            # Test connection
            conn = sqlite3.connect(self.database_path)
            conn.close()
            
            self._is_initialized = True
            logger.info("✅ Simple database connection pool initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error initializing connection pool: {e}")
            return False
    
    def get_connection(self) -> sqlite3.Connection:
        """دریافت اتصال"""
        if not self._is_initialized:
            if not self.initialize():
                raise RuntimeError("Connection pool not initialized")
        
        # Create new connection (SQLite handles concurrency well)
        conn = sqlite3.connect(self.database_path, timeout=30.0)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        conn.execute("PRAGMA journal_mode=WAL")  # Enable WAL mode for better concurrency
        conn.execute("PRAGMA synchronous=NORMAL")  # Balance performance and safety
        conn.execute("PRAGMA temp_store=memory")  # Use memory for temporary storage
        conn.execute("PRAGMA mmap_size=268435456")  # 256MB mmap
        
        return conn
    
    def close(self):
        """بستن pool"""
        with self._lock:
            for conn in self._connections:
                try:
                    conn.close()
                except:
                    pass
            self._connections.clear()
        logger.info("✅ Connection pool closed")

class SimpleTransactionManager:
    """مدیر ساده تراکنش‌ها"""
    
    def __init__(self, connection_pool: SimpleConnectionPool):
        self.connection_pool = connection_pool
        self.active_transactions: Dict[str, TransactionMetrics] = {}
        self.transaction_history: List[TransactionMetrics] = []
        self._lock = threading.Lock()
        self._transaction_counter = 0
        
    def generate_transaction_id(self) -> str:
        """تولید شناسه تراکنش"""
        with self._lock:
            self._transaction_counter += 1
            return f"txn_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self._transaction_counter:06d}"
    
    @contextmanager
    def transaction(self, transaction_type: TransactionType = TransactionType.WRITE, 
                   timeout: int = 30, retry_count: int = 3):
        """Context manager برای مدیریت تراکنش"""
        transaction_id = self.generate_transaction_id()
        connection = None
        
        # Create transaction metrics
        metrics = TransactionMetrics(
            transaction_id=transaction_id,
            transaction_type=transaction_type,
            start_time=datetime.now()
        )
        
        with self._lock:
            self.active_transactions[transaction_id] = metrics
        
        try:
            connection = self.connection_pool.get_connection()
            connection.execute("BEGIN")
            
            logger.debug(f"🔄 Transaction started: {transaction_id} ({transaction_type.value})")
            
            yield connection, metrics
            
            # Commit transaction
            connection.commit()
            metrics.status = TransactionStatus.COMMITTED
            metrics.end_time = datetime.now()
            metrics.duration = (metrics.end_time - metrics.start_time).total_seconds()
            
            logger.debug(f"✅ Transaction committed: {transaction_id} ({metrics.duration:.3f}s)")
            
        except Exception as e:
            if connection:
                connection.rollback()
            
            metrics.status = TransactionStatus.ROLLED_BACK
            metrics.end_time = datetime.now()
            metrics.duration = (metrics.end_time - metrics.start_time).total_seconds()
            metrics.error_message = str(e)
            
            logger.error(f"❌ Transaction rolled back: {transaction_id} - {e}")
            
            # Retry logic for certain errors
            if retry_count > 0 and "database is locked" in str(e).lower():
                logger.info(f"🔄 Retrying transaction: {transaction_id}")
                time.sleep(0.5)  # Wait before retry
                with self.transaction(transaction_type, timeout, retry_count - 1) as (retry_conn, retry_metrics):
                    yield retry_conn, retry_metrics
            else:
                raise
        
        finally:
            if connection:
                connection.close()
            
            # Move to history
            with self._lock:
                if transaction_id in self.active_transactions:
                    del self.active_transactions[transaction_id]
                self.transaction_history.append(metrics)
                
                # Keep only last 1000 transactions
                if len(self.transaction_history) > 1000:
                    self.transaction_history = self.transaction_history[-1000:]
    
    def get_transaction_statistics(self) -> Dict[str, Any]:
        """دریافت آمار تراکنش‌ها"""
        with self._lock:
            total_transactions = len(self.transaction_history)
            if total_transactions == 0:
                return {}
            
            committed = sum(1 for t in self.transaction_history if t.status == TransactionStatus.COMMITTED)
            rolled_back = sum(1 for t in self.transaction_history if t.status == TransactionStatus.ROLLED_BACK)
            
            durations = [t.duration for t in self.transaction_history if t.duration is not None]
            avg_duration = sum(durations) / len(durations) if durations else 0
            
            return {
                "total_transactions": total_transactions,
                "committed": committed,
                "rolled_back": rolled_back,
                "success_rate": (committed / total_transactions) * 100 if total_transactions > 0 else 0,
                "average_duration": avg_duration,
                "active_transactions": len(self.active_transactions)
            }

class SimpleDatabaseManager:
    """مدیر ساده دیتابیس"""
    
    def __init__(self, database_path: str = "trading_system.db"):
        self.database_path = database_path
        self.connection_pool = SimpleConnectionPool(database_path)
        self.transaction_manager = SimpleTransactionManager(self.connection_pool)
        self._is_initialized = False
        
        # SQL statements
        self.create_tables_sql = """
        CREATE TABLE IF NOT EXISTS trading_signals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            signal_type TEXT NOT NULL,
            price REAL NOT NULL,
            confidence REAL DEFAULT 0.0,
            source TEXT NOT NULL,
            signal_metadata TEXT,
            processed BOOLEAN DEFAULT 0,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_signals_symbol ON trading_signals(symbol);
        CREATE INDEX IF NOT EXISTS idx_signals_timestamp ON trading_signals(timestamp);
        CREATE INDEX IF NOT EXISTS idx_signals_processed ON trading_signals(processed);
        
        CREATE TABLE IF NOT EXISTS trading_positions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            position_type TEXT NOT NULL,
            entry_price REAL NOT NULL,
            exit_price REAL,
            quantity REAL NOT NULL,
            status TEXT DEFAULT 'open',
            profit_loss REAL DEFAULT 0.0,
            opened_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            closed_at DATETIME,
            position_metadata TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_positions_symbol ON trading_positions(symbol);
        CREATE INDEX IF NOT EXISTS idx_positions_status ON trading_positions(status);
        
        CREATE TABLE IF NOT EXISTS trading_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transaction_id TEXT UNIQUE NOT NULL,
            symbol TEXT NOT NULL,
            transaction_type TEXT NOT NULL,
            price REAL NOT NULL,
            quantity REAL NOT NULL,
            commission REAL DEFAULT 0.0,
            total_amount REAL NOT NULL,
            status TEXT DEFAULT 'pending',
            transaction_metadata TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_transactions_symbol ON trading_transactions(symbol);
        CREATE INDEX IF NOT EXISTS idx_transactions_timestamp ON trading_transactions(timestamp);
        CREATE INDEX IF NOT EXISTS idx_transactions_status ON trading_transactions(status);
        """
    
    def initialize(self) -> bool:
        """راه‌اندازی مدیر دیتابیس"""
        try:
            # Initialize connection pool
            if not self.connection_pool.initialize():
                return False
            
            # Create tables
            with self.transaction_manager.transaction(TransactionType.WRITE) as (conn, metrics):
                for statement in self.create_tables_sql.split(';'):
                    if statement.strip():
                        conn.execute(statement)
            
            self._is_initialized = True
            logger.info("✅ Simple database manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error initializing database manager: {e}")
            return False
    
    def insert_trading_signal(self, symbol: str, signal_type: str, price: float, 
                            confidence: float = 0.0, source: str = "system", 
                            metadata: Optional[Dict[str, Any]] = None) -> Optional[int]:
        """درج سیگنال معاملاتی"""
        try:
            with self.transaction_manager.transaction(TransactionType.WRITE) as (conn, metrics):
                cursor = conn.execute(
                    """
                    INSERT INTO trading_signals 
                    (symbol, signal_type, price, confidence, source, signal_metadata)
                    VALUES (?, ?, ?, ?, ?, ?)
                    """,
                    (symbol, signal_type, price, confidence, source, 
                     json.dumps(metadata) if metadata else None)
                )
                
                metrics.affected_rows = cursor.rowcount
                return cursor.lastrowid
                
        except Exception as e:
            logger.error(f"❌ Error inserting trading signal: {e}")
            return None
    
    def get_trading_signals(self, symbol: Optional[str] = None, 
                           processed: Optional[bool] = None,
                           limit: int = 100) -> List[Dict[str, Any]]:
        """دریافت سیگنال‌های معاملاتی"""
        try:
            with self.transaction_manager.transaction(TransactionType.READ) as (conn, metrics):
                query = "SELECT * FROM trading_signals WHERE 1=1"
                params = []
                
                if symbol:
                    query += " AND symbol = ?"
                    params.append(symbol)
                
                if processed is not None:
                    query += " AND processed = ?"
                    params.append(processed)
                
                query += " ORDER BY created_at DESC LIMIT ?"
                params.append(limit)
                
                cursor = conn.execute(query, params)
                rows = cursor.fetchall()
                
                return [
                    {
                        "id": row["id"],
                        "symbol": row["symbol"],
                        "signal_type": row["signal_type"],
                        "price": row["price"],
                        "confidence": row["confidence"],
                        "source": row["source"],
                        "metadata": json.loads(row["signal_metadata"]) if row["signal_metadata"] else None,
                        "processed": bool(row["processed"]),
                        "timestamp": row["timestamp"],
                        "created_at": row["created_at"]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"❌ Error getting trading signals: {e}")
            return []
    
    def bulk_insert_signals(self, signals: List[Dict[str, Any]]) -> int:
        """درج انبوه سیگنال‌ها"""
        try:
            with self.transaction_manager.transaction(TransactionType.BULK_INSERT) as (conn, metrics):
                data = [
                    (
                        signal.get('symbol'),
                        signal.get('signal_type'),
                        signal.get('price'),
                        signal.get('confidence', 0.0),
                        signal.get('source', 'system'),
                        json.dumps(signal.get('metadata')) if signal.get('metadata') else None
                    )
                    for signal in signals
                ]
                
                conn.executemany(
                    """
                    INSERT INTO trading_signals 
                    (symbol, signal_type, price, confidence, source, signal_metadata)
                    VALUES (?, ?, ?, ?, ?, ?)
                    """,
                    data
                )
                
                metrics.affected_rows = len(signals)
                return len(signals)
                
        except Exception as e:
            logger.error(f"❌ Error bulk inserting signals: {e}")
            return 0
    
    def execute_query(self, query: str, params: Optional[List[Any]] = None) -> List[Dict[str, Any]]:
        """اجرای کوئری"""
        try:
            with self.transaction_manager.transaction(TransactionType.READ) as (conn, metrics):
                cursor = conn.execute(query, params or [])
                rows = cursor.fetchall()
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"❌ Error executing query: {e}")
            return []
    
    def get_database_statistics(self) -> Dict[str, Any]:
        """دریافت آمار دیتابیس"""
        try:
            with self.transaction_manager.transaction(TransactionType.READ) as (conn, metrics):
                stats = {}
                
                # Count tables
                cursor = conn.execute("SELECT COUNT(*) FROM trading_signals")
                stats['trading_signals_count'] = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM trading_positions")
                stats['trading_positions_count'] = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM trading_transactions")
                stats['trading_transactions_count'] = cursor.fetchone()[0]
                
                # Database size
                cursor = conn.execute("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
                stats['database_size_bytes'] = cursor.fetchone()[0]
                
                # Transaction statistics
                stats['transaction_stats'] = self.transaction_manager.get_transaction_statistics()
                
                return stats
                
        except Exception as e:
            logger.error(f"❌ Error getting database statistics: {e}")
            return {}
    
    def cleanup_old_records(self, days: int = 30) -> int:
        """پاک‌سازی رکوردهای قدیمی"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            with self.transaction_manager.transaction(TransactionType.BULK_DELETE) as (conn, metrics):
                # Delete old processed signals
                cursor = conn.execute(
                    "DELETE FROM trading_signals WHERE processed = 1 AND created_at < ?",
                    (cutoff_date,)
                )
                deleted_signals = cursor.rowcount
                
                # Delete old closed positions
                cursor = conn.execute(
                    "DELETE FROM trading_positions WHERE status = 'closed' AND closed_at < ?",
                    (cutoff_date,)
                )
                deleted_positions = cursor.rowcount
                
                total_deleted = deleted_signals + deleted_positions
                metrics.affected_rows = total_deleted
                
                logger.info(f"✅ Cleaned up {total_deleted} old records")
                return total_deleted
                
        except Exception as e:
            logger.error(f"❌ Error cleaning up old records: {e}")
            return 0
    
    def close_connection(self):
        """بستن اتصال دیتابیس (نام جایگزین برای close)"""
        self.close()
    
    def close(self):
        """بستن مدیر دیتابیس"""
        try:
            self.connection_pool.close()
            logger.info("✅ Simple database manager closed")
        except Exception as e:
            logger.error(f"❌ Error closing database manager: {e}")

# Global database manager instance
_database_manager: Optional[SimpleDatabaseManager] = None

def get_database_manager() -> Optional[SimpleDatabaseManager]:
    """دریافت مدیر دیتابیس سراسری"""
    return _database_manager

def initialize_database_manager(database_path: str = "trading_system.db") -> bool:
    """راه‌اندازی مدیر دیتابیس سراسری"""
    global _database_manager
    
    try:
        _database_manager = SimpleDatabaseManager(database_path)
        return _database_manager.initialize()
    except Exception as e:
        logger.error(f"❌ Error initializing global database manager: {e}")
        return False

def close_database_manager():
    """بستن مدیر دیتابیس سراسری"""
    global _database_manager
    
    if _database_manager:
        _database_manager.close()
        _database_manager = None

if __name__ == "__main__":
    """تست سیستم مدیریت دیتابیس"""
    print("🗄️ Testing Simple Database Transaction Management...")
    
    # Initialize database manager
    db_manager = SimpleDatabaseManager("test_trading.db")
    
    if db_manager.initialize():
        print("✅ Database manager initialized successfully")
        
        # Test inserting signals
        signal_id = db_manager.insert_trading_signal(
            symbol="EURUSD",
            signal_type="buy",
            price=1.1234,
            confidence=0.85,
            source="test_system",
            metadata={"test": "data"}
        )
        
        if signal_id:
            print(f"✅ Signal inserted with ID: {signal_id}")
        
        # Test getting signals
        signals = db_manager.get_trading_signals(symbol="EURUSD")
        print(f"✅ Retrieved {len(signals)} signals")
        
        # Test bulk insert
        bulk_signals = [
            {"symbol": "GBPUSD", "signal_type": "sell", "price": 1.2345, "confidence": 0.75},
            {"symbol": "USDJPY", "signal_type": "buy", "price": 110.50, "confidence": 0.90}
        ]
        
        inserted_count = db_manager.bulk_insert_signals(bulk_signals)
        print(f"✅ Bulk inserted {inserted_count} signals")
        
        # Test statistics
        stats = db_manager.get_database_statistics()
        print(f"✅ Database statistics: {stats}")
        
        # Close database manager
        db_manager.close()
        
        print("🎉 Simple Database Transaction Management is ready!")
    else:
        print("❌ Failed to initialize database manager") 