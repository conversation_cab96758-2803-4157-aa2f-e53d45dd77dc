#!/usr/bin/env python3
"""
🔧 Install spaCy Models
نصب مدل‌های spaCy برای entity recognition
"""

import subprocess
import sys
import os

def install_spacy_models():
    """نصب مدل‌های spaCy"""
    try:
        print("🔧 Installing spaCy models...")
        
        # Install English model
        subprocess.check_call([sys.executable, "-m", "spacy", "download", "en_core_web_sm"])
        print("✅ English model (en_core_web_sm) installed")
        
        # Try to install additional models
        try:
            subprocess.check_call([sys.executable, "-m", "spacy", "download", "en_core_web_md"])
            print("✅ English medium model (en_core_web_md) installed")
        except:
            print("⚠️ Medium model not available, using small model")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to install spaCy models: {e}")
        return False

def test_spacy():
    """تست spaCy models"""
    try:
        import spacy
        
        # Test English model
        nlp = spacy.load("en_core_web_sm")
        doc = nlp("Apple Inc. is buying Tesla for $1 billion")
        entities = [(ent.text, ent.label_) for ent in doc.ents]
        
        print(f"✅ spaCy working. Entities found: {entities}")
        return True
        
    except Exception as e:
        print(f"❌ spaCy test failed: {e}")
        return False

def create_spacy_config():
    """ایجاد تنظیمات spaCy"""
    try:
        config_content = """
# spaCy Configuration
# Enhanced entity recognition setup

import spacy
import warnings

# Suppress warnings
warnings.filterwarnings('ignore', category=UserWarning, message='.*spaCy.*')

def load_spacy_model():
    try:
        return spacy.load("en_core_web_sm")
    except OSError:
        try:
            return spacy.load("en_core_web_md")
        except OSError:
            print("⚠️ spaCy models not found. Install with: python -m spacy download en_core_web_sm")
            return None

# Global spaCy model
SPACY_MODEL = load_spacy_model()

print("✅ spaCy configuration loaded")
"""
        
        with open("spacy_config.py", "w") as f:
            f.write(config_content)
        
        print("✅ spaCy configuration saved")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create spaCy config: {e}")
        return False

def main():
    print("🔧 spaCy Models Installation")
    print("=" * 40)
    
    # Install spaCy models
    if install_spacy_models():
        print("\n📊 Testing spaCy...")
        if test_spacy():
            print("✅ spaCy working correctly")
        else:
            print("⚠️ spaCy may have issues")
    
    # Create configuration
    create_spacy_config()
    
    print("\n🎯 spaCy Installation Complete:")
    print("✅ English models installed")
    print("✅ Entity recognition enabled")
    print("✅ Configuration saved")
    print("=" * 40)

if __name__ == "__main__":
    main() 