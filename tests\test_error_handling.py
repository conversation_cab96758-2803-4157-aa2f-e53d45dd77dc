import pytest
import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.sentiment_analyzer import SentimentAnalyzer
from utils.source_credibility import SourceCredibility

@pytest.fixture
def mock_analyzer(monkeypatch):
    """
    Create a SentimentAnalyzer with mocked methods to test error handling.
    """
    analyzer = SentimentAnalyzer()
    
    # Mock the detect_language method to simulate errors
    def mock_detect_language(text):
        if text == "trigger_error":
            raise ValueError("Simulated language detection error")
        return "en"
    
    monkeypatch.setattr(analyzer, 'detect_language', mock_detect_language)
    
    # Return the mocked analyzer
    return analyzer

def test_language_detection_error(mock_analyzer):
    """
    Test that the analyzer handles language detection errors gracefully.
    """
    # This should trigger the error in detect_language
    result = mock_analyzer.analyze("trigger_error")
    
    # Should return neutral sentiment with 0 score when language detection fails
    assert result['label'] == 'neutral'
    assert result['score'] == 0.0
    assert result['weighted_score'] == 0.0

def test_none_input(mock_analyzer):
    """
    Test that the analyzer handles None input gracefully.
    """
    result = mock_analyzer.analyze(None)
    
    # Should return neutral sentiment with 0 score for None input
    assert result['label'] == 'neutral'
    assert result['score'] == 0.0
    assert result['weighted_score'] == 0.0

def test_empty_string(mock_analyzer):
    """
    Test that the analyzer handles empty string input gracefully.
    """
    result = mock_analyzer.analyze("")
    
    # Should return neutral sentiment with 0 score for empty string
    assert result['label'] == 'neutral'
    assert result['score'] == 0.0
    assert result['weighted_score'] == 0.0

def test_empty_list(mock_analyzer):
    """
    Test that the analyzer handles empty list input gracefully.
    """
    result = mock_analyzer.analyze([])
    
    # Should return empty list for empty list input
    assert result == []

def test_list_with_error(mock_analyzer):
    """
    Test that the analyzer handles errors in a list of inputs gracefully.
    """
    results = mock_analyzer.analyze(["normal text", "trigger_error", "another normal text"])
    
    # Should return a list of results with neutral sentiment for the error case
    assert len(results) == 3
    # All results should be neutral since models aren't loaded
    assert results[0]['label'] == 'neutral'
    assert results[1]['label'] == 'neutral'  # Error case should be neutral
    assert results[1]['score'] == 0.0
    assert results[1]['weighted_score'] == 0.0
    assert results[2]['label'] == 'neutral'

def test_get_sentiment_score_error_handling(mock_analyzer):
    """
    Test that get_sentiment_score handles errors gracefully.
    """
    # Should return 0.0 for error case
    score = mock_analyzer.get_sentiment_score("trigger_error")
    assert score == 0.0
    
    # All scores should be 0.0 since models aren't loaded
    normal_score = mock_analyzer.get_sentiment_score("This is a positive text")
    assert normal_score == 0.0 