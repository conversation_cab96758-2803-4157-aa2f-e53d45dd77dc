2025-07-15 00:36:41,261 - INFO - Memory monitoring started
2025-07-15 00:36:41,279 - INFO - Order cleanup thread started
2025-07-15 00:36:41,625 - INFO - Loaded 3 models from registry
2025-07-15 00:36:41,694 - INFO - Advanced Risk Calculator initialized
2025-07-15 00:36:43,515 - INFO - Advanced Correlation Analyzer initialized
2025-07-15 00:36:43,548 - INFO - Advanced Backtesting Engine initialized
2025-07-15 00:36:43,575 - INFO - Circuit breaker 'default' registered
2025-07-15 00:36:43,575 - INFO - Circuit breaker 'api_calls' registered
2025-07-15 00:36:43,575 - INFO - Retry handler 'default' registered
2025-07-15 00:36:43,575 - INFO - Retry handler 'network' registered
2025-07-15 00:37:18,649 - [32mINFO[0m - Device set to use cpu
2025-07-15 00:37:22,069 - [32mINFO[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 00:37:22,070 - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 00:37:27,670 - [32mINFO[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased
2025-07-15 00:37:27,673 - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 00:37:27,679 - [32mINFO[0m - Device set to use cpu
2025-07-15 00:37:30,039 - [32mINFO[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 00:37:30,041 - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 00:37:33,673 - [32mINFO[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased
2025-07-15 00:37:33,674 - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 00:37:33,869 - INFO - Plutus Integrated Trading System initialized
2025-07-15 00:37:33,869 - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 00:37:33,874 - INFO - Plutus Integrated Trading System initialized
2025-07-15 00:37:33,875 - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 00:37:33,985 - INFO - 
============================================================
2025-07-15 00:37:33,988 - INFO - ============================================================
2025-07-15 00:38:50,667 - INFO - Memory monitoring started
2025-07-15 00:38:50,681 - INFO - Order cleanup thread started
2025-07-15 00:38:50,987 - INFO - Loaded 3 models from registry
2025-07-15 00:38:51,017 - INFO - Advanced Risk Calculator initialized
2025-07-15 00:38:52,103 - INFO - Advanced Correlation Analyzer initialized
2025-07-15 00:38:52,107 - INFO - Advanced Backtesting Engine initialized
2025-07-15 00:38:52,115 - INFO - Circuit breaker 'default' registered
2025-07-15 00:38:52,115 - INFO - Circuit breaker 'api_calls' registered
2025-07-15 00:38:52,115 - INFO - Retry handler 'default' registered
2025-07-15 00:38:52,115 - INFO - Retry handler 'network' registered
2025-07-15 00:39:14,712 - [32mINFO[0m - Device set to use cpu
2025-07-15 00:39:19,528 - [32mINFO[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 00:39:19,528 - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 00:39:21,786 - [32mINFO[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased
2025-07-15 00:39:21,786 - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 00:39:21,791 - [32mINFO[0m - Device set to use cpu
2025-07-15 00:39:23,265 - [32mINFO[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 00:39:23,276 - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 00:39:26,023 - [32mINFO[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased
2025-07-15 00:39:26,024 - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 00:39:26,139 - INFO - Plutus Integrated Trading System initialized
2025-07-15 00:39:26,150 - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 00:39:26,155 - INFO - Plutus Integrated Trading System initialized
2025-07-15 00:39:26,155 - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 00:39:26,235 - INFO - 
============================================================
2025-07-15 00:39:26,239 - INFO - ============================================================
2025-07-15 00:48:51,155 - INFO - Memory monitoring started
2025-07-15 00:48:51,186 - INFO - Order cleanup thread started
2025-07-15 00:48:51,428 - INFO - Loaded 3 models from registry
2025-07-15 00:48:51,458 - INFO - Advanced Risk Calculator initialized
2025-07-15 00:48:53,086 - INFO - Advanced Correlation Analyzer initialized
2025-07-15 00:48:53,088 - INFO - Advanced Backtesting Engine initialized
2025-07-15 00:48:53,112 - INFO - Circuit breaker 'default' registered
2025-07-15 00:48:53,112 - INFO - Circuit breaker 'api_calls' registered
2025-07-15 00:48:53,113 - INFO - Retry handler 'default' registered
2025-07-15 00:48:53,113 - INFO - Retry handler 'network' registered
2025-07-15 00:49:15,535 - [32mINFO[0m - Device set to use cpu
2025-07-15 00:49:19,485 - [32mINFO[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 00:49:19,487 - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 00:49:23,417 - [32mINFO[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased
2025-07-15 00:49:23,418 - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 00:49:23,422 - [32mINFO[0m - Device set to use cpu
2025-07-15 00:49:24,817 - [32mINFO[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 00:49:24,818 - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 00:49:27,522 - [32mINFO[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased
2025-07-15 00:49:27,523 - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 00:49:28,015 - INFO - Plutus Integrated Trading System initialized
2025-07-15 00:49:28,016 - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 00:49:28,021 - INFO - Plutus Integrated Trading System initialized
2025-07-15 00:49:28,021 - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 00:49:28,110 - INFO - 
============================================================
2025-07-15 00:49:28,113 - INFO - ============================================================
2025-07-15 01:21:25,929 - INFO - Memory monitoring started
2025-07-15 01:21:25,932 - INFO - Order cleanup thread started
2025-07-15 01:21:26,244 - INFO - Loaded 3 models from registry
2025-07-15 01:21:26,270 - INFO - Advanced Risk Calculator initialized
2025-07-15 01:21:28,046 - INFO - Advanced Correlation Analyzer initialized
2025-07-15 01:21:28,048 - INFO - Advanced Backtesting Engine initialized
2025-07-15 01:21:28,069 - INFO - Circuit breaker 'default' registered
2025-07-15 01:21:28,070 - INFO - Circuit breaker 'api_calls' registered
2025-07-15 01:21:28,070 - INFO - Retry handler 'default' registered
2025-07-15 01:21:28,070 - INFO - Retry handler 'network' registered
2025-07-15 01:21:54,217 - [32mINFO[0m - Device set to use cpu
2025-07-15 01:21:57,451 - [32mINFO[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 01:21:57,451 - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 01:22:01,618 - [32mINFO[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased
2025-07-15 01:22:01,619 - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 01:22:01,626 - [32mINFO[0m - Device set to use cpu
2025-07-15 01:22:03,097 - [32mINFO[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 01:22:03,098 - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 01:22:05,631 - [32mINFO[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased
2025-07-15 01:22:05,633 - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 01:22:05,725 - INFO - Plutus Integrated Trading System initialized
2025-07-15 01:22:05,726 - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 01:22:05,731 - INFO - Plutus Integrated Trading System initialized
2025-07-15 01:22:05,732 - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 01:22:05,806 - INFO - 
============================================================
2025-07-15 01:22:05,810 - INFO - ============================================================
2025-07-15 01:24:06,432 - __main__ - INFO - Starting comprehensive model performance testing...
2025-07-15 01:24:06,432 - __main__ - INFO - Loading test data...
2025-07-15 01:24:06,464 - __main__ - INFO - Test data loaded: 20000 records
2025-07-15 01:24:06,464 - __main__ - INFO - Loading trained models...
2025-07-15 01:24:06,499 - core.config.ConfigManager - INFO - ✅ Configuration loaded from config.yaml
2025-07-15 01:24:07,065 - core.memory_manager - INFO - Memory monitoring started
2025-07-15 01:24:07,068 - core.order_manager - INFO - Order cleanup thread started
2025-07-15 01:24:07,239 - core.model_versioning - INFO - Loaded 3 models from registry
2025-07-15 01:24:07,246 - core.advanced_risk_metrics - INFO - Advanced Risk Calculator initialized
2025-07-15 01:24:07,894 - core.correlation_analysis - INFO - Advanced Correlation Analyzer initialized
2025-07-15 01:24:07,896 - core.backtesting_framework - INFO - Advanced Backtesting Engine initialized
2025-07-15 01:24:07,899 - core.enhanced_error_handling.global - INFO - Circuit breaker 'default' registered
2025-07-15 01:24:07,899 - core.enhanced_error_handling.global - INFO - Circuit breaker 'api_calls' registered
2025-07-15 01:24:07,899 - core.enhanced_error_handling.global - INFO - Retry handler 'default' registered
2025-07-15 01:24:07,900 - core.enhanced_error_handling.global - INFO - Retry handler 'network' registered
2025-07-15 01:24:08,358 - core.utils - INFO - ✅ Performance monitoring started
2025-07-15 01:24:08,361 - core.utils - INFO - ✅ System monitoring setup completed
2025-07-15 01:24:08,361 - utils - [32mINFO[0m - ✅ Utils package initialized successfully
2025-07-15 01:24:22,422 - utils.sentiment_analyzer - [32mINFO[0m - پروکسی با موفقیت تنظیم شد.
2025-07-15 01:24:23,156 - ModelManager - INFO - 🤖 Initializing AI models...
2025-07-15 01:24:23,156 - utils.sentiment_analyzer - [32mINFO[0m - Device set to use cpu
2025-07-15 01:24:36,464 - utils.sentiment_analyzer - [31mERROR[0m - Failed to load model mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis: (ProtocolError('Connection aborted.', FileNotFoundError(2, 'No such file or directory')), '(Request ID: c03af089-9847-4fd2-bd54-e739f6d6e72e)')
2025-07-15 01:24:36,464 - utils.sentiment_analyzer - [31mERROR[0m - Error loading en model: (ProtocolError('Connection aborted.', FileNotFoundError(2, 'No such file or directory')), '(Request ID: c03af089-9847-4fd2-bd54-e739f6d6e72e)')
2025-07-15 01:24:36,475 - utils.sentiment_analyzer - [32mINFO[0m - Using VADER fallback for en
2025-07-15 01:24:36,476 - utils.sentiment_analyzer - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 01:24:43,840 - utils.sentiment_analyzer - [31mERROR[0m - Failed to load model HooshvareLab/bert-fa-base-uncased: (ProtocolError('Connection aborted.', FileNotFoundError(2, 'No such file or directory')), '(Request ID: c89dfa89-5c45-4e5d-93a1-0bc341e41dc4)')
2025-07-15 01:24:43,842 - utils.sentiment_analyzer - [31mERROR[0m - Error loading fa model: (ProtocolError('Connection aborted.', FileNotFoundError(2, 'No such file or directory')), '(Request ID: c89dfa89-5c45-4e5d-93a1-0bc341e41dc4)')
2025-07-15 01:24:43,887 - utils.sentiment_analyzer - [32mINFO[0m - Using VADER fallback for fa
2025-07-15 01:24:43,911 - utils.sentiment_analyzer - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 01:24:43,911 - ai_models.SentimentEnsemble - INFO - ✅ Sentiment ensemble initialized
2025-07-15 01:24:43,911 - utils.sentiment_analyzer - [32mINFO[0m - Device set to use cpu
2025-07-15 01:24:48,530 - utils.sentiment_analyzer - [31mERROR[0m - Failed to load model mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis: (ProtocolError('Connection aborted.', FileNotFoundError(2, 'No such file or directory')), '(Request ID: 5cbd39ab-074d-4a5f-ab74-615d97d1e00d)')
2025-07-15 01:24:48,530 - utils.sentiment_analyzer - [31mERROR[0m - Error loading en model: (ProtocolError('Connection aborted.', FileNotFoundError(2, 'No such file or directory')), '(Request ID: 5cbd39ab-074d-4a5f-ab74-615d97d1e00d)')
2025-07-15 01:24:48,533 - utils.sentiment_analyzer - [32mINFO[0m - Using VADER fallback for en
2025-07-15 01:24:48,533 - utils.sentiment_analyzer - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 01:24:55,773 - utils.sentiment_analyzer - [31mERROR[0m - Failed to load model HooshvareLab/bert-fa-base-uncased: (ProtocolError('Connection aborted.', FileNotFoundError(2, 'No such file or directory')), '(Request ID: 862c891d-5ce0-4319-9d57-14b1586dac3c)')
2025-07-15 01:24:55,773 - utils.sentiment_analyzer - [31mERROR[0m - Error loading fa model: (ProtocolError('Connection aborted.', FileNotFoundError(2, 'No such file or directory')), '(Request ID: 862c891d-5ce0-4319-9d57-14b1586dac3c)')
2025-07-15 01:24:55,773 - utils.sentiment_analyzer - [32mINFO[0m - Using VADER fallback for fa
2025-07-15 01:24:55,805 - utils.sentiment_analyzer - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 01:24:55,805 - ai_models.SentimentEnsemble - INFO - ✅ Sentiment ensemble initialized
2025-07-15 01:24:55,805 - ai_models.ModelRegistry - INFO - ✅ Model registered: sentiment_ensemble (sentiment_analysis)
2025-07-15 01:24:55,805 - examples.plutus_integration_final - INFO - Plutus Integrated Trading System initialized
2025-07-15 01:24:55,805 - utils.adaptive_plutus_system - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 01:24:55,805 - ai_models.TimeSeriesEnsemble - INFO - ✅ Time series ensemble initialized
2025-07-15 01:24:55,816 - examples.plutus_integration_final - INFO - Plutus Integrated Trading System initialized
2025-07-15 01:24:55,816 - utils.adaptive_plutus_system - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 01:24:55,817 - ai_models.TimeSeriesEnsemble - INFO - ✅ Time series ensemble initialized
2025-07-15 01:24:55,817 - ai_models.ModelRegistry - INFO - ✅ Model registered: timeseries_ensemble (time_series_forecasting)
2025-07-15 01:24:55,817 - ModelManager - INFO - ✅ All AI models initialized successfully
2025-07-15 01:24:55,817 - models - [32mINFO[0m - ✅ Models package initialized successfully
2025-07-15 01:24:55,818 - __main__ - ERROR - Failed to load models: cannot import name 'PPOAgent' from 'models.rl_models' (D:\project\models\rl_models.py)
2025-07-15 01:24:55,818 - __main__ - INFO - Testing sentiment analysis model...
2025-07-15 01:24:55,818 - __main__ - WARNING - Sentiment model not available
2025-07-15 01:24:55,818 - __main__ - INFO - Testing time series model...
2025-07-15 01:24:55,819 - __main__ - WARNING - Time series model not available
2025-07-15 01:24:55,819 - __main__ - INFO - Testing ensemble model...
2025-07-15 01:24:55,819 - __main__ - WARNING - Ensemble model not available
2025-07-15 01:24:55,819 - __main__ - INFO - Testing continual learning model...
2025-07-15 01:24:55,819 - __main__ - WARNING - Continual learning model not available
2025-07-15 01:24:55,820 - __main__ - INFO - Testing deep learning model...
2025-07-15 01:24:55,820 - __main__ - WARNING - Deep learning model not available
2025-07-15 01:24:55,820 - __main__ - INFO - Testing reinforcement learning models...
2025-07-15 01:24:55,820 - __main__ - INFO - Generating performance report...
2025-07-15 01:24:55,823 - __main__ - INFO - ==================================================
2025-07-15 01:24:55,823 - __main__ - INFO - MODEL PERFORMANCE SUMMARY
2025-07-15 01:24:55,823 - __main__ - INFO - ==================================================
2025-07-15 01:24:55,823 - __main__ - INFO - Total models tested: 0
2025-07-15 01:24:55,823 - __main__ - INFO - Total predictions: 0
2025-07-15 01:24:55,823 - __main__ - INFO - Total execution time: 0.00s
2025-07-15 01:24:55,825 - __main__ - INFO - Average time per prediction: 0.0000s
2025-07-15 01:24:55,825 - __main__ - INFO - 
Individual Model Results:
2025-07-15 01:24:55,825 - __main__ - INFO - ==================================================
2025-07-15 01:24:55,825 - __main__ - INFO - Detailed report saved to: model_performance_report.json
2025-07-15 01:24:55,826 - __main__ - INFO - All model performance tests completed
2025-07-15 01:37:45,868 - __main__ - INFO - Starting comprehensive model performance testing...
2025-07-15 01:37:45,868 - __main__ - INFO - Loading test data...
2025-07-15 01:37:45,904 - __main__ - INFO - Test data loaded: 20000 records
2025-07-15 01:37:45,905 - __main__ - INFO - Loading trained models...
2025-07-15 01:37:46,081 - core.config.ConfigManager - INFO - ✅ Configuration loaded from config.yaml
2025-07-15 01:37:47,136 - core.memory_manager - INFO - Memory monitoring started
2025-07-15 01:37:47,152 - core.order_manager - INFO - Order cleanup thread started
2025-07-15 01:37:47,508 - core.model_versioning - INFO - Loaded 3 models from registry
2025-07-15 01:37:47,565 - core.advanced_risk_metrics - INFO - Advanced Risk Calculator initialized
2025-07-15 01:37:49,293 - core.correlation_analysis - INFO - Advanced Correlation Analyzer initialized
2025-07-15 01:37:49,296 - core.backtesting_framework - INFO - Advanced Backtesting Engine initialized
2025-07-15 01:37:49,310 - core.enhanced_error_handling.global - INFO - Circuit breaker 'default' registered
2025-07-15 01:37:49,311 - core.enhanced_error_handling.global - INFO - Circuit breaker 'api_calls' registered
2025-07-15 01:37:49,311 - core.enhanced_error_handling.global - INFO - Retry handler 'default' registered
2025-07-15 01:37:49,311 - core.enhanced_error_handling.global - INFO - Retry handler 'network' registered
2025-07-15 01:37:49,890 - core.utils - INFO - ✅ Performance monitoring started
2025-07-15 01:37:49,894 - core.utils - INFO - ✅ System monitoring setup completed
2025-07-15 01:37:49,894 - utils - [32mINFO[0m - ✅ Utils package initialized successfully
2025-07-15 01:38:19,462 - utils.sentiment_analyzer - [32mINFO[0m - پروکسی با موفقیت تنظیم شد.
2025-07-15 01:38:21,450 - ModelManager - INFO - 🤖 Initializing AI models...
2025-07-15 01:38:21,451 - utils.sentiment_analyzer - [32mINFO[0m - Device set to use cpu
2025-07-15 01:38:24,654 - utils.sentiment_analyzer - [32mINFO[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 01:38:24,667 - utils.sentiment_analyzer - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 01:38:28,767 - utils.sentiment_analyzer - [32mINFO[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased
2025-07-15 01:38:28,767 - utils.sentiment_analyzer - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 01:38:28,768 - ai_models.SentimentEnsemble - INFO - ✅ Sentiment ensemble initialized
2025-07-15 01:38:28,768 - utils.sentiment_analyzer - [32mINFO[0m - Device set to use cpu
2025-07-15 01:38:30,165 - utils.sentiment_analyzer - [32mINFO[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 01:38:30,199 - utils.sentiment_analyzer - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 01:38:32,880 - utils.sentiment_analyzer - [32mINFO[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased
2025-07-15 01:38:32,880 - utils.sentiment_analyzer - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 01:38:32,915 - ai_models.SentimentEnsemble - INFO - ✅ Sentiment ensemble initialized
2025-07-15 01:38:32,916 - ai_models.ModelRegistry - INFO - ✅ Model registered: sentiment_ensemble (sentiment_analysis)
2025-07-15 01:38:32,949 - examples.plutus_integration_final - INFO - Plutus Integrated Trading System initialized
2025-07-15 01:38:32,949 - utils.adaptive_plutus_system - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 01:38:32,949 - ai_models.TimeSeriesEnsemble - INFO - ✅ Time series ensemble initialized
2025-07-15 01:38:32,950 - examples.plutus_integration_final - INFO - Plutus Integrated Trading System initialized
2025-07-15 01:38:32,950 - utils.adaptive_plutus_system - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 01:38:32,951 - ai_models.TimeSeriesEnsemble - INFO - ✅ Time series ensemble initialized
2025-07-15 01:38:32,951 - ai_models.ModelRegistry - INFO - ✅ Model registered: timeseries_ensemble (time_series_forecasting)
2025-07-15 01:38:32,951 - ModelManager - INFO - ✅ All AI models initialized successfully
2025-07-15 01:38:32,952 - models - [32mINFO[0m - ✅ Models package initialized successfully
2025-07-15 01:38:32,953 - __main__ - ERROR - Failed to load models: cannot import name 'PPOAgent' from 'models.rl_models' (D:\project\models\rl_models.py)
2025-07-15 01:38:32,953 - __main__ - INFO - Testing sentiment analysis model...
2025-07-15 01:38:32,953 - __main__ - WARNING - Sentiment model not available
2025-07-15 01:38:32,953 - __main__ - INFO - Testing time series model...
2025-07-15 01:38:32,954 - __main__ - WARNING - Time series model not available
2025-07-15 01:38:32,954 - __main__ - INFO - Testing ensemble model...
2025-07-15 01:38:32,954 - __main__ - WARNING - Ensemble model not available
2025-07-15 01:38:32,954 - __main__ - INFO - Testing continual learning model...
2025-07-15 01:38:32,955 - __main__ - WARNING - Continual learning model not available
2025-07-15 01:38:32,955 - __main__ - INFO - Testing deep learning model...
2025-07-15 01:38:32,955 - __main__ - WARNING - Deep learning model not available
2025-07-15 01:38:32,955 - __main__ - INFO - Testing reinforcement learning models...
2025-07-15 01:38:32,955 - __main__ - INFO - Generating performance report...
2025-07-15 01:38:32,956 - __main__ - INFO - ==================================================
2025-07-15 01:38:32,956 - __main__ - INFO - MODEL PERFORMANCE SUMMARY
2025-07-15 01:38:32,957 - __main__ - INFO - ==================================================
2025-07-15 01:38:32,957 - __main__ - INFO - Total models tested: 0
2025-07-15 01:38:32,957 - __main__ - INFO - Total predictions: 0
2025-07-15 01:38:32,957 - __main__ - INFO - Total execution time: 0.00s
2025-07-15 01:38:32,957 - __main__ - INFO - Average time per prediction: 0.0000s
2025-07-15 01:38:32,958 - __main__ - INFO - 
Individual Model Results:
2025-07-15 01:38:32,958 - __main__ - INFO - ==================================================
2025-07-15 01:38:32,958 - __main__ - INFO - Detailed report saved to: model_performance_report.json
2025-07-15 01:38:32,958 - __main__ - INFO - All model performance tests completed
2025-07-15 20:22:42,165 - [32mINFO[0m - Device set to use cpu
2025-07-15 20:22:46,414 - [32mINFO[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 20:22:46,416 - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 20:22:50,845 - [32mINFO[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased
2025-07-15 20:22:50,867 - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 20:22:50,872 - [32mINFO[0m - Device set to use cpu
2025-07-15 20:22:53,084 - [32mINFO[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 20:22:53,085 - [32mINFO[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
2025-07-15 20:22:55,705 - [32mINFO[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased
2025-07-15 20:22:55,705 - [32mINFO[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased
2025-07-15 20:22:55,854 - INFO - Plutus Integrated Trading System initialized
2025-07-15 20:22:55,855 - [32mINFO[0m - Adaptive Plutus System initialized
2025-07-15 20:22:55,861 - INFO - Plutus Integrated Trading System initialized
2025-07-15 20:22:55,861 - [32mINFO[0m - Adaptive Plutus System initialized
