import numpy as np
import pandas as pd
from typing import Dict, List, Any, Callable, Tuple, Optional, Union
import matplotlib.pyplot as plt
from copy import deepcopy
import gym
from stable_baselines3.common.vec_env import DummyVecEnv
from models.rl_models import RLModelFactory
from evaluation.metrics import calculate_metrics
from portfolio.portfolio_manager import PortfolioManager


class ScenarioBacktesting:
    """
    ماژول scenario-based backtesting برای تست استراتژی‌ها در سناریوهای مختلف بازار
    """
    
    def __init__(self, base_env: gym.Env, model=None):
        """
        راه‌اندازی ماژول بک‌تست سناریو-محور
        
        Args:
            base_env: محیط پایه برای بک‌تست
            model: مدل RL آموزش‌دیده (اختیاری)
        """
        self.base_env = base_env
        self.model = model
        self.scenario_results = {}
        self.rl_factory = RLModelFactory()
        
    def _create_vec_env(self, env):
        """ایجاد VecEnv برای سازگاری با stable-baselines3"""
        return DummyVecEnv([lambda: env])
        
    def historical_crisis_scenario(self, 
                                  crisis_data: pd.DataFrame, 
                                  name: str = "historical_crisis",
                                  description: str = "",
                                  severity: float = 1.0,
                                  duration_factor: float = 1.0) -> Dict[str, Any]:
        """
        شبیه‌سازی سناریوی بحران تاریخی با قابلیت تنظیم شدت و مدت زمان
        
        Args:
            crisis_data: داده‌های بحران تاریخی
            name: نام سناریو
            description: توضیحات سناریو
            severity: ضریب شدت بحران (1.0 = بدون تغییر)
            duration_factor: ضریب مدت زمان بحران (1.0 = بدون تغییر)
            
        Returns:
            نتایج بک‌تست در سناریوی بحران
        """
        if crisis_data is None or crisis_data.empty:
            raise ValueError("داده‌های بحران تاریخی نمی‌تواند خالی باشد")
        
        # تنظیم مدت زمان بحران
        if duration_factor != 1.0:
            original_len = len(crisis_data)
            new_len = int(original_len * duration_factor)
            if duration_factor > 1.0:
                # تکرار نمونه‌ها برای افزایش مدت زمان
                crisis_data = crisis_data.iloc[np.linspace(0, original_len-1, new_len, dtype=int)]
            else:
                # انتخاب نمونه‌ها برای کاهش مدت زمان
                crisis_data = crisis_data.iloc[:new_len]
        
        # تنظیم شدت بحران
        if severity != 1.0:
            # تغییر شدت نوسانات قیمت
            mean_price = crisis_data['close'].mean()
            crisis_data.loc[:, 'close'] = mean_price + (crisis_data['close'] - mean_price) * severity
            crisis_data.loc[:, 'high'] = mean_price + (crisis_data['high'] - mean_price) * severity
            crisis_data.loc[:, 'low'] = mean_price + (crisis_data['low'] - mean_price) * severity
            
        # ایجاد محیط بک‌تست با داده‌های بحران
        crisis_env = deepcopy(self.base_env)
        crisis_env.df = crisis_data
        
        # اجرای بک‌تست
        return self._run_backtest(crisis_env, name, description)
    
    def parameter_sensitivity_analysis(self, 
                                     param_name: str, 
                                     param_values: List[Any],
                                     market_type: str = "all") -> Dict[str, Dict[str, Any]]:
        """
        تحلیل حساسیت پارامترهای استراتژی در سناریوهای مختلف بازار
        
        Args:
            param_name: نام پارامتر مورد بررسی
            param_values: لیست مقادیر مختلف پارامتر
            market_type: نوع بازار ("bullish", "bearish", "volatile", "all")
            
        Returns:
            نتایج بک‌تست برای هر مقدار پارامتر در هر نوع بازار
        """
        results = {}
        
        # تعیین انواع بازار برای تست
        market_types = ["bullish", "bearish", "volatile"] if market_type == "all" else [market_type]
        
        for value in param_values:
            value_results = {}
            
            for mkt_type in market_types:
                # ایجاد محیط متناسب با نوع بازار
                scenario_env = self._create_market_scenario(mkt_type)
                
                # تنظیم پارامتر در محیط یا مدل
                if hasattr(scenario_env, param_name):
                    setattr(scenario_env, param_name, value)
                elif self.model and hasattr(self.model, param_name):
                    setattr(self.model, param_name, value)
                elif self.model and hasattr(self.model.policy, param_name):
                    setattr(self.model.policy, param_name, value)
                
                # اجرای بک‌تست
                scenario_name = f"{mkt_type}_{param_name}_{value}"
                scenario_desc = f"تحلیل حساسیت {param_name}={value} در بازار {mkt_type}"
                result = self._run_backtest(scenario_env, scenario_name, scenario_desc)
                value_results[mkt_type] = result
            
            results[str(value)] = value_results
            
        return results
    
    def _create_market_scenario(self, market_type: str) -> gym.Env:
        """
        ایجاد سناریوی بازار بر اساس نوع بازار
        
        Args:
            market_type: نوع بازار ("bullish", "bearish", "volatile")
            
        Returns:
            محیط شبیه‌سازی شده برای نوع بازار مشخص شده
        """
        scenario_env = deepcopy(self.base_env)
        df = scenario_env.df.copy()
        
        if market_type == "bullish":
            # ایجاد روند صعودی با افزایش تدریجی قیمت
            trend_factor = np.linspace(1.0, 1.2, len(df))
            df['close'] = df['close'] * trend_factor
            df['high'] = df['high'] * trend_factor
            df['low'] = df['low'] * trend_factor
            df['open'] = df['open'] * trend_factor
            
        elif market_type == "bearish":
            # ایجاد روند نزولی با کاهش تدریجی قیمت
            trend_factor = np.linspace(1.0, 0.8, len(df))
            df['close'] = df['close'] * trend_factor
            df['high'] = df['high'] * trend_factor
            df['low'] = df['low'] * trend_factor
            df['open'] = df['open'] * trend_factor
            
        elif market_type == "volatile":
            # ایجاد بازار پر نوسان با افزایش فاصله بین high و low
            volatility_factor = 1.5
            mean_prices = df['close'].values
            df['high'] = df['high'] + (df['high'] - mean_prices) * (volatility_factor - 1)
            df['low'] = df['low'] - (mean_prices - df['low']) * (volatility_factor - 1)
            
            # اضافه کردن نوسانات تصادفی
            random_volatility = np.random.normal(0, 0.01, len(df))
            df['close'] = df['close'] * (1 + random_volatility)
            
        scenario_env.df = df
        return scenario_env
    
    def execution_delay_slippage_scenario(self, 
                                        delay_ms: List[int] = [0, 100, 500, 1000],
                                        slippage_bps: List[int] = [0, 1, 5, 10],
                                        market_conditions: List[str] = ["normal", "volatile"]) -> Dict[str, Dict[str, Any]]:
        """
        شبیه‌سازی تأخیرهای اجرای سفارش و لغزش قیمت متغیر بر اساس شرایط بازار
        
        Args:
            delay_ms: لیست تأخیرهای اجرا (میلی‌ثانیه)
            slippage_bps: لیست لغزش قیمت (basis points)
            market_conditions: شرایط بازار برای شبیه‌سازی
            
        Returns:
            نتایج بک‌تست برای هر ترکیب تأخیر و لغزش
        """
        results = {}
        
        for condition in market_conditions:
            condition_results = {}
            
            # ایجاد محیط متناسب با شرایط بازار
            if condition == "volatile":
                scenario_env = self._create_market_scenario("volatile")
            else:
                scenario_env = deepcopy(self.base_env)
            
            for delay in delay_ms:
                for slippage in slippage_bps:
                    # تنظیم تأخیر و لغزش در محیط
                    scenario_env.execution_delay_ms = delay
                    scenario_env.slippage_bps = slippage
                    
                    # محاسبه لغزش قیمت بر اساس شرایط بازار
                    if condition == "volatile":
                        # لغزش بیشتر در بازار پر نوسان
                        effective_slippage = slippage * 2
                    else:
                        effective_slippage = slippage
                    
                    # اعمال لغزش قیمت در داده‌ها
                    df = scenario_env.df.copy()
                    slippage_factor = effective_slippage / 10000  # تبدیل bps به درصد
                    
                    # شبیه‌سازی تأخیر با استفاده از داده‌های قدیمی‌تر
                    if delay > 0:
                        # تعداد فریم‌های تأخیر بر اساس میلی‌ثانیه و تایم‌فریم
                        timeframe_ms = {"1m": 60000, "5m": 300000, "15m": 900000, "1h": 3600000}
                        tf = getattr(scenario_env, "timeframe", "1m")
                        frames_delay = max(1, int(delay / timeframe_ms.get(tf, 60000)))
                        
                        # شیفت داده‌ها برای شبیه‌سازی تأخیر
                        df_delayed = df.shift(frames_delay).bfill()
                        
                        # ترکیب داده‌های اصلی و تأخیردار
                        scenario_env.df_original = df.copy()  # داده‌های اصلی برای مقایسه
                        scenario_env.df_execution = df_delayed  # داده‌های اجرا با تأخیر
                    
                    # اجرای بک‌تست
                    scenario_name = f"{condition}_delay{delay}_slip{slippage}"
                    scenario_desc = f"تأخیر {delay}ms و لغزش {slippage}bps در بازار {condition}"
                    result = self._run_backtest(scenario_env, scenario_name, scenario_desc)
                    
                    key = f"delay{delay}_slip{slippage}"
                    condition_results[key] = result
            
            results[condition] = condition_results
            
        return results
    
    def regime_switching_scenario(self, 
                                num_regimes: int = 3, 
                                regime_lengths: List[int] = None,
                                transition_matrix: np.ndarray = None) -> Dict[str, Any]:
        """
        شبیه‌سازی تغییرات رژیم بازار با مدل‌های مارکوف
        
        Args:
            num_regimes: تعداد رژیم‌های بازار
            regime_lengths: طول هر رژیم (اختیاری)
            transition_matrix: ماتریس انتقال مارکوف (اختیاری)
            
        Returns:
            نتایج بک‌تست در سناریوی تغییر رژیم
        """
        scenario_env = deepcopy(self.base_env)
        df = scenario_env.df.copy()
        total_len = len(df)
        
        # تعیین طول رژیم‌ها
        if regime_lengths is None:
            # تقسیم مساوی داده‌ها بین رژیم‌ها
            base_length = total_len // num_regimes
            regime_lengths = [base_length] * num_regimes
            # اضافه کردن باقیمانده به آخرین رژیم
            regime_lengths[-1] += total_len % num_regimes
        
        # تعیین ماتریس انتقال
        if transition_matrix is None:
            # ماتریس انتقال پیش‌فرض با احتمال بالای ماندن در همان رژیم
            transition_matrix = np.eye(num_regimes) * 0.8
            for i in range(num_regimes):
                for j in range(num_regimes):
                    if i != j:
                        transition_matrix[i, j] = 0.2 / (num_regimes - 1)
        
        # تولید توالی رژیم‌ها با استفاده از زنجیره مارکوف
        regimes = []
        current_regime = 0
        
        for _ in range(total_len):
            regimes.append(current_regime)
            # انتخاب رژیم بعدی بر اساس ماتریس انتقال
            current_regime = np.random.choice(num_regimes, p=transition_matrix[current_regime])
        
        # تعریف ویژگی‌های هر رژیم
        regime_features = [
            {"trend": 1.0005, "volatility": 0.8},    # رژیم 0: روند صعودی با نوسان کم
            {"trend": 0.9995, "volatility": 1.2},    # رژیم 1: روند نزولی با نوسان متوسط
            {"trend": 1.0000, "volatility": 2.0}     # رژیم 2: بدون روند با نوسان زیاد
        ]
        
        # اعمال ویژگی‌های رژیم به داده‌ها
        price_multiplier = 1.0
        regime_data = []
        
        for i, regime in enumerate(regimes):
            features = regime_features[regime]
            
            # اعمال روند
            price_multiplier *= features["trend"]
            
            # کپی داده‌های اصلی برای این نقطه زمانی
            row = df.iloc[i % len(df)].copy()
            
            # اعمال ضریب قیمت و نوسان
            row["open"] *= price_multiplier
            row["high"] *= price_multiplier * (1 + 0.0005 * features["volatility"])
            row["low"] *= price_multiplier * (1 - 0.0005 * features["volatility"])
            row["close"] *= price_multiplier
            
            regime_data.append(row)
        
        # ایجاد دیتافریم جدید با داده‌های رژیم
        regime_df = pd.DataFrame(regime_data)
        
        # اضافه کردن ستون رژیم برای تحلیل بعدی
        regime_df["regime"] = regimes
        
        # تنظیم محیط با داده‌های رژیم
        scenario_env.df = regime_df
        
        # اجرای بک‌تست
        scenario_name = f"regime_switching_{num_regimes}"
        scenario_desc = f"سناریوی تغییر رژیم با {num_regimes} رژیم مختلف"
        return self._run_backtest(scenario_env, scenario_name, scenario_desc)
    
    def _run_backtest(self, scenario_env: gym.Env, name: str, description: str) -> Dict[str, Any]:
        """
        اجرای بک‌تست روی یک سناریوی خاص
        
        Args:
            scenario_env: محیط شبیه‌سازی برای سناریو
            name: نام سناریو
            description: توضیحات سناریو
            
        Returns:
            نتایج بک‌تست
        """
        if self.model is None:
            raise ValueError("مدل برای بک‌تست تنظیم نشده است")
        
        # تبدیل به VecEnv برای سازگاری با stable-baselines3
        vec_env = self._create_vec_env(scenario_env)
        
        # اجرای بک‌تست
        metrics = calculate_metrics(vec_env, self.model)
        
        # ذخیره نتایج
        result = {
            "name": name,
            "description": description,
            "metrics": metrics,
            "env_config": {
                attr: getattr(scenario_env, attr)
                for attr in dir(scenario_env)
                if not attr.startswith("_") and not callable(getattr(scenario_env, attr))
            }
        }
        
        self.scenario_results[name] = result
        return result
    
    def compare_scenarios(self, scenario_names: List[str] = None) -> pd.DataFrame:
        """
        مقایسه نتایج سناریوهای مختلف
        
        Args:
            scenario_names: لیست نام سناریوها برای مقایسه (اگر None باشد، همه سناریوها مقایسه می‌شوند)
            
        Returns:
            دیتافریم مقایسه‌ای از معیارهای کلیدی
        """
        if not self.scenario_results:
            raise ValueError("هیچ سناریویی اجرا نشده است")
        
        if scenario_names is None:
            scenario_names = list(self.scenario_results.keys())
        
        comparison_data = []
        
        for name in scenario_names:
            if name not in self.scenario_results:
                continue
                
            result = self.scenario_results[name]
            metrics = result["metrics"]
            
            row = {
                "scenario": name,
                "description": result["description"],
                "win_rate": metrics.get("win_rate", 0),
                "sharpe_ratio": metrics.get("sharpe_ratio", 0),
                "max_drawdown": metrics.get("max_drawdown", 0),
                "daily_drawdown": metrics.get("daily_drawdown", 0),
                "final_balance": metrics.get("equity_curve", [0])[-1]
            }
            
            comparison_data.append(row)
        
        return pd.DataFrame(comparison_data)
    
    def visualize_scenario_results(self, scenario_names: List[str] = None, metrics: List[str] = None):
        """
        نمایش گرافیکی نتایج سناریوهای مختلف
        
        Args:
            scenario_names: لیست نام سناریوها برای نمایش (اگر None باشد، همه سناریوها نمایش داده می‌شوند)
            metrics: لیست معیارهای مورد نظر برای نمایش
        """
        if not self.scenario_results:
            raise ValueError("هیچ سناریویی اجرا نشده است")
        
        if scenario_names is None:
            scenario_names = list(self.scenario_results.keys())
            
        if metrics is None:
            metrics = ["equity_curve"]
        
        # تنظیم نمودار
        fig, axes = plt.subplots(len(metrics), 1, figsize=(12, 4 * len(metrics)), sharex=True)
        if len(metrics) == 1:
            axes = [axes]
        
        for i, metric in enumerate(metrics):
            ax = axes[i]
            
            for name in scenario_names:
                if name not in self.scenario_results:
                    continue
                    
                result = self.scenario_results[name]
                
                if metric == "equity_curve":
                    equity_curve = result["metrics"].get("equity_curve", [])
                    ax.plot(equity_curve, label=name)
                    ax.set_ylabel("Balance")
                    ax.set_title("Equity Curve")
                else:
                    # برای سایر معیارها، نمودار میله‌ای رسم می‌کنیم
                    pass
            
            ax.grid(True)
            ax.legend()
        
        plt.tight_layout()
        plt.show()
        
        # نمودار مقایسه‌ای معیارهای کلیدی
        comparison_df = self.compare_scenarios(scenario_names)
        
        metrics_to_plot = ["win_rate", "sharpe_ratio", "max_drawdown", "final_balance"]
        metrics_df = comparison_df[["scenario"] + [m for m in metrics_to_plot if m in comparison_df.columns]]
        
        # تنظیم نمودار میله‌ای
        fig, axes = plt.subplots(len(metrics_to_plot), 1, figsize=(10, 3 * len(metrics_to_plot)))
        if len(metrics_to_plot) == 1:
            axes = [axes]
            
        for i, metric in enumerate([m for m in metrics_to_plot if m in comparison_df.columns]):
            ax = axes[i]
            metrics_df.plot(x="scenario", y=metric, kind="bar", ax=ax)
            ax.set_title(f"{metric} Comparison")
            ax.grid(True)
        
        plt.tight_layout()
        plt.show()

    def monte_carlo_stress_scenarios(self, n_scenarios: int = 10, volatility_scale: float = 2.0) -> Dict[str, Any]:
        """
        تولید خودکار سناریوهای استرس با مدل‌های مونت کارلو (افزایش تصادفی نوسانات)
        Args:
            n_scenarios: تعداد سناریوهای تصادفی
            volatility_scale: ضریب افزایش نوسان
        Returns:
            نتایج بک‌تست برای هر سناریوی تصادفی
        """
        results = {}
        base_df = self.base_env.df.copy()
        for i in range(n_scenarios):
            df = base_df.copy()
            # افزایش نوسان تصادفی
            random_vol = np.random.normal(0, volatility_scale * df['close'].std() / 100, len(df))
            df['close'] = df['close'] * (1 + random_vol)
            df['high'] = df['high'] * (1 + np.abs(random_vol))
            df['low'] = df['low'] * (1 - np.abs(random_vol))
            scenario_env = deepcopy(self.base_env)
            scenario_env.df = df
            name = f"monte_carlo_stress_{i+1}"
            desc = f"سناریوی استرس مونت کارلو شماره {i+1} با ضریب نوسان {volatility_scale}"
            results[name] = self._run_backtest(scenario_env, name, desc)
        return results

    def multi_market_backtest(self, market_dfs: Dict[str, pd.DataFrame], correlation_matrix: np.ndarray = None) -> Dict[str, Any]:
        """
        بک‌تست چندبازاری با همبستگی‌های پویا
        Args:
            market_dfs: دیکشنری {نام بازار: دیتافریم بازار}
            correlation_matrix: ماتریس همبستگی (اختیاری)
        Returns:
            نتایج بک‌تست برای هر بازار و ترکیب همبسته
        """
        results = {}
        market_names = list(market_dfs.keys())
        for name, df in market_dfs.items():
            scenario_env = deepcopy(self.base_env)
            scenario_env.df = df
            scenario_env.symbol = name
            desc = f"بک‌تست بازار {name}"
            results[name] = self._run_backtest(scenario_env, name, desc)
        # اگر ماتریس همبستگی داده شده باشد، ترکیب بازارها را نیز تست کن
        if correlation_matrix is not None and len(market_names) > 1:
            # ترکیب خطی بازارها بر اساس همبستگی
            combined_df = sum([market_dfs[n] * correlation_matrix[i, 0] for i, n in enumerate(market_names)]) / np.sum(correlation_matrix[:, 0])
            scenario_env = deepcopy(self.base_env)
            scenario_env.df = combined_df
            scenario_env.symbol = "multi_market_combined"
            desc = "بک‌تست ترکیبی چندبازاری با همبستگی پویا"
            results["multi_market_combined"] = self._run_backtest(scenario_env, "multi_market_combined", desc)
        return results

    def news_event_impact_scenario(self, news_events: List[int], impact_scale: float = 0.05) -> Dict[str, Any]:
        """
        شبیه‌سازی رویدادهای اخبار مهم و تأثیر آن‌ها بر بازار
        Args:
            news_events: لیست اندیس‌های زمانی که رویداد خبری رخ داده است
            impact_scale: ضریب تأثیر خبر بر قیمت (مثبت یا منفی)
        Returns:
            نتایج بک‌تست با رویدادهای خبری
        """
        df = self.base_env.df.copy()
        for idx in news_events:
            if 0 <= idx < len(df):
                # اعمال شوک مثبت یا منفی تصادفی
                shock = np.random.choice([-1, 1]) * impact_scale * df['close'].iloc[idx]
                df.loc[df.index[idx], 'close'] += shock
                df.loc[df.index[idx], 'high'] += abs(shock)
                df.loc[df.index[idx], 'low'] -= abs(shock)
        scenario_env = deepcopy(self.base_env)
        scenario_env.df = df
        name = "news_event_impact"
        desc = f"شبیه‌سازی تأثیر {len(news_events)} رویداد خبری بر بازار با ضریب {impact_scale}"
        return {name: self._run_backtest(scenario_env, name, desc)}

    def worst_case_robust_optimization(self, n_scenarios: int = 10, perturbation_scale: float = 0.1) -> Dict[str, Any]:
        """
        تحلیل سناریوهای بدترین حالت با روش‌های بهینه‌سازی مقاوم (perturbation)
        Args:
            n_scenarios: تعداد سناریوهای بدترین حالت
            perturbation_scale: ضریب تغییرات منفی (کاهش قیمت)
        Returns:
            نتایج بک‌تست برای هر سناریوی بدترین حالت
        """
        results = {}
        base_df = self.base_env.df.copy()
        for i in range(n_scenarios):
            df = base_df.copy()
            # کاهش تصادفی قیمت‌ها (perturbation)
            negative_shock = np.abs(np.random.normal(0, perturbation_scale, len(df)))
            df['close'] = df['close'] * (1 - negative_shock)
            df['high'] = df['high'] * (1 - negative_shock / 2)
            df['low'] = df['low'] * (1 - negative_shock)
            scenario_env = deepcopy(self.base_env)
            scenario_env.df = df
            name = f"worst_case_{i+1}"
            desc = f"سناریوی بدترین حالت شماره {i+1} با ضریب perturbation {perturbation_scale}"
            results[name] = self._run_backtest(scenario_env, name, desc)
        return results

    def liquidity_shock_scenario(self, shock_indices: List[int], shock_scale: float = 0.15) -> Dict[str, Any]:
        """
        شبیه‌سازی شوک‌های نقدینگی و تغییرات ناگهانی در عمق بازار
        Args:
            shock_indices: لیست اندیس‌های زمانی که شوک نقدینگی رخ می‌دهد
            shock_scale: ضریب شدت شوک
        Returns:
            نتایج بک‌تست با شوک‌های نقدینگی
        """
        df = self.base_env.df.copy()
        for idx in shock_indices:
            if 0 <= idx < len(df):
                # کاهش ناگهانی حجم و افزایش نوسان
                df.loc[df.index[idx], 'volume'] = df['volume'].iloc[idx] * (1 - shock_scale)
                price_shock = np.random.normal(0, shock_scale * df['close'].iloc[idx])
                df.loc[df.index[idx], 'close'] += price_shock
                df.loc[df.index[idx], 'high'] += abs(price_shock)
                df.loc[df.index[idx], 'low'] -= abs(price_shock)
        scenario_env = deepcopy(self.base_env)
        scenario_env.df = df
        name = "liquidity_shock"
        desc = f"شبیه‌سازی {len(shock_indices)} شوک نقدینگی با ضریب {shock_scale}"
        return {name: self._run_backtest(scenario_env, name, desc)}

    def multi_agent_behavior_backtest(self, agent_behaviors: List[Dict[str, Any]], agent_weights: List[float] = None) -> Dict[str, Any]:
        """
        بک‌تست چندعاملی با شبیه‌سازی رفتار سایر معامله‌گران
        Args:
            agent_behaviors: لیست دیکشنری رفتارهای عامل‌ها (مثلاً {'trend': 1.01, 'volatility': 1.2})
            agent_weights: وزن هر عامل در ترکیب (اختیاری)
        Returns:
            نتایج بک‌تست برای ترکیب رفتار چندعامل
        """
        df = self.base_env.df.copy()
        n_agents = len(agent_behaviors)
        if agent_weights is None:
            agent_weights = [1.0 / n_agents] * n_agents
        combined_df = pd.DataFrame(0, index=df.index, columns=df.columns)
        for i, behavior in enumerate(agent_behaviors):
            agent_df = df.copy()
            trend = behavior.get('trend', 1.0)
            volatility = behavior.get('volatility', 1.0)
            # اعمال روند و نوسان برای هر عامل
            trend_factor = np.linspace(1.0, trend, len(agent_df))
            agent_df['close'] = agent_df['close'] * trend_factor
            agent_df['high'] = agent_df['high'] * trend_factor * (1 + 0.01 * volatility)
            agent_df['low'] = agent_df['low'] * trend_factor * (1 - 0.01 * volatility)
            combined_df += agent_weights[i] * agent_df
        scenario_env = deepcopy(self.base_env)
        scenario_env.df = combined_df
        name = "multi_agent_behavior"
        desc = f"بک‌تست ترکیبی رفتار {n_agents} عامل با وزن‌های {agent_weights}"
        return {name: self._run_backtest(scenario_env, name, desc)}

    def market_manipulation_scenario(self, manipulation_indices: List[int], manipulation_scale: float = 0.2) -> Dict[str, Any]:
        """
        شبیه‌سازی دستکاری بازار و حرکات قیمتی غیرطبیعی (مانند spoofing/layering)
        Args:
            manipulation_indices: لیست اندیس‌های زمانی که دستکاری رخ می‌دهد
            manipulation_scale: ضریب شدت دستکاری
        Returns:
            نتایج بک‌تست با دستکاری بازار
        """
        df = self.base_env.df.copy()
        for idx in manipulation_indices:
            if 0 <= idx < len(df):
                # اعمال جهش قیمتی غیرطبیعی
                jump = np.random.choice([-1, 1]) * manipulation_scale * df['close'].iloc[idx]
                df.loc[df.index[idx], 'close'] += jump
                df.loc[df.index[idx], 'high'] += abs(jump) * 1.5
                df.loc[df.index[idx], 'low'] -= abs(jump) * 1.5
        scenario_env = deepcopy(self.base_env)
        scenario_env.df = df
        name = "market_manipulation"
        desc = f"شبیه‌سازی {len(manipulation_indices)} دستکاری بازار با ضریب {manipulation_scale}"
        return {name: self._run_backtest(scenario_env, name, desc)}

    def whale_behavior_scenario(self, whale_indices: List[int], whale_impact: float = 0.25) -> Dict[str, Any]:
        """
        شبیه‌سازی رفتار معامله‌گران بزرگ (نهنگ‌ها)
        Args:
            whale_indices: لیست اندیس‌های زمانی که نهنگ وارد بازار می‌شود
            whale_impact: ضریب تأثیر نهنگ بر قیمت و حجم
        Returns:
            نتایج بک‌تست با رفتار نهنگ
        """
        df = self.base_env.df.copy()
        for idx in whale_indices:
            if 0 <= idx < len(df):
                # افزایش ناگهانی حجم و حرکت شدید قیمت
                df.loc[df.index[idx], 'volume'] = df['volume'].iloc[idx] * (1 + whale_impact * 5)
                price_move = np.random.choice([-1, 1]) * whale_impact * df['close'].iloc[idx]
                df.loc[df.index[idx], 'close'] += price_move
                df.loc[df.index[idx], 'high'] += abs(price_move) * 2
                df.loc[df.index[idx], 'low'] -= abs(price_move) * 2
        scenario_env = deepcopy(self.base_env)
        scenario_env.df = df
        name = "whale_behavior"
        desc = f"شبیه‌سازی {len(whale_indices)} رفتار نهنگ با ضریب {whale_impact}"
        return {name: self._run_backtest(scenario_env, name, desc)}

    def run_scenario_with_xai(self, scenario_func, *args, xai_methods=None, xai_kwargs=None, **kwargs):
        """
        اجرای سناریو و تحلیل explainable AI به صورت یکپارچه
        Args:
            scenario_func: تابع سناریو (مثلاً self.historical_crisis_scenario)
            *args, **kwargs: آرگومان‌های سناریو
            xai_methods: لیست متدهای XAI برای اجرا (مثلاً ['feature_importance', 'counterfactual_analysis'])
            xai_kwargs: دیکشنری آرگومان‌های هر متد XAI (اختیاری)
        Returns:
            dict شامل نتایج سناریو و تحلیل XAI
        """
        # اجرای سناریو و دریافت نتایج بک‌تست
        scenario_result = scenario_func(*args, **kwargs)
        # استخراج نام سناریو و محیط
        if isinstance(scenario_result, dict):
            # فرض: فقط یک سناریو اجرا شده (برای سناریوهای منفرد)
            scenario_name = list(scenario_result.keys())[0]
            scenario_metrics = scenario_result[scenario_name]['metrics']
            scenario_env = None
            if 'env_config' in scenario_result[scenario_name]:
                scenario_env = scenario_result[scenario_name]['env_config']
        else:
            raise ValueError('خروجی سناریو باید دیکشنری باشد')
        # اجرای تحلیل XAI
        xai_results = {}
        if self.model is not None and hasattr(self, 'base_env'):
            try:
                from evaluation.explainable_ai import ExplainableAI
                xai = ExplainableAI(self.model, self.base_env)
                if xai_methods is None:
                    xai_methods = ['feature_importance']
                if xai_kwargs is None:
                    xai_kwargs = {}
                for method in xai_methods:
                    func = getattr(xai, method, None)
                    if func is not None:
                        args_ = xai_kwargs.get(method, [])
                        kwargs_ = xai_kwargs.get(f'{method}_kwargs', {})
                        xai_results[method] = func(*args_, **kwargs_)
            except Exception as e:
                xai_results['error'] = str(e)
        return {
            'scenario_result': scenario_result,
            'xai_results': xai_results
        } 