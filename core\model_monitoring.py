#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ Model Monitoring System
سیستم نظارت بر مدل‌ها با Performance Tracking و Drift Detection
"""

import os
import sys
import time
import json
import numpy as np
import threading
from typing import Dict, List, Optional, Any, Union, Callable, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from enum import Enum
from collections import deque, defaultdict
from statistics import mean, stdev
import logging
from contextlib import contextmanager

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.exceptions import TradingSystemError, ValidationError, ModelLoadError

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class AlertSeverity(Enum):
    """شدت هشدار"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class DriftType(Enum):
    """نوع انحراف"""
    DATA_DRIFT = "data_drift"
    CONCEPT_DRIFT = "concept_drift"
    LABEL_DRIFT = "label_drift"
    PREDICTION_DRIFT = "prediction_drift"

class PerformanceMetric(Enum):
    """معیار عملکرد"""
    ACCURACY = "accuracy"
    PRECISION = "precision"
    RECALL = "recall"
    F1_SCORE = "f1_score"
    AUC_ROC = "auc_roc"
    MSE = "mse"
    RMSE = "rmse"
    MAE = "mae"
    R2_SCORE = "r2_score"
    LATENCY = "latency"
    THROUGHPUT = "throughput"

class MonitoringStatus(Enum):
    """وضعیت نظارت"""
    ACTIVE = "active"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"

@dataclass
class DataSample:
    """نمونه داده"""
    timestamp: datetime
    features: Dict[str, Any]
    prediction: Optional[Any] = None
    actual: Optional[Any] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if isinstance(self.features, dict):
            # Convert numpy arrays to lists for JSON serialization
            for key, value in self.features.items():
                if hasattr(value, 'tolist'):
                    self.features[key] = value.tolist()

@dataclass
class PerformanceSnapshot:
    """اسناپ‌شات عملکرد"""
    timestamp: datetime
    model_name: str
    model_version: str
    metrics: Dict[str, float]
    sample_count: int
    time_window: str
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "model_name": self.model_name,
            "model_version": self.model_version,
            "metrics": self.metrics,
            "sample_count": self.sample_count,
            "time_window": self.time_window
        }

@dataclass
class DriftAlert:
    """هشدار انحراف"""
    timestamp: datetime
    model_name: str
    model_version: str
    drift_type: DriftType
    severity: AlertSeverity
    drift_score: float
    threshold: float
    affected_features: List[str]
    description: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "model_name": self.model_name,
            "model_version": self.model_version,
            "drift_type": self.drift_type.value,
            "severity": self.severity.value,
            "drift_score": self.drift_score,
            "threshold": self.threshold,
            "affected_features": self.affected_features,
            "description": self.description,
            "metadata": self.metadata
        }

@dataclass
class MonitoringRule:
    """قانون نظارت"""
    rule_id: str
    name: str
    metric: str
    operator: str  # '<', '>', '<=', '>='
    threshold: float
    severity: AlertSeverity
    time_window: int  # minutes
    enabled: bool = True
    description: str = ""

class StatisticalDriftDetector:
    """آشکارساز انحراف آماری"""
    
    def __init__(self, sensitivity: float = 0.05):
        self.sensitivity = sensitivity  # p-value threshold
        
    def detect_data_drift(self, reference_data: np.ndarray, 
                         current_data: np.ndarray) -> Tuple[bool, float, str]:
        """تشخیص انحراف داده"""
        try:
            from scipy import stats
            
            # Kolmogorov-Smirnov test for distribution difference
            ks_statistic, p_value = stats.ks_2samp(reference_data, current_data)
            
            drift_detected = p_value < self.sensitivity
            
            description = f"KS test: statistic={ks_statistic:.4f}, p-value={p_value:.4f}"
            
            return drift_detected, ks_statistic, description
            
        except ImportError:
            # Fallback to simple statistical comparison
            ref_mean, ref_std = np.mean(reference_data), np.std(reference_data)
            cur_mean, cur_std = np.mean(current_data), np.std(current_data)
            
            # Simple threshold-based detection
            mean_diff = abs(cur_mean - ref_mean) / (ref_std + 1e-8)
            std_diff = abs(cur_std - ref_std) / (ref_std + 1e-8)
            
            drift_score = max(mean_diff, std_diff)
            drift_detected = drift_score > 2.0  # 2 standard deviations
            
            description = f"Statistical drift: mean_diff={mean_diff:.4f}, std_diff={std_diff:.4f}"
            
            return drift_detected, drift_score, description
    
    def detect_concept_drift(self, predictions: List[float], 
                           actuals: List[float], window_size: int = 100) -> Tuple[bool, float, str]:
        """تشخیص انحراف مفهوم"""
        if len(predictions) < window_size * 2:
            return False, 0.0, "Insufficient data for concept drift detection"
        
        # Split into two windows
        mid_point = len(predictions) // 2
        old_errors = [abs(p - a) for p, a in zip(predictions[:mid_point], actuals[:mid_point])]
        new_errors = [abs(p - a) for p, a in zip(predictions[mid_point:], actuals[mid_point:])]
        
        try:
            from scipy import stats
            
            # Mann-Whitney U test for difference in error distributions
            statistic, p_value = stats.mannwhitneyu(old_errors, new_errors, alternative='two-sided')
            
            drift_detected = p_value < self.sensitivity
            description = f"Mann-Whitney U test: statistic={statistic:.4f}, p-value={p_value:.4f}"
            
            return drift_detected, statistic, description
            
        except ImportError:
            # Fallback to simple error comparison
            old_mean_error = np.mean(old_errors)
            new_mean_error = np.mean(new_errors)
            
            error_ratio = new_mean_error / (old_mean_error + 1e-8)
            drift_detected = error_ratio > 1.5 or error_ratio < 0.67  # 50% change threshold
            
            description = f"Error ratio: {error_ratio:.4f}"
            
            return drift_detected, error_ratio, description

class PerformanceTracker:
    """ردیاب عملکرد"""
    
    def __init__(self, max_history_size: int = 10000):
        self.max_history_size = max_history_size
        self.performance_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history_size))
        self.metrics_buffer: Dict[str, List[float]] = defaultdict(list)
        
    def record_prediction(self, model_name: str, model_version: str,
                         prediction: Any, actual: Optional[Any] = None,
                         latency: Optional[float] = None,
                         metadata: Optional[Dict[str, Any]] = None):
        """ثبت پیش‌بینی"""
        record = {
            "timestamp": datetime.now(),
            "model_name": model_name,
            "model_version": model_version,
            "prediction": prediction,
            "actual": actual,
            "latency": latency,
            "metadata": metadata or {}
        }
        
        key = f"{model_name}:{model_version}"
        self.performance_history[key].append(record)
        
        # Track latency
        if latency is not None:
            self.metrics_buffer[f"{key}:latency"].append(latency)
    
    def calculate_metrics(self, model_name: str, model_version: str,
                         time_window_minutes: int = 60) -> Dict[str, float]:
        """محاسبه معیارها"""
        key = f"{model_name}:{model_version}"
        history = self.performance_history[key]
        
        if not history:
            return {}
        
        # Filter by time window
        cutoff_time = datetime.now() - timedelta(minutes=time_window_minutes)
        recent_records = [r for r in history if r["timestamp"] >= cutoff_time]
        
        if not recent_records:
            return {}
        
        metrics = {}
        
        # Calculate latency metrics
        latencies = [r["latency"] for r in recent_records if r["latency"] is not None]
        if latencies:
            metrics["avg_latency"] = np.mean(latencies)
            metrics["max_latency"] = np.max(latencies)
            metrics["min_latency"] = np.min(latencies)
            metrics["p95_latency"] = np.percentile(latencies, 95)
        
        # Calculate throughput
        if recent_records:
            total_time = (recent_records[-1]["timestamp"] - recent_records[0]["timestamp"]).total_seconds()
            if total_time > 0:
                metrics["throughput"] = len(recent_records) / total_time
        
        # Calculate accuracy metrics if actuals are available
        predictions = []
        actuals = []
        
        for record in recent_records:
            if record["actual"] is not None:
                predictions.append(record["prediction"])
                actuals.append(record["actual"])
        
        if predictions and actuals:
            if self._is_classification_task(predictions, actuals):
                metrics.update(self._calculate_classification_metrics(predictions, actuals))
            else:
                metrics.update(self._calculate_regression_metrics(predictions, actuals))
        
        return metrics
    
    def _is_classification_task(self, predictions: List, actuals: List) -> bool:
        """تشخیص نوع مسئله"""
        # Simple heuristic: if all values are integers or booleans, it's classification
        try:
            pred_set = set(predictions)
            actual_set = set(actuals)
            
            # Check if all values are integers/booleans and small number of unique values
            all_int = all(isinstance(x, (int, bool, np.integer)) for x in pred_set.union(actual_set))
            small_cardinality = len(pred_set.union(actual_set)) <= 10
            
            return all_int and small_cardinality
        except:
            return False
    
    def _calculate_classification_metrics(self, predictions: List, actuals: List) -> Dict[str, float]:
        """محاسبه معیارهای کلاسیفیکیشن"""
        try:
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
            
            metrics = {}
            metrics["accuracy"] = accuracy_score(actuals, predictions)
            metrics["precision"] = precision_score(actuals, predictions, average='weighted', zero_division=0)
            metrics["recall"] = recall_score(actuals, predictions, average='weighted', zero_division=0)
            metrics["f1_score"] = f1_score(actuals, predictions, average='weighted', zero_division=0)
            
            return metrics
            
        except ImportError:
            # Simple accuracy calculation
            correct = sum(1 for p, a in zip(predictions, actuals) if p == a)
            return {"accuracy": correct / len(predictions)}
    
    def _calculate_regression_metrics(self, predictions: List, actuals: List) -> Dict[str, float]:
        """محاسبه معیارهای رگرسیون"""
        try:
            from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
            
            metrics = {}
            metrics["mse"] = mean_squared_error(actuals, predictions)
            metrics["rmse"] = np.sqrt(metrics["mse"])
            metrics["mae"] = mean_absolute_error(actuals, predictions)
            metrics["r2_score"] = r2_score(actuals, predictions)
            
            return metrics
            
        except ImportError:
            # Simple MSE and MAE calculation
            errors = [abs(p - a) for p, a in zip(predictions, actuals)]
            squared_errors = [(p - a) ** 2 for p, a in zip(predictions, actuals)]
            
            return {
                "mae": np.mean(errors),
                "mse": np.mean(squared_errors),
                "rmse": np.sqrt(np.mean(squared_errors))
            }
    
    def get_performance_trend(self, model_name: str, model_version: str,
                            metric: str, time_points: int = 10) -> List[Dict[str, Any]]:
        """روند عملکرد"""
        key = f"{model_name}:{model_version}"
        history = self.performance_history[key]
        
        if not history:
            return []
        
        # Divide history into time_points buckets
        total_records = len(history)
        bucket_size = max(1, total_records // time_points)
        
        trends = []
        for i in range(0, total_records, bucket_size):
            bucket = list(history)[i:i + bucket_size]
            if bucket:
                bucket_metrics = self._calculate_bucket_metrics(bucket)
                if metric in bucket_metrics:
                    trends.append({
                        "timestamp": bucket[-1]["timestamp"].isoformat(),
                        "value": bucket_metrics[metric],
                        "sample_count": len(bucket)
                    })
        
        return trends
    
    def _calculate_bucket_metrics(self, bucket: List[Dict]) -> Dict[str, float]:
        """محاسبه معیارهای bucket"""
        if not bucket:
            return {}
        
        # Extract data
        predictions = [r["prediction"] for r in bucket if r["prediction"] is not None]
        actuals = [r["actual"] for r in bucket if r["actual"] is not None]
        latencies = [r["latency"] for r in bucket if r["latency"] is not None]
        
        metrics = {}
        
        # Latency metrics
        if latencies:
            metrics["avg_latency"] = np.mean(latencies)
            metrics["max_latency"] = np.max(latencies)
        
        # Performance metrics
        if predictions and actuals and len(predictions) == len(actuals):
            if self._is_classification_task(predictions, actuals):
                metrics.update(self._calculate_classification_metrics(predictions, actuals))
            else:
                metrics.update(self._calculate_regression_metrics(predictions, actuals))
        
        return metrics

class ModelMonitor:
    """نظارت‌گر مدل"""
    
    def __init__(self, model_name: str, model_version: str):
        self.model_name = model_name
        self.model_version = model_version
        self.logger = logging.getLogger(f"{__name__}.{model_name}")
        
        # Components
        self.performance_tracker = PerformanceTracker()
        self.drift_detector = StatisticalDriftDetector()
        
        # Configuration
        self.monitoring_rules: List[MonitoringRule] = []
        self.drift_thresholds = {
            DriftType.DATA_DRIFT: 0.1,
            DriftType.CONCEPT_DRIFT: 0.1,
            DriftType.PREDICTION_DRIFT: 0.15
        }
        
        # State
        self.status = MonitoringStatus.ACTIVE
        self.data_samples: deque = deque(maxlen=10000)
        self.reference_data: Optional[Dict[str, np.ndarray]] = None
        self.alerts: List[DriftAlert] = []
        self.performance_snapshots: List[PerformanceSnapshot] = []
        
        # Callbacks
        self.alert_callbacks: List[Callable] = []
        self.performance_callbacks: List[Callable] = []
        
        # Threading
        self.lock = threading.Lock()
        self.monitoring_thread: Optional[threading.Thread] = None
        self.stop_monitoring = threading.Event()
        
    def set_reference_data(self, features: Dict[str, List[float]]):
        """تنظیم داده مرجع"""
        with self.lock:
            self.reference_data = {}
            for feature_name, values in features.items():
                self.reference_data[feature_name] = np.array(values)
            
            self.logger.info(f"Reference data set for {len(features)} features")
    
    def add_monitoring_rule(self, rule: MonitoringRule):
        """اضافه کردن قانون نظارت"""
        with self.lock:
            self.monitoring_rules.append(rule)
            self.logger.info(f"Monitoring rule added: {rule.name}")
    
    def record_prediction(self, features: Dict[str, Any], prediction: Any,
                         actual: Optional[Any] = None, latency: Optional[float] = None,
                         metadata: Optional[Dict[str, Any]] = None):
        """ثبت پیش‌بینی"""
        # Record in performance tracker
        self.performance_tracker.record_prediction(
            self.model_name, self.model_version, prediction, actual, latency, metadata
        )
        
        # Store data sample
        sample = DataSample(
            timestamp=datetime.now(),
            features=features,
            prediction=prediction,
            actual=actual,
            metadata=metadata or {}
        )
        
        with self.lock:
            self.data_samples.append(sample)
        
        # Check for drift
        self._check_drift(sample)
    
    def _check_drift(self, sample: DataSample):
        """بررسی انحراف"""
        if not self.reference_data:
            return
        
        # Check data drift for each feature
        for feature_name, feature_value in sample.features.items():
            if feature_name in self.reference_data:
                try:
                    # Convert single value to array for comparison
                    current_values = [feature_value] if not isinstance(feature_value, list) else feature_value
                    current_array = np.array(current_values)
                    
                    # Get recent samples for this feature
                    recent_samples = []
                    cutoff_time = datetime.now() - timedelta(hours=1)
                    
                    with self.lock:
                        for s in self.data_samples:
                            if s.timestamp >= cutoff_time and feature_name in s.features:
                                val = s.features[feature_name]
                                if isinstance(val, list):
                                    recent_samples.extend(val)
                                else:
                                    recent_samples.append(val)
                    
                    if len(recent_samples) >= 30:  # Minimum samples for drift detection
                        recent_array = np.array(recent_samples)
                        
                        drift_detected, drift_score, description = self.drift_detector.detect_data_drift(
                            self.reference_data[feature_name], recent_array
                        )
                        
                        if drift_detected:
                            self._create_drift_alert(
                                drift_type=DriftType.DATA_DRIFT,
                                drift_score=drift_score,
                                threshold=self.drift_thresholds[DriftType.DATA_DRIFT],
                                affected_features=[feature_name],
                                description=f"Data drift detected in {feature_name}: {description}"
                            )
                
                except Exception as e:
                    self.logger.error(f"Error checking drift for {feature_name}: {e}")
    
    def _create_drift_alert(self, drift_type: DriftType, drift_score: float,
                          threshold: float, affected_features: List[str], description: str):
        """ایجاد هشدار انحراف"""
        # Determine severity
        if drift_score > threshold * 3:
            severity = AlertSeverity.CRITICAL
        elif drift_score > threshold * 2:
            severity = AlertSeverity.HIGH
        elif drift_score > threshold * 1.5:
            severity = AlertSeverity.MEDIUM
        else:
            severity = AlertSeverity.LOW
        
        alert = DriftAlert(
            timestamp=datetime.now(),
            model_name=self.model_name,
            model_version=self.model_version,
            drift_type=drift_type,
            severity=severity,
            drift_score=drift_score,
            threshold=threshold,
            affected_features=affected_features,
            description=description
        )
        
        with self.lock:
            self.alerts.append(alert)
        
        # Notify callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"Alert callback error: {e}")
        
        self.logger.warning(f"Drift alert: {alert.description}")
    
    def check_performance_rules(self):
        """بررسی قوانین عملکرد"""
        current_metrics = self.performance_tracker.calculate_metrics(
            self.model_name, self.model_version
        )
        
        for rule in self.monitoring_rules:
            if not rule.enabled or rule.metric not in current_metrics:
                continue
            
            metric_value = current_metrics[rule.metric]
            
            # Check rule condition
            rule_violated = False
            if rule.operator == '>':
                rule_violated = metric_value > rule.threshold
            elif rule.operator == '<':
                rule_violated = metric_value < rule.threshold
            elif rule.operator == '>=':
                rule_violated = metric_value >= rule.threshold
            elif rule.operator == '<=':
                rule_violated = metric_value <= rule.threshold
            
            if rule_violated:
                self._create_performance_alert(rule, metric_value)
    
    def _create_performance_alert(self, rule: MonitoringRule, metric_value: float):
        """ایجاد هشدار عملکرد"""
        alert = DriftAlert(
            timestamp=datetime.now(),
            model_name=self.model_name,
            model_version=self.model_version,
            drift_type=DriftType.PREDICTION_DRIFT,  # Use prediction drift for performance issues
            severity=rule.severity,
            drift_score=metric_value,
            threshold=rule.threshold,
            affected_features=[],
            description=f"Performance rule violated: {rule.name} - {rule.metric} {rule.operator} {rule.threshold} (actual: {metric_value:.4f})"
        )
        
        with self.lock:
            self.alerts.append(alert)
        
        # Notify callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"Performance alert callback error: {e}")
        
        self.logger.warning(f"Performance alert: {alert.description}")
    
    def create_performance_snapshot(self, time_window_minutes: int = 60) -> PerformanceSnapshot:
        """ایجاد اسناپ‌شات عملکرد"""
        metrics = self.performance_tracker.calculate_metrics(
            self.model_name, self.model_version, time_window_minutes
        )
        
        # Count samples in time window
        cutoff_time = datetime.now() - timedelta(minutes=time_window_minutes)
        with self.lock:
            sample_count = sum(1 for s in self.data_samples if s.timestamp >= cutoff_time)
        
        snapshot = PerformanceSnapshot(
            timestamp=datetime.now(),
            model_name=self.model_name,
            model_version=self.model_version,
            metrics=metrics,
            sample_count=sample_count,
            time_window=f"{time_window_minutes}m"
        )
        
        with self.lock:
            self.performance_snapshots.append(snapshot)
            # Keep only last 100 snapshots
            if len(self.performance_snapshots) > 100:
                self.performance_snapshots.pop(0)
        
        # Notify callbacks
        for callback in self.performance_callbacks:
            try:
                callback(snapshot)
            except Exception as e:
                self.logger.error(f"Performance snapshot callback error: {e}")
        
        return snapshot
    
    def start_monitoring(self, check_interval_minutes: int = 5):
        """شروع نظارت"""
        if self.status == MonitoringStatus.ACTIVE:
            return
        
        self.status = MonitoringStatus.ACTIVE
        self.stop_monitoring.clear()
        
        def monitoring_loop():
            while not self.stop_monitoring.wait(check_interval_minutes * 60):
                try:
                    self.check_performance_rules()
                    self.create_performance_snapshot()
                except Exception as e:
                    self.logger.error(f"Monitoring error: {e}")
        
        self.monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        self.logger.info(f"Model monitoring started for {self.model_name}:v{self.model_version}")
    
    def stop_monitoring_process(self):
        """توقف نظارت"""
        self.status = MonitoringStatus.STOPPED
        self.stop_monitoring.set()
        
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        self.logger.info(f"Model monitoring stopped for {self.model_name}:v{self.model_version}")
    
    def get_recent_alerts(self, hours: int = 24) -> List[DriftAlert]:
        """هشدارهای اخیر"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        with self.lock:
            return [alert for alert in self.alerts if alert.timestamp >= cutoff_time]
    
    def get_performance_history(self, hours: int = 24) -> List[PerformanceSnapshot]:
        """تاریخچه عملکرد"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        with self.lock:
            return [snapshot for snapshot in self.performance_snapshots if snapshot.timestamp >= cutoff_time]
    
    def get_statistics(self) -> Dict[str, Any]:
        """آمار نظارت"""
        with self.lock:
            recent_alerts = self.get_recent_alerts(24)
            
            alert_counts = {}
            for severity in AlertSeverity:
                alert_counts[severity.value] = sum(1 for alert in recent_alerts if alert.severity == severity)
            
            drift_counts = {}
            for drift_type in DriftType:
                drift_counts[drift_type.value] = sum(1 for alert in recent_alerts if alert.drift_type == drift_type)
            
            return {
                "model_name": self.model_name,
                "model_version": self.model_version,
                "status": self.status.value,
                "total_samples": len(self.data_samples),
                "total_alerts": len(self.alerts),
                "recent_alerts_24h": len(recent_alerts),
                "alert_counts_by_severity": alert_counts,
                "drift_counts_by_type": drift_counts,
                "monitoring_rules": len(self.monitoring_rules),
                "has_reference_data": self.reference_data is not None
            }
    
    def export_monitoring_report(self, filename: str = None) -> str:
        """گزارش نظارت"""
        if filename is None:
            filename = f"monitoring_report_{self.model_name}_v{self.model_version}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "model_info": {
                "name": self.model_name,
                "version": self.model_version,
                "monitoring_status": self.status.value
            },
            "statistics": self.get_statistics(),
            "recent_alerts": [alert.to_dict() for alert in self.get_recent_alerts(24)],
            "performance_history": [snapshot.to_dict() for snapshot in self.get_performance_history(24)],
            "monitoring_rules": [
                {
                    "rule_id": rule.rule_id,
                    "name": rule.name,
                    "metric": rule.metric,
                    "operator": rule.operator,
                    "threshold": rule.threshold,
                    "severity": rule.severity.value,
                    "enabled": rule.enabled
                }
                for rule in self.monitoring_rules
            ]
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Monitoring report exported to {filename}")
            return filename
        except Exception as e:
            self.logger.error(f"Failed to export monitoring report: {e}")
            return ""
    
    def add_alert_callback(self, callback: Callable[[DriftAlert], None]):
        """اضافه کردن callback هشدار"""
        self.alert_callbacks.append(callback)
    
    def add_performance_callback(self, callback: Callable[[PerformanceSnapshot], None]):
        """اضافه کردن callback عملکرد"""
        self.performance_callbacks.append(callback)

class ModelMonitoringManager:
    """مدیر نظارت مدل‌ها"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.monitors: Dict[str, ModelMonitor] = {}
        self.global_alert_callbacks: List[Callable] = []
        self.lock = threading.Lock()
        
    def create_monitor(self, model_name: str, model_version: str) -> ModelMonitor:
        """ایجاد نظارت‌گر مدل"""
        monitor_key = f"{model_name}:v{model_version}"
        
        with self.lock:
            if monitor_key in self.monitors:
                return self.monitors[monitor_key]
            
            monitor = ModelMonitor(model_name, model_version)
            
            # Add global callbacks
            for callback in self.global_alert_callbacks:
                monitor.add_alert_callback(callback)
            
            self.monitors[monitor_key] = monitor
            
            self.logger.info(f"Monitor created for {monitor_key}")
            return monitor
    
    def get_monitor(self, model_name: str, model_version: str) -> Optional[ModelMonitor]:
        """دریافت نظارت‌گر"""
        monitor_key = f"{model_name}:v{model_version}"
        return self.monitors.get(monitor_key)
    
    def start_all_monitoring(self):
        """شروع نظارت همه مدل‌ها"""
        with self.lock:
            for monitor in self.monitors.values():
                if monitor.status != MonitoringStatus.ACTIVE:
                    monitor.start_monitoring()
        
        self.logger.info(f"Started monitoring for {len(self.monitors)} models")
    
    def start_monitoring(self):
        """شروع نظارت (alias برای start_all_monitoring)"""
        self.start_all_monitoring()
    
    def stop_all_monitoring(self):
        """توقف نظارت همه مدل‌ها"""
        with self.lock:
            for monitor in self.monitors.values():
                if monitor.status == MonitoringStatus.ACTIVE:
                    monitor.stop_monitoring_process()
        
        self.logger.info("Stopped monitoring for all models")
    
    def get_global_statistics(self) -> Dict[str, Any]:
        """آمار کلی"""
        with self.lock:
            total_monitors = len(self.monitors)
            active_monitors = sum(1 for m in self.monitors.values() if m.status == MonitoringStatus.ACTIVE)
            
            total_alerts = 0
            total_samples = 0
            
            for monitor in self.monitors.values():
                stats = monitor.get_statistics()
                total_alerts += stats["total_alerts"]
                total_samples += stats["total_samples"]
            
            return {
                "total_monitors": total_monitors,
                "active_monitors": active_monitors,
                "total_alerts": total_alerts,
                "total_samples": total_samples,
                "monitoring_coverage": (active_monitors / total_monitors * 100) if total_monitors > 0 else 0
            }
    
    def add_global_alert_callback(self, callback: Callable[[DriftAlert], None]):
        """اضافه کردن callback کلی هشدار"""
        self.global_alert_callbacks.append(callback)
        
        # Add to existing monitors
        with self.lock:
            for monitor in self.monitors.values():
                monitor.add_alert_callback(callback)

# Global monitoring manager
monitoring_manager = ModelMonitoringManager()

@contextmanager
def monitoring_context(model_name: str, model_version: str):
    """Context manager برای نظارت مدل"""
    monitor = monitoring_manager.create_monitor(model_name, model_version)
    monitor.start_monitoring()
    
    try:
        yield monitor
    finally:
        monitor.stop_monitoring_process()

# Test and examples
def test_model_monitoring():
    """تست سیستم نظارت مدل"""
    print("👁️ Testing Model Monitoring System...")
    
    # Create monitor
    monitor = ModelMonitor("test_model", "1.0")
    
    # Set reference data
    reference_features = {
        "feature1": [1.0, 1.1, 0.9, 1.2, 0.8] * 20,
        "feature2": [2.0, 2.1, 1.9, 2.2, 1.8] * 20
    }
    monitor.set_reference_data(reference_features)
    
    # Add monitoring rules
    accuracy_rule = MonitoringRule(
        rule_id="accuracy_rule",
        name="Accuracy Threshold",
        metric="accuracy",
        operator="<",
        threshold=0.8,
        severity=AlertSeverity.HIGH,
        time_window=60
    )
    monitor.add_monitoring_rule(accuracy_rule)
    
    latency_rule = MonitoringRule(
        rule_id="latency_rule",
        name="Latency Threshold",
        metric="avg_latency",
        operator=">",
        threshold=100.0,
        severity=AlertSeverity.MEDIUM,
        time_window=60
    )
    monitor.add_monitoring_rule(latency_rule)
    
    # Simulate predictions
    print("\n1. Recording predictions...")
    import random
    
    for i in range(100):
        features = {
            "feature1": random.gauss(1.0, 0.1),
            "feature2": random.gauss(2.0, 0.1)
        }
        
        prediction = random.choice([0, 1])
        actual = prediction if random.random() > 0.1 else 1 - prediction  # 90% accuracy
        latency = random.gauss(50, 10)
        
        monitor.record_prediction(features, prediction, actual, latency)
    
    print(f"✓ Recorded 100 predictions")
    
    # Create performance snapshot
    print("\n2. Creating performance snapshot...")
    snapshot = monitor.create_performance_snapshot()
    print(f"✓ Snapshot created: {snapshot.sample_count} samples, {len(snapshot.metrics)} metrics")
    
    # Check statistics
    print("\n3. Checking statistics...")
    stats = monitor.get_statistics()
    print(f"✓ Total samples: {stats['total_samples']}")
    print(f"✓ Total alerts: {stats['total_alerts']}")
    print(f"✓ Recent alerts: {stats['recent_alerts_24h']}")
    
    # Export report
    print("\n4. Exporting report...")
    report_file = monitor.export_monitoring_report()
    if report_file:
        print(f"✓ Report exported: {report_file}")
        # Clean up
        import os
        if os.path.exists(report_file):
            os.remove(report_file)
    
    print("\n✅ Model Monitoring System test completed!")

if __name__ == "__main__":
    test_model_monitoring() 