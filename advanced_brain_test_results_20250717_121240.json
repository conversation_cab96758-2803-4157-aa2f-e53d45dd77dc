{"timestamp": "2025-07-17T12:12:40.957956", "available_modules": {"memory_manager": {"success": true, "stats": "MemoryStats(timestamp=datetime.datetime(2025, 7, 17, 12, 12, 37, 635249), total_memory=8105.42578125, used_memory=7217.078125, available_memory=888.34765625, memory_percent=89.0, swap_memory=2631.52734375, process_memory=1950.5625, memory_level=<MemoryLevel.HIGH: 'high'>, gc_count=0, active_objects=0)"}, "enhanced_replay": {"success": true, "buffer_size": 1000}, "genetic_evolution": {"success": true}, "continual_learning": {"success": true, "ewc_lambda": 0.4}, "backtesting": {"success": true}}, "brain_decisions": [{"action": "train", "model": "FinBERT", "reasoning": "Selected FinBERT based on priority 1", "confidence": 0.7433321950683219, "expected_performance": 0.8374377921342151}, {"action": "train", "model": "EnhancedDQNAgent", "reasoning": "Selected EnhancedDQNAgent based on priority 1", "confidence": 0.7812834855027747, "expected_performance": 0.7376493667413556}, {"action": "train", "model": "DQN_Agent", "reasoning": "Selected DQN_Agent based on priority 1", "confidence": 0.8311883445160857, "expected_performance": 0.7176499782666613}], "resource_predictions": {"FinBERT": {"memory_mb": 2457.6, "training_time_min": 49.50000000000001, "confidence": 0.8813284848811247}, "LSTM_TimeSeries": {"memory_mb": 1228.8, "training_time_min": 33.0, "confidence": 0.8222209879068572}, "DQN_Agent": {"memory_mb": 1440.0, "training_time_min": 66.0, "confidence": 0.8113985781112051}, "PPO_Agent": {"memory_mb": 1320.0, "training_time_min": 60.50000000000001, "confidence": 0.7577903322507833}, "EnhancedDQNAgent": {"memory_mb": 1800.0, "training_time_min": 88.0, "confidence": 0.880795592643344}}, "integration": {"success": false}, "advanced_features": {"genetic_optimization": {"success": true, "optimized_params": {"learning_rate": 0.001, "batch_size": 64, "hidden_size": 128}}, "continual_learning": {"success": true, "ewc_lambda": 0.4, "replay_buffer_size": 10000}, "enhanced_replay": {"success": true, "buffer_size": 1000, "experiences_added": 10, "batch_size": 5}, "backtesting": {"success": true, "engine_initialized": true, "metrics": {"sharpe_ratio": 1.5, "max_drawdown": 0.15, "win_rate": 0.65}}}}