{"type": "startup_tests", "timestamp": "2025-07-14T22:28:34.297759", "summary": {"total_tests": 4, "passed": 0, "failed": 4, "success_rate": 0.0, "total_duration": 264.74714946746826}, "results": [{"name": "test_advanced_risk_manager.py", "passed": false, "duration": 89.15509963035583, "output": "============================= test session starts =============================\nplatform win32 -- Python 3.9.0, pytest-8.3.5, pluggy-1.5.0 -- C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe\ncachedir: .pytest_cache\nrootdir: D:\\project\nconfigfile: pytest.ini\nplugins: anyio-4.9.0, asyncio-1.0.0, cov-6.2.1, mock-3.14.1\nasyncio: mode=strict, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... collected 45 items\n\ntests/test_advanced_risk_manager.py::TestRiskParameters::test_default_parameters PASSED [  2%]\ntests/test_advanced_risk_manager.py::TestRiskParameters::test_custom_parameters PASSED [  4%]\ntests/test_advanced_risk_manager.py::TestRiskParameters::test_calculated_properties PASSED [  6%]\ntests/test_advanced_risk_manager.py::TestRiskParameters::test_edge_cases PASSED [  8%]\ntests/test_advanced_risk_manager.py::TestPosition::test_position_creation PASSED [ 11%]\ntests/test_advanced_risk_manager.py::TestPosition::test_risk_reward_ratio PASSED [ 13%]\ntests/test_advanced_risk_manager.py::TestPosition::test_zero_risk_amount PASSED [ 15%]\ntests/test_advanced_risk_manager.py::TestPerformanceMetrics::test_performance_metrics_creation PASSED [ 17%]\ntests/test_advanced_risk_manager.py::TestPerformanceMetrics::test_drawdown_percentage PASSED [ 20%]\ntests/test_advanced_risk_manager.py::TestPerformanceMetrics::test_zero_capital_drawdown PASSED [ 22%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_initialization PASSED [ 24%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_default_risk_parameters PASSED [ 26%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_calculate_position_size_buy PASSED [ 28%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_calculate_position_size_sell PASSED [ 31%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_calculate_position_size_zero_risk PASSED [ 33%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_calculate_position_size_capital_limit PASSED [ 35%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_calculate_stop_loss_take_profit_buy PASSED [ 37%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_calculate_stop_loss_take_profit_sell PASSED [ 40%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_check_risk_limits_normal PASSED [ 42%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_check_risk_limits_max_drawdown PASSED [ 44%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_check_risk_limits_daily_loss PASSED [ 46%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_check_risk_limits_max_positions PASSED [ 48%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_check_risk_limits_insufficient_capital PASSED [ 51%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_check_risk_limits_targets_reached PASSED [ 53%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_can_open_position_success PASSED [ 55%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_can_open_position_existing PASSED [ 57%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_can_open_position_risk_limits PASSED [ 60%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_open_position_success PASSED [ 62%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_open_position_with_custom_levels PASSED [ 64%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_open_position_failure PASSED [ 66%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_close_position_success_profit PASSED [ 68%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_close_position_success_loss PASSED [ 71%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_close_position_nonexistent PASSED [ 73%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_close_position_sell_success PASSED [ 75%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_update_daily_reset PASSED [ 77%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_get_risk_level_low PASSED [ 80%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_get_risk_level_medium PASSED [ 82%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_get_risk_level_high PASSED [ 84%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_get_risk_level_extreme PASSED [ 86%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_get_portfolio_status PASSED [ 88%]\ntests/test_advanced_risk_manager.py::TestAdvancedRiskManager::test_save_state PASSED [ 91%]\ntests/test_advanced_risk_manager.py::TestIntegrationScenarios::test_full_trading_cycle PASSED [ 93%]\ntests/test_advanced_risk_manager.py::TestIntegrationScenarios::test_multiple_positions_scenario PASSED [ 95%]\ntests/test_advanced_risk_manager.py::TestIntegrationScenarios::test_risk_limit_scenario PASSED [ 97%]\ntests/test_advanced_risk_manager.py::TestIntegrationScenarios::test_profit_target_scenario PASSED [100%]\nERROR: Coverage failure: total of 13 is less than fail-under=80\n\n\n=============================== tests coverage ================================\n_______________ coverage: platform win32, python 3.9.0-final-0 ________________\\n\\nName                                        Stmts   Miss  Cover   Missing\\n-------------------------------------------------------------------------\\nactivate_inactive_modules.py                  137    137     0%   8-347\\nadvanced_system_diagnostics.py                436    436     0%   8-769\\nadvanced_system_diagnostics_simple.py         408    408     0%   8-703\\nai_brain_fix.py                                12     12     0%   7-41\\nai_models\\\\__init__.py                         515    337    35%   17-19, 46-55, 62-64, 84, 115-117, 121-124, 128, 132, 136-141, 145, 166, 170, 179, 189, 209, 214-217, 222-229, 234-243, 247-254, 258, 267, 271, 297, 302-305, 310-317, 321, 331-334, 338-344, 348-384, 393-398, 402, 438-440, 444, 457-462, 466-480, 484-488, 497, 508-511, 515-523, 528-532, 545, 555-558, 562-570, 574-578, 589, 599-602, 606-614, 618-622, 633, 643-646, 650-657, 661-664, 668, 678-681, 685-692, 696-700, 704, 714-717, 721-728, 732-735, 739-742, 746, 756-759, 763-770, 774-777, 790, 800-925, 945-949, 953-957, 961-962, 966, 970, 974\\nai_models\\\\huggingface_models.py               278    278     0%   8-544\\nai_models\\\\model_manager.py                    307    307     0%   8-582\\nai_models\\\\sentiment_models.py                  83     63    24%   17-21, 25-35, 39-54, 58, 68-72, 76-86, 90-105, 109, 119-123, 127-137, 141-156, 160\\napi\\\\__init__.py                               266    141    47%   61, 71, 125-127, 131-145, 149-163, 167, 200-210, 235-237, 263, 276, 280-301, 307, 311-317, 325-328, 332-354, 358-379, 384, 394-406, 415-427, 436-470, 479-498, 507-532, 542-563, 569-574, 578-590, 622-624\\napi\\\\endpoints.py                              138     87    37%   73-75, 79, 90-334, 338-349, 353-355\\napi\\\\realtime_dashboard.py                     336    185    45%   109-111, 115-117, 121-126, 130-141, 145, 172-258, 262-274, 278, 290-320, 329, 340-342, 359-363, 367-377, 381-397, 411-464, 469, 474, 507, 512-525, 530-535, 540, 545-553, 558-559, 564-565, 570-571, 576-577, 582-583, 588, 601-610, 615, 620-638, 643-652, 658-678, 687-698, 705-709, 713, 1006-1007\\napi\\\\server.py                                 100    100     0%   8-196\\nbakap.py                                       72     72     0%   1-122\\nclean_cache.py                                 17     17     0%   1-21\\ncomplete_debug_test.py                        100    100     0%   7-161\\ncomplete_model_audit.py                       166    166     0%   9-417\\ncomplete_module_test.py                       161    161     0%   8-278\\ncomprehensive_system_check.py                 126    126     0%   7-202\\ncomprehensive_system_test.py                  173    173     0%   7-282\\ncore\\\\__init__.py                              345    239    31%   83-88, 101-114, 126-131, 144-151, 163-169, 184-193, 212-225, 245, 296-325, 352-373, 395-411, 432-447, 469, 508-523, 534-539, 551-557, 719-732, 736-774, 778-816\\ncore\\\\advanced_config.py                       274    274     0%   8-401\\ncore\\\\advanced_order_management.py             368    224    39%   38-39, 140-150, 154, 158-160, 164, 173-186, 207, 211-212, 216-218, 222-231, 237-241, 245-271, 277-300, 305-332, 336-360, 364-375, 379-409, 414-449, 454-455, 459-483, 487-508, 512-530, 534-550, 554-555, 559, 563-568, 572, 576, 580-587, 600, 604, 614-617, 623-626, 631, 636, 640-657\\ncore\\\\advanced_risk_metrics.py                 300    213    29%   92, 146-231, 235-279, 283, 296, 310, 314-315, 319-320, 324-327, 331-333, 337-355, 359, 363-364, 368-369, 373-381, 386-394, 398-403, 407-416, 421-432, 437-438, 443-475, 479-495, 499-523, 527-545, 553, 557, 561, 565-591\\ncore\\\\backtesting_framework.py                 156     84    46%   62-63, 67, 73-75, 79-89, 102-111, 122-124, 142-147, 154-170, 174-187, 191-219, 231-232, 247, 251, 259-285\\ncore\\\\base.py                                  235    150    36%   29, 51, 76, 81, 86, 91, 95, 106-107, 118-129, 133, 137, 141-157, 161-174, 178-189, 193-205, 211-213, 217-230, 234-250, 254-270, 274-285, 300-301, 305, 317-326, 331, 336, 340-353, 357-384, 388-406, 410, 422\\ncore\\\\circuit_breaker_system.py                109     70    36%   102-105, 111-128, 131, 137-141, 144-150, 153-155, 161-163, 169, 179, 190-192, 197-205, 213-227, 235-249\\ncore\\\\config.py                                158     47    70%   93, 126-127, 131-133, 137-150, 191-198, 204-205, 213-218, 229-254\\ncore\\\\configuration_management.py              467    258    45%   36, 46-47, 50, 53, 61, 68, 108, 132-134, 138-140, 158-160, 164-166, 187-189, 193-195, 199-203, 219-222, 235-237, 250-252, 291-309, 314, 319, 322, 326-331, 336-343, 375-409, 413-419, 423-441, 449-471, 475-480, 484-497, 501-504, 508-517, 521-529, 533-541, 545-551, 555-578, 582-587, 591-592, 596-598, 602-606, 610, 614-626, 630-646, 651-663, 667-681, 685-690, 694-697, 701-704, 712, 716, 720, 724, 728, 732, 736, 740-775\\ncore\\\\correlation_analysis.py                  336    246    27%   70, 74-80, 85-88, 101, 105-119, 165-220, 241-294, 311-375, 392-465, 480-525, 542-600, 621-672, 677, 689, 704, 710, 714, 718, 723-756\\ncore\\\\database_manager.py                      332    332     0%   8-583\\ncore\\\\database_transaction_manager.py          324    192    41%   36-39, 141-154, 158-192, 196-197, 201-202, 206-210, 214-218, 229-234, 240-244, 248-250, 259-337, 341-357, 361-374, 378-401, 427-430, 434-449, 453-457, 461, 465-475, 479-489, 493, 502-504, 514-518, 524-527, 532, 536, 540, 544-568\\ncore\\\\enhanced_error_handling.py               314    170    46%   41-45, 48, 134, 179-196, 200-202, 206-210, 214-219, 223, 240-257, 265-284, 289-291, 310-315, 346-369, 373-377, 381-382, 389, 396-407, 417-438, 443-460, 464-473, 477-486, 491-495, 519, 523, 527, 531-573\\ncore\\\\error_handler.py                         240    131    45%   96-101, 105-118, 122-125, 129-130, 134-138, 177-195, 202-220, 224-225, 229-242, 246-250, 254-259, 263-274, 278-291, 303-308, 312, 328-329, 333-338, 342-368, 375-378, 390-404, 409-415, 421-425, 430, 436, 444-463\\ncore\\\\exceptions.py                             19      0   100%\\ncore\\\\logger.py                                 87     30    66%   43, 46, 106, 108, 115-122, 126-133, 137-144, 158-164\\ncore\\\\memory_manager.py                        266    110    59%   60, 85-91, 95-105, 109-112, 172, 180-181, 238, 242, 246-273, 302, 305-308, 317, 324, 336-341, 358-360, 364, 371, 384-385, 389-444, 449, 457-459, 467-468, 472-477, 481-484, 512-525, 529-532, 537-565\\ncore\\\\mlops_versioning.py                      100     66    34%   26-28, 90-111, 116-131, 136-150, 158-161, 165-175, 184, 187, 190\\ncore\\\\model_monitoring.py                      502    368    27%   77-81, 95, 120, 150, 155-181, 186-215, 221-223, 230-245, 250-294, 299-309, 313-327, 331-347, 356-378, 382-404, 410-440, 444-449, 453-455, 462-479, 483-524, 530-561, 565-587, 591-613, 617-648, 652-669, 673-679, 683-685, 689-691, 695-706, 721-754, 758, 762, 775-790, 794-795, 799-804, 808-813, 817-829, 839-844, 852-858, 863-938, 941\\ncore\\\\model_versioning.py                      441    275    38%   83-85, 111-119, 132, 136, 140-148, 156-178, 183-202, 224, 232-236, 243, 265-267, 295-296, 301-343, 348-369, 373-392, 397-409, 413-425, 429-441, 445-469, 473-479, 483-522, 526-549, 553-570, 585-593, 607-615, 619-622, 626-627, 631-645, 649-652, 660-744, 747\\ncore\\\\multi_exchange.py                        542    389    28%   70-73, 93-111, 125, 130, 135, 140, 145, 150, 155, 159-170, 174-176, 180-182, 186-190, 203, 207, 211, 215-219, 223-227, 231-235, 241-245, 249-271, 275-284, 288, 292-327, 331-360, 364-371, 376, 382-386, 390-409, 413-422, 426, 430-464, 468-496, 500-506, 510, 544-569, 573-586, 590-602, 607-623, 627-647, 651-654, 658-668, 672-677, 682-691, 695, 699, 703-713, 718-722, 726-729, 740-749, 753-755, 759-770, 785-797, 808-815, 819-836, 844-848, 853-923, 926\\ncore\\\\order_manager.py                         337    254    25%   57-104, 109-121, 172-212, 216-231, 235-251, 257-303, 307-382, 386-390, 394-398, 402, 406-409, 413, 417, 421-424, 428-431, 435-440, 444, 448, 452-505, 523-525, 529, 532, 535, 538-540, 548-549, 557-571, 576-636\\ncore\\\\performance_optimizer.py                 261    261     0%   8-575\\ncore\\\\realtime_data_processing.py              378    247    35%   78-79, 113-117, 122, 126, 130, 136-139, 143-170, 174-192, 196-203, 207-224, 228, 232, 236-237, 243-255, 266-276, 280-283, 287-305, 309-329, 333-347, 351-365, 369-381, 385-386, 390-391, 395-397, 401, 413-421, 432-442, 446-458, 462-470, 474-478, 482-489, 493-502, 506-517, 522, 526-530, 534-536, 540, 547-549, 553, 566-568, 573-575, 580-585, 596-599, 610-668, 671\\ncore\\\\shared_types.py                           79      0   100%\\ncore\\\\simple_config.py                         173     69    60%   61-70, 85-87, 99-103, 158, 163-174, 179-198, 202-226, 230, 234, 252, 257-258, 262, 266-267, 271-284\\ncore\\\\simple_database_manager.py               274    210    23%   60-64, 68-79, 83-95, 99-106, 112-116, 120-122, 128-189, 193-204, 217-223, 284-301, 307-324, 330-367, 371-399, 403-412, 416-441, 445-471, 475, 479-483, 490, 496-501, 507-509, 513-556\\ncore\\\\utils.py                                 372    282    24%   38-50, 55-67, 71-75, 79-82, 86-90, 94-100, 104-107, 138-141, 159-161, 165, 176-178, 188-193, 205-218, 222-229, 233-252, 268-304, 316-321, 334-348, 361-374, 387-400, 412-441, 454-470, 485-488, 492-504, 513-565, 581-599, 609-629, 643-656, 671-676, 690-693, 707-712, 722-739, 745-766\\ncvxpy_fix.py                                   17     17     0%   7-37\\ndata\\\\__init__.py                                0      0   100%\\ndata\\\\fetcher.py                                25     25     0%   1-38\\ndata\\\\import sys.py                              7      7     0%   1-12\\ndata\\\\preprocessor.py                           43     43     0%   1-76\\ndata\\\\test_fetcher.py                            7      7     0%   1-12\\ndebug_fitness_issue.py                         63     63     0%   6-124\\ndownload_and_test_models.py                   183    183     0%   9-331\\ndownload_hf_model.py                            0      0   100%\\nencoding_config.py                              9      9     0%   7-20\\nenhanced_financial_models.py                  356    356     0%   16-731\\nenhanced_spacy_mock.py                        116    116     0%   7-183\\nenv\\\\__init__.py                               194    136    30%   39-59, 63-76, 80-86, 90-103, 107, 120-134, 138-140, 144-146, 151-160, 173, 177-179, 184-202, 207-212, 216-231, 235-250, 254-261, 266-271, 275-292, 297-304, 308, 318-319, 323, 337-342, 346-347, 351-353, 358, 372-375, 381, 427-429\\nenv\\\\portfolio.py                               16     13    19%   6-23\\nenv\\\\trading_env.py                            229    201    12%   18-19, 29-102, 105-129, 133-192, 199-224, 227-292, 296-305, 308-476, 479, 482, 485, 489-492, 505-506\\nevaluation\\\\__init__.py                        251    151    40%   72, 115-117, 121-127, 131-141, 145, 160-203, 207-214, 218-230, 238-256, 274-324, 333-370, 379-408, 417-453, 484-495, 499-522, 534, 538, 542, 588-590\\nevaluation\\\\comparison.py                      185    138    25%   35-36, 53-55, 65-66, 73-143, 149-161, 167-207, 211-240, 244-263, 267-282, 288-289, 300-318, 326-345, 349, 388-408, 411\\nevaluation\\\\metrics.py                          35     33     6%   16-70\\nevaluation\\\\scenario_backtesting.py            354    324     8%   26-29, 33, 54-81, 98-126, 138-169, 186-238, 255-325, 339-361, 373-400, 410-466, 477-491, 502-519, 530-542, 553-567, 578-591, 602-621, 632-644, 655-668, 682-711\\nevaluation\\\\tests.py                             0      0   100%\\nexamples\\\\plutus_api_example.py                212    190    10%   28-32, 41-66, 75-105, 112-147, 154-200, 209-210, 220-264, 271-306, 313-392, 401-432, 436-449\\nexamples\\\\plutus_integration_final.py          198    166    16%   54-110, 117-171, 178-222, 229-288, 295-323, 330-343, 350-396, 399\\nexecution_pipeline.py                         315    239    24%   88-98, 102-108, 112-119, 123, 136-166, 170-175, 179, 247-300, 312-344, 352-388, 396-476, 484-541, 549-581, 589-619, 627-656, 664-667, 691-710, 714-729, 744-750\\nexpert_system_analysis.py                     324    324     0%   8-580\\nfinal_complete_fix_test.py                    229    229     0%   8-378\\nfinal_complete_test.py                         45     45     0%   7-71\\nfinal_real_models_system.py                   277    277     0%   16-569\\nfinal_summary.py                               40     40     0%   6-52\\nfinal_system_analysis.py                      111    111     0%   6-262\\nfinal_system_test.py                           24     24     0%   7-46\\nfinancial_models_free_usage.py                244    244     0%   16-441\\nfix_all_minor_issues.py                        92     92     0%   8-215\\nfix_all_warnings.py                           113    113     0%   8-373\\nfix_broken_non_test_files.py                  154    154     0%   8-2213\\nfix_clarabel_solver.py                         63     63     0%   7-115\\nfix_comprehensive_system_test.py              228    228     0%   7-396\\nfix_cursor_proxy.py                           129    129     0%   6-239\\nfix_encoding_issues.py                         66     66     0%   8-133\\nfix_final_issues.py                            98     98     0%   8-392\\nfix_import_issues.py                           79     79     0%   8-157\\nfix_inactive_modules.py                        69     69     0%   8-266\\nfix_main_system.py                            143    143     0%   8-367\\nfix_memory_issues.py                           78     78     0%   9-156\\nfix_multi_exchange.py                          84     84     0%   8-260\\nfix_persian_model.py                           79     79     0%   8-287\\nfix_real_issues.py                             97     97     0%   8-367\\nfix_remaining_issues.py                       138    138     0%   8-504\\nfix_spacy_models.py                            86     86     0%   8-342\\nfunctional_test.py                            127    127     0%   5-205\\nhuggingface_fix.py                             19     19     0%   7-36\\nimmediate_test.py                             145    145     0%   7-239\\ninstall_and_test_ai_models.py                 181    181     0%   9-331\\ninstall_real_models.py                         36     36     0%   6-75\\ninstall_simple.py                              28     28     0%   10-53\\ninstall_spacy_models.py                        54     54     0%   7-111\\nintegrated_trading_system.py                  222    222     0%   12-456\\nintegration_summary.py                         84     84     0%   7-131\\nllma.py                                       149    149     0%   8-356\\nlogging_config.py                              30     30     0%   7-63\\nmain.py                                        19     19     0%   9-35\\nmain_new.py                                   471    378    20%   37-38, 161-163, 181-183, 192-194, 234-241, 249-268, 272-284, 288-393, 397-417, 421-428, 432-439, 443-450, 454-461, 465-491, 495-532, 536-563, 567-659, 663-688, 692-721, 725-771, 780-823, 828-865, 869\\nmodel_usage_examples.py                        27     27     0%   7-56\\nmodels\\\\__init__.py                            166    104    37%   113-122, 126-133, 137-158, 162-193, 197, 201-204, 216-230, 234-247, 251-265, 274-281, 285, 289, 293, 376-378\\nmodels\\\\ai_agent.py                            466    371    20%   105-144, 152-154, 158, 186-208, 212-227, 231-238, 243-248, 253-258, 263-268, 272-321, 325-346, 350-368, 372-416, 420-444, 448-464, 470-510, 517-561, 565-572, 576-583, 587-609, 613-628, 634-658, 665-785, 799-831, 836-851, 855-871, 875-908, 912-944, 949\\nmodels\\\\continual_learning.py                  245    197    20%   39-43, 55-107, 118-129, 145-154, 175-181, 194-198, 214-217, 223-227, 234-250, 263-264, 267, 312-339, 350-367, 377, 390-391, 409-419, 435-454, 467-478, 498-511, 522-534, 545-557, 569-594, 615-653, 661-663, 667-668, 672-678, 682-686, 690-695\\nmodels\\\\ensemble_model.py                      253    228    10%   65-99, 110-136, 153-211, 230-275, 282-332, 348-378, 389, 401-437, 456-516, 522-539, 545-571\\nmodels\\\\hierarchical_rl.py                     243    243     0%   15-552\\nmodels\\\\meta_learner.py                        184    184     0%   1-431\\nmodels\\\\rl_models.py                           109     87    20%   21-78, 84-114, 116, 134, 140-145, 180-181, 195, 213, 228, 243, 252, 267, 284, 298-302, 305, 319, 331\\nmodels\\\\unified_trading_system.py              632    632     0%   14-1512\\nmodels\\\\zero_shot_learning.py                  298    298     0%   1-815\\nmodule_analysis.py                            202    202     0%   8-394\\noptimization\\\\__init__.py                      279    192    31%   62, 103-105, 109-115, 119-126, 130, 146-198, 217-257, 267-313, 323-356, 366-399, 408-437, 443-459, 463-492, 497-540, 552, 585-587\\noptimization\\\\bayesian.py                      119    100    16%   25-40, 50-73, 79, 99-150, 160-169, 179-200, 207-221, 227-232, 247-249, 255-267\\noptimization\\\\genetic.py                        81     70    14%   16-26, 32-74, 83-94, 99-110, 114-135, 139-155\\noptimization\\\\pso.py                           185    185     0%   8-385\\npersian_sentiment_fallback.py                  57     37    35%   47-90, 99-114, 121-122, 130\\nph3_optimizer.py                              297    297     0%   1-567\\nportfolio\\\\__init__.py                         298    204    32%   51-58, 62, 66-76, 94-95, 99, 103-105, 111-144, 148-158, 162-168, 172-185, 189, 205-212, 217-233, 239-270, 274-337, 341-343, 347-354, 358-371, 375, 379, 383-385, 389-395, 399-413, 417, 435-449, 453-454, 458-460, 465-467, 471-473, 477-481, 488, 495, 499, 503, 507, 511, 520-523, 567-569\\nportfolio\\\\advanced_risk_manager.py            250    144    42%   83-85, 106-108, 142, 145, 156-157, 167-174, 194, 198, 202, 206, 209, 212, 220-242, 248-301, 306-375, 379-386, 391-400, 405-415, 465-505, 510-550\\nportfolio\\\\portfolio_manager.py                157    131    17%   14-15, 24-52, 63-137, 147-165, 170-191, 209-250, 271-315, 325-357, 369, 373, 377-380, 384, 388, 392-395, 413\\nportfolio\\\\smart_portfolio_manager.py          259    259     0%   8-547\\npractical_model_test.py                       164    164     0%   9-362\\nprofit_target_demo.py                         230    230     0%   8-461\\nproject_analyzer.py                           149    149     0%   8-285\\nquick_complete_system_test.py                 195    195     0%   8-302\\nquick_debug_test.py                            31     31     0%   6-45\\nquick_final_test.py                           141    141     0%   7-235\\nquick_fix_main.py                              15     15     0%   7-41\\nquick_reality_check.py                         94     94     0%   6-141\\nquick_status_check.py                          44     44     0%   5-66\\nquick_test.py                                  74     74     0%   7-108\\nreal_huggingface_models.py                     40     40     0%   16-306\\nrun_comprehensive_tests.py                     82     82     0%   6-206\\nrun_sentiment_tests.py                         51     51     0%   1-105\\nrun_tests.py                                   44     44     0%   9-84\\nsentiment_integration.py                      211    211     0%   9-364\\nsetup_financial_models.py                     163    163     0%   10-390\\nsimple_debug_test.py                           25     25     0%   6-37\\nsimple_financial_models.py                    253    253     0%   14-486\\nsimple_model_test.py                           39     39     0%   7-69\\nsimple_profit_demo.py                         187    187     0%   8-350\\nsimple_system_test.py                         144    144     0%   6-287\\nsimple_test.py                                 77     77     0%   6-120\\nsystem_dashboard.py                            91     91     0%   7-148\\nsystem_status.py                               58     58     0%   7-90\\ntest_advanced_risk_metrics.py                  82     82     0%   7-143\\ntest_ai_agent.py                               70     70     0%   6-133\\ntest_ai_agent_integration.py                   46     46     0%   11-102\\ntest_all_fixes.py                              76     76     0%   7-134\\ntest_backtesting_integration.py                96     96     0%   8-194\\ntest_complete_system.py                        83     83     0%   7-134\\ntest_connection_no_proxy.py                    35     35     0%   6-59\\ntest_fetcher.py                                 2      2     0%   1-3\\ntest_final_integration.py                     306    306     0%   8-656\\ntest_genetic_algorithm_deep.py                128    128     0%   6-319\\ntest_hierarchical_simple.py                    23     23     0%   8-43\\ntest_integrated_system.py                     187    187     0%   15-368\\ntest_integration.py                           228    228     0%   10-405\\ntest_integration_final.py                     275    275     0%   13-483\\ntest_integration_fixed.py                     243    243     0%   12-428\\ntest_integration_v2.py                        370    370     0%   9-630\\ntest_local_only.py                             41     41     0%   6-80\\ntest_models_simple.py                          55     55     0%   6-84\\ntest_offline.py                                 0      0   100%\\ntest_persian_improved.py                       38     38     0%   4-73\\ntest_ray.py                                     8      8     0%   1-9\\ntest_real_models.py                           170    170     0%   8-382\\ntest_real_models_with_proxy.py                196    196     0%   9-437\\ntest_sentiment_complete.py                     32     32     0%   4-63\\ntest_simple_models.py                          87     87     0%   7-183\\ntest_unified_system.py                        199    199     0%   6-515\\ntest_with_http_proxy.py                        65     65     0%   7-118\\ntest_with_token.py                             81     81     0%   7-190\\ntests\\\\conftest.py                             320    215    33%   43-45, 77-78, 83-84, 91, 96-98, 103, 108-115, 122-127, 132-137, 144-151, 156-164, 169-175, 182-195, 200-212, 219-237, 242-261, 268-280, 287-299, 306-317, 322-339, 346-356, 361-372, 379-382, 387-393, 400, 421, 441-472, 479, 501, 504, 519, 523-527, 531-536, 540-542\\ntests\\\\test_advanced_risk_manager.py           137      1    99%   316\\ntests\\\\test_plutus_models_comprehensive.py     342    306    11%   62-103, 110-181, 188-301, 308-359, 366-428, 435-503, 510-578, 586-599, 606-621, 625-674\\nultimate_final_test.py                        143    143     0%   8-206\\nutils\\\\__init__.py                              96     33    66%   109, 117, 128-133, 138-143, 148-152, 158, 162, 166, 172, 176, 180, 184-189, 274-276\\nutils\\\\adaptive_margin_control.py              301    264    12%   21-26, 29-33, 61-90, 98-113, 123-130, 141-152, 164-176, 188-200, 213-224, 233-242, 255-263, 276-298, 316-369, 387-405, 417-448, 461-464, 476-488, 508-647, 659-689, 701-717, 729-743, 755-771, 783-798\\nutils\\\\adaptive_plutus_system.py               467    374    20%   120-136, 141-164, 168-183, 187-205, 219-252, 256-292, 296-324, 328-382, 394-460, 465-512, 516-581, 586-617, 621-648, 652-667, 687-698, 702-705, 709-733, 737-762, 766-810, 814-890, 894-937, 940\\nutils\\\\advanced_order_test.py                   61     61     0%   7-106\\nutils\\\\advanced_reward_system.py               145    102    30%   72-95, 114-133, 158-194, 198-208, 223-228, 232-255, 278-311, 329-345, 355, 369-370, 374-393, 397\\nutils\\\\advanced_rl_agent.py                    351    351     0%   8-732\\nutils\\\\advanced_technical_indicators.py        428    370    14%   65-67, 71-73, 77-84, 89-98, 103-111, 116-122, 127-134, 139-152, 157-162, 167-171, 175-177, 181-183, 192-209, 220-256, 260-280, 285-289, 294-305, 310-334, 339-347, 352-358, 363-367, 371-383, 389-403, 408-413, 417-424, 429-434, 438-443, 448-454, 458-461, 465-471, 475-485, 489-494, 499-502, 507-512, 517-529, 534-541, 545-554, 558-568, 573-592, 597-603, 607-608, 612, 616-649, 653-719, 723-732, 736, 740, 780-834, 837\\nutils\\\\alpha_beta_attribution.py               308    186    40%   132-162, 183-213, 223-227, 242-264, 268-273, 279-312, 321-327, 350-413, 436-445, 452-453, 461-482, 487, 492, 496, 501-502, 507-508, 517-542, 555-587, 592-608, 626-647\\nutils\\\\anomaly_detection_system.py             357    357     0%   8-858\\nutils\\\\auto_drawdown_control.py                397    269    32%   133-179, 183-190, 196-241, 259-271, 276-278, 282-286, 305-336, 341-350, 367-401, 405-415, 419-424, 428-435, 443-483, 511-518, 526-544, 551-571, 576-586, 611-674, 682-723, 728-739, 786-815, 826-856\\nutils\\\\auto_feature_engineering.py              52     52     0%   1-72\\nutils\\\\auto_hyperparameter_tuning.py           294    294     0%   9-613\\nutils\\\\auto_hyperparameter_tuning_fixed.py     225    225     0%   6-461\\nutils\\\\auto_market_maker.py                    271    241    11%   19-23, 26-28, 65-102, 116-140, 153-159, 171-172, 186-208, 222-237, 248-271, 284, 290-338, 353-432, 441-454, 463-480, 489-492, 507-512, 523-527, 541-652\\nutils\\\\backtesting_framework.py                247    211    15%   27-29, 40-43, 47-50, 54-71, 86-110, 114-122, 126-130, 134-178, 182-204, 211-229, 233-299, 303-316, 320-351, 355-377, 381-385, 390-435, 439-443\\nutils\\\\circuit_breaker_test.py                  71     71     0%   7-124\\nutils\\\\cleanup.py                               10      7    30%   12-23\\nutils\\\\config.py                                 7      4    43%   14-18\\nutils\\\\config_override.py                       30     30     0%   1-35\\nutils\\\\config_validation.py                     14     14     0%   1-16\\nutils\\\\data_cleaning.py                        211    211     0%   2-360\\nutils\\\\data_cleaning_pipeline.py                51     51     0%   1-144\\nutils\\\\data_pipeline.py                         19     19     0%   1-29\\nutils\\\\data_utils.py                           114    102    11%   33-55, 72-127, 143-194, 215-228, 254-260, 264-268\\nutils\\\\database_transaction_test.py             95     95     0%   8-213\\nutils\\\\enhanced_adaptive_plutus.py             240    240     0%   6-444\\nutils\\\\explainable_ai.py                       961    961     0%   1-1877\\nutils\\\\export.py                                12      6    50%   7-8, 12-13, 17-18\\nutils\\\\federated_learning_system.py            226    226     0%   9-523\\nutils\\\\genetic_strategy_evolution.py           408    408     0%   8-870\\nutils\\\\hft_modeling.py                         285    186    35%   96-108, 112-129, 146-189, 194-199, 219-226, 230-257, 261-272, 277-281, 324-369, 389-428, 450-469, 532-550, 554-566, 585-602, 606-608, 634-635, 639-640, 648-657, 667-672\\nutils\\\\hierarchical_rl.py                       25     11    56%   25-27, 31-44, 48\\nutils\\\\integrated_advanced_system.py           260    260     0%   12-534\\nutils\\\\intelligent_memory_system.py            427    427     0%   8-970\\nutils\\\\log_config.py                            14     14     0%   1-29\\nutils\\\\logger.py                                11      0   100%\\nutils\\\\market_regime_detector.py               284    284     0%   6-630\\nutils\\\\memory_manager_test.py                  138    138     0%   8-246\\nutils\\\\mlops_versioning_test.py                 55     55     0%   7-102\\nutils\\\\model_monitoring_test.py                306    306     0%   8-584\\nutils\\\\model_versioning_test.py                245    245     0%   8-469\\nutils\\\\multi_exchange_routing.py               414    289    30%   99-102, 106-114, 118-119, 123-144, 154-184, 188, 192, 226-281, 285, 321-326, 348-373, 401-426, 461-492, 514-539, 559, 563-587, 591-604, 608-617, 621-627, 631, 659-679, 694-734, 763-820, 835-855, 870-895, 905-916, 920, 932-938\\nutils\\\\multi_exchange_test.py                  188    188     0%   8-406\\nutils\\\\multi_step_prediction.py                320    320     0%   9-692\\nutils\\\\multi_step_prediction_fixed.py          252    252     0%   6-474\\nutils\\\\news_volume_analyzer.py                  94     94     0%   1-269\\nutils\\\\offline_sentiment_analyzer.py           128     96    25%   35-36, 43-83, 91-103, 108-111, 115-187, 191-207, 217-221, 228-229, 233-242, 246-267\\nutils\\\\operational_integration.py              112     83    26%   29-30, 34-35, 39-66, 70-101, 105, 115-129, 133-139, 143-150, 154-191, 200-207, 216, 220, 224, 228, 232, 237-253\\nutils\\\\order_manager_test.py                   193    193     0%   8-421\\nutils\\\\plutus_integration.py                   227    187    18%   39-52, 67-95, 109-136, 153-168, 183-218, 230-251, 259-261, 276-296, 308-322, 334-356, 360-377, 381-400, 404-423, 427-444, 448-462, 466-474, 478-486, 490-512, 523-546, 550-551\\nutils\\\\portfolio_visualizer.py                  51     51     0%   1-61\\nutils\\\\proxy_manager.py                         83     64    23%   24-27, 31-46, 50-63, 73-89, 93-107, 111-123, 129-141, 150-152\\nutils\\\\proxy_setup.py                           22     22     0%   1-41\\nutils\\\\realtime_data_test.py                   105    105     0%   7-234\\nutils\\\\reward_redistribution.py                403    272    33%   61, 66, 77, 80, 87-88, 91, 94, 101-102, 105-116, 119, 139-151, 155-161, 165-169, 190-219, 223-249, 254-288, 301-315, 319-323, 327-338, 351-358, 362-380, 384-385, 399-418, 450-455, 460-471, 479-545, 552-566, 570, 588-591, 595-640, 652-653, 659-664, 671-713, 724-766, 789, 791, 795-799, 803-806, 810-815, 824, 832-843\\nutils\\\\risk_manager.py                         179    165     8%   40-56, 81-138, 154-174, 201-239, 261-289, 319-350, 373-408, 429-456, 472-520\\nutils\\\\rl_training_utils.py                    877    751    14%   11-20, 24-33, 37-43, 49-60, 64-66, 71, 76-86, 90-93, 97-103, 106-120, 124-130, 133-135, 138-144, 147-153, 157-167, 170-183, 186-195, 198-206, 209-219, 224-229, 232-243, 246-257, 262-267, 270-279, 284-288, 291-298, 302-307, 310-323, 327-331, 334-342, 346-348, 351-356, 403-446, 449-459, 463-469, 474-483, 487-497, 500-511, 516-522, 526-530, 533-540, 544-555, 560-566, 570-575, 579-589, 596-612, 618-625, 629-635, 644-645, 653, 661-663, 666-671, 674, 677-681, 685-687, 691-693, 701-704, 707-709, 712-718, 721-722, 726-727, 730-731, 739-741, 744-757, 760, 768-772, 775-777, 780-784, 788-791, 794-795, 803-806, 810-813, 816-818, 821-825, 829-834, 842-849, 853-854, 858-864, 868-874, 878-902, 906-918, 922-946, 950-966, 970-980, 984-1017, 1021-1029, 1033-1056, 1060-1074, 1078-1092, 1096-1103, 1113-1121, 1125, 1129-1150, 1154-1167, 1178-1180, 1184-1192, 1196-1214, 1221-1228, 1236-1269, 1276-1277, 1281-1284, 1288, 1292-1296\\nutils\\\\security.py                               7      3    57%   7, 17, 27\\nutils\\\\sentiment_analyzer.py                   602    398    34%   23-25, 55-56, 117-125, 159-163, 184-186, 190-243, 269-271, 275-288, 292-307, 311-322, 335-336, 340-354, 358-368, 372-375, 379-392, 423-438, 447-452, 456-463, 485-489, 493-557, 571, 597-598, 602-616, 620-630, 634-637, 663-686, 691-719, 726-727, 731-753, 764-772, 776-779, 799, 868-870, 874-875, 879-891, 899-982, 998-1059, 1076-1087, 1093-1110, 1114, 1118, 1127, 1131-1133, 1138-1158, 1166-1169, 1174-1182, 1187-1258\\nutils\\\\sentiment_analyzer_old.py               113    113     0%   1-253\\nutils\\\\sentiment_integrator.py                  98     98     0%   1-414\\nutils\\\\simple_backtest_test.py                  91     91     0%   7-156\\nutils\\\\simple_config_test.py                   136    136     0%   7-181\\nutils\\\\simple_database_test.py                 123    123     0%   7-171\\nutils\\\\simple_db_test.py                        43     43     0%   7-79\\nutils\\\\source_credibility.py                    81     81     0%   1-230\\nutils\\\\technical_indicators.py                 109    109     0%   2-176\\nutils\\\\test_all_phase4.py                      128    128     0%   7-198\\nutils\\\\test_fetcher.py                          15     15     0%   1-18\\nutils\\\\test_runner.py                          193    131    32%   40-41, 44, 63, 129-151, 155-177, 181-199, 203-271, 281-299, 303-327, 331-348, 352-369, 373-376, 392, 396, 400, 405-432\\nutils\\\\ultra_simple_test.py                     43     43     0%   6-69\\nutils\\\\zero_shot_learning.py                    19     11    42%   17-18, 22-32, 36\\nverified_working_models.py                    199    199     0%   7-437\\nverify_downloaded_models.py                   132    132     0%   9-324\\nverify_trading_system.py                       50     50     0%   8-91\\nwarning_suppressor.py                          17      0   100%\\n\\u0628\\u0631\\u0631\\u0633\\u06cc_\\u062c\\u0627\\u0645\\u0639_\\u0645\\u0627\\u0698\\u0648\\u0644_\\u0647\\u0627.py                        188    188     0%   8-371\\n\\u062a\\u0633\\u062a_\\u06a9\\u0627\\u0645\\u0644_\\u0686\\u0647\\u0627\\u0631_\\u0645\\u0648\\u0631\\u062f.py                         151    151     0%   5-273\\n\\u06a9\\u0627\\u0631\\u0647\\u0627\\u06cc_\\u0646\\u0627\\u0642\\u0635.py                                142    142     0%   8-921\\n-------------------------------------------------------------------------\\nTOTAL                                       45510  39435    13%\\nFAIL Required test coverage of 80% not reached. Total coverage: 13.35%\n============================= 45 passed in 9.06s ==============================\n", "error": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pytest_asyncio\\plugin.py:208: PytestDeprecationWarning: The configuration option \"asyncio_default_fixture_loop_scope\" is unset.\nThe event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: \"function\", \"class\", \"module\", \"package\", \"session\"\n\n  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))\n", "timestamp": "2025-07-14T22:25:38.655930"}, {"name": "test_smart_portfolio_manager.py", "passed": false, "duration": 64.79497480392456, "output": "============================= test session starts =============================\nplatform win32 -- Python 3.9.0, pytest-8.3.5, pluggy-1.5.0 -- C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe\ncachedir: .pytest_cache\nrootdir: D:\\project\nconfigfile: pytest.ini\nplugins: anyio-4.9.0, asyncio-1.0.0, cov-6.2.1, mock-3.14.1\nasyncio: mode=strict, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... collected 29 items\n\ntests/test_smart_portfolio_manager.py::TestProfitTarget::test_profit_target_creation PASSED [  3%]\ntests/test_smart_portfolio_manager.py::TestProfitTarget::test_profit_target_achieved PASSED [  6%]\ntests/test_smart_portfolio_manager.py::TestTradingOpportunity::test_trading_opportunity_creation PASSED [ 10%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_initialization PASSED [ 13%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_default_initialization PASSED [ 17%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_add_trading_signal_success FAILED [ 20%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_add_trading_signal_failure PASSED [ 24%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_analyze_signal_success PASSED [ 27%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_analyze_signal_low_confidence PASSED [ 31%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_analyze_signal_cannot_open_position PASSED [ 34%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_analyze_signal_zero_position_size PASSED [ 37%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_calculate_priority_high PASSED [ 41%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_calculate_priority_low PASSED [ 44%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_execute_best_opportunity_success FAILED [ 48%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_execute_best_opportunity_no_opportunities PASSED [ 51%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_execute_best_opportunity_cannot_open PASSED [ 55%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_execute_best_opportunity_failed_execution PASSED [ 58%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_check_profit_targets_daily_achieved PASSED [ 62%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_check_profit_targets_not_achieved PASSED [ 65%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_adjust_trading_strategy_high_risk FAILED [ 68%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_adjust_trading_strategy_target_close PASSED [ 72%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_adjust_trading_strategy_behind_target PASSED [ 75%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_monitor_positions_stop_loss_buy PASSED [ 79%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_start_auto_trading PASSED [ 82%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_stop_auto_trading PASSED [ 86%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_get_comprehensive_report PASSED [ 89%]\ntests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_save_comprehensive_state PASSED [ 93%]\ntests/test_smart_portfolio_manager.py::TestIntegrationScenarios::test_signal_to_execution_flow PASSED [ 96%]\ntests/test_smart_portfolio_manager.py::TestIntegrationScenarios::test_multiple_signals_prioritization FAILED [100%]\nERROR: Coverage failure: total of 14 is less than fail-under=80\n\n\n================================== FAILURES ===================================\n__________ TestSmartPortfolioManager.test_add_trading_signal_success __________\ntests\\test_smart_portfolio_manager.py:134: in test_add_trading_signal_success\n    assert success == True\nE   assert False == True\n---------------------------- Captured stdout call -----------------------------\n\\U0001f4ca New trading opportunity: EURUSD\\n   Action: buy\\n   Confidence: 0.85\\n   Priority: 1\\n\\u274c Error adding signal: unsupported format string passed to Mock.__format__\n_______ TestSmartPortfolioManager.test_execute_best_opportunity_success _______\ntests\\test_smart_portfolio_manager.py:303: in test_execute_best_opportunity_success\n    success = self.portfolio_manager.execute_best_opportunity()\nportfolio\\smart_portfolio_manager.py:247: in execute_best_opportunity\n    self._update_performance_stats(best_opportunity)\nportfolio\\smart_portfolio_manager.py:261: in _update_performance_stats\n    new_avg = ((current_avg * (total_signals - 1)) + opportunity.confidence_score) / total_signals\nE   ZeroDivisionError: float division by zero\n______ TestSmartPortfolioManager.test_adjust_trading_strategy_high_risk _______\ntests\\test_smart_portfolio_manager.py:426: in test_adjust_trading_strategy_high_risk\n    assert self.portfolio_manager.aggressive_mode == False\nE   assert True == False\nE    +  where True = <portfolio.smart_portfolio_manager.SmartPortfolioManager object at 0x0000015DB6467550>.aggressive_mode\nE    +    where <portfolio.smart_portfolio_manager.SmartPortfolioManager object at 0x0000015DB6467550> = <test_smart_portfolio_manager.TestSmartPortfolioManager object at 0x0000015D85620E80>.portfolio_manager\n---------------------------- Captured stdout call -----------------------------\n\\u26a0\\ufe0f Switching to conservative mode due to high risk level\\n\\U0001f680 Switching to aggressive mode - behind on daily target\n________ TestIntegrationScenarios.test_multiple_signals_prioritization ________\ntests\\test_smart_portfolio_manager.py:590: in test_multiple_signals_prioritization\n    assert first_opportunity.priority <= 2  # Should be high priority due to high confidence\nE   AssertionError: assert 3 <= 2\nE    +  where 3 = TradingOpportunity(symbol='EURUSD', signal=TradingSignal(symbol='EURUSD', action='buy', confidence=0.95, price=1.085, timestamp=datetime.datetime(2025, 7, 14, 22, 26, 36, 5854), reasoning='High conf'), risk_reward_ratio=0.7500000000000062, position_size=1000.0, expected_profit=15.000000000000124, confidence_score=0.95, priority=3).priority\n---------------------------- Captured stdout call -----------------------------\n\\U0001f4ca New trading opportunity: EURUSD\\n   Action: buy\\n   Confidence: 0.95\\n   Priority: 3\\n   Expected Profit: $15.00\\n\\U0001f4ca New trading opportunity: GBPUSD\\n   Action: buy\\n   Confidence: 0.75\\n   Priority: 5\\n   Expected Profit: $-165.00\\n\\U0001f4ca New trading opportunity: USDJPY\\n   Action: buy\\n   Confidence: 0.85\\n   Priority: 4\\n   Expected Profit: $-149400.00\n=============================== tests coverage ================================\n_______________ coverage: platform win32, python 3.9.0-final-0 ________________\\n\\nName                                        Stmts   Miss  Cover   Missing\\n-------------------------------------------------------------------------\\nactivate_inactive_modules.py                  137    137     0%   8-347\\nadvanced_system_diagnostics.py                436    436     0%   8-769\\nadvanced_system_diagnostics_simple.py         408    408     0%   8-703\\nai_brain_fix.py                                12     12     0%   7-41\\nai_models\\\\__init__.py                         515    337    35%   17-19, 46-55, 62-64, 84, 115-117, 121-124, 128, 132, 136-141, 145, 166, 170, 179, 189, 209, 214-217, 222-229, 234-243, 247-254, 258, 267, 271, 297, 302-305, 310-317, 321, 331-334, 338-344, 348-384, 393-398, 402, 438-440, 444, 457-462, 466-480, 484-488, 497, 508-511, 515-523, 528-532, 545, 555-558, 562-570, 574-578, 589, 599-602, 606-614, 618-622, 633, 643-646, 650-657, 661-664, 668, 678-681, 685-692, 696-700, 704, 714-717, 721-728, 732-735, 739-742, 746, 756-759, 763-770, 774-777, 790, 800-925, 945-949, 953-957, 961-962, 966, 970, 974\\nai_models\\\\huggingface_models.py               278    278     0%   8-544\\nai_models\\\\model_manager.py                    307    307     0%   8-582\\nai_models\\\\sentiment_models.py                  83     63    24%   17-21, 25-35, 39-54, 58, 68-72, 76-86, 90-105, 109, 119-123, 127-137, 141-156, 160\\napi\\\\__init__.py                               266    141    47%   61, 71, 125-127, 131-145, 149-163, 167, 200-210, 235-237, 263, 276, 280-301, 307, 311-317, 325-328, 332-354, 358-379, 384, 394-406, 415-427, 436-470, 479-498, 507-532, 542-563, 569-574, 578-590, 622-624\\napi\\\\endpoints.py                              138     87    37%   73-75, 79, 90-334, 338-349, 353-355\\napi\\\\realtime_dashboard.py                     336    185    45%   109-111, 115-117, 121-126, 130-141, 145, 172-258, 262-274, 278, 290-320, 329, 340-342, 359-363, 367-377, 381-397, 411-464, 469, 474, 507, 512-525, 530-535, 540, 545-553, 558-559, 564-565, 570-571, 576-577, 582-583, 588, 601-610, 615, 620-638, 643-652, 658-678, 687-698, 705-709, 713, 1006-1007\\napi\\\\server.py                                 100    100     0%   8-196\\nbakap.py                                       72     72     0%   1-122\\nclean_cache.py                                 17     17     0%   1-21\\ncomplete_debug_test.py                        100    100     0%   7-161\\ncomplete_model_audit.py                       166    166     0%   9-417\\ncomplete_module_test.py                       161    161     0%   8-278\\ncomprehensive_system_check.py                 126    126     0%   7-202\\ncomprehensive_system_test.py                  173    173     0%   7-282\\ncore\\\\__init__.py                              345    239    31%   83-88, 101-114, 126-131, 144-151, 163-169, 184-193, 212-225, 245, 296-325, 352-373, 395-411, 432-447, 469, 508-523, 534-539, 551-557, 719-732, 736-774, 778-816\\ncore\\\\advanced_config.py                       274    274     0%   8-401\\ncore\\\\advanced_order_management.py             368    224    39%   38-39, 140-150, 154, 158-160, 164, 173-186, 207, 211-212, 216-218, 222-231, 237-241, 245-271, 277-300, 305-332, 336-360, 364-375, 379-409, 414-449, 454-455, 459-483, 487-508, 512-530, 534-550, 554-555, 559, 563-568, 572, 576, 580-587, 600, 604, 614-617, 623-626, 631, 636, 640-657\\ncore\\\\advanced_risk_metrics.py                 300    213    29%   92, 146-231, 235-279, 283, 296, 310, 314-315, 319-320, 324-327, 331-333, 337-355, 359, 363-364, 368-369, 373-381, 386-394, 398-403, 407-416, 421-432, 437-438, 443-475, 479-495, 499-523, 527-545, 553, 557, 561, 565-591\\ncore\\\\backtesting_framework.py                 156     84    46%   62-63, 67, 73-75, 79-89, 102-111, 122-124, 142-147, 154-170, 174-187, 191-219, 231-232, 247, 251, 259-285\\ncore\\\\base.py                                  235    150    36%   29, 51, 76, 81, 86, 91, 95, 106-107, 118-129, 133, 137, 141-157, 161-174, 178-189, 193-205, 211-213, 217-230, 234-250, 254-270, 274-285, 300-301, 305, 317-326, 331, 336, 340-353, 357-384, 388-406, 410, 422\\ncore\\\\circuit_breaker_system.py                109     70    36%   102-105, 111-128, 131, 137-141, 144-150, 153-155, 161-163, 169, 179, 190-192, 197-205, 213-227, 235-249\\ncore\\\\config.py                                158     47    70%   93, 126-127, 131-133, 137-150, 191-198, 204-205, 213-218, 229-254\\ncore\\\\configuration_management.py              467    258    45%   36, 46-47, 50, 53, 61, 68, 108, 132-134, 138-140, 158-160, 164-166, 187-189, 193-195, 199-203, 219-222, 235-237, 250-252, 291-309, 314, 319, 322, 326-331, 336-343, 375-409, 413-419, 423-441, 449-471, 475-480, 484-497, 501-504, 508-517, 521-529, 533-541, 545-551, 555-578, 582-587, 591-592, 596-598, 602-606, 610, 614-626, 630-646, 651-663, 667-681, 685-690, 694-697, 701-704, 712, 716, 720, 724, 728, 732, 736, 740-775\\ncore\\\\correlation_analysis.py                  336    246    27%   70, 74-80, 85-88, 101, 105-119, 165-220, 241-294, 311-375, 392-465, 480-525, 542-600, 621-672, 677, 689, 704, 710, 714, 718, 723-756\\ncore\\\\database_manager.py                      332    332     0%   8-583\\ncore\\\\database_transaction_manager.py          324    192    41%   36-39, 141-154, 158-192, 196-197, 201-202, 206-210, 214-218, 229-234, 240-244, 248-250, 259-337, 341-357, 361-374, 378-401, 427-430, 434-449, 453-457, 461, 465-475, 479-489, 493, 502-504, 514-518, 524-527, 532, 536, 540, 544-568\\ncore\\\\enhanced_error_handling.py               314    170    46%   41-45, 48, 134, 179-196, 200-202, 206-210, 214-219, 223, 240-257, 265-284, 289-291, 310-315, 346-369, 373-377, 381-382, 389, 396-407, 417-438, 443-460, 464-473, 477-486, 491-495, 519, 523, 527, 531-573\\ncore\\\\error_handler.py                         240    131    45%   96-101, 105-118, 122-125, 129-130, 134-138, 177-195, 202-220, 224-225, 229-242, 246-250, 254-259, 263-274, 278-291, 303-308, 312, 328-329, 333-338, 342-368, 375-378, 390-404, 409-415, 421-425, 430, 436, 444-463\\ncore\\\\exceptions.py                             19      0   100%\\ncore\\\\logger.py                                 87     30    66%   43, 46, 106, 108, 115-122, 126-133, 137-144, 158-164\\ncore\\\\memory_manager.py                        266    122    54%   60, 85-91, 95-105, 109-112, 123-126, 172, 180-181, 238, 242, 246-273, 302, 304, 306, 317, 321-322, 328-354, 358-360, 364, 371, 384-385, 389-444, 449, 457-459, 467-468, 472-477, 481-484, 512-525, 529-532, 537-565\\ncore\\\\mlops_versioning.py                      100     66    34%   26-28, 90-111, 116-131, 136-150, 158-161, 165-175, 184, 187, 190\\ncore\\\\model_monitoring.py                      502    368    27%   77-81, 95, 120, 150, 155-181, 186-215, 221-223, 230-245, 250-294, 299-309, 313-327, 331-347, 356-378, 382-404, 410-440, 444-449, 453-455, 462-479, 483-524, 530-561, 565-587, 591-613, 617-648, 652-669, 673-679, 683-685, 689-691, 695-706, 721-754, 758, 762, 775-790, 794-795, 799-804, 808-813, 817-829, 839-844, 852-858, 863-938, 941\\ncore\\\\model_versioning.py                      441    275    38%   83-85, 111-119, 132, 136, 140-148, 156-178, 183-202, 224, 232-236, 243, 265-267, 295-296, 301-343, 348-369, 373-392, 397-409, 413-425, 429-441, 445-469, 473-479, 483-522, 526-549, 553-570, 585-593, 607-615, 619-622, 626-627, 631-645, 649-652, 660-744, 747\\ncore\\\\multi_exchange.py                        542    389    28%   70-73, 93-111, 125, 130, 135, 140, 145, 150, 155, 159-170, 174-176, 180-182, 186-190, 203, 207, 211, 215-219, 223-227, 231-235, 241-245, 249-271, 275-284, 288, 292-327, 331-360, 364-371, 376, 382-386, 390-409, 413-422, 426, 430-464, 468-496, 500-506, 510, 544-569, 573-586, 590-602, 607-623, 627-647, 651-654, 658-668, 672-677, 682-691, 695, 699, 703-713, 718-722, 726-729, 740-749, 753-755, 759-770, 785-797, 808-815, 819-836, 844-848, 853-923, 926\\ncore\\\\order_manager.py                         337    254    25%   57-104, 109-121, 172-212, 216-231, 235-251, 257-303, 307-382, 386-390, 394-398, 402, 406-409, 413, 417, 421-424, 428-431, 435-440, 444, 448, 452-505, 523-525, 529, 532, 535, 538-540, 548-549, 557-571, 576-636\\ncore\\\\performance_optimizer.py                 261    261     0%   8-575\\ncore\\\\realtime_data_processing.py              378    247    35%   78-79, 113-117, 122, 126, 130, 136-139, 143-170, 174-192, 196-203, 207-224, 228, 232, 236-237, 243-255, 266-276, 280-283, 287-305, 309-329, 333-347, 351-365, 369-381, 385-386, 390-391, 395-397, 401, 413-421, 432-442, 446-458, 462-470, 474-478, 482-489, 493-502, 506-517, 522, 526-530, 534-536, 540, 547-549, 553, 566-568, 573-575, 580-585, 596-599, 610-668, 671\\ncore\\\\shared_types.py                           79      0   100%\\ncore\\\\simple_config.py                         173     69    60%   61-70, 85-87, 99-103, 158, 163-174, 179-198, 202-226, 230, 234, 252, 257-258, 262, 266-267, 271-284\\ncore\\\\simple_database_manager.py               274    210    23%   60-64, 68-79, 83-95, 99-106, 112-116, 120-122, 128-189, 193-204, 217-223, 284-301, 307-324, 330-367, 371-399, 403-412, 416-441, 445-471, 475, 479-483, 490, 496-501, 507-509, 513-556\\ncore\\\\utils.py                                 372    282    24%   38-50, 55-67, 71-75, 79-82, 86-90, 94-100, 104-107, 138-141, 159-161, 165, 176-178, 188-193, 205-218, 222-229, 233-252, 268-304, 316-321, 334-348, 361-374, 387-400, 412-441, 454-470, 485-488, 492-504, 513-565, 581-599, 609-629, 643-656, 671-676, 690-693, 707-712, 722-739, 745-766\\ncvxpy_fix.py                                   17     17     0%   7-37\\ndata\\\\__init__.py                                0      0   100%\\ndata\\\\fetcher.py                                25     25     0%   1-38\\ndata\\\\import sys.py                              7      7     0%   1-12\\ndata\\\\preprocessor.py                           43     43     0%   1-76\\ndata\\\\test_fetcher.py                            7      7     0%   1-12\\ndebug_fitness_issue.py                         63     63     0%   6-124\\ndownload_and_test_models.py                   183    183     0%   9-331\\ndownload_hf_model.py                            0      0   100%\\nencoding_config.py                              9      9     0%   7-20\\nenhanced_financial_models.py                  356    356     0%   16-731\\nenhanced_spacy_mock.py                        116    116     0%   7-183\\nenv\\\\__init__.py                               194    136    30%   39-59, 63-76, 80-86, 90-103, 107, 120-134, 138-140, 144-146, 151-160, 173, 177-179, 184-202, 207-212, 216-231, 235-250, 254-261, 266-271, 275-292, 297-304, 308, 318-319, 323, 337-342, 346-347, 351-353, 358, 372-375, 381, 427-429\\nenv\\\\portfolio.py                               16     13    19%   6-23\\nenv\\\\trading_env.py                            229    201    12%   18-19, 29-102, 105-129, 133-192, 199-224, 227-292, 296-305, 308-476, 479, 482, 485, 489-492, 505-506\\nevaluation\\\\__init__.py                        251    151    40%   72, 115-117, 121-127, 131-141, 145, 160-203, 207-214, 218-230, 238-256, 274-324, 333-370, 379-408, 417-453, 484-495, 499-522, 534, 538, 542, 588-590\\nevaluation\\\\comparison.py                      185    138    25%   35-36, 53-55, 65-66, 73-143, 149-161, 167-207, 211-240, 244-263, 267-282, 288-289, 300-318, 326-345, 349, 388-408, 411\\nevaluation\\\\metrics.py                          35     33     6%   16-70\\nevaluation\\\\scenario_backtesting.py            354    324     8%   26-29, 33, 54-81, 98-126, 138-169, 186-238, 255-325, 339-361, 373-400, 410-466, 477-491, 502-519, 530-542, 553-567, 578-591, 602-621, 632-644, 655-668, 682-711\\nevaluation\\\\tests.py                             0      0   100%\\nexamples\\\\plutus_api_example.py                212    190    10%   28-32, 41-66, 75-105, 112-147, 154-200, 209-210, 220-264, 271-306, 313-392, 401-432, 436-449\\nexamples\\\\plutus_integration_final.py          198    166    16%   54-110, 117-171, 178-222, 229-288, 295-323, 330-343, 350-396, 399\\nexecution_pipeline.py                         315    239    24%   88-98, 102-108, 112-119, 123, 136-166, 170-175, 179, 247-300, 312-344, 352-388, 396-476, 484-541, 549-581, 589-619, 627-656, 664-667, 691-710, 714-729, 744-750\\nexpert_system_analysis.py                     324    324     0%   8-580\\nfinal_complete_fix_test.py                    229    229     0%   8-378\\nfinal_complete_test.py                         45     45     0%   7-71\\nfinal_real_models_system.py                   277    277     0%   16-569\\nfinal_summary.py                               40     40     0%   6-52\\nfinal_system_analysis.py                      111    111     0%   6-262\\nfinal_system_test.py                           24     24     0%   7-46\\nfinancial_models_free_usage.py                244    244     0%   16-441\\nfix_all_minor_issues.py                        92     92     0%   8-215\\nfix_all_warnings.py                           113    113     0%   8-373\\nfix_broken_non_test_files.py                  154    154     0%   8-2213\\nfix_clarabel_solver.py                         63     63     0%   7-115\\nfix_comprehensive_system_test.py              228    228     0%   7-396\\nfix_cursor_proxy.py                           129    129     0%   6-239\\nfix_encoding_issues.py                         66     66     0%   8-133\\nfix_final_issues.py                            98     98     0%   8-392\\nfix_import_issues.py                           79     79     0%   8-157\\nfix_inactive_modules.py                        69     69     0%   8-266\\nfix_main_system.py                            143    143     0%   8-367\\nfix_memory_issues.py                           78     78     0%   9-156\\nfix_multi_exchange.py                          84     84     0%   8-260\\nfix_persian_model.py                           79     79     0%   8-287\\nfix_real_issues.py                             97     97     0%   8-367\\nfix_remaining_issues.py                       138    138     0%   8-504\\nfix_spacy_models.py                            86     86     0%   8-342\\nfunctional_test.py                            127    127     0%   5-205\\nhuggingface_fix.py                             19     19     0%   7-36\\nimmediate_test.py                             145    145     0%   7-239\\ninstall_and_test_ai_models.py                 181    181     0%   9-331\\ninstall_real_models.py                         36     36     0%   6-75\\ninstall_simple.py                              28     28     0%   10-53\\ninstall_spacy_models.py                        54     54     0%   7-111\\nintegrated_trading_system.py                  222    222     0%   12-456\\nintegration_summary.py                         84     84     0%   7-131\\nllma.py                                       149    149     0%   8-356\\nlogging_config.py                              30     30     0%   7-63\\nmain.py                                        19     19     0%   9-35\\nmain_new.py                                   471    378    20%   37-38, 161-163, 181-183, 192-194, 234-241, 249-268, 272-284, 288-393, 397-417, 421-428, 432-439, 443-450, 454-461, 465-491, 495-532, 536-563, 567-659, 663-688, 692-721, 725-771, 780-823, 828-865, 869\\nmodel_usage_examples.py                        27     27     0%   7-56\\nmodels\\\\__init__.py                            166    104    37%   113-122, 126-133, 137-158, 162-193, 197, 201-204, 216-230, 234-247, 251-265, 274-281, 285, 289, 293, 376-378\\nmodels\\\\ai_agent.py                            466    371    20%   105-144, 152-154, 158, 186-208, 212-227, 231-238, 243-248, 253-258, 263-268, 272-321, 325-346, 350-368, 372-416, 420-444, 448-464, 470-510, 517-561, 565-572, 576-583, 587-609, 613-628, 634-658, 665-785, 799-831, 836-851, 855-871, 875-908, 912-944, 949\\nmodels\\\\continual_learning.py                  245    197    20%   39-43, 55-107, 118-129, 145-154, 175-181, 194-198, 214-217, 223-227, 234-250, 263-264, 267, 312-339, 350-367, 377, 390-391, 409-419, 435-454, 467-478, 498-511, 522-534, 545-557, 569-594, 615-653, 661-663, 667-668, 672-678, 682-686, 690-695\\nmodels\\\\ensemble_model.py                      253    228    10%   65-99, 110-136, 153-211, 230-275, 282-332, 348-378, 389, 401-437, 456-516, 522-539, 545-571\\nmodels\\\\hierarchical_rl.py                     243    243     0%   15-552\\nmodels\\\\meta_learner.py                        184    184     0%   1-431\\nmodels\\\\rl_models.py                           109     87    20%   21-78, 84-114, 116, 134, 140-145, 180-181, 195, 213, 228, 243, 252, 267, 284, 298-302, 305, 319, 331\\nmodels\\\\unified_trading_system.py              632    632     0%   14-1512\\nmodels\\\\zero_shot_learning.py                  298    298     0%   1-815\\nmodule_analysis.py                            202    202     0%   8-394\\noptimization\\\\__init__.py                      279    192    31%   62, 103-105, 109-115, 119-126, 130, 146-198, 217-257, 267-313, 323-356, 366-399, 408-437, 443-459, 463-492, 497-540, 552, 585-587\\noptimization\\\\bayesian.py                      119    100    16%   25-40, 50-73, 79, 99-150, 160-169, 179-200, 207-221, 227-232, 247-249, 255-267\\noptimization\\\\genetic.py                        81     70    14%   16-26, 32-74, 83-94, 99-110, 114-135, 139-155\\noptimization\\\\pso.py                           185    185     0%   8-385\\npersian_sentiment_fallback.py                  57     37    35%   47-90, 99-114, 121-122, 130\\nph3_optimizer.py                              297    297     0%   1-567\\nportfolio\\\\__init__.py                         298    204    32%   51-58, 62, 66-76, 94-95, 99, 103-105, 111-144, 148-158, 162-168, 172-185, 189, 205-212, 217-233, 239-270, 274-337, 341-343, 347-354, 358-371, 375, 379, 383-385, 389-395, 399-413, 417, 435-449, 453-454, 458-460, 465-467, 471-473, 477-481, 488, 495, 499, 503, 507, 511, 520-523, 567-569\\nportfolio\\\\advanced_risk_manager.py            250    140    44%   65, 83-85, 107, 139-161, 171-172, 190, 194, 198, 202, 206, 209, 212, 220-242, 248-301, 306-375, 379-386, 394, 396, 398, 413, 465-505, 510-550\\nportfolio\\\\portfolio_manager.py                157    131    17%   14-15, 24-52, 63-137, 147-165, 170-191, 209-250, 271-315, 325-357, 369, 373, 377-380, 384, 388, 392-395, 413\\nportfolio\\\\smart_portfolio_manager.py          259     60    77%   146, 183, 185, 198, 200, 206, 288-291, 299-302, 345-346, 348-349, 353-354, 356-357, 362-385, 493-547\\npractical_model_test.py                       164    164     0%   9-362\\nprofit_target_demo.py                         230    230     0%   8-461\\nproject_analyzer.py                           149    149     0%   8-285\\nquick_complete_system_test.py                 195    195     0%   8-302\\nquick_debug_test.py                            31     31     0%   6-45\\nquick_final_test.py                           141    141     0%   7-235\\nquick_fix_main.py                              15     15     0%   7-41\\nquick_reality_check.py                         94     94     0%   6-141\\nquick_status_check.py                          44     44     0%   5-66\\nquick_test.py                                  74     74     0%   7-108\\nreal_huggingface_models.py                     40     40     0%   16-306\\nrun_comprehensive_tests.py                     82     82     0%   6-206\\nrun_sentiment_tests.py                         51     51     0%   1-105\\nrun_tests.py                                   44     44     0%   9-84\\nsentiment_integration.py                      211    211     0%   9-364\\nsetup_financial_models.py                     163    163     0%   10-390\\nsimple_debug_test.py                           25     25     0%   6-37\\nsimple_financial_models.py                    253    253     0%   14-486\\nsimple_model_test.py                           39     39     0%   7-69\\nsimple_profit_demo.py                         187    187     0%   8-350\\nsimple_system_test.py                         144    144     0%   6-287\\nsimple_test.py                                 77     77     0%   6-120\\nsystem_dashboard.py                            91     91     0%   7-148\\nsystem_status.py                               58     58     0%   7-90\\ntest_advanced_risk_metrics.py                  82     82     0%   7-143\\ntest_ai_agent.py                               70     70     0%   6-133\\ntest_ai_agent_integration.py                   46     46     0%   11-102\\ntest_all_fixes.py                              76     76     0%   7-134\\ntest_backtesting_integration.py                96     96     0%   8-194\\ntest_complete_system.py                        83     83     0%   7-134\\ntest_connection_no_proxy.py                    35     35     0%   6-59\\ntest_fetcher.py                                 2      2     0%   1-3\\ntest_final_integration.py                     306    306     0%   8-656\\ntest_genetic_algorithm_deep.py                128    128     0%   6-319\\ntest_hierarchical_simple.py                    23     23     0%   8-43\\ntest_integrated_system.py                     187    187     0%   15-368\\ntest_integration.py                           228    228     0%   10-405\\ntest_integration_final.py                     275    275     0%   13-483\\ntest_integration_fixed.py                     243    243     0%   12-428\\ntest_integration_v2.py                        370    370     0%   9-630\\ntest_local_only.py                             41     41     0%   6-80\\ntest_models_simple.py                          55     55     0%   6-84\\ntest_offline.py                                 0      0   100%\\ntest_persian_improved.py                       38     38     0%   4-73\\ntest_ray.py                                     8      8     0%   1-9\\ntest_real_models.py                           170    170     0%   8-382\\ntest_real_models_with_proxy.py                196    196     0%   9-437\\ntest_sentiment_complete.py                     32     32     0%   4-63\\ntest_simple_models.py                          87     87     0%   7-183\\ntest_unified_system.py                        199    199     0%   6-515\\ntest_with_http_proxy.py                        65     65     0%   7-118\\ntest_with_token.py                             81     81     0%   7-190\\ntests\\\\conftest.py                             320    215    33%   43-45, 77-78, 83-84, 91, 96-98, 103, 108-115, 122-127, 132-137, 144-151, 156-164, 169-175, 182-195, 200-212, 219-237, 242-261, 268-280, 287-299, 306-317, 322-339, 346-356, 361-372, 379-382, 387-393, 400, 421, 441-472, 479, 501, 504, 519, 523-527, 531-536, 540-542\\ntests\\\\test_plutus_models_comprehensive.py     342    306    11%   62-103, 110-181, 188-301, 308-359, 366-428, 435-503, 510-578, 586-599, 606-621, 625-674\\ntests\\\\test_smart_portfolio_manager.py         256      5    98%   135-136, 305-306, 594\\nultimate_final_test.py                        143    143     0%   8-206\\nutils\\\\__init__.py                              96     33    66%   109, 117, 128-133, 138-143, 148-152, 158, 162, 166, 172, 176, 180, 184-189, 274-276\\nutils\\\\adaptive_margin_control.py              301    264    12%   21-26, 29-33, 61-90, 98-113, 123-130, 141-152, 164-176, 188-200, 213-224, 233-242, 255-263, 276-298, 316-369, 387-405, 417-448, 461-464, 476-488, 508-647, 659-689, 701-717, 729-743, 755-771, 783-798\\nutils\\\\adaptive_plutus_system.py               467    374    20%   120-136, 141-164, 168-183, 187-205, 219-252, 256-292, 296-324, 328-382, 394-460, 465-512, 516-581, 586-617, 621-648, 652-667, 687-698, 702-705, 709-733, 737-762, 766-810, 814-890, 894-937, 940\\nutils\\\\advanced_order_test.py                   61     61     0%   7-106\\nutils\\\\advanced_reward_system.py               145    102    30%   72-95, 114-133, 158-194, 198-208, 223-228, 232-255, 278-311, 329-345, 355, 369-370, 374-393, 397\\nutils\\\\advanced_rl_agent.py                    351    351     0%   8-732\\nutils\\\\advanced_technical_indicators.py        428    370    14%   65-67, 71-73, 77-84, 89-98, 103-111, 116-122, 127-134, 139-152, 157-162, 167-171, 175-177, 181-183, 192-209, 220-256, 260-280, 285-289, 294-305, 310-334, 339-347, 352-358, 363-367, 371-383, 389-403, 408-413, 417-424, 429-434, 438-443, 448-454, 458-461, 465-471, 475-485, 489-494, 499-502, 507-512, 517-529, 534-541, 545-554, 558-568, 573-592, 597-603, 607-608, 612, 616-649, 653-719, 723-732, 736, 740, 780-834, 837\\nutils\\\\alpha_beta_attribution.py               308    186    40%   132-162, 183-213, 223-227, 242-264, 268-273, 279-312, 321-327, 350-413, 436-445, 452-453, 461-482, 487, 492, 496, 501-502, 507-508, 517-542, 555-587, 592-608, 626-647\\nutils\\\\anomaly_detection_system.py             357    357     0%   8-858\\nutils\\\\auto_drawdown_control.py                397    269    32%   133-179, 183-190, 196-241, 259-271, 276-278, 282-286, 305-336, 341-350, 367-401, 405-415, 419-424, 428-435, 443-483, 511-518, 526-544, 551-571, 576-586, 611-674, 682-723, 728-739, 786-815, 826-856\\nutils\\\\auto_feature_engineering.py              52     52     0%   1-72\\nutils\\\\auto_hyperparameter_tuning.py           294    294     0%   9-613\\nutils\\\\auto_hyperparameter_tuning_fixed.py     225    225     0%   6-461\\nutils\\\\auto_market_maker.py                    271    241    11%   19-23, 26-28, 65-102, 116-140, 153-159, 171-172, 186-208, 222-237, 248-271, 284, 290-338, 353-432, 441-454, 463-480, 489-492, 507-512, 523-527, 541-652\\nutils\\\\backtesting_framework.py                247    211    15%   27-29, 40-43, 47-50, 54-71, 86-110, 114-122, 126-130, 134-178, 182-204, 211-229, 233-299, 303-316, 320-351, 355-377, 381-385, 390-435, 439-443\\nutils\\\\circuit_breaker_test.py                  71     71     0%   7-124\\nutils\\\\cleanup.py                               10      7    30%   12-23\\nutils\\\\config.py                                 7      4    43%   14-18\\nutils\\\\config_override.py                       30     30     0%   1-35\\nutils\\\\config_validation.py                     14     14     0%   1-16\\nutils\\\\data_cleaning.py                        211    211     0%   2-360\\nutils\\\\data_cleaning_pipeline.py                51     51     0%   1-144\\nutils\\\\data_pipeline.py                         19     19     0%   1-29\\nutils\\\\data_utils.py                           114    102    11%   33-55, 72-127, 143-194, 215-228, 254-260, 264-268\\nutils\\\\database_transaction_test.py             95     95     0%   8-213\\nutils\\\\enhanced_adaptive_plutus.py             240    240     0%   6-444\\nutils\\\\explainable_ai.py                       961    961     0%   1-1877\\nutils\\\\export.py                                12      6    50%   7-8, 12-13, 17-18\\nutils\\\\federated_learning_system.py            226    226     0%   9-523\\nutils\\\\genetic_strategy_evolution.py           408    408     0%   8-870\\nutils\\\\hft_modeling.py                         285    186    35%   96-108, 112-129, 146-189, 194-199, 219-226, 230-257, 261-272, 277-281, 324-369, 389-428, 450-469, 532-550, 554-566, 585-602, 606-608, 634-635, 639-640, 648-657, 667-672\\nutils\\\\hierarchical_rl.py                       25     11    56%   25-27, 31-44, 48\\nutils\\\\integrated_advanced_system.py           260    260     0%   12-534\\nutils\\\\intelligent_memory_system.py            427    427     0%   8-970\\nutils\\\\log_config.py                            14     14     0%   1-29\\nutils\\\\logger.py                                11      0   100%\\nutils\\\\market_regime_detector.py               284    284     0%   6-630\\nutils\\\\memory_manager_test.py                  138    138     0%   8-246\\nutils\\\\mlops_versioning_test.py                 55     55     0%   7-102\\nutils\\\\model_monitoring_test.py                306    306     0%   8-584\\nutils\\\\model_versioning_test.py                245    245     0%   8-469\\nutils\\\\multi_exchange_routing.py               414    289    30%   99-102, 106-114, 118-119, 123-144, 154-184, 188, 192, 226-281, 285, 321-326, 348-373, 401-426, 461-492, 514-539, 559, 563-587, 591-604, 608-617, 621-627, 631, 659-679, 694-734, 763-820, 835-855, 870-895, 905-916, 920, 932-938\\nutils\\\\multi_exchange_test.py                  188    188     0%   8-406\\nutils\\\\multi_step_prediction.py                320    320     0%   9-692\\nutils\\\\multi_step_prediction_fixed.py          252    252     0%   6-474\\nutils\\\\news_volume_analyzer.py                  94     94     0%   1-269\\nutils\\\\offline_sentiment_analyzer.py           128     96    25%   35-36, 43-83, 91-103, 108-111, 115-187, 191-207, 217-221, 228-229, 233-242, 246-267\\nutils\\\\operational_integration.py              112     83    26%   29-30, 34-35, 39-66, 70-101, 105, 115-129, 133-139, 143-150, 154-191, 200-207, 216, 220, 224, 228, 232, 237-253\\nutils\\\\order_manager_test.py                   193    193     0%   8-421\\nutils\\\\plutus_integration.py                   227    187    18%   39-52, 67-95, 109-136, 153-168, 183-218, 230-251, 259-261, 276-296, 308-322, 334-356, 360-377, 381-400, 404-423, 427-444, 448-462, 466-474, 478-486, 490-512, 523-546, 550-551\\nutils\\\\portfolio_visualizer.py                  51     51     0%   1-61\\nutils\\\\proxy_manager.py                         83     64    23%   24-27, 31-46, 50-63, 73-89, 93-107, 111-123, 129-141, 150-152\\nutils\\\\proxy_setup.py                           22     22     0%   1-41\\nutils\\\\realtime_data_test.py                   105    105     0%   7-234\\nutils\\\\reward_redistribution.py                403    272    33%   61, 66, 77, 80, 87-88, 91, 94, 101-102, 105-116, 119, 139-151, 155-161, 165-169, 190-219, 223-249, 254-288, 301-315, 319-323, 327-338, 351-358, 362-380, 384-385, 399-418, 450-455, 460-471, 479-545, 552-566, 570, 588-591, 595-640, 652-653, 659-664, 671-713, 724-766, 789, 791, 795-799, 803-806, 810-815, 824, 832-843\\nutils\\\\risk_manager.py                         179    165     8%   40-56, 81-138, 154-174, 201-239, 261-289, 319-350, 373-408, 429-456, 472-520\\nutils\\\\rl_training_utils.py                    877    751    14%   11-20, 24-33, 37-43, 49-60, 64-66, 71, 76-86, 90-93, 97-103, 106-120, 124-130, 133-135, 138-144, 147-153, 157-167, 170-183, 186-195, 198-206, 209-219, 224-229, 232-243, 246-257, 262-267, 270-279, 284-288, 291-298, 302-307, 310-323, 327-331, 334-342, 346-348, 351-356, 403-446, 449-459, 463-469, 474-483, 487-497, 500-511, 516-522, 526-530, 533-540, 544-555, 560-566, 570-575, 579-589, 596-612, 618-625, 629-635, 644-645, 653, 661-663, 666-671, 674, 677-681, 685-687, 691-693, 701-704, 707-709, 712-718, 721-722, 726-727, 730-731, 739-741, 744-757, 760, 768-772, 775-777, 780-784, 788-791, 794-795, 803-806, 810-813, 816-818, 821-825, 829-834, 842-849, 853-854, 858-864, 868-874, 878-902, 906-918, 922-946, 950-966, 970-980, 984-1017, 1021-1029, 1033-1056, 1060-1074, 1078-1092, 1096-1103, 1113-1121, 1125, 1129-1150, 1154-1167, 1178-1180, 1184-1192, 1196-1214, 1221-1228, 1236-1269, 1276-1277, 1281-1284, 1288, 1292-1296\\nutils\\\\security.py                               7      3    57%   7, 17, 27\\nutils\\\\sentiment_analyzer.py                   602    398    34%   23-25, 55-56, 117-125, 159-163, 184-186, 190-243, 269-271, 275-288, 292-307, 311-322, 335-336, 340-354, 358-368, 372-375, 379-392, 423-438, 447-452, 456-463, 485-489, 493-557, 571, 597-598, 602-616, 620-630, 634-637, 663-686, 691-719, 726-727, 731-753, 764-772, 776-779, 799, 868-870, 874-875, 879-891, 899-982, 998-1059, 1076-1087, 1093-1110, 1114, 1118, 1127, 1131-1133, 1138-1158, 1166-1169, 1174-1182, 1187-1258\\nutils\\\\sentiment_analyzer_old.py               113    113     0%   1-253\\nutils\\\\sentiment_integrator.py                  98     98     0%   1-414\\nutils\\\\simple_backtest_test.py                  91     91     0%   7-156\\nutils\\\\simple_config_test.py                   136    136     0%   7-181\\nutils\\\\simple_database_test.py                 123    123     0%   7-171\\nutils\\\\simple_db_test.py                        43     43     0%   7-79\\nutils\\\\source_credibility.py                    81     81     0%   1-230\\nutils\\\\technical_indicators.py                 109    109     0%   2-176\\nutils\\\\test_all_phase4.py                      128    128     0%   7-198\\nutils\\\\test_fetcher.py                          15     15     0%   1-18\\nutils\\\\test_runner.py                          193    131    32%   40-41, 44, 63, 129-151, 155-177, 181-199, 203-271, 281-299, 303-327, 331-348, 352-369, 373-376, 392, 396, 400, 405-432\\nutils\\\\ultra_simple_test.py                     43     43     0%   6-69\\nutils\\\\zero_shot_learning.py                    19     11    42%   17-18, 22-32, 36\\nverified_working_models.py                    199    199     0%   7-437\\nverify_downloaded_models.py                   132    132     0%   9-324\\nverify_trading_system.py                       50     50     0%   8-91\\nwarning_suppressor.py                          17      0   100%\\n\\u0628\\u0631\\u0631\\u0633\\u06cc_\\u062c\\u0627\\u0645\\u0639_\\u0645\\u0627\\u0698\\u0648\\u0644_\\u0647\\u0627.py                        188    188     0%   8-371\\n\\u062a\\u0633\\u062a_\\u06a9\\u0627\\u0645\\u0644_\\u0686\\u0647\\u0627\\u0631_\\u0645\\u0648\\u0631\\u062f.py                         151    151     0%   5-273\\n\\u06a9\\u0627\\u0631\\u0647\\u0627\\u06cc_\\u0646\\u0627\\u0642\\u0635.py                                142    142     0%   8-921\\n-------------------------------------------------------------------------\\nTOTAL                                       45629  39248    14%\\nFAIL Required test coverage of 80% not reached. Total coverage: 13.98%\n=========================== short test summary info ===========================\nFAILED tests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_add_trading_signal_success\nFAILED tests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_execute_best_opportunity_success\nFAILED tests/test_smart_portfolio_manager.py::TestSmartPortfolioManager::test_adjust_trading_strategy_high_risk\nFAILED tests/test_smart_portfolio_manager.py::TestIntegrationScenarios::test_multiple_signals_prioritization\n======================== 4 failed, 25 passed in 7.10s =========================\n", "error": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pytest_asyncio\\plugin.py:208: PytestDeprecationWarning: The configuration option \"asyncio_default_fixture_loop_scope\" is unset.\nThe event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: \"function\", \"class\", \"module\", \"package\", \"session\"\n\n  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))\n", "timestamp": "2025-07-14T22:26:43.455907"}, {"name": "test_integrated_system.py", "passed": false, "duration": 64.8235456943512, "output": "============================= test session starts =============================\nplatform win32 -- Python 3.9.0, pytest-8.3.5, pluggy-1.5.0 -- C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe\ncachedir: .pytest_cache\nrootdir: D:\\project\nconfigfile: pytest.ini\nplugins: anyio-4.9.0, asyncio-1.0.0, cov-6.2.1, mock-3.14.1\nasyncio: mode=strict, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... collected 23 items\n\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_initialization PASSED [  4%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_risk_parameters_setup PASSED [  8%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_start_system PASSED [ 13%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_stop_system PASSED [ 17%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_generate_signal_for_symbol_success FAILED [ 21%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_generate_signal_for_symbol_risk_limits PASSED [ 26%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_generate_signal_for_symbol_low_confidence FAILED [ 30%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_generate_signal_for_symbol_existing_position FAILED [ 34%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_check_system_health_normal PASSED [ 39%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_check_system_health_extreme_risk PASSED [ 43%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_check_system_health_low_capital PASSED [ 47%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_check_profit_achievements_daily PASSED [ 52%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_check_profit_achievements_all_targets PASSED [ 56%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_display_current_status FAILED [ 60%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_manual_trade_success PASSED [ 65%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_manual_trade_signal_rejected PASSED [ 69%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_manual_trade_execution_failed PASSED [ 73%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_get_current_price PASSED [ 78%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_force_close_all_positions PASSED [ 82%]\ntests/test_integrated_system.py::TestIntegratedTradingSystem::test_save_session_report FAILED [ 86%]\ntests/test_integrated_system.py::TestSystemIntegration::test_full_trading_cycle FAILED [ 91%]\ntests/test_integrated_system.py::TestSystemIntegration::test_risk_management_integration PASSED [ 95%]\ntests/test_integrated_system.py::TestSystemIntegration::test_profit_target_integration PASSED [100%]\nERROR: Coverage failure: total of 14 is less than fail-under=80\n\n\n================================== FAILURES ===================================\n_____ TestIntegratedTradingSystem.test_generate_signal_for_symbol_success _____\ntests\\test_integrated_system.py:101: in test_generate_signal_for_symbol_success\n    with patch('integrated_trading_system.random.uniform') as mock_uniform:\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\unittest\\mock.py:1389: in __enter__\n    self.target = self.getter()\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\unittest\\mock.py:1564: in <lambda>\n    getter = lambda: _importer(target)\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\unittest\\mock.py:1240: in _importer\n    thing = _dot_lookup(thing, comp, import_path)\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\unittest\\mock.py:1229: in _dot_lookup\n    __import__(import_path)\nE   ModuleNotFoundError: No module named 'integrated_trading_system.random'; 'integrated_trading_system' is not a package\n---------------------------- Captured stdout setup ----------------------------\n\\U0001f680 Initializing Integrated Trading System...\\n\\u2705 Integrated Trading System initialized successfully\n_ TestIntegratedTradingSystem.test_generate_signal_for_symbol_low_confidence __\ntests\\test_integrated_system.py:135: in test_generate_signal_for_symbol_low_confidence\n    with patch('integrated_trading_system.random.uniform') as mock_uniform:\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\unittest\\mock.py:1389: in __enter__\n    self.target = self.getter()\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\unittest\\mock.py:1564: in <lambda>\n    getter = lambda: _importer(target)\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\unittest\\mock.py:1240: in _importer\n    thing = _dot_lookup(thing, comp, import_path)\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\unittest\\mock.py:1229: in _dot_lookup\n    __import__(import_path)\nE   ModuleNotFoundError: No module named 'integrated_trading_system.random'; 'integrated_trading_system' is not a package\n---------------------------- Captured stdout setup ----------------------------\n\\U0001f680 Initializing Integrated Trading System...\\n\\u2705 Integrated Trading System initialized successfully\n_ TestIntegratedTradingSystem.test_generate_signal_for_symbol_existing_position _\ntests\\test_integrated_system.py:153: in test_generate_signal_for_symbol_existing_position\n    with patch('integrated_trading_system.random.uniform') as mock_uniform:\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\unittest\\mock.py:1389: in __enter__\n    self.target = self.getter()\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\unittest\\mock.py:1564: in <lambda>\n    getter = lambda: _importer(target)\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\unittest\\mock.py:1240: in _importer\n    thing = _dot_lookup(thing, comp, import_path)\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\unittest\\mock.py:1229: in _dot_lookup\n    __import__(import_path)\nE   ModuleNotFoundError: No module named 'integrated_trading_system.random'; 'integrated_trading_system' is not a package\n---------------------------- Captured stdout setup ----------------------------\n\\U0001f680 Initializing Integrated Trading System...\\n\\u2705 Integrated Trading System initialized successfully\n___________ TestIntegratedTradingSystem.test_display_current_status ___________\ntests\\test_integrated_system.py:262: in test_display_current_status\n    self.trading_system.display_current_status()\nintegrated_trading_system.py:311: in display_current_status\n    print(f\"   Open Positions: {report['portfolio_status']['positions']['open_positions']}\")\nE   KeyError: 'positions'\n---------------------------- Captured stdout setup ----------------------------\n\\U0001f680 Initializing Integrated Trading System...\\n\\u2705 Integrated Trading System initialized successfully\n---------------------------- Captured stdout call -----------------------------\n\\n============================================================\\n\\U0001f4ca CURRENT SYSTEM STATUS\\n============================================================\\n\\U0001f4b0 Capital Status:\\n   Current: $1000.00\\n   Total PnL: $50.00\\n   Daily PnL: $5.00\\n\\n\\U0001f3af Progress to Targets:\\n   Daily: $5.00 / $5.00 (100.0%)\\n   Weekly: $30.00 / $30.00 (100.0%)\\n   Monthly: $80.00 / $80.00 (100.0%)\\n\\n\\U0001f4c8 Trading Statistics:\\n   Signals Received: 10\\n   Action Rate: 50.0%\n____________ TestIntegratedTradingSystem.test_save_session_report _____________\ntests\\test_integrated_system.py:359: in test_save_session_report\n    filename = self.trading_system.save_session_report()\nintegrated_trading_system.py:410: in save_session_report\n    \"win_rate\": report[\"portfolio_status\"][\"performance\"][\"win_rate\"],\nE   KeyError: 'performance'\n---------------------------- Captured stdout setup ----------------------------\n\\U0001f680 Initializing Integrated Trading System...\\n\\u2705 Integrated Trading System initialized successfully\n________________ TestSystemIntegration.test_full_trading_cycle ________________\ntests\\test_integrated_system.py:418: in test_full_trading_cycle\n    assert signal is not None\nE   assert None is not None\n---------------------------- Captured stdout setup ----------------------------\n\\U0001f680 Initializing Integrated Trading System...\\n\\u2705 Integrated Trading System initialized successfully\n=============================== tests coverage ================================\n_______________ coverage: platform win32, python 3.9.0-final-0 ________________\\n\\nName                                        Stmts   Miss  Cover   Missing\\n-------------------------------------------------------------------------\\nactivate_inactive_modules.py                  137    137     0%   8-347\\nadvanced_system_diagnostics.py                436    436     0%   8-769\\nadvanced_system_diagnostics_simple.py         408    408     0%   8-703\\nai_brain_fix.py                                12     12     0%   7-41\\nai_models\\\\__init__.py                         515    337    35%   17-19, 46-55, 62-64, 84, 115-117, 121-124, 128, 132, 136-141, 145, 166, 170, 179, 189, 209, 214-217, 222-229, 234-243, 247-254, 258, 267, 271, 297, 302-305, 310-317, 321, 331-334, 338-344, 348-384, 393-398, 402, 438-440, 444, 457-462, 466-480, 484-488, 497, 508-511, 515-523, 528-532, 545, 555-558, 562-570, 574-578, 589, 599-602, 606-614, 618-622, 633, 643-646, 650-657, 661-664, 668, 678-681, 685-692, 696-700, 704, 714-717, 721-728, 732-735, 739-742, 746, 756-759, 763-770, 774-777, 790, 800-925, 945-949, 953-957, 961-962, 966, 970, 974\\nai_models\\\\huggingface_models.py               278    278     0%   8-544\\nai_models\\\\model_manager.py                    307    307     0%   8-582\\nai_models\\\\sentiment_models.py                  83     63    24%   17-21, 25-35, 39-54, 58, 68-72, 76-86, 90-105, 109, 119-123, 127-137, 141-156, 160\\napi\\\\__init__.py                               266    141    47%   61, 71, 125-127, 131-145, 149-163, 167, 200-210, 235-237, 263, 276, 280-301, 307, 311-317, 325-328, 332-354, 358-379, 384, 394-406, 415-427, 436-470, 479-498, 507-532, 542-563, 569-574, 578-590, 622-624\\napi\\\\endpoints.py                              138     87    37%   73-75, 79, 90-334, 338-349, 353-355\\napi\\\\realtime_dashboard.py                     336    185    45%   109-111, 115-117, 121-126, 130-141, 145, 172-258, 262-274, 278, 290-320, 329, 340-342, 359-363, 367-377, 381-397, 411-464, 469, 474, 507, 512-525, 530-535, 540, 545-553, 558-559, 564-565, 570-571, 576-577, 582-583, 588, 601-610, 615, 620-638, 643-652, 658-678, 687-698, 705-709, 713, 1006-1007\\napi\\\\server.py                                 100    100     0%   8-196\\nbakap.py                                       72     72     0%   1-122\\nclean_cache.py                                 17     17     0%   1-21\\ncomplete_debug_test.py                        100    100     0%   7-161\\ncomplete_model_audit.py                       166    166     0%   9-417\\ncomplete_module_test.py                       161    161     0%   8-278\\ncomprehensive_system_check.py                 126    126     0%   7-202\\ncomprehensive_system_test.py                  173    173     0%   7-282\\ncore\\\\__init__.py                              345    239    31%   83-88, 101-114, 126-131, 144-151, 163-169, 184-193, 212-225, 245, 296-325, 352-373, 395-411, 432-447, 469, 508-523, 534-539, 551-557, 719-732, 736-774, 778-816\\ncore\\\\advanced_config.py                       274    274     0%   8-401\\ncore\\\\advanced_order_management.py             368    224    39%   38-39, 140-150, 154, 158-160, 164, 173-186, 207, 211-212, 216-218, 222-231, 237-241, 245-271, 277-300, 305-332, 336-360, 364-375, 379-409, 414-449, 454-455, 459-483, 487-508, 512-530, 534-550, 554-555, 559, 563-568, 572, 576, 580-587, 600, 604, 614-617, 623-626, 631, 636, 640-657\\ncore\\\\advanced_risk_metrics.py                 300    213    29%   92, 146-231, 235-279, 283, 296, 310, 314-315, 319-320, 324-327, 331-333, 337-355, 359, 363-364, 368-369, 373-381, 386-394, 398-403, 407-416, 421-432, 437-438, 443-475, 479-495, 499-523, 527-545, 553, 557, 561, 565-591\\ncore\\\\backtesting_framework.py                 156     84    46%   62-63, 67, 73-75, 79-89, 102-111, 122-124, 142-147, 154-170, 174-187, 191-219, 231-232, 247, 251, 259-285\\ncore\\\\base.py                                  235    150    36%   29, 51, 76, 81, 86, 91, 95, 106-107, 118-129, 133, 137, 141-157, 161-174, 178-189, 193-205, 211-213, 217-230, 234-250, 254-270, 274-285, 300-301, 305, 317-326, 331, 336, 340-353, 357-384, 388-406, 410, 422\\ncore\\\\circuit_breaker_system.py                109     70    36%   102-105, 111-128, 131, 137-141, 144-150, 153-155, 161-163, 169, 179, 190-192, 197-205, 213-227, 235-249\\ncore\\\\config.py                                158     47    70%   93, 126-127, 131-133, 137-150, 191-198, 204-205, 213-218, 229-254\\ncore\\\\configuration_management.py              467    258    45%   36, 46-47, 50, 53, 61, 68, 108, 132-134, 138-140, 158-160, 164-166, 187-189, 193-195, 199-203, 219-222, 235-237, 250-252, 291-309, 314, 319, 322, 326-331, 336-343, 375-409, 413-419, 423-441, 449-471, 475-480, 484-497, 501-504, 508-517, 521-529, 533-541, 545-551, 555-578, 582-587, 591-592, 596-598, 602-606, 610, 614-626, 630-646, 651-663, 667-681, 685-690, 694-697, 701-704, 712, 716, 720, 724, 728, 732, 736, 740-775\\ncore\\\\correlation_analysis.py                  336    246    27%   70, 74-80, 85-88, 101, 105-119, 165-220, 241-294, 311-375, 392-465, 480-525, 542-600, 621-672, 677, 689, 704, 710, 714, 718, 723-756\\ncore\\\\database_manager.py                      332    332     0%   8-583\\ncore\\\\database_transaction_manager.py          324    192    41%   36-39, 141-154, 158-192, 196-197, 201-202, 206-210, 214-218, 229-234, 240-244, 248-250, 259-337, 341-357, 361-374, 378-401, 427-430, 434-449, 453-457, 461, 465-475, 479-489, 493, 502-504, 514-518, 524-527, 532, 536, 540, 544-568\\ncore\\\\enhanced_error_handling.py               314    170    46%   41-45, 48, 134, 179-196, 200-202, 206-210, 214-219, 223, 240-257, 265-284, 289-291, 310-315, 346-369, 373-377, 381-382, 389, 396-407, 417-438, 443-460, 464-473, 477-486, 491-495, 519, 523, 527, 531-573\\ncore\\\\error_handler.py                         240    131    45%   96-101, 105-118, 122-125, 129-130, 134-138, 177-195, 202-220, 224-225, 229-242, 246-250, 254-259, 263-274, 278-291, 303-308, 312, 328-329, 333-338, 342-368, 375-378, 390-404, 409-415, 421-425, 430, 436, 444-463\\ncore\\\\exceptions.py                             19      0   100%\\ncore\\\\logger.py                                 87     30    66%   43, 46, 106, 108, 115-122, 126-133, 137-144, 158-164\\ncore\\\\memory_manager.py                        266    122    54%   60, 85-91, 95-105, 109-112, 123-126, 172, 180-181, 238, 242, 246-273, 302, 304, 306, 317, 321-322, 328-354, 358-360, 364, 371, 384-385, 389-444, 449, 457-459, 467-468, 472-477, 481-484, 512-525, 529-532, 537-565\\ncore\\\\mlops_versioning.py                      100     66    34%   26-28, 90-111, 116-131, 136-150, 158-161, 165-175, 184, 187, 190\\ncore\\\\model_monitoring.py                      502    368    27%   77-81, 95, 120, 150, 155-181, 186-215, 221-223, 230-245, 250-294, 299-309, 313-327, 331-347, 356-378, 382-404, 410-440, 444-449, 453-455, 462-479, 483-524, 530-561, 565-587, 591-613, 617-648, 652-669, 673-679, 683-685, 689-691, 695-706, 721-754, 758, 762, 775-790, 794-795, 799-804, 808-813, 817-829, 839-844, 852-858, 863-938, 941\\ncore\\\\model_versioning.py                      441    275    38%   83-85, 111-119, 132, 136, 140-148, 156-178, 183-202, 224, 232-236, 243, 265-267, 295-296, 301-343, 348-369, 373-392, 397-409, 413-425, 429-441, 445-469, 473-479, 483-522, 526-549, 553-570, 585-593, 607-615, 619-622, 626-627, 631-645, 649-652, 660-744, 747\\ncore\\\\multi_exchange.py                        542    389    28%   70-73, 93-111, 125, 130, 135, 140, 145, 150, 155, 159-170, 174-176, 180-182, 186-190, 203, 207, 211, 215-219, 223-227, 231-235, 241-245, 249-271, 275-284, 288, 292-327, 331-360, 364-371, 376, 382-386, 390-409, 413-422, 426, 430-464, 468-496, 500-506, 510, 544-569, 573-586, 590-602, 607-623, 627-647, 651-654, 658-668, 672-677, 682-691, 695, 699, 703-713, 718-722, 726-729, 740-749, 753-755, 759-770, 785-797, 808-815, 819-836, 844-848, 853-923, 926\\ncore\\\\order_manager.py                         337    254    25%   57-104, 109-121, 172-212, 216-231, 235-251, 257-303, 307-382, 386-390, 394-398, 402, 406-409, 413, 417, 421-424, 428-431, 435-440, 444, 448, 452-505, 523-525, 529, 532, 535, 538-540, 548-549, 557-571, 576-636\\ncore\\\\performance_optimizer.py                 261    261     0%   8-575\\ncore\\\\realtime_data_processing.py              378    247    35%   78-79, 113-117, 122, 126, 130, 136-139, 143-170, 174-192, 196-203, 207-224, 228, 232, 236-237, 243-255, 266-276, 280-283, 287-305, 309-329, 333-347, 351-365, 369-381, 385-386, 390-391, 395-397, 401, 413-421, 432-442, 446-458, 462-470, 474-478, 482-489, 493-502, 506-517, 522, 526-530, 534-536, 540, 547-549, 553, 566-568, 573-575, 580-585, 596-599, 610-668, 671\\ncore\\\\shared_types.py                           79      0   100%\\ncore\\\\simple_config.py                         173     69    60%   61-70, 85-87, 99-103, 158, 163-174, 179-198, 202-226, 230, 234, 252, 257-258, 262, 266-267, 271-284\\ncore\\\\simple_database_manager.py               274    210    23%   60-64, 68-79, 83-95, 99-106, 112-116, 120-122, 128-189, 193-204, 217-223, 284-301, 307-324, 330-367, 371-399, 403-412, 416-441, 445-471, 475, 479-483, 490, 496-501, 507-509, 513-556\\ncore\\\\utils.py                                 372    282    24%   38-50, 55-67, 71-75, 79-82, 86-90, 94-100, 104-107, 138-141, 159-161, 165, 176-178, 188-193, 205-218, 222-229, 233-252, 268-304, 316-321, 334-348, 361-374, 387-400, 412-441, 454-470, 485-488, 492-504, 513-565, 581-599, 609-629, 643-656, 671-676, 690-693, 707-712, 722-739, 745-766\\ncvxpy_fix.py                                   17     17     0%   7-37\\ndata\\\\__init__.py                                0      0   100%\\ndata\\\\fetcher.py                                25     25     0%   1-38\\ndata\\\\import sys.py                              7      7     0%   1-12\\ndata\\\\preprocessor.py                           43     43     0%   1-76\\ndata\\\\test_fetcher.py                            7      7     0%   1-12\\ndebug_fitness_issue.py                         63     63     0%   6-124\\ndownload_and_test_models.py                   183    183     0%   9-331\\ndownload_hf_model.py                            0      0   100%\\nencoding_config.py                              9      9     0%   7-20\\nenhanced_financial_models.py                  356    356     0%   16-731\\nenhanced_spacy_mock.py                        116    116     0%   7-183\\nenv\\\\__init__.py                               194    136    30%   39-59, 63-76, 80-86, 90-103, 107, 120-134, 138-140, 144-146, 151-160, 173, 177-179, 184-202, 207-212, 216-231, 235-250, 254-261, 266-271, 275-292, 297-304, 308, 318-319, 323, 337-342, 346-347, 351-353, 358, 372-375, 381, 427-429\\nenv\\\\portfolio.py                               16     13    19%   6-23\\nenv\\\\trading_env.py                            229    201    12%   18-19, 29-102, 105-129, 133-192, 199-224, 227-292, 296-305, 308-476, 479, 482, 485, 489-492, 505-506\\nevaluation\\\\__init__.py                        251    151    40%   72, 115-117, 121-127, 131-141, 145, 160-203, 207-214, 218-230, 238-256, 274-324, 333-370, 379-408, 417-453, 484-495, 499-522, 534, 538, 542, 588-590\\nevaluation\\\\comparison.py                      185    138    25%   35-36, 53-55, 65-66, 73-143, 149-161, 167-207, 211-240, 244-263, 267-282, 288-289, 300-318, 326-345, 349, 388-408, 411\\nevaluation\\\\metrics.py                          35     33     6%   16-70\\nevaluation\\\\scenario_backtesting.py            354    324     8%   26-29, 33, 54-81, 98-126, 138-169, 186-238, 255-325, 339-361, 373-400, 410-466, 477-491, 502-519, 530-542, 553-567, 578-591, 602-621, 632-644, 655-668, 682-711\\nevaluation\\\\tests.py                             0      0   100%\\nexamples\\\\plutus_api_example.py                212    190    10%   28-32, 41-66, 75-105, 112-147, 154-200, 209-210, 220-264, 271-306, 313-392, 401-432, 436-449\\nexamples\\\\plutus_integration_final.py          198    166    16%   54-110, 117-171, 178-222, 229-288, 295-323, 330-343, 350-396, 399\\nexecution_pipeline.py                         315    239    24%   88-98, 102-108, 112-119, 123, 136-166, 170-175, 179, 247-300, 312-344, 352-388, 396-476, 484-541, 549-581, 589-619, 627-656, 664-667, 691-710, 714-729, 744-750\\nexpert_system_analysis.py                     324    324     0%   8-580\\nfinal_complete_fix_test.py                    229    229     0%   8-378\\nfinal_complete_test.py                         45     45     0%   7-71\\nfinal_real_models_system.py                   277    277     0%   16-569\\nfinal_summary.py                               40     40     0%   6-52\\nfinal_system_analysis.py                      111    111     0%   6-262\\nfinal_system_test.py                           24     24     0%   7-46\\nfinancial_models_free_usage.py                244    244     0%   16-441\\nfix_all_minor_issues.py                        92     92     0%   8-215\\nfix_all_warnings.py                           113    113     0%   8-373\\nfix_broken_non_test_files.py                  154    154     0%   8-2213\\nfix_clarabel_solver.py                         63     63     0%   7-115\\nfix_comprehensive_system_test.py              228    228     0%   7-396\\nfix_cursor_proxy.py                           129    129     0%   6-239\\nfix_encoding_issues.py                         66     66     0%   8-133\\nfix_final_issues.py                            98     98     0%   8-392\\nfix_import_issues.py                           79     79     0%   8-157\\nfix_inactive_modules.py                        69     69     0%   8-266\\nfix_main_system.py                            143    143     0%   8-367\\nfix_memory_issues.py                           78     78     0%   9-156\\nfix_multi_exchange.py                          84     84     0%   8-260\\nfix_persian_model.py                           79     79     0%   8-287\\nfix_real_issues.py                             97     97     0%   8-367\\nfix_remaining_issues.py                       138    138     0%   8-504\\nfix_spacy_models.py                            86     86     0%   8-342\\nfunctional_test.py                            127    127     0%   5-205\\nhuggingface_fix.py                             19     19     0%   7-36\\nimmediate_test.py                             145    145     0%   7-239\\ninstall_and_test_ai_models.py                 181    181     0%   9-331\\ninstall_real_models.py                         36     36     0%   6-75\\ninstall_simple.py                              28     28     0%   10-53\\ninstall_spacy_models.py                        54     54     0%   7-111\\nintegrated_trading_system.py                  222     77    65%   131-153, 171-180, 191-207, 212-228, 240, 281-286, 313-323, 379, 419-425, 429-456\\nintegration_summary.py                         84     84     0%   7-131\\nllma.py                                       149    149     0%   8-356\\nlogging_config.py                              30     30     0%   7-63\\nmain.py                                        19     19     0%   9-35\\nmain_new.py                                   471    378    20%   37-38, 161-163, 181-183, 192-194, 234-241, 249-268, 272-284, 288-393, 397-417, 421-428, 432-439, 443-450, 454-461, 465-491, 495-532, 536-563, 567-659, 663-688, 692-721, 725-771, 780-823, 828-865, 869\\nmodel_usage_examples.py                        27     27     0%   7-56\\nmodels\\\\__init__.py                            166    104    37%   113-122, 126-133, 137-158, 162-193, 197, 201-204, 216-230, 234-247, 251-265, 274-281, 285, 289, 293, 376-378\\nmodels\\\\ai_agent.py                            466    371    20%   105-144, 152-154, 158, 186-208, 212-227, 231-238, 243-248, 253-258, 263-268, 272-321, 325-346, 350-368, 372-416, 420-444, 448-464, 470-510, 517-561, 565-572, 576-583, 587-609, 613-628, 634-658, 665-785, 799-831, 836-851, 855-871, 875-908, 912-944, 949\\nmodels\\\\continual_learning.py                  245    197    20%   39-43, 55-107, 118-129, 145-154, 175-181, 194-198, 214-217, 223-227, 234-250, 263-264, 267, 312-339, 350-367, 377, 390-391, 409-419, 435-454, 467-478, 498-511, 522-534, 545-557, 569-594, 615-653, 661-663, 667-668, 672-678, 682-686, 690-695\\nmodels\\\\ensemble_model.py                      253    228    10%   65-99, 110-136, 153-211, 230-275, 282-332, 348-378, 389, 401-437, 456-516, 522-539, 545-571\\nmodels\\\\hierarchical_rl.py                     243    243     0%   15-552\\nmodels\\\\meta_learner.py                        184    165    10%   33-37, 41-48, 52-53, 69-92, 113-152, 173-203, 219-243, 261-301, 319-431\\nmodels\\\\rl_models.py                           109     87    20%   21-78, 84-114, 116, 134, 140-145, 180-181, 195, 213, 228, 243, 252, 267, 284, 298-302, 305, 319, 331\\nmodels\\\\unified_trading_system.py              632    519    18%   17-19, 52-114, 181-182, 196-230, 235-249, 254-274, 281-309, 316-347, 352-368, 374-383, 388-426, 434-508, 520-529, 534-550, 560-576, 583-610, 615-635, 654-680, 687-712, 722-826, 858-889, 914-926, 939-946, 954-985, 997-1045, 1059-1095, 1105-1120, 1125-1143, 1148-1164, 1173-1212, 1217-1249, 1254-1275, 1280-1293, 1297-1298, 1304-1313, 1317-1329, 1335-1395, 1407-1426, 1441-1485, 1493-1512\\nmodels\\\\zero_shot_learning.py                  298    298     0%   1-815\\nmodule_analysis.py                            202    202     0%   8-394\\noptimization\\\\__init__.py                      279    192    31%   62, 103-105, 109-115, 119-126, 130, 146-198, 217-257, 267-313, 323-356, 366-399, 408-437, 443-459, 463-492, 497-540, 552, 585-587\\noptimization\\\\bayesian.py                      119    100    16%   25-40, 50-73, 79, 99-150, 160-169, 179-200, 207-221, 227-232, 247-249, 255-267\\noptimization\\\\genetic.py                        81     70    14%   16-26, 32-74, 83-94, 99-110, 114-135, 139-155\\noptimization\\\\pso.py                           185    185     0%   8-385\\npersian_sentiment_fallback.py                  57     37    35%   47-90, 99-114, 121-122, 130\\nph3_optimizer.py                              297    297     0%   1-567\\nportfolio\\\\__init__.py                         298    204    32%   51-58, 62, 66-76, 94-95, 99, 103-105, 111-144, 148-158, 162-168, 172-185, 189, 205-212, 217-233, 239-270, 274-337, 341-343, 347-354, 358-371, 375, 379, 383-385, 389-395, 399-413, 417, 435-449, 453-454, 458-460, 465-467, 471-473, 477-481, 488, 495, 499, 503, 507, 511, 520-523, 567-569\\nportfolio\\\\advanced_risk_manager.py            250    167    33%   55, 60, 65, 83-85, 106-108, 139-161, 167-174, 178-214, 220-242, 248-301, 306-375, 379-386, 391-400, 405-415, 465-505, 510-550\\nportfolio\\\\portfolio_manager.py                157    131    17%   14-15, 24-52, 63-137, 147-165, 170-191, 209-250, 271-315, 325-357, 369, 373, 377-380, 384, 388, 392-395, 413\\nportfolio\\\\smart_portfolio_manager.py          259    202    22%   89-111, 117-165, 169-212, 217-252, 256-266, 270-307, 312-333, 338-357, 362-385, 389-394, 398-399, 404-406, 453-488, 493-547\\npractical_model_test.py                       164    164     0%   9-362\\nprofit_target_demo.py                         230    230     0%   8-461\\nproject_analyzer.py                           149    149     0%   8-285\\nquick_complete_system_test.py                 195    195     0%   8-302\\nquick_debug_test.py                            31     31     0%   6-45\\nquick_final_test.py                           141    141     0%   7-235\\nquick_fix_main.py                              15     15     0%   7-41\\nquick_reality_check.py                         94     94     0%   6-141\\nquick_status_check.py                          44     44     0%   5-66\\nquick_test.py                                  74     74     0%   7-108\\nreal_huggingface_models.py                     40     40     0%   16-306\\nrun_comprehensive_tests.py                     82     82     0%   6-206\\nrun_sentiment_tests.py                         51     51     0%   1-105\\nrun_tests.py                                   44     44     0%   9-84\\nsentiment_integration.py                      211    211     0%   9-364\\nsetup_financial_models.py                     163    163     0%   10-390\\nsimple_debug_test.py                           25     25     0%   6-37\\nsimple_financial_models.py                    253    253     0%   14-486\\nsimple_model_test.py                           39     39     0%   7-69\\nsimple_profit_demo.py                         187    187     0%   8-350\\nsimple_system_test.py                         144    144     0%   6-287\\nsimple_test.py                                 77     77     0%   6-120\\nsystem_dashboard.py                            91     91     0%   7-148\\nsystem_status.py                               58     58     0%   7-90\\ntest_advanced_risk_metrics.py                  82     82     0%   7-143\\ntest_ai_agent.py                               70     70     0%   6-133\\ntest_ai_agent_integration.py                   46     46     0%   11-102\\ntest_all_fixes.py                              76     76     0%   7-134\\ntest_backtesting_integration.py                96     96     0%   8-194\\ntest_complete_system.py                        83     83     0%   7-134\\ntest_connection_no_proxy.py                    35     35     0%   6-59\\ntest_fetcher.py                                 2      2     0%   1-3\\ntest_final_integration.py                     306    306     0%   8-656\\ntest_genetic_algorithm_deep.py                128    128     0%   6-319\\ntest_hierarchical_simple.py                    23     23     0%   8-43\\ntest_integrated_system.py                     187    187     0%   15-368\\ntest_integration.py                           228    228     0%   10-405\\ntest_integration_final.py                     275    275     0%   13-483\\ntest_integration_fixed.py                     243    243     0%   12-428\\ntest_integration_v2.py                        370    370     0%   9-630\\ntest_local_only.py                             41     41     0%   6-80\\ntest_models_simple.py                          55     55     0%   6-84\\ntest_offline.py                                 0      0   100%\\ntest_persian_improved.py                       38     38     0%   4-73\\ntest_ray.py                                     8      8     0%   1-9\\ntest_real_models.py                           170    170     0%   8-382\\ntest_real_models_with_proxy.py                196    196     0%   9-437\\ntest_sentiment_complete.py                     32     32     0%   4-63\\ntest_simple_models.py                          87     87     0%   7-183\\ntest_unified_system.py                        199    199     0%   6-515\\ntest_with_http_proxy.py                        65     65     0%   7-118\\ntest_with_token.py                             81     81     0%   7-190\\ntests\\\\conftest.py                             320    215    33%   43-45, 77-78, 83-84, 91, 96-98, 103, 108-115, 122-127, 132-137, 144-151, 156-164, 169-175, 182-195, 200-212, 219-237, 242-261, 268-280, 287-299, 306-317, 322-339, 346-356, 361-372, 379-382, 387-393, 400, 421, 441-472, 479, 501, 504, 519, 523-527, 531-536, 540-542\\ntests\\\\test_integrated_system.py               218     22    90%   102-112, 136-138, 154-156, 361-363, 421-430, 498\\ntests\\\\test_plutus_models_comprehensive.py     342    306    11%   62-103, 110-181, 188-301, 308-359, 366-428, 435-503, 510-578, 586-599, 606-621, 625-674\\nultimate_final_test.py                        143    143     0%   8-206\\nutils\\\\__init__.py                              96     33    66%   109, 117, 128-133, 138-143, 148-152, 158, 162, 166, 172, 176, 180, 184-189, 274-276\\nutils\\\\adaptive_margin_control.py              301    264    12%   21-26, 29-33, 61-90, 98-113, 123-130, 141-152, 164-176, 188-200, 213-224, 233-242, 255-263, 276-298, 316-369, 387-405, 417-448, 461-464, 476-488, 508-647, 659-689, 701-717, 729-743, 755-771, 783-798\\nutils\\\\adaptive_plutus_system.py               467    374    20%   120-136, 141-164, 168-183, 187-205, 219-252, 256-292, 296-324, 328-382, 394-460, 465-512, 516-581, 586-617, 621-648, 652-667, 687-698, 702-705, 709-733, 737-762, 766-810, 814-890, 894-937, 940\\nutils\\\\advanced_order_test.py                   61     61     0%   7-106\\nutils\\\\advanced_reward_system.py               145    102    30%   72-95, 114-133, 158-194, 198-208, 223-228, 232-255, 278-311, 329-345, 355, 369-370, 374-393, 397\\nutils\\\\advanced_rl_agent.py                    351    351     0%   8-732\\nutils\\\\advanced_technical_indicators.py        428    370    14%   65-67, 71-73, 77-84, 89-98, 103-111, 116-122, 127-134, 139-152, 157-162, 167-171, 175-177, 181-183, 192-209, 220-256, 260-280, 285-289, 294-305, 310-334, 339-347, 352-358, 363-367, 371-383, 389-403, 408-413, 417-424, 429-434, 438-443, 448-454, 458-461, 465-471, 475-485, 489-494, 499-502, 507-512, 517-529, 534-541, 545-554, 558-568, 573-592, 597-603, 607-608, 612, 616-649, 653-719, 723-732, 736, 740, 780-834, 837\\nutils\\\\alpha_beta_attribution.py               308    186    40%   132-162, 183-213, 223-227, 242-264, 268-273, 279-312, 321-327, 350-413, 436-445, 452-453, 461-482, 487, 492, 496, 501-502, 507-508, 517-542, 555-587, 592-608, 626-647\\nutils\\\\anomaly_detection_system.py             357    357     0%   8-858\\nutils\\\\auto_drawdown_control.py                397    269    32%   133-179, 183-190, 196-241, 259-271, 276-278, 282-286, 305-336, 341-350, 367-401, 405-415, 419-424, 428-435, 443-483, 511-518, 526-544, 551-571, 576-586, 611-674, 682-723, 728-739, 786-815, 826-856\\nutils\\\\auto_feature_engineering.py              52     52     0%   1-72\\nutils\\\\auto_hyperparameter_tuning.py           294    294     0%   9-613\\nutils\\\\auto_hyperparameter_tuning_fixed.py     225    225     0%   6-461\\nutils\\\\auto_market_maker.py                    271    241    11%   19-23, 26-28, 65-102, 116-140, 153-159, 171-172, 186-208, 222-237, 248-271, 284, 290-338, 353-432, 441-454, 463-480, 489-492, 507-512, 523-527, 541-652\\nutils\\\\backtesting_framework.py                247    211    15%   27-29, 40-43, 47-50, 54-71, 86-110, 114-122, 126-130, 134-178, 182-204, 211-229, 233-299, 303-316, 320-351, 355-377, 381-385, 390-435, 439-443\\nutils\\\\circuit_breaker_test.py                  71     71     0%   7-124\\nutils\\\\cleanup.py                               10      7    30%   12-23\\nutils\\\\config.py                                 7      4    43%   14-18\\nutils\\\\config_override.py                       30     30     0%   1-35\\nutils\\\\config_validation.py                     14     14     0%   1-16\\nutils\\\\data_cleaning.py                        211    211     0%   2-360\\nutils\\\\data_cleaning_pipeline.py                51     51     0%   1-144\\nutils\\\\data_pipeline.py                         19     19     0%   1-29\\nutils\\\\data_utils.py                           114    102    11%   33-55, 72-127, 143-194, 215-228, 254-260, 264-268\\nutils\\\\database_transaction_test.py             95     95     0%   8-213\\nutils\\\\enhanced_adaptive_plutus.py             240    240     0%   6-444\\nutils\\\\explainable_ai.py                       961    961     0%   1-1877\\nutils\\\\export.py                                12      6    50%   7-8, 12-13, 17-18\\nutils\\\\federated_learning_system.py            226    226     0%   9-523\\nutils\\\\genetic_strategy_evolution.py           408    408     0%   8-870\\nutils\\\\hft_modeling.py                         285    186    35%   96-108, 112-129, 146-189, 194-199, 219-226, 230-257, 261-272, 277-281, 324-369, 389-428, 450-469, 532-550, 554-566, 585-602, 606-608, 634-635, 639-640, 648-657, 667-672\\nutils\\\\hierarchical_rl.py                       25     11    56%   25-27, 31-44, 48\\nutils\\\\integrated_advanced_system.py           260    260     0%   12-534\\nutils\\\\intelligent_memory_system.py            427    427     0%   8-970\\nutils\\\\log_config.py                            14     14     0%   1-29\\nutils\\\\logger.py                                11      0   100%\\nutils\\\\market_regime_detector.py               284    284     0%   6-630\\nutils\\\\memory_manager_test.py                  138    138     0%   8-246\\nutils\\\\mlops_versioning_test.py                 55     55     0%   7-102\\nutils\\\\model_monitoring_test.py                306    306     0%   8-584\\nutils\\\\model_versioning_test.py                245    245     0%   8-469\\nutils\\\\multi_exchange_routing.py               414    289    30%   99-102, 106-114, 118-119, 123-144, 154-184, 188, 192, 226-281, 285, 321-326, 348-373, 401-426, 461-492, 514-539, 559, 563-587, 591-604, 608-617, 621-627, 631, 659-679, 694-734, 763-820, 835-855, 870-895, 905-916, 920, 932-938\\nutils\\\\multi_exchange_test.py                  188    188     0%   8-406\\nutils\\\\multi_step_prediction.py                320    320     0%   9-692\\nutils\\\\multi_step_prediction_fixed.py          252    252     0%   6-474\\nutils\\\\news_volume_analyzer.py                  94     94     0%   1-269\\nutils\\\\offline_sentiment_analyzer.py           128     96    25%   35-36, 43-83, 91-103, 108-111, 115-187, 191-207, 217-221, 228-229, 233-242, 246-267\\nutils\\\\operational_integration.py              112     83    26%   29-30, 34-35, 39-66, 70-101, 105, 115-129, 133-139, 143-150, 154-191, 200-207, 216, 220, 224, 228, 232, 237-253\\nutils\\\\order_manager_test.py                   193    193     0%   8-421\\nutils\\\\plutus_integration.py                   227    187    18%   39-52, 67-95, 109-136, 153-168, 183-218, 230-251, 259-261, 276-296, 308-322, 334-356, 360-377, 381-400, 404-423, 427-444, 448-462, 466-474, 478-486, 490-512, 523-546, 550-551\\nutils\\\\portfolio_visualizer.py                  51     51     0%   1-61\\nutils\\\\proxy_manager.py                         83     64    23%   24-27, 31-46, 50-63, 73-89, 93-107, 111-123, 129-141, 150-152\\nutils\\\\proxy_setup.py                           22     22     0%   1-41\\nutils\\\\realtime_data_test.py                   105    105     0%   7-234\\nutils\\\\reward_redistribution.py                403    272    33%   61, 66, 77, 80, 87-88, 91, 94, 101-102, 105-116, 119, 139-151, 155-161, 165-169, 190-219, 223-249, 254-288, 301-315, 319-323, 327-338, 351-358, 362-380, 384-385, 399-418, 450-455, 460-471, 479-545, 552-566, 570, 588-591, 595-640, 652-653, 659-664, 671-713, 724-766, 789, 791, 795-799, 803-806, 810-815, 824, 832-843\\nutils\\\\risk_manager.py                         179    165     8%   40-56, 81-138, 154-174, 201-239, 261-289, 319-350, 373-408, 429-456, 472-520\\nutils\\\\rl_training_utils.py                    877    751    14%   11-20, 24-33, 37-43, 49-60, 64-66, 71, 76-86, 90-93, 97-103, 106-120, 124-130, 133-135, 138-144, 147-153, 157-167, 170-183, 186-195, 198-206, 209-219, 224-229, 232-243, 246-257, 262-267, 270-279, 284-288, 291-298, 302-307, 310-323, 327-331, 334-342, 346-348, 351-356, 403-446, 449-459, 463-469, 474-483, 487-497, 500-511, 516-522, 526-530, 533-540, 544-555, 560-566, 570-575, 579-589, 596-612, 618-625, 629-635, 644-645, 653, 661-663, 666-671, 674, 677-681, 685-687, 691-693, 701-704, 707-709, 712-718, 721-722, 726-727, 730-731, 739-741, 744-757, 760, 768-772, 775-777, 780-784, 788-791, 794-795, 803-806, 810-813, 816-818, 821-825, 829-834, 842-849, 853-854, 858-864, 868-874, 878-902, 906-918, 922-946, 950-966, 970-980, 984-1017, 1021-1029, 1033-1056, 1060-1074, 1078-1092, 1096-1103, 1113-1121, 1125, 1129-1150, 1154-1167, 1178-1180, 1184-1192, 1196-1214, 1221-1228, 1236-1269, 1276-1277, 1281-1284, 1288, 1292-1296\\nutils\\\\security.py                               7      3    57%   7, 17, 27\\nutils\\\\sentiment_analyzer.py                   602    398    34%   23-25, 55-56, 117-125, 159-163, 184-186, 190-243, 269-271, 275-288, 292-307, 311-322, 335-336, 340-354, 358-368, 372-375, 379-392, 423-438, 447-452, 456-463, 485-489, 493-557, 571, 597-598, 602-616, 620-630, 634-637, 663-686, 691-719, 726-727, 731-753, 764-772, 776-779, 799, 868-870, 874-875, 879-891, 899-982, 998-1059, 1076-1087, 1093-1110, 1114, 1118, 1127, 1131-1133, 1138-1158, 1166-1169, 1174-1182, 1187-1258\\nutils\\\\sentiment_analyzer_old.py               113    113     0%   1-253\\nutils\\\\sentiment_integrator.py                  98     98     0%   1-414\\nutils\\\\simple_backtest_test.py                  91     91     0%   7-156\\nutils\\\\simple_config_test.py                   136    136     0%   7-181\\nutils\\\\simple_database_test.py                 123    123     0%   7-171\\nutils\\\\simple_db_test.py                        43     43     0%   7-79\\nutils\\\\source_credibility.py                    81     81     0%   1-230\\nutils\\\\technical_indicators.py                 109    109     0%   2-176\\nutils\\\\test_all_phase4.py                      128    128     0%   7-198\\nutils\\\\test_fetcher.py                          15     15     0%   1-18\\nutils\\\\test_runner.py                          193    131    32%   40-41, 44, 63, 129-151, 155-177, 181-199, 203-271, 281-299, 303-327, 331-348, 352-369, 373-376, 392, 396, 400, 405-432\\nutils\\\\ultra_simple_test.py                     43     43     0%   6-69\\nutils\\\\zero_shot_learning.py                    19     11    42%   17-18, 22-32, 36\\nverified_working_models.py                    199    199     0%   7-437\\nverify_downloaded_models.py                   132    132     0%   9-324\\nverify_trading_system.py                       50     50     0%   8-91\\nwarning_suppressor.py                          17      0   100%\\n\\u0628\\u0631\\u0631\\u0633\\u06cc_\\u062c\\u0627\\u0645\\u0639_\\u0645\\u0627\\u0698\\u0648\\u0644_\\u0647\\u0627.py                        188    188     0%   8-371\\n\\u062a\\u0633\\u062a_\\u06a9\\u0627\\u0645\\u0644_\\u0686\\u0647\\u0627\\u0631_\\u0645\\u0648\\u0631\\u062f.py                         151    151     0%   5-273\\n\\u06a9\\u0627\\u0631\\u0647\\u0627\\u06cc_\\u0646\\u0627\\u0642\\u0635.py                                142    142     0%   8-921\\n-------------------------------------------------------------------------\\nTOTAL                                       45591  39157    14%\\nFAIL Required test coverage of 80% not reached. Total coverage: 14.11%\n=========================== short test summary info ===========================\nFAILED tests/test_integrated_system.py::TestIntegratedTradingSystem::test_generate_signal_for_symbol_success\nFAILED tests/test_integrated_system.py::TestIntegratedTradingSystem::test_generate_signal_for_symbol_low_confidence\nFAILED tests/test_integrated_system.py::TestIntegratedTradingSystem::test_generate_signal_for_symbol_existing_position\nFAILED tests/test_integrated_system.py::TestIntegratedTradingSystem::test_display_current_status\nFAILED tests/test_integrated_system.py::TestIntegratedTradingSystem::test_save_session_report\nFAILED tests/test_integrated_system.py::TestSystemIntegration::test_full_trading_cycle\n======================== 6 failed, 17 passed in 8.23s =========================\n", "error": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pytest_asyncio\\plugin.py:208: PytestDeprecationWarning: The configuration option \"asyncio_default_fixture_loop_scope\" is unset.\nThe event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: \"function\", \"class\", \"module\", \"package\", \"session\"\n\n  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))\n", "timestamp": "2025-07-14T22:27:48.296027"}, {"name": "test_integration_*.py", "passed": false, "duration": 45.97352933883667, "output": "============================= test session starts =============================\nplatform win32 -- Python 3.9.0, pytest-8.3.5, pluggy-1.5.0 -- C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe\ncachedir: .pytest_cache\nrootdir: D:\\project\nconfigfile: pytest.ini\nplugins: anyio-4.9.0, asyncio-1.0.0, cov-6.2.1, mock-3.14.1\nasyncio: mode=strict, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... ✅ All warnings suppressed\n✅ All warnings suppressed\n2025-07-14 22:27:54 - utils - \u001b[32mINFO\u001b[0m - ✅ Utils package initialized successfully\n✅ All warnings suppressed\n2025-07-14 22:28:08 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - پروکسی با موفقیت تنظیم شد.\n2025-07-14 22:28:09 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-14 22:28:13 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-14 22:28:13 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-14 22:28:16 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n2025-07-14 22:28:16 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\n2025-07-14 22:28:16 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-14 22:28:17 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-14 22:28:17 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-14 22:28:20 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n2025-07-14 22:28:20 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\n2025-07-14 22:28:20 - utils.adaptive_plutus_system - \u001b[32mINFO\u001b[0m - Adaptive Plutus System initialized\n2025-07-14 22:28:20 - utils.adaptive_plutus_system - \u001b[32mINFO\u001b[0m - Adaptive Plutus System initialized\n2025-07-14 22:28:20 - models - \u001b[32mINFO\u001b[0m - ✅ Models package initialized successfully\n2025-07-14 22:28:20 - env - \u001b[32mINFO\u001b[0m - ✅ Environment package initialized successfully\n2025-07-14 22:28:20 - portfolio - \u001b[32mINFO\u001b[0m - ✅ Portfolio package initialized successfully\n2025-07-14 22:28:20 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: bayesian\n2025-07-14 22:28:20 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: genetic\n2025-07-14 22:28:20 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: grid_search\n2025-07-14 22:28:20 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: random_search\n2025-07-14 22:28:20 - optimization - \u001b[32mINFO\u001b[0m - Optimization engine initialized\n2025-07-14 22:28:20 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: bayesian\n2025-07-14 22:28:20 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: genetic\n2025-07-14 22:28:20 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: grid_search\n2025-07-14 22:28:20 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: random_search\n2025-07-14 22:28:20 - optimization - \u001b[32mINFO\u001b[0m - Optimization engine initialized\n2025-07-14 22:28:20 - optimization - \u001b[32mINFO\u001b[0m - ✅ Optimization package initialized successfully\n2025-07-14 22:28:21 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-14 22:28:23 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-14 22:28:23 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-14 22:28:23 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /health\n2025-07-14 22:28:23 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /status\n2025-07-14 22:28:23 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /models\n2025-07-14 22:28:23 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: POST /predict\n2025-07-14 22:28:23 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /portfolio\n2025-07-14 22:28:23 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /signals\n2025-07-14 22:28:23 - api - \u001b[32mINFO\u001b[0m - Middleware added: _rate_limit_middleware\n2025-07-14 22:28:23 - api - \u001b[32mINFO\u001b[0m - Middleware added: _logging_middleware\n2025-07-14 22:28:23 - api - \u001b[32mINFO\u001b[0m - API manager initialized on localhost:8000\n2025-07-14 22:28:23 - api - \u001b[32mINFO\u001b[0m - ✅ API package initialized successfully\n2025-07-14 22:28:23 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-14 22:28:24 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-14 22:28:24 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-14 22:28:27 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n2025-07-14 22:28:27 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\n2025-07-14 22:28:27 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-14 22:28:28 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-14 22:28:28 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-14 22:28:32 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n2025-07-14 22:28:32 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\n2025-07-14 22:28:32 - utils.adaptive_plutus_system - \u001b[32mINFO\u001b[0m - Adaptive Plutus System initialized\n2025-07-14 22:28:32 - utils.adaptive_plutus_system - \u001b[32mINFO\u001b[0m - Adaptive Plutus System initialized\n2025-07-14 22:28:32 - models.__init__ - \u001b[32mINFO\u001b[0m - ✅ Models package initialized successfully\n2025-07-14 22:28:32 - models.__init__ - \u001b[32mINFO\u001b[0m - ✅ Models package initialized successfully\n2025-07-14 22:28:32 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: model_performance\n2025-07-14 22:28:32 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: strategy_performance\n2025-07-14 22:28:32 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: risk_assessment\n2025-07-14 22:28:32 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: prediction_accuracy\n2025-07-14 22:28:32 - evaluation - \u001b[32mINFO\u001b[0m - Evaluation engine initialized\n2025-07-14 22:28:32 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: model_performance\n2025-07-14 22:28:32 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: strategy_performance\n2025-07-14 22:28:32 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: risk_assessment\n2025-07-14 22:28:32 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: prediction_accuracy\n2025-07-14 22:28:32 - evaluation - \u001b[32mINFO\u001b[0m - Evaluation engine initialized\n2025-07-14 22:28:32 - evaluation - \u001b[32mINFO\u001b[0m - ✅ Evaluation package initialized successfully\ncollected 0 items\n\n============================ no tests ran in 0.01s ============================\n", "error": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\pytest_asyncio\\plugin.py:208: PytestDeprecationWarning: The configuration option \"asyncio_default_fixture_loop_scope\" is unset.\nThe event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: \"function\", \"class\", \"module\", \"package\", \"session\"\n\n  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))\nINFO:core.memory_manager:Memory monitoring started\nINFO:core.order_manager:Order cleanup thread started\nINFO:core.model_versioning:Loaded 3 models from registry\nINFO:core.advanced_risk_metrics:Advanced Risk Calculator initialized\nINFO:core.correlation_analysis:Advanced Correlation Analyzer initialized\nINFO:core.backtesting_framework:Advanced Backtesting Engine initialized\nINFO:core.enhanced_error_handling.global:Circuit breaker 'default' registered\nINFO:core.enhanced_error_handling.global:Circuit breaker 'api_calls' registered\nINFO:core.enhanced_error_handling.global:Retry handler 'default' registered\nINFO:core.enhanced_error_handling.global:Retry handler 'network' registered\nINFO:core.utils:✅ Performance monitoring started\nINFO:core.utils:✅ System monitoring setup completed\n\u001b[32mINFO\u001b[0m:utils:✅ Utils package initialized successfully\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:پروکسی با موفقیت تنظیم شد.\nINFO:ModelManager:🤖 Initializing AI models...\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\nINFO:ai_models.SentimentEnsemble:✅ Sentiment ensemble initialized\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\nINFO:ai_models.SentimentEnsemble:✅ Sentiment ensemble initialized\nINFO:ai_models.ModelRegistry:✅ Model registered: sentiment_ensemble (sentiment_analysis)\nINFO:examples.plutus_integration_final:Plutus Integrated Trading System initialized\n\u001b[32mINFO\u001b[0m:utils.adaptive_plutus_system:Adaptive Plutus System initialized\nINFO:ai_models.TimeSeriesEnsemble:✅ Time series ensemble initialized\nINFO:examples.plutus_integration_final:Plutus Integrated Trading System initialized\n\u001b[32mINFO\u001b[0m:utils.adaptive_plutus_system:Adaptive Plutus System initialized\nINFO:ai_models.TimeSeriesEnsemble:✅ Time series ensemble initialized\nINFO:ai_models.ModelRegistry:✅ Model registered: timeseries_ensemble (time_series_forecasting)\nINFO:ModelManager:✅ All AI models initialized successfully\n\u001b[32mINFO\u001b[0m:models:✅ Models package initialized successfully\n\u001b[32mINFO\u001b[0m:env:✅ Environment package initialized successfully\n\u001b[32mINFO\u001b[0m:portfolio:✅ Portfolio package initialized successfully\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: bayesian\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: genetic\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: grid_search\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: random_search\n\u001b[32mINFO\u001b[0m:optimization:Optimization engine initialized\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: bayesian\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: genetic\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: grid_search\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: random_search\n\u001b[32mINFO\u001b[0m:optimization:Optimization engine initialized\n\u001b[32mINFO\u001b[0m:optimization:✅ Optimization package initialized successfully\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /health\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /status\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /models\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: POST /predict\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /portfolio\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /signals\n\u001b[32mINFO\u001b[0m:api:Middleware added: _rate_limit_middleware\n\u001b[32mINFO\u001b[0m:api:Middleware added: _logging_middleware\n\u001b[32mINFO\u001b[0m:api:API manager initialized on localhost:8000\n\u001b[32mINFO\u001b[0m:api:✅ API package initialized successfully\nINFO:core.error_handler:🔧 Recovery strategy registered for network_ConnectionError\nINFO:core.error_handler:🔧 Recovery strategy registered for model_ModelError\nINFO:ModelManager:🤖 Initializing AI models...\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\nINFO:ai_models.SentimentEnsemble:✅ Sentiment ensemble initialized\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\nINFO:ai_models.SentimentEnsemble:✅ Sentiment ensemble initialized\nINFO:ai_models.ModelRegistry:✅ Model registered: sentiment_ensemble (sentiment_analysis)\nINFO:examples.plutus_integration_final:Plutus Integrated Trading System initialized\n\u001b[32mINFO\u001b[0m:utils.adaptive_plutus_system:Adaptive Plutus System initialized\nINFO:ai_models.TimeSeriesEnsemble:✅ Time series ensemble initialized\nINFO:examples.plutus_integration_final:Plutus Integrated Trading System initialized\n\u001b[32mINFO\u001b[0m:utils.adaptive_plutus_system:Adaptive Plutus System initialized\nINFO:ai_models.TimeSeriesEnsemble:✅ Time series ensemble initialized\nINFO:ai_models.ModelRegistry:✅ Model registered: timeseries_ensemble (time_series_forecasting)\nINFO:ModelManager:✅ All AI models initialized successfully\n\u001b[32mINFO\u001b[0m:models.__init__:✅ Models package initialized successfully\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: model_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: strategy_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: risk_assessment\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: prediction_accuracy\n\u001b[32mINFO\u001b[0m:evaluation:Evaluation engine initialized\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: model_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: strategy_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: risk_assessment\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: prediction_accuracy\n\u001b[32mINFO\u001b[0m:evaluation:Evaluation engine initialized\n\u001b[32mINFO\u001b[0m:evaluation:✅ Evaluation package initialized successfully\nINFO:main_new:✅ AI Brain (Pearl-3x7B) module loaded successfully\nERROR:main_new:❌ Error initializing AI Brain supervisor: __init__() got an unexpected keyword argument 'name'\nERROR: file or directory not found: tests/test_integration_*.py\n\n", "timestamp": "2025-07-14T22:28:34.273561"}]}