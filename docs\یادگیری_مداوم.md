# مستند جامع: ContinualLearning

## مسئولیت
پیاده‌سازی یادگیری مداوم در مدل‌های RL برای جلوگیری از فراموشی فاجعه‌آمیز با استفاده از EWC، Experience Replay و Knowledge Distillation.

## پارامترها
- model: مدل اصلی RL
- ewc_lambda: ضریب اهمیت EWC
- replay_buffer_capacity: ظرفیت بافر تجربه
- distillation_temp: دمای تقطیر دانش
- distillation_weight: وزن تقطیر در تابع هزینه
- use_ewc/use_replay/use_distillation: فعال‌سازی روش‌ها

## متدهای کلیدی
- prepare_for_new_task: آماده‌سازی برای تسک جدید
- add_experience: افزودن تجربه به بافر
- update_model_loss: به‌روزرسانی تابع هزینه
- train_with_replay: آموزش با تجربیات گذشته
- evaluate_forgetting: ارزیابی میزان فراموشی

## نمونه کد
```python
from models.continual_learning import ContinualLearning
cl = ContinualLearning(model, ewc_lambda=0.4, use_ewc=True, use_replay=True)
cl.prepare_for_new_task('Task_1')
cl.add_experience((obs, action, reward, next_obs, done))
```

## مدیریت خطا
در صورت خطا در محاسبه Fisher matrix یا تقطیر دانش، خطا لاگ و عملیات متوقف می‌شود.

## بهترین شیوه
- قبل از شروع تسک جدید، prepare_for_new_task را فراخوانی کنید.
- تجربیات مهم را در بافر ذخیره کنید.

## نمودار
- نمودار فراموشی و عملکرد در طول تسک‌ها قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول در models/__init__.py و zero_shot_learning استفاده شده و در مثال‌ها فعال است.

## وضعیت عملیاتی
✅ عملیاتی و در جریان اصلی پروژه فعال است. 