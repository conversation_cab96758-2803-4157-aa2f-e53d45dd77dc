import os
import json
import pandas as pd
from utils.export import export_to_csv, export_to_json, export_to_excel

def test_export_to_csv(tmp_path):
    results = [
        {"symbol": "EURUSD", "win_rate": 0.6},
        {"symbol": "GBPUSD", "win_rate": 0.7},
    ]
    out = tmp_path / "results.csv"
    export_to_csv(results, str(out))
    df = pd.read_csv(out)
    assert set(df.columns) == {"symbol", "win_rate"}
    assert len(df) == 2

def test_export_to_json(tmp_path):
    results = [
        {"symbol": "EURUSD", "win_rate": 0.6},
        {"symbol": "GBPUSD", "win_rate": 0.7},
    ]
    out = tmp_path / "results.json"
    export_to_json(results, str(out))
    with open(out, encoding="utf-8") as f:
        data = json.load(f)
    assert isinstance(data, list)
    assert data[0]["symbol"] == "EURUSD"

def test_export_to_excel(tmp_path):
    results = [
        {"symbol": "EURUSD", "win_rate": 0.6},
        {"symbol": "GBPUSD", "win_rate": 0.7},
    ]
    out = tmp_path / "results.xlsx"
    export_to_excel(results, str(out))
    df = pd.read_excel(out)
    assert set(df.columns) == {"symbol", "win_rate"}
    assert len(df) == 2
