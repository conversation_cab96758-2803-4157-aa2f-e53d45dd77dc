#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔌 Circuit Breaker System
سیستم سراسری Circuit Breaker برای تمام سرویس‌ها با قابلیت health-check و بازیابی خودکار.
این ماژول بر روی قابلیت‌های CircuitBreaker و RetryHandler موجود در core.enhanced_error_handling سوار می‌شود
و یک لایهٔ مدیریتی برای ثبت، مانیتور و استفاده از circuit breakerها فراهم می‌کند.
"""

from __future__ import annotations

import asyncio
import logging
import time
from contextlib import asynccontextmanager, contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from types import TracebackType
from typing import Any, Awaitable, Callable, Dict, Optional, Type

try:
    # Import primitives from the enhanced error handling module
    from core.enhanced_error_handling import (
        CircuitBreaker,
        CircuitBreakerConfig,
        CircuitState,
        Re<PERSON><PERSON><PERSON><PERSON>,
        RetryConfig,
        with_circuit_breaker,
        with_retry,
        ErrorSeverity,
    )

    _ENHANCED_AVAILABLE = True
except ImportError:  # pragma: no cover – fallback if user removed the package
    _ENHANCED_AVAILABLE = False

    # Minimal fallbacks to avoid hard crashes during static analysis / unit tests
    class CircuitState:  # type: ignore
        CLOSED = "closed"
        OPEN = "open"
        HALF_OPEN = "half_open"

    @dataclass
    class CircuitBreakerConfig:  # type: ignore
        failure_threshold: int = 5
        recovery_timeout: int = 30
        half_open_success_threshold: int = 3

    class CircuitBreaker:  # type: ignore
        def __init__(self, name: str, config: CircuitBreakerConfig):
            self.name = name
            self.state = CircuitState.CLOSED
            self.config = config
            self._failure_count = 0
            self._success_count = 0
            self._opened_at: Optional[float] = None

        def record_success(self):
            self._failure_count = 0
            if self.state == CircuitState.HALF_OPEN:
                self._success_count += 1
                if self._success_count >= self.config.half_open_success_threshold:
                    self.state = CircuitState.CLOSED
                    self._success_count = 0

        def record_failure(self):
            self._failure_count += 1
            if self._failure_count >= self.config.failure_threshold:
                self.state = CircuitState.OPEN
                self._opened_at = time.time()

        def allow_request(self):
            if self.state == CircuitState.OPEN and self._opened_at is not None:
                if time.time() - self._opened_at >= self.config.recovery_timeout:
                    self.state = CircuitState.HALF_OPEN
            return self.state != CircuitState.OPEN

    RetryHandler = None  # type: ignore
    RetryConfig = None  # type: ignore
    with_circuit_breaker = lambda cb: (lambda func: func)  # type: ignore
    with_retry = lambda *a, **k: (lambda f: f)  # type: ignore
    ErrorSeverity = str  # type: ignore

logger = logging.getLogger(__name__)

@dataclass
class BreakerMetrics:
    """Metrics for each circuit breaker"""
    name: str
    state: str
    failure_count: int
    success_count: int
    opened_at: Optional[datetime]
    reset_timeout: int
    last_state_change: datetime = field(default_factory=datetime.utcnow)

class CircuitBreakerManager:
    """Central registry and monitor for circuit breakers."""

    def __init__(self):
        self._breakers: Dict[str, CircuitBreaker] = {}
        self._lock = asyncio.Lock()
        self._monitor_task: Optional[asyncio.Task] = None
        self._running = False

    # ------------------------------------------------------------------
    # Registry helpers
    # ------------------------------------------------------------------
    def register_breaker(self, name: str, config: Optional[CircuitBreakerConfig] = None) -> CircuitBreaker:
        if name in self._breakers:
            return self._breakers[name]
        if config is None:
            config = CircuitBreakerConfig()
        
        if _ENHANCED_AVAILABLE:
            # The enhanced CircuitBreaker seems to take the name in its constructor
            breaker = CircuitBreaker(name)
            # Monkey-patch state for compatibility with tests if it's missing
            if not hasattr(breaker, 'state'):
                 # We assume the library uses 'current_state'
                breaker.state = breaker.current_state
        else:
            breaker = CircuitBreaker(name, config)
            
        self._breakers[name] = breaker
        logger.info(f"✅ Circuit breaker registered: {name}")
        return breaker

    def get_breaker(self, name: str) -> Optional[CircuitBreaker]:
        return self._breakers.get(name)

    # ------------------------------------------------------------------
    # Monitoring loop
    # ------------------------------------------------------------------
    async def start_monitoring(self, interval: int = 5):
        if self._running:
            return
        self._running = True
        logger.info("🚦 Circuit breaker monitoring started")
        self._monitor_task = asyncio.create_task(self._monitor_loop(interval))

    async def stop_monitoring(self):
        self._running = False
        if self._monitor_task:
            self._monitor_task.cancel()
            with contextlib.suppress(Exception):
                await self._monitor_task
            self._monitor_task = None
        logger.info("🛑 Circuit breaker monitoring stopped")

    async def _monitor_loop(self, interval: int):
        while self._running:
            await asyncio.sleep(interval)
            await self._log_metrics()

    # ------------------------------------------------------------------
    # Metrics
    # ------------------------------------------------------------------
    async def _log_metrics(self):
        for name, breaker in self._breakers.items():
            metrics = self._collect_metrics(breaker)
            logger.debug(
                f"[CB] {name} | state={metrics.state} failures={metrics.failure_count} "
                f"opened_at={metrics.opened_at}"
            )

    def _collect_metrics(self, breaker: CircuitBreaker) -> BreakerMetrics:
        return BreakerMetrics(
            name=breaker.name,
            state=str(breaker.state),
            failure_count=getattr(breaker, "_failure_count", 0),
            success_count=getattr(breaker, "_success_count", 0),
            opened_at=datetime.utcfromtimestamp(breaker._opened_at) if getattr(breaker, "_opened_at", None) else None,
            reset_timeout=breaker.config.recovery_timeout,
        )

    def describe_all(self) -> Dict[str, BreakerMetrics]:
        return {name: self._collect_metrics(b) for name, b in self._breakers.items()}

# ----------------------------------------------------------------------
# Helper decorators
# ----------------------------------------------------------------------

_CB_MANAGER: Optional[CircuitBreakerManager] = None


def get_circuit_breaker_manager() -> CircuitBreakerManager:
    global _CB_MANAGER
    if _CB_MANAGER is None:
        _CB_MANAGER = CircuitBreakerManager()
    return _CB_MANAGER


def circuit_breaker(name: str = "default"):
    """Decorator to wrap sync/async functions with named circuit breaker."""
    manager = get_circuit_breaker_manager()
    breaker = manager.register_breaker(name)

    def decorator(func: Callable):
        # Use existing helper from enhanced_error_handling if available
        wrapped = with_circuit_breaker(breaker)(func) if _ENHANCED_AVAILABLE else func
        return wrapped

    return decorator

# ----------------------------------------------------------------------
# Context manager utilities
# ----------------------------------------------------------------------

@contextmanager
def circuit_context(name: str = "default"):
    breaker = get_circuit_breaker_manager().register_breaker(name)
    if _ENHANCED_AVAILABLE:
        # The enhanced breaker should be a context manager itself
        with breaker:
            yield
    else:
        # Fallback implementation
        if not breaker.allow_request():
            raise RuntimeError(f"Circuit {name} is open – request blocked")
        try:
            yield
            breaker.record_success()
        except Exception:
            breaker.record_failure()
            raise

# ----------------------------------------------------------------------
# Async context manager for service calls
# ----------------------------------------------------------------------

@asynccontextmanager
async def circuit_async(name: str = "default"):
    breaker = get_circuit_breaker_manager().register_breaker(name)
    if _ENHANCED_AVAILABLE:
        # The enhanced breaker should support async context management
        async with breaker:
            yield
    else:
        # Fallback implementation
        if not breaker.allow_request():
            raise RuntimeError(f"Circuit {name} is open – request blocked")
        try:
            yield
            breaker.record_success()
        except Exception:
            breaker.record_failure()
            raise
