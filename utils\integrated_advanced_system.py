"""
Integrated Advanced Trading System
سیستم معاملاتی پیشرفته یکپارچه

این سیستم تمام ویژگی‌های پیشرفته را ترکیب می‌کند:
1. Market Regime Detection
2. Advanced Reinforcement Learning
3. Multi-Step Prediction
4. Adaptive Learning
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import pandas as pd
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict

from utils.enhanced_adaptive_plutus import EnhancedAdaptivePlutusSystem
from utils.advanced_rl_agent import AdvancedRLTradingSystem, MarketState, TradingAction
from utils.multi_step_prediction_fixed import MultiStepPredictionSystem

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class IntegratedSignal:
    """سیگنال یکپارچه از تمام سیستم‌ها"""
    symbol: str
    timestamp: datetime
    
    # Regime Detection
    current_regime: str
    regime_confidence: float
    
    # RL Agent
    rl_action: str
    rl_confidence: float
    rl_position_size: float
    
    # Multi-Step Predictions
    short_term_direction: str  # 1h, 4h
    long_term_direction: str   # 12h, 24h
    prediction_confidence: float
    ensemble_agreement: float
    
    # Adaptive Learning
    adaptive_weights: Dict
    learning_confidence: float
    
    # Final Recommendation
    final_action: str
    final_confidence: float
    risk_level: str
    position_size: float
    stop_loss: float
    take_profit: float

class IntegratedAdvancedTradingSystem:
    """سیستم معاملاتی پیشرفته یکپارچه"""
    
    def __init__(self, db_path: str = "integrated_system.db"):
        self.db_path = db_path
        
        # Initialize subsystems
        logger.info("Initializing integrated advanced trading system...")
        
        self.adaptive_system = EnhancedAdaptivePlutusSystem(f"{db_path}_adaptive.db")
        self.rl_system = AdvancedRLTradingSystem(f"{db_path}_rl.db")
        self.prediction_system = MultiStepPredictionSystem(f"{db_path}_predictions.db")
        
        # System weights
        self.system_weights = {
            'regime_weight': 0.25,
            'rl_weight': 0.30,
            'prediction_weight': 0.25,
            'adaptive_weight': 0.20
        }
        
        # Performance tracking
        self.performance_history = []
        self.signal_history = []
        
        logger.info("Integrated system initialized successfully")
    
    def initialize_with_data(self, symbols: List[str], historical_data: Dict[str, pd.DataFrame]) -> bool:
        """راه‌اندازی سیستم با داده‌های تاریخی"""
        try:
            logger.info("Initializing all subsystems with historical data...")
            
            # Initialize regime detection
            regime_success = self.adaptive_system.initialize_regime_detection(historical_data)
            if not regime_success:
                logger.error("Failed to initialize regime detection")
                return False
            
            # Train prediction models
            for symbol, data in historical_data.items():
                self.prediction_system.train_models(symbol, data)
            
            # Initialize RL system with sample experiences
            self._initialize_rl_with_samples(historical_data)
            
            logger.info("All subsystems initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing integrated system: {str(e)}")
            return False
    
    def _initialize_rl_with_samples(self, historical_data: Dict[str, pd.DataFrame]):
        """راه‌اندازی RL با نمونه‌های اولیه"""
        for symbol, data in historical_data.items():
            if len(data) < 100:
                continue
            
            # Create sample experiences
            sample_size = min(50, len(data) // 10)
            for i in range(sample_size):
                try:
                    idx = np.random.randint(50, len(data) - 10)
                    current_data = data.iloc[idx]
                    future_data = data.iloc[idx + 5]
                    
                    # Create market state
                    market_data = {
                        'price_trend': (current_data['close'] - data.iloc[idx-5]['close']) / data.iloc[idx-5]['close'],
                        'volatility': data['close'].iloc[idx-20:idx].pct_change().std(),
                        'volume_trend': 0.5,
                        'rsi': 50.0,
                        'regime': 'sideways_market',
                        'signal_confidence': 0.6,
                        'recent_performance': 0.0
                    }
                    
                    state = self.rl_system.create_market_state(market_data)
                    action = self.rl_system.get_optimal_action(state, training=True)
                    
                    # Calculate reward
                    price_change = (future_data['close'] - current_data['close']) / current_data['close']
                    market_outcome = {'pnl': price_change * 100, 'price_change': price_change}
                    reward = self.rl_system.calculate_reward(action, market_outcome)
                    
                    # Create next state
                    next_market_data = market_data.copy()
                    next_market_data['recent_performance'] = reward / 10
                    next_state = self.rl_system.create_market_state(next_market_data)
                    
                    # Add experience
                    self.rl_system.add_experience(state, action, reward, next_state, False, symbol)
                    
                except Exception as e:
                    continue
            
            # Train RL agent
            if self.rl_system.replay_buffer.size() > 10:
                self.rl_system.train_agent(batch_size=10, episodes=3)
    
    def get_integrated_signal(self, symbol: str, data: pd.DataFrame, current_price: float) -> IntegratedSignal:
        """دریافت سیگنال یکپارچه از تمام سیستم‌ها"""
        try:
            logger.info(f"Generating integrated signal for {symbol}")
            
            # 1. Get regime-aware signal
            regime_signal = self.adaptive_system.get_regime_aware_signal(symbol, "H1")
            
            # 2. Get RL recommendation
            rl_recommendation = self._get_rl_recommendation(symbol, data, current_price, regime_signal)
            
            # 3. Get multi-step predictions
            prediction_signals = self.prediction_system.generate_trading_signals(symbol, data, current_price)
            
            # 4. Get adaptive weights
            try:
                adaptive_weights = self.adaptive_system.get_adaptive_weights(symbol, "H1")
            except AttributeError:
                adaptive_weights = {'confidence_multiplier': 1.0}
            
            # Extract information
            regime_info = regime_signal.get("regime_info", {})
            recommendation = regime_signal.get("recommendation", {})
            
            current_regime = regime_info.get("current_regime", "unknown")
            regime_confidence = regime_info.get("regime_confidence", 0.5)
            
            rl_action = rl_recommendation.get("recommended_action", "hold")
            rl_confidence = rl_recommendation.get("confidence", 0.5)
            rl_position_size = rl_recommendation.get("position_size", 0.1)
            
            # Multi-step prediction analysis
            if 'error' not in prediction_signals:
                predictions = prediction_signals.get("individual_predictions", {})
                short_term_up = 0
                short_term_down = 0
                long_term_up = 0
                long_term_down = 0
                
                for horizon, pred in predictions.items():
                    direction = pred.get('direction', 'neutral')
                    confidence = pred.get('confidence', 0.5)
                    
                    if horizon in ['1h', '4h']:
                        if direction == 'up':
                            short_term_up += confidence
                        elif direction == 'down':
                            short_term_down += confidence
                    else:  # 12h, 24h
                        if direction == 'up':
                            long_term_up += confidence
                        elif direction == 'down':
                            long_term_down += confidence
                
                short_term_direction = 'up' if short_term_up > short_term_down else 'down'
                long_term_direction = 'up' if long_term_up > long_term_down else 'down'
                prediction_confidence = prediction_signals.get("overall_confidence", 0.5)
                ensemble_agreement = prediction_signals.get("ensemble_agreement", 0.5)
            else:
                short_term_direction = 'neutral'
                long_term_direction = 'neutral'
                prediction_confidence = 0.5
                ensemble_agreement = 0.5
            
            # Combine all signals
            final_decision = self._combine_signals(
                regime_signal, rl_recommendation, prediction_signals, adaptive_weights
            )
            
            # Create integrated signal
            integrated_signal = IntegratedSignal(
                symbol=symbol,
                timestamp=datetime.now(),
                current_regime=current_regime,
                regime_confidence=regime_confidence,
                rl_action=rl_action,
                rl_confidence=rl_confidence,
                rl_position_size=rl_position_size,
                short_term_direction=short_term_direction,
                long_term_direction=long_term_direction,
                prediction_confidence=prediction_confidence,
                ensemble_agreement=ensemble_agreement,
                adaptive_weights=adaptive_weights,
                learning_confidence=adaptive_weights.get('confidence_multiplier', 1.0),
                final_action=final_decision['action'],
                final_confidence=final_decision['confidence'],
                risk_level=final_decision['risk_level'],
                position_size=final_decision['position_size'],
                stop_loss=final_decision['stop_loss'],
                take_profit=final_decision['take_profit']
            )
            
            # Store signal
            self.signal_history.append(integrated_signal)
            
            return integrated_signal
            
        except Exception as e:
            logger.error(f"Error generating integrated signal: {str(e)}")
            
            # Return default signal
            return IntegratedSignal(
                symbol=symbol,
                timestamp=datetime.now(),
                current_regime="unknown",
                regime_confidence=0.5,
                rl_action="hold",
                rl_confidence=0.5,
                rl_position_size=0.1,
                short_term_direction="neutral",
                long_term_direction="neutral",
                prediction_confidence=0.5,
                ensemble_agreement=0.5,
                adaptive_weights={},
                learning_confidence=0.5,
                final_action="HOLD",
                final_confidence=0.5,
                risk_level="medium",
                position_size=0.1,
                stop_loss=0.02,
                take_profit=0.04
            )
    
    def _get_rl_recommendation(self, symbol: str, data: pd.DataFrame, current_price: float, regime_signal: Dict) -> Dict:
        """دریافت توصیه از RL Agent"""
        try:
            # Create market state for RL
            regime_info = regime_signal.get("regime_info", {})
            combined_signal = regime_signal.get("combined_signal", {})
            
            market_data = {
                'price_trend': data['close'].pct_change(5).iloc[-1] if len(data) > 5 else 0.0,
                'volatility': data['close'].pct_change().rolling(20).std().iloc[-1] if len(data) > 20 else 0.5,
                'volume_trend': 0.5,  # placeholder
                'rsi': 50.0,  # placeholder
                'regime': regime_info.get('current_regime', 'sideways_market'),
                'signal_confidence': combined_signal.get('confidence', 0.5),
                'recent_performance': 0.0
            }
            
            return self.rl_system.generate_trading_recommendation(market_data)
            
        except Exception as e:
            logger.error(f"Error getting RL recommendation: {str(e)}")
            return {
                'recommended_action': 'hold',
                'confidence': 0.5,
                'position_size': 0.1
            }
    
    def _combine_signals(self, regime_signal: Dict, rl_recommendation: Dict, 
                        prediction_signals: Dict, adaptive_weights: Dict) -> Dict:
        """ترکیب سیگنال‌ها برای تصمیم نهایی"""
        
        # Extract actions and confidences
        regime_action = regime_signal.get("recommendation", {}).get("action", "HOLD")
        regime_confidence = regime_signal.get("regime_info", {}).get("regime_confidence", 0.5)
        
        rl_action = rl_recommendation.get("recommended_action", "hold")
        rl_confidence = rl_recommendation.get("confidence", 0.5)
        
        if 'error' not in prediction_signals:
            prediction_action = prediction_signals.get("recommended_action", "HOLD")
            prediction_confidence = prediction_signals.get("overall_confidence", 0.5)
        else:
            prediction_action = "HOLD"
            prediction_confidence = 0.5
        
        adaptive_confidence = adaptive_weights.get('confidence_multiplier', 1.0)
        
        # Convert actions to scores
        action_scores = {'BUY': 0, 'SELL': 0, 'HOLD': 0}
        
        # Regime signal
        if regime_action in action_scores:
            action_scores[regime_action] += self.system_weights['regime_weight'] * regime_confidence
        
        # RL signal
        rl_action_mapped = rl_action.upper() if rl_action.upper() in action_scores else 'HOLD'
        action_scores[rl_action_mapped] += self.system_weights['rl_weight'] * rl_confidence
        
        # Prediction signal
        if prediction_action in action_scores:
            action_scores[prediction_action] += self.system_weights['prediction_weight'] * prediction_confidence
        
        # Adaptive weight boost
        max_score_action = max(action_scores, key=action_scores.get)
        action_scores[max_score_action] += self.system_weights['adaptive_weight'] * adaptive_confidence
        
        # Determine final action
        final_action = max(action_scores, key=action_scores.get)
        final_confidence = action_scores[final_action] / sum(self.system_weights.values())
        
        # Risk management
        risk_level = self._assess_risk_level(regime_signal, prediction_signals)
        position_size = self._calculate_position_size(final_confidence, risk_level)
        stop_loss, take_profit = self._calculate_risk_levels(risk_level, final_action)
        
        return {
            'action': final_action,
            'confidence': min(1.0, final_confidence),
            'risk_level': risk_level,
            'position_size': position_size,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'action_scores': action_scores
        }
    
    def _assess_risk_level(self, regime_signal: Dict, prediction_signals: Dict) -> str:
        """ارزیابی سطح ریسک"""
        regime_info = regime_signal.get("regime_info", {})
        current_regime = regime_info.get("current_regime", "unknown")
        
        if current_regime == "high_volatility":
            return "very_high"
        elif current_regime == "bear_market":
            return "high"
        elif current_regime == "bull_market":
            return "medium"
        elif current_regime == "low_volatility":
            return "low"
        else:
            return "medium"
    
    def _calculate_position_size(self, confidence: float, risk_level: str) -> float:
        """محاسبه اندازه موقعیت"""
        base_size = 0.1
        
        # Confidence adjustment
        confidence_factor = confidence
        
        # Risk adjustment
        risk_factors = {
            "very_low": 1.2,
            "low": 1.1,
            "medium": 1.0,
            "high": 0.8,
            "very_high": 0.6
        }
        
        risk_factor = risk_factors.get(risk_level, 1.0)
        
        final_size = base_size * confidence_factor * risk_factor
        return min(0.25, max(0.01, final_size))  # Between 1% and 25%
    
    def _calculate_risk_levels(self, risk_level: str, action: str) -> tuple:
        """محاسبه سطوح stop loss و take profit"""
        base_stop_loss = 0.02
        base_take_profit = 0.04
        
        risk_multipliers = {
            "very_low": 0.7,
            "low": 0.8,
            "medium": 1.0,
            "high": 1.3,
            "very_high": 1.5
        }
        
        multiplier = risk_multipliers.get(risk_level, 1.0)
        
        stop_loss = base_stop_loss * multiplier
        take_profit = base_take_profit * multiplier
        
        return stop_loss, take_profit
    
    def get_system_status(self) -> Dict:
        """دریافت وضعیت کلی سیستم"""
        try:
            rl_stats = self.rl_system.get_agent_statistics()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'adaptive_system_status': 'operational',
                'rl_system_status': {
                    'training_episodes': rl_stats['training_episodes'],
                    'exploration_rate': rl_stats['exploration_rate'],
                    'q_table_size': rl_stats['q_table_size']
                },
                'prediction_system_status': 'operational',
                'total_signals_generated': len(self.signal_history),
                'system_weights': self.system_weights,
                'recent_signals': len([s for s in self.signal_history if (datetime.now() - s.timestamp).seconds < 3600])
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {str(e)}")
            return {'error': str(e)}
    
    def save_all_systems(self):
        """ذخیره تمام سیستم‌ها"""
        try:
            self.rl_system.save_system()
            # Other systems save automatically
            logger.info("All systems saved successfully")
        except Exception as e:
            logger.error(f"Error saving systems: {str(e)}")

def main():
    """تست سیستم یکپارچه"""
    print("Integrated Advanced Trading System Test")
    print("=" * 50)
    
    # Create integrated system
    integrated_system = IntegratedAdvancedTradingSystem("test_integrated.db")
    
    # Create sample data
    from tests.test_plutus_models_comprehensive import PlutusModelTester
    model_tester = PlutusModelTester()
    
    symbols = ["EURUSD", "GBPUSD"]
    historical_data = {}
    
    print("Loading historical data...")
    for symbol in symbols:
        data = model_tester.load_real_project_data(symbol, "H1")
        if not data.empty:
            historical_data[symbol] = data
            print(f"  {symbol}: {len(data)} records loaded")
    
    if not historical_data:
        print("No historical data available")
        return
    
    # Initialize system
    print("\nInitializing integrated system...")
    success = integrated_system.initialize_with_data(symbols, historical_data)
    
    if not success:
        print("Failed to initialize integrated system")
        return
    
    print("✅ Integrated system initialized successfully")
    
    # Test integrated signals
    print("\nGenerating integrated signals...")
    for symbol in symbols:
        data = historical_data[symbol]
        current_price = data['close'].iloc[-1]
        
        print(f"\n{symbol} Analysis:")
        print(f"  Current price: {current_price:.5f}")
        
        # Get integrated signal
        signal = integrated_system.get_integrated_signal(symbol, data, current_price)
        
        print(f"  Current regime: {signal.current_regime} ({signal.regime_confidence:.1%})")
        print(f"  RL recommendation: {signal.rl_action} ({signal.rl_confidence:.1%})")
        print(f"  Short-term direction: {signal.short_term_direction}")
        print(f"  Long-term direction: {signal.long_term_direction}")
        print(f"  Final action: {signal.final_action}")
        print(f"  Final confidence: {signal.final_confidence:.1%}")
        print(f"  Position size: {signal.position_size:.1%}")
        print(f"  Risk level: {signal.risk_level}")
    
    # System status
    print(f"\nSystem Status:")
    status = integrated_system.get_system_status()
    
    if 'error' not in status:
        print(f"  Total signals generated: {status['total_signals_generated']}")
        print(f"  RL training episodes: {status['rl_system_status']['training_episodes']}")
        print(f"  RL exploration rate: {status['rl_system_status']['exploration_rate']:.3f}")
        print(f"  Recent signals (1h): {status['recent_signals']}")
    
    # Save systems
    integrated_system.save_all_systems()
    
    print(f"\n✅ Integrated Advanced Trading System test completed!")

if __name__ == "__main__":
    main() 