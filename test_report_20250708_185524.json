{"available": 0, "loadable": 0, "failed": 36, "total": 36, "details": {"amazon/chronos-bolt-mini": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "amazon/chronos-bolt-small": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "amazon/chronos-bolt-tiny": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "amazon/chronos-bolt-base": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "amazon/chronos-t5-large": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "amazon/chronos-t5-small": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "amazon/chronos-t5-base": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "amazon/chronos-t5-mini": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "ProsusAI/finbert": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "amazon/chronos-t5-tiny": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "Salesforce/moirai-1.0-R-base": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "ElKulako/cryptobert": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "google/timesfm-1.0-200m": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "bilalzafar/FinAI-BERT": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "nlptown/bert-base-multilingual-uncased-sentiment": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "zhayunduo/roberta-base-stocktwits-finetuned": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "StephanAkkerman/FinTwitBERT-sentiment": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "cardiffnlp/twitter-roberta-base-sentiment-latest": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "FinGPT/fingpt-forecaster_dow30_llama2-7b_lora": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "ChanceFocus/finma-7b-full": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "bavest/fin-llama-33b-merged": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "arcee-ai/Llama-3-SEC-Base": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "ChanceFocus/finma-7b-nlp": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "microsoft/layoutlmv3-base": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "google/pix2struct-base": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "microsoft/table-transformer-structure-recognition": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "facebook/bart-large-cnn": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "allenai/longformer-base-4096": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "microsoft/layoutlmv2-base-uncased": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "google/pegasus-xsum": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "EleutherAI/gpt-neo-1.3B": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "microsoft/dit-base-finetuned-rvlcdip": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "facebook/opt-1.3b": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "sentence-transformers/all-MiniLM-L6-v2": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "microsoft/prophetnet-large-uncased": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}, "microsoft/codebert-base": {"available": false, "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url:", "downloads": 0, "likes": 0}}}