"""
Advanced Reward Redistribution System Example

این مثال نحوه استفاده از سیستم پیشرفته تعدیل پاداش را نشان می‌دهد.
شامل:
- استفاده از استراتژی‌های مختلف
- سیستم حافظه و پیش‌بینی
- نظارت بر کیفیت پاداش
- بهینه‌سازی استراتژی
"""

import numpy as np
import matplotlib.pyplot as plt

from utils.reward_redistribution import (
    create_advanced_redistributor,
    RewardOptimizer
)


def simulate_trading_session():
    """شبیه‌سازی یک جلسه معاملاتی با drawdown و recovery"""
    
    print("=== Advanced Reward Redistribution System Demo ===\n")
    
    # ایجاد redistributor با استراتژی adaptive
    redistributor = create_advanced_redistributor(
        'adaptive',
        max_multiplier=4.0,
        min_multiplier=0.2,
        enable_prediction=True,
        enable_quality_monitoring=True,
        enable_risk_adjustment=True
    )
    
    # شبیه‌سازی داده‌های معاملاتی
    np.random.seed(42)
    initial_capital = 10000
    
    # متغیرهای ذخیره‌سازی
    equity_curve = [float(initial_capital)]
    raw_rewards = []
    redistributed_rewards = []
    actions = []
    timestamps = []
    
    # فازهای مختلف بازار
    phases = [
        ('trending_up', 100),    # روند صعودی
        ('volatile', 100),       # نوسانی
        ('drawdown', 100),       # افت
        ('recovery', 100),       # بازیابی
        ('stable', 100)          # ثبات
    ]
    
    current_equity = initial_capital
    step = 0
    
    for phase, duration in phases:
        print(f"\nPhase: {phase.upper()} ({duration} steps)")
        
        for _ in range(duration):
            # تولید action تصادفی
            action = np.random.choice(['buy', 'sell', 'hold'], p=[0.4, 0.4, 0.2])
            actions.append(action)
            
            # تولید reward بر اساس فاز
            if phase == 'trending_up':
                base_reward = np.random.normal(20, 10)
            elif phase == 'volatile':
                base_reward = np.random.normal(0, 30)
            elif phase == 'drawdown':
                base_reward = np.random.normal(-15, 15)
            elif phase == 'recovery':
                base_reward = np.random.normal(10, 12)
            else:  # stable
                base_reward = np.random.normal(5, 5)
            
            # Context بازار
            context = {
                'volatility': abs(np.random.normal(0.02, 0.01)),
                'trend': np.random.normal(0.05 if phase == 'trending_up' else -0.05 if phase == 'drawdown' else 0, 0.02),
                'volume': abs(np.random.normal(1000, 200)),
                'spread': abs(np.random.normal(0.001, 0.0002))
            }
            
            # تعدیل پاداش
            adjusted_reward = redistributor.redistribute_single(
                base_reward,
                equity_curve,
                action=action,
                additional_context=context
            )
            
            # ذخیره داده‌ها
            raw_rewards.append(base_reward)
            redistributed_rewards.append(adjusted_reward)
            timestamps.append(step)
            
            # به‌روزرسانی equity
            current_equity += adjusted_reward
            equity_curve.append(current_equity)
            
            # بهینه‌سازی دوره‌ای استراتژی
            if step % 100 == 0 and step > 0:
                print(f"\nOptimizing strategy at step {step}...")
                result = redistributor.optimize_strategy()
                print(f"Optimization result: {result['status']}")
                if result['status'] == 'optimized':
                    print(f"New strategy: {result['new_strategy']}")
            
            step += 1
    
    # پیش‌بینی پاداش‌های آینده
    print("\n=== Reward Prediction ===")
    future_context = {
        'volatility': 0.015,
        'trend': 0.02,
        'volume': 1100,
        'spread': 0.0012
    }
    predicted_reward = redistributor.predict_next_reward(future_context)
    print(f"Predicted next reward: {predicted_reward:.2f}")
    
    # خلاصه عملکرد
    print("\n=== Performance Summary ===")
    summary = redistributor.get_performance_summary()
    
    print(f"\nRedistribution Statistics:")
    print(f"  Total redistributions: {summary['redistribution_stats']['count']}")
    print(f"  Average multiplier: {summary['redistribution_stats']['avg_multiplier']:.3f}")
    print(f"  Total original rewards: {summary['redistribution_stats']['total_original']:.2f}")
    print(f"  Total redistributed rewards: {summary['redistribution_stats']['total_redistributed']:.2f}")
    
    print(f"\nMemory Statistics:")
    print(f"  Total records: {summary['memory_stats']['total_records']}")
    print(f"  Average performance: {summary['memory_stats']['avg_performance']:.2f}")
    print(f"  Drawdown stats: {summary['memory_stats']['drawdown_stats']}")
    
    print(f"\nQuality Monitoring:")
    print(f"  Current quality: {summary['quality_stats']['current_quality']:.3f}")
    print(f"  Quality trend: {summary['quality_stats']['quality_trend']}")
    print(f"  Is acceptable: {summary['quality_stats']['is_acceptable']}")
    
    # Visualization
    create_visualizations(
        timestamps, equity_curve, raw_rewards, 
        redistributed_rewards, phases
    )
    
    return {
        'equity_curve': equity_curve,
        'raw_rewards': raw_rewards,
        'redistributed_rewards': redistributed_rewards,
        'summary': summary
    }


def create_visualizations(timestamps, equity_curve, raw_rewards, 
                         redistributed_rewards, phases):
    """ایجاد نمودارهای تحلیلی"""
    
    fig, axes = plt.subplots(3, 1, figsize=(12, 10))
    
    # محاسبه drawdown
    peak = np.maximum.accumulate(equity_curve[1:])
    drawdown = (peak - equity_curve[1:]) / peak * 100
    
    # نمودار 1: Equity Curve و Drawdown
    ax1 = axes[0]
    ax1_dd = ax1.twinx()
    
    ax1.plot(timestamps, equity_curve[1:], 'b-', linewidth=2, label='Equity')
    ax1_dd.fill_between(timestamps, drawdown, alpha=0.3, color='red', label='Drawdown %')
    ax1_dd.plot(timestamps, drawdown, 'r--', alpha=0.7)
    
    # نشان دادن فازها
    phase_start = 0
    colors = ['green', 'orange', 'red', 'blue', 'gray']
    for i, (phase, duration) in enumerate(phases):
        ax1.axvspan(phase_start, phase_start + duration, alpha=0.1, 
                   color=colors[i], label=phase)
        phase_start += duration
    
    ax1.set_ylabel('Equity ($)', fontsize=12)
    ax1_dd.set_ylabel('Drawdown (%)', fontsize=12)
    ax1.set_title('Equity Curve with Market Phases', fontsize=14)
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')
    
    # نمودار 2: مقایسه پاداش‌ها
    ax2 = axes[1]
    ax2.plot(timestamps, np.cumsum(raw_rewards), 'r-', alpha=0.7, 
             label='Cumulative Raw Rewards')
    ax2.plot(timestamps, np.cumsum(redistributed_rewards), 'g-', linewidth=2,
             label='Cumulative Redistributed Rewards')
    ax2.set_ylabel('Cumulative Rewards ($)', fontsize=12)
    ax2.set_title('Raw vs Redistributed Rewards Comparison', fontsize=14)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # نمودار 3: ضریب تعدیل
    ax3 = axes[2]
    multipliers = np.array(redistributed_rewards) / (np.array(raw_rewards) + 1e-10)
    ax3.plot(timestamps, multipliers, 'purple', alpha=0.7, linewidth=1)
    ax3.axhline(y=1.0, color='black', linestyle='--', alpha=0.5)
    ax3.set_ylabel('Reward Multiplier', fontsize=12)
    ax3.set_xlabel('Time Step', fontsize=12)
    ax3.set_title('Dynamic Reward Adjustment Multiplier', fontsize=14)
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('advanced_reward_redistribution_analysis.png', dpi=300)
    plt.show()


def test_different_strategies():
    """مقایسه استراتژی‌های مختلف"""
    
    print("\n=== Strategy Comparison ===\n")
    
    strategies = ['linear', 'exponential', 'adaptive']
    results = {}
    
    # داده‌های تست یکسان
    np.random.seed(42)
    test_rewards = np.random.normal(0, 20, 200)
    test_equity = 10000 + np.cumsum(test_rewards)
    
    for strategy in strategies:
        print(f"\nTesting {strategy.upper()} strategy...")
        
        redistributor = create_advanced_redistributor(strategy)
        redistributed = redistributor.redistribute_batch(
            test_rewards.tolist(),
            test_equity.tolist()
        )
        
        # محاسبه متریک‌ها
        total_return = sum(redistributed)
        volatility = np.std(redistributed)
        sharpe_ratio = total_return / volatility if volatility > 0 else 0
        
        results[strategy] = {
            'total_return': total_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'redistributed': redistributed
        }
        
        print(f"  Total return: {total_return:.2f}")
        print(f"  Volatility: {volatility:.2f}")
        print(f"  Sharpe ratio: {sharpe_ratio:.3f}")
    
    # نمودار مقایسه
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # میله‌ای برای متریک‌ها
    strategies_list = list(results.keys())
    sharpe_ratios = [results[s]['sharpe_ratio'] for s in strategies_list]
    total_returns = [results[s]['total_return'] for s in strategies_list]
    
    x = np.arange(len(strategies_list))
    width = 0.35
    
    ax1.bar(x - width/2, sharpe_ratios, width, label='Sharpe Ratio', alpha=0.8)
    ax1.bar(x + width/2, np.array(total_returns)/100, width, 
            label='Total Return (÷100)', alpha=0.8)
    ax1.set_xlabel('Strategy')
    ax1.set_xticks(x)
    ax1.set_xticklabels(strategies_list)
    ax1.legend()
    ax1.set_title('Strategy Performance Metrics')
    ax1.grid(True, alpha=0.3)
    
    # خط برای پاداش‌های تجمعی
    for strategy in strategies_list:
        cumsum_rewards = np.cumsum(results[strategy]['redistributed'])
        ax2.plot(cumsum_rewards, label=f'{strategy.capitalize()}', linewidth=2)
    
    ax2.set_xlabel('Time Step')
    ax2.set_ylabel('Cumulative Redistributed Rewards')
    ax2.set_title('Cumulative Returns by Strategy')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('strategy_comparison.png', dpi=300)
    plt.show()
    
    return results


def optimize_hyperparameters():
    """بهینه‌سازی hyperparameter ها"""
    
    print("\n=== Hyperparameter Optimization ===\n")
    
    # داده‌های validation
    np.random.seed(42)
    validation_rewards = np.random.normal(0, 15, 100)
    validation_equity = 10000 + np.cumsum(validation_rewards)
    
    validation_data = {
        'rewards': validation_rewards.tolist(),
        'equity_curve': validation_equity.tolist()
    }
    
    # ایجاد optimizer
    optimizer = RewardOptimizer(optimization_method='grid_search')
    redistributor = create_advanced_redistributor('adaptive')
    
    # بهینه‌سازی
    print("Running grid search optimization...")
    result = optimizer.optimize_hyperparameters(redistributor, validation_data)
    
    print(f"\nOptimization Results:")
    print(f"  Best parameters: {result['best_params']}")
    print(f"  Best score: {result['best_score']:.3f}")
    print(f"  Method: {result['method']}")
    
    # نمایش نتایج همه ترکیب‌ها
    print("\nAll tested combinations:")
    for config in result['all_results'][:5]:  # نمایش 5 مورد اول
        print(f"  Max multiplier: {config['max_multiplier']:.1f}, "
              f"Min multiplier: {config['min_multiplier']:.2f}, "
              f"Score: {config['score']:.3f}")
    
    return result


if __name__ == "__main__":
    # اجرای شبیه‌سازی اصلی
    print("Starting advanced reward redistribution demonstration...\n")
    
    # 1. شبیه‌سازی جلسه معاملاتی
    trading_results = simulate_trading_session()
    
    # 2. مقایسه استراتژی‌ها
    strategy_results = test_different_strategies()
    
    # 3. بهینه‌سازی hyperparameter
    optimization_results = optimize_hyperparameters()
    
    print("\n=== Demo Complete ===")
    print("Check the generated plots for visual analysis.")
    print("Files saved:")
    print("  - advanced_reward_redistribution_analysis.png")
    print("  - strategy_comparison.png") 