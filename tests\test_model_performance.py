import unittest
import time
import sys
import os
import numpy as np

# اضافه کردن مسیر پروژه به sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sentiment_model import FinBERTModel
from models.time_series_model import TimeSeriesLSTM
from core.base import BaseModel

class TestModelPerformance(unittest.TestCase):
    def test_sentiment_model_performance(self):
        model = FinBERTModel()
        start_time = time.time()
        model.load_model()
        start = time.time()
        prediction = model.predict("The market is booming!")
        duration = time.time() - start
        self.assertLess(duration, 1.0)  # باید کمتر از 1 ثانیه باشه
        self.assertGreater(prediction.confidence, 0.5)

    def test_time_series_model_performance(self):
        model = TimeSeriesLSTM()
        start_time = time.time()
        # For testing, we'll train a dummy model instead of loading from a path
        # In a real scenario, you would provide a path to a pre-trained model
        dummy_data = np.random.rand(100, 1)
        model.train(dummy_data, epochs=1, batch_size=10)
        start = time.time()
        # Provide dummy data for prediction
        prediction = model.predict(dummy_data)
        duration = time.time() - start
        self.assertLess(duration, 2.0)  # تست عملکرد

if __name__ == "__main__":
    unittest.main()