# 📡 ماژول API

## 🎯 هدف
این ماژول مسئول ارائه رابط‌های برنامه‌نویسی (API) برای تعامل با سیستم معاملاتی و نمایش داشبورد بلادرنگ است.

## 📂 ساختار فایل‌ها

### 🔄 endpoints.py
رابط‌های برنامه‌نویسی اصلی سیستم

#### نقاط پایانی:
- `/api/v1/status`: وضعیت سیستم
- `/api/v1/trades`: لیست معاملات
- `/api/v1/positions`: موقعیت‌های باز
- `/api/v1/portfolio`: اطلاعات پورتفولیو
- `/api/v1/signals`: سیگنال‌های معاملاتی
- `/api/v1/performance`: متریک‌های عملکرد

#### قابلیت‌ها:
- احراز هویت با JWT
- محدودیت نرخ درخواست
- لاگ تمام درخواست‌ها
- مستندسازی خودکار با Swagger

### 📊 realtime_dashboard.py
داشبورد بلادرنگ برای نمایش وضعیت سیستم

#### ویژگی‌ها:
- نمایش نمودارهای قیمت
- وضعیت معاملات فعال
- متریک‌های عملکرد
- هشدارها و اعلان‌ها
- تاریخچه معاملات

#### تکنولوژی‌ها:
- WebSocket برای به‌روزرسانی بلادرنگ
- Redis برای کش داده‌ها
- Plotly برای نمودارها
- React برای رابط کاربری

### 🔒 security.py
تنظیمات امنیتی API

#### قابلیت‌ها:
- مدیریت توکن‌ها
- CORS
- محدودیت IP
- لاگ امنیتی

## 🔌 نحوه استفاده

### راه‌اندازی API:
```python
from api import create_app
app = create_app()
app.run()
```

### راه‌اندازی داشبورد:
```python
from api.realtime_dashboard import Dashboard
dashboard = Dashboard()
dashboard.start()
```

## 🔗 وابستگی‌ها
- FastAPI: فریم‌ورک API
- WebSocket: ارتباط بلادرنگ
- Redis: کش داده
- JWT: احراز هویت
- Swagger: مستندسازی

## 📈 مثال‌های استفاده

### دریافت وضعیت سیستم:
```python
import requests

response = requests.get(
    "http://localhost:8000/api/v1/status",
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)
print(response.json())
```

### اتصال به WebSocket:
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/trades');
ws.onmessage = (event) => {
    const trade = JSON.parse(event.data);
    updateChart(trade);
};
```

## ⚙️ تنظیمات

### تنظیمات API:
```yaml
api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  rate_limit: 100
  timeout: 30
```

### تنظیمات داشبورد:
```yaml
dashboard:
  update_interval: 1
  history_size: 1000
  cache_ttl: 300
```

## 🔒 امنیت

### سطوح دسترسی:
1. `read`: فقط خواندن
2. `trade`: معامله
3. `admin`: مدیریت

### محدودیت‌ها:
- حداکثر 100 درخواست در دقیقه
- حداکثر 10 اتصال همزمان WebSocket
- حداکثر 1000 رکورد در هر درخواست

## 📊 متریک‌ها

### متریک‌های API:
- زمان پاسخ
- تعداد درخواست‌ها
- نرخ خطا
- تعداد کاربران فعال

### متریک‌های داشبورد:
- تاخیر به‌روزرسانی
- مصرف حافظه
- بار CPU
- پهنای باند مصرفی 