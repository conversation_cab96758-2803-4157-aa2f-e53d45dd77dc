#!/usr/bin/env python3
"""
🧪 تست مخصوص H2O Brain برای بررسی حل مشکل hyperparameter_suggestions
"""

import pandas as pd
import numpy as np
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the fixed system
from fixed_ultimate_main import MultiBrainSystem

def test_h2o_brain_fix():
    """تست مخصوص H2O Brain"""
    print("🧪 Testing H2O Brain Fix")
    print("=" * 50)
    
    # Create sample data
    print("📊 Creating sample market data...")
    data = pd.DataFrame({
        'open': np.random.uniform(1.1000, 1.1100, 100),
        'high': np.random.uniform(1.1050, 1.1150, 100),
        'low': np.random.uniform(1.0950, 1.1050, 100),
        'close': np.random.uniform(1.1000, 1.1100, 100),
        'volume': np.random.randint(1000, 10000, 100),
        'rsi': np.random.uniform(20, 80, 100),
        'macd': np.random.uniform(-0.01, 0.01, 100),
        'bb_upper': np.random.uniform(1.1080, 1.1120, 100),
        'bb_lower': np.random.uniform(1.0980, 1.1020, 100),
        'sma_20': np.random.uniform(1.1000, 1.1100, 100)
    })
    
    print(f"✅ Sample data created: {len(data)} rows, {len(data.columns)} columns")
    
    # Initialize Multi-Brain System
    print("\n🧠 Initializing Multi-Brain System...")
    try:
        multi_brain = MultiBrainSystem()
        print("✅ Multi-Brain System initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize Multi-Brain System: {e}")
        return False
    
    # Test brain analysis
    print("\n🔍 Testing brain analysis...")
    try:
        analysis_results = multi_brain.analyze_market_conditions(
            data=data,
            model_type="LSTM",
            symbol="EURUSD"
        )
        
        print("✅ Brain analysis completed successfully")
        print(f"📊 Analysis results keys: {list(analysis_results.keys())}")
        
        # Check for the specific keys that were causing issues
        required_keys = [
            'hyperparameter_suggestions',
            'model_recommendations', 
            'distributed_config',
            'data_insights'
        ]
        
        print("\n🔍 Checking for required keys:")
        for key in required_keys:
            if key in analysis_results:
                print(f"   ✅ {key}: Found")
                if isinstance(analysis_results[key], dict):
                    if 'error' in analysis_results[key]:
                        print(f"      ⚠️ Contains error: {analysis_results[key]['error']}")
                    else:
                        print(f"      ✅ Valid data: {len(analysis_results[key])} items")
                else:
                    print(f"      ⚠️ Not a dict: {type(analysis_results[key])}")
            else:
                print(f"   ❌ {key}: Missing")
        
        # Test H2O Brain specifically
        print("\n📊 Testing H2O Brain specifically...")
        if multi_brain.h2o_brain:
            try:
                h2o_result = multi_brain.h2o_brain.analyze_data_patterns(data)
                print("✅ H2O Brain analysis completed")
                print(f"📊 H2O result keys: {list(h2o_result.keys())}")
                
                if 'data_quality' in h2o_result:
                    quality = h2o_result['data_quality']
                    print(f"📈 Data Quality: {quality.get('completeness', 0):.2%}")
                
            except Exception as e:
                print(f"⚠️ H2O Brain error: {e}")
        else:
            print("⚠️ H2O Brain not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Brain analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """تست مدیریت خطا"""
    print("\n🛡️ Testing Error Handling")
    print("=" * 50)
    
    # Create problematic data
    problematic_data = pd.DataFrame({
        'text_column': ['hello', 'world', 'test'],  # Non-numeric data
        'empty_column': [None, None, None],         # All null
        'mixed_column': [1, 'text', None]           # Mixed types
    })
    
    try:
        multi_brain = MultiBrainSystem()
        
        # This should handle errors gracefully
        analysis_results = multi_brain.analyze_market_conditions(
            data=problematic_data,
            model_type="LSTM", 
            symbol="TEST"
        )
        
        print("✅ Error handling working correctly")
        print(f"📊 Analysis completed with problematic data")
        
        # Check that all required keys exist even with errors
        required_keys = [
            'hyperparameter_suggestions',
            'model_recommendations',
            'distributed_config', 
            'data_insights'
        ]
        
        all_keys_present = all(key in analysis_results for key in required_keys)
        print(f"🔑 All required keys present: {all_keys_present}")
        
        return all_keys_present
        
    except Exception as e:
        print(f"❌ Error handling failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 H2O BRAIN FIX TEST")
    print("=" * 60)
    
    # Test 1: Normal operation
    test1_result = test_h2o_brain_fix()
    
    # Test 2: Error handling
    test2_result = test_error_handling()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Normal Operation: {'PASSED' if test1_result else 'FAILED'}")
    print(f"🛡️ Error Handling: {'PASSED' if test2_result else 'FAILED'}")
    
    overall_result = test1_result and test2_result
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_result else '❌ SOME TESTS FAILED'}")
    
    if overall_result:
        print("\n🎉 H2O Brain fix is working correctly!")
        print("💡 The 'hyperparameter_suggestions' error has been resolved.")
    else:
        print("\n⚠️ Some issues remain. Please check the output above.")
