#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 Profit Target Demo System
سیستم نمایشی اهداف سود با $1000 سرمایه
"""

import os
import sys
import time
import json
from datetime import datetime
from typing import Dict, List

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from portfolio.advanced_risk_manager import AdvancedRiskManager, RiskParameters
from portfolio.smart_portfolio_manager import SmartPortfolioManager
from core.base import TradingSignal

class ProfitTargetDemo:
    """سیستم نمایشی اهداف سود"""
    
    def __init__(self):
        print("🎯 PROFIT TARGET DEMO SYSTEM")
        print("=" * 60)
        
        # تنظیمات مطابق درخواست کاربر
        self.risk_params = RiskParameters(
            initial_capital=1000.0,           # سرمایه $1000
            max_drawdown_percent=10.0,        # حداکثر ضرر 10%
            daily_loss_limit_percent=4.0,     # حداکثر ضرر روزانه 4%
            daily_profit_target_per_symbol=5.0, # $5 روزانه هر نماد
            weekly_profit_target=30.0,        # $30 هفتگی
            monthly_profit_target=80.0,       # $80 ماهانه
            risk_per_trade_percent=2.0,       # 2% ریسک هر معامله
            max_positions=5,                  # حداکثر 5 موقعیت
            stop_loss_percent=1.5,            # 1.5% stop loss
            take_profit_percent=3.0           # 3% take profit
        )
        
        # ایجاد portfolio manager
        self.portfolio_manager = SmartPortfolioManager(self.risk_params)
        
        # نمادهای معاملاتی
        self.symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD"]
        
        # آمار demo
        self.demo_stats = {
            "start_time": datetime.now(),
            "trades_executed": 0,
            "successful_trades": 0,
            "total_signals": 0,
            "targets_achieved": {
                "daily": False,
                "weekly": False,
                "monthly": False
            }
        }
        
        print(f"💰 Initial Capital: ${self.risk_params.initial_capital}")
        print(f"🎯 Daily Target: ${self.risk_params.daily_profit_target_per_symbol} per symbol")
        print(f"📊 Weekly Target: ${self.risk_params.weekly_profit_target}")
        print(f"🚀 Monthly Target: ${self.risk_params.monthly_profit_target}")
        print(f"📉 Max Drawdown: {self.risk_params.max_drawdown_percent}%")
        print(f"📊 Daily Loss Limit: {self.risk_params.daily_loss_limit_percent}%")
        print("=" * 60)
    
    def generate_demo_signal(self, symbol: str) -> TradingSignal:
        """تولید سیگنال نمایشی"""
        
        import random
        
        # قیمت‌های پایه
        base_prices = {
            "EURUSD": 1.0850,
            "GBPUSD": 1.2650,
            "USDJPY": 150.50,
            "AUDUSD": 0.6750,
            "USDCAD": 1.3550
        }
        
        # شبیه‌سازی قیمت با نوسان
        base_price = base_prices.get(symbol, 1.0000)
        volatility = 0.005  # 0.5% نوسان
        price = base_price + random.uniform(-volatility, volatility)
        
        # تولید سیگنال
        action = random.choice(["buy", "sell"])
        confidence = random.uniform(0.75, 0.95)  # confidence بالا
        
        signal = TradingSignal(
            symbol=symbol,
            action=action,
            confidence=confidence,
            price=price,
            timestamp=datetime.now(),
            reasoning=f"Demo signal - {action.upper()} {symbol} @ {price:.4f}"
        )
        
        return signal
    
    def simulate_price_movement(self, symbol: str, entry_price: float) -> float:
        """شبیه‌سازی حرکت قیمت"""
        
        import random
        
        # شبیه‌سازی حرکت قیمت (70% شانس موفقیت)
        success_rate = 0.7
        
        if random.random() < success_rate:
            # حرکت مثبت
            movement = random.uniform(0.002, 0.005)  # 0.2% تا 0.5%
            return entry_price + movement
        else:
            # حرکت منفی
            movement = random.uniform(0.001, 0.003)  # 0.1% تا 0.3%
            return entry_price - movement
    
    def execute_demo_trade(self, symbol: str) -> Dict:
        """اجرای معامله نمایشی"""
        
        print(f"\n🔍 Processing {symbol}...")
        
        # تولید سیگنال
        signal = self.generate_demo_signal(symbol)
        self.demo_stats["total_signals"] += 1
        
        print(f"📊 Signal: {signal.action.upper()} {symbol} @ {signal.price:.4f}")
        print(f"🎯 Confidence: {signal.confidence:.2f}")
        
        # بررسی امکان معامله
        can_trade, reason = self.portfolio_manager.risk_manager.can_open_position(
            symbol, signal.price, signal.action
        )
        
        if not can_trade:
            print(f"❌ Cannot trade: {reason}")
            return {"success": False, "reason": reason}
        
        # افزودن سیگنال به portfolio manager
        opportunity_created = self.portfolio_manager.add_trading_signal(signal)
        
        if not opportunity_created:
            print(f"❌ No trading opportunity created")
            return {"success": False, "reason": "No opportunity"}
        
        # اجرای معامله
        executed = self.portfolio_manager.execute_best_opportunity()
        
        if executed:
            self.demo_stats["trades_executed"] += 1
            print(f"✅ Trade executed successfully")
            
            # شبیه‌سازی مرور زمان و حرکت قیمت
            time.sleep(2)
            
            # شبیه‌سازی بستن موقعیت
            position = self.portfolio_manager.risk_manager.positions.get(symbol)
            if position:
                # شبیه‌سازی قیمت جدید
                new_price = self.simulate_price_movement(symbol, position.entry_price)
                
                # بررسی stop loss و take profit
                if position.side == "buy":
                    if new_price <= position.stop_loss:
                        # Stop loss triggered
                        self.portfolio_manager.risk_manager.close_position(symbol, new_price, "stop_loss")
                        print(f"🛑 Stop loss triggered at {new_price:.4f}")
                    elif new_price >= position.take_profit:
                        # Take profit triggered
                        self.portfolio_manager.risk_manager.close_position(symbol, new_price, "take_profit")
                        print(f"🎯 Take profit triggered at {new_price:.4f}")
                        self.demo_stats["successful_trades"] += 1
                    else:
                        # Manual close after some time
                        self.portfolio_manager.risk_manager.close_position(symbol, new_price, "manual_close")
                        if new_price > position.entry_price:
                            print(f"✅ Manual close with profit at {new_price:.4f}")
                            self.demo_stats["successful_trades"] += 1
                        else:
                            print(f"📉 Manual close with loss at {new_price:.4f}")
                
                else:  # sell position
                    if new_price >= position.stop_loss:
                        # Stop loss triggered
                        self.portfolio_manager.risk_manager.close_position(symbol, new_price, "stop_loss")
                        print(f"🛑 Stop loss triggered at {new_price:.4f}")
                    elif new_price <= position.take_profit:
                        # Take profit triggered
                        self.portfolio_manager.risk_manager.close_position(symbol, new_price, "take_profit")
                        print(f"🎯 Take profit triggered at {new_price:.4f}")
                        self.demo_stats["successful_trades"] += 1
                    else:
                        # Manual close
                        self.portfolio_manager.risk_manager.close_position(symbol, new_price, "manual_close")
                        if new_price < position.entry_price:
                            print(f"✅ Manual close with profit at {new_price:.4f}")
                            self.demo_stats["successful_trades"] += 1
                        else:
                            print(f"📉 Manual close with loss at {new_price:.4f}")
            
            return {"success": True, "executed": True}
        else:
            print(f"❌ Trade execution failed")
            return {"success": False, "reason": "Execution failed"}
    
    def check_target_achievements(self):
        """بررسی دستیابی به اهداف"""
        
        report = self.portfolio_manager.get_comprehensive_report()
        
        # بررسی اهداف
        targets = report["profit_targets"]
        
        if targets["daily"]["achieved"] and not self.demo_stats["targets_achieved"]["daily"]:
            self.demo_stats["targets_achieved"]["daily"] = True
            print(f"\n🎉 DAILY TARGET ACHIEVED!")
            print(f"   Target: ${targets['daily']['target']:.2f}")
            print(f"   Achieved: ${targets['daily']['current']:.2f}")
        
        if targets["weekly"]["achieved"] and not self.demo_stats["targets_achieved"]["weekly"]:
            self.demo_stats["targets_achieved"]["weekly"] = True
            print(f"\n🎉 WEEKLY TARGET ACHIEVED!")
            print(f"   Target: ${targets['weekly']['target']:.2f}")
            print(f"   Achieved: ${targets['weekly']['current']:.2f}")
        
        if targets["monthly"]["achieved"] and not self.demo_stats["targets_achieved"]["monthly"]:
            self.demo_stats["targets_achieved"]["monthly"] = True
            print(f"\n🎉 MONTHLY TARGET ACHIEVED!")
            print(f"   Target: ${targets['monthly']['target']:.2f}")
            print(f"   Achieved: ${targets['monthly']['current']:.2f}")
        
        return targets
    
    def display_progress_report(self):
        """نمایش گزارش پیشرفت"""
        
        print("\n" + "="*60)
        print("📊 PROGRESS REPORT")
        print("="*60)
        
        # گزارش پرتفولیو
        report = self.portfolio_manager.get_comprehensive_report()
        
        print(f"💰 Capital Status:")
        print(f"   Initial: ${self.risk_params.initial_capital:.2f}")
        print(f"   Current: ${report['portfolio_status']['capital']['current']:.2f}")
        print(f"   Total PnL: ${report['portfolio_status']['capital']['total_pnl']:.2f}")
        print(f"   Daily PnL: ${report['portfolio_status']['capital']['daily_pnl']:.2f}")
        
        print(f"\n🎯 Target Progress:")
        targets = report["profit_targets"]
        
        daily_progress = targets["daily"]["progress_percent"]
        weekly_progress = targets["weekly"]["progress_percent"]
        monthly_progress = targets["monthly"]["progress_percent"]
        
        print(f"   Daily:   ${targets['daily']['current']:.2f} / ${targets['daily']['target']:.2f} ({daily_progress:.1f}%)")
        print(f"   Weekly:  ${targets['weekly']['current']:.2f} / ${targets['weekly']['target']:.2f} ({weekly_progress:.1f}%)")
        print(f"   Monthly: ${targets['monthly']['current']:.2f} / ${targets['monthly']['target']:.2f} ({monthly_progress:.1f}%)")
        
        print(f"\n📈 Trading Statistics:")
        print(f"   Total Signals: {self.demo_stats['total_signals']}")
        print(f"   Trades Executed: {self.demo_stats['trades_executed']}")
        print(f"   Successful Trades: {self.demo_stats['successful_trades']}")
        
        if self.demo_stats["trades_executed"] > 0:
            success_rate = (self.demo_stats["successful_trades"] / self.demo_stats["trades_executed"]) * 100
            print(f"   Success Rate: {success_rate:.1f}%")
        
        print(f"\n⚡ Risk Status:")
        print(f"   Risk Level: {report['system_status']['risk_level'].upper()}")
        print(f"   Drawdown: {report['portfolio_status']['risk']['current_drawdown_percent']:.2f}%")
        print(f"   Open Positions: {report['portfolio_status']['positions']['open_positions']}")
        
        print(f"\n🕐 Session Info:")
        session_duration = datetime.now() - self.demo_stats["start_time"]
        print(f"   Duration: {session_duration}")
        print(f"   Targets Achieved: {sum(self.demo_stats['targets_achieved'].values())}/3")
        
        print("="*60)
    
    def run_demo_session(self, duration_minutes: int = 10):
        """اجرای جلسه نمایشی"""
        
        print(f"\n🚀 Starting demo session for {duration_minutes} minutes...")
        print("=" * 60)
        
        from datetime import timedelta
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        trade_interval = 30  # 30 ثانیه بین معاملات
        symbol_index = 0
        
        try:
            while datetime.now() < end_time:
                # انتخاب نماد
                symbol = self.symbols[symbol_index % len(self.symbols)]
                symbol_index += 1
                
                # اجرای معامله
                result = self.execute_demo_trade(symbol)
                
                # بررسی اهداف
                self.check_target_achievements()
                
                # نمایش گزارش هر 5 معامله
                if self.demo_stats["trades_executed"] % 5 == 0 and self.demo_stats["trades_executed"] > 0:
                    self.display_progress_report()
                
                # بررسی پایان زودهنگام
                if all(self.demo_stats["targets_achieved"].values()):
                    print("\n🎉 ALL TARGETS ACHIEVED! Ending demo session early.")
                    break
                
                # استراحت
                time.sleep(trade_interval)
                
        except KeyboardInterrupt:
            print("\n⏹️ Demo session interrupted by user")
        
        # گزارش نهایی
        self.display_final_report()
    
    def display_final_report(self):
        """نمایش گزارش نهایی"""
        
        print("\n" + "="*60)
        print("🎯 FINAL DEMO REPORT")
        print("="*60)
        
        # آخرین وضعیت
        self.display_progress_report()
        
        # خلاصه نتایج
        print(f"\n📋 SUMMARY:")
        
        report = self.portfolio_manager.get_comprehensive_report()
        initial_capital = self.risk_params.initial_capital
        final_capital = report['portfolio_status']['capital']['current']
        total_pnl = report['portfolio_status']['capital']['total_pnl']
        
        print(f"   Initial Capital: ${initial_capital:.2f}")
        print(f"   Final Capital: ${final_capital:.2f}")
        print(f"   Total Profit/Loss: ${total_pnl:.2f}")
        print(f"   Return: {(total_pnl/initial_capital)*100:.2f}%")
        
        # ارزیابی موفقیت
        targets_achieved = sum(self.demo_stats["targets_achieved"].values())
        
        if targets_achieved == 3:
            print(f"\n🎉 EXCELLENT: All targets achieved!")
        elif targets_achieved == 2:
            print(f"\n👍 GOOD: {targets_achieved}/3 targets achieved")
        elif targets_achieved == 1:
            print(f"\n📊 FAIR: {targets_achieved}/3 targets achieved")
        else:
            print(f"\n⚠️ NEEDS IMPROVEMENT: No targets achieved")
        
        # نکات
        print(f"\n💡 OBSERVATIONS:")
        if total_pnl > 0:
            print(f"   ✅ Profitable session")
        else:
            print(f"   ❌ Loss-making session")
        
        if self.demo_stats["trades_executed"] > 0:
            success_rate = (self.demo_stats["successful_trades"] / self.demo_stats["trades_executed"]) * 100
            if success_rate >= 60:
                print(f"   ✅ Good success rate: {success_rate:.1f}%")
            else:
                print(f"   ⚠️ Low success rate: {success_rate:.1f}%")
        
        drawdown = report['portfolio_status']['risk']['current_drawdown_percent']
        if drawdown < 5:
            print(f"   ✅ Low drawdown: {drawdown:.1f}%")
        else:
            print(f"   ⚠️ High drawdown: {drawdown:.1f}%")
        
        print("="*60)
        
        # ذخیره گزارش
        self.save_demo_report()
    
    def save_demo_report(self):
        """ذخیره گزارش نمایشی"""
        
        report = self.portfolio_manager.get_comprehensive_report()
        
        demo_report = {
            "demo_info": {
                "start_time": self.demo_stats["start_time"].isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration": str(datetime.now() - self.demo_stats["start_time"]),
                "symbols_traded": self.symbols
            },
            "targets": {
                "daily": {
                    "target": self.risk_params.daily_profit_target_per_symbol,
                    "achieved": self.demo_stats["targets_achieved"]["daily"]
                },
                "weekly": {
                    "target": self.risk_params.weekly_profit_target,
                    "achieved": self.demo_stats["targets_achieved"]["weekly"]
                },
                "monthly": {
                    "target": self.risk_params.monthly_profit_target,
                    "achieved": self.demo_stats["targets_achieved"]["monthly"]
                }
            },
            "performance": {
                "initial_capital": self.risk_params.initial_capital,
                "final_capital": report['portfolio_status']['capital']['current'],
                "total_pnl": report['portfolio_status']['capital']['total_pnl'],
                "return_percent": (report['portfolio_status']['capital']['total_pnl'] / self.risk_params.initial_capital) * 100,
                "max_drawdown": report['portfolio_status']['risk']['max_drawdown'],
                "success_rate": (self.demo_stats["successful_trades"] / max(self.demo_stats["trades_executed"], 1)) * 100
            },
            "trading_stats": self.demo_stats,
            "full_report": report
        }
        
        filename = f"profit_target_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w') as f:
            json.dump(demo_report, f, indent=2, default=str)
        
        print(f"✅ Demo report saved to {filename}")

# اجرای نمایشی
if __name__ == "__main__":
    from datetime import timedelta
    
    print("🎯 PROFIT TARGET DEMO")
    print("Target: $5 daily per symbol with $1000 capital")
    print("Risk: Max 10% drawdown, 4% daily limit")
    print("=" * 60)
    
    # ایجاد سیستم نمایشی
    demo = ProfitTargetDemo()
    
    # نمایش وضعیت اولیه
    demo.display_progress_report()
    
    print("\n🚀 Starting quick demo with 3 trades...")
    
    # اجرای چند معامله نمایشی
    for i, symbol in enumerate(demo.symbols[:3]):
        print(f"\n--- Trade {i+1}/3 ---")
        result = demo.execute_demo_trade(symbol)
        demo.check_target_achievements()
        time.sleep(2)
    
    # گزارش نهایی
    demo.display_final_report()
    
    print("\n🎯 Demo completed!")
    print("For full session demo, run: demo.run_demo_session(duration_minutes=10)") 