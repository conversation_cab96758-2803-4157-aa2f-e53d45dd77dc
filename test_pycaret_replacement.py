#!/usr/bin/env python3
"""
🧪 تست جایگزینی H2O Brain با PyCaret Brain
"""

import pandas as pd
import numpy as np
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pycaret_replacement():
    """تست جایگزینی H2O با PyCaret"""
    print("🧪 TESTING PYCARET REPLACEMENT")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import MultiBrainSystem, PyCaretBrain, PYCARET_AVAILABLE
        print("✅ Successfully imported PyCaretBrain")
        print(f"🎯 PyCaret available: {PYCARET_AVAILABLE}")
        
        # Test PyCaretBrain directly
        print("\n🎯 Testing PyCaretBrain directly...")
        pycaret_brain = PyCaretBrain()
        print("✅ PyCaretBrain initialized")
        
        # Create test data
        data = pd.DataFrame({
            'close': np.random.uniform(1.1000, 1.1100, 100),
            'volume': np.random.randint(1000, 10000, 100),
            'rsi': np.random.uniform(20, 80, 100),
            'macd': np.random.uniform(-0.01, 0.01, 100),
            'sma_20': np.random.uniform(1.1000, 1.1100, 100)
        })
        
        print(f"📊 Test data: {len(data)} rows, {len(data.columns)} columns")
        
        # Test PyCaret analysis
        print("\n🔍 Testing PyCaret analysis...")
        pycaret_result = pycaret_brain.analyze_data_patterns(data)
        print("✅ PyCaret analysis completed")
        print(f"   Result type: {type(pycaret_result)}")
        print(f"   Keys: {list(pycaret_result.keys())}")
        
        # Check for expected keys
        expected_keys = ['data_quality', 'feature_importance', 'anomaly_detection', 'trend_analysis']
        all_present = all(key in pycaret_result for key in expected_keys)
        print(f"✅ All expected keys present: {'YES' if all_present else 'NO'}")
        
        # Test MultiBrainSystem
        print("\n🧠 Testing MultiBrainSystem with PyCaret...")
        multi_brain = MultiBrainSystem()
        print("✅ MultiBrainSystem initialized")
        
        # Check if PyCaret brain is available
        has_pycaret_brain = hasattr(multi_brain, 'pycaret_brain') and multi_brain.pycaret_brain is not None
        print(f"✅ PyCaret Brain in MultiBrainSystem: {'YES' if has_pycaret_brain else 'NO'}")
        
        # Check if H2O brain is removed
        has_h2o_brain = hasattr(multi_brain, 'h2o_brain')
        print(f"✅ H2O Brain removed: {'YES' if not has_h2o_brain else 'NO'}")
        
        # Test full analysis
        print("\n🔍 Testing full multi-brain analysis...")
        analysis = multi_brain.analyze_training_situation(data, "LSTM", "EURUSD")
        print("✅ Multi-brain analysis completed")
        
        # Check for hyperparameter_suggestions
        if 'hyperparameter_suggestions' in analysis:
            print("✅ hyperparameter_suggestions present")
            return True
        else:
            print("❌ hyperparameter_suggestions missing")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_no_h2o_references():
    """تست عدم وجود مراجع H2O"""
    print("\n🔍 CHECKING FOR H2O REFERENCES")
    print("=" * 50)
    
    try:
        # Read the file and check for H2O references
        with open('fixed_ultimate_main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for problematic H2O references
        h2o_references = [
            'H2OBrain',
            'h2o_brain',
            'H2O_AVAILABLE',
            'import h2o',
            'h2o.init',
            'H2OAutoML'
        ]
        
        found_references = []
        for ref in h2o_references:
            if ref in content:
                found_references.append(ref)
        
        if found_references:
            print(f"⚠️ Found H2O references: {found_references}")
            return False
        else:
            print("✅ No H2O references found")
            return True
            
    except Exception as e:
        print(f"❌ File check failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 PYCARET REPLACEMENT TEST")
    print("=" * 60)
    
    # Test 1: PyCaret functionality
    test1 = test_pycaret_replacement()
    
    # Test 2: No H2O references
    test2 = test_no_h2o_references()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 REPLACEMENT TEST SUMMARY")
    print("=" * 60)
    print(f"🎯 PyCaret functionality: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"🗑️ H2O removal: {'✅ PASSED' if test2 else '❌ FAILED'}")
    
    overall = test1 and test2
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if overall else '❌ SOME TESTS FAILED'}")
    
    if overall:
        print("\n🎉 H2O BRAIN SUCCESSFULLY REPLACED WITH PYCARET!")
        print("💡 Benefits of PyCaret Brain:")
        print("   - ✅ No more H2O dependency issues")
        print("   - 🎯 Advanced AutoML capabilities")
        print("   - 📊 Better model recommendations")
        print("   - 🚀 Faster and more reliable")
        print("   - 🛡️ No more hyperparameter_suggestions errors")
    else:
        print("\n⚠️ Some issues remain. Check the output above.")
