#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 Model Analysis and Optimization System
سیستم تحلیل و بهینه‌سازی مدل‌ها

This script analyzes all models in the project and determines:
1. Which models can be trained locally
2. Which models need Google Colab
3. Pre-trained alternatives available
4. Optimal datasets and testing strategies
"""

import psutil
import GPUtil
import torch
import tensorflow as tf
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any
import logging
from dataclasses import dataclass
from enum import Enum
import requests
import json
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelComplexity(Enum):
    """Model complexity levels"""
    LIGHT = "light"      # Can train locally
    MEDIUM = "medium"    # Needs good hardware
    HEAVY = "heavy"      # Needs Google Colab
    EXTREME = "extreme"  # Needs powerful cloud resources

@dataclass
class ModelInfo:
    """Information about each model"""
    name: str
    category: str
    complexity: ModelComplexity
    min_ram_gb: float
    min_vram_gb: float
    estimated_training_time_hours: float
    pre_trained_available: bool
    pre_trained_source: str
    recommended_dataset: str
    local_trainable: bool
    optimization_notes: str

@dataclass
class SystemSpecs:
    """System specifications"""
    total_ram_gb: float
    available_ram_gb: float
    gpu_count: int
    gpu_memory_gb: List[float]
    cpu_cores: int
    has_cuda: bool

class ModelAnalyzer:
    """Analyzes models and system capabilities"""
    
    def __init__(self):
        self.system_specs = self._get_system_specs()
        self.models_info = self._initialize_models_info()
        
    def _get_system_specs(self) -> SystemSpecs:
        """Get current system specifications"""
        # RAM info
        ram = psutil.virtual_memory()
        total_ram_gb = ram.total / (1024**3)
        available_ram_gb = ram.available / (1024**3)
        
        # CPU info
        cpu_cores = psutil.cpu_count(logical=False)
        
        # GPU info
        gpu_count = 0
        gpu_memory_gb = []
        has_cuda = torch.cuda.is_available()
        
        try:
            gpus = GPUtil.getGPUs()
            gpu_count = len(gpus)
            gpu_memory_gb = [gpu.memoryTotal / 1024 for gpu in gpus]
        except:
            logger.warning("Could not detect GPU information")
            
        return SystemSpecs(
            total_ram_gb=total_ram_gb,
            available_ram_gb=available_ram_gb,
            gpu_count=gpu_count,
            gpu_memory_gb=gpu_memory_gb,
            cpu_cores=cpu_cores,
            has_cuda=has_cuda
        )
    
    def _initialize_models_info(self) -> Dict[str, ModelInfo]:
        """Initialize information about all models"""
        models = {}
        
        # 1. Sentiment Analysis Models
        models["FinBERTModel"] = ModelInfo(
            name="FinBERTModel",
            category="Sentiment Analysis",
            complexity=ModelComplexity.MEDIUM,
            min_ram_gb=8.0,
            min_vram_gb=4.0,
            estimated_training_time_hours=2.0,
            pre_trained_available=True,
            pre_trained_source="huggingface:ProsusAI/finbert",
            recommended_dataset="financial_news_sentiment",
            local_trainable=True,
            optimization_notes="Use pre-trained FinBERT and fine-tune on financial data"
        )
        
        models["CryptoBERTModel"] = ModelInfo(
            name="CryptoBERTModel",
            category="Sentiment Analysis",
            complexity=ModelComplexity.MEDIUM,
            min_ram_gb=8.0,
            min_vram_gb=4.0,
            estimated_training_time_hours=2.5,
            pre_trained_available=True,
            pre_trained_source="huggingface:ElKulako/cryptobert",
            recommended_dataset="crypto_news_sentiment",
            local_trainable=True,
            optimization_notes="Use CryptoBERT pre-trained model for crypto sentiment"
        )
        
        models["FinancialSentimentModel"] = ModelInfo(
            name="FinancialSentimentModel",
            category="Sentiment Analysis",
            complexity=ModelComplexity.LIGHT,
            min_ram_gb=4.0,
            min_vram_gb=2.0,
            estimated_training_time_hours=1.0,
            pre_trained_available=True,
            pre_trained_source="huggingface:mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
            recommended_dataset="financial_phrasebank",
            local_trainable=True,
            optimization_notes="Lightweight model, perfect for local training"
        )
        
        models["SentimentEnsemble"] = ModelInfo(
            name="SentimentEnsemble",
            category="Sentiment Analysis",
            complexity=ModelComplexity.MEDIUM,
            min_ram_gb=12.0,
            min_vram_gb=6.0,
            estimated_training_time_hours=3.0,
            pre_trained_available=False,
            pre_trained_source="custom_ensemble",
            recommended_dataset="combined_financial_sentiment",
            local_trainable=True,
            optimization_notes="Ensemble of above models, train components separately"
        )
        
        # 2. Time Series Models
        models["ChronosModel"] = ModelInfo(
            name="ChronosModel",
            category="Time Series",
            complexity=ModelComplexity.HEAVY,
            min_ram_gb=16.0,
            min_vram_gb=8.0,
            estimated_training_time_hours=8.0,
            pre_trained_available=True,
            pre_trained_source="huggingface:amazon/chronos-t5-small",
            recommended_dataset="financial_time_series",
            local_trainable=False,
            optimization_notes="Use Amazon Chronos pre-trained, fine-tune on financial data"
        )
        
        models["TimeSeriesModel"] = ModelInfo(
            name="TimeSeriesModel",
            category="Time Series",
            complexity=ModelComplexity.MEDIUM,
            min_ram_gb=8.0,
            min_vram_gb=4.0,
            estimated_training_time_hours=4.0,
            pre_trained_available=True,
            pre_trained_source="custom_lstm_gru",
            recommended_dataset="forex_historical_data",
            local_trainable=True,
            optimization_notes="LSTM/GRU based, can train locally with optimization"
        )
        
        models["TimeSeriesEnsemble"] = ModelInfo(
            name="TimeSeriesEnsemble",
            category="Time Series",
            complexity=ModelComplexity.HEAVY,
            min_ram_gb=20.0,
            min_vram_gb=10.0,
            estimated_training_time_hours=12.0,
            pre_trained_available=False,
            pre_trained_source="custom_ensemble",
            recommended_dataset="multi_timeframe_data",
            local_trainable=False,
            optimization_notes="Ensemble of multiple time series models"
        )
        
        # 3. Reinforcement Learning Models
        rl_models = ["PPO", "A2C", "DQN", "SAC", "TD3", "DDPG", "RecurrentPPO", "QRDQN", "TQC", "MaskablePPO"]
        for model_name in rl_models:
            complexity = ModelComplexity.MEDIUM if model_name in ["A2C", "DQN"] else ModelComplexity.HEAVY
            local_trainable = model_name in ["A2C", "DQN", "PPO"]
            
            models[model_name] = ModelInfo(
                name=model_name,
                category="Reinforcement Learning",
                complexity=complexity,
                min_ram_gb=8.0 if complexity == ModelComplexity.MEDIUM else 16.0,
                min_vram_gb=4.0 if complexity == ModelComplexity.MEDIUM else 8.0,
                estimated_training_time_hours=6.0 if complexity == ModelComplexity.MEDIUM else 12.0,
                pre_trained_available=True,
                pre_trained_source=f"stable_baselines3:{model_name}",
                recommended_dataset="trading_environment_data",
                local_trainable=local_trainable,
                optimization_notes=f"Use Stable-Baselines3 {model_name} with custom trading env"
            )
        
        # 4. Ensemble Models
        models["EnsembleModel"] = ModelInfo(
            name="EnsembleModel",
            category="Ensemble",
            complexity=ModelComplexity.HEAVY,
            min_ram_gb=24.0,
            min_vram_gb=12.0,
            estimated_training_time_hours=15.0,
            pre_trained_available=False,
            pre_trained_source="custom_ensemble",
            recommended_dataset="combined_rl_data",
            local_trainable=False,
            optimization_notes="Ensemble of RL models, train components separately"
        )
        
        models["ModelEnsemble"] = ModelInfo(
            name="ModelEnsemble",
            category="Ensemble",
            complexity=ModelComplexity.HEAVY,
            min_ram_gb=20.0,
            min_vram_gb=10.0,
            estimated_training_time_hours=12.0,
            pre_trained_available=False,
            pre_trained_source="custom_ensemble",
            recommended_dataset="multi_modal_data",
            local_trainable=False,
            optimization_notes="Multi-modal ensemble, requires cloud training"
        )
        
        # 5. Continual Learning Models
        models["ContinualLearningSystem"] = ModelInfo(
            name="ContinualLearningSystem",
            category="Continual Learning",
            complexity=ModelComplexity.MEDIUM,
            min_ram_gb=12.0,
            min_vram_gb=6.0,
            estimated_training_time_hours=8.0,
            pre_trained_available=True,
            pre_trained_source="custom_implementation",
            recommended_dataset="sequential_trading_data",
            local_trainable=True,
            optimization_notes="EWC + Replay buffer, manageable locally"
        )
        
        models["EWCLayer"] = ModelInfo(
            name="EWCLayer",
            category="Continual Learning",
            complexity=ModelComplexity.LIGHT,
            min_ram_gb=4.0,
            min_vram_gb=2.0,
            estimated_training_time_hours=2.0,
            pre_trained_available=True,
            pre_trained_source="pytorch_implementation",
            recommended_dataset="task_sequence_data",
            local_trainable=True,
            optimization_notes="Lightweight EWC implementation"
        )
        
        models["ReplayBuffer"] = ModelInfo(
            name="ReplayBuffer",
            category="Continual Learning",
            complexity=ModelComplexity.LIGHT,
            min_ram_gb=6.0,
            min_vram_gb=1.0,
            estimated_training_time_hours=1.0,
            pre_trained_available=True,
            pre_trained_source="stable_baselines3",
            recommended_dataset="experience_replay_data",
            local_trainable=True,
            optimization_notes="Memory buffer, very lightweight"
        )
        
        # 6. Deep Learning Models
        models["LayoutLMModel"] = ModelInfo(
            name="LayoutLMModel",
            category="Deep Learning",
            complexity=ModelComplexity.EXTREME,
            min_ram_gb=32.0,
            min_vram_gb=16.0,
            estimated_training_time_hours=24.0,
            pre_trained_available=True,
            pre_trained_source="huggingface:microsoft/layoutlm-base-uncased",
            recommended_dataset="financial_documents",
            local_trainable=False,
            optimization_notes="Very heavy model, requires cloud training"
        )
        
        models["T5Model"] = ModelInfo(
            name="T5Model",
            category="Deep Learning",
            complexity=ModelComplexity.HEAVY,
            min_ram_gb=16.0,
            min_vram_gb=8.0,
            estimated_training_time_hours=10.0,
            pre_trained_available=True,
            pre_trained_source="huggingface:t5-small",
            recommended_dataset="financial_text_generation",
            local_trainable=False,
            optimization_notes="Use T5-small variant for local training possibility"
        )
        
        models["BERTModel"] = ModelInfo(
            name="BERTModel",
            category="Deep Learning",
            complexity=ModelComplexity.MEDIUM,
            min_ram_gb=8.0,
            min_vram_gb=4.0,
            estimated_training_time_hours=4.0,
            pre_trained_available=True,
            pre_trained_source="huggingface:bert-base-uncased",
            recommended_dataset="financial_text_classification",
            local_trainable=True,
            optimization_notes="Use DistilBERT for lighter alternative"
        )
        
        models["BARTModel"] = ModelInfo(
            name="BARTModel",
            category="Deep Learning",
            complexity=ModelComplexity.HEAVY,
            min_ram_gb=16.0,
            min_vram_gb=8.0,
            estimated_training_time_hours=8.0,
            pre_trained_available=True,
            pre_trained_source="huggingface:facebook/bart-base",
            recommended_dataset="financial_summarization",
            local_trainable=False,
            optimization_notes="Use BART-base for smaller footprint"
        )
        
        return models
    
    def analyze_local_capability(self) -> Dict[str, Any]:
        """Analyze which models can be trained locally"""
        local_models = []
        cloud_models = []
        
        for model_name, model_info in self.models_info.items():
            can_train_locally = self._can_train_locally(model_info)
            
            if can_train_locally:
                local_models.append(model_name)
            else:
                cloud_models.append(model_name)
        
        return {
            "system_specs": self.system_specs,
            "local_trainable": local_models,
            "cloud_required": cloud_models,
            "total_models": len(self.models_info),
            "local_percentage": len(local_models) / len(self.models_info) * 100
        }
    
    def _can_train_locally(self, model_info: ModelInfo) -> bool:
        """Check if a model can be trained locally"""
        # Check RAM requirement
        if model_info.min_ram_gb > self.system_specs.available_ram_gb:
            return False
        
        # Check VRAM requirement
        if self.system_specs.gpu_count == 0:
            return False
        
        max_gpu_memory = max(self.system_specs.gpu_memory_gb) if self.system_specs.gpu_memory_gb else 0
        if model_info.min_vram_gb > max_gpu_memory:
            return False
        
        # Check if model is marked as locally trainable
        return model_info.local_trainable
    
    def generate_training_recommendations(self) -> Dict[str, Any]:
        """Generate training recommendations for all models"""
        recommendations = {
            "local_training": [],
            "cloud_training": [],
            "pre_trained_alternatives": [],
            "optimization_strategies": []
        }
        
        for model_name, model_info in self.models_info.items():
            if self._can_train_locally(model_info):
                recommendations["local_training"].append({
                    "model": model_name,
                    "category": model_info.category,
                    "estimated_time": model_info.estimated_training_time_hours,
                    "dataset": model_info.recommended_dataset,
                    "notes": model_info.optimization_notes
                })
            else:
                recommendations["cloud_training"].append({
                    "model": model_name,
                    "category": model_info.category,
                    "estimated_time": model_info.estimated_training_time_hours,
                    "dataset": model_info.recommended_dataset,
                    "notes": model_info.optimization_notes
                })
            
            if model_info.pre_trained_available:
                recommendations["pre_trained_alternatives"].append({
                    "model": model_name,
                    "source": model_info.pre_trained_source,
                    "optimization": model_info.optimization_notes
                })
        
        return recommendations
    
    def print_analysis_report(self):
        """Print comprehensive analysis report"""
        analysis = self.analyze_local_capability()
        recommendations = self.generate_training_recommendations()
        
        print("🧠 MODEL ANALYSIS REPORT")
        print("=" * 50)
        
        print(f"\n📊 SYSTEM SPECIFICATIONS:")
        print(f"RAM: {self.system_specs.total_ram_gb:.1f} GB (Available: {self.system_specs.available_ram_gb:.1f} GB)")
        print(f"GPU Count: {self.system_specs.gpu_count}")
        if self.system_specs.gpu_memory_gb:
            print(f"GPU Memory: {', '.join([f'{mem:.1f} GB' for mem in self.system_specs.gpu_memory_gb])}")
        print(f"CPU Cores: {self.system_specs.cpu_cores}")
        print(f"CUDA Available: {self.system_specs.has_cuda}")
        
        print(f"\n🎯 TRAINING CAPABILITY ANALYSIS:")
        print(f"Total Models: {analysis['total_models']}")
        print(f"Local Trainable: {len(analysis['local_trainable'])} ({analysis['local_percentage']:.1f}%)")
        print(f"Cloud Required: {len(analysis['cloud_required'])}")
        
        print(f"\n✅ MODELS FOR LOCAL TRAINING:")
        for model in recommendations["local_training"]:
            print(f"  • {model['model']} ({model['category']}) - {model['estimated_time']}h")
        
        print(f"\n☁️ MODELS FOR CLOUD TRAINING:")
        for model in recommendations["cloud_training"]:
            print(f"  • {model['model']} ({model['category']}) - {model['estimated_time']}h")
        
        print(f"\n🔄 PRE-TRAINED ALTERNATIVES:")
        for model in recommendations["pre_trained_alternatives"]:
            print(f"  • {model['model']}: {model['source']}")

def main():
    """Main function"""
    analyzer = ModelAnalyzer()
    analyzer.print_analysis_report()
    
    # Save detailed analysis to file
    analysis = analyzer.analyze_local_capability()
    recommendations = analyzer.generate_training_recommendations()
    
    report = {
        "system_specs": analysis["system_specs"].__dict__,
        "analysis": analysis,
        "recommendations": recommendations,
        "models_info": {name: info.__dict__ for name, info in analyzer.models_info.items()}
    }
    
    with open("model_analysis_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 Detailed report saved to: model_analysis_report.json")

if __name__ == "__main__":
    main()
