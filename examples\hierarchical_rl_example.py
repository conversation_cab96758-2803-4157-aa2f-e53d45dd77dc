#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Example of using Hierarchical Reinforcement Learning for Trading

This example demonstrates:
1. Training a hierarchical RL model with three levels
2. Strategic level: High-level strategy selection
3. Tactical level: Entry/exit timing and risk management
4. Execution level: Precise trade execution
5. Analyzing strategy usage and performance
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import logging
import random

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.hierarchical_rl import HierarchicalRL
from env.trading_env import TradingEnv
from utils.data_utils import prepare_data_for_env

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_hierarchical_env(data: pd.DataFrame, symbol: str, timeframe: str):
    """
    Create trading environment with hierarchical observation space
    """
    # Add hierarchical features to the environment
    env = TradingEnv(
        df=data,
        symbol=symbol,
        style='spot',
        timeframe=timeframe,
        indicators={'rsi': {'period': 14}, 'ma': {'period': 20}, 'stochastic': {'period': 14}}
    )
    
    # Extend observation space for hierarchical features
    original_obs_space = env.observation_space.shape[0]
    hierarchical_obs_space = original_obs_space + 10  # Add 10 hierarchical features
    
    # Override observation space
    env.observation_space = env.observation_space.__class__(
        low=np.concatenate([env.observation_space.low, np.full(10, -np.inf)]),
        high=np.concatenate([env.observation_space.high, np.full(10, np.inf)]),
        shape=(hierarchical_obs_space,),
        dtype=env.observation_space.dtype
    )
    
    return env

def generate_hierarchical_observation(env: TradingEnv, step: int) -> np.ndarray:
    """
    Generate hierarchical observation with strategic, tactical, and execution features
    """
    # Get base observation
    base_obs = env._get_observation()
    
    # Strategic features (market regime, volatility, trend strength)
    strategic_features = [
        env.df['close'].iloc[step] / env.df['close'].iloc[max(0, step-20)] - 1,  # 20-period return
        env.df['close'].rolling(20).std().iloc[step] / env.df['close'].iloc[step],  # Volatility
        (env.df['close'].iloc[step] - env.df['ma'].iloc[step]) / env.df['ma'].iloc[step],  # Trend strength
        env.df['rsi'].iloc[step] / 100,  # RSI normalized
        env.df['stoch_k'].iloc[step] / 100,  # Stochastic normalized
        env.balance / env.initial_balance,  # Account balance ratio
        float(env.position),  # Current position
        env.risk_manager.risk_metrics['daily_drawdown'],  # Daily drawdown
        env.risk_manager.risk_metrics['total_drawdown'],  # Total drawdown
        step / len(env.df)  # Time progress
    ]
    
    # Combine base and hierarchical features
    hierarchical_obs = np.concatenate([base_obs, strategic_features])
    
    return hierarchical_obs

def train_hierarchical_model(env: TradingEnv, 
                           model: HierarchicalRL,
                           num_episodes: int = 100,
                           max_steps: int = 1000) -> Dict[str, List]:
    """
    Train hierarchical RL model
    """
    training_data = {
        'observations': [],
        'strategies': [],
        'tactical_actions': [],
        'execution_actions': [],
        'rewards': [],
        'dones': [],
        'episode_rewards': []
    }
    
    for episode in range(num_episodes):
        obs = env.reset()
        episode_reward = 0
        episode_data = {
            'observations': [],
            'strategies': [],
            'tactical_actions': [],
            'execution_actions': [],
            'rewards': [],
            'dones': []
        }
        
        for step in range(max_steps):
            # Generate hierarchical observation
            hierarchical_obs = generate_hierarchical_observation(env, step)
            
            # Get hierarchical actions
            strategy, tactical_action, execution_action = model.act(hierarchical_obs)
            
            # Execute action in environment
            action = np.array([execution_action])
            obs, reward, done, info = env.step(action)
            
            # Store data
            episode_data['observations'].append(hierarchical_obs)
            episode_data['strategies'].append(strategy)
            episode_data['tactical_actions'].append(tactical_action)
            episode_data['execution_actions'].append(execution_action)
            episode_data['rewards'].append(reward)
            episode_data['dones'].append(done)
            
            episode_reward += reward
            
            if done:
                break
        
        # Store episode data
        training_data['observations'].extend(episode_data['observations'])
        training_data['strategies'].extend(episode_data['strategies'])
        training_data['tactical_actions'].extend(episode_data['tactical_actions'])
        training_data['execution_actions'].extend(episode_data['execution_actions'])
        training_data['rewards'].extend(episode_data['rewards'])
        training_data['dones'].extend(episode_data['dones'])
        training_data['episode_rewards'].append(episode_reward)
        
        # Train on episode data
        if len(episode_data['observations']) > 0:
            model.train_step(episode_data)
        
        if episode % 10 == 0:
            logger.info(f"Episode {episode}: Reward = {episode_reward:.2f}")
    
    return training_data

def evaluate_hierarchical_model(env: TradingEnv, 
                              model: HierarchicalRL,
                              num_episodes: int = 10) -> Dict[str, float]:
    """
    Evaluate hierarchical RL model
    """
    episode_rewards = []
    strategy_usage = {i: 0 for i in range(4)}
    tactical_usage = {i: 0 for i in range(3)}
    
    for episode in range(num_episodes):
        obs = env.reset()
        episode_reward = 0
        
        for step in range(len(env.df) - 1):
            # Generate hierarchical observation
            hierarchical_obs = generate_hierarchical_observation(env, step)
            
            # Get hierarchical actions
            strategy, tactical_action, execution_action = model.act(hierarchical_obs)
            
            # Execute action
            action = np.array([execution_action])
            obs, reward, done, info = env.step(action)
            
            episode_reward += reward
            strategy_usage[strategy] += 1
            tactical_usage[tactical_action] += 1
            
            if done:
                break
        
        episode_rewards.append(episode_reward)
    
    # Calculate metrics
    total_actions = sum(strategy_usage.values())
    strategy_percentages = {f"strategy_{i}": count/total_actions*100 for i, count in strategy_usage.items()}
    
    total_tactical = sum(tactical_usage.values())
    tactical_percentages = {f"tactical_{i}": count/total_tactical*100 for i, count in tactical_usage.items()}
    
    return {
        'mean_reward': np.mean(episode_rewards),
        'std_reward': np.std(episode_rewards),
        'strategy_usage': strategy_percentages,
        'tactical_usage': tactical_percentages,
        'episode_rewards': episode_rewards
    }

def plot_hierarchical_results(training_data: Dict[str, List], 
                            evaluation_results: Dict[str, float],
                            model: HierarchicalRL):
    """
    Plot hierarchical RL results
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Plot training rewards
    axes[0, 0].plot(training_data['episode_rewards'])
    axes[0, 0].set_title('Training Episode Rewards')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('Reward')
    axes[0, 0].grid(True)
    
    # Plot strategy usage
    strategy_names = ['Trend Following', 'Mean Reversion', 'Breakout', 'Momentum']
    strategy_counts = [training_data['strategies'].count(i) for i in range(4)]
    axes[0, 1].pie(strategy_counts, labels=strategy_names, autopct='%1.1f%%')
    axes[0, 1].set_title('Strategy Usage During Training')
    
    # Plot tactical action usage
    tactical_names = ['Hold', 'Enter', 'Exit']
    tactical_counts = [training_data['tactical_actions'].count(i) for i in range(3)]
    axes[1, 0].bar(tactical_names, tactical_counts)
    axes[1, 0].set_title('Tactical Action Usage')
    axes[1, 0].set_ylabel('Count')
    
    # Plot evaluation rewards
    axes[1, 1].hist(evaluation_results['episode_rewards'], bins=10, alpha=0.7)
    axes[1, 1].axvline(evaluation_results['mean_reward'], color='red', linestyle='--', 
                       label=f'Mean: {evaluation_results["mean_reward"]:.2f}')
    axes[1, 1].set_title('Evaluation Episode Rewards')
    axes[1, 1].set_xlabel('Reward')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].legend()
    
    plt.tight_layout()
    plt.savefig('hierarchical_rl_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_strategy_analysis(model: HierarchicalRL):
    """
    Plot detailed strategy analysis
    """
    analysis = model.get_strategy_analysis()
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Strategy usage pie chart
    strategy_names = list(analysis['strategy_usage'].keys())
    strategy_values = list(analysis['strategy_usage'].values())
    axes[0, 0].pie(strategy_values, labels=strategy_names, autopct='%1.1f%%')
    axes[0, 0].set_title('Strategy Usage Analysis')
    
    # Training losses
    losses = analysis['training_losses']
    loss_names = list(losses.keys())
    loss_values = list(losses.values())
    axes[0, 1].bar(loss_names, loss_values)
    axes[0, 1].set_title('Training Losses')
    axes[0, 1].set_ylabel('Loss')
    
    # Reward progression
    if model.training_history['total_reward']:
        axes[1, 0].plot(model.training_history['total_reward'])
        axes[1, 0].set_title('Training Reward Progression')
        axes[1, 0].set_xlabel('Training Step')
        axes[1, 0].set_ylabel('Reward')
        axes[1, 0].grid(True)
    
    # Loss progression
    if model.training_history['strategic_loss']:
        axes[1, 1].plot(model.training_history['strategic_loss'], label='Strategic')
        axes[1, 1].plot(model.training_history['tactical_loss'], label='Tactical')
        axes[1, 1].plot(model.training_history['execution_loss'], label='Execution')
        axes[1, 1].set_title('Loss Progression')
        axes[1, 1].set_xlabel('Training Step')
        axes[1, 1].set_ylabel('Loss')
        axes[1, 1].legend()
        axes[1, 1].grid(True)
    
    plt.tight_layout()
    plt.savefig('hierarchical_strategy_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """Main function to demonstrate hierarchical RL"""
    
    # Prepare data
    logger.info("Preparing market data")
    market_data = prepare_data_for_env(
        symbol='EURUSD',
        timeframe='H1',
        train_ratio=0.7,
        test_ratio=0.3
    )
    
    # Create hierarchical environment
    logger.info("Creating hierarchical trading environment")
    env = create_hierarchical_env(market_data['train'], 'EURUSD', 'H1')
    
    # Initialize hierarchical RL model
    logger.info("Initializing hierarchical RL model")
    obs_dim = env.observation_space.shape[0]
    model = HierarchicalRL(
        obs_dim=obs_dim,
        device='cpu',
        learning_rate=3e-4,
        gamma=0.99,
        gae_lambda=0.95,
        clip_ratio=0.2,
        value_loss_coef=0.5,
        entropy_coef=0.01
    )
    
    # Train hierarchical model
    logger.info("Training hierarchical RL model")
    training_data = train_hierarchical_model(env, model, num_episodes=50, max_steps=500)
    
    # Evaluate model
    logger.info("Evaluating hierarchical RL model")
    test_env = create_hierarchical_env(market_data['test'], 'EURUSD', 'H1')
    evaluation_results = evaluate_hierarchical_model(test_env, model, num_episodes=10)
    
    # Print results
    logger.info("=== Hierarchical RL Results ===")
    logger.info(f"Mean Reward: {evaluation_results['mean_reward']:.2f}")
    logger.info(f"Reward Std: {evaluation_results['std_reward']:.2f}")
    logger.info("Strategy Usage:")
    for strategy, percentage in evaluation_results['strategy_usage'].items():
        logger.info(f"  {strategy}: {percentage:.1f}%")
    logger.info("Tactical Usage:")
    for tactical, percentage in evaluation_results['tactical_usage'].items():
        logger.info(f"  {tactical}: {percentage:.1f}%")
    
    # Plot results
    logger.info("Generating plots")
    plot_hierarchical_results(training_data, evaluation_results, model)
    plot_strategy_analysis(model)
    
    # Save model
    logger.info("Saving hierarchical RL model")
    os.makedirs('saved_models', exist_ok=True)
    model.save('saved_models/hierarchical_rl_model')
    
    # Test model loading
    logger.info("Testing model loading")
    new_model = HierarchicalRL(obs_dim=obs_dim)
    new_model.load('saved_models/hierarchical_rl_model')
    
    # Test inference
    test_obs = np.random.randn(obs_dim)
    strategy, tactical, execution = new_model.act(test_obs)
    logger.info(f"Test inference: Strategy={strategy}, Tactical={tactical}, Execution={execution:.3f}")
    
    logger.info("Hierarchical RL experiment completed successfully")

if __name__ == "__main__":
    main() 