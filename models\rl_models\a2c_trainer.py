import torch
import numpy as np
import gymnasium as gym
from stable_baselines3 import A2C
from stable_baselines3.common.vec_env import DummyVecEnv
from typing import Dict, Any

class TradingEnvironment(gym.Env):
    """محیط سفارشی‌سازی‌شده برای معاملات"""
    def __init__(self, market_data):
        super().__init__()
        self.market_data = market_data
        self.current_step = 0
        
        # فضای اکشن و استیت
        self.action_space = gym.spaces.Discrete(3)  # خرید، فروش، نگهداری
        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf, 
            shape=(10,), dtype=np.float32  # 10 ویژگی مختلف
        )
    
    def reset(self, seed=None):
        self.current_step = 0
        return self._get_observation(), {}
    
    def step(self, action):
        # محاسبه پاداش و اکشن
        reward = self._calculate_reward(action)
        done = self.current_step >= len(self.market_data) - 1
        
        return (
            self._get_observation(), 
            reward, 
            done, 
            False, 
            {}
        )
    
    def _get_observation(self):
        # استخراج ویژگی‌ها از داده‌های بازار
        features = self.market_data.iloc[self.current_step]
        return np.array([
            features['open'], 
            features['close'], 
            features['volume'],
            # سایر ویژگی‌ها
        ])
    
    def _calculate_reward(self, action):
        # منطق محاسبه پاداش
        price_change = self.market_data['close'].pct_change().iloc[self.current_step]
        
        if action == 0:  # خرید
            return price_change
        elif action == 1:  # فروش
            return -price_change
        else:  # نگهداری
            return 0

class A2CTrainer:
    def __init__(self, market_data, pearl_agent=None):
        self.market_data = market_data
        self.pearl = pearl_agent
        
        # ایجاد محیط
        self.env = DummyVecEnv([lambda: TradingEnvironment(market_data)])
        
        # تنظیمات اولیه A2C
        self.model = A2C(
            "MlpPolicy", 
            self.env, 
            verbose=1,
            learning_rate=0.001,
            n_steps=5
        )
    
    def train(self, total_timesteps=10000):
        """آموزش مدل"""
        try:
            # اجرای آموزش
            self.model.learn(total_timesteps=total_timesteps)
            
            # گزارش به Pearl
            if self.pearl:
                performance = self._evaluate_performance()
                self.pearl.log_model_performance(
                    model_name='a2c',
                    metrics=performance
                )
            
            return True
        except Exception as e:
            print(f"خطا در آموزش: {e}")
            return False
    
    def _evaluate_performance(self) -> Dict[str, Any]:
        """ارزیابی عملکرد مدل"""
        # شبیه‌سازی معاملات
        obs = self.env.reset()
        done = False
        total_reward = 0
        
        while not done:
            action, _ = self.model.predict(obs)
            obs, reward, done, _ = self.env.step(action)
            total_reward += reward
        
        return {
            'total_reward': total_reward,
            'sharpe_ratio': self._calculate_sharpe_ratio(),
            'max_drawdown': self._calculate_max_drawdown()
        }
    
    def _calculate_sharpe_ratio(self):
        # محاسبه نسبت شارپ
        returns = self.market_data['close'].pct_change()
        return np.mean(returns) / np.std(returns)
    
    def _calculate_max_drawdown(self):
        # محاسبه حداکثر افت
        cumulative_returns = (1 + self.market_data['close'].pct_change()).cumprod()
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / running_max
        return np.min(drawdown)

def main():
    # بارگذاری داده‌های بازار
    import pandas as pd
    market_data = pd.read_csv('data/BTCUSD/D1.csv')
    
    # راه‌اندازی و آموزش
    trainer = A2CTrainer(market_data)
    trainer.train()

if __name__ == "__main__":
    main() 