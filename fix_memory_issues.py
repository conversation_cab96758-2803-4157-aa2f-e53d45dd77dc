#!/usr/bin/env python3
"""
Memory Optimization Script
=========================

This script addresses memory pressure issues in the trading system.
"""

import gc
import os
import sys
import psutil
import logging
from typing import Dict

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_memory_usage() -> Dict[str, float]:
    """Get current memory usage statistics"""
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
            'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
            'percent': process.memory_percent(),
            'available_mb': psutil.virtual_memory().available / 1024 / 1024
        }
    except Exception as e:
        logger.error(f"Error getting memory usage: {e}")
        return {'rss_mb': 0, 'vms_mb': 0, 'percent': 0, 'available_mb': 0}


def optimize_memory():
    """Optimize memory usage"""
    logger.info("🧹 Starting memory optimization...")
    
    # Get initial memory usage
    initial_memory = get_memory_usage()
    logger.info(f"Initial memory usage: {initial_memory['rss_mb']:.1f} MB ({initial_memory['percent']:.1f}%)")
    
    # Force garbage collection
    logger.info("🗑️ Running garbage collection...")
    collected = gc.collect()
    logger.info(f"Collected {collected} objects")
    
    # Clear module caches
    logger.info("🧽 Clearing module caches...")
    try:
        if hasattr(sys, '_clear_type_cache'):
            sys._clear_type_cache()  # type: ignore
    except Exception:
        pass  # Ignore if not available
    
    # Get final memory usage
    final_memory = get_memory_usage()
    memory_saved = initial_memory['rss_mb'] - final_memory['rss_mb']
    
    logger.info(f"Final memory usage: {final_memory['rss_mb']:.1f} MB ({final_memory['percent']:.1f}%)")
    logger.info(f"Memory saved: {memory_saved:.1f} MB")
    
    return memory_saved


def check_memory_pressure() -> bool:
    """Check if system is under memory pressure"""
    memory_info = get_memory_usage()
    
    # Consider memory pressure if:
    # - Process uses more than 80% of available memory
    # - System has less than 500MB available
    memory_pressure = (
        memory_info['percent'] > 80 or 
        memory_info['available_mb'] < 500
    )
    
    if memory_pressure:
        logger.warning(f"⚠️ Memory pressure detected!")
        logger.warning(f"Process memory: {memory_info['percent']:.1f}%")
        logger.warning(f"Available memory: {memory_info['available_mb']:.1f} MB")
    
    return memory_pressure


def optimize_test_environment():
    """Optimize memory for test environment"""
    logger.info("🧪 Optimizing test environment...")
    
    # Reduce test data size
    os.environ['PYTEST_DISABLE_PLUGIN_AUTOLOAD'] = '1'
    os.environ['PYTHONDONTWRITEBYTECODE'] = '1'
    
    # Set memory-friendly garbage collection
    gc.set_threshold(700, 10, 10)  # More aggressive GC
    
    # Optimize memory usage
    optimize_memory()
    
    logger.info("✅ Test environment optimized")


def create_memory_monitor():
    """Create a simple memory monitor"""
    def monitor_memory():
        memory_info = get_memory_usage()
        if memory_info['percent'] > 85:
            logger.warning(f"High memory usage: {memory_info['percent']:.1f}%")
            optimize_memory()
    
    return monitor_memory


def fix_test_memory_issues():
    """Fix memory issues in tests"""
    logger.info("🔧 Fixing test memory issues...")
    
    # Check if we're in a test environment
    if 'pytest' in sys.modules or 'unittest' in sys.modules:
        optimize_test_environment()
    
    # Check for memory pressure
    if check_memory_pressure():
        optimize_memory()
    
    logger.info("✅ Memory issues addressed")


def main():
    """Main function"""
    print("Memory Optimization Tool")
    print("=" * 40)
    
    # Show initial memory status
    memory_info = get_memory_usage()
    print(f"Current memory usage: {memory_info['rss_mb']:.1f} MB ({memory_info['percent']:.1f}%)")
    print(f"Available memory: {memory_info['available_mb']:.1f} MB")
    
    # Check for memory pressure
    if check_memory_pressure():
        print("\nMemory pressure detected - optimizing...")
        saved = optimize_memory()
        print(f"Optimization complete - saved {saved:.1f} MB")
    else:
        print("\nMemory usage is normal")
    
    # Fix test-specific issues
    fix_test_memory_issues()
    
    print("\nMemory optimization completed!")


if __name__ == "__main__":
    main()