"""
سیستم تشخیص رژیم بازار برای سیستم تطبیقی Plutus
Market Regime Detection System for Adaptive Plutus
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import logging
from sklearn.mixture import GaussianMixture
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MarketRegime:
    """تعریف رژیم بازار"""
    name: str
    characteristics: Dict[str, Any]
    optimal_weights: Dict[str, float]
    risk_level: str
    description: str

@dataclass
class RegimeDetectionResult:
    """نتیجه تشخیص رژیم"""
    current_regime: str
    confidence: float
    regime_probabilities: Dict[str, float]
    market_features: Dict[str, float]
    timestamp: datetime

class MarketRegimeDetector:
    """سیستم تشخیص رژیم بازار"""
    
    def __init__(self):
        self.regimes = self._define_market_regimes()
        self.feature_scaler = StandardScaler()
        self.regime_model = None
        self.lookback_period = 120  # 120 periods for regime detection
        self.min_data_points = 50
        
        # تاریخچه رژیم‌ها
        self.regime_history = []
        
        logger.info("Market Regime Detector initialized")
    
    def fit(self, features_array: np.ndarray):
        """آموزش مدل با آرایه ویژگی‌ها (برای سازگاری با sklearn)"""
        try:
            if len(features_array) < 10:
                logger.warning("Insufficient data for regime model training")
                return False
            
            # نرمال‌سازی ویژگی‌ها
            features_scaled = self.feature_scaler.fit_transform(features_array)
            
            # آموزش مدل Gaussian Mixture
            n_components = min(5, len(features_array) // 3)
            self.regime_model = GaussianMixture(
                n_components=n_components,
                covariance_type='full',
                random_state=42,
                max_iter=100
            )
            
            self.regime_model.fit(features_scaled)
            
            # تعریف برچسب‌های رژیم
            self.regime_labels = list(self.regimes.keys())[:n_components]
            
            logger.info(f"Regime model trained with {len(features_array)} samples and {n_components} components")
            return True
            
        except Exception as e:
            logger.error(f"Error training regime model: {e}")
            return False
    
    def predict_regime(self, features_array: np.ndarray) -> Dict[str, Any]:
        """پیش‌بینی رژیم برای ویژگی‌های جدید"""
        try:
            if self.regime_model is None:
                logger.warning("Regime model not trained")
                return {
                    'regime': 'sideways_market',
                    'confidence': 0.5,
                    'regime_weights': {}
                }
            
            # نرمال‌سازی ویژگی‌ها
            features_scaled = self.feature_scaler.transform(features_array)
            
            # پیش‌بینی رژیم
            regime_probs = self.regime_model.predict_proba(features_scaled)[0]
            regime_idx = np.argmax(regime_probs)
            
            # تعیین رژیم
            if hasattr(self, 'regime_labels') and regime_idx < len(self.regime_labels):
                regime = self.regime_labels[regime_idx]
            else:
                regime = list(self.regimes.keys())[regime_idx % len(self.regimes)]
            
            confidence = regime_probs[regime_idx]
            
            # وزن‌های رژیم
            regime_weights = self.get_regime_optimal_weights(regime)
            
            return {
                'regime': regime,
                'confidence': float(confidence),
                'regime_weights': regime_weights
            }
            
        except Exception as e:
            logger.error(f"Error predicting regime: {e}")
            return {
                'regime': 'sideways_market',
                'confidence': 0.5,
                'regime_weights': {}
            }
    
    def _define_market_regimes(self) -> Dict[str, MarketRegime]:
        """تعریف رژیم‌های مختلف بازار"""
        regimes = {
            "bull_market": MarketRegime(
                name="Bull Market",
                characteristics={
                    "trend_strength": "high_positive",
                    "volatility": "low_to_medium",
                    "volume": "high",
                    "momentum": "positive",
                    "support_resistance": "strong_support"
                },
                optimal_weights={
                    "chronos_weight": 0.7,
                    "fingpt_weight": 0.3,
                    "confidence_threshold": 0.6,
                    "position_size_multiplier": 1.2
                },
                risk_level="medium",
                description="Strong upward trend with good momentum"
            ),
            
            "bear_market": MarketRegime(
                name="Bear Market",
                characteristics={
                    "trend_strength": "high_negative",
                    "volatility": "medium_to_high",
                    "volume": "high",
                    "momentum": "negative",
                    "support_resistance": "weak_support"
                },
                optimal_weights={
                    "chronos_weight": 0.4,
                    "fingpt_weight": 0.6,
                    "confidence_threshold": 0.7,
                    "position_size_multiplier": 0.8
                },
                risk_level="high",
                description="Strong downward trend with high volatility"
            ),
            
            "sideways_market": MarketRegime(
                name="Sideways Market",
                characteristics={
                    "trend_strength": "low",
                    "volatility": "low",
                    "volume": "medium",
                    "momentum": "neutral",
                    "support_resistance": "strong_both"
                },
                optimal_weights={
                    "chronos_weight": 0.5,
                    "fingpt_weight": 0.5,
                    "confidence_threshold": 0.75,
                    "position_size_multiplier": 0.9
                },
                risk_level="low",
                description="Range-bound market with clear support/resistance"
            ),
            
            "high_volatility": MarketRegime(
                name="High Volatility",
                characteristics={
                    "trend_strength": "variable",
                    "volatility": "very_high",
                    "volume": "very_high",
                    "momentum": "erratic",
                    "support_resistance": "broken"
                },
                optimal_weights={
                    "chronos_weight": 0.3,
                    "fingpt_weight": 0.7,
                    "confidence_threshold": 0.8,
                    "position_size_multiplier": 0.6
                },
                risk_level="very_high",
                description="Extremely volatile market with unpredictable moves"
            ),
            
            "low_volatility": MarketRegime(
                name="Low Volatility",
                characteristics={
                    "trend_strength": "very_low",
                    "volatility": "very_low",
                    "volume": "low",
                    "momentum": "minimal",
                    "support_resistance": "stable"
                },
                optimal_weights={
                    "chronos_weight": 0.6,
                    "fingpt_weight": 0.4,
                    "confidence_threshold": 0.65,
                    "position_size_multiplier": 1.1
                },
                risk_level="very_low",
                description="Calm market with minimal price movement"
            )
        }
        
        return regimes
    
    def extract_market_features(self, price_data: pd.DataFrame) -> Dict[str, float]:
        """استخراج ویژگی‌های بازار برای تشخیص رژیم"""
        try:
            if len(price_data) < self.min_data_points:
                raise ValueError(f"Insufficient data: {len(price_data)} < {self.min_data_points}")
            
            # محاسبه بازده‌ها
            returns = price_data['close'].pct_change().dropna()
            
            # ویژگی‌های اصلی
            features = {}
            
            # 1. Trend Strength (قدرت روند)
            sma_20 = price_data['close'].rolling(20).mean()
            sma_50 = price_data['close'].rolling(50).mean()
            trend_strength = ((sma_20.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]) * 100
            features['trend_strength'] = trend_strength
            
            # 2. Volatility (نوسان)
            volatility = returns.std() * np.sqrt(252) * 100  # Annualized volatility
            features['volatility'] = volatility
            
            # 3. Volume Trend (روند حجم)
            if 'volume' in price_data.columns:
                volume_ma = price_data['volume'].rolling(20).mean()
                volume_trend = (volume_ma.iloc[-1] / volume_ma.iloc[-20] - 1) * 100 if len(volume_ma) >= 20 else 0
            else:
                volume_trend = 0
            features['volume_trend'] = volume_trend
            
            # 4. Momentum (مومنتوم)
            rsi = self._calculate_rsi(price_data['close'])
            features['rsi'] = rsi
            
            # 5. Price Position (موقعیت قیمت)
            high_20 = price_data['high'].rolling(20).max()
            low_20 = price_data['low'].rolling(20).min()
            price_position = ((price_data['close'].iloc[-1] - low_20.iloc[-1]) / 
                            (high_20.iloc[-1] - low_20.iloc[-1])) * 100
            features['price_position'] = price_position
            
            # 6. Support/Resistance Strength (قدرت سپورت/رزیستنس)
            support_resistance_strength = self._calculate_support_resistance_strength(price_data)
            features['support_resistance_strength'] = support_resistance_strength
            
            # 7. Market Efficiency (کارایی بازار)
            autocorr = returns.autocorr(lag=1)
            features['autocorrelation'] = autocorr if not np.isnan(autocorr) else 0
            
            # 8. Drawdown (افت قیمت)
            cumulative_returns = (1 + returns).cumprod()
            running_max = cumulative_returns.expanding().max()
            drawdown = ((cumulative_returns - running_max) / running_max).min() * 100
            features['max_drawdown'] = abs(drawdown)
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting market features: {str(e)}")
            return self._get_default_features()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """محاسبه RSI"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi.iloc[-1] if not np.isnan(rsi.iloc[-1]) else 50.0
            
        except Exception:
            return 50.0  # Neutral RSI
    
    def _calculate_support_resistance_strength(self, price_data: pd.DataFrame) -> float:
        """محاسبه قدرت سپورت/رزیستنس"""
        try:
            # تشخیص نقاط pivot
            highs = price_data['high'].rolling(5, center=True).max()
            lows = price_data['low'].rolling(5, center=True).min()
            
            # تعداد تست‌های سپورت/رزیستنس
            current_price = price_data['close'].iloc[-1]
            recent_highs = highs.dropna()[-20:]  # 20 high اخیر
            recent_lows = lows.dropna()[-20:]   # 20 low اخیر
            
            # محاسبه قدرت بر اساس تعداد تست‌ها
            resistance_tests = len(recent_highs[abs(recent_highs - current_price) / current_price < 0.01])
            support_tests = len(recent_lows[abs(recent_lows - current_price) / current_price < 0.01])
            
            strength = (resistance_tests + support_tests) / 2
            return min(strength, 10)  # محدود به 10
            
        except Exception:
            return 1.0  # پیش‌فرض
    
    def _get_default_features(self) -> Dict[str, float]:
        """ویژگی‌های پیش‌فرض در صورت خطا"""
        return {
            'trend_strength': 0.0,
            'volatility': 15.0,
            'volume_trend': 0.0,
            'rsi': 50.0,
            'price_position': 50.0,
            'support_resistance_strength': 1.0,
            'autocorrelation': 0.0,
            'max_drawdown': 5.0
        }
    
    def train_regime_model(self, historical_data: Dict[str, pd.DataFrame]):
        """آموزش مدل تشخیص رژیم"""
        try:
            logger.info("Training regime detection model...")
            
            # استخراج ویژگی‌ها از داده‌های تاریخی
            all_features = []
            
            for symbol, data in historical_data.items():
                if len(data) < self.lookback_period:
                    continue
                
                # تقسیم داده‌ها به window های کوچک
                for i in range(self.lookback_period, len(data), 24):  # هر 24 ساعت
                    window_data = data.iloc[i-self.lookback_period:i]
                    features = self.extract_market_features(window_data)
                    
                    if features:
                        all_features.append(list(features.values()))
            
            if len(all_features) < 50:
                logger.warning("Insufficient training data for regime model")
                return False
            
            # نرمال‌سازی ویژگی‌ها
            features_array = np.array(all_features)
            features_scaled = self.feature_scaler.fit_transform(features_array)
            
            # آموزش مدل Gaussian Mixture
            n_components = len(self.regimes)
            self.regime_model = GaussianMixture(
                n_components=n_components,
                covariance_type='full',
                random_state=42,
                max_iter=200
            )
            
            self.regime_model.fit(features_scaled)
            
            logger.info(f"Regime model trained successfully with {len(all_features)} samples")
            return True
            
        except Exception as e:
            logger.error(f"Error training regime model: {str(e)}")
            return False
    
    def detect_regime(self, price_data: pd.DataFrame) -> RegimeDetectionResult:
        """تشخیص رژیم فعلی بازار"""
        try:
            # استخراج ویژگی‌ها
            features = self.extract_market_features(price_data)
            
            if not features:
                return self._get_default_regime_result()
            
            # پیش‌بینی رژیم
            if self.regime_model is not None:
                features_array = np.array([list(features.values())])
                features_scaled = self.feature_scaler.transform(features_array)
                
                # دریافت احتمالات
                probabilities = self.regime_model.predict_proba(features_scaled)[0]
                predicted_regime_idx = np.argmax(probabilities)
                
                # تطبیق با رژیم‌های تعریف شده
                regime_names = list(self.regimes.keys())
                current_regime = regime_names[predicted_regime_idx % len(regime_names)]
                confidence = probabilities[predicted_regime_idx]
                
                # ایجاد دیکشنری احتمالات
                regime_probabilities = {
                    regime_names[i]: probabilities[i] 
                    for i in range(min(len(regime_names), len(probabilities)))
                }
                
            else:
                # تشخیص رژیم بر اساس قوانین ساده
                current_regime, confidence, regime_probabilities = self._rule_based_detection(features)
            
            # ایجاد نتیجه
            result = RegimeDetectionResult(
                current_regime=current_regime,
                confidence=confidence,
                regime_probabilities=regime_probabilities,
                market_features=features,
                timestamp=datetime.now()
            )
            
            # اضافه کردن به تاریخچه
            self.regime_history.append(result)
            
            # نگه داشتن تنها 100 نتیجه اخیر
            if len(self.regime_history) > 100:
                self.regime_history = self.regime_history[-100:]
            
            return result
            
        except Exception as e:
            logger.error(f"Error detecting regime: {str(e)}")
            return self._get_default_regime_result()
    
    def _rule_based_detection(self, features: Dict[str, float]) -> Tuple[str, float, Dict[str, float]]:
        """تشخیص رژیم بر اساس قوانین ساده"""
        try:
            trend_strength = features.get('trend_strength', 0)
            volatility = features.get('volatility', 15)
            rsi = features.get('rsi', 50)
            
            # قوانین تشخیص
            if volatility > 25:
                regime = "high_volatility"
                confidence = min(0.9, volatility / 30)
            elif volatility < 8:
                regime = "low_volatility"
                confidence = min(0.9, (10 - volatility) / 10)
            elif trend_strength > 2 and rsi > 60:
                regime = "bull_market"
                confidence = min(0.9, trend_strength / 5)
            elif trend_strength < -2 and rsi < 40:
                regime = "bear_market"
                confidence = min(0.9, abs(trend_strength) / 5)
            else:
                regime = "sideways_market"
                confidence = 0.6
            
            # ایجاد احتمالات ساختگی
            regime_probabilities = {r: 0.1 for r in self.regimes.keys()}
            regime_probabilities[regime] = confidence
            
            # نرمال‌سازی احتمالات
            total_prob = sum(regime_probabilities.values())
            regime_probabilities = {k: v/total_prob for k, v in regime_probabilities.items()}
            
            return regime, confidence, regime_probabilities
            
        except Exception as e:
            logger.error(f"Error in rule-based detection: {str(e)}")
            return "sideways_market", 0.5, {r: 0.2 for r in self.regimes.keys()}
    
    def _get_default_regime_result(self) -> RegimeDetectionResult:
        """نتیجه پیش‌فرض در صورت خطا"""
        return RegimeDetectionResult(
            current_regime="sideways_market",
            confidence=0.5,
            regime_probabilities={r: 0.2 for r in self.regimes.keys()},
            market_features=self._get_default_features(),
            timestamp=datetime.now()
        )
    
    def get_regime_optimal_weights(self, regime_name: str) -> Dict[str, float]:
        """دریافت وزن‌های بهینه برای رژیم"""
        if regime_name in self.regimes:
            return self.regimes[regime_name].optimal_weights.copy()
        else:
            # وزن‌های پیش‌فرض
            return {
                "chronos_weight": 0.5,
                "fingpt_weight": 0.5,
                "confidence_threshold": 0.65,
                "position_size_multiplier": 1.0
            }
    
    def analyze_regime_stability(self, lookback_periods: int = 10) -> Dict[str, Any]:
        """تحلیل ثبات رژیم"""
        try:
            if len(self.regime_history) < lookback_periods:
                return {"stability": "insufficient_data", "dominant_regime": None}
            
            recent_regimes = [r.current_regime for r in self.regime_history[-lookback_periods:]]
            
            # محاسبه ثبات
            unique_regimes = set(recent_regimes)
            regime_changes = sum(1 for i in range(1, len(recent_regimes)) 
                               if recent_regimes[i] != recent_regimes[i-1])
            
            stability_score = 1 - (regime_changes / (len(recent_regimes) - 1))
            
            # رژیم غالب
            from collections import Counter
            regime_counts = Counter(recent_regimes)
            dominant_regime = regime_counts.most_common(1)[0][0]
            
            # تحلیل اعتماد
            recent_confidences = [r.confidence for r in self.regime_history[-lookback_periods:]]
            avg_confidence = np.mean(recent_confidences)
            
            return {
                "stability_score": stability_score,
                "stability": "high" if stability_score > 0.8 else "medium" if stability_score > 0.5 else "low",
                "dominant_regime": dominant_regime,
                "regime_changes": regime_changes,
                "avg_confidence": avg_confidence,
                "unique_regimes": len(unique_regimes)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing regime stability: {str(e)}")
            return {"stability": "error", "dominant_regime": None}
    
    def get_regime_transition_probability(self, from_regime: str, to_regime: str) -> float:
        """محاسبه احتمال انتقال بین رژیم‌ها"""
        try:
            if len(self.regime_history) < 10:
                return 0.1  # احتمال پیش‌فرض
            
            transitions = []
            for i in range(1, len(self.regime_history)):
                prev_regime = self.regime_history[i-1].current_regime
                curr_regime = self.regime_history[i].current_regime
                transitions.append((prev_regime, curr_regime))
            
            # شمارش انتقال‌ها
            from_count = sum(1 for t in transitions if t[0] == from_regime)
            transition_count = sum(1 for t in transitions if t[0] == from_regime and t[1] == to_regime)
            
            if from_count == 0:
                return 0.1
            
            return transition_count / from_count
            
        except Exception as e:
            logger.error(f"Error calculating transition probability: {str(e)}")
            return 0.1

def main():
    """مثال استفاده از سیستم تشخیص رژیم"""
    print("🎭 Market Regime Detection System Demo")
    print("=" * 50)
    
    # ایجاد detector
    detector = MarketRegimeDetector()
    
    # شبیه‌سازی داده‌های بازار
    dates = pd.date_range(start='2023-01-01', end='2024-01-01', freq='H')
    np.random.seed(42)
    
    # شبیه‌سازی قیمت‌ها
    returns = np.random.normal(0, 0.02, len(dates))
    prices = 100 * (1 + returns).cumprod()
    
    market_data = pd.DataFrame({
        'timestamp': dates,
        'open': prices * (1 + np.random.normal(0, 0.001, len(dates))),
        'high': prices * (1 + abs(np.random.normal(0, 0.002, len(dates)))),
        'low': prices * (1 - abs(np.random.normal(0, 0.002, len(dates)))),
        'close': prices,
        'volume': np.random.lognormal(10, 0.5, len(dates))
    })
    
    print(f"Generated {len(market_data)} data points")
    
    # تست استخراج ویژگی‌ها
    features = detector.extract_market_features(market_data)
    print(f"\n📊 Market Features:")
    for key, value in features.items():
        print(f"  {key}: {value:.2f}")
    
    # تست تشخیص رژیم
    regime_result = detector.detect_regime(market_data)
    print(f"\n🎭 Detected Regime: {regime_result.current_regime}")
    print(f"   Confidence: {regime_result.confidence:.1%}")
    print(f"   Timestamp: {regime_result.timestamp}")
    
    print(f"\n📈 Regime Probabilities:")
    for regime, prob in regime_result.regime_probabilities.items():
        print(f"  {regime}: {prob:.1%}")
    
    # تست وزن‌های بهینه
    optimal_weights = detector.get_regime_optimal_weights(regime_result.current_regime)
    print(f"\n⚖️  Optimal Weights for {regime_result.current_regime}:")
    for key, value in optimal_weights.items():
        print(f"  {key}: {value}")
    
    # شبیه‌سازی چند تشخیص
    print(f"\n🔄 Multiple Detections:")
    for i in range(5):
        # شبیه‌سازی داده‌های مختلف
        subset_data = market_data.iloc[i*100:(i+1)*100+120]
        if len(subset_data) >= detector.min_data_points:
            result = detector.detect_regime(subset_data)
            print(f"  Detection {i+1}: {result.current_regime} ({result.confidence:.1%})")
    
    # تحلیل ثبات رژیم
    stability = detector.analyze_regime_stability()
    print(f"\n🎯 Regime Stability Analysis:")
    print(f"  Stability: {stability.get('stability', 'N/A')}")
    print(f"  Dominant Regime: {stability.get('dominant_regime', 'N/A')}")
    print(f"  Regime Changes: {stability.get('regime_changes', 'N/A')}")

if __name__ == "__main__":
    main() 