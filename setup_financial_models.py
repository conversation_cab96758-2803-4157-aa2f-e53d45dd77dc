#!/usr/bin/env python3
"""
🔧 اسکریپت نصب و راه‌اندازی مدل‌های مالی
راه‌اندازی خودکار سیستم استفاده از مدل‌های Hugging Face

استفاده:
python setup_financial_models.py
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def print_header():
    """چاپ هدر"""
    print("""
🤖 راه‌اندازی سیستم مدل‌های مالی
=====================================
نصب و پیکربندی خودکار برای استفاده از مدل‌های Hugging Face
بدون نیاز به GPU قوی محلی
""")

def check_python_version():
    """بررسی نسخه پایتون"""
    version = sys.version_info
    print(f"🐍 نسخه پایتون: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ نسخه پایتون باید 3.8 یا بالاتر باشد")
        return False
    
    print("✅ نسخه پایتون مناسب است")
    return True

def install_package(package_name, import_name=None):
    """نصب پکیج"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ {package_name} از قبل نصب شده")
        return True
    except ImportError:
        print(f"📦 در حال نصب {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} با موفقیت نصب شد")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ خطا در نصب {package_name}")
            return False

def install_requirements():
    """نصب پکیج‌های مورد نیاز"""
    print("\n📦 نصب پکیج‌های مورد نیاز...")
    
    packages = [
        ("torch", "torch"),
        ("transformers", "transformers"),
        ("huggingface-hub", "huggingface_hub"),
        ("psutil", "psutil"),
        ("numpy", "numpy"),
        ("requests", "requests")
    ]
    
    all_installed = True
    for package, import_name in packages:
        if not install_package(package, import_name):
            all_installed = False
    
    return all_installed

def detect_gpu():
    """تشخیص GPU"""
    print("\n🔍 تشخیص GPU...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            print(f"✅ GPU پیدا شد: {gpu_name}")
            print(f"💾 حافظه GPU: {gpu_memory:.1f} GB")
            
            if gpu_memory > 8:
                print("🚀 GPU قوی - مدل‌های بزرگ قابل اجرا")
                return "high"
            elif gpu_memory > 4:
                print("⚙️ GPU متوسط - مدل‌های متوسط قابل اجرا")
                return "medium"
            else:
                print("⚠️ GPU کم - مدل‌های کوچک قابل اجرا")
                return "low"
        else:
            print("❌ GPU پیدا نشد - استفاده از CPU")
            return "cpu"
    except Exception as e:
        print(f"❌ خطا در تشخیص GPU: {e}")
        return "cpu"

def check_system_resources():
    """بررسی منابع سیستم"""
    print("\n💻 بررسی منابع سیستم...")
    
    try:
        import psutil
        
        # CPU
        cpu_count = psutil.cpu_count()
        print(f"🔢 تعداد CPU: {cpu_count}")
        
        # RAM
        ram_info = psutil.virtual_memory()
        ram_gb = ram_info.total / (1024**3)
        ram_available = ram_info.available / (1024**3)
        
        print(f"💾 کل RAM: {ram_gb:.1f} GB")
        print(f"📊 RAM در دسترس: {ram_available:.1f} GB")
        
        # توصیه
        if ram_gb > 16:
            print("🚀 RAM عالی - قابلیت اجرای مدل‌های بزرگ")
        elif ram_gb > 8:
            print("⚙️ RAM خوب - قابلیت اجرای مدل‌های متوسط")
        elif ram_gb > 4:
            print("⚠️ RAM کم - مدل‌های کوچک یا API")
        else:
            print("🔴 RAM خیلی کم - حتماً از API استفاده کنید")
        
        return {
            "cpu_count": cpu_count,
            "ram_gb": ram_gb,
            "ram_available": ram_available
        }
        
    except Exception as e:
        print(f"❌ خطا در بررسی منابع: {e}")
        return None

def get_hf_token():
    """دریافت Hugging Face token"""
    print("\n🔑 پیکربندی Hugging Face Token...")
    
    # بررسی environment variable
    existing_token = os.environ.get("HF_TOKEN") or os.environ.get("HUGGINGFACE_HUB_TOKEN")
    if existing_token:
        print("✅ Token از قبل تنظیم شده")
        return existing_token
    
    print("""
برای استفاده از API رایگان Hugging Face نیاز به token دارید:

1. برید به: https://huggingface.co/settings/tokens
2. Create new token کنید
3. دسترسی 'Read' انتخاب کنید
4. Token رو کپی کنید

⚠️ اگر token ندارید، روی Enter بزنید (استفاده محدود)
""")
    
    token = input("🔑 Token خود را وارد کنید: ").strip()
    
    if token:
        # تست token
        try:
            from huggingface_hub import whoami
            user_info = whoami(token)
            print(f"✅ Token معتبر - خوش آمدید {user_info.get('name', 'کاربر')}")
            
            # ذخیره در environment
            os.environ["HF_TOKEN"] = token
            return token
            
        except Exception as e:
            print(f"❌ Token نامعتبر: {e}")
            return None
    else:
        print("⚠️ بدون token، امکانات محدود خواهد بود")
        return None

def create_config_file(gpu_type, system_resources, hf_token):
    """ساخت فایل پیکربندی"""
    print("\n⚙️ ساخت فایل پیکربندی...")
    
    config = f"""# پیکربندی سیستم مدل‌های مالی
# تولید خودکار در {platform.system()} {platform.release()}

[system]
gpu_type = {gpu_type}
cpu_count = {system_resources.get('cpu_count', 'unknown') if system_resources else 'unknown'}
ram_gb = {system_resources.get('ram_gb', 'unknown') if system_resources else 'unknown'}
platform = {platform.system()}

[huggingface]
token_available = {bool(hf_token)}
cache_dir = ./hf_cache

[models]
# مدل‌های پیشنهادی بر اساس منابع سیستم
primary_model = {"ProsusAI/finbert" if gpu_type == "high" else "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"}
fallback_model = nlptown/bert-base-multilingual-uncased-sentiment
api_model = mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis

[performance]
# تنظیمات بهینه‌سازی
batch_size = {32 if gpu_type == "high" else 16 if gpu_type == "medium" else 8}
use_cache = true
cache_file = financial_cache.pkl
max_sequence_length = 512
"""
    
    config_path = Path("financial_models_config.ini")
    config_path.write_text(config, encoding='utf-8')
    print(f"✅ فایل پیکربندی ذخیره شد: {config_path}")
    
    return config_path

def create_example_usage():
    """ساخت فایل نمونه استفاده"""
    print("\n📝 ساخت فایل نمونه استفاده...")
    
    example_code = '''#!/usr/bin/env python3
"""
🧪 نمونه استفاده از سیستم مدل‌های مالی
"""

from financial_models_free_usage import FinancialModelManager, TradingSignalGenerator
import os

def main():
    # راه‌اندازی با token (اختیاری)
    hf_token = os.environ.get("HF_TOKEN")  # یا مستقیماً token بنویسید
    
    print("🚀 راه‌اندازی سیستم...")
    analyzer = FinancialModelManager(hf_token=hf_token)
    
    # نمایش آمار سیستم
    stats = analyzer.get_stats()
    print(f"📊 آمار سیستم: {stats}")
    
    # تست تحلیل احساس
    print("\\n🧪 تست تحلیل احساس:")
    
    financial_texts = [
        "Apple stock surges 20% after record quarterly earnings beat expectations",
        "Market panic spreads as inflation data shows unexpected rise",
        "Tech giants report mixed earnings amid economic uncertainty",
        "Banking sector shows resilience despite regulatory challenges",
        "Oil prices stabilize after volatile trading session"
    ]
    
    for text in financial_texts:
        result = analyzer.analyze_sentiment(text)
        if "error" not in result:
            sentiment = result['dominant_sentiment']
            confidence = result['confidence']
            print(f"📰 {text[:60]}...")
            print(f"   ➤ احساس: {sentiment} (اطمینان: {confidence:.2f})")
        else:
            print(f"❌ خطا: {result['error']}")
    
    # تست سیگنال معاملاتی
    print("\\n📈 تست سیگنال معاملاتی:")
    
    signal_generator = TradingSignalGenerator(analyzer)
    
    news_data = [
        "Federal Reserve considers dovish stance on interest rates",
        "Strong corporate earnings drive market optimism",
        "Economic indicators show steady growth momentum"
    ]
    
    price_data = [150.5, 152.3, 151.8, 153.2, 155.1]  # قیمت‌های نمونه
    
    signal = signal_generator.generate_signal(news_data, price_data)
    print(f"🎯 سیگنال معاملاتی توصیه شده: {signal}")
    
    # تحلیل دسته‌ای
    print("\\n🔄 تست تحلیل دسته‌ای:")
    
    batch_texts = [
        "Market volatility increases amid geopolitical tensions",
        "Cryptocurrency sector shows bullish momentum",
        "Energy stocks lead market gains on supply concerns",
        "Healthcare sector remains defensive amid uncertainty",
        "Technology earnings season begins with mixed signals"
    ]
    
    batch_results = analyzer.batch_analyze(batch_texts)
    
    for text, result in zip(batch_texts, batch_results):
        if "error" not in result:
            print(f"📊 {result['dominant_sentiment']} - {text[:50]}...")
        else:
            print(f"❌ خطا در: {text[:50]}...")
    
    print("\\n✅ تمام تست‌ها کامل شد!")

if __name__ == "__main__":
    main()
'''
    
    example_path = Path("example_usage.py")
    example_path.write_text(example_code, encoding='utf-8')
    print(f"✅ فایل نمونه ذخیره شد: {example_path}")
    
    return example_path

def run_basic_test():
    """اجرای تست اولیه"""
    print("\n🧪 اجرای تست اولیه...")
    
    try:
        from financial_models_free_usage import FinancialModelManager
        
        # تست سریع
        analyzer = FinancialModelManager()
        
        test_text = "Apple stock rises on positive earnings report"
        result = analyzer.analyze_sentiment(test_text)
        
        if "error" not in result:
            print(f"✅ تست موفق!")
            print(f"📰 متن: {test_text}")
            print(f"📊 نتیجه: {result['dominant_sentiment']} (اطمینان: {result['confidence']})")
            return True
        else:
            print(f"❌ خطا در تست: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ خطا در اجرای تست: {e}")
        return False

def main():
    """تابع اصلی"""
    print_header()
    
    # بررسی نسخه پایتون
    if not check_python_version():
        sys.exit(1)
    
    # نصب پکیج‌ها
    if not install_requirements():
        print("❌ خطا در نصب پکیج‌ها. لطفاً دستی نصب کنید.")
        sys.exit(1)
    
    # تشخیص GPU
    gpu_type = detect_gpu()
    
    # بررسی منابع سیستم
    system_resources = check_system_resources()
    
    # دریافت token
    hf_token = get_hf_token()
    
    # ساخت فایل پیکربندی
    config_path = create_config_file(gpu_type, system_resources, hf_token)
    
    # ساخت فایل نمونه
    example_path = create_example_usage()
    
    # اجرای تست اولیه
    test_success = run_basic_test()
    
    # گزارش نهایی
    print("\n🎉 راه‌اندازی کامل شد!")
    print(f"📁 فایل‌های ایجاد شده:")
    print(f"   - {config_path}")
    print(f"   - {example_path}")
    print(f"   - financial_models_free_usage.py")
    
    print(f"\n🚀 برای شروع:")
    print(f"   python example_usage.py")
    
    if test_success:
        print("✅ سیستم آماده استفاده است!")
    else:
        print("⚠️ ممکن است نیاز به پیکربندی اضافی باشد")
    
    print(f"\n📚 راهنمای استفاده:")
    print(f"   - برای API رایگان: https://huggingface.co/docs/api-inference")
    print(f"   - برای Google Colab: https://colab.research.google.com/")
    print(f"   - برای Kaggle: https://www.kaggle.com/code")

if __name__ == "__main__":
    main() 