#!/usr/bin/env python3
"""
تست عملکرد کامل مدل‌های آموزش دیده
شامل تمام مدل‌های PPO، A2C، Sentiment، Time Series، Deep Learning و سایرین
"""

import os
import sys
import logging
import warnings
import time
import gc
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional
import json

# اضافه کردن مسیر پروژه
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# تنظیمات پروکسی
try:
    with open('PROXY.json', 'r') as f:
        proxy_config = json.load(f)
    os.environ['HTTP_PROXY'] = proxy_config.get('http_proxy', '')
    os.environ['HTTPS_PROXY'] = proxy_config.get('https_proxy', '')
    os.environ['SOCKS_PROXY'] = proxy_config.get('socks_proxy', '')
except:
    pass

# تنظیمات لاگینگ
# Configure logging for Windows compatibility
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('model_performance_test.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

class ModelPerformanceTester:
    """تست عملکرد کامل تمام مدل‌های آموزش دیده"""
    
    def __init__(self):
        self.test_data = None
        self.models = {}
        self.results = {}
        self.model_paths = {
            'sentiment': 'models/sentiment_model.pkl',
            'time_series': 'models/time_series_model.pkl', 
            'ensemble': 'models/ensemble_model.pkl',
            'continual': 'models/continual_model.pkl',
            'deep_learning': 'models/deep_learning_model.pkl',
            'ppo': 'models/ppo_model.pkl',
            'a2c': 'models/a2c_model.pkl',
            'dqn': 'models/dqn_model.pkl'
        }
        
    def load_test_data(self):
        """بارگذاری داده‌های تست"""
        logger.info("Loading test data...")
        try:
            # Generate synthetic test data
            np.random.seed(42)
            dates = pd.date_range('2023-01-01', periods=20000, freq='H')
            
            self.test_data = pd.DataFrame({
                'timestamp': dates,
                'open': np.random.normal(100, 10, 20000),
                'high': np.random.normal(102, 10, 20000),
                'low': np.random.normal(98, 10, 20000),
                'close': np.random.normal(101, 10, 20000),
                'volume': np.random.randint(1000, 10000, 20000),
                'sentiment_score': np.random.normal(0, 1, 20000),
                'news_text': ['Sample news text'] * 20000
            })
            
            logger.info(f"Test data loaded: {len(self.test_data)} records")
            return True
        except Exception as e:
            logger.error(f"Failed to load test data: {e}")
            return False
    
    def calculate_rsi(self, prices, period=14):
        """محاسبه RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def load_models(self):
        """بارگذاری مدل‌های آموزش دیده"""
        logger.info("Loading trained models...")
        
        try:
            # Import model classes
            from models.rl_models import PPOAgent, A2CAgent, DQNAgent
            from models.sentiment_model import SentimentAnalyzer
            from models.time_series_model import TimeSeriesModel
            from models.ensemble_model import EnsembleModel
            from models.continual_learning import ContinualLearningModel
            from models.deep_learning_model import DeepLearningModel
            
            # Load each model type
            model_loaders = {
                'sentiment': self._load_sentiment_model,
                'time_series': self._load_time_series_model,
                'ensemble': self._load_ensemble_model,
                'continual': self._load_continual_model,
                'deep_learning': self._load_deep_learning_model,
                'ppo': self._load_ppo_model,
                'a2c': self._load_a2c_model,
                'dqn': self._load_dqn_model
            }
            
            for model_type, loader in model_loaders.items():
                try:
                    self.models[model_type] = loader()
                    logger.info(f"Loaded {model_type} model successfully")
                except Exception as e:
                    logger.warning(f"Failed to load {model_type} model: {e}")
                    self.models[model_type] = None
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to load models: {e}")
            return False
    
    def _load_sentiment_model(self):
        """بارگذاری مدل تحلیل احساسات"""
        try:
            from models.sentiment_model import SentimentAnalyzer
            model = SentimentAnalyzer()
            if os.path.exists(self.model_paths['sentiment']):
                model.load_model(self.model_paths['sentiment'])
            return model
        except Exception as e:
            logger.warning(f"Sentiment model load failed: {e}")
            return None
    
    def _load_time_series_model(self):
        """بارگذاری مدل سری زمانی"""
        try:
            from models.time_series_model import TimeSeriesModel
            model = TimeSeriesModel()
            if os.path.exists(self.model_paths['time_series']):
                model.load_model(self.model_paths['time_series'])
            return model
        except Exception as e:
            logger.warning(f"Time series model load failed: {e}")
            return None
    
    def _load_ensemble_model(self):
        """بارگذاری مدل ensemble"""
        try:
            from models.ensemble_model import EnsembleModel
            model = EnsembleModel()
            if os.path.exists(self.model_paths['ensemble']):
                model.load_model(self.model_paths['ensemble'])
            return model
        except Exception as e:
            logger.warning(f"Ensemble model load failed: {e}")
            return None
    
    def _load_continual_model(self):
        """بارگذاری مدل یادگیری مداوم"""
        try:
            from models.continual_learning import ContinualLearningModel
            model = ContinualLearningModel()
            if os.path.exists(self.model_paths['continual']):
                model.load_model(self.model_paths['continual'])
            return model
        except Exception as e:
            logger.warning(f"Continual learning model load failed: {e}")
            return None
    
    def _load_deep_learning_model(self):
        """بارگذاری مدل یادگیری عمیق"""
        try:
            from models.deep_learning_model import DeepLearningModel
            model = DeepLearningModel()
            if os.path.exists(self.model_paths['deep_learning']):
                model.load_model(self.model_paths['deep_learning'])
            return model
        except Exception as e:
            logger.warning(f"Deep learning model load failed: {e}")
            return None
    
    def _load_ppo_model(self):
        """بارگذاری مدل PPO"""
        try:
            from models.rl_models import PPOAgent
            model = PPOAgent()
            if os.path.exists(self.model_paths['ppo']):
                model.load_model(self.model_paths['ppo'])
            return model
        except Exception as e:
            logger.warning(f"PPO model load failed: {e}")
            return None
    
    def _load_a2c_model(self):
        """بارگذاری مدل A2C"""
        try:
            from models.rl_models import A2CAgent
            model = A2CAgent()
            if os.path.exists(self.model_paths['a2c']):
                model.load_model(self.model_paths['a2c'])
            return model
        except Exception as e:
            logger.warning(f"A2C model load failed: {e}")
            return None
    
    def _load_dqn_model(self):
        """بارگذاری مدل DQN"""
        try:
            from models.rl_models import DQNAgent
            model = DQNAgent()
            if os.path.exists(self.model_paths['dqn']):
                model.load_model(self.model_paths['dqn'])
            return model
        except Exception as e:
            logger.warning(f"DQN model load failed: {e}")
            return None
    
    def test_sentiment_model(self):
        """تست مدل تحلیل احساسات"""
        logger.info("Testing sentiment analysis model...")
        
        if not self.models.get('sentiment'):
            logger.warning("Sentiment model not available")
            return None
        
        try:
            model = self.models['sentiment']
            test_texts = self.test_data['news_text'].head(100).tolist()
            
            start_time = time.time()
            predictions = []
            for text in test_texts:
                pred = model.predict(text)
                predictions.append(pred)
            
            end_time = time.time()
            
            results = {
                'model_type': 'sentiment',
                'predictions_count': len(predictions),
                'execution_time': end_time - start_time,
                'avg_time_per_prediction': (end_time - start_time) / len(predictions),
                'predictions': predictions[:10]  # First 10 for display
            }
            
            logger.info(f"Sentiment model tested: {len(predictions)} predictions in {results['execution_time']:.2f}s")
            return results
            
        except Exception as e:
            logger.error(f"Sentiment model test failed: {e}")
            return None
    
    def test_time_series_model(self):
        """تست مدل سری زمانی"""
        logger.info("Testing time series model...")
        
        if not self.models.get('time_series'):
            logger.warning("Time series model not available")
            return None
        
        try:
            model = self.models['time_series']
            test_data = self.test_data[['open', 'high', 'low', 'close', 'volume']].head(1000)
            
            start_time = time.time()
            predictions = model.predict(test_data)
            end_time = time.time()
            
            results = {
                'model_type': 'time_series',
                'predictions_count': len(predictions) if hasattr(predictions, '__len__') else 1,
                'execution_time': end_time - start_time,
                'avg_time_per_prediction': (end_time - start_time) / (len(predictions) if hasattr(predictions, '__len__') else 1),
                'predictions': predictions[:10] if hasattr(predictions, '__len__') else predictions
            }
            
            logger.info(f"Time series model tested: {results['predictions_count']} predictions in {results['execution_time']:.2f}s")
            return results
            
        except Exception as e:
            logger.error(f"Time series model test failed: {e}")
            return None
    
    def test_ensemble_model(self):
        """تست مدل ensemble"""
        logger.info("Testing ensemble model...")
        
        if not self.models.get('ensemble'):
            logger.warning("Ensemble model not available")
            return None
        
        try:
            model = self.models['ensemble']
            test_data = self.test_data[['open', 'high', 'low', 'close', 'volume']].head(1000)
            
            start_time = time.time()
            predictions = model.predict(test_data)
            end_time = time.time()
            
            results = {
                'model_type': 'ensemble',
                'predictions_count': len(predictions) if hasattr(predictions, '__len__') else 1,
                'execution_time': end_time - start_time,
                'avg_time_per_prediction': (end_time - start_time) / (len(predictions) if hasattr(predictions, '__len__') else 1),
                'predictions': predictions[:10] if hasattr(predictions, '__len__') else predictions
            }
            
            logger.info(f"Ensemble model tested: {results['predictions_count']} predictions in {results['execution_time']:.2f}s")
            return results
            
        except Exception as e:
            logger.error(f"Ensemble model test failed: {e}")
            return None
    
    def test_continual_model(self):
        """تست مدل یادگیری مداوم"""
        logger.info("Testing continual learning model...")
        
        if not self.models.get('continual'):
            logger.warning("Continual learning model not available")
            return None
        
        try:
            model = self.models['continual']
            test_data = self.test_data[['open', 'high', 'low', 'close', 'volume']].head(1000)
            
            start_time = time.time()
            predictions = model.predict(test_data)
            end_time = time.time()
            
            results = {
                'model_type': 'continual_learning',
                'predictions_count': len(predictions) if hasattr(predictions, '__len__') else 1,
                'execution_time': end_time - start_time,
                'avg_time_per_prediction': (end_time - start_time) / (len(predictions) if hasattr(predictions, '__len__') else 1),
                'predictions': predictions[:10] if hasattr(predictions, '__len__') else predictions
            }
            
            logger.info(f"Continual learning model tested: {results['predictions_count']} predictions in {results['execution_time']:.2f}s")
            return results
            
        except Exception as e:
            logger.error(f"Continual learning model test failed: {e}")
            return None
    
    def test_deep_learning_model(self):
        """تست مدل یادگیری عمیق"""
        logger.info("Testing deep learning model...")
        
        if not self.models.get('deep_learning'):
            logger.warning("Deep learning model not available")
            return None
        
        try:
            model = self.models['deep_learning']
            test_data = self.test_data[['open', 'high', 'low', 'close', 'volume']].head(1000)
            
            start_time = time.time()
            predictions = model.predict(test_data)
            end_time = time.time()
            
            results = {
                'model_type': 'deep_learning',
                'predictions_count': len(predictions) if hasattr(predictions, '__len__') else 1,
                'execution_time': end_time - start_time,
                'avg_time_per_prediction': (end_time - start_time) / (len(predictions) if hasattr(predictions, '__len__') else 1),
                'predictions': predictions[:10] if hasattr(predictions, '__len__') else predictions
            }
            
            logger.info(f"Deep learning model tested: {results['predictions_count']} predictions in {results['execution_time']:.2f}s")
            return results
            
        except Exception as e:
            logger.error(f"Deep learning model test failed: {e}")
            return None
    
    def test_rl_models(self):
        """تست مدل‌های یادگیری تقویتی"""
        logger.info("Testing reinforcement learning models...")
        
        rl_results = {}
        
        # Test PPO
        if self.models.get('ppo'):
            logger.info("Testing PPO model...")
            try:
                model = self.models['ppo']
                test_data = self.test_data[['open', 'high', 'low', 'close', 'volume']].head(100)
                
                start_time = time.time()
                actions = []
                for _, row in test_data.iterrows():
                    state = row.values
                    action = model.predict(state)
                    actions.append(action)
                
                end_time = time.time()
                
                rl_results['ppo'] = {
                    'model_type': 'ppo',
                    'predictions_count': len(actions),
                    'execution_time': end_time - start_time,
                    'avg_time_per_prediction': (end_time - start_time) / len(actions),
                    'actions': actions[:10]
                }
                
                logger.info(f"PPO model tested: {len(actions)} actions in {rl_results['ppo']['execution_time']:.2f}s")
                
            except Exception as e:
                logger.error(f"PPO model test failed: {e}")
                rl_results['ppo'] = None
        
        # Test A2C
        if self.models.get('a2c'):
            logger.info("Testing A2C model...")
            try:
                model = self.models['a2c']
                test_data = self.test_data[['open', 'high', 'low', 'close', 'volume']].head(100)
                
                start_time = time.time()
                actions = []
                for _, row in test_data.iterrows():
                    state = row.values
                    action = model.predict(state)
                    actions.append(action)
                
                end_time = time.time()
                
                rl_results['a2c'] = {
                    'model_type': 'a2c',
                    'predictions_count': len(actions),
                    'execution_time': end_time - start_time,
                    'avg_time_per_prediction': (end_time - start_time) / len(actions),
                    'actions': actions[:10]
                }
                
                logger.info(f"A2C model tested: {len(actions)} actions in {rl_results['a2c']['execution_time']:.2f}s")
                
            except Exception as e:
                logger.error(f"A2C model test failed: {e}")
                rl_results['a2c'] = None
        
        # Test DQN
        if self.models.get('dqn'):
            logger.info("Testing DQN model...")
            try:
                model = self.models['dqn']
                test_data = self.test_data[['open', 'high', 'low', 'close', 'volume']].head(100)
                
                start_time = time.time()
                actions = []
                for _, row in test_data.iterrows():
                    state = row.values
                    action = model.predict(state)
                    actions.append(action)
                
                end_time = time.time()
                
                rl_results['dqn'] = {
                    'model_type': 'dqn',
                    'predictions_count': len(actions),
                    'execution_time': end_time - start_time,
                    'avg_time_per_prediction': (end_time - start_time) / len(actions),
                    'actions': actions[:10]
                }
                
                logger.info(f"DQN model tested: {len(actions)} actions in {rl_results['dqn']['execution_time']:.2f}s")
                
            except Exception as e:
                logger.error(f"DQN model test failed: {e}")
                rl_results['dqn'] = None
        
        return rl_results
    
    def run_all_tests(self):
        """اجرای تمام تست‌ها"""
        logger.info("Starting comprehensive model performance testing...")
        
        if not self.load_test_data():
            logger.error("Failed to load test data")
            return
        
        self.load_models()
        
        # Run individual model tests
        test_functions = [
            self.test_sentiment_model,
            self.test_time_series_model,
            self.test_ensemble_model,
            self.test_continual_model,
            self.test_deep_learning_model
        ]
        
        for test_func in test_functions:
            try:
                result = test_func()
                if result:
                    self.results[result['model_type']] = result
            except Exception as e:
                logger.error(f"Test function {test_func.__name__} failed: {e}")
        
        # Test RL models separately
        rl_results = self.test_rl_models()
        self.results.update(rl_results)
        
        # Generate summary report
        self.generate_report()
        
        logger.info("All model performance tests completed")
    
    def generate_report(self):
        """تولید گزارش عملکرد"""
        logger.info("Generating performance report...")
        
        report = {
            'test_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_models_tested': len(self.results),
            'models': {}
        }
        
        total_predictions = 0
        total_execution_time = 0
        
        for model_type, result in self.results.items():
            if result:
                report['models'][model_type] = {
                    'status': 'success',
                    'predictions_count': result.get('predictions_count', 0),
                    'execution_time': result.get('execution_time', 0),
                    'avg_time_per_prediction': result.get('avg_time_per_prediction', 0)
                }
                
                total_predictions += result.get('predictions_count', 0)
                total_execution_time += result.get('execution_time', 0)
            else:
                report['models'][model_type] = {
                    'status': 'failed',
                    'predictions_count': 0,
                    'execution_time': 0,
                    'avg_time_per_prediction': 0
                }
        
        report['summary'] = {
            'total_predictions': total_predictions,
            'total_execution_time': total_execution_time,
            'overall_avg_time_per_prediction': total_execution_time / total_predictions if total_predictions > 0 else 0
        }
        
        # Save report
        with open('model_performance_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        logger.info("=" * 50)
        logger.info("MODEL PERFORMANCE SUMMARY")
        logger.info("=" * 50)
        logger.info(f"Total models tested: {report['total_models_tested']}")
        logger.info(f"Total predictions: {total_predictions}")
        logger.info(f"Total execution time: {total_execution_time:.2f}s")
        logger.info(f"Average time per prediction: {report['summary']['overall_avg_time_per_prediction']:.4f}s")
        
        logger.info("\nIndividual Model Results:")
        for model_type, result in report['models'].items():
            status = "SUCCESS" if result['status'] == 'success' else "FAILED"
            logger.info(f"{model_type.upper()}: {status}")
            if result['status'] == 'success':
                logger.info(f"  - Predictions: {result['predictions_count']}")
                logger.info(f"  - Time: {result['execution_time']:.2f}s")
                logger.info(f"  - Avg per pred: {result['avg_time_per_prediction']:.4f}s")
        
        logger.info("=" * 50)
        logger.info("Detailed report saved to: model_performance_report.json")

def main():
    """تابع اصلی"""
    tester = ModelPerformanceTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main() 