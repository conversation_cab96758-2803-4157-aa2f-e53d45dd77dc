#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Integrated Trading System Tests
تست‌های یکپارچه کل سیستم معاملاتی
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch, MagicMock
import pytest
from datetime import datetime, timedelta
import threading
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from integrated_trading_system import IntegratedTradingSystem
from portfolio.advanced_risk_manager import RiskParameters
from core.base import TradingSignal

class TestIntegratedTradingSystem:
    """تست‌های سیستم معاملاتی یکپارچه"""
    
    def setup_method(self):
        """تنظیم اولیه"""
        with patch('integrated_trading_system.UnifiedTradingSystem'):
            with patch('integrated_trading_system.AdaptivePlutusSystem'):
                self.trading_system = IntegratedTradingSystem()
    
    def test_initialization(self):
        """تست مقداردهی اولیه"""
        assert self.trading_system.risk_params.initial_capital == 1000.0
        assert self.trading_system.risk_params.daily_profit_target_per_symbol == 5.0
        assert self.trading_system.risk_params.weekly_profit_target == 30.0
        assert self.trading_system.risk_params.monthly_profit_target == 80.0
        assert self.trading_system.risk_params.max_drawdown_percent == 10.0
        assert self.trading_system.risk_params.daily_loss_limit_percent == 4.0
        
        assert len(self.trading_system.symbols) == 5
        assert "EURUSD" in self.trading_system.symbols
        assert "GBPUSD" in self.trading_system.symbols
        
        assert self.trading_system.system_running == False
        assert self.trading_system.auto_trading_enabled == True
        assert self.trading_system.monitoring_enabled == True
    
    def test_risk_parameters_setup(self):
        """تست تنظیمات پارامترهای ریسک"""
        params = self.trading_system.risk_params
        
        assert params.initial_capital == 1000.0
        assert params.max_drawdown_percent == 10.0
        assert params.daily_loss_limit_percent == 4.0
        assert params.risk_per_trade_percent == 2.0
        assert params.max_positions == 5
        assert params.stop_loss_percent == 1.5
        assert params.take_profit_percent == 3.0
    
    def test_start_system(self):
        """تست شروع سیستم"""
        with patch.object(self.trading_system.portfolio_manager, 'start_auto_trading') as mock_start_trading:
            with patch('threading.Thread') as mock_thread:
                mock_thread_instance = Mock()
                mock_thread.return_value = mock_thread_instance
                
                result = self.trading_system.start_system()
                
                assert result == True
                assert self.trading_system.system_running == True
                assert self.trading_system.session_stats["start_time"] is not None
                mock_start_trading.assert_called_once()
                
                # Check threads are created
                assert mock_thread.call_count == 2  # monitoring and signal generation
    
    def test_stop_system(self):
        """تست توقف سیستم"""
        self.trading_system.system_running = True
        
        with patch.object(self.trading_system.portfolio_manager, 'stop_auto_trading') as mock_stop_trading:
            with patch.object(self.trading_system, 'save_session_report') as mock_save:
                
                result = self.trading_system.stop_system()
                
                assert result == True
                assert self.trading_system.system_running == False
                mock_stop_trading.assert_called_once()
                mock_save.assert_called_once()
    
    def test_generate_signal_for_symbol_success(self):
        """تست تولید سیگنال موفق"""
        with patch.object(self.trading_system.portfolio_manager.risk_manager, 'check_risk_limits') as mock_check:
            mock_check.return_value = {
                "max_drawdown_ok": True,
                "daily_loss_ok": True
            }
            
            with patch('integrated_trading_system.random.uniform') as mock_uniform:
                mock_uniform.side_effect = [0.005, 0.85]  # price change, confidence
                
                with patch('integrated_trading_system.random.choice') as mock_choice:
                    mock_choice.return_value = "buy"
                    
                    signal = self.trading_system._generate_signal_for_symbol("EURUSD")
                    
                    assert signal is not None
                    assert signal.symbol == "EURUSD"
                    assert signal.action == "buy"
                    assert signal.confidence >= 0.75
                    assert signal.price > 0
    
    def test_generate_signal_for_symbol_risk_limits(self):
        """تست تولید سیگنال با محدودیت ریسک"""
        with patch.object(self.trading_system.portfolio_manager.risk_manager, 'check_risk_limits') as mock_check:
            mock_check.return_value = {
                "max_drawdown_ok": False,
                "daily_loss_ok": True
            }
            
            signal = self.trading_system._generate_signal_for_symbol("EURUSD")
            
            assert signal is None
    
    def test_generate_signal_for_symbol_low_confidence(self):
        """تست تولید سیگنال با confidence پایین"""
        with patch.object(self.trading_system.portfolio_manager.risk_manager, 'check_risk_limits') as mock_check:
            mock_check.return_value = {
                "max_drawdown_ok": True,
                "daily_loss_ok": True
            }
            
            with patch('integrated_trading_system.random.uniform') as mock_uniform:
                mock_uniform.side_effect = [0.005, 0.60]  # price change, low confidence
                
                signal = self.trading_system._generate_signal_for_symbol("EURUSD")
                
                assert signal is None
    
    def test_generate_signal_for_symbol_existing_position(self):
        """تست تولید سیگنال برای نمادی که موقعیت دارد"""
        with patch.object(self.trading_system.portfolio_manager.risk_manager, 'check_risk_limits') as mock_check:
            mock_check.return_value = {
                "max_drawdown_ok": True,
                "daily_loss_ok": True
            }
            
            # Add existing position
            self.trading_system.portfolio_manager.risk_manager.positions["EURUSD"] = Mock()
            
            with patch('integrated_trading_system.random.uniform') as mock_uniform:
                mock_uniform.side_effect = [0.005, 0.85]  # price change, confidence
                
                signal = self.trading_system._generate_signal_for_symbol("EURUSD")
                
                assert signal is None
    
    def test_check_system_health_normal(self):
        """تست بررسی سلامت سیستم در حالت عادی"""
        with patch.object(self.trading_system.portfolio_manager, 'get_comprehensive_report') as mock_report:
            mock_report.return_value = {
                "system_status": {"risk_level": "low"},
                "portfolio_status": {"capital": {"current": 1000.0}}
            }
            
            # Should not change auto_trading_enabled
            initial_auto_trading = self.trading_system.auto_trading_enabled
            
            self.trading_system._check_system_health()
            
            assert self.trading_system.auto_trading_enabled == initial_auto_trading
    
    def test_check_system_health_extreme_risk(self):
        """تست بررسی سلامت سیستم با ریسک بحرانی"""
        with patch.object(self.trading_system.portfolio_manager, 'get_comprehensive_report') as mock_report:
            mock_report.return_value = {
                "system_status": {"risk_level": "extreme"},
                "portfolio_status": {"capital": {"current": 1000.0}}
            }
            
            self.trading_system._check_system_health()
            
            assert self.trading_system.auto_trading_enabled == False
    
    def test_check_system_health_low_capital(self):
        """تست بررسی سلامت سیستم با سرمایه کم"""
        with patch.object(self.trading_system.portfolio_manager, 'get_comprehensive_report') as mock_report:
            mock_report.return_value = {
                "system_status": {"risk_level": "low"},
                "portfolio_status": {"capital": {"current": 400.0}}  # Less than 50% of initial
            }
            
            with patch.object(self.trading_system, 'stop_system') as mock_stop:
                
                self.trading_system._check_system_health()
                
                mock_stop.assert_called_once()
    
    def test_check_profit_achievements_daily(self):
        """تست بررسی دستیابی به هدف روزانه"""
        with patch.object(self.trading_system.portfolio_manager, 'get_comprehensive_report') as mock_report:
            mock_report.return_value = {
                "profit_targets": {
                    "daily": {"achieved": True, "target": 5.0, "current": 6.0},
                    "weekly": {"achieved": False, "target": 30.0, "current": 20.0},
                    "monthly": {"achieved": False, "target": 80.0, "current": 50.0}
                }
            }
            
            self.trading_system._check_profit_achievements()
            
            assert self.trading_system.session_stats["achievement_times"]["daily"] is not None
    
    def test_check_profit_achievements_all_targets(self):
        """تست بررسی دستیابی به همه اهداف"""
        with patch.object(self.trading_system.portfolio_manager, 'get_comprehensive_report') as mock_report:
            mock_report.return_value = {
                "profit_targets": {
                    "daily": {"achieved": True, "target": 5.0, "current": 6.0},
                    "weekly": {"achieved": True, "target": 30.0, "current": 35.0},
                    "monthly": {"achieved": True, "target": 80.0, "current": 85.0}
                }
            }
            
            self.trading_system._check_profit_achievements()
            
            assert self.trading_system.session_stats["achievement_times"]["daily"] is not None
            assert self.trading_system.session_stats["achievement_times"]["weekly"] is not None
            assert self.trading_system.session_stats["achievement_times"]["monthly"] is not None
    
    def test_display_current_status(self):
        """تست نمایش وضعیت فعلی"""
        # Set start time to avoid None error
        self.trading_system.session_stats["start_time"] = datetime.now()
        
        with patch.object(self.trading_system.portfolio_manager, 'get_comprehensive_report') as mock_report:
            mock_report.return_value = {
                "portfolio_status": {
                    "capital": {"current": 1000.0, "total_pnl": 50.0, "daily_pnl": 5.0}
                },
                "profit_targets": {
                    "daily": {"current": 5.0, "target": 5.0, "progress_percent": 100.0},
                    "weekly": {"current": 30.0, "target": 30.0, "progress_percent": 100.0},
                    "monthly": {"current": 80.0, "target": 80.0, "progress_percent": 100.0}
                },
                "trading_stats": {
                    "signals_received": 10,
                    "action_rate": 50.0
                },
                "positions": {
                    "open_positions": 2
                },
                "system_status": {
                    "risk_level": "low",
                    "auto_trading_enabled": True
                }
            }
            
            # Should not raise exception
            self.trading_system.display_current_status()
    
    def test_manual_trade_success(self):
        """تست معامله دستی موفق"""
        with patch.object(self.trading_system.portfolio_manager, 'add_trading_signal') as mock_add_signal:
            mock_add_signal.return_value = True
            
            with patch.object(self.trading_system.portfolio_manager, 'execute_best_opportunity') as mock_execute:
                mock_execute.return_value = True
                
                result = self.trading_system.manual_trade("EURUSD", "buy", 0.8)
                
                assert result == True
                mock_add_signal.assert_called_once()
                mock_execute.assert_called_once()
    
    def test_manual_trade_signal_rejected(self):
        """تست معامله دستی با سیگنال رد شده"""
        with patch.object(self.trading_system.portfolio_manager, 'add_trading_signal') as mock_add_signal:
            mock_add_signal.return_value = False
            
            result = self.trading_system.manual_trade("EURUSD", "buy", 0.8)
            
            assert result == False
    
    def test_manual_trade_execution_failed(self):
        """تست معامله دستی با شکست در اجرا"""
        with patch.object(self.trading_system.portfolio_manager, 'add_trading_signal') as mock_add_signal:
            mock_add_signal.return_value = True
            
            with patch.object(self.trading_system.portfolio_manager, 'execute_best_opportunity') as mock_execute:
                mock_execute.return_value = False
                
                result = self.trading_system.manual_trade("EURUSD", "buy", 0.8)
                
                assert result == False
    
    def test_get_current_price(self):
        """تست دریافت قیمت فعلی"""
        price_eurusd = self.trading_system._get_current_price("EURUSD")
        assert price_eurusd == 1.0850
        
        price_gbpusd = self.trading_system._get_current_price("GBPUSD")
        assert price_gbpusd == 1.2650
        
        price_unknown = self.trading_system._get_current_price("UNKNOWN")
        assert price_unknown == 1.0000
    
    def test_force_close_all_positions(self):
        """تست بستن اجباری تمام موقعیت‌ها"""
        # Add mock positions
        mock_position1 = Mock()
        mock_position2 = Mock()
        
        self.trading_system.portfolio_manager.risk_manager.positions = {
            "EURUSD": mock_position1,
            "GBPUSD": mock_position2
        }
        
        with patch.object(self.trading_system.portfolio_manager.risk_manager, 'close_position') as mock_close:
            mock_close.return_value = True
            
            result = self.trading_system.force_close_all_positions()
            
            assert result == 2  # Number of positions closed
            assert mock_close.call_count == 2
    
    def test_save_session_report(self):
        """تست ذخیره گزارش جلسه"""
        self.trading_system.session_stats["start_time"] = datetime.now()
        
        with patch.object(self.trading_system.portfolio_manager, 'get_comprehensive_report') as mock_report:
            mock_report.return_value = {
                "portfolio_status": {
                    "capital": {"current": 1050.0, "total_pnl": 50.0, "daily_pnl": 5.0, "weekly_pnl": 30.0, "monthly_pnl": 80.0}
                },
                "profit_targets": {
                    "daily": {"achieved": True},
                    "weekly": {"achieved": True},
                    "monthly": {"achieved": True}
                },
                "performance": {
                    "win_rate": 75.0,
                    "total_trades": 10,
                    "avg_profit_per_trade": 5.0
                },
                "risk": {
                    "max_drawdown": 20.0
                },
                "system_status": {
                    "risk_level": "low"
                }
            }
            
            with patch('builtins.open', mock_open=True) as mock_file:
                with patch('json.dump') as mock_json:
                    
                    filename = self.trading_system.save_session_report()
                    
                    assert filename.startswith("trading_session_")
                    assert filename.endswith(".json")
                    mock_file.assert_called_once()
                    mock_json.assert_called_once()

class TestSystemIntegration:
    """تست‌های یکپارچگی سیستم"""
    
    def setup_method(self):
        """تنظیم اولیه"""
        with patch('integrated_trading_system.UnifiedTradingSystem'):
            with patch('integrated_trading_system.AdaptivePlutusSystem'):
                self.trading_system = IntegratedTradingSystem()
    
    def test_full_trading_cycle(self):
        """تست چرخه کامل معاملاتی"""
        # Set start time
        self.trading_system.session_stats["start_time"] = datetime.now()
        
        # Mock all dependencies
        with patch.object(self.trading_system.portfolio_manager.risk_manager, 'check_risk_limits') as mock_check:
            mock_check.return_value = {
                "max_drawdown_ok": True,
                "daily_loss_ok": True
            }
            
            with patch.object(self.trading_system.portfolio_manager, 'add_trading_signal') as mock_add_signal:
                mock_add_signal.return_value = True
                
                with patch.object(self.trading_system.portfolio_manager, 'execute_best_opportunity') as mock_execute:
                    mock_execute.return_value = True
                    
                    with patch.object(self.trading_system.portfolio_manager, 'get_comprehensive_report') as mock_report:
                        mock_report.return_value = {
                            "portfolio_status": {
                                "capital": {"current": 1050.0, "total_pnl": 50.0, "daily_pnl": 5.0}
                            },
                            "profit_targets": {
                                "daily": {"achieved": True, "target": 5.0, "current": 6.0},
                                "weekly": {"achieved": False, "target": 30.0, "current": 20.0},
                                "monthly": {"achieved": False, "target": 80.0, "current": 50.0}
                            },
                            "trading_stats": {
                                "signals_received": 1,
                                "action_rate": 100.0
                            },
                            "positions": {
                                "open_positions": 0
                            },
                            "system_status": {
                                "risk_level": "low",
                                "auto_trading_enabled": True
                            }
                        }
                        
                        # 1. Generate signal
                        signal = self.trading_system._generate_signal_for_symbol("EURUSD")
                        assert signal is not None
                        
                        # 2. Add signal
                        success = self.trading_system.portfolio_manager.add_trading_signal(signal)
                        assert success == True
                        
                        # 3. Execute trade
                        success = self.trading_system.portfolio_manager.execute_best_opportunity()
                        assert success == True
                        
                        # 4. Check achievements
                        self.trading_system._check_profit_achievements()
                        assert self.trading_system.session_stats["achievement_times"]["daily"] is not None
                        
                        # 5. Display status
                        self.trading_system.display_current_status()
    
    def test_risk_management_integration(self):
        """تست یکپارچگی مدیریت ریسک"""
        # Test that risk limits are properly enforced
        
        # 1. Set high drawdown
        with patch.object(self.trading_system.portfolio_manager, 'get_comprehensive_report') as mock_report:
            mock_report.return_value = {
                "system_status": {"risk_level": "extreme"},
                "portfolio_status": {"capital": {"current": 1000.0}}
            }
            
            # 2. Check system health
            self.trading_system._check_system_health()
            
            # 3. Verify auto trading is disabled
            assert self.trading_system.auto_trading_enabled == False
        
        # 4. Test signal generation with risk limits
        with patch.object(self.trading_system.portfolio_manager.risk_manager, 'check_risk_limits') as mock_check:
            mock_check.return_value = {
                "max_drawdown_ok": False,
                "daily_loss_ok": True
            }
            
            signal = self.trading_system._generate_signal_for_symbol("EURUSD")
            assert signal is None
    
    def test_profit_target_integration(self):
        """تست یکپارچگی اهداف سود"""
        # Test that profit targets are properly tracked and achieved
        
        with patch.object(self.trading_system.portfolio_manager, 'get_comprehensive_report') as mock_report:
            # First call - targets not achieved
            mock_report.return_value = {
                "profit_targets": {
                    "daily": {"achieved": False, "target": 5.0, "current": 3.0},
                    "weekly": {"achieved": False, "target": 30.0, "current": 20.0},
                    "monthly": {"achieved": False, "target": 80.0, "current": 50.0}
                }
            }
            
            self.trading_system._check_profit_achievements()
            
            # Verify no achievements recorded
            assert self.trading_system.session_stats["achievement_times"]["daily"] is None
            
            # Second call - daily target achieved
            mock_report.return_value = {
                "profit_targets": {
                    "daily": {"achieved": True, "target": 5.0, "current": 6.0},
                    "weekly": {"achieved": False, "target": 30.0, "current": 25.0},
                    "monthly": {"achieved": False, "target": 80.0, "current": 60.0}
                }
            }
            
            self.trading_system._check_profit_achievements()
            
            # Verify daily achievement recorded
            assert self.trading_system.session_stats["achievement_times"]["daily"] is not None
            assert self.trading_system.session_stats["achievement_times"]["weekly"] is None

# Test runner
if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"]) 