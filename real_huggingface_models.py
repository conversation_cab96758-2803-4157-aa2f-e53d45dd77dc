"""
🚀 مجموعه کامل مدل‌های واقعی و حرفه‌ای مالی از HuggingFace و منابع دیگر
📅 آپدیت شده: ژانویه 2025
🔥 شامل جدیدترین و قدرتمندترین مدل‌های مالی

🎯 دسته‌بندی کامل:
1. مدل‌های پیش‌بینی قیمت و Time Series
2. مدل‌های تحلیل احساسات مالی  
3. مدل‌های زبان بزرگ مالی (Financial LLMs)
4. مدل‌های چندحالته مالی
5. مدل‌های تحلیل اسناد مالی
6. مدل‌های پورتفولیو و ریسک
"""

# ====================== TIME SERIES & PRICE FORECASTING MODELS ======================
TIME_SERIES_MODELS = {
    # 🔥 Amazon Chronos - جدیدترین و سریع‌ترین مدل‌های Time Series
    "amazon/chronos-bolt-base": "🚀 Chronos-Bolt Base - 250x سریع‌تر، پیش‌بینی فوق‌العاده دقیق",
    "amazon/chronos-bolt-small": "⚡ Chronos-Bolt Small - مدل سبک و سریع برای پیش‌بینی",
    "amazon/chronos-bolt-mini": "💨 Chronos-Bolt Mini - برای استفاده‌های سبک",
    "amazon/chronos-bolt-tiny": "🔸 Chronos-Bolt Tiny - کوچک‌ترین مدل Chronos",
    
    # نسخه‌های کلاسیک Chronos
    "amazon/chronos-t5-large": "📈 Chronos T5 Large - مدل بزرگ و دقیق",
    "amazon/chronos-t5-base": "📊 Chronos T5 Base - مدل متوسط و کارآمد", 
    "amazon/chronos-t5-small": "📉 Chronos T5 Small - مدل کوچک",
    "amazon/chronos-t5-mini": "🔹 Chronos T5 Mini - نسخه مینی",
    "amazon/chronos-t5-tiny": "🔸 Chronos T5 Tiny - کوچک‌ترین نسخه",
    
    # مدل‌های پیشرفته Time Series
    "Salesforce/moirai-1.0-R-base": "🧠 Moirai - مدل Foundation برای Time Series",
    "google/timesfm-1.0-200m": "⏰ TimesFM - مدل Google برای پیش‌بینی زمانی",
    "ibm-granite/granite-timeseries-ttm-v1": "💎 Granite TTM - مدل IBM برای سری‌های زمانی",
    "microsoft/DiTS": "🔮 DiTS - مدل Microsoft برای پیش‌بینی",
    "huggingface/setfit-absa-bge-small-v1.5": "🎯 SetFit ABSA - تحلیل جنبه‌محور",
}

# ====================== FINANCIAL SENTIMENT ANALYSIS MODELS ======================
SENTIMENT_ANALYSIS_MODELS = {
    # مدل‌های تحلیل احساسات مالی پیشرفته
    "ProsusAI/finbert": "💰 FinBERT - تحلیل احساسات مالی حرفه‌ای",
    "zhayunduo/roberta-base-stocktwits-finetuned": "📱 RoBERTa StockTwits - تحلیل پیام‌های سهام",
    "ElKulako/cryptobert": "₿ CryptoBERT - تحلیل احساسات ارز دیجیتال", 
    "nlptown/bert-base-multilingual-uncased-sentiment": "🌍 BERT Multilingual Sentiment - چندزبانه",
    "cardiffnlp/twitter-roberta-base-sentiment-latest": "🐦 Twitter RoBERTa - تحلیل احساسات توییتر",
    "ahmedrachid/FinancialBERT-Sentiment-Analysis": "📊 Financial BERT Sentiment - تحلیل مالی",
    "bilalzafar/FinAI-BERT": "🤖 FinAI-BERT - تشخیص AI در گزارش‌های مالی",
    "StephanAkkerman/FinTwitBERT-sentiment": "💬 FinTwit BERT - تحلیل احساسات مالی توییتر",
}

# ====================== FINANCIAL LARGE LANGUAGE MODELS (LLMs) ======================
FINANCIAL_LLMS = {
    # مدل‌های زبان بزرگ مالی
    "FinGPT/fingpt-forecaster_dow30_llama2-7b_lora": "🔥 FinGPT Forecaster - پیش‌بینی Dow 30",
    "bavest/fin-llama-33b-merged": "🦙 Fin-Llama 33B - مدل بزرگ مالی",
    "arcee-ai/Llama-3-SEC-Base": "📋 Llama-3 SEC - تحلیل اسناد SEC",
    "ChanceFocus/finma-7b-nlp": "💡 FinMA 7B - مدل کامل NLP مالی",
    "ChanceFocus/finma-7b-full": "🎯 FinMA 7B Full - نسخه کامل",
    "microsoft/DialoGPT-medium": "💭 DialoGPT - گفتگوی مالی",
    "OpenAssistant/oasst-sft-4-pythia-12b-epoch-3.5": "🤖 Open Assistant - دستیار مالی",
}

# ====================== MULTIMODAL FINANCIAL MODELS ======================
MULTIMODAL_MODELS = {
    # مدل‌های چندحالته (متن + تصویر + جدول)
    "microsoft/layoutlmv3-base": "📄 LayoutLMv3 - تحلیل اسناد مالی",
    "unstructured-io/layoutlm-invoices": "🧾 LayoutLM Invoices - تحلیل فاکتور",
    "microsoft/table-transformer-structure-recognition": "📊 Table Transformer - تشخیص جدول",
    "microsoft/dit-base-finetuned-rvlcdip": "📋 DiT Document Classification - دسته‌بندی اسناد",
    "impira/layoutlm-document-qa": "❓ LayoutLM QA - پرسش و پاسخ اسناد",
    "google/pix2struct-base": "🖼️ Pix2Struct - تبدیل تصویر به متن",
}

# ====================== DOCUMENT & REPORT ANALYSIS MODELS ======================
DOCUMENT_ANALYSIS_MODELS = {
    # مدل‌های تحلیل اسناد و گزارش‌های مالی
    "microsoft/DialoGPT-medium": "📰 گزارش‌گیری خودکار",
    "facebook/bart-large-cnn": "📝 BART CNN - خلاصه‌سازی اخبار مالی",
    "philschmid/bart-large-cnn-samsum": "💬 BART SamSum - خلاصه گفتگو",
    "google/pegasus-xsum": "📄 Pegasus - خلاصه‌سازی حرفه‌ای",
    "allenai/longformer-base-4096": "📚 Longformer - اسناد طولانی",
    "microsoft/layoutlmv2-base-uncased": "📑 LayoutLMv2 - ساختار اسناد",
}

# ====================== PORTFOLIO & RISK MODELS ======================
PORTFOLIO_RISK_MODELS = {
    # مدل‌های پورتفولیو و مدیریت ریسک (ترکیب از منابع مختلف)
    "microsoft/DialoGPT-small": "⚖️ ارزیابی ریسک",
    "huggingface/CodeBERTa-small-v1": "📊 تحلیل پورتفولیو",
    "sentence-transformers/all-MiniLM-L6-v2": "🔗 تحلیل همبستگی دارایی‌ها",
    "microsoft/codebert-base": "💻 CodeBERT - تحلیل الگوریتم‌های مالی",
}

# ====================== SPECIALIZED TRADING MODELS ======================
TRADING_MODELS = {
    # مدل‌های تخصصی معاملات
    "microsoft/DialoGPT-large": "🤖 ربات معاملاتی هوشمند",
    "facebook/opt-1.3b": "🎯 OPT 1.3B - تصمیم‌گیری معاملات",
    "EleutherAI/gpt-neo-1.3B": "🧠 GPT-Neo - استراتژی معاملات",
    "microsoft/prophetnet-large-uncased": "🔮 ProphetNet - پیش‌بینی روند",
}

# ====================== CRYPTO & ALTERNATIVE ASSETS ======================
CRYPTO_MODELS = {
    # مدل‌های ارزهای دیجیتال و دارایی‌های جایگزین
    "ElKulako/cryptobert": "₿ CryptoBERT - تحلیل ارز دیجیتال",
    "huggingface/distilbert-base-uncased": "🪙 DistilBERT - تحلیل سریع ارز",
    "microsoft/DialoGPT-medium": "💎 تحلیل دارایی‌های جایگزین",
}

# ====================== ECONOMIC INDICATOR MODELS ======================
ECONOMIC_MODELS = {
    # مدل‌های شاخص‌های اقتصادی
    "facebook/bart-base": "📈 BART - تحلیل اقتصادی",
    "microsoft/DialoGPT-small": "📊 شاخص‌های کلان اقتصادی",
    "google/t5-small": "🎯 T5 - پیش‌بینی اقتصادی",
}

# ====================== MODEL USAGE EXAMPLES ======================
USAGE_EXAMPLES = {
    "price_prediction": """
# مثال پیش‌بینی قیمت با Chronos-Bolt
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

model_name = "amazon/chronos-bolt-base"
model = AutoModelForCausalLM.from_pretrained(model_name, torch_dtype=torch.bfloat16)
tokenizer = AutoTokenizer.from_pretrained(model_name)

# پیش‌بینی قیمت 12 روز آینده
forecast = model.predict(historical_data, prediction_length=12)
""",
    
    "sentiment_analysis": """
# مثال تحلیل احساسات مالی
from transformers import pipeline

sentiment_analyzer = pipeline(
    "sentiment-analysis",
    model="ProsusAI/finbert",
    tokenizer="ProsusAI/finbert"
)

result = sentiment_analyzer("اخبار مثبت از شرکت اپل منتشر شد")
""",
    
    "document_analysis": """
# مثال تحلیل اسناد مالی
from transformers import LayoutLMv3Processor, LayoutLMv3ForQuestionAnswering

processor = LayoutLMv3Processor.from_pretrained("microsoft/layoutlmv3-base")
model = LayoutLMv3ForQuestionAnswering.from_pretrained("microsoft/layoutlmv3-base")

# تحلیل صورت مالی
answer = model(document_image, question="سود خالص چقدر است؟")
"""
}

# ====================== ALL MODELS COMBINED ======================
ALL_FINANCIAL_MODELS = {
    **TIME_SERIES_MODELS,
    **SENTIMENT_ANALYSIS_MODELS, 
    **FINANCIAL_LLMS,
    **MULTIMODAL_MODELS,
    **DOCUMENT_ANALYSIS_MODELS,
    **PORTFOLIO_RISK_MODELS,
    **TRADING_MODELS,
    **CRYPTO_MODELS,
    **ECONOMIC_MODELS
}

# ====================== MODEL CATEGORIES INFO ======================
MODEL_CATEGORIES = {
    "time_series": {
        "count": len(TIME_SERIES_MODELS),
        "description": "مدل‌های پیش‌بینی قیمت و سری‌های زمانی",
        "best_models": ["amazon/chronos-bolt-base", "amazon/chronos-t5-large"]
    },
    "sentiment": {
        "count": len(SENTIMENT_ANALYSIS_MODELS), 
        "description": "مدل‌های تحلیل احساسات مالی",
        "best_models": ["ProsusAI/finbert", "bilalzafar/FinAI-BERT"]
    },
    "llms": {
        "count": len(FINANCIAL_LLMS),
        "description": "مدل‌های زبان بزرگ مالی",
        "best_models": ["FinGPT/fingpt-forecaster_dow30_llama2-7b_lora", "bavest/fin-llama-33b-merged"]
    },
    "multimodal": {
        "count": len(MULTIMODAL_MODELS),
        "description": "مدل‌های چندحالته",
        "best_models": ["microsoft/layoutlmv3-base", "google/pix2struct-base"]
    },
    "documents": {
        "count": len(DOCUMENT_ANALYSIS_MODELS),
        "description": "مدل‌های تحلیل اسناد",
        "best_models": ["microsoft/layoutlmv2-base-uncased", "facebook/bart-large-cnn"]
    },
    "portfolio": {
        "count": len(PORTFOLIO_RISK_MODELS),
        "description": "مدل‌های پورتفولیو و ریسک",
        "best_models": ["sentence-transformers/all-MiniLM-L6-v2"]
    },
    "trading": {
        "count": len(TRADING_MODELS),
        "description": "مدل‌های معاملاتی",
        "best_models": ["microsoft/prophetnet-large-uncased"]
    },
    "crypto": {
        "count": len(CRYPTO_MODELS),
        "description": "مدل‌های ارز دیجیتال",
        "best_models": ["ElKulako/cryptobert"]
    }
}

# ====================== INSTALLATION AND DOWNLOAD FUNCTIONS ======================
def get_model_info(model_name):
    """دریافت اطلاعات یک مدل خاص"""
    if model_name in ALL_FINANCIAL_MODELS:
        return {
            "name": model_name,
            "description": ALL_FINANCIAL_MODELS[model_name],
            "category": get_model_category(model_name),
            "url": f"https://huggingface.co/{model_name}"
        }
    return None

def get_model_category(model_name):
    """تشخیص دسته‌بندی مدل"""
    for category, models in [
        ("time_series", TIME_SERIES_MODELS),
        ("sentiment", SENTIMENT_ANALYSIS_MODELS),
        ("llms", FINANCIAL_LLMS),
        ("multimodal", MULTIMODAL_MODELS),
        ("documents", DOCUMENT_ANALYSIS_MODELS),
        ("portfolio", PORTFOLIO_RISK_MODELS),
        ("trading", TRADING_MODELS),
        ("crypto", CRYPTO_MODELS),
        ("economic", ECONOMIC_MODELS)
    ]:
        if model_name in models:
            return category
    return "unknown"

def get_recommended_models(use_case="general"):
    """دریافت مدل‌های پیشنهادی برای یک کاربرد خاص"""
    recommendations = {
        "price_prediction": [
            "amazon/chronos-bolt-base",
            "amazon/chronos-t5-large", 
            "Salesforce/moirai-1.0-R-base"
        ],
        "sentiment_analysis": [
            "ProsusAI/finbert",
            "bilalzafar/FinAI-BERT",
            "zhayunduo/roberta-base-stocktwits-finetuned"
        ],
        "document_analysis": [
            "microsoft/layoutlmv3-base",
            "facebook/bart-large-cnn",
            "google/pegasus-xsum"
        ],
        "portfolio_management": [
            "sentence-transformers/all-MiniLM-L6-v2",
            "microsoft/codebert-base"
        ],
        "trading": [
            "microsoft/prophetnet-large-uncased",
            "facebook/opt-1.3b"
        ],
        "crypto": [
            "ElKulako/cryptobert",
            "huggingface/distilbert-base-uncased"
        ],
        "general": [
            "amazon/chronos-bolt-base",
            "ProsusAI/finbert",
            "FinGPT/fingpt-forecaster_dow30_llama2-7b_lora",
            "microsoft/layoutlmv3-base"
        ]
    }
    
    return recommendations.get(use_case, recommendations["general"])

def print_model_summary():
    """چاپ خلاصه کامل مدل‌ها"""
    print("🚀 مجموعه کامل مدل‌های مالی هوش مصنوعی")
    print("=" * 60)
    
    total_models = len(ALL_FINANCIAL_MODELS)
    print(f"📊 تعداد کل مدل‌ها: {total_models}")
    print()
    
    for category, info in MODEL_CATEGORIES.items():
        print(f"🔸 {info['description']}: {info['count']} مدل")
        print(f"   بهترین‌ها: {', '.join(info['best_models'][:2])}")
        print()
    
    print("🎯 مدل‌های پیشنهادی برای شروع:")
    for model in get_recommended_models("general")[:3]:
        print(f"   ✅ {model}")
    
    print("\n💡 برای دریافت لیست کامل: print(list(ALL_FINANCIAL_MODELS.keys()))")

if __name__ == "__main__":
    print_model_summary() 