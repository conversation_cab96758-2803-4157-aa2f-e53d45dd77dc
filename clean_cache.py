import os
import shutil
import sys

def clean_pycache():
    """Clean all __pycache__ directories in the project."""
    count = 0
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            print(f"Removing {pycache_path}")
            try:
                shutil.rmtree(pycache_path)
                count += 1
            except Exception as e:
                print(f"Error removing {pycache_path}: {e}")
    
    print(f"Removed {count} __pycache__ directories")

if __name__ == "__main__":
    clean_pycache() 