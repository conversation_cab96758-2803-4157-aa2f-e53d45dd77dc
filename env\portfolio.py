import cvxpy as cp

cp.settings.SOLVER = "SCS"  # یا "ECOS"

from pypfopt.efficient_frontier import EfficientFrontier
import pandas as pd


class PortfolioManager:
    def __init__(self, dfs):
        self.dfs = dfs

    def optimize(self):
        for symbol, df in self.dfs.items():
            print(f"Columns for {symbol}: {df.columns.tolist()}")
        returns = pd.concat(
            [df["close"].pct_change().dropna() for df in self.dfs.values()], axis=1
        )
        cov_matrix = returns.cov() * 252
        mean_returns = returns.mean() * 252
        num_assets = len(self.dfs)
        weights = [1 / num_assets] * num_assets
        return weights
