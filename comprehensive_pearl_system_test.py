"""
🧪 Comprehensive Pearl-3x7B System Test
تست جامع و پیشرفته سیستم Pearl-3x7B

این تست شامل:
- تست کامل سیستم مدیریت حافظه
- تست سیستم لاگینگ پیشرفته
- تست پردازشگر داده با تشخیص ناهنجاری
- تست آموزش مدل‌های احساسات
- تست یکپارچگی کل سیستم
- تست عملکرد تحت فشار
"""

import os
import sys
import time
import threading
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
import json
import gc
import psutil

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import Pearl-3x7B components
from core.memory_manager import (
    IntelligentMemoryManager, 
    create_memory_manager_for_pearl,
    prepare_memory_for_training,
    cleanup_after_training,
    get_memory_status_for_pearl
)
from core.logger import (
    get_pearl_logger,
    TrainingSession,
    PearlIntelligentLogger,
    TrainingLogEntry,
    EvaluationLogEntry
)
from data.preprocessor import (
    AdvancedDataPreprocessor,
    AnomalyDetectionSystem,
    DatasetBuilder,
    create_preprocessor_for_pearl,
    process_raw_data_directory
)
from training.train_sentiment import (
    PearlSentimentTrainer,
    SentimentTrainingConfig,
    train_all_sentiment_models
)

class ComprehensivePearlSystemTest:
    """تست جامع سیستم Pearl-3x7B"""
    
    def __init__(self):
        self.logger = get_pearl_logger("system_test")
        self.test_results = {}
        self.start_time = time.time()
        self.memory_manager = create_memory_manager_for_pearl()
        
        # Test configuration
        self.stress_test_duration = 30  # seconds
        self.memory_stress_iterations = 100
        self.concurrent_threads = 4
        
        self.logger.logger.info("🧪 Comprehensive Pearl System Test initialized")
    
    def test_memory_management_system(self) -> Dict[str, Any]:
        """تست سیستم مدیریت حافظه"""
        self.logger.logger.info("🧠 Testing Memory Management System...")
        
        test_results = {
            'basic_functionality': False,
            'memory_pools': False,
            'anomaly_detection': False,
            'training_optimization': False,
            'stress_test': False,
            'memory_leaks': False
        }
        
        try:
            # Test 1: Basic functionality
            initial_memory = self.memory_manager.get_memory_stats()
            self.logger.log_memory_usage(initial_memory.process_memory, "Initial test memory")
            test_results['basic_functionality'] = True
            
            # Test 2: Memory pools
            from core.memory_manager import MemoryPoolType
            pool = self.memory_manager.get_pool(MemoryPoolType.MODEL_WEIGHTS)
            pool_stats = pool.get_stats()
            self.logger.logger.info(f"Memory pool stats: {pool_stats}")
            test_results['memory_pools'] = True
            
            # Test 3: AI model memory management
            session_id = self.memory_manager.optimize_for_model_training("test_model", 32, 512)
            if session_id:
                self.memory_manager.ai_model_manager.end_training_session(session_id)
                test_results['training_optimization'] = True
            
            # Test 4: Memory stress test
            stress_result = self._memory_stress_test()
            test_results['stress_test'] = stress_result
            
            # Test 5: Memory leak detection
            leak_result = self._memory_leak_test()
            test_results['memory_leaks'] = not leak_result  # True if no leaks
            
            self.logger.logger.info("✅ Memory Management System test completed")
            
        except Exception as e:
            self.logger.logger.error(f"❌ Memory Management test failed: {e}")
            test_results['error'] = str(e)
        
        return test_results
    
    def test_logging_system(self) -> Dict[str, Any]:
        """تست سیستم لاگینگ پیشرفته"""
        self.logger.logger.info("📝 Testing Advanced Logging System...")
        
        test_results = {
            'basic_logging': False,
            'structured_logging': False,
            'training_session': False,
            'performance_logging': False,
            'export_functionality': False,
            'concurrent_logging': False
        }
        
        try:
            # Test 1: Basic logging
            self.logger.log_pearl_decision("test_decision", 0.95, "test reasoning")
            test_results['basic_logging'] = True
            
            # Test 2: Training session logging
            with TrainingSession(self.logger, "test_model") as session:
                for epoch in range(3):
                    for batch in range(5):
                        self.logger.log_training_step(
                            "test_model", epoch, batch, 
                            np.random.uniform(0.1, 1.0),
                            np.random.uniform(0.7, 0.95),
                            2e-5, 1024.0, 0.1
                        )
                
                # Test evaluation logging
                self.logger.log_evaluation_result(
                    "test_model", "test_dataset",
                    {"accuracy": 0.92, "f1": 0.89, "precision": 0.91},
                    15.5, 1536.0
                )
            test_results['training_session'] = True
            
            # Test 3: Performance logging
            self.logger.log_performance_metric("throughput", 1250.5, "samples/sec")
            self.logger.log_memory_usage(2048.0, "peak during test")
            test_results['performance_logging'] = True
            
            # Test 4: Export functionality
            training_file = self.logger.export_training_logs()
            eval_file = self.logger.export_evaluation_logs()
            summary = self.logger.get_training_summary()
            
            if os.path.exists(training_file) and os.path.exists(eval_file) and summary:
                test_results['export_functionality'] = True
            
            # Test 5: Concurrent logging stress test
            concurrent_result = self._concurrent_logging_test()
            test_results['concurrent_logging'] = concurrent_result
            
            self.logger.logger.info("✅ Logging System test completed")
            
        except Exception as e:
            self.logger.logger.error(f"❌ Logging System test failed: {e}")
            test_results['error'] = str(e)
        
        return test_results
    
    def test_data_preprocessing_system(self) -> Dict[str, Any]:
        """تست سیستم پردازش داده پیشرفته"""
        self.logger.logger.info("🔧 Testing Advanced Data Preprocessing System...")
        
        test_results = {
            'basic_preprocessing': False,
            'anomaly_detection': False,
            'dataset_building': False,
            'technical_indicators': False,
            'data_validation': False,
            'performance_test': False
        }
        
        try:
            # Create test data
            test_data = self._generate_test_financial_data(1000)
            
            # Test 1: Basic preprocessing
            preprocessor = create_preprocessor_for_pearl()
            processed_data = preprocessor.preprocess(test_data, "TEST", "H1", create_datasets=False)
            
            if len(processed_data) > 0 and 'rsi_14' in processed_data.columns:
                test_results['basic_preprocessing'] = True
            
            # Test 2: Anomaly detection
            anomaly_detector = AnomalyDetectionSystem()
            anomaly_data = anomaly_detector.detect_price_anomalies(test_data.copy())
            anomaly_data = anomaly_detector.detect_statistical_outliers(anomaly_data)
            cleaned_data = anomaly_detector.clean_anomalies(anomaly_data)
            
            if 'is_anomaly' in anomaly_data.columns and len(cleaned_data) < len(test_data):
                test_results['anomaly_detection'] = True
            
            # Test 3: Dataset building
            dataset_builder = DatasetBuilder("test_datasets")
            datasets = dataset_builder.create_training_datasets(processed_data, "TEST", "H1")
            
            if all(key in datasets for key in ['train', 'validation', 'test']):
                test_results['dataset_building'] = True
            
            # Test 4: Technical indicators validation
            required_indicators = ['rsi_14', 'macd', 'sma_20', 'bb_upper', 'atr_14']
            if all(indicator in processed_data.columns for indicator in required_indicators):
                test_results['technical_indicators'] = True
            
            # Test 5: Data validation
            validation_passed = self._validate_processed_data(processed_data)
            test_results['data_validation'] = validation_passed
            
            # Test 6: Performance test
            performance_result = self._preprocessing_performance_test()
            test_results['performance_test'] = performance_result
            
            self.logger.logger.info("✅ Data Preprocessing System test completed")
            
        except Exception as e:
            self.logger.logger.error(f"❌ Data Preprocessing test failed: {e}")
            test_results['error'] = str(e)
        
        return test_results
    
    def test_sentiment_training_system(self) -> Dict[str, Any]:
        """تست سیستم آموزش مدل‌های احساسات"""
        self.logger.logger.info("🎯 Testing Sentiment Training System...")
        
        test_results = {
            'trainer_initialization': False,
            'data_preparation': False,
            'model_initialization': False,
            'training_simulation': False,
            'evaluation_system': False,
            'model_saving': False
        }
        
        try:
            # Test 1: Trainer initialization
            config = SentimentTrainingConfig(
                model_name="TestFinBERT",
                batch_size=8,
                num_epochs=1,
                learning_rate=2e-5
            )
            trainer = PearlSentimentTrainer(config)
            test_results['trainer_initialization'] = True
            
            # Test 2: Data preparation
            train_data, val_data, test_data = trainer.prepare_data()
            if all(data for data in [train_data, val_data, test_data]):
                test_results['data_preparation'] = True
            
            # Test 3: Model initialization
            trainer.initialize_model()
            if trainer.model is not None:
                test_results['model_initialization'] = True
            
            # Test 4: Training simulation
            epoch_metrics = trainer.train_epoch(train_data, 0)
            if 'loss' in epoch_metrics and 'accuracy' in epoch_metrics:
                test_results['training_simulation'] = True
            
            # Test 5: Evaluation system
            eval_metrics = trainer.evaluate_model(val_data)
            if len(eval_metrics) > 0 and 'eval_accuracy' in eval_metrics:
                test_results['evaluation_system'] = True
            
            # Test 6: Model saving
            model_path = trainer.save_model()
            if os.path.exists(model_path):
                test_results['model_saving'] = True
            
            self.logger.logger.info("✅ Sentiment Training System test completed")
            
        except Exception as e:
            self.logger.logger.error(f"❌ Sentiment Training test failed: {e}")
            test_results['error'] = str(e)
        
        return test_results

    def test_system_integration(self) -> Dict[str, Any]:
        """تست یکپارچگی کل سیستم"""
        self.logger.logger.info("🔗 Testing System Integration...")

        test_results = {
            'cross_component_communication': False,
            'end_to_end_workflow': False,
            'error_handling': False,
            'resource_cleanup': False,
            'concurrent_operations': False
        }

        try:
            # Test 1: Cross-component communication
            # Memory manager + Logger integration
            session_id = prepare_memory_for_training("integration_test", 16, 256)
            with TrainingSession(self.logger, "integration_test") as training_session:
                self.logger.log_memory_usage(
                    self.memory_manager.get_memory_stats().process_memory,
                    "Integration test"
                )
            cleanup_after_training(session_id)
            test_results['cross_component_communication'] = True

            # Test 2: End-to-end workflow
            # Data preprocessing -> Model training -> Evaluation
            test_data = self._generate_test_financial_data(100)
            preprocessor = create_preprocessor_for_pearl()
            processed_data = preprocessor.preprocess(test_data, "INTEGRATION", "M5")

            config = SentimentTrainingConfig("IntegrationTest", batch_size=4, num_epochs=1)
            trainer = PearlSentimentTrainer(config)
            result = trainer.run_training()

            if result.get('training_completed', False):
                test_results['end_to_end_workflow'] = True

            # Test 3: Error handling
            error_handled = self._test_error_handling()
            test_results['error_handling'] = error_handled

            # Test 4: Resource cleanup
            cleanup_successful = self._test_resource_cleanup()
            test_results['resource_cleanup'] = cleanup_successful

            # Test 5: Concurrent operations
            concurrent_result = self._test_concurrent_operations()
            test_results['concurrent_operations'] = concurrent_result

            self.logger.logger.info("✅ System Integration test completed")

        except Exception as e:
            self.logger.logger.error(f"❌ System Integration test failed: {e}")
            test_results['error'] = str(e)

        return test_results

    def _memory_stress_test(self) -> bool:
        """تست فشار حافظه"""
        try:
            initial_memory = psutil.virtual_memory().percent

            # Create memory pressure
            large_arrays = []
            for i in range(self.memory_stress_iterations):
                arr = np.random.random((1000, 1000))
                large_arrays.append(arr)

                if i % 10 == 0:
                    self.memory_manager.check_memory_pressure()

            # Check if memory management responded
            peak_memory = psutil.virtual_memory().percent

            # Cleanup
            del large_arrays
            gc.collect()

            # Memory should have been managed
            return peak_memory < 95.0  # System didn't crash

        except Exception as e:
            self.logger.logger.error(f"Memory stress test failed: {e}")
            return False

    def _memory_leak_test(self) -> bool:
        """تست نشت حافظه"""
        try:
            initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

            # Simulate training cycles
            for cycle in range(10):
                session_id = prepare_memory_for_training(f"leak_test_{cycle}", 16, 256)

                # Simulate some work
                data = np.random.random((1000, 100))
                processed = data * 2
                del data, processed

                cleanup_after_training(session_id)
                gc.collect()

            final_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory

            # Memory increase should be minimal (< 50MB)
            return memory_increase < 50.0

        except Exception as e:
            self.logger.logger.error(f"Memory leak test failed: {e}")
            return True  # Assume no leak if test fails

    def _concurrent_logging_test(self) -> bool:
        """تست لاگینگ همزمان"""
        try:
            def logging_worker(worker_id):
                worker_logger = get_pearl_logger(f"worker_{worker_id}")
                for i in range(50):
                    worker_logger.log_training_step(
                        f"model_{worker_id}", 1, i,
                        np.random.uniform(0.1, 1.0),
                        np.random.uniform(0.7, 0.95)
                    )
                    time.sleep(0.01)

            threads = []
            for i in range(self.concurrent_threads):
                thread = threading.Thread(target=logging_worker, args=(i,))
                threads.append(thread)
                thread.start()

            for thread in threads:
                thread.join()

            return True

        except Exception as e:
            self.logger.logger.error(f"Concurrent logging test failed: {e}")
            return False

    def _generate_test_financial_data(self, size: int) -> pd.DataFrame:
        """تولید داده‌های مالی تست"""
        dates = pd.date_range(start='2023-01-01', periods=size, freq='H')

        # Generate realistic OHLC data
        base_price = 1.1000
        data = []

        for i, date in enumerate(dates):
            # Random walk with some trend
            change = np.random.normal(0, 0.0001)
            base_price += change

            # OHLC with realistic relationships
            open_price = base_price + np.random.normal(0, 0.00005)
            high_price = open_price + abs(np.random.normal(0, 0.0001))
            low_price = open_price - abs(np.random.normal(0, 0.0001))
            close_price = open_price + np.random.normal(0, 0.00005)

            # Ensure OHLC relationships
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)

            # Add some anomalies
            if np.random.random() < 0.05:  # 5% anomalies
                high_price *= np.random.uniform(1.01, 1.05)
                low_price *= np.random.uniform(0.95, 0.99)

            data.append({
                'datetime': date,
                'open': round(open_price, 5),
                'high': round(high_price, 5),
                'low': round(low_price, 5),
                'close': round(close_price, 5),
                'volume': np.random.randint(1000, 10000)
            })

        return pd.DataFrame(data)

    def _validate_processed_data(self, df: pd.DataFrame) -> bool:
        """اعتبارسنجی داده‌های پردازش شده"""
        try:
            # Check for required columns
            required_cols = ['open', 'high', 'low', 'close', 'rsi_14', 'macd']
            if not all(col in df.columns for col in required_cols):
                return False

            # Check for NaN values
            if df.isnull().any().any():
                return False

            # Check OHLC relationships
            if not (df['high'] >= df['low']).all():
                return False

            if not (df['high'] >= df['open']).all():
                return False

            if not (df['high'] >= df['close']).all():
                return False

            # Check indicator ranges
            if not (df['rsi_14'] >= 0).all() or not (df['rsi_14'] <= 100).all():
                return False

            return True

        except Exception:
            return False

    def _preprocessing_performance_test(self) -> bool:
        """تست عملکرد پردازش داده"""
        try:
            # Test with larger dataset
            large_data = self._generate_test_financial_data(5000)

            start_time = time.time()
            preprocessor = create_preprocessor_for_pearl()
            processed_data = preprocessor.preprocess(large_data, "PERF_TEST", "M15")
            processing_time = time.time() - start_time

            # Should process 5000 records in reasonable time (< 30 seconds)
            return processing_time < 30.0 and len(processed_data) > 0

        except Exception as e:
            self.logger.logger.error(f"Performance test failed: {e}")
            return False

    def _test_error_handling(self) -> bool:
        """تست مدیریت خطا"""
        try:
            # Test with invalid data
            invalid_data = pd.DataFrame({
                'datetime': ['invalid_date'],
                'open': [None],
                'high': [-1],
                'low': [float('inf')],
                'close': ['not_a_number']
            })

            preprocessor = create_preprocessor_for_pearl()
            try:
                result = preprocessor.preprocess(invalid_data, "ERROR_TEST", "M1")
                # Should handle gracefully
                return True
            except Exception:
                # Expected to fail, but should not crash system
                return True

        except Exception:
            return False

    def _test_resource_cleanup(self) -> bool:
        """تست پاکسازی منابع"""
        try:
            initial_memory = psutil.Process().memory_info().rss

            # Create and cleanup multiple sessions
            for i in range(5):
                session_id = prepare_memory_for_training(f"cleanup_test_{i}", 32, 512)
                # Simulate work
                time.sleep(0.1)
                cleanup_after_training(session_id)

            # Force cleanup
            self.memory_manager.force_comprehensive_cleanup()

            final_memory = psutil.Process().memory_info().rss
            memory_increase = (final_memory - initial_memory) / 1024 / 1024  # MB

            # Memory increase should be minimal
            return memory_increase < 20.0

        except Exception:
            return False

    def _test_concurrent_operations(self) -> bool:
        """تست عملیات همزمان"""
        try:
            def worker_task(worker_id):
                # Each worker does preprocessing + training simulation
                test_data = self._generate_test_financial_data(100)
                preprocessor = create_preprocessor_for_pearl()
                processed = preprocessor.preprocess(test_data, f"WORKER_{worker_id}", "M5")

                # Simulate training
                session_id = prepare_memory_for_training(f"worker_{worker_id}", 8, 128)
                time.sleep(0.5)  # Simulate work
                cleanup_after_training(session_id)

                return len(processed) > 0

            # Run concurrent workers
            threads = []
            results = []

            def worker_wrapper(worker_id):
                try:
                    result = worker_task(worker_id)
                    results.append(result)
                except Exception as e:
                    self.logger.logger.error(f"Worker {worker_id} failed: {e}")
                    results.append(False)

            for i in range(3):  # 3 concurrent workers
                thread = threading.Thread(target=worker_wrapper, args=(i,))
                threads.append(thread)
                thread.start()

            for thread in threads:
                thread.join()

            # All workers should succeed
            return all(results) and len(results) == 3

        except Exception as e:
            self.logger.logger.error(f"Concurrent operations test failed: {e}")
            return False

    def run_comprehensive_test(self) -> Dict[str, Any]:
        """اجرای تست جامع کامل"""
        self.logger.logger.info("🚀 Starting Comprehensive Pearl-3x7B System Test")

        # Run all test suites
        test_suites = {
            'memory_management': self.test_memory_management_system,
            'logging_system': self.test_logging_system,
            'data_preprocessing': self.test_data_preprocessing_system,
            'sentiment_training': self.test_sentiment_training_system,
            'system_integration': self.test_system_integration
        }

        overall_results = {}

        for suite_name, test_function in test_suites.items():
            self.logger.logger.info(f"🔄 Running {suite_name} test suite...")

            suite_start = time.time()
            try:
                suite_results = test_function()
                suite_duration = time.time() - suite_start

                suite_results['duration'] = suite_duration
                suite_results['status'] = 'completed'

                # Calculate success rate
                successful_tests = sum(1 for v in suite_results.values()
                                     if isinstance(v, bool) and v)
                total_tests = sum(1 for v in suite_results.values()
                                if isinstance(v, bool))
                suite_results['success_rate'] = successful_tests / total_tests if total_tests > 0 else 0

                overall_results[suite_name] = suite_results

                self.logger.logger.info(
                    f"✅ {suite_name} completed: {successful_tests}/{total_tests} tests passed "
                    f"({suite_results['success_rate']*100:.1f}%) in {suite_duration:.2f}s"
                )

            except Exception as e:
                overall_results[suite_name] = {
                    'status': 'failed',
                    'error': str(e),
                    'duration': time.time() - suite_start
                }
                self.logger.logger.error(f"❌ {suite_name} test suite failed: {e}")

        # Calculate overall statistics
        total_duration = time.time() - self.start_time
        overall_success_rate = self._calculate_overall_success_rate(overall_results)

        # Generate comprehensive report
        report = {
            'test_summary': {
                'total_duration': total_duration,
                'overall_success_rate': overall_success_rate,
                'test_suites_completed': len([r for r in overall_results.values()
                                            if r.get('status') == 'completed']),
                'test_suites_failed': len([r for r in overall_results.values()
                                         if r.get('status') == 'failed']),
                'timestamp': datetime.now().isoformat(),
                'system_info': self._get_system_info()
            },
            'detailed_results': overall_results,
            'recommendations': self._generate_recommendations(overall_results)
        }

        # Save report
        report_file = f"pearl_system_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)

        self.logger.logger.info(f"📊 Comprehensive test completed in {total_duration:.2f}s")
        self.logger.logger.info(f"Overall success rate: {overall_success_rate*100:.1f}%")
        self.logger.logger.info(f"Detailed report saved to: {report_file}")

        return report

    def _calculate_overall_success_rate(self, results: Dict[str, Any]) -> float:
        """محاسبه نرخ موفقیت کلی"""
        total_tests = 0
        successful_tests = 0

        for suite_results in results.values():
            if isinstance(suite_results, dict) and 'success_rate' in suite_results:
                suite_total = sum(1 for v in suite_results.values() if isinstance(v, bool))
                suite_successful = int(suite_results['success_rate'] * suite_total)

                total_tests += suite_total
                successful_tests += suite_successful

        return successful_tests / total_tests if total_tests > 0 else 0

    def _get_system_info(self) -> Dict[str, Any]:
        """اطلاعات سیستم"""
        return {
            'python_version': sys.version,
            'memory_total_gb': psutil.virtual_memory().total / 1024**3,
            'memory_available_gb': psutil.virtual_memory().available / 1024**3,
            'cpu_count': psutil.cpu_count(),
            'platform': sys.platform
        }

    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """تولید توصیه‌ها بر اساس نتایج تست"""
        recommendations = []

        for suite_name, suite_results in results.items():
            if isinstance(suite_results, dict) and suite_results.get('success_rate', 1) < 0.8:
                recommendations.append(
                    f"⚠️ {suite_name} needs attention (success rate: "
                    f"{suite_results.get('success_rate', 0)*100:.1f}%)"
                )

        if not recommendations:
            recommendations.append("✅ All systems performing well!")

        return recommendations

if __name__ == "__main__":
    # Run comprehensive test
    test_runner = ComprehensivePearlSystemTest()
    report = test_runner.run_comprehensive_test()

    # Print summary
    print("\n" + "="*60)
    print("🧪 PEARL-3X7B COMPREHENSIVE SYSTEM TEST RESULTS")
    print("="*60)

    summary = report['test_summary']
    print(f"⏱️  Total Duration: {summary['total_duration']:.2f} seconds")
    print(f"📊 Overall Success Rate: {summary['overall_success_rate']*100:.1f}%")
    print(f"✅ Completed Suites: {summary['test_suites_completed']}")
    print(f"❌ Failed Suites: {summary['test_suites_failed']}")

    print("\n📋 Test Suite Results:")
    for suite_name, results in report['detailed_results'].items():
        if isinstance(results, dict):
            success_rate = results.get('success_rate', 0) * 100
            duration = results.get('duration', 0)
            status = "✅" if results.get('status') == 'completed' else "❌"
            print(f"  {status} {suite_name}: {success_rate:.1f}% ({duration:.2f}s)")

    print("\n💡 Recommendations:")
    for rec in report['recommendations']:
        print(f"  {rec}")

    print(f"\n📄 Detailed report: {[f for f in os.listdir('.') if f.startswith('pearl_system_test_report_')][-1]}")
    print("🎉 Comprehensive test completed!")
