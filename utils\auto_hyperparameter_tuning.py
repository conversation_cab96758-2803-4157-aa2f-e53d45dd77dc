"""
Auto-Hyperparameter Tuning System
سیستم بهینه‌سازی خودکار پارامترها

این سیستم از Bayesian Optimization استفاده می‌کند تا بهترین پارامترها را 
برای مدل‌های مختلف به طور خودکار پیدا کند.
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, asdict
import pickle
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern
from sklearn.preprocessing import StandardScaler
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class HyperparameterSpace:
    """فضای جستجوی پارامترها"""
    name: str
    param_type: str  # 'continuous', 'discrete', 'categorical'
    bounds: Tuple[float, float]  # برای continuous
    choices: List[Any]  # برای discrete/categorical
    
@dataclass
class OptimizationResult:
    """نتیجه بهینه‌سازی"""
    best_params: Dict
    best_score: float
    optimization_history: List[Dict]
    total_evaluations: int
    convergence_info: Dict

class BayesianOptimizer:
    """بهینه‌ساز بیزی برای پارامترها"""
    
    def __init__(self, objective_function: Callable, hyperparameter_spaces: List[HyperparameterSpace]):
        self.objective_function = objective_function
        self.hyperparameter_spaces = hyperparameter_spaces
        self.param_names = [space.name for space in hyperparameter_spaces]
        
        # Gaussian Process for surrogate model
        kernel = Matern(length_scale=1.0, nu=1.5)
        self.gp = GaussianProcessRegressor(
            kernel=kernel,
            alpha=1e-6,
            normalize_y=True,
            n_restarts_optimizer=5,
            random_state=42
        )
        
        # Optimization history
        self.X_evaluated = []
        self.y_evaluated = []
        self.optimization_history = []
        
        # Scaler for normalization
        self.scaler = StandardScaler()
        
        logger.info(f"Bayesian Optimizer initialized with {len(hyperparameter_spaces)} parameters")
    
    def _encode_parameters(self, params: Dict) -> np.ndarray:
        """تبدیل پارامترها به بردار عددی"""
        encoded = []
        
        for space in self.hyperparameter_spaces:
            value = params[space.name]
            
            if space.param_type == 'continuous':
                # Normalize to [0, 1]
                normalized = (value - space.bounds[0]) / (space.bounds[1] - space.bounds[0])
                encoded.append(normalized)
                
            elif space.param_type == 'discrete':
                # Convert to index
                if value in space.choices:
                    index = space.choices.index(value)
                    normalized = index / (len(space.choices) - 1)
                    encoded.append(normalized)
                else:
                    encoded.append(0.0)
                    
            elif space.param_type == 'categorical':
                # One-hot encoding
                for choice in space.choices:
                    encoded.append(1.0 if value == choice else 0.0)
        
        return np.array(encoded)
    
    def _decode_parameters(self, encoded: np.ndarray) -> Dict:
        """تبدیل بردار عددی به پارامترها"""
        params = {}
        idx = 0
        
        for space in self.hyperparameter_spaces:
            if space.param_type == 'continuous':
                normalized = encoded[idx]
                value = space.bounds[0] + normalized * (space.bounds[1] - space.bounds[0])
                params[space.name] = value
                idx += 1
                
            elif space.param_type == 'discrete':
                normalized = encoded[idx]
                choice_idx = int(normalized * (len(space.choices) - 1))
                choice_idx = max(0, min(choice_idx, len(space.choices) - 1))
                params[space.name] = space.choices[choice_idx]
                idx += 1
                
            elif space.param_type == 'categorical':
                # Find the category with highest value
                category_values = encoded[idx:idx+len(space.choices)]
                best_idx = np.argmax(category_values)
                params[space.name] = space.choices[best_idx]
                idx += len(space.choices)
        
        return params
    
    def _acquisition_function(self, X: np.ndarray, xi: float = 0.01) -> np.ndarray:
        """تابع اکتساب (Expected Improvement)"""
        if len(self.X_evaluated) == 0:
            return np.zeros(X.shape[0])
        
        # Predict mean and std
        mu, sigma = self.gp.predict(X, return_std=True)
        
        # Current best
        f_best = np.max(self.y_evaluated)
        
        # Expected Improvement
        with np.errstate(divide='warn'):
            imp = mu - f_best - xi
            Z = imp / sigma
            ei = imp * self._norm_cdf(Z) + sigma * self._norm_pdf(Z)
            ei[sigma == 0.0] = 0.0
        
        return ei
    
    def _norm_cdf(self, x):
        """Standard normal CDF"""
        return 0.5 * (1 + np.sign(x) * np.sqrt(1 - np.exp(-2 * x**2 / np.pi)))
    
    def _norm_pdf(self, x):
        """Standard normal PDF"""
        return np.exp(-0.5 * x**2) / np.sqrt(2 * np.pi)
    
    def _suggest_next_point(self) -> Dict:
        """پیشنهاد نقطه بعدی برای ارزیابی"""
        if len(self.X_evaluated) == 0:
            # Random initial point
            return self._generate_random_params()
        
        # Optimize acquisition function
        def neg_acquisition(x):
            return -self._acquisition_function(x.reshape(1, -1))[0]
        
        # Multiple random starts
        best_x = None
        best_acq = -np.inf
        
        for _ in range(10):
            x0 = np.random.random(len(self.X_evaluated[0]))
            
            try:
                result = minimize(
                    neg_acquisition,
                    x0,
                    bounds=[(0, 1)] * len(x0),
                    method='L-BFGS-B'
                )
                
                if result.success and -result.fun > best_acq:
                    best_acq = -result.fun
                    best_x = result.x
            except:
                continue
        
        if best_x is None:
            return self._generate_random_params()
        
        return self._decode_parameters(best_x)
    
    def _generate_random_params(self) -> Dict:
        """تولید پارامترهای تصادفی"""
        params = {}
        
        for space in self.hyperparameter_spaces:
            if space.param_type == 'continuous':
                value = np.random.uniform(space.bounds[0], space.bounds[1])
                params[space.name] = value
                
            elif space.param_type == 'discrete':
                value = np.random.choice(space.choices)
                params[space.name] = value
                
            elif space.param_type == 'categorical':
                value = np.random.choice(space.choices)
                params[space.name] = value
        
        return params
    
    def optimize(self, n_calls: int = 50, n_random_starts: int = 10) -> OptimizationResult:
        """بهینه‌سازی پارامترها"""
        logger.info(f"Starting Bayesian optimization with {n_calls} evaluations")
        
        # Random initialization
        for i in range(n_random_starts):
            params = self._generate_random_params()
            score = self.objective_function(params)
            
            encoded_params = self._encode_parameters(params)
            self.X_evaluated.append(encoded_params)
            self.y_evaluated.append(score)
            
            self.optimization_history.append({
                'iteration': i,
                'params': params,
                'score': score,
                'type': 'random'
            })
            
            logger.info(f"Random init {i+1}/{n_random_starts}: score={score:.6f}")
        
        # Bayesian optimization
        for i in range(n_random_starts, n_calls):
            # Fit GP
            X_array = np.array(self.X_evaluated)
            y_array = np.array(self.y_evaluated)
            
            if len(X_array) > 0:
                self.gp.fit(X_array, y_array)
            
            # Suggest next point
            params = self._suggest_next_point()
            score = self.objective_function(params)
            
            encoded_params = self._encode_parameters(params)
            self.X_evaluated.append(encoded_params)
            self.y_evaluated.append(score)
            
            self.optimization_history.append({
                'iteration': i,
                'params': params,
                'score': score,
                'type': 'bayesian'
            })
            
            logger.info(f"Bayesian iter {i+1}/{n_calls}: score={score:.6f}")
        
        # Find best result
        best_idx = np.argmax(self.y_evaluated)
        best_params = self.optimization_history[best_idx]['params']
        best_score = self.y_evaluated[best_idx]
        
        # Convergence info
        convergence_info = {
            'converged': self._check_convergence(),
            'improvement_rate': self._calculate_improvement_rate(),
            'final_gp_score': self.gp.score(X_array, y_array) if len(X_array) > 0 else 0
        }
        
        result = OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_history=self.optimization_history,
            total_evaluations=len(self.y_evaluated),
            convergence_info=convergence_info
        )
        
        logger.info(f"Optimization completed. Best score: {best_score:.6f}")
        return result
    
    def _check_convergence(self, window_size: int = 10) -> bool:
        """بررسی همگرایی"""
        if len(self.y_evaluated) < window_size * 2:
            return False
        
        recent_scores = self.y_evaluated[-window_size:]
        previous_scores = self.y_evaluated[-window_size*2:-window_size]
        
        recent_mean = np.mean(recent_scores)
        previous_mean = np.mean(previous_scores)
        
        improvement = (recent_mean - previous_mean) / abs(previous_mean)
        return improvement < 0.01  # Less than 1% improvement
    
    def _calculate_improvement_rate(self) -> float:
        """محاسبه نرخ بهبود"""
        if len(self.y_evaluated) < 2:
            return 0.0
        
        initial_best = max(self.y_evaluated[:5]) if len(self.y_evaluated) >= 5 else self.y_evaluated[0]
        final_best = max(self.y_evaluated)
        
        return (final_best - initial_best) / abs(initial_best)

class AutoHyperparameterTuner:
    """سیستم تنظیم خودکار پارامترها"""
    
    def __init__(self, db_path: str = "hyperparameter_tuning.db"):
        self.db_path = db_path
        self.optimization_results = {}
        self.model_configs = {}
        
        self._init_database()
        logger.info("Auto-Hyperparameter Tuner initialized")
    
    def _init_database(self):
        """راه‌اندازی پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول نتایج بهینه‌سازی
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS optimization_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                model_name TEXT,
                symbol TEXT,
                timeframe TEXT,
                best_params TEXT,
                best_score REAL,
                total_evaluations INTEGER,
                convergence_info TEXT
            )
        ''')
        
        # جدول تاریخچه بهینه‌سازی
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS optimization_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                optimization_id INTEGER,
                iteration INTEGER,
                params TEXT,
                score REAL,
                optimization_type TEXT,
                FOREIGN KEY (optimization_id) REFERENCES optimization_results (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def define_rl_hyperparameters(self) -> List[HyperparameterSpace]:
        """تعریف فضای جستجوی پارامترهای RL"""
        return [
            HyperparameterSpace("learning_rate", "continuous", (0.001, 0.1), []),
            HyperparameterSpace("discount_factor", "continuous", (0.9, 0.99), []),
            HyperparameterSpace("epsilon_decay", "continuous", (0.99, 0.999), []),
            HyperparameterSpace("epsilon_min", "continuous", (0.01, 0.1), []),
            HyperparameterSpace("batch_size", "discrete", (0, 0), [16, 32, 64, 128]),
        ]
    
    def define_prediction_hyperparameters(self) -> List[HyperparameterSpace]:
        """تعریف فضای جستجوی پارامترهای پیش‌بینی"""
        return [
            HyperparameterSpace("n_estimators", "discrete", (0, 0), [50, 100, 200, 300]),
            HyperparameterSpace("max_depth", "discrete", (0, 0), [5, 8, 10, 15, 20]),
            HyperparameterSpace("min_samples_split", "discrete", (0, 0), [2, 5, 10]),
            HyperparameterSpace("min_samples_leaf", "discrete", (0, 0), [1, 2, 4]),
            HyperparameterSpace("max_features", "categorical", (0, 0), ["sqrt", "log2", "auto"]),
        ]
    
    def optimize_rl_parameters(self, symbol: str, timeframe: str, 
                             evaluation_function: Callable) -> OptimizationResult:
        """بهینه‌سازی پارامترهای RL"""
        logger.info(f"Optimizing RL parameters for {symbol} {timeframe}")
        
        hyperparameter_spaces = self.define_rl_hyperparameters()
        optimizer = BayesianOptimizer(evaluation_function, hyperparameter_spaces)
        
        result = optimizer.optimize(n_calls=30, n_random_starts=5)
        
        # ذخیره نتایج
        self._save_optimization_result("RL_Agent", symbol, timeframe, result)
        
        return result
    
    def optimize_prediction_parameters(self, symbol: str, timeframe: str,
                                     evaluation_function: Callable) -> OptimizationResult:
        """بهینه‌سازی پارامترهای پیش‌بینی"""
        logger.info(f"Optimizing prediction parameters for {symbol} {timeframe}")
        
        hyperparameter_spaces = self.define_prediction_hyperparameters()
        optimizer = BayesianOptimizer(evaluation_function, hyperparameter_spaces)
        
        result = optimizer.optimize(n_calls=25, n_random_starts=5)
        
        # ذخیره نتایج
        self._save_optimization_result("Prediction_Model", symbol, timeframe, result)
        
        return result
    
    def _save_optimization_result(self, model_name: str, symbol: str, timeframe: str, 
                                result: OptimizationResult):
        """ذخیره نتایج بهینه‌سازی"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # ذخیره نتیجه اصلی
        cursor.execute('''
            INSERT INTO optimization_results 
            (timestamp, model_name, symbol, timeframe, best_params, best_score, 
             total_evaluations, convergence_info)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            datetime.now().isoformat(),
            model_name,
            symbol,
            timeframe,
            json.dumps(result.best_params),
            result.best_score,
            result.total_evaluations,
            json.dumps(result.convergence_info)
        ))
        
        optimization_id = cursor.lastrowid
        
        # ذخیره تاریخچه
        for entry in result.optimization_history:
            # Convert numpy types to Python types
            params = entry['params'].copy()
            for key, value in params.items():
                if hasattr(value, 'item'):  # numpy scalar
                    params[key] = value.item()
            
            cursor.execute('''
                INSERT INTO optimization_history 
                (optimization_id, iteration, params, score, optimization_type)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                optimization_id,
                entry['iteration'],
                json.dumps(params),
                float(entry['score']),
                entry['type']
            ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Optimization results saved for {model_name} {symbol} {timeframe}")
    
    def get_best_parameters(self, model_name: str, symbol: str, timeframe: str) -> Optional[Dict]:
        """دریافت بهترین پارامترها"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT best_params, best_score FROM optimization_results 
            WHERE model_name = ? AND symbol = ? AND timeframe = ?
            ORDER BY best_score DESC LIMIT 1
        ''', (model_name, symbol, timeframe))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return json.loads(result[0])
        return None
    
    def get_optimization_summary(self) -> Dict:
        """خلاصه بهینه‌سازی‌ها"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # تعداد کل بهینه‌سازی‌ها
        cursor.execute('SELECT COUNT(*) FROM optimization_results')
        total_optimizations = cursor.fetchone()[0]
        
        # بهترین نتایج
        cursor.execute('''
            SELECT model_name, symbol, timeframe, best_score 
            FROM optimization_results 
            ORDER BY best_score DESC LIMIT 10
        ''')
        best_results = cursor.fetchall()
        
        # آمار مدل‌ها
        cursor.execute('''
            SELECT model_name, COUNT(*), AVG(best_score) 
            FROM optimization_results 
            GROUP BY model_name
        ''')
        model_stats = cursor.fetchall()
        
        conn.close()
        
        return {
            'total_optimizations': total_optimizations,
            'best_results': [
                {
                    'model': row[0],
                    'symbol': row[1],
                    'timeframe': row[2],
                    'score': row[3]
                }
                for row in best_results
            ],
            'model_statistics': [
                {
                    'model': row[0],
                    'optimizations': row[1],
                    'avg_score': row[2]
                }
                for row in model_stats
            ]
        }

def main():
    """تست سیستم تنظیم خودکار پارامترها"""
    print("Auto-Hyperparameter Tuning System Test")
    print("=" * 45)
    
    # ایجاد سیستم
    tuner = AutoHyperparameterTuner("test_hyperparameter_tuning.db")
    
    # تابع ارزیابی نمونه برای RL
    def evaluate_rl_params(params: Dict) -> float:
        """تابع ارزیابی نمونه برای پارامترهای RL"""
        # شبیه‌سازی عملکرد RL با پارامترهای مختلف
        learning_rate = params['learning_rate']
        discount_factor = params['discount_factor']
        epsilon_decay = params['epsilon_decay']
        epsilon_min = params['epsilon_min']
        batch_size = params['batch_size']
        
        # فرمول ساده برای شبیه‌سازی عملکرد
        score = (
            (1 - learning_rate) * 0.3 +  # کمتر بهتر
            discount_factor * 0.3 +       # بیشتر بهتر
            epsilon_decay * 0.2 +          # بیشتر بهتر
            (1 - epsilon_min) * 0.1 +     # کمتر بهتر
            (batch_size / 128) * 0.1       # متوسط بهتر
        )
        
        # اضافه کردن نویز
        score += np.random.normal(0, 0.05)
        
        return score
    
    # تابع ارزیابی نمونه برای پیش‌بینی
    def evaluate_prediction_params(params: Dict) -> float:
        """تابع ارزیابی نمونه برای پارامترهای پیش‌بینی"""
        n_estimators = params['n_estimators']
        max_depth = params['max_depth']
        min_samples_split = params['min_samples_split']
        min_samples_leaf = params['min_samples_leaf']
        max_features = params['max_features']
        
        # شبیه‌سازی دقت مدل
        base_score = 0.7
        
        # تأثیر پارامترها
        base_score += (n_estimators / 300) * 0.15
        base_score += (max_depth / 20) * 0.1
        base_score -= (min_samples_split / 10) * 0.05
        base_score -= (min_samples_leaf / 4) * 0.03
        
        if max_features == "sqrt":
            base_score += 0.02
        elif max_features == "log2":
            base_score += 0.01
        
        # اضافه کردن نویز
        base_score += np.random.normal(0, 0.03)
        
        return base_score
    
    print("Testing RL parameter optimization...")
    rl_result = tuner.optimize_rl_parameters("EURUSD", "H1", evaluate_rl_params)
    
    print(f"RL Optimization Results:")
    print(f"  Best score: {rl_result.best_score:.6f}")
    print(f"  Best parameters:")
    for param, value in rl_result.best_params.items():
        print(f"    {param}: {value}")
    print(f"  Total evaluations: {rl_result.total_evaluations}")
    print(f"  Converged: {rl_result.convergence_info['converged']}")
    
    print(f"\nTesting Prediction parameter optimization...")
    pred_result = tuner.optimize_prediction_parameters("EURUSD", "H1", evaluate_prediction_params)
    
    print(f"Prediction Optimization Results:")
    print(f"  Best score: {pred_result.best_score:.6f}")
    print(f"  Best parameters:")
    for param, value in pred_result.best_params.items():
        print(f"    {param}: {value}")
    print(f"  Total evaluations: {pred_result.total_evaluations}")
    print(f"  Converged: {pred_result.convergence_info['converged']}")
    
    # خلاصه کلی
    print(f"\nOptimization Summary:")
    summary = tuner.get_optimization_summary()
    print(f"  Total optimizations: {summary['total_optimizations']}")
    print(f"  Best results:")
    for result in summary['best_results'][:3]:
        print(f"    {result['model']} {result['symbol']}: {result['score']:.6f}")
    
    print(f"\n✅ Auto-Hyperparameter Tuning System test completed!")

if __name__ == "__main__":
    main() 