
import pandas as pd

# --- Microstructure Noise Cleaning ---
def microstructure_noise_cleaning(df: pd.DataFrame, price_col: str = 'close', volume_col: str = 'volume', window: int = 5) -> pd.DataFrame:
    """
    حذف نویز microstructure با حذف داده‌هایی که تغییرات قیمتی بسیار کوچک و حجم پایین دارند (نویز بازار).
    اگر ستون‌های قیمت و حجم وجود داشته باشد، داده‌هایی که تغییر قیمت آنها در بازه کوتاه کمتر از آستانه و حجم پایین دارند حذف می‌شوند.
    """
    if price_col not in df.columns or volume_col not in df.columns:
        return df
    price_diff = df[price_col].diff().abs().fillna(0)
    vol = df[volume_col].fillna(0)
    price_thresh = price_diff.rolling(window).mean().quantile(0.10)
    vol_thresh = vol.rolling(window).mean().quantile(0.10)
    mask = ~((price_diff < price_thresh) & (vol < vol_thresh))
    return df.loc[mask].reset_index(drop=True)

import pandas as pd
from typing import List, Optional, Dict
from sklearn.ensemble import IsolationForest

def drop_missing(df: pd.DataFrame, columns: Optional[List[str]] = None) -> pd.DataFrame:
    """حذف ردیف‌هایی که مقادیر گمشده دارند."""
    if columns:
        return df.dropna(subset=columns)
    return df.dropna()

def fill_missing(df: pd.DataFrame, value: float = 0, columns: Optional[List[str]] = None) -> pd.DataFrame:
    """جایگزینی مقادیر گمشده با مقدار مشخص (پیش‌فرض ۰)."""
    if columns:
        df[columns] = df[columns].fillna(value)
    else:
        df = df.fillna(value)
    return df

def remove_outliers(df: pd.DataFrame, columns: Optional[List[str]] = None, z_thresh: float = 3.0) -> pd.DataFrame:
    """حذف داده‌های پرت بر اساس Z-Score."""
    import numpy as np
    cols = columns or df.select_dtypes(include='number').columns.tolist()
    for col in cols:
        col_zscore = (df[col] - df[col].mean()) / df[col].std(ddof=0)
        df = df[col_zscore.abs() <= z_thresh]
    return df

def filter_invalid(
    df: pd.DataFrame,
    columns: Optional[List[str]] = None,
    min_val: Optional[float] = None,
    max_val: Optional[float] = None
) -> pd.DataFrame:
    """فیلتر داده‌های غیرمنطقی بر اساس بازه مجاز."""
    cols = columns or df.select_dtypes(include='number').columns.tolist()
    for col in cols:
        if min_val is not None:
            df = df[df[col] >= min_val]
        if max_val is not None:
            df = df[df[col] <= max_val]
    return df

# مثال استفاده:
# pipeline.add_step(lambda df: drop_missing(df, columns=['close']))
# pipeline.add_step(lambda df: remove_outliers(df, columns=['close'], z_thresh=3))

# --- ایده‌های پیشرفته پاک‌سازی داده ---
def drop_duplicates(df: pd.DataFrame, subset: Optional[List[str]] = None) -> pd.DataFrame:
    """حذف ردیف‌های تکراری بر اساس ستون‌های مشخص یا کل داده."""
    return df.drop_duplicates(subset=subset)

def fix_inconsistent_data(df: pd.DataFrame, column: str, mapping: Dict[str, str]) -> pd.DataFrame:
    """اصلاح داده‌های ناسازگار (مثلاً یکسان‌سازی نام‌ها یا مقادیر متنی)."""
    df[column] = df[column].replace(mapping)
    return df

def remove_time_anomalies(df: pd.DataFrame, time_col: str) -> pd.DataFrame:
    """حذف داده‌هایی که ترتیب زمانی ندارند یا timestamp غیرمنطقی دارند."""
    df = df.sort_values(time_col)
    # حذف ردیف‌هایی که ترتیب زمانی ندارند (یعنی اختلاف منفی یا صفر دارند)
    diffs = df[time_col].diff()
    # اختلاف باید مثبت باشد (یعنی زمان فعلی > قبلی)
    mask = (diffs > pd.Timedelta(0)) | diffs.isna()
    df = df[mask]
    return df

def remove_sudden_spikes(df: pd.DataFrame, column: str, window: int = 5, threshold: float = 5.0) -> pd.DataFrame:
    """حذف اسپایک‌های ناگهانی بر اساس انحراف شدید از میانگین متحرک."""
    # حذف اسپایک با استفاده از اختلاف نسبت به همسایه‌ها (روش robust)
    s = df[column]
    # اختلاف با همسایه قبلی و بعدی
    prev_diff = (s - s.shift(1)).abs()
    next_diff = (s - s.shift(-1)).abs()
    # میانه اختلاف‌ها (robust به outlier)
    med_prev = prev_diff.median()
    med_next = next_diff.median()
    # اسپایک اگر اختلاف با هر همسایه خیلی بیشتر از میانه باشد
    mask = (prev_diff < threshold * med_prev) & (next_diff < threshold * med_next)
    # اولین و آخرین مقدار را همیشه نگه می‌داریم
    mask.iloc[0] = True
    mask.iloc[-1] = True
    return df[mask]

def impute_missing_statistical(df: pd.DataFrame, column: str, method: str = 'mean') -> pd.DataFrame:
    """ایمپیوته مقادیر گمشده با میانگین/میانه/مد."""
    if method == 'mean':
        value = df[column].mean()
    elif method == 'median':
        value = df[column].median()
    elif method == 'mode':
        value = df[column].mode()[0]
    else:
        raise ValueError('method must be mean, median, or mode')
    df[column] = df[column].fillna(value)
    return df

def remove_zero_abnormal_volume(df: pd.DataFrame, volume_col: str, min_volume: float = 1.0) -> pd.DataFrame:
    """حذف ردیف‌هایی با حجم معاملات صفر یا غیرعادی."""
    return df[df[volume_col] >= min_volume]

def normalize_categorical(df: pd.DataFrame, column: str, mapping: Dict[str, int]) -> pd.DataFrame:
    """استانداردسازی مقادیر متنی (مثلاً باینری‌سازی یا نگاشت به اعداد)."""
    df[column] = df[column].map(mapping)
    return df

def filter_market_hours(df: pd.DataFrame, time_col: str, market_open: str = '09:00', market_close: str = '17:00') -> pd.DataFrame:
    """حذف داده‌های خارج از ساعات بازار (بر اساس ساعت)."""
    times = pd.to_datetime(df[time_col]).dt.time
    from datetime import time
    open_t = time.fromisoformat(market_open)
    close_t = time.fromisoformat(market_close)
    mask = times >= open_t
    mask &= times <= close_t
    return df[mask]

def filter_unrealistic_financial_ratios(df: pd.DataFrame, num_col: str, denom_col: str, min_ratio: float = 0.01, max_ratio: float = 100.0) -> pd.DataFrame:
    """حذف داده‌هایی با نسبت مالی غیرمنطقی (مثلاً price/volume)."""
    ratio = df[num_col] / df[denom_col]
    return df[(ratio >= min_ratio) & (ratio <= max_ratio)]

def remove_unrealistic_price_change(df: pd.DataFrame, price_col: str, max_pct_change: float = 0.2) -> pd.DataFrame:
    """حذف داده‌هایی با تغییرات قیمتی غیرواقعی (مثلاً بیش از ۲۰٪ در یک گام)."""
    pct_change = df[price_col].pct_change().abs()
    # ردیف‌هایی که تغییر قیمت بیش از حد دارند حذف می‌شوند (به جز اولین مقدار که NaN است)
    mask = (pct_change < max_pct_change) | (pct_change.isna())
    return df[mask]

# مثال استفاده پیشرفته:
# pipeline.add_step(lambda df: drop_duplicates(df))
# pipeline.add_step(lambda df: fix_inconsistent_data(df, 'symbol', {'eurusd':'EURUSD', 'EURusd':'EURUSD'}))
# pipeline.add_step(lambda df: remove_time_anomalies(df, 'datetime'))
# pipeline.add_step(lambda df: remove_sudden_spikes(df, 'close', window=5, threshold=5))
# pipeline.add_step(lambda df: impute_missing_statistical(df, 'close', method='median'))
# pipeline.add_step(lambda df: remove_zero_abnormal_volume(df, 'volume', min_volume=1))
# pipeline.add_step(lambda df: normalize_categorical(df, 'side', {'buy':1, 'sell':0}))
# pipeline.add_step(lambda df: filter_market_hours(df, 'datetime', '09:00', '17:00'))
# pipeline.add_step(lambda df: filter_unrealistic_financial_ratios(df, 'close', 'volume', 0.01, 100))
# pipeline.add_step(lambda df: remove_unrealistic_price_change(df, 'close', max_pct_change=0.2))

# --- Advanced Financial Cleaning Functions ---
import numpy as np

def spike_correction(df: pd.DataFrame, columns: Optional[List[str]] = None, threshold: float = 5.0) -> pd.DataFrame:
    """
    اصلاح اسپایک‌های قیمتی با جایگزینی مقادیر پرت ناگهانی با مقدار میانگین همسایه‌ها.
    """
    cols = columns or df.select_dtypes(include=[np.number]).columns.tolist()
    for col in cols:
        series = df[col]
        diff = series.diff().abs()
        spikes = diff > (threshold * diff.rolling(window=5, min_periods=1).median())
        df.loc[spikes, col] = np.nan
    return df

def contextual_outlier_cleaning(df: pd.DataFrame, columns: Optional[List[str]] = None, window: int = 20, z_thresh: float = 3.5) -> pd.DataFrame:
    """
    حذف/اصلاح outlierهای کانتکست‌محور با توجه به رفتار محلی داده.
    """
    cols = columns or df.select_dtypes(include=[np.number]).columns.tolist()
    for col in cols:
        rolling_mean = df[col].rolling(window=window, min_periods=1).mean()
        rolling_std = df[col].rolling(window=window, min_periods=1).std()
        z = (df[col] - rolling_mean) / (rolling_std + 1e-8)
        df.loc[z.abs() > z_thresh, col] = np.nan
    return df

def anomaly_detection_cleaning(df: pd.DataFrame, columns: Optional[List[str]] = None, method: str = 'isolation_forest', contamination: float = 0.01) -> pd.DataFrame:
    """
    شناسایی و حذف داده‌های غیرعادی با مدل IsolationForest.
    """
    cols = columns or df.select_dtypes(include=[np.number]).columns.tolist()
    X = df[cols].values
    if method == 'isolation_forest':
        clf = IsolationForest(contamination=contamination, random_state=42)
        preds = clf.fit_predict(X)
        mask = preds == 1
        return df.loc[mask].reset_index(drop=True)
    else:
        raise NotImplementedError('Only isolation_forest is implemented')

def adaptive_outlier_cleaning(df: pd.DataFrame, columns: Optional[List[str]] = None, z_thresh: float = 3.0, adapt_factor: float = 0.5) -> pd.DataFrame:
    """
    حذف/اصلاح outlierهای تطبیقی با توجه به نوسانات بازار.
    """
    cols = columns or df.select_dtypes(include=[np.number]).columns.tolist()
    for col in cols:
        rolling_std = df[col].rolling(window=20, min_periods=1).std()
        adaptive_z = z_thresh + adapt_factor * (rolling_std / (rolling_std.median() + 1e-8))
        z = (df[col] - df[col].mean()) / (df[col].std() + 1e-8)
        df.loc[z.abs() > adaptive_z, col] = np.nan
    return df

def remove_holiday_illiquid(df: pd.DataFrame, datetime_col: str = 'datetime', holiday_list=None, min_volume: float = 1.0) -> pd.DataFrame:
    """
    حذف داده‌های مربوط به تعطیلات رسمی یا زمان‌های illiquid (حجم پایین).
    """
    if holiday_list is not None:
        df = df[~df[datetime_col].isin(holiday_list)]
    if 'volume' in df.columns:
        df = df[df['volume'] >= min_volume]
    return df


def corporate_action_cleaning(df: pd.DataFrame, adjustment_dict: dict = None, price_col: str = 'close') -> pd.DataFrame:
    """
    تعدیل قیمت‌ها بر اساس corporate actions (مثلاً تقسیم سود یا افزایش سرمایه).
    adjustment_dict: دیکشنری {datetime: adjustment_factor}
    """
    if adjustment_dict is not None and price_col in df.columns and 'datetime' in df.columns:
        df = df.copy()
        df['adj_factor'] = df['datetime'].map(adjustment_dict).fillna(1.0)
        df[price_col] = df[price_col] * df['adj_factor']
        df = df.drop(columns=['adj_factor'])
    return df


def ml_based_cleaning(df: pd.DataFrame, columns: list = None, model=None) -> pd.DataFrame:
    """
    حذف داده‌های outlier با مدل ML (IsolationForest) روی ستون‌های عددی یا انتخابی.
    """
    cols = columns or df.select_dtypes(include='number').columns.tolist()
    if len(cols) == 0:
        return df
    X = df[cols].values
    if model is None:
        model = IsolationForest(contamination=0.01, random_state=42)
    preds = model.fit_predict(X)
    mask = preds == 1
    return df.loc[mask].reset_index(drop=True)


def adaptive_cleaning_by_reward(df: pd.DataFrame, reward_col: str = None) -> pd.DataFrame:
    """
    پاک‌سازی تطبیقی بر اساس رفتار ربات یا reward (عملیاتی).
    اگر reward_col داده شود، نمونه‌هایی که reward آنها در صدک پایین (مثلاً 5%) است حذف می‌شوند.
    """
    if reward_col is None or reward_col not in df.columns:
        return df
    # حذف داده‌هایی که reward بسیار پایین دارند (مثلاً پایین‌ترین 5%)
    threshold = df[reward_col].quantile(0.05)
    mask = df[reward_col] > threshold
    return df.loc[mask].reset_index(drop=True)


def explainable_cleaning(df: pd.DataFrame, log: list = None) -> pd.DataFrame:
    """
    cleaning با قابلیت توضیح‌پذیری و ثبت لاگ تغییرات (عملیاتی با SHAP).
    اگر ستون هدف (مثلاً 'close') وجود داشته باشد، تاثیر ویژگی‌ها با SHAP محاسبه و نمونه‌های غیرعادی حذف می‌شوند.
    """
    try:
        import shap
        from sklearn.ensemble import RandomForestRegressor
        # فقط اگر ستون 'close' و حداقل 3 ویژگی عددی وجود دارد
        if 'close' not in df.columns:
            if log is not None:
                log.append('explainable_cleaning: no close column')
            return df
        feature_cols = [c for c in df.select_dtypes(include='number').columns if c != 'close']
        if len(feature_cols) < 2:
            if log is not None:
                log.append('explainable_cleaning: not enough features')
            return df
        # مدل رگرسیون برای تخمین close
        X = df[feature_cols].fillna(0)
        y = df['close'].fillna(0)
        model = RandomForestRegressor(n_estimators=30, random_state=42)
        model.fit(X, y)
        explainer = shap.Explainer(model, X)
        shap_values = explainer(X)
        # محاسبه تاثیر کل هر نمونه (مجموع قدر مطلق)
        sample_importance = shap_values.abs.sum(axis=1).values
        # آستانه: صدک 99 تاثیر (نمونه‌های بسیار غیرعادی حذف شوند)
        import numpy as np
        threshold = np.quantile(sample_importance, 0.99)
        mask = sample_importance < threshold
        removed = (~mask).sum()
        df_clean = df.loc[mask].reset_index(drop=True)
        if log is not None:
            log.append(f'explainable_cleaning: removed {removed} outlier samples by SHAP')
        return df_clean
    except Exception as e:
        if log is not None:
            log.append(f'explainable_cleaning: error {str(e)}')
        return df


def testable_cleaning(df: pd.DataFrame, test_func=None) -> pd.DataFrame:
    """
    cleaning با تست و اعتبارسنجی خودکار.
    """
    if test_func is not None:
        assert test_func(df)
    return df


def scenario_based_cleaning(df: pd.DataFrame, scenario_func=None) -> pd.DataFrame:
    """
    پاک‌سازی مبتنی بر سناریوهای بازار (مثلاً حذف داده‌های شوک خبری).
    اگر scenario_func داده شود، داده‌های سناریومحور حذف یا علامت‌گذاری می‌شوند.
    """
    if scenario_func is not None:
        return scenario_func(df)
    return df


def feature_driven_cleaning(df: pd.DataFrame, feature_func=None) -> pd.DataFrame:
    """
    پاک‌سازی بر اساس ویژگی‌های مشتق‌شده (feature engineering).
    اگر feature_func داده شود، ویژگی‌های جدید ساخته و داده‌های غیرمفید حذف می‌شوند.
    """
    if feature_func is not None:
        df = feature_func(df)
        # حذف ستون‌هایی که واریانس بسیار کم دارند (feature selection ساده)
        numeric_cols = df.select_dtypes(include='number').columns
        low_var_cols = [c for c in numeric_cols if df[c].std() < 1e-6]
        if low_var_cols:
            df = df.drop(columns=low_var_cols)
    return df


def imitation_cleaning(df: pd.DataFrame, imitation_func=None) -> pd.DataFrame:
    """
    پاک‌سازی بر اساس رفتار الگوریتم imitation learning (عملیاتی).
    اگر imitation_func داده شود، داده‌های مشابه رفتار expert انتخاب می‌شوند.
    """
    if imitation_func is not None:
        mask = imitation_func(df)
        if hasattr(mask, 'shape') and mask.shape[0] == len(df):
            return df.loc[mask].reset_index(drop=True)
    return df


def cross_validation_cleaning(df: pd.DataFrame, cv_func=None) -> pd.DataFrame:
    """
    پاک‌سازی بر اساس نتایج cross-validation و افت عملکرد مدل (عملیاتی).
    اگر cv_func داده شود، داده‌هایی که باعث افت شدید metric می‌شوند حذف می‌شوند.
    """
    if cv_func is not None:
        mask = cv_func(df)
        if hasattr(mask, 'shape') and mask.shape[0] == len(df):
            return df.loc[mask].reset_index(drop=True)
    return df
