"""
🧪 AI Models Tests
تست‌های مدل‌های AI

این فایل شامل تست‌های unit برای مدل‌های AI است
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Import modules under test
try:
    from ai_models import (
        ModelManager, ModelRegistry, initialize_models,
        get_model, get_available_models, model_registry
    )
    from ai_models.model_manager import ModelCache, ModelMetrics
    from ai_models.huggingface_models import HuggingFaceModel
    from ai_models.sentiment_models import (
        FinBERTModel, CryptoBERTModel, FinancialSentimentModel
    )
    from core.base import ModelPrediction
    from core.exceptions import ModelLoadError
    
    AI_MODELS_AVAILABLE = True
    
except ImportError as e:
    print(f"⚠️ AI models not available: {e}")
    AI_MODELS_AVAILABLE = False

pytestmark = pytest.mark.skipif(not AI_MODELS_AVAILABLE, reason="AI models not available")

@pytest.mark.ai
class TestModelRegistry:
    """تست رجیستری مدل"""
    
    def test_model_registry_creation(self):
        """تست ایجاد رجیستری مدل"""
        registry = ModelRegistry()
        assert registry is not None
        assert hasattr(registry, 'models')
        assert hasattr(registry, 'model_configs')
    
    def test_register_model(self):
        """تست ثبت مدل"""
        registry = ModelRegistry()
        
        # Create mock model
        mock_model = Mock()
        mock_model.name = "test_model"
        mock_model.model_type = "test"
        
        # Register model
        result = registry.register_model("test_model", mock_model)
        assert result is True
        
        # Check if model is registered
        registered_model = registry.get_model("test_model")
        assert registered_model is mock_model
    
    def test_get_model(self):
        """تست دریافت مدل"""
        registry = ModelRegistry()
        
        # Test getting non-existent model
        model = registry.get_model("non_existent")
        assert model is None
        
        # Register and get model
        mock_model = Mock()
        registry.register_model("test_model", mock_model)
        
        retrieved_model = registry.get_model("test_model")
        assert retrieved_model is mock_model
    
    def test_get_available_models(self):
        """تست دریافت مدل‌های موجود"""
        registry = ModelRegistry()
        
        # Initially empty
        models = registry.get_available_models()
        assert isinstance(models, list)
        
        # Add models
        registry.register_model("model1", Mock())
        registry.register_model("model2", Mock())
        
        models = registry.get_available_models()
        assert len(models) >= 2
        assert "model1" in models
        assert "model2" in models
    
    def test_unregister_model(self):
        """تست حذف مدل"""
        registry = ModelRegistry()
        
        # Register model
        mock_model = Mock()
        registry.register_model("test_model", mock_model)
        
        # Unregister model
        result = registry.unregister_model("test_model")
        assert result is True
        
        # Check if model is removed
        model = registry.get_model("test_model")
        assert model is None

@pytest.mark.ai
class TestModelManager:
    """تست مدیر مدل"""
    
    def test_model_manager_creation(self):
        """تست ایجاد مدیر مدل"""
        manager = ModelManager()
        assert manager is not None
        assert hasattr(manager, 'registry')
        assert hasattr(manager, 'cache')
        assert hasattr(manager, 'metrics')
    
    def test_initialize_models(self):
        """تست مقداردهی اولیه مدل‌ها"""
        # This might not work without actual models, so we'll mock it
        with patch('ai_models.model_manager.ModelManager.load_default_models') as mock_load:
            mock_load.return_value = True
            
            manager = initialize_models()
            assert manager is not None
    
    @patch('ai_models.model_manager.ModelManager._load_huggingface_model')
    def test_load_model(self, mock_load_hf):
        """تست بارگذاری مدل"""
        manager = ModelManager()
        
        # Mock successful loading
        mock_model = Mock()
        mock_load_hf.return_value = mock_model
        
        model = manager.load_model("test_model", "sentiment")
        assert model is mock_model
        mock_load_hf.assert_called_once()
    
    def test_get_model_info(self):
        """تست دریافت اطلاعات مدل"""
        manager = ModelManager()
        
        # Register mock model
        mock_model = Mock()
        mock_model.get_model_info.return_value = {
            "name": "test_model",
            "type": "test",
            "status": "loaded"
        }
        
        manager.registry.register_model("test_model", mock_model)
        
        info = manager.get_model_info("test_model")
        assert info["name"] == "test_model"
        assert info["type"] == "test"
    
    def test_model_health_check(self):
        """تست بررسی سلامت مدل"""
        manager = ModelManager()
        
        # Register mock model with health check
        mock_model = Mock()
        mock_model.health_check.return_value = {"status": "healthy"}
        
        manager.registry.register_model("test_model", mock_model)
        
        health = manager.check_model_health("test_model")
        assert health["status"] == "healthy"

@pytest.mark.ai
class TestModelCache:
    """تست کش مدل"""
    
    def test_cache_creation(self):
        """تست ایجاد کش"""
        cache = ModelCache(max_size=10)
        assert cache is not None
        assert cache.max_size == 10
    
    def test_cache_operations(self):
        """تست عملیات کش"""
        cache = ModelCache(max_size=2)
        
        # Test caching
        mock_model1 = Mock()
        mock_model2 = Mock()
        
        cache.put("model1", mock_model1)
        cache.put("model2", mock_model2)
        
        # Test retrieval
        cached_model1 = cache.get("model1")
        assert cached_model1 is mock_model1
        
        cached_model2 = cache.get("model2")
        assert cached_model2 is mock_model2
    
    def test_cache_eviction(self):
        """تست حذف از کش"""
        cache = ModelCache(max_size=2)
        
        # Fill cache beyond capacity
        cache.put("model1", Mock())
        cache.put("model2", Mock())
        cache.put("model3", Mock())  # Should evict oldest
        
        # Check if oldest is evicted
        evicted_model = cache.get("model1")
        assert evicted_model is None
        
        # Check if newest are still there
        assert cache.get("model2") is not None
        assert cache.get("model3") is not None

@pytest.mark.ai
class TestHuggingFaceModel:
    """تست مدل HuggingFace"""
    
    @patch('ai_models.huggingface_models.transformers')
    def test_huggingface_model_creation(self, mock_transformers):
        """تست ایجاد مدل HuggingFace"""
        # Mock transformers
        mock_transformers.AutoTokenizer.from_pretrained.return_value = Mock()
        mock_transformers.AutoModel.from_pretrained.return_value = Mock()
        
        model = HuggingFaceModel(
            name="test/model",
            model_type="sentiment"
        )
        
        assert model is not None
        assert model.name == "test/model"
        assert model.model_type == "sentiment"
    
    @patch('ai_models.huggingface_models.transformers')
    def test_model_loading(self, mock_transformers):
        """تست بارگذاری مدل"""
        # Mock transformers
        mock_tokenizer = Mock()
        mock_model = Mock()
        
        mock_transformers.AutoTokenizer.from_pretrained.return_value = mock_tokenizer
        mock_transformers.AutoModel.from_pretrained.return_value = mock_model
        
        hf_model = HuggingFaceModel(
            name="test/model",
            model_type="sentiment"
        )
        
        result = hf_model.load_model()
        assert result is True
        assert hf_model.tokenizer is mock_tokenizer
        assert hf_model.model is mock_model
    
    @patch('ai_models.huggingface_models.transformers')
    def test_model_prediction(self, mock_transformers):
        """تست پیش‌بینی مدل"""
        # Mock transformers and model
        mock_tokenizer = Mock()
        mock_model = Mock()
        
        # Mock tokenizer output
        mock_tokenizer.return_value = {
            'input_ids': [[1, 2, 3]],
            'attention_mask': [[1, 1, 1]]
        }
        
        # Mock model output
        mock_output = Mock()
        mock_output.last_hidden_state = Mock()
        mock_model.return_value = mock_output
        
        mock_transformers.AutoTokenizer.from_pretrained.return_value = mock_tokenizer
        mock_transformers.AutoModel.from_pretrained.return_value = mock_model
        
        hf_model = HuggingFaceModel(
            name="test/model",
            model_type="sentiment"
        )
        hf_model.load_model()
        
        # Test prediction
        result = hf_model.predict("test text")
        assert isinstance(result, ModelPrediction)
    
    def test_proxy_configuration(self):
        """تست پیکربندی پروکسی"""
        proxy_config = {
            "http_proxy": "http://127.0.0.1:10809",
            "https_proxy": "http://127.0.0.1:10809"
        }
        
        model = HuggingFaceModel(
            name="test/model",
            model_type="sentiment",
            proxy_config=proxy_config
        )
        
        assert model.proxy_config == proxy_config
    
    def test_device_management(self):
        """تست مدیریت device"""
        model = HuggingFaceModel(
            name="test/model",
            model_type="sentiment",
            device="cpu"
        )
        
        assert model.device == "cpu"
        
        # Test auto device detection
        auto_model = HuggingFaceModel(
            name="test/model",
            model_type="sentiment",
            device="auto"
        )
        
        assert auto_model.device in ["cpu", "cuda"]

@pytest.mark.ai
class TestSentimentModels:
    """تست مدل‌های تحلیل احساسات"""
    
    @patch('ai_models.sentiment_models.HuggingFaceModel.load_model')
    def test_finbert_model(self, mock_load):
        """تست مدل FinBERT"""
        mock_load.return_value = True
        
        model = FinBERTModel()
        assert model is not None
        assert model.name == "ProsusAI/finbert"
        assert model.model_type == "sentiment"
        
        # Test model loading
        result = model.load_model()
        assert result is True
    
    @patch('ai_models.sentiment_models.HuggingFaceModel.predict')
    def test_finbert_prediction(self, mock_predict):
        """تست پیش‌بینی FinBERT"""
        # Mock prediction output
        mock_predict.return_value = ModelPrediction(
            prediction="positive",
            confidence=0.85,
            metadata={"scores": {"positive": 0.85, "negative": 0.15}}
        )
        
        model = FinBERTModel()
        result = model.predict("Bitcoin is performing well")
        
        assert isinstance(result, ModelPrediction)
        assert result.prediction == "positive"
        assert result.confidence == 0.85
    
    @patch('ai_models.sentiment_models.HuggingFaceModel.load_model')
    def test_cryptobert_model(self, mock_load):
        """تست مدل CryptoBERT"""
        mock_load.return_value = True
        
        model = CryptoBERTModel()
        assert model is not None
        assert "crypto" in model.name.lower() or "ElKulako/cryptobert" in model.name
        assert model.model_type == "sentiment"
    
    def test_financial_sentiment_model(self):
        """تست مدل تحلیل احساسات مالی"""
        model = FinancialSentimentModel(name="test/financial-sentiment")
        assert model is not None
        assert model.name == "test/financial-sentiment"
        assert model.model_type == "sentiment"
    
    def test_financial_keywords_processing(self):
        """تست پردازش کلمات کلیدی مالی"""
        model = FinancialSentimentModel()
        
        # Test keyword enhancement
        text = "BTC price surge"
        enhanced_text = model._enhance_financial_text(text)
        
        # Should contain more financial context
        assert len(enhanced_text) >= len(text)
    
    def test_crypto_specific_features(self):
        """تست ویژگی‌های خاص کریپتو"""
        model = CryptoBERTModel()
        
        # Test crypto-specific processing
        crypto_text = "Bitcoin reaches new ATH"
        result = model._preprocess_crypto_text(crypto_text)
        
        # Should handle crypto-specific terms
        assert "bitcoin" in result.lower() or "btc" in result.lower()

@pytest.mark.ai
class TestModelMetrics:
    """تست متریک‌های مدل"""
    
    def test_metrics_creation(self):
        """تست ایجاد متریک‌ها"""
        metrics = ModelMetrics()
        assert metrics is not None
        assert hasattr(metrics, 'prediction_count')
        assert hasattr(metrics, 'total_time')
        assert hasattr(metrics, 'error_count')
    
    def test_metrics_recording(self):
        """تست ثبت متریک‌ها"""
        metrics = ModelMetrics()
        
        # Record prediction
        metrics.record_prediction(0.5)  # 0.5 seconds
        
        assert metrics.prediction_count == 1
        assert metrics.total_time == 0.5
        assert metrics.error_count == 0
    
    def test_metrics_calculation(self):
        """تست محاسبه متریک‌ها"""
        metrics = ModelMetrics()
        
        # Record multiple predictions
        metrics.record_prediction(0.3)
        metrics.record_prediction(0.7)
        metrics.record_prediction(0.5)
        
        # Calculate average time
        avg_time = metrics.get_average_time()
        assert avg_time == 0.5  # (0.3 + 0.7 + 0.5) / 3
        
        # Test statistics
        stats = metrics.get_statistics()
        assert stats['prediction_count'] == 3
        assert stats['total_time'] == 1.5
        assert stats['average_time'] == 0.5
        assert stats['error_count'] == 0

@pytest.mark.ai
@pytest.mark.integration
class TestModelIntegration:
    """تست یکپارچه‌سازی مدل‌ها"""
    
    def test_model_workflow(self):
        """تست workflow کامل مدل"""
        # Create manager
        manager = ModelManager()
        
        # Mock model
        mock_model = Mock()
        mock_model.name = "test_model"
        mock_model.model_type = "sentiment"
        mock_model.predict.return_value = ModelPrediction(
            prediction="positive",
            confidence=0.8,
            metadata={}
        )
        
        # Register model
        manager.registry.register_model("test_model", mock_model)
        
        # Get model
        model = manager.get_model("test_model")
        assert model is mock_model
        
        # Make prediction
        result = model.predict("test input")
        assert isinstance(result, ModelPrediction)
        assert result.confidence == 0.8
    
    def test_model_ensemble(self):
        """تست ensemble مدل‌ها"""
        # Create multiple mock models
        model1 = Mock()
        model1.predict.return_value = ModelPrediction("positive", 0.7, {})
        
        model2 = Mock()
        model2.predict.return_value = ModelPrediction("positive", 0.9, {})
        
        models = [model1, model2]
        
        # Simple ensemble logic
        predictions = [model.predict("test") for model in models]
        confidences = [p.confidence for p in predictions]
        avg_confidence = sum(confidences) / len(confidences)
        
        assert avg_confidence == 0.8  # (0.7 + 0.9) / 2

@pytest.mark.ai
@pytest.mark.asyncio
class TestAsyncModelOperations:
    """تست عملیات async مدل"""
    
    async def test_async_model_loading(self):
        """تست بارگذاری async مدل"""
        async def async_load_model():
            await asyncio.sleep(0.1)  # Simulate loading time
            return True
        
        result = await async_load_model()
        assert result is True
    
    async def test_async_batch_prediction(self):
        """تست پیش‌بینی دسته‌ای async"""
        mock_model = Mock()
        mock_model.predict.return_value = ModelPrediction("positive", 0.8, {})
        
        async def async_batch_predict(texts):
            results = []
            for text in texts:
                # Simulate async prediction
                await asyncio.sleep(0.01)
                result = mock_model.predict(text)
                results.append(result)
            return results
        
        texts = ["text1", "text2", "text3"]
        results = await async_batch_predict(texts)
        
        assert len(results) == 3
        for result in results:
            assert isinstance(result, ModelPrediction)

@pytest.mark.ai
@pytest.mark.performance
class TestModelPerformance:
    """تست عملکرد مدل"""
    
    def test_model_loading_time(self, performance_tracker):
        """تست زمان بارگذاری مدل"""
        performance_tracker.start()
        
        # Simulate model loading
        import time
        time.sleep(0.1)
        
        performance_tracker.stop()
        
        duration = performance_tracker.get_duration()
        assert duration >= 0.1
        assert duration < 1.0  # Should be reasonable
    
    def test_prediction_time(self, performance_tracker):
        """تست زمان پیش‌بینی"""
        mock_model = Mock()
        mock_model.predict.return_value = ModelPrediction("positive", 0.8, {})
        
        performance_tracker.start()
        
        # Make multiple predictions
        for _ in range(10):
            mock_model.predict("test text")
        
        performance_tracker.stop()
        
        duration = performance_tracker.get_duration()
        avg_time_per_prediction = duration / 10
        
        # Should be fast
        assert avg_time_per_prediction < 0.1
    
    def test_memory_usage(self, performance_tracker):
        """تست استفاده از حافظه"""
        performance_tracker.start()
        
        # Simulate memory-intensive operation
        large_data = [i for i in range(100000)]
        
        performance_tracker.stop()
        
        memory_usage = performance_tracker.get_memory_usage()
        
        # Should track memory change
        if memory_usage:
            assert 'delta_mb' in memory_usage

@pytest.mark.ai
@pytest.mark.network
@pytest.mark.slow
class TestModelDownload:
    """تست دانلود مدل (نیاز به شبکه)"""
    
    @pytest.mark.skipif(True, reason="Requires network and is slow")
    def test_model_download(self):
        """تست دانلود مدل واقعی"""
        # This would test actual model download
        # Skipped by default due to network requirement
        pass
    
    @pytest.mark.skipif(True, reason="Requires proxy configuration")
    def test_model_download_with_proxy(self):
        """تست دانلود مدل با پروکسی"""
        # This would test download through proxy
        # Skipped by default
        pass

@pytest.mark.ai
class TestErrorHandling:
    """تست مدیریت خطا در مدل‌ها"""
    
    def test_model_load_error(self):
        """تست خطای بارگذاری مدل"""
        with patch('ai_models.huggingface_models.transformers.AutoModel.from_pretrained') as mock_load:
            mock_load.side_effect = Exception("Model not found")
            
            model = HuggingFaceModel("nonexistent/model", "sentiment")
            
            with pytest.raises(ModelLoadError):
                model.load_model()
    
    def test_prediction_error(self):
        """تست خطای پیش‌بینی"""
        mock_model = Mock()
        mock_model.predict.side_effect = Exception("Prediction failed")
        
        manager = ModelManager()
        manager.registry.register_model("error_model", mock_model)
        
        model = manager.get_model("error_model")
        
        with pytest.raises(Exception):
            model.predict("test input")
    
    def test_network_error_handling(self):
        """تست مدیریت خطای شبکه"""
        with patch('ai_models.huggingface_models.transformers.AutoModel.from_pretrained') as mock_load:
            mock_load.side_effect = ConnectionError("Network error")
            
            model = HuggingFaceModel("test/model", "sentiment")
            
            with pytest.raises(Exception):
                model.load_model()
    
    def test_invalid_input_handling(self):
        """تست مدیریت ورودی نامعتبر"""
        mock_model = Mock()
        mock_model.predict.return_value = ModelPrediction("unknown", 0.0, {})
        
        # Test with None input
        result = mock_model.predict(None)
        assert result.confidence == 0.0
        
        # Test with empty input
        result = mock_model.predict("")
        assert result.confidence == 0.0 