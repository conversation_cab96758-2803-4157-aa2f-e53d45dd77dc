"""
🏠 Test Local HuggingFace Models
تست مدل‌های محلی HuggingFace

این تست شامل:
1. تست LocalHuggingFaceModel
2. تست LocalEnsembleModel
3. تست Enhanced HuggingFaceSentimentManager
4. تست عملکرد مدل‌های محلی
"""

import os
import sys
import time
import json
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_local_model():
    """تست مدل محلی"""
    print("🏠 Testing Local HuggingFace Model...")
    
    try:
        from ai_models.sentiment_models import LocalHuggingFaceModel
        
        # Test Financial RoBERTa (should be downloaded)
        print("🔄 Testing Financial RoBERTa...")
        roberta = LocalHuggingFaceModel("financial_roberta")
        
        # Load model
        load_success = roberta.load()
        if load_success:
            print("✅ Financial RoBERTa loaded successfully from local cache")
            
            # Test sentiment analysis
            test_texts = [
                "The stock market is showing strong bullish momentum today",
                "Economic uncertainty is causing significant market volatility",
                "Company earnings exceeded all analyst expectations"
            ]
            
            for text in test_texts:
                result = roberta.analyze_sentiment(text)
                print(f"  📊 Text: {text[:50]}...")
                print(f"      Sentiment: {result.sentiment} (confidence: {result.confidence:.3f})")
                print(f"      Processing time: {result.processing_time:.3f}s")
            
            return True
        else:
            print("❌ Failed to load Financial RoBERTa")
            return False
            
    except Exception as e:
        print(f"❌ Local model test failed: {e}")
        return False

def test_local_ensemble():
    """تست ensemble محلی"""
    print("\n🎯 Testing Local Ensemble Model...")
    
    try:
        from ai_models.sentiment_models import LocalEnsembleModel
        
        # Initialize local ensemble
        ensemble = LocalEnsembleModel()
        print("✅ Local ensemble initialized")
        
        # Download and load models
        print("🔄 Loading local models...")
        load_results = ensemble.download_and_load_models()
        
        successful_models = sum(1 for success in load_results.values() if success)
        print(f"📊 Local ensemble status: {successful_models} models loaded")
        
        if successful_models > 0:
            print("✅ Local ensemble ready")
            
            # Test ensemble analysis
            test_text = "The financial markets are experiencing unprecedented growth with strong investor confidence"
            
            result = ensemble.analyze_sentiment(test_text)
            print(f"  🎯 Local Ensemble Result:")
            print(f"      Text: {test_text}")
            print(f"      Sentiment: {result.sentiment} (confidence: {result.confidence:.3f})")
            print(f"      Probabilities: {result.probabilities}")
            print(f"      Processing time: {result.processing_time:.3f}s")
            
            # Get model status
            status = ensemble.get_model_status()
            print(f"  📊 Model Status:")
            print(f"      Models loaded: {status['models_loaded']}")
            print(f"      Available models: {status['available_models']}")
            
            return True
        else:
            print("❌ No local models loaded")
            return False
            
    except Exception as e:
        print(f"❌ Local ensemble test failed: {e}")
        return False

def test_enhanced_manager():
    """تست مدیر پیشرفته"""
    print("\n🤗 Testing Enhanced HuggingFace Sentiment Manager...")
    
    try:
        from ai_models.sentiment_models import HuggingFaceSentimentManager
        
        # Initialize manager with local preference
        manager = HuggingFaceSentimentManager(prefer_local=True, use_proxy=True)
        print("✅ Enhanced manager initialized")
        
        # Initialize all models
        print("🔄 Initializing all models...")
        summary = manager.initialize_all_models()
        
        print(f"📊 Initialization Summary:")
        print(f"    Successful: {summary['initialization_successful']}")
        print(f"    Active model type: {summary['active_model_type']}")
        print(f"    Proxy enabled: {summary['proxy_enabled']}")
        print(f"    Prefer local: {summary['prefer_local']}")
        
        if summary["initialization_successful"]:
            print("✅ Manager initialization successful")
            
            # Test sentiment analysis
            test_texts = [
                "The cryptocurrency market is experiencing massive growth",
                "Federal Reserve policy changes are impacting global markets",
                "Tech stocks are showing remarkable resilience"
            ]
            
            print(f"  🎯 Testing sentiment analysis:")
            for text in test_texts:
                result = manager.analyze_with_best_model(text)
                print(f"    Text: {text[:40]}...")
                print(f"    Result: {result.sentiment} ({result.confidence:.3f}) - {result.processing_time:.3f}s")
            
            # Get detailed analysis
            detailed_text = "The stock market rally continues with strong corporate earnings and positive economic indicators"
            detailed = manager.get_detailed_analysis(detailed_text)
            print(f"  📊 Detailed Analysis:")
            print(f"    Text: {detailed_text[:50]}...")
            print(f"    Result: {detailed['result']['sentiment']} ({detailed['result']['confidence']:.3f})")
            
            # Get model status
            status = manager.get_model_status()
            print(f"  📋 Model Status:")
            print(f"    Active model type: {status['active_model_type']}")
            print(f"    Active model available: {status['active_model_available']}")
            
            return True
        else:
            print("❌ Manager initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced manager test failed: {e}")
        return False

def test_performance_benchmark():
    """تست عملکرد"""
    print("\n⚡ Testing Performance Benchmark...")
    
    try:
        from ai_models.sentiment_models import HuggingFaceSentimentManager
        
        manager = HuggingFaceSentimentManager(prefer_local=True)
        summary = manager.initialize_all_models()
        
        if not summary["initialization_successful"]:
            print("⚠️ Skipping performance test - no models loaded")
            return True
        
        # Performance test texts
        test_texts = [
            "The market is bullish with strong momentum",
            "Economic indicators show positive growth trends",
            "Investor sentiment remains optimistic despite volatility",
            "Corporate earnings are exceeding expectations",
            "The Federal Reserve's decision supports market stability",
            "Technology sector shows remarkable innovation",
            "Financial markets demonstrate strong resilience",
            "Global economic outlook appears increasingly positive",
            "Investment opportunities are expanding rapidly",
            "Market fundamentals support continued growth"
        ]
        
        print(f"🔄 Testing performance on {len(test_texts)} texts...")
        
        # Warm-up run
        manager.analyze_with_best_model("Warm-up text for model initialization")
        
        # Actual performance test
        start_time = time.time()
        results = []
        
        for i, text in enumerate(test_texts):
            text_start = time.time()
            result = manager.analyze_with_best_model(text)
            text_time = time.time() - text_start
            
            results.append({
                "text": text[:30] + "...",
                "sentiment": result.sentiment,
                "confidence": result.confidence,
                "time": text_time
            })
            
            if i % 3 == 0:  # Show progress
                print(f"    Processed {i+1}/{len(test_texts)} texts...")
        
        total_time = time.time() - start_time
        avg_time = total_time / len(test_texts)
        
        print(f"📊 Performance Results:")
        print(f"    Total time: {total_time:.3f}s")
        print(f"    Average time per text: {avg_time:.3f}s")
        print(f"    Texts per second: {1/avg_time:.1f}")
        print(f"    Model type: {summary['active_model_type']}")
        
        # Show sample results
        print(f"  🎯 Sample Results:")
        for result in results[:3]:
            print(f"    {result['text']}: {result['sentiment']} ({result['confidence']:.3f}) - {result['time']:.3f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def test_model_switching():
    """تست تغییر مدل"""
    print("\n🔄 Testing Model Switching...")
    
    try:
        from ai_models.sentiment_models import HuggingFaceSentimentManager
        
        manager = HuggingFaceSentimentManager()
        
        # Test switching to local models
        print("🏠 Switching to local models...")
        local_success = manager.switch_to_local_models()
        
        if local_success:
            print("✅ Successfully switched to local models")
            
            # Test analysis with local models
            test_text = "Local model test: The market shows positive trends"
            result = manager.analyze_with_best_model(test_text)
            print(f"    Local result: {result.sentiment} ({result.confidence:.3f})")
            
            # Get status
            status = manager.get_model_status()
            print(f"    Active type: {status['active_model_type']}")
            
            return True
        else:
            print("⚠️ Could not switch to local models (may not be available)")
            return True  # Not a critical failure
            
    except Exception as e:
        print(f"❌ Model switching test failed: {e}")
        return False

def main():
    """اجرای تمام تست‌ها"""
    print("🏠 LOCAL HUGGINGFACE MODELS TESTS")
    print("=" * 70)
    
    tests = [
        ("Local HuggingFace Model", test_local_model),
        ("Local Ensemble Model", test_local_ensemble),
        ("Enhanced Manager", test_enhanced_manager),
        ("Performance Benchmark", test_performance_benchmark),
        ("Model Switching", test_model_switching)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 LOCAL HUGGINGFACE MODELS TEST RESULTS")
    print("=" * 70)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    success_rate = (passed / total) * 100
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Success Rate: {success_rate:.1f}% ({passed}/{total})")
    
    if success_rate >= 80:
        print("🎉 Local HuggingFace Models are working excellently!")
        print("🚀 Ready for production sentiment analysis with local models!")
    elif success_rate >= 60:
        print("⚠️ Local models mostly working, minor issues remain")
    else:
        print("❌ Local models need improvements")
    
    # Save results
    results_file = f"local_huggingface_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            "test_results": results,
            "success_rate": success_rate,
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total,
                "passed_tests": passed,
                "failed_tests": total - passed
            }
        }, f, indent=2, ensure_ascii=False)
    
    print(f"📄 Detailed results saved to: {results_file}")
    
    return success_rate

if __name__ == "__main__":
    success_rate = main()
    exit(0 if success_rate >= 70 else 1)
