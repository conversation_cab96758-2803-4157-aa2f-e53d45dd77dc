# 🆓 **راه‌حل‌های رایگان برای استفاده از مدل‌های مالی بدون GPU قوی**

## 📋 **فهرست راه‌حل‌ها**

### 🔥 **1. Hugging Face Inference API (رایگان)**
- **محدودیت رایگان**: 1000 request/ماه برای free tier
- **مدل‌های پشتیبانی شده**: تمام مدل‌های financial از لیست قبلی
- **سرعت**: فوری (serverless)
- **استفاده**: API calls یا Python client

### ☁️ **2. Google Colab رایگان**
- **GPU رایگان**: Tesla T4 تا 12 ساعت/روز
- **RAM**: تا 12.7GB
- **Storage**: 107GB موقت
- **مدل‌های اجرایی**: تمام مدل‌های تا 7B parameter

### 🏆 **3. Kaggle Notebooks**
- **GPU رایگان**: Tesla T4 تا 30 ساعت/هفته  
- **RAM**: تا 13GB
- **Storage**: 20GB
- **مزیت اضافی**: دسترسی به dataset های مالی

### 💻 **4. مدل‌های CPU-Friendly**
- **مدل‌های lightweight**: DistilBERT, FinBERT base
- **Quantized models**: 8-bit, 4-bit
- **کتابخانه‌های optimized**: ONNX, TensorFlow Lite

---

## 🚀 **راه‌حل 1: Hugging Face Inference API**

### **نصب کتابخانه:**
```bash
pip install huggingface_hub transformers torch
```

### **دریافت API Token:**
1. برو به: https://hf.co/settings/tokens
2. Generate new token با دسترسی `inference`
3. Token رو کپی کن

### **کد پایه برای استفاده:**
```python
from huggingface_hub import InferenceClient
import os

# تنظیم API token
os.environ["HF_TOKEN"] = "hf_xxxxxxxxxxxxxxx"  # token تو

# ساخت client
client = InferenceClient()

# استفاده از مدل sentiment analysis مالی
def analyze_financial_sentiment(text):
    result = client.text_classification(
        text=text,
        model="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"
    )
    return result

# تست
news = "Apple stock surges after strong quarterly earnings"
sentiment = analyze_financial_sentiment(news)
print(f"خبر: {news}")
print(f"احساس: {sentiment}")
```

### **استفاده از مدل‌های پیش‌بینی قیمت:**
```python
# پیش‌بینی time series با Chronos
def predict_prices(price_data):
    prediction = client.feature_extraction(
        inputs=price_data,
        model="amazon/chronos-t5-small"
    )
    return prediction

# تست با داده‌های OHLC
prices = [100.5, 101.2, 99.8, 102.1, 103.5]
forecast = predict_prices(prices)
print(f"پیش‌بینی قیمت: {forecast}")
```

---

## ☁️ **راه‌حل 2: Google Colab رایگان**

### **شروع سریع:**
```python
# در Colab notebook جدید
!pip install transformers datasets torch accelerate

# بررسی GPU
import torch
print(f"GPU موجود: {torch.cuda.is_available()}")
print(f"نوع GPU: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}")

# لود مدل financial sentiment
from transformers import pipeline

# استفاده از مدل FinBERT
finbert = pipeline(
    "sentiment-analysis",
    model="ProsusAI/finbert",
    device=0 if torch.cuda.is_available() else -1
)

# تست
texts = [
    "The company reported record profits this quarter",
    "Stock market crashes amid economic uncertainty", 
    "Neutral trading volume observed today"
]

for text in texts:
    result = finbert(text)
    print(f"متن: {text}")
    print(f"نتیجه: {result}\n")
```

### **Notebook های آماده Colab:**
```python
# کپی کردن notebook های آماده
!git clone https://github.com/verma-rishu/R-D_FinGPT.git
!git clone https://github.com/LikithMeruvu/FinBert-Finetuning-for-Stock-Sentiment.git

# اجرای notebook های FinGPT
%cd R-D_FinGPT
%run FinGPT_ChatGLM2_6B.ipynb
```

---

## 🏆 **راه‌حل 3: Kaggle Notebooks**

### **شروع در Kaggle:**
1. برو به: https://www.kaggle.com/code
2. New Notebook بساز
3. Settings > Accelerator > GPU T4 x2 انتخاب کن

### **کد استارتر Kaggle:**
```python
# دانلود و استفاده از مدل‌های مالی
import kaggle
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch

# لود مدل روی GPU Kaggle
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"استفاده از: {device}")

# مدل sentiment analysis مالی
model_name = "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForSequenceClassification.from_pretrained(model_name).to(device)

def analyze_sentiment(text):
    inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True).to(device)
    with torch.no_grad():
        outputs = model(**inputs)
        predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
    
    labels = ['negative', 'neutral', 'positive']
    scores = predictions.cpu().numpy()[0]
    
    result = {labels[i]: float(scores[i]) for i in range(len(labels))}
    return result

# تست
financial_news = [
    "Tesla stock jumps 15% on strong delivery numbers",
    "Banking sector faces regulatory pressures",
    "Market remains stable with low volatility"
]

for news in financial_news:
    sentiment = analyze_sentiment(news)
    print(f"خبر: {news}")
    print(f"تحلیل احساس: {sentiment}\n")
```

---

## 💻 **راه‌حل 4: مدل‌های CPU-Friendly**

### **استفاده از مدل‌های Quantized:**
```python
# نصب کتابخانه‌های لازم
!pip install transformers torch optimum onnxruntime

from transformers import pipeline
from optimum.onnxruntime import ORTModelForSequenceClassification
from transformers import AutoTokenizer

# لود مدل quantized برای CPU
model_name = "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"

# روش 1: استفاده از مدل optimized
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = ORTModelForSequenceClassification.from_pretrained(
    model_name, 
    from_tf=False,
    provider="CPUExecutionProvider"  # اجرا روی CPU
)

# تابع تحلیل سریع
def quick_sentiment_analysis(text):
    inputs = tokenizer(text, return_tensors="pt")
    outputs = model(**inputs)
    return outputs.logits.argmax().item()

# تست سرعت
import time

texts = ["Stock prices are rising"] * 100

start_time = time.time()
results = [quick_sentiment_analysis(text) for text in texts]
end_time = time.time()

print(f"تحلیل {len(texts)} متن در {end_time - start_time:.2f} ثانیه")
print(f"سرعت: {len(texts)/(end_time - start_time):.1f} متن/ثانیه")
```

### **استفاده از مدل‌های کوچک:**
```python
# مدل‌های lightweight برای CPU
lightweight_models = {
    "financial_sentiment": "ProsusAI/finbert",
    "economic_analysis": "jean-baptiste/camembert-ner-with-dates",
    "market_classification": "nlptown/bert-base-multilingual-uncased-sentiment"
}

# تابع انتخاب بهترین مدل بر اساس منابع
def select_optimal_model(task="financial_sentiment", max_memory_gb=4):
    if max_memory_gb < 2:
        return "distilbert-base-uncased"  # خیلی کوچک
    elif max_memory_gb < 4:
        return "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"
    else:
        return "ProsusAI/finbert"  # کامل

# استفاده خودکار
optimal_model = select_optimal_model(max_memory_gb=2)
classifier = pipeline("sentiment-analysis", model=optimal_model, device=-1)

# تست
result = classifier("Apple reported strong quarterly earnings")
print(f"نتیجه با مدل بهینه: {result}")
```

---

## 🔄 **راه‌حل 5: ترکیب چندین روش**

### **کد هوشمند برای انتخاب بهترین روش:**
```python
import torch
import psutil
import requests

class SmartFinancialAnalyzer:
    def __init__(self):
        self.device = self._detect_best_device()
        self.model = self._load_optimal_model()
    
    def _detect_best_device(self):
        """تشخیص بهترین device موجود"""
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            if gpu_memory > 8:
                return "cuda_high"
            else:
                return "cuda_low"
        else:
            ram_gb = psutil.virtual_memory().total / 1e9
            if ram_gb > 8:
                return "cpu_high"
            else:
                return "cpu_low"
    
    def _load_optimal_model(self):
        """انتخاب مدل بر اساس منابع"""
        if self.device == "cuda_high":
            # استفاده از مدل‌های بزرگ
            return pipeline("sentiment-analysis", 
                          model="ProsusAI/finbert", device=0)
        elif self.device == "cuda_low":
            # مدل متوسط
            return pipeline("sentiment-analysis", 
                          model="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis", 
                          device=0)
        elif self.device == "cpu_high":
            # CPU با quantization
            return pipeline("sentiment-analysis", 
                          model="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis", 
                          device=-1)
        else:
            # fallback به API
            return self._use_api_fallback()
    
    def _use_api_fallback(self):
        """استفاده از API در صورت کمبود منابع"""
        try:
            from huggingface_hub import InferenceClient
            return InferenceClient()
        except:
            return None
    
    def analyze(self, text):
        """تحلیل هوشمند بر اساس منابع موجود"""
        if isinstance(self.model, InferenceClient):
            # استفاده از API
            return self.model.text_classification(
                text=text,
                model="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"
            )
        else:
            # استفاده از مدل محلی
            return self.model(text)

# استفاده
analyzer = SmartFinancialAnalyzer()
result = analyzer.analyze("Tesla stock soars on positive earnings")
print(f"نتیجه تحلیل: {result}")
```

---

## 🎯 **راه‌حل 6: استفاده ترکیبی برای پروژه معاملاتی**

### **کد یکپارچه برای سیستم معاملاتی:**
```python
class LowResourceTradingSystem:
    """سیستم معاملاتی برای منابع محدود"""
    
    def __init__(self, api_key=None):
        self.api_key = api_key
        self.setup_models()
    
    def setup_models(self):
        """راه‌اندازی مدل‌ها بر اساس منابع موجود"""
        try:
            # سعی در استفاده از GPU
            if torch.cuda.is_available():
                self.sentiment_model = pipeline(
                    "sentiment-analysis",
                    model="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
                    device=0
                )
                self.prediction_mode = "local_gpu"
            else:
                # fallback به CPU
                self.sentiment_model = pipeline(
                    "sentiment-analysis", 
                    model="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
                    device=-1
                )
                self.prediction_mode = "local_cpu"
        except Exception as e:
            # fallback به API
            if self.api_key:
                from huggingface_hub import InferenceClient
                self.client = InferenceClient(token=self.api_key)
                self.prediction_mode = "api"
            else:
                raise Exception("نیاز به API key یا منابع محلی")
    
    def analyze_market_sentiment(self, news_list):
        """تحلیل احساس بازار از اخبار"""
        sentiments = []
        
        for news in news_list:
            if self.prediction_mode == "api":
                result = self.client.text_classification(
                    text=news,
                    model="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"
                )
            else:
                result = self.sentiment_model(news)
            
            sentiments.append(result)
        
        return sentiments
    
    def generate_trading_signal(self, price_data, news_data):
        """تولید سیگنال معاملاتی"""
        # تحلیل احساس اخبار
        sentiment_scores = self.analyze_market_sentiment(news_data)
        
        # محاسبه میانگین احساس
        positive_count = sum(1 for s in sentiment_scores 
                           if s[0]['label'] == 'POSITIVE')
        sentiment_ratio = positive_count / len(sentiment_scores)
        
        # ترکیب با تحلیل تکنیکال ساده
        price_trend = "up" if price_data[-1] > price_data[-5] else "down"
        
        # تصمیم‌گیری
        if sentiment_ratio > 0.6 and price_trend == "up":
            return "BUY"
        elif sentiment_ratio < 0.4 and price_trend == "down":
            return "SELL"
        else:
            return "HOLD"

# استفاده در پروژه معاملاتی
trading_system = LowResourceTradingSystem(api_key="hf_xxxxxxxxx")

# تست با داده‌های نمونه
news = [
    "Apple reports record iPhone sales",
    "Tech stocks rally on positive earnings",
    "Market optimism grows stronger"
]

prices = [150, 152, 151, 153, 155, 157, 156]

signal = trading_system.generate_trading_signal(prices, news)
print(f"سیگنال معاملاتی: {signal}")
```

---

## 📊 **مقایسه راه‌حل‌ها**

| روش | هزینه | سرعت | دقت | محدودیت |
|-----|-------|-------|------|----------|
| HF API | رایگان (محدود) | عالی | بالا | 1000 req/ماه |
| Google Colab | رایگان | خوب | بالا | 12 ساعت/روز |
| Kaggle | رایگان | خوب | بالا | 30 ساعت/هفته |
| CPU Local | رایگان | متوسط | متوسط | بدون محدودیت |
| Quantized | رایگان | خوب | متوسط | بدون محدودیت |

---

## 🎓 **نکات مهم و بهینه‌سازی**

### **1. مدیریت محدودیت‌های API:**
```python
# کش کردن نتایج برای صرفه‌جویی در API calls
import pickle
import hashlib

class CachedAnalyzer:
    def __init__(self, cache_file="analysis_cache.pkl"):
        self.cache_file = cache_file
        self.cache = self.load_cache()
    
    def load_cache(self):
        try:
            with open(self.cache_file, 'rb') as f:
                return pickle.load(f)
        except:
            return {}
    
    def save_cache(self):
        with open(self.cache_file, 'wb') as f:
            pickle.dump(self.cache, f)
    
    def get_cache_key(self, text):
        return hashlib.md5(text.encode()).hexdigest()
    
    def analyze_with_cache(self, text):
        key = self.get_cache_key(text)
        if key in self.cache:
            return self.cache[key]
        
        # اجرای تحلیل واقعی
        result = self.analyze(text)  # تابع تحلیل واقعی
        self.cache[key] = result
        self.save_cache()
        return result
```

### **2. بهینه‌سازی استفاده از منابع:**
```python
# تنظیم batch size بر اساس منابع
def optimize_batch_size():
    available_memory = psutil.virtual_memory().available / 1e9
    if available_memory > 8:
        return 32
    elif available_memory > 4:
        return 16
    else:
        return 8

# استفاده بهینه
batch_size = optimize_batch_size()
texts = ["متن 1", "متن 2", ...]  # لیست متن‌ها

# پردازش به صورت batch
for i in range(0, len(texts), batch_size):
    batch = texts[i:i+batch_size]
    results = model(batch)  # پردازش دسته‌ای
```

---

## 🚀 **شروع سریع - کد آماده**

```python
# کپی و paste این کد برای شروع سریع
import torch
from transformers import pipeline
import os

# تشخیص خودکار بهترین روش
def setup_financial_analyzer():
    try:
        # تست GPU
        if torch.cuda.is_available():
            print("✅ GPU پیدا شد - استفاده از مدل محلی")
            return pipeline(
                "sentiment-analysis",
                model="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
                device=0
            )
        else:
            print("⚠️ GPU پیدا نشد - استفاده از CPU")
            return pipeline(
                "sentiment-analysis",
                model="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
                device=-1
            )
    except Exception as e:
        print(f"❌ خطا در لود مدل محلی: {e}")
        print("🔄 تلاش برای استفاده از API...")
        
        # fallback به API
        hf_token = input("لطفاً Hugging Face token خود را وارد کنید: ")
        os.environ["HF_TOKEN"] = hf_token
        
        from huggingface_hub import InferenceClient
        return InferenceClient()

# راه‌اندازی
analyzer = setup_financial_analyzer()

# تست
test_news = "Apple stock jumps 10% on strong quarterly results"
result = analyzer(test_news) if hasattr(analyzer, '__call__') else analyzer.text_classification(
    text=test_news,
    model="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"
)

print(f"📰 خبر: {test_news}")
print(f"📊 نتیجه: {result}")
```

با این راه‌حل‌ها می‌تونی بدون هیچ هزینه‌ای و بدون نیاز به GPU قوی از مدل‌های پیشرفته مالی استفاده کنی! 🎉 