import unittest
import sys
import os
import numpy as np
import pandas as pd
import torch

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.auto_market_maker import AutoMarketMaker, DeepRLMarketMaker

class TestAutoMarketMaker(unittest.TestCase):
    """
    Test cases for the AutoMarketMaker class
    """
    
    def setUp(self):
        """
        Set up test environment before each test
        """
        # Simple market maker with default settings
        self.market_maker = AutoMarketMaker(
            spread_range=(0.0001, 0.001),
            inventory_limit=10000,
            use_deep_rl=False  # Use simple Q-learning for faster tests
        )
        
        # Sample order book data
        self.order_book = {
            'EURUSD': {'bid': 1.1000, 'ask': 1.1002},
            'GBPUSD': {'bid': 1.3000, 'ask': 1.3005}
        }
        
        # Sample market features
        self.market_features = {
            'volatility': 0.002,
            'volume': 0.5
        }
    
    def test_initialization(self):
        """Test that the market maker initializes correctly"""
        self.assertEqual(self.market_maker.inventory, 0)
        self.assertEqual(len(self.market_maker.history), 0)
        self.assertEqual(len(self.market_maker.quote_history), 0)
        self.assertEqual(self.market_maker.total_pnl, 0)
    
    def test_quote_generation(self):
        """Test quote generation"""
        quote = self.market_maker.quote(self.order_book, 'EURUSD')
        
        # Check that quote has expected fields
        self.assertIn('bid', quote)
        self.assertIn('ask', quote)
        self.assertIn('spread', quote)
        
        # Check that spread is within range
        self.assertGreaterEqual(quote['spread'], self.market_maker.spread_range[0])
        self.assertLessEqual(quote['spread'], self.market_maker.spread_range[1])
        
        # Check that bid is less than ask
        self.assertLess(quote['bid'], quote['ask'])
    
    def test_inventory_update(self):
        """Test inventory update"""
        initial_inventory = self.market_maker.inventory
        
        # Buy 1000 units
        self.market_maker.update_inventory('buy', 1000)
        self.assertEqual(self.market_maker.inventory, initial_inventory + 1000)
        self.assertEqual(len(self.market_maker.history), 1)
        
        # Sell 500 units
        self.market_maker.update_inventory('sell', 500)
        self.assertEqual(self.market_maker.inventory, initial_inventory + 1000 - 500)
        self.assertEqual(len(self.market_maker.history), 2)
    
    def test_market_making_simulation(self):
        """Test market making simulation"""
        # Create a sequence of order books
        order_books = [
            {'EURUSD': {'bid': 1.1000, 'ask': 1.1002}},
            {'EURUSD': {'bid': 1.1001, 'ask': 1.1003}},
            {'EURUSD': {'bid': 1.0999, 'ask': 1.1001}}
        ]
        
        # Create actions
        actions = [
            {'side': 'buy', 'amount': 1000, 'price': 1.1000},
            {'side': 'sell', 'amount': 500, 'price': 1.1003},
            {'side': 'sell', 'amount': 500, 'price': 1.1001}
        ]
        
        # Run simulation
        results = self.market_maker.simulate_market_making(order_books, 'EURUSD', actions)
        
        # Check results
        self.assertEqual(len(results), 3)
        self.assertEqual(self.market_maker.inventory, 0)  # Should be back to 0 after simulation
        self.assertGreaterEqual(len(self.market_maker.quote_history), 3)
        self.assertGreaterEqual(len(self.market_maker.pnl_history), 3)
    
    def test_state_extraction(self):
        """Test state extraction from order book"""
        state = self.market_maker._get_state(self.order_book, 'EURUSD', self.market_features)
        
        # Check state dimensions
        self.assertEqual(len(state), 7)
        
        # Check state values
        self.assertEqual(state[0], 1.1000)  # bid
        self.assertEqual(state[1], 1.1002)  # ask
        self.assertEqual(state[2], 1.1001)  # mid
        # Use assertAlmostEqual for floating point comparison
        self.assertAlmostEqual(state[3], 0.0002, places=6)  # spread
        self.assertEqual(state[4], 0)  # normalized inventory (0 at start)
        self.assertEqual(state[5], 0.002)  # volatility
        self.assertEqual(state[6], 0.5)  # volume
    
    def test_reset(self):
        """Test reset functionality"""
        # Make some changes to state
        self.market_maker.update_inventory('buy', 1000)
        self.market_maker.quote_history.append({'bid': 1.1, 'ask': 1.11, 'spread': 0.01})
        self.market_maker.pnl_history.append(100)
        self.market_maker.total_pnl = 100
        
        # Reset
        self.market_maker.reset()
        
        # Check reset state
        self.assertEqual(self.market_maker.inventory, 0)
        self.assertEqual(len(self.market_maker.history), 0)
        self.assertEqual(len(self.market_maker.quote_history), 0)
        self.assertEqual(len(self.market_maker.pnl_history), 0)
        self.assertEqual(self.market_maker.total_pnl, 0)
    
    def test_deep_rl_model(self):
        """Test deep RL model initialization"""
        # Create market maker with deep RL
        deep_mm = AutoMarketMaker(
            spread_range=(0.0001, 0.001),
            inventory_limit=10000,
            use_deep_rl=True
        )
        
        # Check model initialization
        self.assertIsInstance(deep_mm.model, DeepRLMarketMaker)
        self.assertIsInstance(deep_mm.target_model, DeepRLMarketMaker)
        
        # Test forward pass
        state = deep_mm._get_state(self.order_book, 'EURUSD', self.market_features)
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        with torch.no_grad():
            output = deep_mm.model(state_tensor)
        
        # Check output dimensions
        self.assertEqual(output.shape, (1, 9))  # Batch size 1, 9 actions
    
    def test_explanation(self):
        """Test decision explanation"""
        # Set inventory to test explanation
        self.market_maker.inventory = 5000
        
        explanation = self.market_maker.explain_decision(
            self.order_book, 'EURUSD', self.market_features
        )
        
        # Check explanation fields
        self.assertIn('spread', explanation)
        self.assertIn('primary_factor', explanation)
        self.assertIn('factor_weights', explanation)
        self.assertIn('inventory_level', explanation)
        self.assertIn('model_type', explanation)
        
        # Check inventory level calculation
        self.assertEqual(explanation['inventory_level'], 0.5)  # 5000/10000
        
        # Check model type
        self.assertEqual(explanation['model_type'], 'q_learning')

    def test_explain_decision(self):
        """Test the explain_decision method."""
        # Setup test order book
        order_book = {
            'EURUSD': {
                'bid': 1.1000,
                'ask': 1.1010
            }
        }
        
        # Get explanation
        explanation = self.market_maker.explain_decision(order_book, 'EURUSD')
        
        # Check explanation structure
        self.assertIsInstance(explanation, dict)
        self.assertIn('decision', explanation)
        self.assertIn('bid', explanation)
        self.assertIn('ask', explanation)
        self.assertIn('spread', explanation)
        self.assertIn('mid_price', explanation)
        self.assertIn('inventory_position', explanation)
        self.assertIn('inventory_skew', explanation)
        self.assertIn('primary_factors', explanation)
        self.assertIn('strategy', explanation)
        self.assertIn('expected', explanation)
        self.assertIn('recommendation', explanation)
        
        # Check primary factors
        self.assertIsInstance(explanation['primary_factors'], list)
        if explanation['primary_factors']:
            factor = explanation['primary_factors'][0]
            self.assertIn('factor', factor)
            self.assertIn('importance', factor)
            self.assertIn('description', factor)
        
        # Test with invalid symbol
        invalid_explanation = self.market_maker.explain_decision(order_book, 'INVALID')
        self.assertEqual(invalid_explanation['decision'], 'no_quote')
        
        # Test with market features
        market_features = {
            'volatility': 0.05,
            'volume': 0.8
        }
        
        explanation_with_features = self.market_maker.explain_decision(
            order_book, 'EURUSD', market_features)
        self.assertIsInstance(explanation_with_features, dict)
        
        # Verify that high volatility is reflected in the explanation
        has_volatility_factor = False
        for factor in explanation_with_features['primary_factors']:
            if factor['factor'] == 'high_volatility':
                has_volatility_factor = True
                break
        
        self.assertTrue(has_volatility_factor)

if __name__ == '__main__':
    unittest.main() 