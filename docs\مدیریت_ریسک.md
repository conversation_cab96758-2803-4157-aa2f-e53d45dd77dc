# مستند جامع: RiskManager

## مسئولیت
مدیریت ریسک معاملات، کنترل درادون روزانه و کلی، تنظیم حجم معاملات بر اساس سرمایه و ریسک.

## پارامترها
- initial_balance: موجودی اولیه
- max_daily_drawdown: حداکثر درادون روزانه
- max_total_drawdown: حداکثر درادون کلی
- risk_per_trade: درصد ریسک هر معامله
- max_open_positions: حداکثر پوزیشن باز
- max_daily_trades: حداکثر معاملات روزانه

## متدهای کلیدی
- update_balance: بروزرسانی موجودی و محاسبه درادون
- get_risk_status: وضعیت فعلی ریسک
- calculate_position_size: محاسبه حجم پوزیشن
- open_position/close_position: باز و بستن پوزیشن
- check_stop_levels: بررسی فعال شدن حد ضرر/سود

## نمونه کد
```python
from utils.risk_manager import RiskManager
rm = RiskManager(initial_balance=1000)
rm.update_balance(950)
status = rm.get_risk_status()
```

## مدیریت خطا
در صورت عبور از حد مجاز، وضعیت به 'stop_trading' تغییر می‌کند.

## بهترین شیوه
همیشه قبل از باز کردن پوزیشن جدید، وضعیت ریسک را چک کنید.

## نمودار
- نمودار drawdown و balance در trade history قابل ترسیم است.

## اتصال به اسکریپت اصلی
- در env/trading_env.py به صورت مستقیم استفاده شده و در جریان اصلی معاملات فعال است.
- در تست‌ها و ماژول‌های دیگر نیز به صورت مستقیم و غیرمستقیم فراخوانی می‌شود. 