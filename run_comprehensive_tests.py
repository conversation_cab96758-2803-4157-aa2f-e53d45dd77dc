"""
Comprehensive Test Runner
اجرای جامع تمام تست‌ها و ذخیره نتایج
"""

import subprocess
import json
import time
from datetime import datetime
import os
from pathlib import Path

def run_command_and_log(command, log_file):
    """اجرای دستور و ذخیره خروجی"""
    print(f"🔄 Running: {command}")
    
    start_time = time.time()
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=120  # 2 minute timeout
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # ذخیره نتایج
        log_data = {
            'command': command,
            'timestamp': datetime.now().isoformat(),
            'execution_time': execution_time,
            'return_code': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'success': result.returncode == 0
        }
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Completed in {execution_time:.2f}s - {'Success' if result.returncode == 0 else 'Failed'}")
        
        return log_data
        
    except subprocess.TimeoutExpired:
        print(f"❌ Command timed out after 120 seconds")
        return {
            'command': command,
            'timestamp': datetime.now().isoformat(),
            'execution_time': 120,
            'return_code': -1,
            'stdout': '',
            'stderr': 'Command timed out',
            'success': False
        }
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return {
            'command': command,
            'timestamp': datetime.now().isoformat(),
            'execution_time': 0,
            'return_code': -1,
            'stdout': '',
            'stderr': str(e),
            'success': False
        }

def main():
    """اجرای تست‌های جامع"""
    
    print("🚀 COMPREHENSIVE ADVANCED SYSTEM TESTING")
    print("=" * 60)
    
    # ایجاد دایرکتوری لاگ
    log_dir = Path("test_logs")
    log_dir.mkdir(exist_ok=True)
    
    # تست‌های مختلف
    tests = [
        {
            'name': 'Final Integration Test',
            'command': 'python test_final_integration.py',
            'log_file': log_dir / 'final_integration_test.json'
        },
        {
            'name': 'Advanced RL Agent',
            'command': 'python utils/advanced_rl_agent.py',
            'log_file': log_dir / 'advanced_rl_agent.json'
        },
        {
            'name': 'Multi-Step Prediction',
            'command': 'python utils/multi_step_prediction_fixed.py',
            'log_file': log_dir / 'multi_step_prediction.json'
        },
        {
            'name': 'Market Regime Detector',
            'command': 'python utils/market_regime_detector.py',
            'log_file': log_dir / 'market_regime_detector.json'
        },
        {
            'name': 'Federated Learning System',
            'command': 'python utils/federated_learning_system.py',
            'log_file': log_dir / 'federated_learning_system.json'
        },
        {
            'name': 'Anomaly Detection System',
            'command': 'python utils/anomaly_detection_system.py',
            'log_file': log_dir / 'anomaly_detection_system.json'
        },
        {
            'name': 'Genetic Strategy Evolution',
            'command': 'python utils/genetic_strategy_evolution.py',
            'log_file': log_dir / 'genetic_strategy_evolution.json'
        },
        {
            'name': 'Intelligent Memory System',
            'command': 'python utils/intelligent_memory_system.py',
            'log_file': log_dir / 'intelligent_memory_system.json'
        }
    ]
    
    # اجرای تست‌ها
    results = []
    total_start_time = time.time()
    
    for i, test in enumerate(tests, 1):
        print(f"\n📋 Test {i}/{len(tests)}: {test['name']}")
        print("-" * 50)
        
        result = run_command_and_log(test['command'], test['log_file'])
        result['test_name'] = test['name']
        results.append(result)
        
        # نمایش خلاصه
        if result['success']:
            print(f"✅ {test['name']}: PASSED")
        else:
            print(f"❌ {test['name']}: FAILED")
            if result['stderr']:
                print(f"   Error: {result['stderr'][:200]}...")
    
    total_end_time = time.time()
    total_execution_time = total_end_time - total_start_time
    
    # تحلیل نتایج
    successful_tests = sum(1 for r in results if r['success'])
    failed_tests = len(results) - successful_tests
    
    # ذخیره خلاصه نتایج
    summary = {
        'timestamp': datetime.now().isoformat(),
        'total_tests': len(results),
        'successful_tests': successful_tests,
        'failed_tests': failed_tests,
        'success_rate': successful_tests / len(results) * 100,
        'total_execution_time': total_execution_time,
        'individual_results': results
    }
    
    summary_file = log_dir / 'test_summary.json'
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    # گزارش نهایی
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏱️ Total Execution Time: {total_execution_time:.2f} seconds")
    print(f"📈 Success Rate: {successful_tests}/{len(results)} ({successful_tests/len(results)*100:.1f}%)")
    
    print(f"\n🎯 DETAILED RESULTS:")
    for result in results:
        status = "✅ PASSED" if result['success'] else "❌ FAILED"
        time_str = f"{result['execution_time']:.2f}s"
        print(f"   {status} - {result['test_name']} ({time_str})")
    
    print(f"\n📁 Log files saved in: {log_dir.absolute()}")
    print(f"📄 Summary file: {summary_file.absolute()}")
    
    # نمایش خطاها
    if failed_tests > 0:
        print(f"\n⚠️ FAILED TESTS DETAILS:")
        for result in results:
            if not result['success']:
                print(f"   ❌ {result['test_name']}:")
                if result['stderr']:
                    print(f"      Error: {result['stderr'][:300]}...")
                print(f"      Return code: {result['return_code']}")
    
    print(f"\n🏆 FINAL VERDICT:")
    if successful_tests == len(results):
        print("   ✅ ALL TESTS PASSED - SYSTEM READY FOR PRODUCTION!")
    elif successful_tests >= len(results) * 0.8:
        print("   ⚠️ MOST TESTS PASSED - SYSTEM MOSTLY READY")
    else:
        print("   ❌ MULTIPLE FAILURES - SYSTEM NEEDS ATTENTION")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 