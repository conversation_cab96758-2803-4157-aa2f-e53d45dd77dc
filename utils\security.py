class SecureStorage:
    """
    کلاس نمونه برای ذخیره‌سازی امن داده‌ها (نمونه‌سازی، بدون رمزنگاری واقعی).
    """
    def __init__(self) -> None:
        """ایجاد نمونه SecureStorage."""
        pass

    def encrypt(self, data: str) -> str:
        """
        رمزنگاری داده (در این نسخه فقط بازگرداندن داده).
        Args:
            data: داده ورودی
        Returns:
            str: داده رمزنگاری‌شده (در اینجا همان داده)
        """
        return data  # بدون رمزنگاری، فقط داده رو برگردون

    def decrypt(self, encrypted_data: str) -> str:
        """
        رمزگشایی داده (در این نسخه فقط بازگرداندن داده).
        Args:
            encrypted_data: داده رمزنگاری‌شده
        Returns:
            str: داده رمزگشایی‌شده (در اینجا همان داده)
        """
        return encrypted_data  # بدون رمزگشایی، فقط داده رو برگردون
