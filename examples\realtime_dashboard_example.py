#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
مثال کاربردی Real-time Dashboard System
نمونه استفاده از سیستم داشبورد زمان واقعی

این مثال شامل:
- راه‌اندازی داشبورد
- شبیه‌سازی داده‌های زمان واقعی
- مدیریت هشدارها
- تولید گزارش‌ها
- تست WebSocket
"""

import asyncio
import json
import requests
import websockets
import threading
import time
from datetime import datetime, timedelta
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.realtime_dashboard import RealTimeDashboard, AlertSeverity
import uvicorn


class DashboardDemo:
    """نمایش عملکرد داشبورد"""
    
    def __init__(self):
        self.dashboard = RealTimeDashboard()
        self.base_url = "http://localhost:8000"
        self.ws_url = "ws://localhost:8000"
        self.running = False
        self.server_thread = None
    
    def start_server(self):
        """راه‌اندازی سرور داشبورد"""
        print("🚀 Starting Real-time Dashboard Server...")
        
        def run_server():
            uvicorn.run(
                self.dashboard.app,
                host="0.0.0.0",
                port=8000,
                log_level="info"
            )
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        
        # Wait for server to start
        time.sleep(3)
        
        try:
            response = requests.get(f"{self.base_url}/api/health", timeout=5)
            if response.status_code == 200:
                print("✅ Dashboard Server Started Successfully!")
                print(f"📊 Dashboard URL: {self.base_url}")
                print(f"🔗 WebSocket URL: {self.ws_url}")
                return True
            else:
                print("❌ Server health check failed")
                return False
        except Exception as e:
            print(f"❌ Failed to connect to server: {e}")
            return False
    
    def demonstrate_api_endpoints(self):
        """نمایش endpoints API"""
        print("\n" + "="*50)
        print("🔍 API Endpoints Demonstration")
        print("="*50)
        
        try:
            # Health check
            print("\n1. Health Check:")
            response = requests.get(f"{self.base_url}/api/health")
            if response.status_code == 200:
                health_data = response.json()
                print(f"   Status: {health_data['status']}")
                print(f"   Connected Clients: {health_data['connected_clients']}")
                print(f"   Active Alerts: {health_data['active_alerts']}")
            
            # Get current data
            print("\n2. Current Data:")
            response = requests.get(f"{self.base_url}/api/data")
            if response.status_code == 200:
                data = response.json()
                print(f"   Portfolio Value: ${data['portfolio']['value']:,.2f}")
                print(f"   Daily Return: {data['portfolio']['daily_return']:.2%}")
                print(f"   Current Drawdown: {data['drawdown']['current']:.2%}")
                print(f"   Alpha: {data['alpha_beta']['alpha']:.3f}")
                print(f"   Beta: {data['alpha_beta']['beta']:.3f}")
                print(f"   Market Sentiment: {data['sentiment']['overall_sentiment']}")
            
            # Get configuration
            print("\n3. Configuration:")
            response = requests.get(f"{self.base_url}/api/config")
            if response.status_code == 200:
                config = response.json()
                print(f"   Theme: {config['theme']}")
                print(f"   Refresh Interval: {config['refresh_interval']}ms")
                print(f"   Max Data Points: {config['max_data_points']}")
                print(f"   Notifications: {config['enable_notifications']}")
            
            # Update configuration
            print("\n4. Update Configuration:")
            new_config = {
                "refresh_interval": 2000,
                "enable_notifications": True,
                "show_performance_chart": True
            }
            response = requests.post(f"{self.base_url}/api/config", json=new_config)
            if response.status_code == 200:
                result = response.json()
                print(f"   Config Update: {result['status']}")
            
            # Get alerts
            print("\n5. Alerts:")
            response = requests.get(f"{self.base_url}/api/alerts")
            if response.status_code == 200:
                alerts = response.json()
                print(f"   Total Alerts: {len(alerts)}")
                for alert in alerts[-3:]:  # Show last 3 alerts
                    print(f"   - {alert['title']}: {alert['severity']}")
            
            # Generate daily report
            print("\n6. Daily Report:")
            response = requests.get(f"{self.base_url}/api/reports/daily")
            if response.status_code == 200:
                report = response.json()
                if 'error' not in report:
                    print(f"   Date: {report['date']}")
                    print(f"   Daily Return: {report['summary']['daily_return']:.2%}")
                    print(f"   Portfolio Value: ${report['summary']['portfolio_value']:,.2f}")
                    print(f"   Total Trades: {report['summary']['total_trades']}")
                else:
                    print(f"   Report Error: {report['error']}")
            
        except Exception as e:
            print(f"❌ API demonstration failed: {e}")
    
    async def demonstrate_websocket(self):
        """نمایش WebSocket"""
        print("\n" + "="*50)
        print("🔄 WebSocket Real-time Updates")
        print("="*50)
        
        try:
            client_id = "demo_client"
            uri = f"{self.ws_url}/ws/{client_id}"
            
            async with websockets.connect(uri) as websocket:
                print(f"✅ Connected to WebSocket: {client_id}")
                
                # Listen for updates
                update_count = 0
                start_time = time.time()
                
                while update_count < 10 and time.time() - start_time < 30:  # 10 updates or 30 seconds
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        data = json.loads(message)
                        
                        if data.get('type') == 'data_update':
                            update_count += 1
                            portfolio_data = data['data']['portfolio']
                            drawdown_data = data['data']['drawdown']
                            
                            print(f"\n📊 Update #{update_count}:")
                            print(f"   Portfolio: ${portfolio_data['value']:,.2f} ({portfolio_data['daily_return']:+.2%})")
                            print(f"   Drawdown: {drawdown_data['current']:.2%} (Max: {drawdown_data['max']:.2%})")
                            print(f"   Timestamp: {data['data']['timestamp']}")
                            
                            # Send config update
                            if update_count == 3:
                                config_message = {
                                    'type': 'config_update',
                                    'config': {
                                        'theme': 'dark',
                                        'refresh_interval': 1500
                                    }
                                }
                                await websocket.send(json.dumps(config_message))
                                print("📝 Sent config update")
                        
                    except asyncio.TimeoutError:
                        print("⏰ WebSocket timeout - no updates received")
                        break
                
                print(f"\n✅ WebSocket demonstration completed ({update_count} updates)")
                
        except Exception as e:
            print(f"❌ WebSocket demonstration failed: {e}")
    
    def demonstrate_alert_system(self):
        """نمایش سیستم هشدار"""
        print("\n" + "="*50)
        print("🚨 Alert System Demonstration")
        print("="*50)
        
        try:
            # Add custom alerts
            print("\n1. Adding Custom Alerts:")
            
            # Add alerts directly to aggregator
            alert1 = self.dashboard.data_aggregator.add_alert(
                "Portfolio Performance Alert",
                "Daily return exceeded -3% threshold",
                AlertSeverity.HIGH,
                "Performance Monitor"
            )
            print(f"   ✅ Added HIGH alert: {alert1.id}")
            
            alert2 = self.dashboard.data_aggregator.add_alert(
                "System Latency Alert",
                "HFT system latency above 5ms",
                AlertSeverity.MEDIUM,
                "HFT Monitor"
            )
            print(f"   ✅ Added MEDIUM alert: {alert2.id}")
            
            alert3 = self.dashboard.data_aggregator.add_alert(
                "Market Sentiment Alert",
                "Negative sentiment detected in news analysis",
                AlertSeverity.LOW,
                "Sentiment Analyzer"
            )
            print(f"   ✅ Added LOW alert: {alert3.id}")
            
            # Get alerts via API
            print("\n2. Retrieving Alerts via API:")
            response = requests.get(f"{self.base_url}/api/alerts")
            if response.status_code == 200:
                alerts = response.json()
                print(f"   Total alerts: {len(alerts)}")
                
                for alert in alerts[-3:]:
                    print(f"   - {alert['title']}")
                    print(f"     Severity: {alert['severity']}")
                    print(f"     Source: {alert['source']}")
                    print(f"     Acknowledged: {alert['acknowledged']}")
            
            # Acknowledge an alert
            print("\n3. Acknowledging Alert:")
            response = requests.post(f"{self.base_url}/api/alerts/{alert1.id}/acknowledge")
            if response.status_code == 200:
                print(f"   ✅ Alert {alert1.id} acknowledged")
            
            # Verify acknowledgment
            print("\n4. Verifying Acknowledgment:")
            response = requests.get(f"{self.base_url}/api/alerts")
            if response.status_code == 200:
                alerts = response.json()
                for alert in alerts:
                    if alert['id'] == alert1.id:
                        print(f"   Alert {alert1.id} acknowledged: {alert['acknowledged']}")
                        break
            
        except Exception as e:
            print(f"❌ Alert system demonstration failed: {e}")
    
    def demonstrate_data_flow(self):
        """نمایش جریان داده‌ها"""
        print("\n" + "="*50)
        print("📈 Data Flow Demonstration")
        print("="*50)
        
        try:
            print("\n1. Portfolio Data Evolution:")
            
            # Collect multiple data points
            for i in range(5):
                response = requests.get(f"{self.base_url}/api/data")
                if response.status_code == 200:
                    data = response.json()
                    
                    print(f"   Sample {i+1}:")
                    print(f"     Portfolio Value: ${data['portfolio']['value']:,.2f}")
                    print(f"     Daily Return: {data['portfolio']['daily_return']:+.2%}")
                    print(f"     Volatility: {data['portfolio']['volatility']:.2%}")
                    print(f"     Sharpe Ratio: {data['portfolio']['sharpe_ratio']:.3f}")
                    print(f"     Alpha: {data['alpha_beta']['alpha']:+.3f}")
                    print(f"     Beta: {data['alpha_beta']['beta']:.3f}")
                    
                    # HFT Metrics
                    hft = data['hft']
                    print(f"     HFT Latency: {hft['latency_ms']:.1f}ms")
                    print(f"     Fill Rate: {hft['order_fill_rate']:.1%}")
                    print(f"     Active Orders: {hft['active_orders']}")
                    
                    # Exchange Routing
                    routing = data['routing']
                    print(f"     Active Exchanges: {routing['active_exchanges']}")
                    print(f"     Execution Rate: {routing['best_execution_rate']:.1%}")
                    print(f"     Arbitrage Opportunities: {routing['arbitrage_opportunities']}")
                    
                    # Sentiment
                    sentiment = data['sentiment']
                    print(f"     Market Sentiment: {sentiment['overall_sentiment']}")
                    print(f"     Sentiment Score: {sentiment['average_score']:.2f}")
                    
                    print()
                
                time.sleep(2)  # Wait 2 seconds between samples
            
        except Exception as e:
            print(f"❌ Data flow demonstration failed: {e}")
    
    def demonstrate_reports(self):
        """نمایش گزارش‌ها"""
        print("\n" + "="*50)
        print("📄 Reports Demonstration")
        print("="*50)
        
        try:
            # Generate and display daily report
            print("\n1. Daily Report:")
            response = requests.get(f"{self.base_url}/api/reports/daily")
            if response.status_code == 200:
                report = response.json()
                
                if 'error' not in report:
                    print(f"   Date: {report['date']}")
                    print(f"   Summary:")
                    summary = report['summary']
                    print(f"     Daily Return: {summary['daily_return']:+.2%}")
                    print(f"     Daily Volatility: {summary['daily_volatility']:.2%}")
                    print(f"     Total Trades: {summary['total_trades']}")
                    print(f"     Portfolio Value: ${summary['portfolio_value']:,.2f}")
                    print(f"     Alerts Count: {summary['alerts_count']}")
                    
                    print(f"   Performance:")
                    performance = report['performance']
                    print(f"     Best Return: {performance['best_return']:+.2%}")
                    print(f"     Worst Return: {performance['worst_return']:+.2%}")
                    print(f"     Average Return: {performance['avg_return']:+.2%}")
                    
                    print(f"   Risk Metrics:")
                    risk = report['risk_metrics']
                    print(f"     Volatility: {risk['volatility']:.2%}")
                    print(f"     Max Drawdown: {risk['max_drawdown']:.2%}")
                    print(f"     VaR 95%: {risk['var_95']:+.2%}")
                else:
                    print(f"   Report Error: {report['error']}")
            
            # Weekly and Monthly reports
            print("\n2. Weekly Report:")
            response = requests.get(f"{self.base_url}/api/reports/weekly")
            if response.status_code == 200:
                report = response.json()
                print(f"   Status: {report.get('status', 'Generated')}")
            
            print("\n3. Monthly Report:")
            response = requests.get(f"{self.base_url}/api/reports/monthly")
            if response.status_code == 200:
                report = response.json()
                print(f"   Status: {report.get('status', 'Generated')}")
            
        except Exception as e:
            print(f"❌ Reports demonstration failed: {e}")
    
    def demonstrate_dashboard_features(self):
        """نمایش ویژگی‌های داشبورد"""
        print("\n" + "="*50)
        print("🎛️ Dashboard Features Overview")
        print("="*50)
        
        print("\n📊 Available Features:")
        print("   ✅ Real-time Portfolio Monitoring")
        print("   ✅ WebSocket Live Updates")
        print("   ✅ Alpha/Beta Attribution Analysis")
        print("   ✅ Drawdown Control & Monitoring")
        print("   ✅ Multi-language Sentiment Analysis")
        print("   ✅ HFT Performance Metrics")
        print("   ✅ Multi-exchange Routing Status")
        print("   ✅ Intelligent Alert System")
        print("   ✅ Automated Report Generation")
        print("   ✅ Responsive Web Interface")
        print("   ✅ RESTful API")
        print("   ✅ Configurable Themes & Settings")
        
        print("\n🔧 Technical Specifications:")
        print("   - FastAPI Backend")
        print("   - WebSocket Real-time Communication")
        print("   - Chart.js Visualization")
        print("   - Tailwind CSS Styling")
        print("   - Responsive Mobile Design")
        print("   - JSON API Endpoints")
        print("   - Configurable Alert Thresholds")
        print("   - Automated Background Tasks")
        
        print("\n🚀 Performance Metrics:")
        print("   - Sub-second Data Updates")
        print("   - Multiple Concurrent Connections")
        print("   - Efficient Memory Management")
        print("   - Scalable Architecture")
        print("   - Error Handling & Recovery")
        
        print(f"\n🌐 Access URLs:")
        print(f"   Dashboard: {self.base_url}")
        print(f"   API Health: {self.base_url}/api/health")
        print(f"   Live Data: {self.base_url}/api/data")
        print(f"   Configuration: {self.base_url}/api/config")
        print(f"   Alerts: {self.base_url}/api/alerts")
        print(f"   Reports: {self.base_url}/api/reports/daily")
    
    async def run_complete_demo(self):
        """اجرای نمایش کامل"""
        print("🎯 Real-time Trading Dashboard - Complete Demonstration")
        print("="*60)
        
        # Start server
        if not self.start_server():
            print("❌ Failed to start server. Exiting...")
            return
        
        try:
            # Demonstrate features
            self.demonstrate_dashboard_features()
            
            # API endpoints
            self.demonstrate_api_endpoints()
            
            # Alert system
            self.demonstrate_alert_system()
            
            # Data flow
            self.demonstrate_data_flow()
            
            # Reports
            self.demonstrate_reports()
            
            # WebSocket (async)
            await self.demonstrate_websocket()
            
            print("\n" + "="*60)
            print("✅ Complete Dashboard Demonstration Finished!")
            print("🌐 Dashboard is still running at: http://localhost:8000")
            print("💡 Open the URL in your browser to see the live dashboard")
            print("🔄 WebSocket updates will continue in real-time")
            print("📊 All 7 trading system capabilities are integrated")
            print("="*60)
            
        except Exception as e:
            print(f"❌ Demo failed: {e}")
    
    def run_browser_demo(self):
        """اجرای نمایش مرورگر"""
        print("\n" + "="*50)
        print("🌐 Browser Dashboard Demo")
        print("="*50)
        
        if not self.start_server():
            print("❌ Failed to start server")
            return
        
        print(f"\n✅ Dashboard is running at: {self.base_url}")
        print("\n📋 Instructions:")
        print("   1. Open your web browser")
        print(f"   2. Navigate to: {self.base_url}")
        print("   3. Watch real-time updates")
        print("   4. Observe live charts and metrics")
        print("   5. Check alerts panel")
        print("   6. Monitor all 7 system capabilities")
        
        print("\n🎯 What you'll see:")
        print("   - Live portfolio value updates")
        print("   - Real-time drawdown monitoring")
        print("   - Alpha/Beta attribution metrics")
        print("   - Market sentiment analysis")
        print("   - HFT performance indicators")
        print("   - Exchange routing status")
        print("   - Dynamic alert notifications")
        print("   - Interactive charts and graphs")
        
        print("\n⏰ Dashboard will run indefinitely...")
        print("   Press Ctrl+C to stop")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n🛑 Dashboard stopped by user")


def main():
    """تابع اصلی"""
    demo = DashboardDemo()
    
    print("🎯 Real-time Trading Dashboard Demo")
    print("Choose demo mode:")
    print("1. Complete API Demo (automated)")
    print("2. Browser Demo (manual)")
    print("3. Both")
    
    choice = input("\nEnter your choice (1/2/3): ").strip()
    
    if choice == "1":
        asyncio.run(demo.run_complete_demo())
    elif choice == "2":
        demo.run_browser_demo()
    elif choice == "3":
        # Run complete demo first
        asyncio.run(demo.run_complete_demo())
        
        # Then keep server running for browser
        print("\n" + "="*50)
        print("🌐 Switching to Browser Mode...")
        print("="*50)
        
        demo.run_browser_demo()
    else:
        print("❌ Invalid choice. Running complete demo...")
        asyncio.run(demo.run_complete_demo())


if __name__ == "__main__":
    main() 