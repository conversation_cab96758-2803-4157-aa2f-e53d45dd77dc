import pytest
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import sys
import json
from unittest.mock import MagicMock, patch
import sklearn.linear_model
from utils.auto_feature_engineering import AutoFeatureEngineering

# اضافه کردن مسیر پروژه به sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.explainable_ai import ExplainableAI


class MockModel:
    """مدل مصنوعی برای تست"""
    
    def __init__(self, action_size=1):
        self.action_size = action_size
        self.policy = MagicMock()
        
    def predict(self, observation, deterministic=True):
        """شبیه‌سازی پیش‌بینی مدل"""
        if len(observation.shape) == 1:
            observation = observation.reshape(1, -1)
        
        # تولید اقدام تصادفی بین -1 و 1
        actions = np.random.uniform(-1, 1, (observation.shape[0], self.action_size))
        if self.action_size == 1:
            actions = actions.flatten()
        
        # شبیه‌سازی _states
        states = None
        
        return actions, states


class TestExplainableAI:
    
    @pytest.fixture
    def explainable_ai(self):
        """فیکسچر برای ایجاد نمونه ExplainableAI"""
        model = MockModel()
        feature_names = ["feature_1", "feature_2", "feature_3", "feature_4", "feature_5"]
        return ExplainableAI(model=model, feature_names=feature_names)
    
    @pytest.fixture
    def sample_observations(self):
        """فیکسچر برای ایجاد مشاهدات نمونه"""
        # 100 مشاهده با 5 ویژگی
        return np.random.randn(100, 5)
    
    @pytest.fixture
    def sample_actions(self):
        """فیکسچر برای ایجاد اقدامات نمونه"""
        # ایجاد اقدامات نمونه
        return np.random.uniform(-1, 1, 100)
    
    @pytest.fixture
    def sample_returns(self):
        """فیکسچر برای ایجاد بازده‌های نمونه"""
        # ایجاد بازده‌های نمونه
        return np.random.randn(100)
    
    @pytest.fixture
    def sample_market_data(self):
        """فیکسچر برای ایجاد داده‌های بازار نمونه"""
        # ایجاد داده‌های بازار نمونه
        dates = pd.date_range(start='2020-01-01', periods=100)
        data = {
            'open': np.random.randn(100) * 10 + 100,
            'high': np.random.randn(100) * 10 + 105,
            'low': np.random.randn(100) * 10 + 95,
            'close': np.random.randn(100) * 10 + 100,
            'volume': np.random.randint(1000, 10000, 100)
        }
        return pd.DataFrame(data, index=dates)
    
    @pytest.fixture
    def sample_time_data(self):
        """فیکسچر برای ایجاد داده‌های زمانی نمونه"""
        # ایجاد داده‌های زمانی برای تست نقشه حرارتی
        return pd.date_range(start='2020-01-01', periods=100)
    
    def test_feature_importance(self, explainable_ai, sample_observations):
        """تست محاسبه اهمیت ویژگی‌ها"""
        importances = explainable_ai.feature_importance(sample_observations, method='permutation', n_samples=5)
        
        # بررسی خروجی
        assert isinstance(importances, dict)
        assert len(importances) == 5  # تعداد ویژگی‌ها
        assert all(0 <= v <= 1 for v in importances.values())  # مقادیر نرمال‌شده
        assert abs(sum(importances.values()) - 1.0) < 1e-6  # جمع مقادیر باید 1 باشد
    
    def test_decision_explanation(self, explainable_ai, sample_observations):
        """تست توضیح تصمیم"""
        explanation = explainable_ai.decision_explanation(sample_observations[0])
        
        # بررسی خروجی
        assert isinstance(explanation, dict)
        assert 'action' in explanation
        assert 'top_features' in explanation
        assert 'explanation' in explanation
        assert len(explanation['top_features']) <= 3  # حداکثر 3 ویژگی مهم
    
    def test_visualize_feature_importance(self, explainable_ai, sample_observations, tmp_path):
        """تست نمایش اهمیت ویژگی‌ها"""
        # ابتدا اهمیت ویژگی‌ها را محاسبه می‌کنیم
        explainable_ai.feature_importance(sample_observations, method='permutation', n_samples=5)
        
        # تست نمایش اهمیت ویژگی‌ها
        save_path = os.path.join(tmp_path, "importance.png")
        fig = explainable_ai.visualize_feature_importance(save_path=save_path)
        
        # بررسی خروجی
        assert fig is None  # وقتی save_path داده شود، خروجی باید None باشد
        assert os.path.exists(save_path)  # فایل باید ایجاد شده باشد
    
    def test_visualize_decision_process(self, explainable_ai, sample_observations, tmp_path):
        """تست نمایش فرآیند تصمیم‌گیری"""
        save_path = os.path.join(tmp_path, "decision_process.png")
        fig = explainable_ai.visualize_decision_process(sample_observations[0], save_path=save_path)
        
        # بررسی خروجی
        assert fig is None  # وقتی save_path داده شود، خروجی باید None باشد
        assert os.path.exists(save_path)  # فایل باید ایجاد شده باشد
    
    def test_compare_decisions(self, explainable_ai, sample_observations, tmp_path):
        """تست مقایسه تصمیمات"""
        save_path = os.path.join(tmp_path, "compare_decisions.png")
        fig = explainable_ai.compare_decisions(sample_observations[:5], save_path=save_path)
        
        # بررسی خروجی
        assert fig is None  # وقتی save_path داده شود، خروجی باید None باشد
        assert os.path.exists(save_path)  # فایل باید ایجاد شده باشد
    
    def test_generate_explanation_report(self, explainable_ai, sample_observations, tmp_path):
        """تست ایجاد گزارش توضیحات"""
        save_path = os.path.join(tmp_path, "report.json")
        report = explainable_ai.generate_explanation_report(sample_observations[:10], save_path=save_path)
        
        # بررسی خروجی
        assert report is None  # وقتی save_path داده شود، خروجی باید None باشد
        assert os.path.exists(save_path)  # فایل باید ایجاد شده باشد
        
        # بررسی محتوای فایل
        with open(save_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        assert 'overall_feature_importance' in data
        assert 'top_features' in data
        assert 'sample_decisions' in data
    
    def test_analyze_market_regime(self, explainable_ai, sample_observations, sample_actions, sample_market_data, tmp_path):
        """تست تحلیل رژیم‌های بازار"""
        save_path = os.path.join(tmp_path, "market_regime.png")
        report = explainable_ai.analyze_market_regime(
            sample_observations, 
            actions=sample_actions, 
            market_data=sample_market_data, 
            n_regimes=3, 
            save_path=save_path
        )
        
        # بررسی خروجی
        assert isinstance(report, dict)
        assert 'regime_names' in report
        assert 'regime_importances' in report
        assert 'regime_decisions' in report
        assert os.path.exists(save_path)  # فایل باید ایجاد شده باشد
    
    def test_analyze_decision_risk(self, explainable_ai, sample_observations, sample_actions, sample_returns, tmp_path):
        """تست تحلیل ریسک تصمیمات"""
        save_path = os.path.join(tmp_path, "decision_risk.png")
        report = explainable_ai.analyze_decision_risk(
            sample_observations, 
            actions=sample_actions, 
            returns=sample_returns, 
            save_path=save_path
        )
        
        # بررسی خروجی
        assert isinstance(report, dict)
        assert 'overall_risk_metrics' in report
        assert 'action_risk_analysis' in report
        assert 'var_95' in report['overall_risk_metrics']
        assert 'es_95' in report['overall_risk_metrics']
        assert 'sharpe_ratio' in report['overall_risk_metrics']
        assert 'max_drawdown' in report['overall_risk_metrics']
        assert os.path.exists(save_path)  # فایل باید ایجاد شده باشد
    
    def test_identify_critical_decision_points(self, explainable_ai, sample_observations, sample_actions, sample_returns, tmp_path):
        """تست شناسایی نقاط تصمیم‌گیری بحرانی"""
        save_path = os.path.join(tmp_path, "critical_points.png")
        report = explainable_ai.identify_critical_decision_points(
            sample_observations, 
            actions=sample_actions, 
            returns=sample_returns, 
            n_critical=3, 
            save_path=save_path
        )
        
        # بررسی خروجی
        assert isinstance(report, dict)
        assert 'critical_points' in report
        assert 'critical_importances' in report
        assert len(report['critical_points']) >= 3  # حداقل 3 نقطه بحرانی
        assert os.path.exists(save_path)  # فایل باید ایجاد شده باشد 
        
    def test_create_decision_heatmap(self, explainable_ai, sample_observations, sample_actions, sample_time_data, tmp_path):
        """تست ایجاد نقشه حرارتی تصمیمات"""
        save_path = os.path.join(tmp_path, "decision_heatmap.png")
        report = explainable_ai.create_decision_heatmap(
            sample_observations,
            actions=sample_actions,
            timestamps=sample_time_data,
            feature_idx=[0, 1],  # تست با دو ویژگی اول
            save_path=save_path
        )
        
        # بررسی خروجی
        assert isinstance(report, dict)
        assert 'heatmap_data' in report
        assert 'feature_correlations' in report
        assert 'time_patterns' in report
        assert os.path.exists(save_path)  # فایل باید ایجاد شده باشد
    
    def test_counterfactual_analysis(self, explainable_ai, sample_observations, tmp_path):
        """تست تحلیل کانتر-فکتوال"""
        observation = sample_observations[0]
        save_path = os.path.join(tmp_path, "counterfactual.png")
        
        report = explainable_ai.counterfactual_analysis(
            observation,
            target_action=0.5,  # هدف اقدام مشخص
            feature_ranges={0: (-2, 2), 1: (-2, 2)},  # محدوده تغییرات برای ویژگی‌های 0 و 1
            n_samples=10,
            save_path=save_path
        )
        
        # بررسی خروجی
        assert isinstance(report, dict)
        assert 'counterfactuals' in report
        assert 'original_action' in report
        assert 'feature_changes' in report
        assert 'minimal_counterfactual' in report
        assert os.path.exists(save_path)  # فایل باید ایجاد شده باشد
    
    def test_sensitivity_analysis(self, explainable_ai, sample_observations, tmp_path):
        """تست تحلیل حساسیت چند بعدی"""
        observation = sample_observations[0]
        save_path = os.path.join(tmp_path, "sensitivity.png")
        
        report = explainable_ai.sensitivity_analysis(
            observation,
            feature_ranges={0: (-2, 2), 1: (-2, 2), 2: (-2, 2)},
            n_samples=5,
            save_path=save_path
        )
        
        # بررسی خروجی
        assert isinstance(report, dict)
        assert 'sensitivity_scores' in report
        assert 'interaction_effects' in report
        assert 'feature_boundaries' in report
        assert os.path.exists(save_path)  # فایل باید ایجاد شده باشد
    
    def test_detect_bias(self, explainable_ai, sample_observations, sample_actions, tmp_path):
        """تست تشخیص و توضیح سوگیری‌های مدل"""
        save_path = os.path.join(tmp_path, "bias_detection.png")
        
        # تعریف گروه‌های مختلف برای بررسی سوگیری
        # در اینجا به عنوان مثال، مشاهدات را بر اساس مقدار ویژگی اول به دو گروه تقسیم می‌کنیم
        group_indices = {
            'group_1': np.where(sample_observations[:, 0] > 0)[0],
            'group_2': np.where(sample_observations[:, 0] <= 0)[0]
        }
        
        report = explainable_ai.detect_bias(
            sample_observations,
            actions=sample_actions,
            group_indices=group_indices,
            save_path=save_path
        )
        
        # بررسی خروجی
        assert isinstance(report, dict)
        assert 'bias_metrics' in report
        assert 'group_comparisons' in report
        assert 'feature_bias' in report
        assert os.path.exists(save_path)  # فایل باید ایجاد شده باشد
    
    def test_advanced_pnl_attribution(self, explainable_ai, sample_observations, sample_actions, sample_returns, tmp_path):
        """تست تحلیل پیشرفته سود و زیان"""
        save_path = os.path.join(tmp_path, "advanced_pnl.png")
        # فرض: هر اقدام منجر به یک بازده می‌شود
        report = explainable_ai.advanced_pnl_attribution(
            sample_observations,
            actions=sample_actions,
            returns=sample_returns,
            save_path=save_path
        )
        # بررسی ساختار خروجی
        assert isinstance(report, dict)
        assert 'pnl_breakdown' in report
        assert 'feature_attribution' in report
        assert 'extreme_trades' in report
        assert os.path.exists(save_path)

    def test_stress_scenario_analysis(self, explainable_ai, sample_observations, sample_actions, sample_returns, tmp_path):
        """تست تحلیل سناریوهای استرس"""
        save_path = os.path.join(tmp_path, "stress_scenario.png")
        # سناریوهای استرس: شوک مثبت و منفی به ویژگی اول
        scenarios = [
            {'name': 'positive_shock', 'feature_idx': 0, 'shock': 2.0},
            {'name': 'negative_shock', 'feature_idx': 0, 'shock': -2.0}
        ]
        report = explainable_ai.stress_scenario_analysis(
            sample_observations,
            actions=sample_actions,
            returns=sample_returns,
            scenarios=scenarios,
            save_path=save_path
        )
        assert isinstance(report, dict)
        assert 'scenario_results' in report
        assert 'overall_impact' in report
        assert os.path.exists(save_path)

    def test_decision_memory_analysis(self, explainable_ai, sample_observations, sample_actions, tmp_path):
        """تست تحلیل حافظه تصمیمات"""
        save_path = os.path.join(tmp_path, "decision_memory.png")
        # فرض: اقدامات و مشاهدات متوالی
        report = explainable_ai.decision_memory_analysis(
            sample_observations,
            actions=sample_actions,
            window_size=10,
            save_path=save_path
        )
        assert isinstance(report, dict)
        assert 'memory_patterns' in report
        assert 'recurring_sequences' in report
        assert 'memory_score' in report
        assert os.path.exists(save_path)

    def test_behavioral_anomaly_detection(self, explainable_ai, sample_observations, sample_actions, tmp_path):
        """تست تشخیص و توضیح انحرافات رفتاری"""
        save_path = os.path.join(tmp_path, "behavioral_anomaly.png")
        # فرض: اقدامات مدل با نویز و چند نقطه پرت
        actions = sample_actions.copy()
        actions[0] = 10  # نقطه پرت
        actions[1] = -10 # نقطه پرت
        report = explainable_ai.behavioral_anomaly_detection(
            sample_observations,
            actions=actions,
            threshold=3.0,
            save_path=save_path
        )
        assert isinstance(report, dict)
        assert 'anomalies' in report
        assert 'anomaly_scores' in report
        assert 'explanation' in report
        assert os.path.exists(save_path)

    def test_generate_nlg_explanation(self, explainable_ai, sample_observations):
        """تست تولید توضیح زبانی هوشمند (NLG)"""
        text = explainable_ai.generate_nlg_explanation(sample_observations[0])
        assert isinstance(text, str)
        assert "مدل تصمیم گرفت" in text

    def test_visualize_decision_flow(self, explainable_ai, sample_observations, tmp_path):
        """تست نمودار جریان تصمیم"""
        save_path = os.path.join(tmp_path, "decision_flow.png")
        fig = explainable_ai.visualize_decision_flow(sample_observations[0], save_path=save_path)
        assert fig is None
        assert os.path.exists(save_path)

    def test_interactive_decision_space(self, explainable_ai, sample_observations, sample_actions, tmp_path):
        """تست نمایش تعاملی فضای تصمیم"""
        save_path = os.path.join(tmp_path, "decision_space.html")
        result = explainable_ai.interactive_decision_space(sample_observations, sample_actions, save_path=save_path)
        assert result is None
        assert os.path.exists(save_path)

    def test_expert_comparison(self, explainable_ai, sample_observations, sample_actions, tmp_path):
        """تست مقایسه تصمیمات با معامله‌گر خبره"""
        expert_actions = np.random.choice(sample_actions, size=sample_observations.shape[0])
        save_path = os.path.join(tmp_path, "expert_comparison.png")
        result = explainable_ai.expert_comparison(sample_observations, expert_actions, sample_actions, save_path=save_path)
        assert isinstance(result, dict)
        assert 'match_percent' in result
        assert os.path.exists(save_path)

    def test_news_impact_analysis(self, explainable_ai, sample_observations, sample_actions, tmp_path):
        """تست تحلیل اثر اخبار بر تصمیمات"""
        news_events = np.zeros(100)
        news_events[10] = 1
        news_events[50] = 1
        news_events[90] = 1
        save_path = os.path.join(tmp_path, "news_impact.png")
        result = explainable_ai.news_impact_analysis(sample_observations, sample_actions, news_events, save_path=save_path)
        assert isinstance(result, dict)
        assert 'stats' in result
        assert os.path.exists(save_path)

    def test_market_impact_analysis(self, explainable_ai, sample_observations, sample_actions, sample_market_data, tmp_path):
        """تست تحلیل تاثیر معاملات بر بازار"""
        save_path = os.path.join(tmp_path, "market_impact.png")
        result = explainable_ai.market_impact_analysis(sample_observations, sample_actions, sample_market_data, save_path=save_path)
        assert isinstance(result, dict)
        assert 'corr_price' in result
        assert 'corr_volume' in result
        assert os.path.exists(save_path)

    def test_decision_lag_analysis(self, explainable_ai, sample_observations, sample_actions, sample_time_data, tmp_path):
        """تست تحلیل تاخیر تصمیم"""
        save_path = os.path.join(tmp_path, "decision_lag.png")
        result = explainable_ai.decision_lag_analysis(sample_observations, sample_actions, sample_time_data, save_path=save_path)
        assert isinstance(result, dict)
        assert 'mean_lag' in result
        assert os.path.exists(save_path)

    def test_explanation_quality_assessment(self, explainable_ai, sample_observations, sample_actions, tmp_path):
        """تست ارزیابی کیفیت توضیحات"""
        explanations = [explainable_ai.generate_nlg_explanation(obs, act) for obs, act in zip(sample_observations, sample_actions)]
        save_path = os.path.join(tmp_path, "explanation_quality.png")
        result = explainable_ai.explanation_quality_assessment(sample_observations, sample_actions, explanations, save_path=save_path)
        assert isinstance(result, dict)
        assert 'simplicity' in result
        assert 'coverage' in result
        assert 'uniformity' in result
        assert os.path.exists(save_path)

    def test_auto_feature_engineering_basic(self):
        X = np.random.randn(100, 5)
        y = np.random.randint(0, 2, 100)
        afe = AutoFeatureEngineering(n_features=5)
        X_new = afe.fit_transform(X, y)
        assert X_new.shape[1] <= 5
        suggestions = afe.suggest_new_features(X, y)
        assert len(suggestions) > 0

    def test_auto_feature_engineering_active_learning(self):
        X = np.random.randn(100, 5)
        y = np.random.randint(0, 2, 100)
        import sklearn.linear_model
        model = sklearn.linear_model.LogisticRegression().fit(X, y)
        afe = AutoFeatureEngineering(n_features=5)
        idx = afe.select_samples_for_labeling(X, model, n_samples=10)
        assert len(idx) == 10 