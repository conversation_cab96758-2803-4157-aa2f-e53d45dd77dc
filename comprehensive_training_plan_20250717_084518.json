{"models": {"FinBERT": {"name": "FinBERT", "category": "sentiment", "type": "transformer", "trainer_class": "PearlSentimentTrainer", "config_class": "SentimentTrainingConfig", "evaluation_metrics": ["accuracy", "precision", "recall", "f1_score"], "training_time_estimate": 45, "memory_requirement": 2048, "dependencies": ["transformers", "torch"], "status": "not_trained"}, "CryptoBERT": {"name": "CryptoBERT", "category": "sentiment", "type": "transformer", "trainer_class": "PearlSentimentTrainer", "config_class": "SentimentTrainingConfig", "evaluation_metrics": ["accuracy", "precision", "recall", "f1_score"], "training_time_estimate": 40, "memory_requirement": 1800, "dependencies": ["transformers", "torch"], "status": "not_trained"}, "FinancialSentimentModel": {"name": "FinancialSentimentModel", "category": "sentiment", "type": "ensemble", "trainer_class": "PearlSentimentTrainer", "config_class": "SentimentTrainingConfig", "evaluation_metrics": ["accuracy", "precision", "recall", "f1_score", "ensemble_agreement"], "training_time_estimate": 60, "memory_requirement": 3000, "dependencies": ["transformers", "torch", "sklearn"], "status": "not_trained"}, "SentimentEnsemble": {"name": "SentimentEnsemble", "category": "sentiment", "type": "ensemble", "trainer_class": "PearlSentimentTrainer", "config_class": "SentimentTrainingConfig", "evaluation_metrics": ["accuracy", "precision", "recall", "f1_score", "ensemble_diversity"], "training_time_estimate": 90, "memory_requirement": 4000, "dependencies": ["transformers", "torch", "sklearn"], "status": "not_trained"}, "LSTM_TimeSeries": {"name": "LSTM_TimeSeries", "category": "timeseries", "type": "lstm", "trainer_class": "PearlTimeSeriesTrainer", "config_class": "TimeSeriesTrainingConfig", "evaluation_metrics": ["rmse", "mae", "mape", "directional_accuracy"], "training_time_estimate": 30, "memory_requirement": 1024, "dependencies": ["torch", "numpy", "pandas"], "status": "not_trained"}, "GRU_TimeSeries": {"name": "GRU_TimeSeries", "category": "timeseries", "type": "gru", "trainer_class": "PearlTimeSeriesTrainer", "config_class": "TimeSeriesTrainingConfig", "evaluation_metrics": ["rmse", "mae", "mape", "directional_accuracy"], "training_time_estimate": 25, "memory_requirement": 900, "dependencies": ["torch", "numpy", "pandas"], "status": "not_trained"}, "Transformer_TimeSeries": {"name": "Transformer_TimeSeries", "category": "timeseries", "type": "transformer", "trainer_class": "PearlTimeSeriesTrainer", "config_class": "TimeSeriesTrainingConfig", "evaluation_metrics": ["rmse", "mae", "mape", "directional_accuracy", "attention_weights"], "training_time_estimate": 50, "memory_requirement": 2048, "dependencies": ["torch", "numpy", "pandas"], "status": "not_trained"}, "ChronosModel": {"name": "ChronosModel", "category": "timeseries", "type": "foundation", "trainer_class": "PearlTimeSeriesTrainer", "config_class": "TimeSeriesTrainingConfig", "evaluation_metrics": ["rmse", "mae", "mape", "directional_accuracy"], "training_time_estimate": 20, "memory_requirement": 1500, "dependencies": ["chronos", "torch"], "status": "not_trained"}, "TimeSeriesEnsemble": {"name": "TimeSeriesEnsemble", "category": "timeseries", "type": "ensemble", "trainer_class": "PearlTimeSeriesTrainer", "config_class": "TimeSeriesTrainingConfig", "evaluation_metrics": ["rmse", "mae", "mape", "directional_accuracy", "ensemble_variance"], "training_time_estimate": 80, "memory_requirement": 3500, "dependencies": ["torch", "numpy", "pandas", "sklearn"], "status": "not_trained"}, "DQN_Agent": {"name": "DQN_Agent", "category": "reinforcement_learning", "type": "dqn", "trainer_class": "PearlRLTrainer", "config_class": "RLTrainingConfig", "evaluation_metrics": ["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"], "training_time_estimate": 60, "memory_requirement": 1200, "dependencies": ["torch", "gym", "numpy"], "status": "not_trained"}, "A2C_Agent": {"name": "A2C_Agent", "category": "reinforcement_learning", "type": "a2c", "trainer_class": "PearlRLTrainer", "config_class": "RLTrainingConfig", "evaluation_metrics": ["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"], "training_time_estimate": 45, "memory_requirement": 1000, "dependencies": ["torch", "gym", "numpy"], "status": "not_trained"}, "PPO_Agent": {"name": "PPO_Agent", "category": "reinforcement_learning", "type": "ppo", "trainer_class": "PearlRLTrainer", "config_class": "RLTrainingConfig", "evaluation_metrics": ["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"], "training_time_estimate": 55, "memory_requirement": 1100, "dependencies": ["torch", "gym", "numpy"], "status": "not_trained"}, "TD3_Agent": {"name": "TD3_Agent", "category": "reinforcement_learning", "type": "td3", "trainer_class": "PearlRLTrainer", "config_class": "RLTrainingConfig", "evaluation_metrics": ["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"], "training_time_estimate": 70, "memory_requirement": 1300, "dependencies": ["torch", "gym", "numpy"], "status": "not_trained"}, "HierarchicalRL": {"name": "HierarchicalRL", "category": "advanced_rl", "type": "hierarchical", "trainer_class": "HierarchicalRLTrainer", "config_class": "HierarchicalRLConfig", "evaluation_metrics": ["avg_reward", "success_rate", "hierarchy_efficiency"], "training_time_estimate": 90, "memory_requirement": 2000, "dependencies": ["torch", "gym", "numpy"], "status": "not_trained"}, "MetaLearner": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "meta_learning", "type": "meta", "trainer_class": "MetaLearnerTrainer", "config_class": "MetaLearnerConfig", "evaluation_metrics": ["adaptation_speed", "few_shot_accuracy", "generalization"], "training_time_estimate": 120, "memory_requirement": 2500, "dependencies": ["torch", "higher", "numpy"], "status": "not_trained"}, "ZeroShotLearning": {"name": "ZeroShotLearning", "category": "zero_shot", "type": "zero_shot", "trainer_class": "ZeroShotTrainer", "config_class": "ZeroShotConfig", "evaluation_metrics": ["zero_shot_accuracy", "transfer_efficiency"], "training_time_estimate": 80, "memory_requirement": 1800, "dependencies": ["torch", "transformers", "clip"], "status": "not_trained"}, "ContinualLearning": {"name": "ContinualLearning", "category": "continual", "type": "continual", "trainer_class": "ContinualLearningTrainer", "config_class": "ContinualLearningConfig", "evaluation_metrics": ["retention_rate", "plasticity", "catastrophic_forgetting"], "training_time_estimate": 100, "memory_requirement": 2200, "dependencies": ["torch", "avalanche", "numpy"], "status": "not_trained"}, "UnifiedTradingSystem": {"name": "UnifiedTradingSystem", "category": "unified", "type": "ensemble", "trainer_class": "UnifiedSystemTrainer", "config_class": "UnifiedSystemConfig", "evaluation_metrics": ["overall_performance", "component_agreement", "system_stability"], "training_time_estimate": 150, "memory_requirement": 5000, "dependencies": ["torch", "sklearn", "numpy", "pandas"], "status": "not_trained"}, "ModelEnsemble": {"name": "ModelEnsemble", "category": "ensemble", "type": "weighted_ensemble", "trainer_class": "EnsembleTrainer", "config_class": "EnsembleConfig", "evaluation_metrics": ["ensemble_accuracy", "diversity_score", "weight_stability"], "training_time_estimate": 40, "memory_requirement": 1500, "dependencies": ["sklearn", "numpy"], "status": "not_trained"}}, "training_plans": {"FinBERT": {"model_info": {"name": "FinBERT", "category": "sentiment", "type": "transformer", "trainer_class": "PearlSentimentTrainer", "config_class": "SentimentTrainingConfig", "evaluation_metrics": ["accuracy", "precision", "recall", "f1_score"], "training_time_estimate": 45, "memory_requirement": 2048, "dependencies": ["transformers", "torch"], "status": "not_trained"}, "priority": 1, "training_config": {"batch_size": 16, "learning_rate": 2e-05, "num_epochs": 10, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1}, "evaluation_strategy": "cross_validation_with_stratification", "optimization_strategy": "bayesian_optimization", "expected_performance": {"accuracy": 0.85, "precision": 0.82, "recall": 0.88, "f1_score": 0.85}}, "CryptoBERT": {"model_info": {"name": "CryptoBERT", "category": "sentiment", "type": "transformer", "trainer_class": "PearlSentimentTrainer", "config_class": "SentimentTrainingConfig", "evaluation_metrics": ["accuracy", "precision", "recall", "f1_score"], "training_time_estimate": 40, "memory_requirement": 1800, "dependencies": ["transformers", "torch"], "status": "not_trained"}, "priority": 2, "training_config": {"batch_size": 16, "learning_rate": 2e-05, "num_epochs": 10, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1}, "evaluation_strategy": "cross_validation_with_stratification", "optimization_strategy": "bayesian_optimization", "expected_performance": {"accuracy": 0.85, "precision": 0.82, "recall": 0.88, "f1_score": 0.85}}, "FinancialSentimentModel": {"model_info": {"name": "FinancialSentimentModel", "category": "sentiment", "type": "ensemble", "trainer_class": "PearlSentimentTrainer", "config_class": "SentimentTrainingConfig", "evaluation_metrics": ["accuracy", "precision", "recall", "f1_score", "ensemble_agreement"], "training_time_estimate": 60, "memory_requirement": 3000, "dependencies": ["transformers", "torch", "sklearn"], "status": "not_trained"}, "priority": 3, "training_config": {"batch_size": 16, "learning_rate": 2e-05, "num_epochs": 10, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1}, "evaluation_strategy": "cross_validation_with_stratification", "optimization_strategy": "grid_search", "expected_performance": {"accuracy": 0.85, "precision": 0.82, "recall": 0.88, "f1_score": 0.85}}, "SentimentEnsemble": {"model_info": {"name": "SentimentEnsemble", "category": "sentiment", "type": "ensemble", "trainer_class": "PearlSentimentTrainer", "config_class": "SentimentTrainingConfig", "evaluation_metrics": ["accuracy", "precision", "recall", "f1_score", "ensemble_diversity"], "training_time_estimate": 90, "memory_requirement": 4000, "dependencies": ["transformers", "torch", "sklearn"], "status": "not_trained"}, "priority": 4, "training_config": {"batch_size": 16, "learning_rate": 2e-05, "num_epochs": 10, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1}, "evaluation_strategy": "cross_validation_with_stratification", "optimization_strategy": "grid_search", "expected_performance": {"accuracy": 0.85, "precision": 0.82, "recall": 0.88, "f1_score": 0.85}}, "LSTM_TimeSeries": {"model_info": {"name": "LSTM_TimeSeries", "category": "timeseries", "type": "lstm", "trainer_class": "PearlTimeSeriesTrainer", "config_class": "TimeSeriesTrainingConfig", "evaluation_metrics": ["rmse", "mae", "mape", "directional_accuracy"], "training_time_estimate": 30, "memory_requirement": 1024, "dependencies": ["torch", "numpy", "pandas"], "status": "not_trained"}, "priority": 1, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 100, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1, "sequence_length": 60, "prediction_horizon": 1}, "evaluation_strategy": "time_series_split_validation", "optimization_strategy": "bayesian_optimization", "expected_performance": {"rmse": 0.05, "mae": 0.03, "mape": 5.0, "directional_accuracy": 0.65}}, "GRU_TimeSeries": {"model_info": {"name": "GRU_TimeSeries", "category": "timeseries", "type": "gru", "trainer_class": "PearlTimeSeriesTrainer", "config_class": "TimeSeriesTrainingConfig", "evaluation_metrics": ["rmse", "mae", "mape", "directional_accuracy"], "training_time_estimate": 25, "memory_requirement": 900, "dependencies": ["torch", "numpy", "pandas"], "status": "not_trained"}, "priority": 2, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 100, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1, "sequence_length": 60, "prediction_horizon": 1}, "evaluation_strategy": "time_series_split_validation", "optimization_strategy": "bayesian_optimization", "expected_performance": {"rmse": 0.05, "mae": 0.03, "mape": 5.0, "directional_accuracy": 0.65}}, "Transformer_TimeSeries": {"model_info": {"name": "Transformer_TimeSeries", "category": "timeseries", "type": "transformer", "trainer_class": "PearlTimeSeriesTrainer", "config_class": "TimeSeriesTrainingConfig", "evaluation_metrics": ["rmse", "mae", "mape", "directional_accuracy", "attention_weights"], "training_time_estimate": 50, "memory_requirement": 2048, "dependencies": ["torch", "numpy", "pandas"], "status": "not_trained"}, "priority": 3, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 100, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1, "sequence_length": 60, "prediction_horizon": 1}, "evaluation_strategy": "time_series_split_validation", "optimization_strategy": "grid_search", "expected_performance": {"rmse": 0.05, "mae": 0.03, "mape": 5.0, "directional_accuracy": 0.65}}, "ChronosModel": {"model_info": {"name": "ChronosModel", "category": "timeseries", "type": "foundation", "trainer_class": "PearlTimeSeriesTrainer", "config_class": "TimeSeriesTrainingConfig", "evaluation_metrics": ["rmse", "mae", "mape", "directional_accuracy"], "training_time_estimate": 20, "memory_requirement": 1500, "dependencies": ["chronos", "torch"], "status": "not_trained"}, "priority": 2, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 100, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1, "sequence_length": 60, "prediction_horizon": 1}, "evaluation_strategy": "time_series_split_validation", "optimization_strategy": "bayesian_optimization", "expected_performance": {"rmse": 0.05, "mae": 0.03, "mape": 5.0, "directional_accuracy": 0.65}}, "TimeSeriesEnsemble": {"model_info": {"name": "TimeSeriesEnsemble", "category": "timeseries", "type": "ensemble", "trainer_class": "PearlTimeSeriesTrainer", "config_class": "TimeSeriesTrainingConfig", "evaluation_metrics": ["rmse", "mae", "mape", "directional_accuracy", "ensemble_variance"], "training_time_estimate": 80, "memory_requirement": 3500, "dependencies": ["torch", "numpy", "pandas", "sklearn"], "status": "not_trained"}, "priority": 4, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 100, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1, "sequence_length": 60, "prediction_horizon": 1}, "evaluation_strategy": "time_series_split_validation", "optimization_strategy": "grid_search", "expected_performance": {"rmse": 0.05, "mae": 0.03, "mape": 5.0, "directional_accuracy": 0.65}}, "DQN_Agent": {"model_info": {"name": "DQN_Agent", "category": "reinforcement_learning", "type": "dqn", "trainer_class": "PearlRLTrainer", "config_class": "RLTrainingConfig", "evaluation_metrics": ["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"], "training_time_estimate": 60, "memory_requirement": 1200, "dependencies": ["torch", "gym", "numpy"], "status": "not_trained"}, "priority": 1, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 50, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1, "num_episodes": 1000, "max_steps_per_episode": 1000, "exploration_rate": 0.1}, "evaluation_strategy": "episode_based_evaluation", "optimization_strategy": "bayesian_optimization", "expected_performance": {"avg_reward": 100.0, "success_rate": 0.7, "sharpe_ratio": 1.5, "max_drawdown": 0.15}}, "A2C_Agent": {"model_info": {"name": "A2C_Agent", "category": "reinforcement_learning", "type": "a2c", "trainer_class": "PearlRLTrainer", "config_class": "RLTrainingConfig", "evaluation_metrics": ["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"], "training_time_estimate": 45, "memory_requirement": 1000, "dependencies": ["torch", "gym", "numpy"], "status": "not_trained"}, "priority": 2, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 50, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1, "num_episodes": 1000, "max_steps_per_episode": 1000, "exploration_rate": 0.1}, "evaluation_strategy": "episode_based_evaluation", "optimization_strategy": "bayesian_optimization", "expected_performance": {"avg_reward": 100.0, "success_rate": 0.7, "sharpe_ratio": 1.5, "max_drawdown": 0.15}}, "PPO_Agent": {"model_info": {"name": "PPO_Agent", "category": "reinforcement_learning", "type": "ppo", "trainer_class": "PearlRLTrainer", "config_class": "RLTrainingConfig", "evaluation_metrics": ["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"], "training_time_estimate": 55, "memory_requirement": 1100, "dependencies": ["torch", "gym", "numpy"], "status": "not_trained"}, "priority": 1, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 50, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1, "num_episodes": 1000, "max_steps_per_episode": 1000, "exploration_rate": 0.1}, "evaluation_strategy": "episode_based_evaluation", "optimization_strategy": "bayesian_optimization", "expected_performance": {"avg_reward": 100.0, "success_rate": 0.7, "sharpe_ratio": 1.5, "max_drawdown": 0.15}}, "TD3_Agent": {"model_info": {"name": "TD3_Agent", "category": "reinforcement_learning", "type": "td3", "trainer_class": "PearlRLTrainer", "config_class": "RLTrainingConfig", "evaluation_metrics": ["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"], "training_time_estimate": 70, "memory_requirement": 1300, "dependencies": ["torch", "gym", "numpy"], "status": "not_trained"}, "priority": 3, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 50, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1, "num_episodes": 1000, "max_steps_per_episode": 1000, "exploration_rate": 0.1}, "evaluation_strategy": "episode_based_evaluation", "optimization_strategy": "grid_search", "expected_performance": {"avg_reward": 100.0, "success_rate": 0.7, "sharpe_ratio": 1.5, "max_drawdown": 0.15}}, "HierarchicalRL": {"model_info": {"name": "HierarchicalRL", "category": "advanced_rl", "type": "hierarchical", "trainer_class": "HierarchicalRLTrainer", "config_class": "HierarchicalRLConfig", "evaluation_metrics": ["avg_reward", "success_rate", "hierarchy_efficiency"], "training_time_estimate": 90, "memory_requirement": 2000, "dependencies": ["torch", "gym", "numpy"], "status": "not_trained"}, "priority": 5, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 50, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1}, "evaluation_strategy": "holdout_validation", "optimization_strategy": "random_search", "expected_performance": {"performance_score": 0.8}}, "MetaLearner": {"model_info": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "meta_learning", "type": "meta", "trainer_class": "MetaLearnerTrainer", "config_class": "MetaLearnerConfig", "evaluation_metrics": ["adaptation_speed", "few_shot_accuracy", "generalization"], "training_time_estimate": 120, "memory_requirement": 2500, "dependencies": ["torch", "higher", "numpy"], "status": "not_trained"}, "priority": 5, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 50, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1}, "evaluation_strategy": "holdout_validation", "optimization_strategy": "random_search", "expected_performance": {"performance_score": 0.8}}, "ZeroShotLearning": {"model_info": {"name": "ZeroShotLearning", "category": "zero_shot", "type": "zero_shot", "trainer_class": "ZeroShotTrainer", "config_class": "ZeroShotConfig", "evaluation_metrics": ["zero_shot_accuracy", "transfer_efficiency"], "training_time_estimate": 80, "memory_requirement": 1800, "dependencies": ["torch", "transformers", "clip"], "status": "not_trained"}, "priority": 5, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 50, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1}, "evaluation_strategy": "holdout_validation", "optimization_strategy": "random_search", "expected_performance": {"performance_score": 0.8}}, "ContinualLearning": {"model_info": {"name": "ContinualLearning", "category": "continual", "type": "continual", "trainer_class": "ContinualLearningTrainer", "config_class": "ContinualLearningConfig", "evaluation_metrics": ["retention_rate", "plasticity", "catastrophic_forgetting"], "training_time_estimate": 100, "memory_requirement": 2200, "dependencies": ["torch", "avalanche", "numpy"], "status": "not_trained"}, "priority": 5, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 50, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1}, "evaluation_strategy": "holdout_validation", "optimization_strategy": "random_search", "expected_performance": {"performance_score": 0.8}}, "UnifiedTradingSystem": {"model_info": {"name": "UnifiedTradingSystem", "category": "unified", "type": "ensemble", "trainer_class": "UnifiedSystemTrainer", "config_class": "UnifiedSystemConfig", "evaluation_metrics": ["overall_performance", "component_agreement", "system_stability"], "training_time_estimate": 150, "memory_requirement": 5000, "dependencies": ["torch", "sklearn", "numpy", "pandas"], "status": "not_trained"}, "priority": 5, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 50, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1}, "evaluation_strategy": "holdout_validation", "optimization_strategy": "random_search", "expected_performance": {"performance_score": 0.8}}, "ModelEnsemble": {"model_info": {"name": "ModelEnsemble", "category": "ensemble", "type": "weighted_ensemble", "trainer_class": "EnsembleTrainer", "config_class": "EnsembleConfig", "evaluation_metrics": ["ensemble_accuracy", "diversity_score", "weight_stability"], "training_time_estimate": 40, "memory_requirement": 1500, "dependencies": ["sklearn", "numpy"], "status": "not_trained"}, "priority": 4, "training_config": {"batch_size": 32, "learning_rate": 0.001, "num_epochs": 50, "early_stopping": true, "patience": 10, "validation_split": 0.2, "test_split": 0.1}, "evaluation_strategy": "holdout_validation", "optimization_strategy": "grid_search", "expected_performance": {"performance_score": 0.8}}}, "training_order": ["LSTM_TimeSeries", "FinBERT", "PPO_Agent", "DQN_Agent", "ChronosModel", "GRU_TimeSeries", "CryptoBERT", "A2C_Agent", "Transformer_TimeSeries", "FinancialSentimentModel", "TD3_Agent", "ModelEnsemble", "TimeSeriesEnsemble", "SentimentEnsemble", "ZeroShotLearning", "HierarchicalRL", "ContinualLearning", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UnifiedTradingSystem"], "categories": {"sentiment": ["FinBERT", "CryptoBERT", "FinancialSentimentModel", "SentimentEnsemble"], "timeseries": ["LSTM_TimeSeries", "GRU_TimeSeries", "Transformer_TimeSeries", "ChronosModel", "TimeSeriesEnsemble"], "reinforcement_learning": ["DQN_Agent", "A2C_Agent", "PPO_Agent", "TD3_Agent"], "advanced": ["HierarchicalRL", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ZeroShotLearning", "ContinualLearning"], "ensemble": ["ModelEnsemble", "UnifiedTradingSystem"]}, "memory_requirements": {"FinBERT": 2048, "CryptoBERT": 1800, "FinancialSentimentModel": 3000, "SentimentEnsemble": 4000, "LSTM_TimeSeries": 1024, "GRU_TimeSeries": 900, "Transformer_TimeSeries": 2048, "ChronosModel": 1500, "TimeSeriesEnsemble": 3500, "DQN_Agent": 1200, "A2C_Agent": 1000, "PPO_Agent": 1100, "TD3_Agent": 1300, "HierarchicalRL": 2000, "MetaLearner": 2500, "ZeroShotLearning": 1800, "ContinualLearning": 2200, "UnifiedTradingSystem": 5000, "ModelEnsemble": 1500}, "total_training_time_estimate": 1250, "generated_at": "2025-07-17T08:45:18.559809"}