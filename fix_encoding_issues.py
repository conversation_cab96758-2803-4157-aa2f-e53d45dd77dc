#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Fix Encoding Issues
حل مشکلات encoding در PowerShell و Python
"""

import os
import sys
import codecs
import glob
from pathlib import Path

def fix_file_encoding(file_path):
    """تبدیل فایل به UTF-8 without BOM"""
    try:
        # Read with different encodings
        encodings_to_try = ['utf-8', 'utf-8-sig', 'cp1252', 'latin1']
        
        content = None
        original_encoding = None
        
        for encoding in encodings_to_try:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    original_encoding = encoding
                    break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            print(f"❌ Could not read {file_path}")
            return False
        
        # Write as UTF-8 without BOM
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        if original_encoding != 'utf-8':
            print(f"✅ Fixed {file_path} ({original_encoding} → utf-8)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False

def fix_project_encodings():
    """رفع مشکلات encoding در پروژه"""
    print("🔧 Fixing Encoding Issues...")
    
    # Python files to fix
    python_files = []
    
    # Find Python files
    for pattern in ['*.py', '**/*.py']:
        python_files.extend(glob.glob(pattern, recursive=True))
    
    # Important files to fix
    important_files = [
        'ai_models/__init__.py',
        'models/unified_trading_system.py',
        'core/base.py',
        'utils/sentiment_analyzer.py'
    ]
    
    fixed_count = 0
    
    for file_path in set(python_files + important_files):
        if os.path.exists(file_path):
            if fix_file_encoding(file_path):
                fixed_count += 1
    
    print(f"✅ Fixed {fixed_count} files")
    return fixed_count > 0

def create_encoding_config():
    """ایجاد تنظیمات encoding"""
    try:
        config_content = '''# -*- coding: utf-8 -*-
"""
Encoding Configuration
تنظیمات کدگذاری برای پروژه
"""

import sys
import os

# Set default encoding
if sys.version_info[0] >= 3:
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# Environment variables for encoding
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'

print("✅ Encoding configuration loaded")
'''
        
        with open("encoding_config.py", "w", encoding="utf-8") as f:
            f.write(config_content)
        
        print("✅ Encoding configuration created")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create encoding config: {e}")
        return False

def main():
    print("🔧 ENCODING ISSUES FIX")
    print("=" * 40)
    
    # Fix project encodings
    if fix_project_encodings():
        print("✅ Project encodings fixed")
    else:
        print("⚠️ Some encoding issues remain")
    
    # Create encoding config
    create_encoding_config()
    
    print("\n🎯 Encoding Issues Resolution:")
    print("✅ Files converted to UTF-8")
    print("✅ PowerShell compatibility improved")
    print("✅ Configuration saved")
    print("=" * 40)

if __name__ == "__main__":
    main() 