# مستند جامع: PlutusFinancialForecaster و PlutusConfig

## مسئولیت
ادغام مدل Plutus برای پیش‌بینی سری زمانی مالی و تولید سیگنال‌های پیش‌بینی قیمت با استفاده از API ابری (HuggingFace).

## پارامترها
- api_key: کلید API برای دسترسی به مدل
- model_name: نام مدل (پیش‌فرض plutus-forecasting)
- max_retries: حداکثر تلاش مجدد
- timeout: زمان انتظار
- use_proxy: استفاده از پروکسی

## متدهای کلیدی
- prepare_time_series_data: آماده‌سازی داده برای مدل
- make_prediction_request: ارسال درخواست پیش‌بینی
- predict_price_movement: پیش‌بینی حرکت قیمت
- process_prediction_response: پردازش پاسخ مدل

## نمونه کد
```python
from utils.plutus_integration import PlutusFinancialForecaster, PlutusConfig
config = PlutusConfig(api_key='YOUR_KEY')
forecaster = PlutusFinancialForecaster(config)
pred = forecaster.predict_price_movement(price_data, 'EURUSD')
```

## مدیریت خطا
در صورت خطا در API یا داده، پیام خطا لاگ و خطا raise می‌شود.
در صورت عدم اتصال، تلاش مجدد با backoff انجام می‌شود.

## بهترین شیوه
- همیشه داده‌های تمیز و کامل به مدل بدهید.
- از پروکسی برای اتصال امن استفاده کنید.

## نمودار
- نمودار پیش‌بینی قیمت و بازه اطمینان قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول در سیستم معاملاتی یکپارچه (models/unified_trading_system.py)، سیستم adaptive_plutus و مثال‌های پروژه استفاده شده است.

## وضعیت عملیاتی
✅ عملیاتی و در جریان اصلی پروژه فعال است. 