"""
📊 Evaluation Package - Refactored for v2.0
پکیج ارزیابی - بازسازی شده برای نسخه 2.0

این پکیج شامل ابزارهای ارزیابی و تحلیل عملکرد سازگار با معماری جدید است
"""

# Import from core
from core.base import BaseComponent, ModelPrediction
from core.logger import get_logger
from core.config import get_config
from core.exceptions import TradingSystemError, ValidationError
from core.utils import monitor_performance

# Legacy imports - maintained for backward compatibility
try:
    from .metrics import (
        PerformanceMetrics,
        TradingMetrics,
        RiskMetrics
    )
except ImportError:
    pass

try:
    from .comparison import (
        ModelComparison,
        StrategyComparison,
        BacktestComparison
    )
except ImportError:
    pass

try:
    from .explainable_ai import (
        ExplainableAI,
        FeatureImportanceAnalyzer,
        ModelExplainer
    )
except ImportError:
    pass

try:
    from .scenario_backtesting import (
        ScenarioBacktesting,
        BacktestEngine,
        ScenarioAnalyzer
    )
except ImportError:
    pass

# New evaluation classes compatible with v2.0
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass
import json

@dataclass
class EvaluationResult:
    """نتیجه ارزیابی"""
    name: str
    timestamp: datetime
    metrics: Dict[str, float]
    details: Dict[str, Any]
    passed: bool
    score: float
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        return {
            "name": self.name,
            "timestamp": self.timestamp.isoformat(),
            "metrics": self.metrics,
            "details": self.details,
            "passed": self.passed,
            "score": self.score
        }

class EvaluationEngine(BaseComponent):
    """موتور ارزیابی نسخه 2.0"""
    
    def __init__(self, name: str = "evaluation_engine", config: Dict[str, Any] = None):
        super().__init__(name, config)
        
        self.config = config or {}
        self.evaluators = {}
        self.results = []
        self.benchmarks = {}
        
        # Evaluation settings
        self.min_score_threshold = self.config.get("min_score_threshold", 0.6)
        self.max_evaluation_time = self.config.get("max_evaluation_time", 300)  # seconds
        
        self.logger = get_logger(__name__)
        
        # Initialize BaseComponent status variables
        self._initialized = False
        self._running = False
    
    def initialize(self) -> bool:
        """مقداردهی اولیه"""
        try:
            # Initialize default evaluators
            self._initialize_default_evaluators()
            
            # Load benchmarks
            self._load_benchmarks()
            
            self.logger.info("Evaluation engine initialized")
            self._initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize evaluation engine: {e}")
            return False
    
    def start(self) -> bool:
        """شروع موتور ارزیابی"""
        if not self._initialized:
            if not self.initialize():
                return False
        
        self._running = True
        self.logger.info("Evaluation engine started")
        return True
    
    def stop(self) -> bool:
        """توقف موتور ارزیابی"""
        try:
            # Save results
            self._save_results()
            
            self._running = False
            self.logger.info("Evaluation engine stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping evaluation engine: {e}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت موتور ارزیابی"""
        return {
            "initialized": self._initialized,
            "running": self._running,
            "evaluators_count": len(self.evaluators),
            "results_count": len(self.results),
            "benchmarks_count": len(self.benchmarks)
        }
    
    def register_evaluator(self, name: str, evaluator: Callable):
        """ثبت ارزیاب جدید"""
        self.evaluators[name] = evaluator
        self.logger.info(f"Evaluator registered: {name}")
    
    def evaluate(self, target: Any, evaluator_name: str, **kwargs) -> EvaluationResult:
        """ارزیابی هدف"""
        if evaluator_name not in self.evaluators:
            raise ValueError(f"Evaluator not found: {evaluator_name}")
        
        evaluator = self.evaluators[evaluator_name]
        
        start_time = datetime.now()
        
        try:
            # Run evaluation
            result = evaluator(target, **kwargs)
            
            # Create evaluation result
            evaluation_result = EvaluationResult(
                name=evaluator_name,
                timestamp=start_time,
                metrics=result.get("metrics", {}),
                details=result.get("details", {}),
                passed=result.get("passed", False),
                score=result.get("score", 0.0)
            )
            
            # Store result
            self.results.append(evaluation_result)
            
            # Log result
            self.logger.info(f"Evaluation completed: {evaluator_name} - Score: {evaluation_result.score:.2f}")
            
            return evaluation_result
            
        except Exception as e:
            self.logger.error(f"Evaluation failed: {evaluator_name} - {e}")
            
            # Create error result
            error_result = EvaluationResult(
                name=evaluator_name,
                timestamp=start_time,
                metrics={},
                details={"error": str(e)},
                passed=False,
                score=0.0
            )
            
            self.results.append(error_result)
            return error_result
    
    def batch_evaluate(self, targets: List[Any], evaluator_names: List[str], **kwargs) -> List[EvaluationResult]:
        """ارزیابی دسته‌ای"""
        results = []
        
        for target in targets:
            for evaluator_name in evaluator_names:
                result = self.evaluate(target, evaluator_name, **kwargs)
                results.append(result)
        
        return results
    
    def compare_models(self, models: List[Any], test_data: Any) -> Dict[str, Any]:
        """مقایسه مدل‌ها"""
        comparison_results = {}
        
        for i, model in enumerate(models):
            model_name = getattr(model, 'name', f'model_{i}')
            
            # Evaluate model
            result = self.evaluate(model, 'model_performance', test_data=test_data)
            comparison_results[model_name] = result
        
        # Find best model
        best_model = max(comparison_results.items(), key=lambda x: x[1].score)
        
        return {
            "results": comparison_results,
            "best_model": best_model[0],
            "best_score": best_model[1].score
        }
    
    def generate_report(self, format: str = "json") -> str:
        """تولید گزارش"""
        try:
            report_data = {
                "timestamp": datetime.now().isoformat(),
                "total_evaluations": len(self.results),
                "passed_evaluations": len([r for r in self.results if r.passed]),
                "average_score": np.mean([r.score for r in self.results]) if self.results else 0.0,
                "results": [r.to_dict() for r in self.results]
            }
            
            if format == "json":
                return json.dumps(report_data, indent=2)
            elif format == "summary":
                return self._generate_summary_report(report_data)
            else:
                return str(report_data)
                
        except Exception as e:
            self.logger.error(f"Error generating report: {e}")
            return f"Error generating report: {e}"
    
    def _initialize_default_evaluators(self):
        """مقداردهی اولیه ارزیاب‌های پیش‌فرض"""
        # Model performance evaluator
        self.register_evaluator("model_performance", self._evaluate_model_performance)
        
        # Strategy evaluator
        self.register_evaluator("strategy_performance", self._evaluate_strategy_performance)
        
        # Risk evaluator
        self.register_evaluator("risk_assessment", self._evaluate_risk)
        
        # Prediction accuracy evaluator
        self.register_evaluator("prediction_accuracy", self._evaluate_prediction_accuracy)
    
    def _evaluate_model_performance(self, model: Any, test_data: Any = None, **kwargs) -> Dict[str, Any]:
        """ارزیابی عملکرد مدل"""
        try:
            metrics = {}
            
            # Test prediction capability
            if test_data is not None and hasattr(model, 'predict'):
                sample_data = test_data.iloc[:10] if hasattr(test_data, 'iloc') else test_data[:10]
                
                predictions = []
                prediction_times = []
                
                for data_point in sample_data:
                    start_time = datetime.now()
                    try:
                        prediction = model.predict(data_point)
                        predictions.append(prediction)
                        prediction_times.append((datetime.now() - start_time).total_seconds())
                    except Exception as e:
                        self.logger.warning(f"Prediction failed: {e}")
                        prediction_times.append(0.0)
                
                # Calculate metrics
                metrics["prediction_success_rate"] = len(predictions) / len(sample_data)
                metrics["avg_prediction_time"] = np.mean(prediction_times)
                metrics["max_prediction_time"] = max(prediction_times)
                
                # Calculate confidence scores
                if predictions:
                    confidences = [getattr(p, 'confidence', 0.5) for p in predictions]
                    metrics["avg_confidence"] = np.mean(confidences)
                    metrics["min_confidence"] = min(confidences)
            
            # Model status check
            if hasattr(model, 'health_check'):
                health = model.health_check()
                metrics["model_health"] = 1.0 if health.get("status") == "healthy" else 0.0
            
            # Calculate overall score
            score = np.mean([v for v in metrics.values() if isinstance(v, (int, float))])
            
            return {
                "metrics": metrics,
                "score": score,
                "passed": score >= self.min_score_threshold,
                "details": {
                    "predictions_count": len(predictions),
                    "test_data_size": len(sample_data) if hasattr(sample_data, '__len__') else 0
                }
            }
            
        except Exception as e:
            return {
                "metrics": {},
                "score": 0.0,
                "passed": False,
                "details": {"error": str(e)}
            }
    
    def _evaluate_strategy_performance(self, strategy: Any, **kwargs) -> Dict[str, Any]:
        """ارزیابی عملکرد استراتژی"""
        try:
            metrics = {}
            
            # Get strategy performance data
            if hasattr(strategy, 'get_performance_summary'):
                performance = strategy.get_performance_summary()
                
                metrics["total_return"] = performance.get("total_return", 0.0)
                metrics["win_rate"] = performance.get("win_rate", 0.0)
                metrics["profit_factor"] = performance.get("profit_factor", 0.0)
                metrics["sharpe_ratio"] = performance.get("sharpe_ratio", 0.0)
                metrics["max_drawdown"] = performance.get("max_drawdown", 100.0)
                
                # Calculate score based on multiple metrics
                score_components = [
                    min(metrics["total_return"] / 100, 1.0),  # Normalize return
                    metrics["win_rate"],
                    min(metrics["profit_factor"] / 2, 1.0),  # Normalize profit factor
                    min(metrics["sharpe_ratio"] / 2, 1.0),  # Normalize Sharpe ratio
                    max(1 - metrics["max_drawdown"] / 100, 0.0)  # Invert drawdown
                ]
                
                score = np.mean([s for s in score_components if s >= 0])
            else:
                score = 0.0
            
            return {
                "metrics": metrics,
                "score": score,
                "passed": score >= self.min_score_threshold,
                "details": {
                    "strategy_name": getattr(strategy, 'name', 'unknown'),
                    "evaluation_type": "strategy_performance"
                }
            }
            
        except Exception as e:
            return {
                "metrics": {},
                "score": 0.0,
                "passed": False,
                "details": {"error": str(e)}
            }
    
    def _evaluate_risk(self, target: Any, **kwargs) -> Dict[str, Any]:
        """ارزیابی ریسک"""
        try:
            metrics = {}
            
            # Risk assessment logic
            if hasattr(target, 'get_risk_metrics'):
                risk_metrics = target.get_risk_metrics()
                
                metrics["var"] = risk_metrics.get("var", 0.0)
                metrics["cvar"] = risk_metrics.get("cvar", 0.0)
                metrics["volatility"] = risk_metrics.get("volatility", 0.0)
                metrics["beta"] = risk_metrics.get("beta", 1.0)
                
                # Calculate risk score (lower risk = higher score)
                risk_score = 1.0 - min(metrics["volatility"], 1.0)
                
                score = risk_score
            else:
                score = 0.5  # Default score if no risk metrics available
            
            return {
                "metrics": metrics,
                "score": score,
                "passed": score >= self.min_score_threshold,
                "details": {
                    "risk_level": "low" if score > 0.7 else "medium" if score > 0.4 else "high"
                }
            }
            
        except Exception as e:
            return {
                "metrics": {},
                "score": 0.0,
                "passed": False,
                "details": {"error": str(e)}
            }
    
    def _evaluate_prediction_accuracy(self, predictions: List[Any], actual: List[Any], **kwargs) -> Dict[str, Any]:
        """ارزیابی دقت پیش‌بینی"""
        try:
            if len(predictions) != len(actual):
                raise ValueError("Predictions and actual values must have the same length")
            
            metrics = {}
            
            # Calculate accuracy metrics
            correct_predictions = sum(1 for p, a in zip(predictions, actual) if p == a)
            metrics["accuracy"] = correct_predictions / len(predictions)
            
            # Calculate other metrics if numerical
            if all(isinstance(p, (int, float)) for p in predictions):
                errors = [abs(p - a) for p, a in zip(predictions, actual)]
                metrics["mae"] = np.mean(errors)  # Mean Absolute Error
                metrics["rmse"] = np.sqrt(np.mean([e**2 for e in errors]))  # Root Mean Square Error
                
                # R-squared
                if len(actual) > 1:
                    ss_res = sum((p - a)**2 for p, a in zip(predictions, actual))
                    ss_tot = sum((a - np.mean(actual))**2 for a in actual)
                    metrics["r_squared"] = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0.0
            
            # Calculate overall score
            score = metrics["accuracy"]
            
            return {
                "metrics": metrics,
                "score": score,
                "passed": score >= self.min_score_threshold,
                "details": {
                    "total_predictions": len(predictions),
                    "correct_predictions": correct_predictions
                }
            }
            
        except Exception as e:
            return {
                "metrics": {},
                "score": 0.0,
                "passed": False,
                "details": {"error": str(e)}
            }
    
    def _load_benchmarks(self):
        """بارگذاری benchmarks"""
        # Load predefined benchmarks
        self.benchmarks = {
            "model_performance": {
                "min_prediction_success_rate": 0.8,
                "max_avg_prediction_time": 1.0,
                "min_avg_confidence": 0.6
            },
            "strategy_performance": {
                "min_total_return": 5.0,
                "min_win_rate": 0.5,
                "min_profit_factor": 1.2,
                "max_max_drawdown": 20.0
            },
            "risk_assessment": {
                "max_volatility": 0.3,
                "max_var": 0.05,
                "max_cvar": 0.08
            }
        }
    
    def _save_results(self):
        """ذخیره نتایج"""
        try:
            # Save to file
            results_data = [r.to_dict() for r in self.results]
            
            import json
            with open(f"evaluation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "w") as f:
                json.dump(results_data, f, indent=2)
            
            self.logger.info(f"Evaluation results saved: {len(results_data)} results")
            
        except Exception as e:
            self.logger.error(f"Error saving results: {e}")
    
    def _generate_summary_report(self, report_data: Dict[str, Any]) -> str:
        """تولید گزارش خلاصه"""
        summary = f"""
Evaluation Summary Report
========================

Generated: {report_data['timestamp']}

Overall Statistics:
- Total Evaluations: {report_data['total_evaluations']}
- Passed Evaluations: {report_data['passed_evaluations']}
- Pass Rate: {report_data['passed_evaluations'] / report_data['total_evaluations'] * 100:.1f}%
- Average Score: {report_data['average_score']:.2f}

Evaluation Results:
"""
        
        for result in report_data['results']:
            summary += f"""
- {result['name']}:
  Score: {result['score']:.2f}
  Status: {'PASSED' if result['passed'] else 'FAILED'}
  Time: {result['timestamp']}
"""
        
        return summary

# Legacy compatibility wrappers
class LegacyEvaluator:
    """ارزیاب قدیمی برای backward compatibility"""
    
    def __init__(self):
        self.evaluation_engine = EvaluationEngine("legacy_evaluator")
        self.evaluation_engine.initialize()
    
    def evaluate_model(self, model, test_data=None):
        """ارزیابی مدل"""
        return self.evaluation_engine.evaluate(model, "model_performance", test_data=test_data)
    
    def evaluate_strategy(self, strategy):
        """ارزیابی استراتژی"""
        return self.evaluation_engine.evaluate(strategy, "strategy_performance")
    
    def compare_models(self, models, test_data=None):
        """مقایسه مدل‌ها"""
        return self.evaluation_engine.compare_models(models, test_data)

# Create global instances
evaluation_engine = EvaluationEngine("global_evaluation_engine")
legacy_evaluator = LegacyEvaluator()

# Export all available functions and classes
__all__ = [
    # New classes
    "EvaluationResult",
    "EvaluationEngine",
    "LegacyEvaluator",
    "evaluation_engine",
    "legacy_evaluator",
    
    # Base classes
    "BaseComponent",
    "ModelPrediction"
]

# Version info
__version__ = "2.0.0"
__author__ = "Trading System Team"

# Migration message
import warnings
warnings.warn(
    "The evaluation package has been refactored for v2.0. "
    "Please update your code to use the new EvaluationEngine class. "
    "Legacy evaluation classes are maintained for backward compatibility but may be removed in future versions.",
    DeprecationWarning,
    stacklevel=2
)

# Initialize evaluation package
def initialize_evaluation_package():
    """مقداردهی اولیه پکیج ارزیابی"""
    logger = get_logger(__name__)
    
    try:
        # Initialize evaluation engine
        evaluation_engine.initialize()
        
        logger.info("✅ Evaluation package initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Evaluation initialization failed: {e}")
        return False

# Auto-initialize when imported
if __name__ != "__main__":
    initialize_evaluation_package()
