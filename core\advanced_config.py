#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚙️ Advanced Configuration Management System
سیستم پیشرفته مدیریت پیکربندی با Pydantic
"""

import os
import sys
import json
import yaml
from typing import Dict, List, Optional, Any, Union, Literal
from pathlib import Path
from datetime import datetime
from pydantic import BaseModel, Field, field_validator, model_validator
from pydantic_settings import BaseSettings
from enum import Enum

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class LogLevel(str, Enum):
    """سطح لاگ"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class TradingMode(str, Enum):
    """حالت معاملاتی"""
    DEMO = "demo"
    LIVE = "live"
    BACKTEST = "backtest"

class DatabaseType(str, Enum):
    """نوع دیتابیس"""
    SQLITE = "sqlite"
    POSTGRESQL = "postgresql"
    MYSQL = "mysql"

class ProxyConfig(BaseModel):
    """تنظیمات پروکسی"""
    enabled: bool = Field(default=False, description="فعال/غیرفعال بودن پروکسی")
    http_url: str = Field(default="http://127.0.0.1:10809", description="آدرس HTTP پروکسی")
    socks_url: str = Field(default="socks5://127.0.0.1:10808", description="آدرس SOCKS پروکسی")
    username: Optional[str] = Field(default=None, description="نام کاربری پروکسی")
    password: Optional[str] = Field(default=None, description="رمز عبور پروکسی")
    timeout: int = Field(default=30, ge=1, le=300, description="زمان انتظار (ثانیه)")
    
    @field_validator('http_url', 'socks_url')
    def validate_url(cls, v):
        if v and not (v.startswith('http://') or v.startswith('https://') or v.startswith('socks5://')):
            raise ValueError('URL must start with http://, https://, or socks5://')
        return v

class LoggingConfig(BaseModel):
    """تنظیمات لاگ"""
    level: LogLevel = Field(default=LogLevel.INFO, description="سطح لاگ")
    file_enabled: bool = Field(default=True, description="فعال بودن لاگ فایل")
    console_enabled: bool = Field(default=True, description="فعال بودن لاگ کنسول")
    file_path: str = Field(default="logs/trading_system.log", description="مسیر فایل لاگ")
    max_file_size: int = Field(default=10485760, ge=1024, description="حداکثر اندازه فایل (بایت)")
    backup_count: int = Field(default=5, ge=1, le=100, description="تعداد فایل بک‌آپ")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="فرمت لاگ"
    )
    
    @field_validator('file_path')
    def validate_file_path(cls, v):
        # Create directory if it doesn't exist
        Path(v).parent.mkdir(parents=True, exist_ok=True)
        return v

class DatabaseConfig(BaseModel):
    """تنظیمات دیتابیس"""
    type: DatabaseType = Field(default=DatabaseType.SQLITE, description="نوع دیتابیس")
    host: str = Field(default="localhost", description="آدرس سرور")
    port: int = Field(default=5432, ge=1, le=65535, description="پورت اتصال")
    database: str = Field(default="trading_system.db", description="نام دیتابیس")
    username: Optional[str] = Field(default=None, description="نام کاربری")
    password: Optional[str] = Field(default=None, description="رمز عبور")
    pool_size: int = Field(default=10, ge=1, le=100, description="اندازه pool اتصال")
    max_overflow: int = Field(default=20, ge=0, le=100, description="حداکثر overflow")
    echo: bool = Field(default=False, description="نمایش کوئری‌ها")
    
    @property
    def connection_string(self) -> str:
        """رشته اتصال دیتابیس"""
        if self.type == DatabaseType.SQLITE:
            return f"sqlite:///{self.database}"
        elif self.type == DatabaseType.POSTGRESQL:
            auth = f"{self.username}:{self.password}@" if self.username else ""
            return f"postgresql://{auth}{self.host}:{self.port}/{self.database}"
        elif self.type == DatabaseType.MYSQL:
            auth = f"{self.username}:{self.password}@" if self.username else ""
            return f"mysql+pymysql://{auth}{self.host}:{self.port}/{self.database}"
        else:
            raise ValueError(f"Unsupported database type: {self.type}")

class TradingConfig(BaseModel):
    """تنظیمات معاملاتی"""
    mode: TradingMode = Field(default=TradingMode.DEMO, description="حالت معاملاتی")
    symbols: List[str] = Field(default=["EURUSD", "GBPUSD", "USDJPY"], description="نمادهای معاملاتی")
    timeframes: List[str] = Field(default=["H1", "H4", "D1"], description="بازه‌های زمانی")
    initial_capital: float = Field(default=10000.0, ge=100.0, description="سرمایه اولیه")
    max_positions: int = Field(default=5, ge=1, le=50, description="حداکثر موقعیت‌ها")
    risk_per_trade: float = Field(default=0.02, ge=0.001, le=0.1, description="ریسک هر معامله")
    commission: float = Field(default=0.001, ge=0.0, le=0.01, description="کمیسیون")
    slippage: float = Field(default=0.001, ge=0.0, le=0.01, description="لغزش")
    
    @field_validator('symbols')
    def validate_symbols(cls, v):
        if not v:
            raise ValueError('At least one symbol must be specified')
        # Validate symbol format (basic check)
        for symbol in v:
            if len(symbol) < 6:
                raise ValueError(f'Invalid symbol format: {symbol}')
        return v

class BacktestingConfig(BaseModel):
    """تنظیمات بک‌تست"""
    enabled: bool = Field(default=True, description="فعال بودن بک‌تست")
    start_date: str = Field(default="2023-01-01", description="تاریخ شروع")
    end_date: str = Field(default="2024-01-01", description="تاریخ پایان")
    data_path: str = Field(default="data", description="مسیر داده‌ها")
    benchmark: str = Field(default="EURUSD", description="بنچمارک")
    save_results: bool = Field(default=True, description="ذخیره نتایج")
    
    @field_validator('start_date', 'end_date')
    def validate_date_format(cls, v):
        try:
            datetime.strptime(v, '%Y-%m-%d')
        except ValueError:
            raise ValueError('Date must be in YYYY-MM-DD format')
        return v
    
    @model_validator(mode='after')
    def validate_date_range(cls, values):
        start = values.start_date
        end = values.end_date
        if start and end:
            start_dt = datetime.strptime(start, '%Y-%m-%d')
            end_dt = datetime.strptime(end, '%Y-%m-%d')
            if start_dt >= end_dt:
                raise ValueError('End date must be after start date')
        return values

class AIModelConfig(BaseModel):
    """تنظیمات مدل‌های AI"""
    enabled: bool = Field(default=True, description="فعال بودن AI")
    sentiment_model: str = Field(
        default="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
        description="مدل تحلیل احساسات"
    )
    timeseries_model: str = Field(default="lstm", description="مدل پیش‌بینی سری زمانی")
    max_memory_usage: int = Field(default=4096, ge=512, description="حداکثر مصرف حافظه (MB)")
    batch_size: int = Field(default=32, ge=1, le=256, description="اندازه batch")
    device: Literal["cpu", "cuda", "auto"] = Field(default="auto", description="دستگاه محاسباتی")
    
    @field_validator('sentiment_model')
    def validate_model_name(cls, v):
        # Basic validation for model name format
        if '/' not in v and v not in ['bert', 'lstm', 'gru', 'transformer']:
            raise ValueError('Invalid model name format')
        return v

class SecurityConfig(BaseModel):
    """تنظیمات امنیتی"""
    api_key_encryption: bool = Field(default=True, description="رمزنگاری کلید API")
    session_timeout: int = Field(default=3600, ge=300, le=86400, description="زمان انقضای جلسه")
    max_login_attempts: int = Field(default=5, ge=1, le=20, description="حداکثر تلاش ورود")
    password_min_length: int = Field(default=8, ge=6, le=128, description="حداقل طول رمز")
    enable_2fa: bool = Field(default=False, description="فعال بودن احراز دو مرحله‌ای")
    allowed_ips: List[str] = Field(default=["127.0.0.1"], description="IP های مجاز")
    
    @field_validator('allowed_ips')
    def validate_ips(cls, v):
        import ipaddress
        for ip in v:
            try:
                ipaddress.ip_address(ip)
            except ValueError:
                raise ValueError(f'Invalid IP address: {ip}')
        return v

class PerformanceConfig(BaseModel):
    """تنظیمات عملکرد"""
    max_threads: int = Field(default=4, ge=1, le=32, description="حداکثر thread")
    cache_size: int = Field(default=1000, ge=10, le=10000, description="اندازه کش")
    cache_ttl: int = Field(default=300, ge=30, le=3600, description="زمان زندگی کش")
    async_enabled: bool = Field(default=True, description="فعال بودن عملیات ناهمزمان")
    memory_limit: int = Field(default=2048, ge=256, description="محدودیت حافظه (MB)")
    gc_threshold: float = Field(default=0.8, ge=0.1, le=0.95, description="آستانه garbage collection")

class NotificationConfig(BaseModel):
    """تنظیمات اعلان‌ها"""
    enabled: bool = Field(default=True, description="فعال بودن اعلان‌ها")
    email_enabled: bool = Field(default=False, description="فعال بودن ایمیل")
    telegram_enabled: bool = Field(default=False, description="فعال بودن تلگرام")
    slack_enabled: bool = Field(default=False, description="فعال بودن Slack")
    webhook_url: Optional[str] = Field(default=None, description="آدرس webhook")
    critical_only: bool = Field(default=True, description="فقط اعلان‌های حیاتی")

class AdvancedTradingSystemConfig(BaseSettings):
    """پیکربندی کامل سیستم معاملاتی"""
    
    # Basic settings
    app_name: str = Field(default="Advanced Trading System", description="نام برنامه")
    version: str = Field(default="2.0.0", description="نسخه")
    debug: bool = Field(default=False, description="حالت دیباگ")
    environment: Literal["development", "staging", "production"] = Field(
        default="development", description="محیط اجرا"
    )
    
    # Component configurations
    proxy: ProxyConfig = Field(default_factory=ProxyConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    trading: TradingConfig = Field(default_factory=TradingConfig)
    backtesting: BacktestingConfig = Field(default_factory=BacktestingConfig)
    ai_models: AIModelConfig = Field(default_factory=AIModelConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    notifications: NotificationConfig = Field(default_factory=NotificationConfig)
    
    # Advanced settings
    auto_save_config: bool = Field(default=True, description="ذخیره خودکار تنظیمات")
    config_backup: bool = Field(default=True, description="بک‌آپ تنظیمات")
    validate_on_load: bool = Field(default=True, description="اعتبارسنجی در هنگام بارگذاری")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        env_nested_delimiter = "__"
        case_sensitive = False
        
    @field_validator('version')
    def validate_version(cls, v):
        import re
        if not re.match(r'^\d+\.\d+\.\d+$', v):
            raise ValueError('Version must be in format X.Y.Z')
        return v
    
    @model_validator(mode='after')
    def validate_environment_consistency(cls, values):
        """اعتبارسنجی سازگاری محیط"""
        env = values.environment
        debug = values.debug
        trading_mode = values.trading.mode
        
        if env == 'production':
            if debug:
                raise ValueError('Debug mode should not be enabled in production')
            if trading_mode == TradingMode.DEMO:
                values.trading.mode = TradingMode.LIVE
                
        return values
    
    def save_to_file(self, file_path: str = "config.yaml") -> None:
        """ذخیره در فایل"""
        config_dict = self.dict()
        
        file_path = Path(file_path)
        
        if file_path.suffix.lower() == '.json':
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False, default=str)
        else:  # YAML
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
        
        print(f"✅ Configuration saved to: {file_path}")
    
    @classmethod
    def load_from_file(cls, file_path: str = "config.yaml") -> 'AdvancedTradingSystemConfig':
        """بارگذاری از فایل"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            print(f"⚠️ Config file not found: {file_path}, using defaults")
            return cls()
        
        try:
            if file_path.suffix.lower() == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
            else:  # YAML
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_dict = yaml.safe_load(f)
            
            return cls(**config_dict)
            
        except Exception as e:
            print(f"❌ Error loading config: {e}")
            print("Using default configuration...")
            return cls()
    
    def backup_config(self, backup_dir: str = "config_backups") -> str:
        """بک‌آپ تنظیمات"""
        backup_path = Path(backup_dir)
        backup_path.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = backup_path / f"config_backup_{timestamp}.yaml"
        
        self.save_to_file(str(backup_file))
        return str(backup_file)
    
    def validate_configuration(self) -> Dict[str, Any]:
        """اعتبارسنجی کامل تنظیمات"""
        validation_results = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": []
        }
        
        # Check database connection
        try:
            connection_string = self.database.connection_string
            validation_results["suggestions"].append("Database connection string generated successfully")
        except Exception as e:
            validation_results["errors"].append(f"Database configuration error: {e}")
            validation_results["is_valid"] = False
        
        # Check trading symbols
        if not self.trading.symbols:
            validation_results["errors"].append("No trading symbols specified")
            validation_results["is_valid"] = False
        
        # Check AI model settings
        if self.ai_models.enabled and self.ai_models.max_memory_usage < 512:
            validation_results["warnings"].append("Low memory allocation for AI models may cause issues")
        
        # Security checks
        if self.environment == "production" and not self.security.api_key_encryption:
            validation_results["warnings"].append("API key encryption should be enabled in production")
        
        # Performance checks
        if self.performance.max_threads > 16:
            validation_results["warnings"].append("High thread count may impact performance")
        
        return validation_results
    
    def get_component_config(self, component_name: str) -> Optional[BaseModel]:
        """دریافت تنظیمات جزء خاص"""
        return getattr(self, component_name, None)
    
    def update_component_config(self, component_name: str, **kwargs) -> bool:
        """بروزرسانی تنظیمات جزء"""
        try:
            if hasattr(self, component_name):
                component = getattr(self, component_name)
                updated_component = component.copy(update=kwargs)
                setattr(self, component_name, updated_component)
                return True
            return False
        except Exception:
            return False

# Global configuration instance
config = AdvancedTradingSystemConfig()

def get_config() -> AdvancedTradingSystemConfig:
    """دریافت تنظیمات سراسری"""
    return config

def load_config(file_path: str = "config.yaml") -> AdvancedTradingSystemConfig:
    """بارگذاری تنظیمات از فایل"""
    global config
    config = AdvancedTradingSystemConfig.load_from_file(file_path)
    return config

def save_config(file_path: str = "config.yaml") -> None:
    """ذخیره تنظیمات در فایل"""
    config.save_to_file(file_path)

def validate_config() -> bool:
    """اعتبارسنجی تنظیمات"""
    results = config.validate_configuration()
    return results["is_valid"]

if __name__ == "__main__":
    """تست سیستم پیکربندی"""
    print("⚙️ Testing Advanced Configuration Management...")
    
    # Create default config
    test_config = AdvancedTradingSystemConfig()
    
    # Test validation
    validation_results = test_config.validate_configuration()
    print(f"✅ Configuration valid: {validation_results['is_valid']}")
    
    # Test save/load
    test_config.save_to_file("test_config.yaml")
    loaded_config = AdvancedTradingSystemConfig.load_from_file("test_config.yaml")
    
    print("🎉 Advanced Configuration Management is ready!") 