from portfolio.portfolio_manager import PortfolioManager

def test_trade_history_and_total_pnl():
    pm = PortfolioManager(initial_balance=1000)
    pm.open_position('EURUSD', quantity=1, entry_price=1.1, position_type='long')
    pm.close_position('EURUSD', exit_price=1.2)
    pm.open_position('USDJPY', quantity=2, entry_price=110, position_type='short')
    pm.close_position('USDJPY', exit_price=108)
    history = pm.get_trade_history()
    assert len(history) == 2
    assert history[0]['symbol'] == 'EURUSD'
    assert history[1]['symbol'] == 'USDJPY'
    assert abs(pm.get_total_pnl() - (0.1 + 4.0)) < 1e-8

def test_trade_report_format():
    pm = PortfolioManager(initial_balance=1000)
    pm.open_position('BTCUSD', quantity=0.5, entry_price=30000, position_type='long')
    pm.close_position('BTCUSD', exit_price=31000)
    report = pm.get_trade_report()
    assert 'BTCUSD' in report
    assert 'symbol,quantity,entry_price,exit_price,position_type,profit' in report
    assert 'long' in report
    assert '500.0' in report
