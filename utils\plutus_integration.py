"""
Plutus Model Integration for Financial Time Series Forecasting
استفاده از مدل Plutus برای پیش‌بینی سری زمانی مالی بدون نصب محلی
"""

import requests
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta
import asyncio
import aiohttp
from dataclasses import dataclass

# Configuration for proxy usage
PROXY_CONFIG = {
    "http": "http://127.0.0.1:10809",
    "https": "http://127.0.0.1:10809"
}

@dataclass
class PlutusConfig:
    """Configuration for Plutus model integration"""
    api_key: str
    model_name: str = "plutus-forecasting"  # This would be the actual model name
    max_retries: int = 3
    timeout: int = 30
    use_proxy: bool = True

class PlutusFinancialForecaster:
    """
    Integration class for Plutus financial time series forecasting model
    Uses Hugging Face Inference API for predictions without local installation
    """
    
    def __init__(self, config: PlutusConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Hugging Face API endpoints
        self.base_url = "https://api-inference.huggingface.co/models"
        self.headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json"
        }
        
        # Session for HTTP requests
        self.session = requests.Session()
        if config.use_proxy:
            self.session.proxies.update(PROXY_CONFIG)
    
    def prepare_time_series_data(self, 
                                price_data: pd.DataFrame,
                                features: List[str] = None) -> Dict[str, Any]:
        """
        Prepare time series data for Plutus model input
        
        Args:
            price_data: DataFrame with OHLCV data
            features: List of feature columns to include
            
        Returns:
            Formatted data for API request
        """
        try:
            if features is None:
                features = ['close', 'volume', 'high', 'low', 'open']
            
            # Ensure we have the required columns
            available_features = [f for f in features if f in price_data.columns]
            
            if not available_features:
                raise ValueError(f"None of the required features {features} found in data")
            
            # Prepare the time series data
            data = {
                "inputs": {
                    "time_series": price_data[available_features].values.tolist(),
                    "timestamps": price_data.index.strftime('%Y-%m-%d %H:%M:%S').tolist() if hasattr(price_data.index, 'strftime') else list(range(len(price_data))),
                    "features": available_features
                },
                "parameters": {
                    "prediction_length": 24,  # Predict next 24 periods
                    "num_samples": 100,       # Number of forecast samples
                    "quantiles": [0.1, 0.5, 0.9]  # Prediction intervals
                }
            }
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error preparing time series data: {str(e)}")
            raise
    
    def make_prediction_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make prediction request to Hugging Face API
        
        Args:
            data: Formatted input data
            
        Returns:
            Prediction response
        """
        # Note: This is a conceptual implementation
        # The actual API endpoint would depend on the specific Plutus model availability
        url = f"{self.base_url}/financial-time-series-plutus"  # Hypothetical endpoint
        
        for attempt in range(self.config.max_retries):
            try:
                response = self.session.post(
                    url,
                    headers=self.headers,
                    json=data,
                    timeout=self.config.timeout
                )
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 503:
                    # Model is loading, wait and retry
                    self.logger.info(f"Model loading, retrying in {2 ** attempt} seconds...")
                    time.sleep(2 ** attempt)
                    continue
                else:
                    response.raise_for_status()
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Request failed (attempt {attempt + 1}): {str(e)}")
                if attempt == self.config.max_retries - 1:
                    raise
                time.sleep(2 ** attempt)
        
        raise Exception("Max retries exceeded")
    
    def predict_price_movement(self, 
                             price_data: pd.DataFrame,
                             symbol: str,
                             prediction_horizon: int = 24) -> Dict[str, Any]:
        """
        Predict price movement using Plutus model
        
        Args:
            price_data: Historical price data
            symbol: Trading symbol
            prediction_horizon: Number of periods to predict
            
        Returns:
            Prediction results with confidence intervals
        """
        try:
            # Prepare data for the model
            model_input = self.prepare_time_series_data(price_data)
            model_input["parameters"]["prediction_length"] = prediction_horizon
            
            # Make prediction request
            response = self.make_prediction_request(model_input)
            
            # Process the response
            predictions = self.process_prediction_response(response, symbol)
            
            return predictions
            
        except Exception as e:
            self.logger.error(f"Error in price movement prediction: {str(e)}")
            raise
    
    def process_prediction_response(self, 
                                  response: Dict[str, Any],
                                  symbol: str) -> Dict[str, Any]:
        """
        Process the prediction response from the API
        
        Args:
            response: Raw API response
            symbol: Trading symbol
            
        Returns:
            Processed predictions
        """
        try:
            # This is a conceptual implementation
            # Actual response structure would depend on the specific model
            
            predictions = {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "forecast": {
                    "mean": response.get("mean_forecast", []),
                    "quantiles": response.get("quantile_forecasts", {}),
                    "confidence_intervals": response.get("confidence_intervals", {})
                },
                "metadata": {
                    "model_version": response.get("model_version", "unknown"),
                    "prediction_horizon": len(response.get("mean_forecast", [])),
                    "input_length": response.get("input_length", 0)
                }
            }
            
            # Calculate trend and signals
            if predictions["forecast"]["mean"]:
                mean_forecast = predictions["forecast"]["mean"]
                current_price = mean_forecast[0] if mean_forecast else 0
                future_price = mean_forecast[-1] if len(mean_forecast) > 1 else current_price
                
                predictions["signals"] = {
                    "trend": "bullish" if future_price > current_price else "bearish",
                    "strength": abs(future_price - current_price) / current_price if current_price > 0 else 0,
                    "confidence": self.calculate_confidence(predictions["forecast"]["quantiles"])
                }
            
            return predictions
            
        except Exception as e:
            self.logger.error(f"Error processing prediction response: {str(e)}")
            raise
    
    def calculate_confidence(self, quantiles: Dict[str, List[float]]) -> float:
        """
        Calculate confidence score from quantile predictions
        
        Args:
            quantiles: Quantile forecasts
            
        Returns:
            Confidence score (0-1)
        """
        try:
            if not quantiles or "0.1" not in quantiles or "0.9" not in quantiles:
                return 0.5  # Default moderate confidence
            
            # Calculate average confidence interval width
            q10 = quantiles["0.1"]
            q90 = quantiles["0.9"]
            
            if len(q10) != len(q90):
                return 0.5
            
            # Narrower confidence intervals indicate higher confidence
            avg_width = np.mean([abs(q90[i] - q10[i]) for i in range(len(q10))])
            
            # Normalize to 0-1 scale (this is a simplified approach)
            confidence = max(0.1, min(0.9, 1.0 - (avg_width / 100)))
            
            return confidence
            
        except Exception as e:
            self.logger.error(f"Error calculating confidence: {str(e)}")
            return 0.5

class PlutusIntegrationExample:
    """
    Example integration of Plutus with existing trading system
    """
    
    def __init__(self, api_key: str):
        self.config = PlutusConfig(api_key=api_key)
        self.forecaster = PlutusFinancialForecaster(self.config)
        self.logger = logging.getLogger(__name__)
    
    def get_enhanced_predictions(self, 
                               symbol: str,
                               price_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Get enhanced predictions combining Plutus with existing systems
        
        Args:
            symbol: Trading symbol
            price_data: Historical price data
            
        Returns:
            Enhanced prediction results
        """
        try:
            # Get Plutus predictions
            plutus_predictions = self.forecaster.predict_price_movement(
                price_data, symbol, prediction_horizon=24
            )
            
            # Combine with existing analysis
            enhanced_results = {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "plutus_forecast": plutus_predictions,
                "combined_signals": self.combine_signals(plutus_predictions),
                "risk_assessment": self.assess_risk(plutus_predictions),
                "trading_recommendations": self.generate_recommendations(plutus_predictions)
            }
            
            return enhanced_results
            
        except Exception as e:
            self.logger.error(f"Error in enhanced predictions: {str(e)}")
            raise
    
    def combine_signals(self, plutus_predictions: Dict[str, Any]) -> Dict[str, Any]:
        """
        Combine Plutus signals with existing trading signals
        
        Args:
            plutus_predictions: Plutus prediction results
            
        Returns:
            Combined signal analysis
        """
        try:
            plutus_signals = plutus_predictions.get("signals", {})
            
            combined = {
                "plutus_trend": plutus_signals.get("trend", "neutral"),
                "plutus_confidence": plutus_signals.get("confidence", 0.5),
                "plutus_strength": plutus_signals.get("strength", 0.0),
                "overall_recommendation": self.calculate_overall_recommendation(plutus_signals)
            }
            
            return combined
            
        except Exception as e:
            self.logger.error(f"Error combining signals: {str(e)}")
            return {"error": str(e)}
    
    def assess_risk(self, plutus_predictions: Dict[str, Any]) -> Dict[str, Any]:
        """
        Assess risk based on Plutus predictions
        
        Args:
            plutus_predictions: Plutus prediction results
            
        Returns:
            Risk assessment
        """
        try:
            forecast = plutus_predictions.get("forecast", {})
            quantiles = forecast.get("quantiles", {})
            
            risk_metrics = {
                "volatility_risk": self.calculate_volatility_risk(quantiles),
                "directional_risk": self.calculate_directional_risk(forecast),
                "confidence_risk": 1.0 - plutus_predictions.get("signals", {}).get("confidence", 0.5),
                "overall_risk_score": 0.0
            }
            
            # Calculate overall risk score
            risk_metrics["overall_risk_score"] = np.mean([
                risk_metrics["volatility_risk"],
                risk_metrics["directional_risk"],
                risk_metrics["confidence_risk"]
            ])
            
            return risk_metrics
            
        except Exception as e:
            self.logger.error(f"Error assessing risk: {str(e)}")
            return {"error": str(e)}
    
    def calculate_volatility_risk(self, quantiles: Dict[str, List[float]]) -> float:
        """Calculate volatility-based risk score"""
        try:
            if not quantiles or "0.1" not in quantiles or "0.9" not in quantiles:
                return 0.5
            
            # Higher volatility = higher risk
            q10 = quantiles["0.1"]
            q90 = quantiles["0.9"]
            
            if len(q10) != len(q90):
                return 0.5
            
            avg_range = np.mean([abs(q90[i] - q10[i]) for i in range(len(q10))])
            
            # Normalize to 0-1 scale
            return min(1.0, avg_range / 50)  # Simplified normalization
            
        except Exception:
            return 0.5
    
    def calculate_directional_risk(self, forecast: Dict[str, Any]) -> float:
        """Calculate directional risk score"""
        try:
            mean_forecast = forecast.get("mean", [])
            if not mean_forecast or len(mean_forecast) < 2:
                return 0.5
            
            # Calculate trend consistency
            changes = [mean_forecast[i+1] - mean_forecast[i] for i in range(len(mean_forecast)-1)]
            
            if not changes:
                return 0.5
            
            # Count direction changes
            direction_changes = sum(1 for i in range(len(changes)-1) 
                                  if (changes[i] > 0) != (changes[i+1] > 0))
            
            # More direction changes = higher risk
            return min(1.0, direction_changes / len(changes))
            
        except Exception:
            return 0.5
    
    def calculate_overall_recommendation(self, signals: Dict[str, Any]) -> str:
        """Calculate overall trading recommendation"""
        try:
            trend = signals.get("trend", "neutral")
            confidence = signals.get("confidence", 0.5)
            strength = signals.get("strength", 0.0)
            
            if confidence < 0.3:
                return "hold"  # Low confidence
            elif trend == "bullish" and strength > 0.02 and confidence > 0.6:
                return "strong_buy"
            elif trend == "bullish" and confidence > 0.5:
                return "buy"
            elif trend == "bearish" and strength > 0.02 and confidence > 0.6:
                return "strong_sell"
            elif trend == "bearish" and confidence > 0.5:
                return "sell"
            else:
                return "hold"
                
        except Exception:
            return "hold"
    
    def generate_recommendations(self, plutus_predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Generate trading recommendations based on Plutus predictions"""
        try:
            signals = plutus_predictions.get("signals", {})
            forecast = plutus_predictions.get("forecast", {})
            
            recommendations = {
                "action": self.calculate_overall_recommendation(signals),
                "position_size": self.calculate_position_size(signals),
                "stop_loss": self.calculate_stop_loss(forecast),
                "take_profit": self.calculate_take_profit(forecast),
                "time_horizon": "short_term",  # Based on 24-period forecast
                "reasoning": self.generate_reasoning(plutus_predictions)
            }
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Error generating recommendations: {str(e)}")
            return {"error": str(e)}
    
    def calculate_position_size(self, signals: Dict[str, Any]) -> float:
        """Calculate recommended position size based on confidence"""
        try:
            confidence = signals.get("confidence", 0.5)
            strength = signals.get("strength", 0.0)
            
            # Base position size on confidence and strength
            base_size = 0.1  # 10% base position
            confidence_multiplier = confidence * 2  # 0-2x multiplier
            strength_multiplier = min(2.0, strength * 50)  # Cap at 2x
            
            position_size = base_size * confidence_multiplier * strength_multiplier
            
            return min(0.5, max(0.01, position_size))  # Cap between 1% and 50%
            
        except Exception:
            return 0.1  # Default 10%
    
    def calculate_stop_loss(self, forecast: Dict[str, Any]) -> float:
        """Calculate stop loss based on forecast volatility"""
        try:
            quantiles = forecast.get("quantiles", {})
            if "0.1" in quantiles and quantiles["0.1"]:
                # Use 10th percentile as stop loss reference
                return abs(quantiles["0.1"][0] * 0.02)  # 2% of the lower bound
            return 0.02  # Default 2% stop loss
            
        except Exception:
            return 0.02
    
    def calculate_take_profit(self, forecast: Dict[str, Any]) -> float:
        """Calculate take profit based on forecast"""
        try:
            quantiles = forecast.get("quantiles", {})
            if "0.9" in quantiles and quantiles["0.9"]:
                # Use 90th percentile as take profit reference
                return abs(quantiles["0.9"][0] * 0.03)  # 3% of the upper bound
            return 0.03  # Default 3% take profit
            
        except Exception:
            return 0.03
    
    def generate_reasoning(self, plutus_predictions: Dict[str, Any]) -> str:
        """Generate human-readable reasoning for the prediction"""
        try:
            signals = plutus_predictions.get("signals", {})
            trend = signals.get("trend", "neutral")
            confidence = signals.get("confidence", 0.5)
            strength = signals.get("strength", 0.0)
            
            reasoning = f"Plutus model predicts {trend} trend with {confidence:.1%} confidence. "
            reasoning += f"Expected price movement strength: {strength:.1%}. "
            
            if confidence > 0.7:
                reasoning += "High confidence prediction suggests strong signal. "
            elif confidence < 0.4:
                reasoning += "Low confidence suggests uncertain market conditions. "
            
            if strength > 0.03:
                reasoning += "Strong expected movement indicates potential trading opportunity."
            elif strength < 0.01:
                reasoning += "Weak expected movement suggests sideways market."
            
            return reasoning
            
        except Exception:
            return "Unable to generate reasoning due to prediction processing error."

# Example usage function
def create_plutus_integration_example():
    """
    Create an example of how to integrate Plutus with the existing trading system
    """
    
    # Note: You would need to get an actual Hugging Face API key
    # and ensure the Plutus model is available on Hugging Face
    
    example_code = '''
# Example usage:
from utils.plutus_integration import PlutusIntegrationExample
import pandas as pd

# Initialize with your Hugging Face API key
api_key = "your_hugging_face_api_key_here"
plutus_integration = PlutusIntegrationExample(api_key)

# Load your price data
price_data = pd.read_csv("data/EURUSD/H1.csv")
price_data['timestamp'] = pd.to_datetime(price_data['timestamp'])
price_data.set_index('timestamp', inplace=True)

# Get enhanced predictions
try:
    predictions = plutus_integration.get_enhanced_predictions("EURUSD", price_data)
    print("Plutus Predictions:", predictions)
except Exception as e:
    print(f"Error: {e}")
    # Fallback to existing prediction methods
'''
    
    return example_code

if __name__ == "__main__":
    # Print example usage
    print("Plutus Integration Example:")
    print(create_plutus_integration_example()) 