{"type": "startup_tests", "timestamp": "2025-07-12T03:21:26.705660", "summary": {"total_tests": 4, "passed": 0, "failed": 4, "success_rate": 0.0, "total_duration": 4.823814153671265}, "results": [{"name": "test_advanced_risk_manager.py", "passed": false, "duration": 1.3624811172485352, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 391\nE       await asyncio.to_thread(env.stop)\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:21:23.219462"}, {"name": "test_smart_portfolio_manager.py", "passed": false, "duration": 1.0174996852874756, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 391\nE       await asyncio.to_thread(env.stop)\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:21:24.240962"}, {"name": "test_integrated_system.py", "passed": false, "duration": 1.1280019283294678, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 391\nE       await asyncio.to_thread(env.stop)\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:21:25.370948"}, {"name": "test_integration_*.py", "passed": false, "duration": 1.3158314228057861, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 391\nE       await asyncio.to_thread(env.stop)\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:21:26.703556"}]}