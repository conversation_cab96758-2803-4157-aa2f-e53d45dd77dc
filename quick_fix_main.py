#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Fix Main Script
"""

import re

def fix_main_file():
    """رفع سریع مشکل main_new.py"""
    
    # خواندن فایل
    with open('main_new.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # حذف import مشکل‌دار
    content = re.sub(
        r'# Import configuration management\nfrom core import \(\n    # Configuration Management\n    CONFIGURATION_MANAGEMENT_AVAILABLE,\n    AdvancedConfigurationManager,\n    config_manager\n\)\n\n',
        '',
        content
    )
    
    # اطمینان از وجود متغیرها
    if 'CONFIGURATION_MANAGEMENT_AVAILABLE = False' not in content:
        insert_point = content.find('# Configure logging')
        if insert_point != -1:
            new_vars = '''# Configuration Management variables
CONFIGURATION_MANAGEMENT_AVAILABLE = False
config_manager = None

'''
            content = content[:insert_point] + new_vars + content[insert_point:]
    
    # ذخیره فایل
    with open('main_new.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Fixed main_new.py")

if __name__ == "__main__":
    fix_main_file() 