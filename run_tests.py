#!/usr/bin/env python3
"""
اسکریپت اجرای تست‌های عملیاتی
==============================

این اسکریپت برای اجرای تست‌ها در محیط عملیاتی استفاده می‌شود.
"""

import os
import sys
import logging
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """تنظیم logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_execution.log', encoding='utf-8')
        ]
    )

def main():
    """اجرای اصلی"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🧪 اجرای تست‌های عملیاتی سیستم معاملاتی")
    print("=" * 50)
    
    try:
        # Import after path setup
        from utils.test_runner import run_startup_tests, run_health_checks
        
        # Run startup tests
        logger.info("🚀 اجرای تست‌های شروع سیستم...")
        startup_success = run_startup_tests()
        
        if startup_success:
            print("✅ تست‌های شروع سیستم: موفق")
            logger.info("✅ Startup tests passed")
        else:
            print("❌ تست‌های شروع سیستم: ناموفق")
            logger.error("❌ Startup tests failed")
        
        # Run health checks
        logger.info("🏥 اجرای تست‌های سلامت سیستم...")
        health_success = run_health_checks()
        
        if health_success:
            print("✅ تست‌های سلامت سیستم: موفق")
            logger.info("✅ Health checks passed")
        else:
            print("❌ تست‌های سلامت سیستم: ناموفق")
            logger.error("❌ Health checks failed")
        
        # Overall result
        overall_success = startup_success and health_success
        
        print("\n" + "=" * 50)
        if overall_success:
            print("🎉 تمام تست‌های عملیاتی با موفقیت اجرا شدند!")
            logger.info("🎉 All operational tests passed!")
        else:
            print("⚠️ برخی تست‌های عملیاتی ناموفق بودند")
            logger.warning("⚠️ Some operational tests failed")
        
        print(f"📊 زمان اجرا: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        return 0 if overall_success else 1
        
    except Exception as e:
        print(f"❌ خطا در اجرای تست‌ها: {e}")
        logger.error(f"❌ Error running tests: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 