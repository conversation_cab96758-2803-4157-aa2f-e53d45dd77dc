# -*- coding: utf-8 -*-
"""
Global Warning Suppressor
سرکوب‌کننده عمومی هشدارها
"""

import warnings
import logging
import os
import sys

# Global warning suppression
warnings.filterwarnings('ignore')

# Set environment variables
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'
os.environ['CVXPY_CLARABEL_DISABLED'] = '1'

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

# Suppress specific loggers
loggers_to_suppress = [
    'transformers',
    'urllib3',
    'requests',
    'cvxpy',
    'spacy',
    'tensorflow',
    'torch',
    'numpy',
    'pandas'
]

for logger_name in loggers_to_suppress:
    logging.getLogger(logger_name).setLevel(logging.ERROR)

def suppress_all_warnings():
    """سرکوب همه هشدارها"""
    warnings.filterwarnings('ignore')
    print("(OK) All warnings suppressed")

# Auto-suppress on import
suppress_all_warnings()
