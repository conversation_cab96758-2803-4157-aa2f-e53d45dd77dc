#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
مثال کاربردی برای استفاده از کلاس EnsembleModel
این مثال نحوه ایجاد و استفاده از مدل ensemble را نشان می‌دهد.
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# اضافه کردن مسیر اصلی پروژه به sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.ensemble_model import EnsembleModel
from models.rl_models import RLModelFactory
from env.trading_env import TradingEnv
from utils.config_override import ConfigOverride

def create_test_env():
    """
    ایجاد یک محیط آزمایشی برای تست مدل ensemble
    """
    # بارگذاری تنظیمات
    config = ConfigOverride.get_config()
    
    # تنظیم پارامترهای محیط
    env_params = {
        'symbol': 'EURUSD',
        'timeframe': 'H1',
        'start_date': '2022-01-01',
        'end_date': '2022-06-30',
        'initial_balance': 10000,
        'commission': 0.0001,
        'window_size': 20,
        'features': ['close', 'open', 'high', 'low', 'volume'],
    }
    
    # ایجاد محیط معاملاتی
    env = TradingEnv(**env_params)
    
    return env

def train_individual_models(env, models_to_train=3):
    """
    آموزش چند مدل RL مختلف برای استفاده در ensemble
    
    پارامترها:
    -----------
    env : محیط یادگیری تقویتی
    models_to_train : تعداد مدل‌هایی که باید آموزش داده شوند
    
    خروجی:
    -------
    List[str]
        لیست مسیرهای checkpoint مدل‌های آموزش‌دیده
    """
    model_factory = RLModelFactory()
    model_types = ['ppo', 'a2c', 'sac'][:models_to_train]
    checkpoint_paths = []
    
    for model_type in model_types:
        print(f"آموزش مدل {model_type}...")
        
        # ایجاد مدل
        model = model_factory.create_model(model_type, env)
        
        # آموزش مدل (با تعداد timestep کم برای مثال)
        model.learn(total_timesteps=10000)
        
        # ذخیره مدل
        checkpoint_dir = os.path.join('checkpoints', 'ensemble_example')
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        checkpoint_path = os.path.join(checkpoint_dir, f"{model_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip")
        model_factory.save_checkpoint(model, checkpoint_path)
        checkpoint_paths.append(checkpoint_path)
        
        print(f"مدل {model_type} در مسیر {checkpoint_path} ذخیره شد.")
    
    return checkpoint_paths

def test_ensemble(env, checkpoint_paths, episodes=5):
    """
    تست مدل ensemble با استفاده از مدل‌های آموزش‌دیده
    
    پارامترها:
    -----------
    env : محیط یادگیری تقویتی
    checkpoint_paths : لیست مسیرهای checkpoint مدل‌ها
    episodes : تعداد اپیزودهای تست
    """
    # ایجاد پیکربندی‌های مدل
    model_configs = []
    for i, path in enumerate(checkpoint_paths):
        model_type = os.path.basename(path).split('_')[0]
        model_configs.append({
            'model_type': model_type,
            'checkpoint_path': path
        })
    
    # ایجاد مدل ensemble
    ensemble = EnsembleModel(
        model_configs=model_configs,
        env=env,
        voting_method='weighted',
        weight_update_freq=50,
        confidence_threshold=0.6,
        diversity_weight=0.3
    )
    
    print(f"مدل ensemble با {len(ensemble.models)} مدل ایجاد شد.")
    
    # تست مدل ensemble
    total_rewards = []
    
    for episode in range(episodes):
        obs = env.reset()
        done = False
        episode_reward = 0
        step = 0
        
        while not done:
            # پیش‌بینی اقدام با استفاده از ensemble
            action, _ = ensemble.predict(obs)
            
            # اجرای اقدام در محیط
            obs, reward, done, info = env.step(action)
            
            # به‌روزرسانی پاداش در ensemble
            ensemble.update_reward(reward)
            
            episode_reward += reward
            step += 1
        
        total_rewards.append(episode_reward)
        print(f"اپیزود {episode+1}: پاداش کل = {episode_reward:.2f}")
    
    # نمایش نمودار وزن‌های مدل‌ها
    weights_fig = ensemble.plot_weights_history()
    weights_fig.savefig('ensemble_weights.png')
    
    # نمایش نمودار عملکرد
    performance_fig = ensemble.plot_performance()
    performance_fig.savefig('ensemble_performance.png')
    
    # ذخیره مدل ensemble
    ensemble_dir = os.path.join('checkpoints', 'ensemble')
    os.makedirs(ensemble_dir, exist_ok=True)
    ensemble.save(ensemble_dir)
    
    print(f"میانگین پاداش در {episodes} اپیزود: {np.mean(total_rewards):.2f}")
    print(f"مدل ensemble در مسیر {ensemble_dir} ذخیره شد.")
    print(f"نمودارها در مسیرهای ensemble_weights.png و ensemble_performance.png ذخیره شدند.")

def compare_with_individual_models(env, ensemble, checkpoint_paths, episodes=5):
    """
    مقایسه عملکرد مدل ensemble با مدل‌های فردی
    
    پارامترها:
    -----------
    env : محیط یادگیری تقویتی
    ensemble : مدل ensemble
    checkpoint_paths : لیست مسیرهای checkpoint مدل‌های فردی
    episodes : تعداد اپیزودهای تست
    """
    model_factory = RLModelFactory()
    model_rewards = {f"model_{i}": [] for i in range(len(checkpoint_paths))}
    model_rewards['ensemble'] = []
    
    for episode in range(episodes):
        # تست هر مدل فردی
        for i, path in enumerate(checkpoint_paths):
            model_type = os.path.basename(path).split('_')[0]
            model = model_factory.load_checkpoint(model_type, env, path)
            
            obs = env.reset()
            done = False
            episode_reward = 0
            
            while not done:
                action, _ = model.predict(obs)
                obs, reward, done, info = env.step(action)
                episode_reward += reward
            
            model_rewards[f"model_{i}"].append(episode_reward)
            print(f"اپیزود {episode+1}, مدل {i}: پاداش کل = {episode_reward:.2f}")
        
        # تست مدل ensemble
        obs = env.reset()
        done = False
        episode_reward = 0
        
        while not done:
            action, _ = ensemble.predict(obs)
            obs, reward, done, info = env.step(action)
            ensemble.update_reward(reward)
            episode_reward += reward
        
        model_rewards['ensemble'].append(episode_reward)
        print(f"اپیزود {episode+1}, ensemble: پاداش کل = {episode_reward:.2f}")
    
    # محاسبه میانگین پاداش‌ها
    mean_rewards = {k: np.mean(v) for k, v in model_rewards.items()}
    
    # نمایش نمودار مقایسه‌ای
    plt.figure(figsize=(10, 6))
    bars = plt.bar(mean_rewards.keys(), mean_rewards.values())
    
    # تنظیم رنگ مدل ensemble به قرمز برای تمایز
    bars[-1].set_color('red')
    
    plt.title('مقایسه عملکرد مدل‌های فردی و ensemble')
    plt.xlabel('مدل')
    plt.ylabel('میانگین پاداش')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('ensemble_comparison.png')
    
    print("نتایج مقایسه:")
    for model, reward in mean_rewards.items():
        print(f"{model}: {reward:.2f}")
    
    print(f"نمودار مقایسه در مسیر ensemble_comparison.png ذخیره شد.")

def main():
    """
    تابع اصلی برای اجرای مثال
    """
    print("شروع مثال کاربردی EnsembleModel...")
    
    # ایجاد محیط آزمایشی
    env = create_test_env()
    
    # بررسی وجود مدل‌های از پیش آموزش‌دیده
    checkpoint_dir = os.path.join('checkpoints', 'ensemble_example')
    if os.path.exists(checkpoint_dir) and len(os.listdir(checkpoint_dir)) >= 3:
        print("استفاده از مدل‌های از پیش آموزش‌دیده...")
        checkpoint_paths = [os.path.join(checkpoint_dir, f) for f in os.listdir(checkpoint_dir) if f.endswith('.zip')][:3]
    else:
        print("آموزش مدل‌های جدید...")
        checkpoint_paths = train_individual_models(env)
    
    # تست مدل ensemble
    test_ensemble(env, checkpoint_paths)
    
    # ایجاد مجدد مدل ensemble برای مقایسه
    model_configs = []
    for i, path in enumerate(checkpoint_paths):
        model_type = os.path.basename(path).split('_')[0]
        model_configs.append({
            'model_type': model_type,
            'checkpoint_path': path
        })
    
    ensemble = EnsembleModel(
        model_configs=model_configs,
        env=env,
        voting_method='weighted'
    )
    
    # مقایسه با مدل‌های فردی
    compare_with_individual_models(env, ensemble, checkpoint_paths)
    
    print("مثال کاربردی EnsembleModel به پایان رسید.")

if __name__ == "__main__":
    main() 