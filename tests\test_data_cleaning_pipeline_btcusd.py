import sys
import os
import pandas as pd
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from utils.data_cleaning_pipeline import advanced_data_cleaning

def test_advanced_data_cleaning_on_btcusd_m15():
    df = pd.read_csv(r'data/BTCUSD/M15.csv')
    print('Original shape:', df.shape)
    cleaning_log = []
    df_clean = advanced_data_cleaning(
        df,
        cleaning_sets=['basic', 'advanced', 'smart'],
        dropna_cols=['close'],
        fillna_value=0.0,
        duplicate_subset=['datetime'],
        filter_cols=['close'],
        min_val=50000,
        max_val=70000,
        outlier_cols=['close'],
        outlier_z=3.0,
        spike_cols=['close'],
        spike_threshold=5.0,
        contextual_outlier_cols=['close'],
        contextual_window=10,
        anomaly_cols=['close'],
        anomaly_method='isolation_forest',
        adaptive_vol_cols=['close'],
        adaptive_z=3.0,
        log_changes=True
    )
    print('Cleaned shape:', df_clean.shape)
    print('NaN in close:', df_clean["close"].isna().sum())
    print('Sample cleaned data:')
    print(df_clean.head())
    assert len(df_clean) <= len(df)
    assert df_clean['close'].isna().sum() == 0
    print('Test passed: advanced_data_cleaning on BTCUSD M15')

if __name__ == '__main__':
    test_advanced_data_cleaning_on_btcusd_m15()
