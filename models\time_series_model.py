"""
📈 Time Series Prediction Model using LSTM
مدل پیش‌بینی سری زمانی با استفاده از LSTM
"""

import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler
import joblib
from pathlib import Path
from typing import Any, Dict, List
from datetime import datetime

from core.base import BaseModel, ModelPrediction
from core.exceptions import ModelError, ModelLoadError
from core.logger import get_logger

logger = get_logger(__name__)

class LSTMModel(nn.Module):
    """Defines the LSTM model architecture."""
    def __init__(self, input_dim, hidden_dim, num_layers, output_dim):
        super(LSTMModel, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.lstm = nn.LSTM(input_dim, hidden_dim, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_dim, output_dim)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_dim).requires_grad_().to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_dim).requires_grad_().to(x.device)
        out, (hn, cn) = self.lstm(x, (h0.detach(), c0.detach()))
        out = self.fc(out[:, -1, :])
        return out

class TimeSeriesLSTM(BaseModel):
    """A complete LSTM-based time series forecasting model."""

    def __init__(self, model_name: str = "TimeSeriesLSTM", model_path: str = None, config: dict = None):
        super().__init__(model_name, model_type="time_series_forecasting", config=config)
        self.model_name = model_name # Keep for internal use if needed
        self.model_path = model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.scaler = MinMaxScaler(feature_range=(-1, 1))
        self.sequence_length = self.config.get("sequence_length", 60)
        
        self.internal_model = LSTMModel(
            input_dim=self.config.get("input_dim", 1),
            hidden_dim=self.config.get("hidden_dim", 50),
            num_layers=self.config.get("num_layers", 2),
            output_dim=self.config.get("output_dim", 1)
        ).to(self.device)

    def _create_dataset(self, data: np.ndarray):
        X, y = [], []
        for i in range(len(data) - self.sequence_length):
            X.append(data[i:(i + self.sequence_length)])
            y.append(data[i + self.sequence_length])
        return np.array(X), np.array(y)

    def train(self, data: np.ndarray, epochs: int = 10, batch_size: int = 64, learning_rate: float = 0.001):
        """Trains the LSTM model on the provided data."""
        logger.info(f"Starting training for {self.model_name}...")
        scaled_data = self.scaler.fit_transform(data.reshape(-1, 1))
        X, y = self._create_dataset(scaled_data)
        
        X_train = torch.from_numpy(X).float().to(self.device)
        y_train = torch.from_numpy(y).float().to(self.device)

        dataset = TensorDataset(X_train, y_train)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(self.internal_model.parameters(), lr=learning_rate)

        for epoch in range(epochs):
            for i, (inputs, labels) in enumerate(dataloader):
                outputs = self.internal_model(inputs)
                optimizer.zero_grad()
                loss = criterion(outputs, labels)
                loss.backward()
                optimizer.step()
            logger.info(f'Epoch [{epoch+1}/{epochs}], Loss: {loss.item():.4f}')
        logger.info(f"✅ Training completed for {self.model_name}.")
        self.is_loaded = True

    def predict(self, data: np.ndarray, **kwargs) -> ModelPrediction:
        """Makes a prediction for the next time step."""
        if not self.is_loaded:
            raise ModelError("Model is not trained or loaded. Call train() or load() first.")

        self.internal_model.eval()
        scaled_data = self.scaler.transform(data.reshape(-1, 1))
        last_sequence = scaled_data[-self.sequence_length:]
        input_tensor = torch.from_numpy(last_sequence).float().unsqueeze(0).to(self.device)

        with torch.no_grad():
            predicted_scaled = self.internal_model(input_tensor)
        
        predicted_value = self.scaler.inverse_transform(predicted_scaled.cpu().numpy())[0][0]

        return ModelPrediction(
            model_name=self.model_name,
            prediction=predicted_value,
            confidence=0.95,  # Default confidence for time series prediction
            timestamp=datetime.now(),
            metadata={'sequence_length': self.sequence_length}
        )

    def save(self, path: str = None):
        """Saves the model and scaler to the specified path."""
        save_path = Path(path or self.model_path or f"trained_models/{self.model_name}")
        save_path.mkdir(parents=True, exist_ok=True)
        torch.save(self.internal_model.state_dict(), save_path / "model.pth")
        joblib.dump(self.scaler, save_path / "scaler.pkl")
        logger.info(f"💾 Model saved to {save_path}")

    def load_model(self, path: str = None):
        """Loads the model and scaler from the specified path."""
        load_path = Path(path or self.model_path or f"trained_models/{self.model_name}")
        if not load_path.exists():
            raise ModelLoadError(f"Model path does not exist: {load_path}")
        
        self.internal_model.load_state_dict(torch.load(load_path / "model.pth"))
        self.scaler = joblib.load(load_path / "scaler.pkl")
        self.internal_model.to(self.device)
        self.is_loaded = True
        logger.info(f"✅ Model loaded from {load_path}")

__all__ = ['TimeSeriesLSTM']