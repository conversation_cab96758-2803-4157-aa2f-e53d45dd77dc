# مستند جامع: IntelligentMemorySystem

## مسئولیت
مدیریت حافظه هوشمند برای ذخیره و بازیابی تجربیات معاملاتی، سیگنال‌ها و داده‌های کلیدی جهت بهبود تصمیم‌گیری سیستم.

## پارامترها
- memory_size: اندازه حافظه
- replay_window: پنجره بازپخش تجربیات

## متدهای کلیدی
- store_experience: ذخیره تجربه جدید
- sample_experiences: نمونه‌گیری از تجربیات
- get_memory_stats: دریافت آمار حافظه

## نمونه کد
```python
from utils.intelligent_memory_system import IntelligentMemorySystem
ims = IntelligentMemorySystem(memory_size=1000)
ims.store_experience(signal, outcome)
memories = ims.sample_experiences(batch_size=32)
```

## مدیریت خطا
در صورت پر شدن حافظه، داده‌های قدیمی حذف می‌شوند.

## بهترین شیوه
- تجربیات کلیدی و مهم را ذخیره کنید.
- برای آموزش مدل‌ها از replay استفاده کنید.

## نمودار
- نمودار توزیع تجربیات و تاثیر آن بر عملکرد قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول در سیستم معاملاتی یکپارچه و برخی تست‌ها استفاده شده است.

## وضعیت عملیاتی
✅ عملیاتی و در جریان اصلی پروژه فعال است. 