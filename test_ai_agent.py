"""
🧪 Test AI Agent
تست AI Agent
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_ai_agent():
    """تست AI Agent"""
    try:
        from models.ai_agent import AIAgent, create_ai_agent
        
        logger.info("🚀 Starting AI Agent test...")
        
        # Create AI Agent
        agent = create_ai_agent()
        logger.info("✅ AI Agent created successfully")
        
        # Create sample market data
        dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
        np.random.seed(42)
        
        market_data = pd.DataFrame({
            'date': dates,
            'open': 100 + np.cumsum(np.random.randn(len(dates)) * 0.5),
            'high': 100 + np.cumsum(np.random.randn(len(dates)) * 0.5) + np.random.rand(1),
            'low': 100 + np.cumsum(np.random.randn(len(dates)) * 0.5) - np.random.rand(1),
            'close': 100 + np.cumsum(np.random.randn(len(dates)) * 0.5),
            'volume': np.random.randint(1000, 10000, len(dates))
        })
        
        # Add target column for testing
        market_data['target'] = market_data['close'].pct_change().shift(-1)
        
        logger.info(f"📊 Created market data with {len(market_data)} records")
        
        # Test market condition analysis
        logger.info("🔍 Testing market condition analysis...")
        market_condition = await agent.analyze_market_conditions(market_data)
        logger.info(f"✅ Market condition: {market_condition.value}")
        
        # Test model performance evaluation (simulated)
        logger.info("📈 Testing model performance evaluation...")
        
        # Simulate some model predictions
        test_predictions = np.random.randn(len(market_data)) * 0.02
        
        # Evaluate a sample model
        performance = await agent.evaluate_model_performance(
            "test_model", 
            agent.ModelType.TIMESERIES,
            market_data, 
            test_predictions
        )
        
        if performance:
            logger.info(f"✅ Model performance evaluated:")
            logger.info(f"   Accuracy: {performance.accuracy:.3f}")
            logger.info(f"   Sharpe Ratio: {performance.sharpe_ratio:.3f}")
            logger.info(f"   Win Rate: {performance.win_rate:.3f}")
            logger.info(f"   Confidence: {performance.confidence_score:.3f}")
        
        # Test best model selection
        logger.info("🎯 Testing best model selection...")
        best_models = await agent.select_best_models(market_condition)
        logger.info(f"✅ Best models for {market_condition.value}: {best_models}")
        
        # Test trading decision
        logger.info("💰 Testing trading decision...")
        
        # Create sentiment data
        sentiment_data = {
            'sentiment_score': 0.1,  # Slightly positive
            'confidence': 0.8
        }
        
        decision = await agent.make_trading_decision(market_data, sentiment_data)
        
        logger.info(f"✅ Trading decision made:")
        logger.info(f"   Action: {decision.action}")
        logger.info(f"   Confidence: {decision.confidence:.3f}")
        logger.info(f"   Models used: {decision.model_used}")
        logger.info(f"   Risk level: {decision.risk_level}")
        logger.info(f"   Expected return: {decision.expected_return:.3f}")
        logger.info(f"   Stop loss: {decision.stop_loss:.2f}")
        logger.info(f"   Take profit: {decision.take_profit:.2f}")
        
        # Test system status
        logger.info("📊 Testing system status...")
        status = agent.get_system_status()
        logger.info(f"✅ System status: {status}")
        
        # Test state saving/loading
        logger.info("💾 Testing state persistence...")
        agent.save_state("test_ai_agent_state.json")
        logger.info("✅ State saved")
        
        # Create new agent and load state
        new_agent = create_ai_agent()
        new_agent.load_state("test_ai_agent_state.json")
        logger.info("✅ State loaded")
        
        logger.info("🎉 AI Agent test completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ AI Agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Run the test
    success = asyncio.run(test_ai_agent())
    
    if success:
        print("\n🎉 AI Agent is working correctly!")
        print("✅ The system can now intelligently select and manage the best trading models")
        print("✅ Market conditions are analyzed automatically")
        print("✅ Trading decisions are made with confidence scores")
        print("✅ Risk management is integrated")
        print("✅ Performance tracking is active")
    else:
        print("\n❌ AI Agent test failed. Please check the logs above.") 