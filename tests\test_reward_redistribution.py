"""Test cases for Advanced Reward Redistribution System"""

import pytest
import numpy as np
from unittest.mock import Mock, patch
from typing import List, Dict, Any

from utils.reward_redistribution import (
    RewardRedistributor,
    AdvancedRewardRedistributor,
    LinearRewardStrategy,
    ExponentialRewardStrategy,
    AdaptiveRewardStrategy,
    RewardMemorySystem,
    RewardPredictor,
    RewardQualityMonitor,
    DynamicRewardWeighting,
    RiskAdjustedRewardSystem,
    RewardOptimizer,
    create_advanced_redistributor,
    redistribute
)


class TestRewardStrategies:
    """Test reward strategies"""
    
    def test_linear_strategy(self):
        strategy = LinearRewardStrategy(base_multiplier=1.0, slope=2.0)
        assert strategy.calculate_multiplier(0.0, {}) == 1.0
        assert strategy.calculate_multiplier(0.1, {}) == 1.2
        assert strategy.calculate_multiplier(0.5, {}) == 2.0
        assert strategy.get_strategy_name() == "Linear"
    
    def test_exponential_strategy(self):
        strategy = ExponentialRewardStrategy(base_multiplier=1.0, exponent=2.0)
        assert strategy.calculate_multiplier(0.0, {}) == 1.0
        assert abs(strategy.calculate_multiplier(0.1, {}) - 1.21) < 0.01
        assert abs(strategy.calculate_multiplier(0.5, {}) - 2.25) < 0.01
        assert strategy.get_strategy_name() == "Exponential"
    
    def test_adaptive_strategy(self):
        strategy = AdaptiveRewardStrategy(volatility_threshold=0.02, trend_threshold=0.1)
        context = {'volatility': 0.01, 'trend': 0.05}
        multiplier = strategy.calculate_multiplier(0.1, context)
        assert multiplier > 1.0
        
        context_high_vol = {'volatility': 0.04, 'trend': 0.1}
        multiplier_high_vol = strategy.calculate_multiplier(0.1, context_high_vol)
        assert multiplier_high_vol > multiplier
        assert strategy.get_strategy_name() == "Adaptive"


class TestRewardMemorySystem:
    """Test reward memory system"""
    
    def test_add_record(self):
        memory = RewardMemorySystem(max_memory_size=100)
        for i in range(10):
            memory.add_record(
                reward=i * 0.1,
                drawdown=i * 0.01,
                performance=100 + i,
                timestamp=i,
                context={'volatility': 0.01}
            )
        assert len(memory.reward_history) == 10
        assert len(memory.drawdown_history) == 10
        assert memory.reward_history[-1] == 0.9
    
    def test_memory_size_limit(self):
        memory = RewardMemorySystem(max_memory_size=5)
        for i in range(10):
            memory.add_record(i, 0.01, 100, i, {})
        assert len(memory.reward_history) == 5
        assert memory.reward_history[0] == 5
    
    def test_weighted_average_performance(self):
        memory = RewardMemorySystem(memory_decay=0.9)
        memory.add_record(1.0, 0.01, 100, 0, {})
        memory.add_record(1.0, 0.01, 110, 1, {})
        memory.add_record(1.0, 0.01, 120, 2, {})
        avg_perf = memory.get_weighted_average_performance()
        assert avg_perf > 110
    
    def test_drawdown_statistics(self):
        memory = RewardMemorySystem()
        drawdowns = [0.05, 0.10, 0.03, 0.20, 0.08]
        for i, dd in enumerate(drawdowns):
            memory.add_record(1.0, dd, 100, i, {})
        stats = memory.get_drawdown_statistics()
        assert abs(stats['mean'] - 0.092) < 0.001
        assert stats['max'] == 0.20
        assert stats['percentile_95'] > 0.15


class TestRewardPredictor:
    """Test reward predictor"""
    
    def test_model_training(self):
        predictor = RewardPredictor(model_type='rf')
        memory = RewardMemorySystem()
        
        np.random.seed(42)
        for i in range(100):
            reward = np.sin(i * 0.1) + np.random.normal(0, 0.1)
            drawdown = abs(np.random.normal(0.05, 0.02))
            performance = 100 + i * 0.5 + np.random.normal(0, 1)
            context = {
                'volatility': abs(np.random.normal(0.01, 0.005)),
                'trend': np.random.normal(0, 0.1),
                'volume': abs(np.random.normal(1, 0.2)),
                'spread': abs(np.random.normal(0.001, 0.0002))
            }
            memory.add_record(reward, drawdown, performance, i, context)
        
        predictor.train(memory)
        assert predictor.is_trained
        
        current_context = {
            'volatility': 0.01,
            'trend': 0.05,
            'volume': 1.0,
            'spread': 0.001
        }
        prediction = predictor.predict_next_reward(current_context, memory)
        assert isinstance(prediction, float)
    
    def test_insufficient_data(self):
        predictor = RewardPredictor(lookback_window=50)
        memory = RewardMemorySystem()
        
        for i in range(10):
            memory.add_record(i * 0.1, 0.01, 100, i, {})
        
        predictor.train(memory)
        assert not predictor.is_trained


class TestRewardQualityMonitor:
    """Test reward quality monitor"""
    
    def test_quality_calculation(self):
        monitor = RewardQualityMonitor(quality_threshold=0.5)
        
        rewards = list(range(20))
        performance = [r * 2 + np.random.normal(0, 0.5) for r in rewards]
        
        quality = monitor.calculate_reward_quality(rewards, performance)
        assert quality > 0.8
        assert monitor.is_quality_acceptable()
    
    def test_quality_trend(self):
        monitor = RewardQualityMonitor()
        
        for i in range(10):
            quality = 0.4 + i * 0.05
            monitor.quality_history.append(quality)
        
        assert monitor.get_quality_trend() == "improving"
        
        monitor.quality_history = [0.8 - i * 0.05 for i in range(10)]
        assert monitor.get_quality_trend() == "declining"


class TestAdvancedRewardRedistributor:
    """Test advanced reward redistributor"""
    
    @pytest.fixture
    def redistributor(self):
        return AdvancedRewardRedistributor(
            strategy=AdaptiveRewardStrategy(),
            max_multiplier=5.0,
            min_multiplier=0.1
        )
    
    def test_single_redistribution(self, redistributor):
        reward = 1.0
        equity_curve = [100, 105, 103, 98, 95]
        redistributed = redistributor.redistribute_single(
            reward, equity_curve, action="buy", 
            additional_context={'volatility': 0.02}
        )
        assert redistributed > reward
        assert redistributed <= reward * redistributor.max_multiplier
    
    def test_batch_redistribution(self, redistributor):
        rewards = [1.0, 0.5, -0.5, 2.0, 1.5]
        equity_curve = [100, 105, 110, 108, 106, 104]
        actions = ["buy", "sell", "hold", "buy", "sell"]
        redistributed = redistributor.redistribute_batch(
            rewards, equity_curve, actions
        )
        assert len(redistributed) == len(rewards)
        assert all(isinstance(r, float) for r in redistributed)
    
    def test_performance_summary(self, redistributor):
        for i in range(10):
            redistributor.redistribute_single(
                1.0, [100 + i], action="buy"
            )
        
        summary = redistributor.get_performance_summary()
        assert 'redistribution_stats' in summary
        assert 'memory_stats' in summary
        assert 'quality_stats' in summary
        assert summary['step_count'] == 10


class TestBackwardCompatibility:
    """Test backward compatibility"""
    
    def test_simple_redistributor(self):
        redistributor = RewardRedistributor(
            thresholds=[0.05, 0.10, 0.20],
            multipliers=[1.0, 1.2, 1.5, 2.0],
            cap=3.0
        )
        
        rewards = [1.0, 2.0, 3.0]
        equity_curve = [100, 98, 95]
        
        redistributed = redistributor.redistribute(rewards, equity_curve)
        assert len(redistributed) == len(rewards)
        assert redistributed[0] == rewards[0] * 1.2
    
    def test_module_function(self):
        rewards = [1.0, 1.5, 2.0]
        equity_curve = [100, 105, 102]
        
        redistributed = redistribute(rewards, equity_curve)
        assert len(redistributed) == len(rewards)
        assert all(isinstance(r, float) for r in redistributed)


class TestFactoryFunction:
    """Test factory function"""
    
    def test_create_advanced_redistributor(self):
        linear_redis = create_advanced_redistributor('linear')
        assert linear_redis.strategy.get_strategy_name() == "Linear"
        
        exp_redis = create_advanced_redistributor('exponential')
        assert exp_redis.strategy.get_strategy_name() == "Exponential"
        
        adaptive_redis = create_advanced_redistributor('adaptive')
        assert adaptive_redis.strategy.get_strategy_name() == "Adaptive"
    
    def test_invalid_strategy(self):
        with pytest.raises(ValueError):
            create_advanced_redistributor('invalid_strategy')


if __name__ == "__main__":
    pytest.main(["-v", __file__])
