from typing import Any, Dict

def validate_config(config: Dict[str, Any]) -> None:
    """اعتبارسنجی ساختار و مقادیر کلیدی config پروژه."""
    required_keys = ["lot_size", "stop_loss", "take_profit", "indicators"]
    for key in required_keys:
        if key not in config:
            raise ValueError(f"Missing required config key: {key}")
    if not (0 < config["lot_size"] < 100):
        raise ValueError("lot_size باید بین ۰ و ۱۰۰ باشد.")
    if not (1 <= config["stop_loss"] <= 1000):
        raise ValueError("stop_loss باید بین ۱ و ۱۰۰۰ باشد.")
    if not (1 <= config["take_profit"] <= 1000):
        raise ValueError("take_profit باید بین ۱ و ۱۰۰۰ باشد.")
    if not isinstance(config["indicators"], dict):
        raise ValueError("indicators باید دیکشنری باشد.")
