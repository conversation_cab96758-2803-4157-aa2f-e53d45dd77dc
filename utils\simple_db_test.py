#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Simple Database Transaction Test
"""

import os
import sys

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_simple_database():
    """تست ساده database transaction manager"""
    print("🧪 Testing Database Transaction Manager...")
    
    try:
        # Test 1: Import
        print("1️⃣ Testing import...")
        from core.database_transaction_manager import (
            get_database_manager,
            transaction,
            TradingSignal
        )
        print("   ✓ Import successful")
        
        # Test 2: Get manager
        print("2️⃣ Testing database manager...")
        db_manager = get_database_manager("sqlite:///simple_test.db")
        print("   ✓ Database manager obtained")
        
        # Test 3: Simple transaction
        print("3️⃣ Testing simple transaction...")
        with transaction() as (session, metrics):
            signal = TradingSignal(
                symbol="EURUSD",
                signal_type="BUY",
                price=1.0950,
                confidence=0.85,
                source="test"
            )
            session.add(signal)
            session.flush()
            print(f"   ✓ Signal inserted: {signal.id}")
            print(f"   ✓ Transaction: {metrics.transaction_id}")
        
        # Test 4: Query
        print("4️⃣ Testing query...")
        with transaction() as (session, metrics):
            signals = session.query(TradingSignal).filter_by(symbol="EURUSD").all()
            print(f"   ✓ Found {len(signals)} signals")
            for signal in signals:
                print(f"   ✓ Signal: {signal.symbol} - {signal.signal_type}")
        
        # Test 5: Statistics
        print("5️⃣ Testing statistics...")
        stats = db_manager.get_system_health()
        print(f"   ✓ Total transactions: {stats['transaction_stats']['total_transactions']}")
        print(f"   ✓ Success rate: {stats['transaction_stats']['success_rate']:.2f}%")
        
        # Cleanup
        print("6️⃣ Cleanup...")
        db_manager.close()
        
        if os.path.exists("simple_test.db"):
            os.remove("simple_test.db")
        
        print("\n🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_database()
    print(f"\n✅ Database Transaction Manager is {'ready' if success else 'not ready'}!") 