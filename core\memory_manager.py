#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 Intelligent Memory Management System for Pearl-3x7B
سیستم هوشمند مدیریت حافظه مخصوص Pearl-3x7B

مطابق نقشه راه گنج:
- جلوگیری از Memory Leak در آموزش مدل‌ها
- مدیریت هوشمند منابع برای AI Models
- بهینه‌سازی حافظه برای عملیات سنگین
- نظارت real-time بر مصرف حافظه
"""

import gc
import os
import sys
import time
import psutil
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from collections import deque
import queue
from weakref import WeakSet
import logging
from contextlib import contextmanager

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class MemoryLevel(Enum):
    """سطح مصرف حافظه"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"

class MemoryPoolType(Enum):
    """نوع Memory Pool"""
    NUMPY_ARRAYS = "numpy_arrays"
    DATAFRAMES = "dataframes"
    TENSORS = "tensors"
    BUFFERS = "buffers"
    CACHE = "cache"
    # Pearl-3x7B specific pools
    MODEL_WEIGHTS = "model_weights"
    TRAINING_DATA = "training_data"
    GRADIENTS = "gradients"
    EMBEDDINGS = "embeddings"
    ATTENTION_MAPS = "attention_maps"

@dataclass
class MemoryStats:
    """آمار حافظه"""
    timestamp: datetime
    total_memory: float
    used_memory: float
    available_memory: float
    memory_percent: float
    swap_memory: float
    process_memory: float
    memory_level: MemoryLevel
    gc_count: int
    active_objects: int
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class MemoryAlert:
    """هشدار حافظه"""
    timestamp: datetime
    level: MemoryLevel
    message: str
    current_usage: float
    threshold: float
    action_taken: str

class MemoryPool:
    """Memory Pool برای بازیافت objects"""
    
    def __init__(self, pool_type: MemoryPoolType, max_size: int = 100):
        self.pool_type = pool_type
        self.max_size = max_size
        self.pool = deque(maxlen=max_size)
        self.hits = 0
        self.misses = 0
        self.lock = threading.Lock()
        
    def get_object(self, size: Optional[int] = None) -> Optional[Any]:
        """دریافت object از pool"""
        with self.lock:
            if self.pool:
                self.hits += 1
                return self.pool.popleft()
            else:
                self.misses += 1
                return None
    
    def return_object(self, obj: Any) -> bool:
        """بازگرداندن object به pool"""
        with self.lock:
            if len(self.pool) < self.max_size:
                # Clear object if possible
                if hasattr(obj, 'clear'):
                    obj.clear()
                elif hasattr(obj, '__del__'):
                    pass  # Let it handle cleanup
                
                self.pool.append(obj)
                return True
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """آمار pool"""
        total_requests = self.hits + self.misses
        hit_rate = (self.hits / total_requests) * 100 if total_requests > 0 else 0
        
        return {
            "pool_type": self.pool_type.value,
            "size": len(self.pool),
            "max_size": self.max_size,
            "hits": self.hits,
            "misses": self.misses,
            "hit_rate": round(hit_rate, 2)
        }
    
    def clear(self):
        """پاک کردن pool"""
        with self.lock:
            self.pool.clear()
            self.hits = 0
            self.misses = 0

    def optimize_for_training(self):
        """بهینه‌سازی pool برای آموزش"""
        with self.lock:
            # Clear pool to free memory
            self.pool.clear()

            # Reduce max size during training to save memory
            if self.pool_type in [MemoryPoolType.TENSORS, MemoryPoolType.NUMPY_ARRAYS]:
                self.max_size = min(self.max_size, 50)  # Reduce for heavy objects
            else:
                self.max_size = min(self.max_size, 20)  # Reduce for other objects

class AIModelMemoryManager:
    """مدیر حافظه مخصوص مدل‌های AI"""

    def __init__(self):
        self.model_memory_usage = {}
        self.training_sessions = {}
        self.model_pools = {}
        self.logger = logging.getLogger(__name__ + ".AIModelMemoryManager")

    def register_model(self, model_name: str, model_size_mb: float):
        """ثبت مدل جدید"""
        self.model_memory_usage[model_name] = {
            'size_mb': model_size_mb,
            'loaded_at': datetime.now(),
            'peak_usage': model_size_mb,
            'current_usage': model_size_mb,
            'training_sessions': 0
        }
        self.logger.info(f"🤖 Registered AI model: {model_name} ({model_size_mb:.1f} MB)")

    def start_training_session(self, model_name: str, batch_size: int, sequence_length: int):
        """شروع جلسه آموزش"""
        session_id = f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # تخمین حافظه مورد نیاز
        estimated_memory = self._estimate_training_memory(batch_size, sequence_length)

        self.training_sessions[session_id] = {
            'model_name': model_name,
            'batch_size': batch_size,
            'sequence_length': sequence_length,
            'estimated_memory': estimated_memory,
            'start_time': datetime.now(),
            'peak_memory': 0,
            'status': 'active'
        }

        if model_name in self.model_memory_usage:
            self.model_memory_usage[model_name]['training_sessions'] += 1

        self.logger.info(f"🎯 Started training session: {session_id} (Est. {estimated_memory:.1f} MB)")
        return session_id

    def end_training_session(self, session_id: str):
        """پایان جلسه آموزش"""
        if session_id in self.training_sessions:
            session = self.training_sessions[session_id]
            session['status'] = 'completed'
            session['end_time'] = datetime.now()
            session['duration'] = (session['end_time'] - session['start_time']).total_seconds()

            self.logger.info(f"✅ Completed training session: {session_id} "
                           f"(Duration: {session['duration']:.1f}s, Peak: {session['peak_memory']:.1f} MB)")

    def _estimate_training_memory(self, batch_size: int, sequence_length: int) -> float:
        """تخمین حافظه مورد نیاز برای آموزش"""
        # تخمین تقریبی بر اساس batch size و sequence length
        base_memory = batch_size * sequence_length * 4 / 1024 / 1024  # 4 bytes per float, convert to MB
        gradient_memory = base_memory * 2  # gradients roughly 2x model size
        optimizer_memory = base_memory * 1.5  # optimizer states

        total_estimated = base_memory + gradient_memory + optimizer_memory
        return total_estimated

    def cleanup_model_memory(self, model_name: str):
        """پاکسازی حافظه مدل"""
        if model_name in self.model_memory_usage:
            # Force garbage collection for model-specific objects
            import gc
            collected = gc.collect()

            self.model_memory_usage[model_name]['current_usage'] = 0
            self.logger.info(f"🧹 Cleaned memory for model: {model_name} (Collected {collected} objects)")

    def get_model_memory_stats(self) -> Dict[str, Any]:
        """آمار حافظه مدل‌ها"""
        return {
            'models': self.model_memory_usage.copy(),
            'active_training_sessions': len([s for s in self.training_sessions.values() if s['status'] == 'active']),
            'total_training_sessions': len(self.training_sessions),
            'total_model_memory': sum(m['current_usage'] for m in self.model_memory_usage.values())
        }

class ResourceMonitor:
    """نظارت بر منابع سیستم"""

    def __init__(self):
        self.process = psutil.Process()
        self.start_time = datetime.now()
        self.peak_memory = 0
        self.memory_history = deque(maxlen=1000)
        
    def get_memory_info(self) -> Dict[str, Any]:
        """اطلاعات حافظه"""
        # System memory
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # Process memory
        process_memory = self.process.memory_info()
        
        # Update peak memory
        current_process_memory = process_memory.rss / 1024 / 1024  # MB
        if current_process_memory > self.peak_memory:
            self.peak_memory = current_process_memory
            
        return {
            "system": {
                "total": memory.total / 1024 / 1024,  # MB
                "used": memory.used / 1024 / 1024,   # MB
                "available": memory.available / 1024 / 1024,  # MB
                "percent": memory.percent
            },
            "swap": {
                "total": swap.total / 1024 / 1024,  # MB
                "used": swap.used / 1024 / 1024,   # MB
                "percent": swap.percent
            },
            "process": {
                "rss": process_memory.rss / 1024 / 1024,  # MB
                "vms": process_memory.vms / 1024 / 1024,  # MB
                "peak": self.peak_memory
            }
        }
    
    def get_cpu_info(self) -> Dict[str, Any]:
        """اطلاعات CPU"""
        return {
            "percent": psutil.cpu_percent(interval=1),
            "count": psutil.cpu_count(),
            "load_avg": os.getloadavg() if hasattr(os, 'getloadavg') else None
        }
    
    def get_disk_info(self) -> Dict[str, Any]:
        """اطلاعات دیسک"""
        disk = psutil.disk_usage('/')
        return {
            "total": disk.total / 1024 / 1024 / 1024,  # GB
            "used": disk.used / 1024 / 1024 / 1024,   # GB
            "free": disk.free / 1024 / 1024 / 1024,   # GB
            "percent": (disk.used / disk.total) * 100
        }

class ConcurrentOperationManager:
    """مدیر عملیات همزمان thread-safe"""

    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.active_operations = {}
        self.operation_queue = queue.Queue()
        self.lock = threading.RLock()
        self.operation_counter = 0

    def submit_operation(self, operation_name: str, func: Callable, *args, **kwargs) -> str:
        """ارسال عملیات برای اجرای همزمان"""
        with self.lock:
            self.operation_counter += 1
            operation_id = f"{operation_name}_{self.operation_counter}_{int(time.time())}"

            future = self.executor.submit(func, *args, **kwargs)
            self.active_operations[operation_id] = {
                'future': future,
                'operation_name': operation_name,
                'start_time': time.time(),
                'status': 'running'
            }

            return operation_id

    def get_operation_status(self, operation_id: str) -> Dict[str, Any]:
        """دریافت وضعیت عملیات"""
        with self.lock:
            if operation_id not in self.active_operations:
                return {'status': 'not_found'}

            operation = self.active_operations[operation_id]
            future = operation['future']

            if future.done():
                if future.exception():
                    status = 'failed'
                    result = {'error': str(future.exception())}
                else:
                    status = 'completed'
                    result = {'result': future.result()}

                operation['status'] = status
                operation['end_time'] = time.time()
                operation['duration'] = operation['end_time'] - operation['start_time']
            else:
                status = 'running'
                result = {}

            return {
                'operation_id': operation_id,
                'operation_name': operation['operation_name'],
                'status': status,
                'start_time': operation['start_time'],
                'duration': operation.get('duration', time.time() - operation['start_time']),
                **result
            }

    def wait_for_operation(self, operation_id: str, timeout: float = None) -> Any:
        """انتظار برای تکمیل عملیات"""
        with self.lock:
            if operation_id not in self.active_operations:
                raise ValueError(f"Operation {operation_id} not found")

            future = self.active_operations[operation_id]['future']

        return future.result(timeout=timeout)

    def cancel_operation(self, operation_id: str) -> bool:
        """لغو عملیات"""
        with self.lock:
            if operation_id not in self.active_operations:
                return False

            future = self.active_operations[operation_id]['future']
            cancelled = future.cancel()

            if cancelled:
                self.active_operations[operation_id]['status'] = 'cancelled'

            return cancelled

    def get_active_operations(self) -> List[Dict[str, Any]]:
        """دریافت لیست عملیات فعال"""
        with self.lock:
            active_ops = []
            for op_id, operation in self.active_operations.items():
                if operation['status'] == 'running':
                    active_ops.append({
                        'operation_id': op_id,
                        'operation_name': operation['operation_name'],
                        'duration': time.time() - operation['start_time']
                    })
            return active_ops

    def cleanup_completed_operations(self):
        """پاکسازی عملیات تکمیل شده"""
        with self.lock:
            completed_ops = [
                op_id for op_id, operation in self.active_operations.items()
                if operation['status'] in ['completed', 'failed', 'cancelled']
            ]

            for op_id in completed_ops:
                del self.active_operations[op_id]

            return len(completed_ops)

    def shutdown(self, wait: bool = True):
        """خاموش کردن executor"""
        self.executor.shutdown(wait=wait)

class IntelligentMemoryManager:
    """مدیر هوشمند حافظه برای Pearl-3x7B"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Thread safety
        self.lock = threading.RLock()
        self.concurrent_manager = ConcurrentOperationManager(max_workers=4)

        self.resource_monitor = ResourceMonitor()
        self.ai_model_manager = AIModelMemoryManager()
        self.memory_pools: Dict[MemoryPoolType, MemoryPool] = {}
        self.memory_history: List[MemoryStats] = []
        self.alerts: List[MemoryAlert] = []
        self.active_objects: WeakSet = WeakSet()

        # Pearl-3x7B specific thresholds (more conservative)
        self.thresholds = {
            MemoryLevel.LOW: 40.0,      # Start monitoring earlier
            MemoryLevel.NORMAL: 60.0,   # More conservative
            MemoryLevel.HIGH: 75.0,     # Earlier intervention
            MemoryLevel.CRITICAL: 90.0  # Emergency cleanup
        }

        # Configuration optimized for AI training
        self.monitoring_enabled = True
        self.auto_gc_enabled = True
        self.gc_threshold = 70.0  # More aggressive for AI workloads
        self.monitoring_interval = 30  # More frequent monitoring for training
        self.max_history_size = 1000

        # Pearl-3x7B specific settings
        self.training_mode = False
        self.model_training_memory_limit = 0.8  # 80% of available memory
        self.emergency_cleanup_enabled = True

        # Initialize memory pools
        self._initialize_pools()

        # Start monitoring
        self._start_monitoring()

        # GC optimization for AI workloads
        self._optimize_gc_for_ai()

    def optimize_for_training(self):
        """بهینه‌سازی حافظه برای آموزش مدل"""
        with self.lock:
            self.logger.info("🚀 Optimizing memory for model training...")

            # Enable training mode
            self.training_mode = True

            # More aggressive GC for training
            self.gc_threshold = 60.0
            self.monitoring_interval = 15

            # Force garbage collection
            self.force_gc()

            # Clear unnecessary caches
            self._clear_training_caches()

            # Optimize memory pools for training
            self._optimize_pools_for_training()

            self.logger.info("✅ Memory optimized for training")

    def _clear_training_caches(self):
        """پاکسازی cache های غیرضروری برای آموزش"""
        try:
            # Clear Python caches
            import sys
            if hasattr(sys, '_clear_type_cache'):
                sys._clear_type_cache()

            # Clear torch caches if available
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except ImportError:
                pass

            self.logger.info("🧹 Training caches cleared")
        except Exception as e:
            self.logger.warning(f"Cache clearing failed: {e}")

    def _optimize_pools_for_training(self):
        """بهینه‌سازی memory pools برای آموزش"""
        try:
            for pool in self.memory_pools.values():
                pool.optimize_for_training()
            self.logger.info("🎯 Memory pools optimized for training")
        except Exception as e:
            self.logger.warning(f"Pool optimization failed: {e}")

    def cleanup_after_training(self):
        """پاکسازی بعد از آموزش"""
        with self.lock:
            self.logger.info("🧹 Cleaning up after training...")

            # Disable training mode
            self.training_mode = False

            # Reset thresholds
            self.gc_threshold = 70.0
            self.monitoring_interval = 30

            # Force cleanup
            self.force_gc()
            self._clear_training_caches()

            self.logger.info("✅ Post-training cleanup completed")
    
    def _initialize_pools(self):
        """مقداردهی اولیه pools"""
        for pool_type in MemoryPoolType:
            self.memory_pools[pool_type] = MemoryPool(pool_type)
    
    def _optimize_gc_for_ai(self):
        """بهینه‌سازی Garbage Collector برای AI workloads"""
        # Tune GC thresholds for AI training (more aggressive)
        gc.set_threshold(500, 8, 8)  # More frequent collection for AI

        # Disable debug flags for production
        # gc.set_debug(gc.DEBUG_STATS)  # DISABLED - causes excessive logging

        self.logger.info("🤖 GC optimized for AI workloads")

    def enable_training_mode(self, model_name: str = None):
        """فعال‌سازی حالت آموزش"""
        self.training_mode = True
        self.monitoring_interval = 15  # More frequent monitoring during training
        self.gc_threshold = 60.0  # More aggressive cleanup during training

        if model_name:
            self.logger.info(f"🎯 Training mode enabled for model: {model_name}")
        else:
            self.logger.info("🎯 Training mode enabled")

    def disable_training_mode(self):
        """غیرفعال‌سازی حالت آموزش"""
        self.training_mode = False
        self.monitoring_interval = 60  # Back to normal monitoring
        self.gc_threshold = 80.0  # Less aggressive cleanup

        # Cleanup after training
        self.force_comprehensive_cleanup()
        self.logger.info("✅ Training mode disabled, cleanup completed")

    def force_comprehensive_cleanup(self):
        """پاکسازی جامع حافظه"""
        self.logger.info("🧹 Starting comprehensive memory cleanup...")

        # Clear all pools
        self.clear_all_pools()

        # Multiple GC passes for thorough cleanup
        total_collected = 0
        for i in range(3):
            collected = gc.collect()
            total_collected += collected
            if collected == 0:
                break

        # Clear AI model memory
        for model_name in list(self.ai_model_manager.model_memory_usage.keys()):
            self.ai_model_manager.cleanup_model_memory(model_name)

        self.logger.info(f"✅ Comprehensive cleanup completed (Collected {total_collected} objects)")
        return total_collected

    def check_training_memory_availability(self, required_memory_mb: float) -> bool:
        """بررسی در دسترس بودن حافظه برای آموزش"""
        current_stats = self.get_memory_stats()
        available_memory = current_stats.available_memory

        # Reserve some memory for system operations
        usable_memory = available_memory * self.model_training_memory_limit

        if required_memory_mb <= usable_memory:
            self.logger.info(f"✅ Memory check passed: {required_memory_mb:.1f} MB available")
            return True
        else:
            self.logger.warning(f"⚠️ Insufficient memory: Need {required_memory_mb:.1f} MB, "
                              f"Available {usable_memory:.1f} MB")
            return False

    def optimize_for_model_training(self, model_name: str, batch_size: int, sequence_length: int):
        """بهینه‌سازی برای آموزش مدل"""
        self.logger.info(f"🎯 Optimizing memory for training {model_name}")

        # Enable training mode
        self.enable_training_mode(model_name)

        # Register training session
        session_id = self.ai_model_manager.start_training_session(
            model_name, batch_size, sequence_length
        )

        # Pre-training cleanup
        self.force_comprehensive_cleanup()

        # Check memory availability
        estimated_memory = self.ai_model_manager._estimate_training_memory(batch_size, sequence_length)
        if not self.check_training_memory_availability(estimated_memory):
            self.logger.error("❌ Insufficient memory for training")
            return None

        return session_id
    
    def get_pool(self, pool_type: MemoryPoolType) -> MemoryPool:
        """دریافت memory pool"""
        return self.memory_pools.get(pool_type)
    
    def register_object(self, obj: Any):
        """ثبت object برای tracking"""
        self.active_objects.add(obj)
    
    def force_gc(self) -> Dict[str, Any]:
        """اجبار Garbage Collection"""
        start_time = time.time()
        
        # Get pre-GC stats
        pre_memory = self.resource_monitor.get_memory_info()
        pre_objects = len(gc.get_objects())
        
        # Force collection
        collected = gc.collect()
        
        # Get post-GC stats
        post_memory = self.resource_monitor.get_memory_info()
        post_objects = len(gc.get_objects())
        
        duration = time.time() - start_time
        
        result = {
            "collected_objects": collected,
            "objects_before": pre_objects,
            "objects_after": post_objects,
            "objects_freed": pre_objects - post_objects,
            "memory_before": pre_memory["process"]["rss"],
            "memory_after": post_memory["process"]["rss"],
            "memory_freed": pre_memory["process"]["rss"] - post_memory["process"]["rss"],
            "duration": duration
        }
        
        self.logger.info(f"Garbage collection completed: {result}")
        return result
    
    def get_memory_stats(self) -> MemoryStats:
        """دریافت آمار حافظه"""
        memory_info = self.resource_monitor.get_memory_info()
        system_memory = memory_info["system"]
        process_memory = memory_info["process"]
        
        # Determine memory level
        memory_level = self._determine_memory_level(system_memory["percent"])
        
        stats = MemoryStats(
            timestamp=datetime.now(),
            total_memory=system_memory["total"],
            used_memory=system_memory["used"],
            available_memory=system_memory["available"],
            memory_percent=system_memory["percent"],
            swap_memory=memory_info["swap"]["used"],
            process_memory=process_memory["rss"],
            memory_level=memory_level,
            gc_count=len(gc.garbage),
            active_objects=len(self.active_objects)
        )
        
        return stats
    
    def _determine_memory_level(self, memory_percent: float) -> MemoryLevel:
        """تعیین سطح حافظه"""
        if memory_percent >= self.thresholds[MemoryLevel.CRITICAL]:
            return MemoryLevel.CRITICAL
        elif memory_percent >= self.thresholds[MemoryLevel.HIGH]:
            return MemoryLevel.HIGH
        elif memory_percent >= self.thresholds[MemoryLevel.NORMAL]:
            return MemoryLevel.NORMAL
        else:
            return MemoryLevel.LOW
    
    def check_memory_pressure(self) -> bool:
        """بررسی فشار حافظه"""
        stats = self.get_memory_stats()

        # Store in history
        self.memory_history.append(stats)

        # Check for memory leaks
        leak_detected = self._detect_memory_leaks()
        if leak_detected:
            self.logger.warning("🚨 Potential memory leak detected!")
            self._handle_memory_leak()

        return leak_detected

    def _detect_memory_leaks(self) -> bool:
        """تشخیص Memory Leak پیشرفته"""
        if len(self.memory_history) < 10:
            return False

        # Get recent memory usage trend
        recent_stats = self.memory_history[-10:]
        memory_values = [stat.process_memory for stat in recent_stats]

        # Check for consistent memory growth
        growth_count = 0
        for i in range(1, len(memory_values)):
            if memory_values[i] > memory_values[i-1]:
                growth_count += 1

        # If memory consistently grows in 80% of recent samples
        if growth_count >= 8:
            # Check if growth is significant
            memory_growth = memory_values[-1] - memory_values[0]
            growth_percentage = (memory_growth / memory_values[0]) * 100

            if growth_percentage > 20:  # More than 20% growth
                self.logger.warning(
                    f"🚨 Memory leak detected: {growth_percentage:.1f}% growth "
                    f"({memory_growth / (1024*1024):.1f} MB) in recent samples"
                )
                return True

        # Check for excessive object count growth
        object_counts = [stat.active_objects for stat in recent_stats]
        if len(object_counts) >= 5:
            object_growth = object_counts[-1] - object_counts[0]
            if object_growth > 1000:  # More than 1000 new objects
                self.logger.warning(
                    f"🚨 Object leak detected: {object_growth} new objects in recent samples"
                )
                return True

        # Check garbage collection efficiency
        gc_counts = [stat.gc_count for stat in recent_stats]
        if len(gc_counts) >= 5:
            avg_gc_count = sum(gc_counts) / len(gc_counts)
            if avg_gc_count > 100:  # High garbage count
                self.logger.warning(
                    f"🚨 GC inefficiency detected: Average {avg_gc_count:.1f} uncollectable objects"
                )
                return True

        return False

    def _handle_memory_leak(self):
        """مدیریت Memory Leak"""
        self.logger.info("🔧 Handling detected memory leak...")

        # Force comprehensive cleanup
        self.force_comprehensive_cleanup()

        # Clear object references
        self.active_objects.clear()

        # Force garbage collection multiple times
        for i in range(3):
            collected = gc.collect()
            self.logger.info(f"GC round {i+1}: collected {collected} objects")

        # Clear memory pools
        for pool in self.memory_pools.values():
            pool.clear()

        # Log memory state after cleanup
        stats = self.get_memory_stats()
        self.logger.info(
            f"✅ Memory leak handling completed. "
            f"Current memory: {stats.process_memory / (1024*1024):.1f} MB"
        )

    def get_memory_leak_report(self) -> Dict[str, Any]:
        """گزارش تشخیص Memory Leak"""
        if len(self.memory_history) < 5:
            return {"status": "insufficient_data", "samples": len(self.memory_history)}

        recent_stats = self.memory_history[-10:] if len(self.memory_history) >= 10 else self.memory_history

        # Memory trend analysis
        memory_values = [stat.process_memory for stat in recent_stats]
        memory_trend = "stable"

        if len(memory_values) >= 3:
            start_memory = memory_values[0]
            end_memory = memory_values[-1]
            growth_percentage = ((end_memory - start_memory) / start_memory) * 100

            if growth_percentage > 10:
                memory_trend = "increasing"
            elif growth_percentage < -10:
                memory_trend = "decreasing"

        # Object count analysis
        object_counts = [stat.active_objects for stat in recent_stats]
        object_trend = "stable"

        if len(object_counts) >= 3:
            start_objects = object_counts[0]
            end_objects = object_counts[-1]
            object_growth = end_objects - start_objects

            if object_growth > 500:
                object_trend = "increasing"
            elif object_growth < -500:
                object_trend = "decreasing"

        # GC efficiency analysis
        gc_counts = [stat.gc_count for stat in recent_stats]
        avg_gc_count = sum(gc_counts) / len(gc_counts) if gc_counts else 0

        gc_efficiency = "good"
        if avg_gc_count > 100:
            gc_efficiency = "poor"
        elif avg_gc_count > 50:
            gc_efficiency = "moderate"

        return {
            "status": "analyzed",
            "samples_analyzed": len(recent_stats),
            "memory_trend": memory_trend,
            "memory_growth_mb": (memory_values[-1] - memory_values[0]) / (1024*1024) if len(memory_values) >= 2 else 0,
            "object_trend": object_trend,
            "object_growth": object_counts[-1] - object_counts[0] if len(object_counts) >= 2 else 0,
            "gc_efficiency": gc_efficiency,
            "avg_gc_count": avg_gc_count,
            "current_memory_mb": memory_values[-1] / (1024*1024) if memory_values else 0,
            "leak_risk": self._assess_leak_risk(memory_trend, object_trend, gc_efficiency)
        }

    def _assess_leak_risk(self, memory_trend: str, object_trend: str, gc_efficiency: str) -> str:
        """ارزیابی ریسک Memory Leak"""
        risk_score = 0

        if memory_trend == "increasing":
            risk_score += 3
        if object_trend == "increasing":
            risk_score += 2
        if gc_efficiency == "poor":
            risk_score += 3
        elif gc_efficiency == "moderate":
            risk_score += 1

        if risk_score >= 6:
            return "high"
        elif risk_score >= 3:
            return "medium"
        else:
            return "low"

    def execute_concurrent_cleanup(self) -> Dict[str, Any]:
        """اجرای پاکسازی همزمان thread-safe"""
        with self.lock:
            cleanup_operations = []

            # Submit cleanup operations concurrently
            gc_op = self.concurrent_manager.submit_operation(
                "garbage_collection", self.force_gc
            )
            cleanup_operations.append(('gc', gc_op))

            pool_cleanup_op = self.concurrent_manager.submit_operation(
                "pool_cleanup", self._cleanup_all_pools
            )
            cleanup_operations.append(('pools', pool_cleanup_op))

            object_cleanup_op = self.concurrent_manager.submit_operation(
                "object_cleanup", self._cleanup_active_objects
            )
            cleanup_operations.append(('objects', object_cleanup_op))

            # Wait for all operations to complete
            results = {}
            for op_name, op_id in cleanup_operations:
                try:
                    result = self.concurrent_manager.wait_for_operation(op_id, timeout=30)
                    results[op_name] = {'status': 'success', 'result': result}
                except Exception as e:
                    results[op_name] = {'status': 'failed', 'error': str(e)}

            # Cleanup completed operations
            self.concurrent_manager.cleanup_completed_operations()

            return results

    def _cleanup_all_pools(self) -> Dict[str, Any]:
        """پاکسازی تمام memory pools"""
        results = {}
        for pool_type, pool in self.memory_pools.items():
            try:
                pool.clear()
                results[pool_type.value] = 'cleared'
            except Exception as e:
                results[pool_type.value] = f'error: {e}'
        return results

    def _cleanup_active_objects(self) -> Dict[str, Any]:
        """پاکسازی active objects"""
        initial_count = len(self.active_objects)
        self.active_objects.clear()
        return {
            'initial_count': initial_count,
            'final_count': len(self.active_objects),
            'cleared': initial_count
        }

    def get_concurrent_operations_status(self) -> Dict[str, Any]:
        """دریافت وضعیت عملیات همزمان"""
        with self.lock:
            active_ops = self.concurrent_manager.get_active_operations()
            return {
                'active_operations_count': len(active_ops),
                'active_operations': active_ops,
                'max_workers': self.concurrent_manager.max_workers
            }

    def force_comprehensive_cleanup_concurrent(self) -> Dict[str, Any]:
        """پاکسازی جامع با استفاده از concurrent operations"""
        self.logger.info("🧹 Starting concurrent comprehensive memory cleanup...")

        start_time = time.time()
        initial_stats = self.get_memory_stats()

        # Execute concurrent cleanup
        cleanup_results = self.execute_concurrent_cleanup()

        # Final garbage collection
        final_gc = self.force_gc()

        final_stats = self.get_memory_stats()
        duration = time.time() - start_time

        memory_freed = initial_stats.process_memory - final_stats.process_memory

        result = {
            'duration': duration,
            'memory_freed_mb': memory_freed / (1024 * 1024),
            'initial_memory_mb': initial_stats.process_memory / (1024 * 1024),
            'final_memory_mb': final_stats.process_memory / (1024 * 1024),
            'cleanup_results': cleanup_results,
            'final_gc': final_gc,
            'success': True
        }

        self.logger.info(
            f"✅ Concurrent cleanup completed in {duration:.2f}s, "
            f"freed {memory_freed / (1024 * 1024):.1f} MB"
        )

        return result

    def shutdown_concurrent_manager(self):
        """خاموش کردن concurrent manager"""
        if hasattr(self, 'concurrent_manager'):
            self.concurrent_manager.shutdown(wait=True)
            self.logger.info("Concurrent operations manager shutdown completed")
        if len(self.memory_history) > self.max_history_size:
            self.memory_history.pop(0)
        
        # Check for alerts
        if stats.memory_level in [MemoryLevel.HIGH, MemoryLevel.CRITICAL]:
            self._handle_memory_pressure(stats)
            return True
            
        return False
    
    def _handle_memory_pressure(self, stats: MemoryStats):
        """مدیریت فشار حافظه"""
        action_taken = "none"
        
        if stats.memory_level == MemoryLevel.HIGH:
            # Clear memory pools
            for pool in self.memory_pools.values():
                pool.clear()
            action_taken = "cleared_pools"
            
        elif stats.memory_level == MemoryLevel.CRITICAL:
            # Aggressive cleanup
            self.clear_all_pools()
            if self.auto_gc_enabled:
                self.force_gc()
            action_taken = "full_cleanup"
        
        # Create alert
        alert = MemoryAlert(
            timestamp=datetime.now(),
            level=stats.memory_level,
            message=f"Memory usage at {stats.memory_percent:.1f}%",
            current_usage=stats.memory_percent,
            threshold=self.thresholds[stats.memory_level],
            action_taken=action_taken
        )
        
        self.alerts.append(alert)
        self.logger.warning(f"Memory pressure detected: {alert.message}")
    
    def clear_all_pools(self):
        """پاک کردن همه pools"""
        for pool in self.memory_pools.values():
            pool.clear()
        self.logger.info("All memory pools cleared")
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """آمار همه pools"""
        return {
            pool_type.value: pool.get_stats() 
            for pool_type, pool in self.memory_pools.items()
        }
    
    def get_system_resources(self) -> Dict[str, Any]:
        """منابع سیستم"""
        return {
            "memory": self.resource_monitor.get_memory_info(),
            "cpu": self.resource_monitor.get_cpu_info(),
            "disk": self.resource_monitor.get_disk_info(),
            "gc_stats": {
                "collections": gc.get_count(),
                "garbage": len(gc.garbage),
                "threshold": gc.get_threshold()
            }
        }
    
    def get_recent_alerts(self, hours: int = 24) -> List[MemoryAlert]:
        """هشدارهای اخیر"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alerts if alert.timestamp >= cutoff_time]
    
    def export_memory_report(self, filename: str = None) -> str:
        """گزارش حافظه"""
        if filename is None:
            filename = f"memory_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # Helper function to serialize datetime objects
        def serialize_datetime(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            return str(obj)
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "current_stats": {
                "timestamp": self.get_memory_stats().timestamp.isoformat(),
                "total_memory": self.get_memory_stats().total_memory,
                "used_memory": self.get_memory_stats().used_memory,
                "available_memory": self.get_memory_stats().available_memory,
                "memory_percent": self.get_memory_stats().memory_percent,
                "swap_memory": self.get_memory_stats().swap_memory,
                "process_memory": self.get_memory_stats().process_memory,
                "memory_level": self.get_memory_stats().memory_level.value,
                "gc_count": self.get_memory_stats().gc_count,
                "active_objects": self.get_memory_stats().active_objects
            },
            "system_resources": self.get_system_resources(),
            "pool_stats": self.get_pool_stats(),
            "recent_alerts": [
                {
                    "timestamp": alert.timestamp.isoformat(),
                    "level": alert.level.value,
                    "message": alert.message,
                    "current_usage": alert.current_usage,
                    "threshold": alert.threshold,
                    "action_taken": alert.action_taken
                }
                for alert in self.get_recent_alerts()
            ],
            "memory_history": [
                {
                    "timestamp": stats.timestamp.isoformat(),
                    "memory_percent": stats.memory_percent,
                    "process_memory": stats.process_memory,
                    "level": stats.memory_level.value
                }
                for stats in self.memory_history[-100:]  # Last 100 records
            ]
        }
        
        try:
            import json
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Memory report exported to {filename}")
            return filename
        except Exception as e:
            self.logger.error(f"Failed to export memory report: {e}")
            return ""
    
    def _start_monitoring(self):
        """شروع نظارت"""
        if not self.monitoring_enabled:
            return
            
        def monitor_worker():
            """Thread worker برای نظارت"""
            while self.monitoring_enabled:
                try:
                    self.check_memory_pressure()
                    time.sleep(self.monitoring_interval)
                except Exception as e:
                    self.logger.error(f"Memory monitoring error: {e}")
                    time.sleep(self.monitoring_interval)
        
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()
        self.logger.info("Memory monitoring started")
    
    def stop_monitoring(self):
        """توقف نظارت"""
        self.monitoring_enabled = False
        self.logger.info("Memory monitoring stopped")
        
    def start_monitoring(self):
        """شروع نظارت - public method"""
        if not hasattr(self, 'monitoring_enabled') or not self.monitoring_enabled:
            self.monitoring_enabled = True
            self._start_monitoring()
            self.logger.info("Memory monitoring started via public method")
        else:
            self.logger.debug("Memory monitoring already running")
    
    def get_system_memory_info(self) -> Dict[str, Any]:
        """دریافت اطلاعات کامل حافظه سیستم"""
        current_stats = self.get_memory_stats()
        system_resources = self.get_system_resources()
        
        return {
            "current_stats": current_stats.to_dict(),
            "system_resources": system_resources,
            "pool_stats": self.get_pool_stats(),
            "recent_alerts": [
                {
                    "timestamp": alert.timestamp.isoformat(),
                    "level": alert.level.value,
                    "message": alert.message,
                    "current_usage": alert.current_usage,
                    "threshold": alert.threshold,
                    "action_taken": alert.action_taken
                }
                for alert in self.get_recent_alerts(hours=1)  # Last hour alerts
            ],
            "memory_history_summary": {
                "total_records": len(self.memory_history),
                "latest_usage": self.memory_history[-1].memory_percent if self.memory_history else 0.0,
                "average_usage": sum(s.memory_percent for s in self.memory_history[-10:]) / min(10, len(self.memory_history)) if self.memory_history else 0.0
            }
        }

# Backward compatibility
AdvancedMemoryManager = IntelligentMemoryManager

# Global instance optimized for Pearl-3x7B
memory_manager = IntelligentMemoryManager()

# Pearl-3x7B specific functions
def create_memory_manager_for_pearl() -> IntelligentMemoryManager:
    """ایجاد مدیر حافظه مخصوص Pearl-3x7B"""
    logger.info("🤖 Creating intelligent memory manager for Pearl-3x7B")
    return IntelligentMemoryManager()

def prepare_memory_for_training(model_name: str, batch_size: int = 32,
                               sequence_length: int = 512) -> Optional[str]:
    """آماده‌سازی حافظه برای آموزش مدل"""
    return memory_manager.optimize_for_model_training(model_name, batch_size, sequence_length)

def cleanup_after_training(session_id: str = None):
    """پاکسازی پس از آموزش"""
    if session_id:
        memory_manager.ai_model_manager.end_training_session(session_id)
    memory_manager.disable_training_mode()

def get_memory_status_for_pearl() -> Dict[str, Any]:
    """وضعیت حافظه برای Pearl-3x7B"""
    return {
        'system_memory': memory_manager.get_memory_stats().to_dict(),
        'ai_models': memory_manager.ai_model_manager.get_model_memory_stats(),
        'training_mode': memory_manager.training_mode,
        'memory_pools': memory_manager.get_pool_stats(),
        'recent_alerts': [
            {
                'timestamp': alert.timestamp.isoformat(),
                'level': alert.level.value,
                'message': alert.message,
                'action_taken': alert.action_taken
            }
            for alert in memory_manager.get_recent_alerts(hours=1)
        ]
    }

@contextmanager
def memory_context(pool_type: MemoryPoolType = None):
    """Context manager برای مدیریت حافظه"""
    initial_stats = memory_manager.get_memory_stats()
    
    try:
        yield memory_manager
    finally:
        final_stats = memory_manager.get_memory_stats()
        memory_used = final_stats.process_memory - initial_stats.process_memory
        
        if memory_used > 0:
            memory_manager.logger.debug(f"Memory used in context: {memory_used:.2f} MB")
            
        # Force cleanup if memory usage is high
        if final_stats.memory_level in [MemoryLevel.HIGH, MemoryLevel.CRITICAL]:
            memory_manager.check_memory_pressure()

def memory_monitor(func):
    """Decorator برای نظارت بر حافظه"""
    def wrapper(*args, **kwargs):
        with memory_context():
            return func(*args, **kwargs)
    return wrapper

# Test and examples
if __name__ == "__main__":
    # Test memory management
    print("🧠 Testing Memory Management System...")
    
    # Get initial stats
    stats = memory_manager.get_memory_stats()
    print(f"Initial memory: {stats.process_memory:.2f} MB ({stats.memory_percent:.1f}%)")
    
    # Test memory pools
    pool = memory_manager.get_pool(MemoryPoolType.BUFFERS)
    print(f"Pool stats: {pool.get_stats()}")
    
    # Test resource monitoring
    resources = memory_manager.get_system_resources()
    print(f"System resources: {resources}")
    
    # Test memory context
    @memory_monitor
    def test_function():
        # Create some objects
        data = [i for i in range(100000)]
        return len(data)
    
    result = test_function()
    print(f"Test function result: {result}")
    
    # Export report
    report_file = memory_manager.export_memory_report()
    print(f"Memory report exported: {report_file}")
    
    print("✅ Memory Management System test completed!") 