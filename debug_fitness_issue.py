"""
Debug Fitness Issue
بررسی مشکل fitness صفر در الگوریتم ژنتیک
"""

import sys
sys.path.append('.')
from utils.genetic_strategy_evolution import TradingStrategy, StrategyEvaluator
from datetime import datetime
import json

def debug_fitness_calculation():
    """تحلیل دقیق مشکل fitness صفر"""
    
    print("=== DEBUG FITNESS CALCULATION ===")
    
    # Create simple test data with clear trend
    market_data = []
    for i in range(100):
        market_data.append({
            'timestamp': datetime.now(),
            'price': 1.1000 + i * 0.0001,  # Clear upward trend
            'volume': 1000,
            'high': 1.1000 + i * 0.0001 + 0.0001,
            'low': 1.1000 + i * 0.0001 - 0.0001
        })
    
    print(f"Market data: {len(market_data)} points")
    print(f"Price trend: {market_data[0]['price']:.5f} -> {market_data[-1]['price']:.5f}")
    
    # Create test strategy
    strategy = TradingStrategy(
        strategy_id='debug_test',
        name='Debug Test',
        parameters={
            'sma_period_short': 10, 
            'sma_period_long': 20, 
            'stop_loss': 0.01, 
            'take_profit': 0.02,
            'position_size': 0.1,  # 10% position size
            'max_positions': 5
        },
        rules=[{'type': 'trend_following', 'action': 'buy', 'weight': 1.0}],
        fitness_score=0.0,
        generation=0,
        parent_ids=[],
        performance_metrics={},
        created_at=datetime.now()
    )
    
    # Test evaluation step by step
    evaluator = StrategyEvaluator(market_data)
    
    print("\n=== STEP 1: Strategy Evaluation ===")
    fitness = evaluator.evaluate_strategy(strategy)
    print(f"Final Fitness Score: {fitness}")
    print(f"Performance Metrics:")
    for key, value in strategy.performance_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n=== STEP 2: Manual Trade Simulation ===")
    # Manual simulation to understand what's happening
    trades = evaluator._simulate_trades(strategy)
    print(f"Total trades generated: {len(trades)}")
    
    buy_trades = [t for t in trades if t['type'] == 'buy']
    sell_trades = [t for t in trades if t['type'] == 'sell']
    
    print(f"Buy trades: {len(buy_trades)}")
    print(f"Sell trades: {len(sell_trades)}")
    
    if buy_trades:
        print("Sample buy trade:")
        print(f"  Price: {buy_trades[0]['price']:.5f}")
        print(f"  Size: {buy_trades[0]['size']:.5f}")
        print(f"  Cost: {buy_trades[0]['cost']:.2f}")
    
    if sell_trades:
        print("Sample sell trade:")
        print(f"  Price: {sell_trades[0]['price']:.5f}")
        print(f"  Profit: {sell_trades[0]['profit']:.2f}")
        print(f"  Return: {sell_trades[0]['return']:.4f}")
    
    print("\n=== STEP 3: Performance Metrics Calculation ===")
    if sell_trades:
        total_return = sum(t['profit'] for t in sell_trades)
        winning_trades = [t for t in sell_trades if t['profit'] > 0]
        win_rate = len(winning_trades) / len(sell_trades)
        
        print(f"Total return: {total_return:.2f}")
        print(f"Winning trades: {len(winning_trades)}/{len(sell_trades)}")
        print(f"Win rate: {win_rate:.2%}")
        
        # Manual fitness calculation
        manual_fitness = total_return * win_rate * len(sell_trades) / 100
        print(f"Manual fitness calculation: {manual_fitness:.6f}")
    
    print("\n=== STEP 4: Signal Generation Test ===")
    # Test signal generation
    test_data_point = market_data[50]  # Middle point
    signals = evaluator._generate_signals(strategy, test_data_point, 50)
    print(f"Signals generated: {len(signals)}")
    for signal in signals:
        print(f"  Action: {signal['action']}, Weight: {signal['weight']}, Type: {signal['rule_type']}")
    
    print("\n=== STEP 5: Indicators Test ===")
    indicators = evaluator._calculate_indicators(strategy, test_data_point, 50)
    print("Indicators:")
    for key, value in indicators.items():
        print(f"  {key}: {value}")
    
    return {
        'fitness': fitness,
        'total_trades': len(trades),
        'buy_trades': len(buy_trades),
        'sell_trades': len(sell_trades),
        'signals': len(signals),
        'performance_metrics': strategy.performance_metrics
    }

if __name__ == "__main__":
    result = debug_fitness_calculation()
    print(f"\n=== FINAL RESULT ===")
    print(json.dumps(result, indent=2)) 