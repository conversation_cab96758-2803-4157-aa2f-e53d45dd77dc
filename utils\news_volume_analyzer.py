import numpy as np
import pandas as pd
import datetime
import logging
from collections import defaultdict, deque
from typing import Dict, List, Optional, Tuple, Union, Deque

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NewsVolumeAnalyzer:
    """
    A class for detecting sudden spikes in news volume that might indicate market-moving events.
    
    This analyzer maintains a sliding window of news counts and can detect when the short-term
    news volume significantly exceeds the long-term average, indicating a potential news shock.
    """
    
    def __init__(
        self,
        short_window: int = 24,  # Hours for short-term window
        long_window: int = 168,  # Hours for long-term window (1 week)
        threshold_ratio: float = 2.5,  # Ratio of short/long volume to trigger alert
        min_news_count: int = 5  # Minimum news count in short window to consider a spike
    ):
        """
        Initialize the NewsVolumeAnalyzer.
        
        Args:
            short_window (int): Size of short-term window in hours (default: 24)
            long_window (int): Size of long-term window in hours (default: 168, i.e., 1 week)
            threshold_ratio (float): Ratio of short-term to long-term volume to trigger alert (default: 2.5)
            min_news_count (int): Minimum news count in short window to consider a spike (default: 5)
        """
        if short_window >= long_window:
            raise ValueError("short_window must be smaller than long_window")
        
        self.short_window = short_window
        self.long_window = long_window
        self.threshold_ratio = threshold_ratio
        self.min_news_count = min_news_count
        
        # Dictionary to store news timestamps by asset/topic
        self.news_timestamps: Dict[str, Deque[datetime.datetime]] = defaultdict(deque)
        
        # Cache for last computed volumes to avoid recalculating frequently
        self.volume_cache: Dict[str, Dict[str, Union[int, float, datetime.datetime]]] = {}
        
        logger.info(f"Initialized NewsVolumeAnalyzer with short_window={short_window}h, "
                   f"long_window={long_window}h, threshold_ratio={threshold_ratio}")
    
    def add_news(self, asset: str, timestamp: Optional[datetime.datetime] = None) -> None:
        """
        Add a news item for a specific asset/topic.
        
        Args:
            asset (str): The asset or topic identifier
            timestamp (datetime, optional): Timestamp of the news. If None, uses current time.
        """
        if timestamp is None:
            timestamp = datetime.datetime.now()
            
        # Normalize asset name
        asset = asset.lower()
        
        # Add timestamp to the deque
        self.news_timestamps[asset].append(timestamp)
        
        # Invalidate cache for this asset
        if asset in self.volume_cache:
            del self.volume_cache[asset]
        
        # Clean up old news (older than long_window)
        self._clean_old_news(asset, timestamp)
    
    def _clean_old_news(self, asset: str, current_time: datetime.datetime) -> None:
        """
        Remove news items older than the long window.
        
        Args:
            asset (str): The asset or topic identifier
            current_time (datetime): Current timestamp for comparison
        """
        cutoff_time = current_time - datetime.timedelta(hours=self.long_window)
        
        # Remove old timestamps from the front of the deque
        while self.news_timestamps[asset] and self.news_timestamps[asset][0] < cutoff_time:
            self.news_timestamps[asset].popleft()
    
    def get_volume(self, asset: str, window_hours: int, current_time: Optional[datetime.datetime] = None) -> int:
        """
        Get the news volume for an asset within a specific time window.
        
        Args:
            asset (str): The asset or topic identifier
            window_hours (int): Number of hours to look back
            current_time (datetime, optional): Current timestamp for comparison. If None, uses current time.
            
        Returns:
            int: Count of news items within the specified window
        """
        if current_time is None:
            current_time = datetime.datetime.now()
            
        # Normalize asset name
        asset = asset.lower()
        
        # Calculate cutoff time
        cutoff_time = current_time - datetime.timedelta(hours=window_hours)
        
        # Count news items within the window
        count = sum(1 for ts in self.news_timestamps[asset] if ts >= cutoff_time)
        
        return count
    
    def detect_volume_spike(
        self, 
        asset: str, 
        current_time: Optional[datetime.datetime] = None
    ) -> Tuple[bool, Dict[str, Union[int, float]]]:
        """
        Detect if there's a significant spike in news volume for an asset.
        
        Args:
            asset (str): The asset or topic identifier
            current_time (datetime, optional): Current timestamp for comparison. If None, uses current time.
            
        Returns:
            Tuple[bool, Dict]: 
                - Boolean indicating if a spike was detected
                - Dictionary with volume metrics (short_volume, long_volume, ratio)
        """
        if current_time is None:
            current_time = datetime.datetime.now()
            
        # Normalize asset name
        asset = asset.lower()
        
        # Check if we have a recent cached result
        if asset in self.volume_cache:
            cache_entry = self.volume_cache[asset]
            cache_time = cache_entry.get('timestamp')
            if cache_time and (current_time - cache_time).total_seconds() < 3600:  # Cache valid for 1 hour
                return cache_entry.get('is_spike', False), {
                    'short_volume': cache_entry.get('short_volume', 0),
                    'long_volume': cache_entry.get('long_volume', 0),
                    'ratio': cache_entry.get('ratio', 0.0)
                }
        
        # Clean up old news first
        self._clean_old_news(asset, current_time)
        
        # Get volumes
        short_volume = self.get_volume(asset, self.short_window, current_time)
        
        # Only calculate long volume if short volume meets minimum threshold
        if short_volume < self.min_news_count:
            result = {
                'short_volume': short_volume,
                'long_volume': 0,
                'ratio': 0.0
            }
            is_spike = False
        else:
            # Calculate long-term volume excluding the short-term window
            total_long_volume = self.get_volume(asset, self.long_window, current_time)
            adjusted_long_volume = max(1, total_long_volume - short_volume)  # Avoid division by zero
            
            # Calculate hourly rates for fair comparison
            short_rate = short_volume / self.short_window
            long_rate = adjusted_long_volume / (self.long_window - self.short_window)
            
            # Avoid division by zero
            ratio = short_rate / max(0.001, long_rate)
            
            result = {
                'short_volume': short_volume,
                'long_volume': adjusted_long_volume,
                'short_rate': short_rate,
                'long_rate': long_rate,
                'ratio': ratio
            }
            
            is_spike = ratio >= self.threshold_ratio and short_volume >= self.min_news_count
        
        # Cache the result
        self.volume_cache[asset] = {
            'timestamp': current_time,
            'is_spike': is_spike,
            'short_volume': result['short_volume'],
            'long_volume': result.get('long_volume', 0),
            'ratio': result.get('ratio', 0.0)
        }
        
        if is_spike:
            logger.info(f"News volume spike detected for {asset}: "
                       f"short_volume={result['short_volume']}, "
                       f"ratio={result.get('ratio', 0.0):.2f}x")
        
        return is_spike, result
    
    def get_all_assets_with_spikes(
        self, 
        current_time: Optional[datetime.datetime] = None
    ) -> List[Tuple[str, Dict[str, Union[int, float]]]]:
        """
        Get all assets currently experiencing news volume spikes.
        
        Args:
            current_time (datetime, optional): Current timestamp for comparison. If None, uses current time.
            
        Returns:
            List[Tuple[str, Dict]]: List of (asset, metrics) tuples for assets with volume spikes
        """
        if current_time is None:
            current_time = datetime.datetime.now()
            
        result = []
        
        for asset in list(self.news_timestamps.keys()):
            is_spike, metrics = self.detect_volume_spike(asset, current_time)
            if is_spike:
                result.append((asset, metrics))
        
        return result

if __name__ == "__main__":
    # Example usage
    analyzer = NewsVolumeAnalyzer(
        short_window=6,  # 6 hours
        long_window=72,  # 3 days
        threshold_ratio=3.0,
        min_news_count=3
    )
    
    # Simulate some news over time
    now = datetime.datetime.now()
    
    # Add regular background news for BTC
    for i in range(72):
        hours_ago = 72 - i
        # Add 1-2 news items every 6 hours
        if i % 6 == 0:
            for _ in range(np.random.randint(1, 3)):
                analyzer.add_news("BTC", now - datetime.timedelta(hours=hours_ago))
    
    # Add a spike of news for BTC in the last 4 hours
    for i in range(10):
        hours_ago = np.random.randint(0, 4)
        analyzer.add_news("BTC", now - datetime.timedelta(hours=hours_ago))
    
    # Add some news for ETH (no spike)
    for i in range(10):
        hours_ago = np.random.randint(0, 72)
        analyzer.add_news("ETH", now - datetime.timedelta(hours=hours_ago))
    
    # Check for spikes
    btc_spike, btc_metrics = analyzer.detect_volume_spike("BTC")
    eth_spike, eth_metrics = analyzer.detect_volume_spike("ETH")
    
    print(f"BTC spike detected: {btc_spike}")
    print(f"BTC metrics: {btc_metrics}")
    print(f"ETH spike detected: {eth_spike}")
    print(f"ETH metrics: {eth_metrics}")
    
    # Get all assets with spikes
    spikes = analyzer.get_all_assets_with_spikes()
    print(f"All assets with spikes: {[asset for asset, _ in spikes]}") 