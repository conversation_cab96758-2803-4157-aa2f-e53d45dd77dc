#!/usr/bin/env python3
"""
🚀 Complete AI Model Installation and Testing
نصب و تست کامل مدل‌های هوش مصنوعی

این اسکریپت تمام مدل‌های AI مورد نیاز رو نصب و تست می‌کنه
"""

import sys
import os
import subprocess
import json
import time
import importlib
from datetime import datetime
from pathlib import Path

def check_and_install_requirements():
    """چک و نصب نیازمندی‌ها"""
    print("📦 Checking and installing requirements...")
    
    required_packages = [
        "torch",
        "transformers",
        "sentence-transformers",
        "accelerate",
        "datasets",
        "numpy",
        "pandas",
        "scikit-learn",
        "aiohttp",
        "fastapi",
        "uvicorn",
        "pydantic",
        "loguru",
        "psutil"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"  ✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package} is missing")
    
    if missing_packages:
        print(f"\n🔧 Installing missing packages: {', '.join(missing_packages)}")
        
        # Install via pip
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"  ✅ {package} installed successfully")
            except subprocess.CalledProcessError as e:
                print(f"  ❌ Failed to install {package}: {e}")
    
    return len(missing_packages) == 0

def setup_proxy_environment():
    """تنظیم محیط proxy"""
    print("\n🌐 Setting up proxy environment...")
    
    if os.path.exists("PROXY.json"):
        # Set proxy environment variables
        os.environ["HTTP_PROXY"] = "http://127.0.0.1:10809"
        os.environ["HTTPS_PROXY"] = "http://127.0.0.1:10809"
        os.environ["http_proxy"] = "http://127.0.0.1:10809"
        os.environ["https_proxy"] = "http://127.0.0.1:10809"
        
        print("  ✅ Proxy environment configured")
        return True
    else:
        print("  ⚠️ No proxy configuration found")
        return False

def test_internet_connection():
    """تست اتصال اینترنت"""
    print("\n🌍 Testing internet connection...")
    
    try:
        import requests
        
        # Test direct connection
        response = requests.get("https://httpbin.org/ip", timeout=10)
        if response.status_code == 200:
            print("  ✅ Internet connection is working")
            return True
        else:
            print(f"  ❌ Connection failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Connection test failed: {e}")
        return False

def download_and_test_model(model_name: str, model_path: str, model_type: str = "transformers"):
    """دانلود و تست یک مدل"""
    print(f"\n🔽 Downloading and testing: {model_name}")
    print(f"   Model path: {model_path}")
    print(f"   Model type: {model_type}")
    
    start_time = time.time()
    result = {
        "model_name": model_name,
        "model_path": model_path,
        "model_type": model_type,
        "status": "failed",
        "download_time": 0,
        "model_size": "unknown",
        "test_result": "failed",
        "error": None
    }
    
    try:
        if model_type == "transformers":
            from transformers import AutoTokenizer, AutoModel, pipeline
            
            # Download tokenizer
            print("    📥 Downloading tokenizer...")
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            
            # Download model
            print("    📥 Downloading model...")
            model = AutoModel.from_pretrained(model_path)
            
            # Test basic functionality
            if "sentiment" in model_name.lower() or "bert" in model_name.lower():
                print("    🧪 Testing sentiment analysis...")
                classifier = pipeline("sentiment-analysis", 
                                    model=model_path, 
                                    tokenizer=tokenizer)
                
                test_result = classifier("Bitcoin price is rising today")
                result["test_result"] = f"Sentiment: {test_result[0]['label']} ({test_result[0]['score']:.2f})"
                print(f"      ✅ {result['test_result']}")
            
            else:
                print("    🧪 Testing basic model functionality...")
                test_text = "This is a test sentence"
                inputs = tokenizer(test_text, return_tensors="pt")
                outputs = model(**inputs)
                result["test_result"] = f"Output shape: {outputs.last_hidden_state.shape}"
                print(f"      ✅ {result['test_result']}")
        
        elif model_type == "sentence-transformers":
            from sentence_transformers import SentenceTransformer
            
            print("    📥 Downloading sentence transformer...")
            model = SentenceTransformer(model_path)
            
            print("    🧪 Testing embedding generation...")
            test_sentences = ["Bitcoin is rising", "Market is stable"]
            embeddings = model.encode(test_sentences)
            
            result["test_result"] = f"Embedding shape: {embeddings.shape}"
            print(f"      ✅ {result['test_result']}")
        
        elif model_type == "chronos":
            print("    📥 Downloading Chronos model...")
            # Chronos requires special handling
            try:
                from chronos import ChronosPipeline
                import torch
                
                pipeline = ChronosPipeline.from_pretrained(model_path)
                
                print("    🧪 Testing time series forecasting...")
                # Create sample data
                context = torch.randn(50)
                forecast = pipeline.predict(context, prediction_length=10)
                
                result["test_result"] = f"Forecast length: {len(forecast)}"
                print(f"      ✅ {result['test_result']}")
                
            except ImportError:
                print("    ⚠️ Chronos library not available, using transformers...")
                from transformers import pipeline
                
                forecaster = pipeline("text-generation", model=model_path)
                result["test_result"] = "Chronos model loaded via transformers"
                print(f"      ✅ {result['test_result']}")
        
        # Calculate model size
        try:
            if hasattr(model, 'parameters'):
                model_size = sum(p.numel() for p in model.parameters()) * 4 / 1024 / 1024  # MB
                result["model_size"] = f"{model_size:.1f}MB"
        except:
            pass
        
        end_time = time.time()
        result["download_time"] = end_time - start_time
        result["status"] = "success"
        
        print(f"    ✅ Model downloaded and tested successfully!")
        print(f"    ⏱️ Time: {result['download_time']:.1f}s")
        print(f"    💾 Size: {result['model_size']}")
        
    except Exception as e:
        result["error"] = str(e)
        print(f"    ❌ Failed: {e}")
    
    return result

def main():
    """تابع اصلی"""
    print("🚀 AI Model Installation and Testing Tool")
    print("=" * 60)
    
    # Create directories
    Path("models").mkdir(exist_ok=True)
    Path("reports").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    # Step 1: Check requirements
    print("\n1️⃣ Checking requirements...")
    if not check_and_install_requirements():
        print("❌ Some requirements couldn't be installed. Please check manually.")
        return
    
    # Step 2: Setup proxy
    print("\n2️⃣ Setting up proxy...")
    proxy_available = setup_proxy_environment()
    
    # Step 3: Test internet
    print("\n3️⃣ Testing internet connection...")
    if not test_internet_connection():
        print("❌ Internet connection failed. Please check your connection or proxy.")
        if not proxy_available:
            print("💡 Tip: Make sure your proxy is running if needed.")
        return
    
    # Step 4: Download and test models
    print("\n4️⃣ Downloading and testing AI models...")
    print("=" * 60)
    
    # Define models to test
    models_to_test = [
        # Sentiment Analysis Models
        ("finbert", "ProsusAI/finbert", "transformers"),
        ("cryptobert", "ElKulako/cryptobert", "transformers"),
        ("financial_sentiment", "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis", "transformers"),
        
        # Embedding Models
        ("sentence_transformer", "sentence-transformers/all-MiniLM-L6-v2", "sentence-transformers"),
        ("financial_bert_embedding", "nlpaueb/sec-bert-base", "transformers"),
        
        # Time Series Models
        ("chronos_tiny", "amazon/chronos-t5-tiny", "chronos"),
        ("chronos_small", "amazon/chronos-t5-small", "chronos"),
        
        # Specialized Models
        ("distilbert_base", "distilbert-base-uncased", "transformers"),
        ("roberta_base", "roberta-base", "transformers"),
        
        # Lightweight Models
        ("bert_tiny", "prajjwal1/bert-tiny", "transformers"),
        ("distilgpt2", "distilgpt2", "transformers")
    ]
    
    all_results = []
    
    for model_name, model_path, model_type in models_to_test:
        try:
            result = download_and_test_model(model_name, model_path, model_type)
            all_results.append(result)
            
            # Small delay between downloads
            time.sleep(1)
            
        except KeyboardInterrupt:
            print("\n⚠️ Download interrupted by user")
            break
        except Exception as e:
            print(f"⚠️ Unexpected error with {model_name}: {e}")
            continue
    
    # Step 5: Generate report
    print("\n5️⃣ Generating report...")
    print("=" * 60)
    
    successful_models = [r for r in all_results if r["status"] == "success"]
    failed_models = [r for r in all_results if r["status"] == "failed"]
    
    print(f"📊 Summary:")
    print(f"  Total models tested: {len(all_results)}")
    print(f"  Successful downloads: {len(successful_models)}")
    print(f"  Failed downloads: {len(failed_models)}")
    print(f"  Success rate: {(len(successful_models)/len(all_results)*100):.1f}%")
    
    if successful_models:
        print(f"\n✅ Successfully downloaded models:")
        for model in successful_models:
            print(f"  • {model['model_name']} ({model['download_time']:.1f}s)")
    
    if failed_models:
        print(f"\n❌ Failed models:")
        for model in failed_models:
            print(f"  • {model['model_name']}: {model['error']}")
    
    # Save report
    report = {
        "timestamp": datetime.now().isoformat(),
        "summary": {
            "total": len(all_results),
            "successful": len(successful_models),
            "failed": len(failed_models),
            "success_rate": len(successful_models)/len(all_results)*100 if all_results else 0
        },
        "results": all_results
    }
    
    report_file = f"reports/ai_model_installation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Detailed report saved to: {report_file}")
    
    # Final message
    if successful_models:
        print(f"\n🎉 Installation completed! {len(successful_models)} models are ready for use.")
        print("💡 You can now use these models in your trading system.")
    else:
        print(f"\n😞 No models were successfully installed.")
        print("💡 Please check your internet connection and try again.")

if __name__ == "__main__":
    main() 