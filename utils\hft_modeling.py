"""High-Frequency Trading (HFT) Modeling

این ماژول برای مدل‌سازی و پیاده‌سازی معاملات با فرکانس بالا طراحی شده است.
شامل تحلیل میکروساختار بازار، بهینه‌سازی تأخیر، و پیش‌بینی قیمت کوتاه‌مدت.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from collections import deque, defaultdict
import time
import threading
import logging
from datetime import datetime, timedelta
import asyncio
from concurrent.futures import ThreadPoolExecutor
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


@dataclass
class OrderBookLevel:
    """سطح order book"""
    price: float
    volume: float
    timestamp: float
    

@dataclass
class Trade:
    """معامله انجام شده"""
    price: float
    volume: float
    timestamp: float
    side: str  # 'buy' or 'sell'
    

@dataclass
class MarketMicrostructure:
    """ساختار میکروساختار بازار"""
    bid_ask_spread: float
    market_depth: float
    price_impact: float
    order_flow_imbalance: float
    volatility: float
    timestamp: float
    

class OrderBookAnalyzer:
    """تحلیل‌گر order book برای HFT"""
    
    def __init__(self, max_levels: int = 10, history_size: int = 1000):
        """
        Parameters
        ----------
        max_levels : int
            حداکثر سطوح order book برای تحلیل
        history_size : int
            اندازه تاریخچه برای نگهداری
        """
        self.max_levels = max_levels
        self.history_size = history_size
        
        # تاریخچه order book
        self.bid_history = deque(maxlen=history_size)
        self.ask_history = deque(maxlen=history_size)
        self.trade_history = deque(maxlen=history_size)
        
        # آمار real-time
        self.current_spread = 0.0
        self.current_depth = 0.0
        self.current_imbalance = 0.0
        
    def update_order_book(self, 
                         bids: List[OrderBookLevel], 
                         asks: List[OrderBookLevel],
                         timestamp: float = None) -> None:
        """به‌روزرسانی order book
        
        Parameters
        ----------
        bids : List[OrderBookLevel]
            لیست bid orders
        asks : List[OrderBookLevel]
            لیست ask orders
        timestamp : float
            زمان به‌روزرسانی
        """
        if timestamp is None:
            timestamp = time.time()
            
        # مرتب‌سازی
        bids_sorted = sorted(bids, key=lambda x: x.price, reverse=True)[:self.max_levels]
        asks_sorted = sorted(asks, key=lambda x: x.price)[:self.max_levels]
        
        # ذخیره در تاریخچه
        self.bid_history.append((timestamp, bids_sorted))
        self.ask_history.append((timestamp, asks_sorted))
        
        # محاسبه آمار فعلی
        self._calculate_current_stats(bids_sorted, asks_sorted)
        
    def _calculate_current_stats(self, bids: List[OrderBookLevel], asks: List[OrderBookLevel]) -> None:
        """محاسبه آمار فعلی order book"""
        if not bids or not asks:
            return
            
        # محاسبه spread
        best_bid = bids[0].price
        best_ask = asks[0].price
        self.current_spread = best_ask - best_bid
        
        # محاسبه عمق بازار
        bid_depth = sum(level.volume for level in bids)
        ask_depth = sum(level.volume for level in asks)
        self.current_depth = bid_depth + ask_depth
        
        # محاسبه عدم تعادل
        if bid_depth + ask_depth > 0:
            self.current_imbalance = (bid_depth - ask_depth) / (bid_depth + ask_depth)
        else:
            self.current_imbalance = 0.0
            
    def calculate_price_impact(self, volume: float, side: str) -> float:
        """محاسبه تأثیر قیمت برای حجم معین
        
        Parameters
        ----------
        volume : float
            حجم سفارش
        side : str
            طرف سفارش ('buy' یا 'sell')
            
        Returns
        -------
        float
            تأثیر قیمت تخمینی
        """
        if not self.bid_history or not self.ask_history:
            return 0.0
            
        # آخرین order book
        _, current_bids = self.bid_history[-1]
        _, current_asks = self.ask_history[-1]
        
        if side == 'buy':
            # محاسبه تأثیر خرید
            remaining_volume = volume
            total_cost = 0.0
            
            for ask in current_asks:
                if remaining_volume <= 0:
                    break
                    
                consumed = min(remaining_volume, ask.volume)
                total_cost += consumed * ask.price
                remaining_volume -= consumed
                
            if volume > 0:
                avg_price = total_cost / volume
                best_ask = current_asks[0].price if current_asks else 0
                return (avg_price - best_ask) / best_ask if best_ask > 0 else 0
                
        else:  # sell
            # محاسبه تأثیر فروش
            remaining_volume = volume
            total_revenue = 0.0
            
            for bid in current_bids:
                if remaining_volume <= 0:
                    break
                    
                consumed = min(remaining_volume, bid.volume)
                total_revenue += consumed * bid.price
                remaining_volume -= consumed
                
            if volume > 0:
                avg_price = total_revenue / volume
                best_bid = current_bids[0].price if current_bids else 0
                return (best_bid - avg_price) / best_bid if best_bid > 0 else 0
                
        return 0.0
        
    def get_market_microstructure(self) -> MarketMicrostructure:
        """دریافت آمار میکروساختار فعلی"""
        # محاسبه نوسان از تاریخچه معاملات
        volatility = 0.0
        if len(self.trade_history) > 10:
            recent_prices = [trade.price for _, trade in list(self.trade_history)[-10:]]
            volatility = np.std(recent_prices) if len(recent_prices) > 1 else 0.0
            
        return MarketMicrostructure(
            bid_ask_spread=self.current_spread,
            market_depth=self.current_depth,
            price_impact=self.calculate_price_impact(1000, 'buy'),  # تأثیر برای 1000 واحد
            order_flow_imbalance=self.current_imbalance,
            volatility=volatility,
            timestamp=time.time()
        )


class LatencyOptimizer:
    """بهینه‌ساز تأخیر برای HFT"""
    
    def __init__(self):
        self.execution_times = deque(maxlen=1000)
        self.network_delays = deque(maxlen=1000)
        self.processing_times = deque(maxlen=1000)
        
    def measure_execution_time(self, func, *args, **kwargs):
        """اندازه‌گیری زمان اجرای تابع"""
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        
        execution_time = (end_time - start_time) * 1000  # milliseconds
        self.execution_times.append(execution_time)
        
        return result, execution_time
        
    def measure_network_delay(self, ping_target: str = "8.8.8.8") -> float:
        """اندازه‌گیری تأخیر شبکه"""
        import subprocess
        import platform
        
        try:
            # تشخیص سیستم‌عامل
            param = "-n" if platform.system().lower() == "windows" else "-c"
            
            # اجرای ping
            result = subprocess.run(
                ["ping", param, "1", ping_target],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            # استخراج زمان ping
            if result.returncode == 0:
                output = result.stdout
                if "time=" in output:
                    time_str = output.split("time=")[1].split("ms")[0]
                    delay = float(time_str)
                    self.network_delays.append(delay)
                    return delay
                    
        except Exception as e:
            logger.error(f"Error measuring network delay: {e}")
            
        return 0.0
        
    def get_latency_stats(self) -> Dict[str, float]:
        """دریافت آمار تأخیر"""
        stats = {}
        
        if self.execution_times:
            stats['avg_execution_time'] = np.mean(self.execution_times)
            stats['p95_execution_time'] = np.percentile(self.execution_times, 95)
            stats['p99_execution_time'] = np.percentile(self.execution_times, 99)
            
        if self.network_delays:
            stats['avg_network_delay'] = np.mean(self.network_delays)
            stats['p95_network_delay'] = np.percentile(self.network_delays, 95)
            
        return stats
        
    def optimize_data_structures(self, data: List[Any]) -> List[Any]:
        """بهینه‌سازی ساختار داده‌ها برای سرعت بیشتر"""
        # استفاده از numpy arrays به جای lists
        if isinstance(data, list) and len(data) > 0:
            if isinstance(data[0], (int, float)):
                return np.array(data)
                
        return data


class ShortTermPredictor:
    """پیش‌بینی‌کننده قیمت کوتاه‌مدت"""
    
    def __init__(self, feature_window: int = 100):
        """
        Parameters
        ----------
        feature_window : int
            اندازه پنجره برای استخراج ویژگی
        """
        self.feature_window = feature_window
        self.scaler = StandardScaler()
        self.model = None
        self.is_trained = False
        
        # تاریخچه داده‌ها
        self.price_history = deque(maxlen=feature_window * 2)
        self.volume_history = deque(maxlen=feature_window * 2)
        self.spread_history = deque(maxlen=feature_window * 2)
        
    def extract_features(self, 
                        prices: List[float],
                        volumes: List[float],
                        spreads: List[float]) -> np.ndarray:
        """استخراج ویژگی برای پیش‌بینی
        
        Parameters
        ----------
        prices : List[float]
            قیمت‌های اخیر
        volumes : List[float]
            حجم‌های اخیر
        spreads : List[float]
            spread های اخیر
            
        Returns
        -------
        np.ndarray
            بردار ویژگی
        """
        features = []
        
        # ویژگی‌های قیمت
        if len(prices) >= 2:
            # بازده‌های کوتاه‌مدت
            returns = np.diff(prices) / prices[:-1]
            features.extend([
                np.mean(returns[-5:]) if len(returns) >= 5 else 0,
                np.std(returns[-5:]) if len(returns) >= 5 else 0,
                np.mean(returns[-10:]) if len(returns) >= 10 else 0,
                np.std(returns[-10:]) if len(returns) >= 10 else 0,
            ])
            
            # مومنتوم
            if len(prices) >= 10:
                momentum = (prices[-1] - prices[-10]) / prices[-10]
                features.append(momentum)
            else:
                features.append(0)
                
        else:
            features.extend([0, 0, 0, 0, 0])
            
        # ویژگی‌های حجم
        if len(volumes) >= 2:
            volume_trend = np.mean(volumes[-5:]) / np.mean(volumes[-10:]) if len(volumes) >= 10 else 1
            features.append(volume_trend)
            
            # نرخ تغییر حجم
            volume_change = (volumes[-1] - volumes[-2]) / volumes[-2] if volumes[-2] > 0 else 0
            features.append(volume_change)
        else:
            features.extend([1, 0])
            
        # ویژگی‌های spread
        if len(spreads) >= 2:
            spread_trend = np.mean(spreads[-5:]) / np.mean(spreads[-10:]) if len(spreads) >= 10 else 1
            features.append(spread_trend)
            
            # نرخ تغییر spread
            spread_change = (spreads[-1] - spreads[-2]) / spreads[-2] if spreads[-2] > 0 else 0
            features.append(spread_change)
        else:
            features.extend([1, 0])
            
        return np.array(features)
        
    def train_model(self, 
                   price_data: List[float],
                   volume_data: List[float],
                   spread_data: List[float],
                   prediction_horizon: int = 5) -> None:
        """آموزش مدل پیش‌بینی
        
        Parameters
        ----------
        price_data : List[float]
            داده‌های قیمت تاریخی
        volume_data : List[float]
            داده‌های حجم تاریخی
        spread_data : List[float]
            داده‌های spread تاریخی
        prediction_horizon : int
            افق پیش‌بینی (تعداد tick های آینده)
        """
        if len(price_data) < self.feature_window + prediction_horizon:
            logger.warning("Not enough data for training")
            return
            
        # تهیه داده‌های آموزش
        X, y = [], []
        
        for i in range(self.feature_window, len(price_data) - prediction_horizon):
            # استخراج ویژگی
            features = self.extract_features(
                price_data[i-self.feature_window:i],
                volume_data[i-self.feature_window:i],
                spread_data[i-self.feature_window:i]
            )
            
            # هدف: جهت حرکت قیمت
            future_price = price_data[i + prediction_horizon]
            current_price = price_data[i]
            direction = 1 if future_price > current_price else 0
            
            X.append(features)
            y.append(direction)
            
        X = np.array(X)
        y = np.array(y)
        
        # نرمال‌سازی ویژگی‌ها
        X_scaled = self.scaler.fit_transform(X)
        
        # آموزش مدل
        self.model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        )
        self.model.fit(X_scaled, y)
        self.is_trained = True
        
        logger.info(f"Model trained on {len(X)} samples")
        
    def predict_direction(self, 
                         recent_prices: List[float],
                         recent_volumes: List[float],
                         recent_spreads: List[float]) -> Tuple[float, float]:
        """پیش‌بینی جهت حرکت قیمت
        
        Parameters
        ----------
        recent_prices : List[float]
            قیمت‌های اخیر
        recent_volumes : List[float]
            حجم‌های اخیر
        recent_spreads : List[float]
            spread های اخیر
            
        Returns
        -------
        Tuple[float, float]
            احتمال حرکت صعودی و اطمینان پیش‌بینی
        """
        if not self.is_trained:
            return 0.5, 0.0
            
        # استخراج ویژگی
        features = self.extract_features(recent_prices, recent_volumes, recent_spreads)
        
        # نرمال‌سازی
        features_scaled = self.scaler.transform(features.reshape(1, -1))
        
        # پیش‌بینی
        prediction = self.model.predict(features_scaled)[0]
        
        # محاسبه اطمینان (بر اساس variance در random forest)
        if hasattr(self.model, 'estimators_'):
            predictions = [tree.predict(features_scaled)[0] for tree in self.model.estimators_]
            confidence = 1.0 - np.std(predictions)
        else:
            confidence = 0.5
            
        return prediction, confidence


class HFTStrategy:
    """استراتژی معاملاتی HFT"""
    
    def __init__(self, 
                 symbol: str,
                 min_spread_threshold: float = 0.0001,
                 max_position_size: float = 10000,
                 max_holding_time: int = 5):
        """
        Parameters
        ----------
        symbol : str
            نماد معاملاتی
        min_spread_threshold : float
            حداقل آستانه spread برای معامله
        max_position_size : float
            حداکثر اندازه پوزیشن
        max_holding_time : int
            حداکثر زمان نگهداری پوزیشن (ثانیه)
        """
        self.symbol = symbol
        self.min_spread_threshold = min_spread_threshold
        self.max_position_size = max_position_size
        self.max_holding_time = max_holding_time
        
        # اجزای سیستم
        self.order_book_analyzer = OrderBookAnalyzer()
        self.latency_optimizer = LatencyOptimizer()
        self.predictor = ShortTermPredictor()
        
        # وضعیت معاملات
        self.current_position = 0.0
        self.position_entry_time = None
        self.pnl = 0.0
        
        # آمار
        self.total_trades = 0
        self.successful_trades = 0
        
    def should_enter_position(self, 
                            market_data: MarketMicrostructure,
                            prediction: float,
                            confidence: float) -> Tuple[bool, str, float]:
        """تصمیم‌گیری برای ورود به پوزیشن
        
        Parameters
        ----------
        market_data : MarketMicrostructure
            داده‌های میکروساختار بازار
        prediction : float
            پیش‌بینی جهت قیمت
        confidence : float
            اطمینان پیش‌بینی
            
        Returns
        -------
        Tuple[bool, str, float]
            (باید وارد شود، جهت، اندازه)
        """
        # بررسی شرایط پایه
        if self.current_position != 0:
            return False, 'hold', 0.0
            
        if market_data.bid_ask_spread < self.min_spread_threshold:
            return False, 'wait', 0.0
            
        if confidence < 0.6:
            return False, 'wait', 0.0
            
        # تصمیم‌گیری بر اساس پیش‌بینی
        if prediction > 0.6:  # پیش‌بینی صعودی قوی
            size = min(self.max_position_size, market_data.market_depth * 0.1)
            return True, 'buy', size
            
        elif prediction < 0.4:  # پیش‌بینی نزولی قوی
            size = min(self.max_position_size, market_data.market_depth * 0.1)
            return True, 'sell', size
            
        return False, 'wait', 0.0
        
    def should_exit_position(self, current_price: float) -> bool:
        """تصمیم‌گیری برای خروج از پوزیشن"""
        if self.current_position == 0:
            return False
            
        # خروج بر اساس زمان
        if self.position_entry_time:
            holding_time = time.time() - self.position_entry_time
            if holding_time > self.max_holding_time:
                return True
                
        # خروج بر اساس سود/زیان
        # (اینجا می‌توان منطق پیچیده‌تری اضافه کرد)
        
        return False
        
    def execute_trade(self, action: str, size: float, price: float) -> bool:
        """اجرای معامله
        
        Parameters
        ----------
        action : str
            نوع معامله ('buy' یا 'sell')
        size : float
            اندازه معامله
        price : float
            قیمت معامله
            
        Returns
        -------
        bool
            موفقیت معامله
        """
        try:
            # شبیه‌سازی معامله
            if action == 'buy':
                self.current_position += size
            elif action == 'sell':
                self.current_position -= size
            elif action == 'close':
                self.current_position = 0.0
                
            self.position_entry_time = time.time()
            self.total_trades += 1
            
            logger.info(f"Executed {action}: {size} @ {price}")
            return True
            
        except Exception as e:
            logger.error(f"Trade execution failed: {e}")
            return False
            
    def get_performance_stats(self) -> Dict[str, float]:
        """دریافت آمار عملکرد"""
        win_rate = self.successful_trades / self.total_trades if self.total_trades > 0 else 0
        
        return {
            'total_trades': self.total_trades,
            'successful_trades': self.successful_trades,
            'win_rate': win_rate,
            'current_position': self.current_position,
            'pnl': self.pnl,
            **self.latency_optimizer.get_latency_stats()
        }


class HFTModelingSystem:
    """سیستم کامل HFT Modeling"""
    
    def __init__(self, symbols: List[str]):
        """
        Parameters
        ----------
        symbols : List[str]
            لیست نمادهای معاملاتی
        """
        self.symbols = symbols
        self.strategies = {symbol: HFTStrategy(symbol) for symbol in symbols}
        self.is_running = False
        
    def start(self):
        """شروع سیستم HFT"""
        self.is_running = True
        logger.info("HFT Modeling System started")
        
    def stop(self):
        """توقف سیستم HFT"""
        self.is_running = False
        logger.info("HFT Modeling System stopped")
        
    def process_market_data(self, 
                          symbol: str,
                          bids: List[OrderBookLevel],
                          asks: List[OrderBookLevel],
                          trades: List[Trade]) -> None:
        """پردازش داده‌های بازار"""
        if symbol not in self.strategies:
            return
            
        strategy = self.strategies[symbol]
        
        # به‌روزرسانی order book
        strategy.order_book_analyzer.update_order_book(bids, asks)
        
        # دریافت آمار میکروساختار
        market_data = strategy.order_book_analyzer.get_market_microstructure()
        
        # پیش‌بینی قیمت (نیاز به داده‌های تاریخی)
        # اینجا باید داده‌های تاریخی را به predictor بدهیم
        
        # تصمیم‌گیری و اجرای معامله
        # ...
        
    def get_system_stats(self) -> Dict[str, Any]:
        """دریافت آمار کل سیستم"""
        stats = {}
        
        for symbol, strategy in self.strategies.items():
            stats[symbol] = strategy.get_performance_stats()
            
        return stats 