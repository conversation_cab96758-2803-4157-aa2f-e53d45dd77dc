import numpy as np


def calculate_metrics(env, model, initial_balance=1000):
    """
    محاسبه معیارهای عملکرد مدل در محیط معاملاتی.

    Args:
        env: محیط تست (VecEnv)
        model: مدل آموزش‌دیده
        initial_balance: بالانس اولیه برای محاسبات

    Returns:
        dict: شامل معیارهایی مثل win_rate، sharpe_ratio، max_drawdown، daily_drawdown و equity_curve
    """
    obs = env.reset()
    done = False
    balance = initial_balance
    equity_curve = [balance]
    trades = []
    wins = 0

    while not done:
        action, _ = model.predict(obs, deterministic=True)
        obs, reward, done, info = env.step([action])
        done = done[0]  # چون VecEnv استفاده می‌کنیم، done یه آرایه‌ست
        balance = info[0]["balance"]
        equity_curve.append(balance)

        if "trade_result" in info[0]:
            trade_result = info[0]["trade_result"]
            trades.append(trade_result)
            if trade_result > 0:
                wins += 1

    # محاسبه معیارها
    metrics = {}

    # Win Rate
    if trades:
        metrics["win_rate"] = (wins / len(trades)) * 100
    else:
        metrics["win_rate"] = 0

    # Sharpe Ratio (فرض می‌کنیم بازده‌ها روزانه هستن)
    returns = np.diff(equity_curve) / equity_curve[:-1]
    if len(returns) > 1 and np.std(returns) != 0:
        metrics["sharpe_ratio"] = (
            np.mean(returns) / np.std(returns) * np.sqrt(252)
        )  # فرض 252 روز معاملاتی
    else:
        metrics["sharpe_ratio"] = 0

    # Max Drawdown
    equity_curve = np.array(equity_curve)
    rolling_max = np.maximum.accumulate(equity_curve)
    drawdowns = (rolling_max - equity_curve) / rolling_max * 100
    metrics["max_drawdown"] = np.max(drawdowns) if len(drawdowns) > 0 else 0

    # Daily Drawdown (میانگین افت روزانه)
    daily_drawdowns = [
        ((rolling_max[i] - equity_curve[i]) / rolling_max[i] * 100)
        for i in range(len(equity_curve))
    ]
    metrics["daily_drawdown"] = np.mean(daily_drawdowns) if daily_drawdowns else 0

    # اضافه کردن equity_curve به خروجی
    metrics["equity_curve"] = equity_curve.tolist()

    return metrics
