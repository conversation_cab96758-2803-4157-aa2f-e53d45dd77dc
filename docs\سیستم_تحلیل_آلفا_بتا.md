# مستندات سیستم تحلیل آلفا-بتا (AlphaBetaAttributionEngine)

## 📋 معرفی کلی

سیستم **AlphaBetaAttributionEngine** یکی از ماژول‌های تحلیلی پیشرفته پروژه است که امکان محاسبه Alpha و Beta در زمان واقعی، تحلیل Attribution چندعاملی، و پیش‌بینی Alpha با یادگیری ماشین را فراهم می‌کند. این سیستم در داشبورد realtime و تحلیل‌های عملکرد استفاده می‌شود.

## 🎯 مسئولیت‌های اصلی

### 1. محاسبه Alpha/Beta
- محاسبه Alpha و Beta کلاسیک
- Alpha/Beta در زمان واقعی با فیلتر کالمن
- محاسبه متریک‌های عملکرد

### 2. تحلیل Attribution
- تجزیه منابع Alpha
- تحلیل چندعاملی
- Attribution بازده و ریسک

### 3. پیش‌بینی Alpha
- پیش‌بینی Alpha با ML
- تحلیل ویژگی‌ها
- مدل‌سازی پیشرفته

## 🔧 کلاس‌های اصلی

### 1. AlphaBetaResult
```python
@dataclass
class AlphaBetaResult:
    alpha: float
    beta: float
    r_squared: float
    tracking_error: float
    information_ratio: float
    sharpe_ratio: float
    treynor_ratio: float
    jensen_alpha: float
    period_start: datetime
    period_end: datetime
    observations: int
    alpha_components: Dict[str, float] = field(default_factory=dict)
    volatility: float = 0.0
    max_drawdown: float = 0.0
    calmar_ratio: float = 0.0
    sortino_ratio: float = 0.0
```

### 2. FactorExposure
```python
@dataclass
class FactorExposure:
    factor_type: RiskFactorType
    exposure: float
    t_stat: float
    p_value: float
    contribution_to_return: float
    contribution_to_risk: float
```

### 3. KalmanFilterAlphaBeta
```python
class KalmanFilterAlphaBeta:
    def __init__(self, process_noise: float = 0.01, observation_noise: float = 0.1):
        self.process_noise = process_noise
        self.observation_noise = observation_noise
        self.state = np.array([0.0, 1.0])  # [alpha, beta]
        self.covariance = np.eye(2) * 1.0
```

### 4. MultiFactorModel
```python
class MultiFactorModel:
    def __init__(self, factors: List[RiskFactorType] = None):
        self.factors = factors or [
            RiskFactorType.MARKET,
            RiskFactorType.SIZE,
            RiskFactorType.VALUE,
            RiskFactorType.MOMENTUM
        ]
```

### 5. AlphaMLPredictor
```python
class AlphaMLPredictor:
    def __init__(self, model_type: str = 'random_forest'):
        self.model_type = model_type
        self.model = None
        self.scaler = StandardScaler()
```

## 📊 متدهای اصلی

### 1. calculate_alpha_beta()
```python
def calculate_alpha_beta(self, 
                       portfolio_returns: pd.Series,
                       benchmark_returns: pd.Series = None,
                       window: int = 252) -> AlphaBetaResult:
    """
    محاسبه Alpha/Beta اصلی
    
    پارامترها:
    - portfolio_returns: بازده پرتفوی
    - benchmark_returns: بازده بنچمارک
    - window: پنجره زمانی محاسبه
    
    خروجی:
    - AlphaBetaResult: نتایج تحلیل
    """
```

### 2. realtime_alpha_beta()
```python
def realtime_alpha_beta(self, 
                      portfolio_return: float, 
                      market_return: float) -> Tuple[float, float]:
    """
    محاسبه Alpha/Beta در زمان واقعی با فیلتر کالمن
    
    پارامترها:
    - portfolio_return: بازده پرتفوی
    - market_return: بازده بازار
    
    خروجی:
    - Tuple[float, float]: (alpha, beta)
    """
```

### 3. multi_factor_attribution()
```python
def multi_factor_attribution(self,
                            portfolio_returns: pd.Series,
                            factor_returns: pd.DataFrame) -> Dict[str, Any]:
    """
    تحلیل Attribution چندعاملی
    
    پارامترها:
    - portfolio_returns: بازده پرتفوی
    - factor_returns: بازده فاکتورها
    
    خروجی:
    - Dict: نتایج تحلیل چندعاملی
    """
```

### 4. decompose_alpha_sources()
```python
def decompose_alpha_sources(self, 
                          portfolio_returns: pd.Series,
                          holdings: pd.DataFrame = None,
                          benchmark_weights: pd.DataFrame = None) -> Dict[str, float]:
    """
    تجزیه منابع Alpha
    
    پارامترها:
    - portfolio_returns: بازده پرتفوی
    - holdings: ترکیب پرتفوی
    - benchmark_weights: وزن‌های بنچمارک
    
    خروجی:
    - Dict[str, float]: منابع Alpha
    """
```

## 🎨 انواع فاکتورهای ریسک

### 1. فاکتورهای اصلی
```python
class RiskFactorType(Enum):
    MARKET = "market"          # فاکتور بازار
    SIZE = "size"              # اندازه شرکت
    VALUE = "value"            # ارزش
    MOMENTUM = "momentum"      # مومنتوم
    QUALITY = "quality"        # کیفیت
    VOLATILITY = "volatility"  # نوسان
    PROFITABILITY = "profitability"  # سودآوری
    INVESTMENT = "investment"  # سرمایه‌گذاری
```

### 2. دوره‌های تحلیل
```python
class AttributionPeriod(Enum):
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"
```

## 🔧 فیلتر کالمن برای Alpha/Beta

### 1. مدل حالت
```python
# State: [alpha, beta]
self.state = np.array([0.0, 1.0])
self.covariance = np.eye(2) * 1.0

# State transition model
F = np.eye(2)  # Identity matrix
Q = np.eye(2) * self.process_noise  # Process noise
```

### 2. مدل مشاهده
```python
# Observation model: portfolio_return = alpha + beta * market_return
H = np.array([1.0, market_return])  # Observation matrix
R = self.observation_noise  # Observation noise
```

### 3. به‌روزرسانی
```python
# Innovation
y = portfolio_return - H @ state_pred
S = H @ cov_pred @ H.T + R

# Kalman gain
K = cov_pred @ H.T / S

# Update state
self.state = state_pred + K * y
```

## 📈 نمونه کد استفاده

```python
from utils.alpha_beta_attribution import AlphaBetaAttributionEngine
import pandas as pd
import numpy as np

# ایجاد موتور تحلیل
engine = AlphaBetaAttributionEngine()

# داده‌های نمونه
dates = pd.date_range('2023-01-01', periods=252, freq='D')
portfolio_returns = pd.Series(np.random.normal(0.001, 0.02, 252), index=dates)
benchmark_returns = pd.Series(np.random.normal(0.0008, 0.015, 252), index=dates)

# محاسبه Alpha/Beta
result = engine.calculate_alpha_beta(portfolio_returns, benchmark_returns)

print(f"Alpha: {result.alpha:.4f}")
print(f"Beta: {result.beta:.4f}")
print(f"R²: {result.r_squared:.4f}")
print(f"Information Ratio: {result.information_ratio:.4f}")
print(f"Sharpe Ratio: {result.sharpe_ratio:.4f}")
print(f"Max Drawdown: {result.max_drawdown:.4f}")

# Alpha/Beta در زمان واقعی
for i in range(10):
    port_ret = np.random.normal(0.001, 0.02)
    market_ret = np.random.normal(0.0008, 0.015)
    
    alpha, beta = engine.realtime_alpha_beta(port_ret, market_ret)
    print(f"Day {i+1}: Alpha={alpha:.4f}, Beta={beta:.4f}")

# تجزیه منابع Alpha
alpha_sources = engine.decompose_alpha_sources(portfolio_returns)
print("منابع Alpha:")
for source, value in alpha_sources.items():
    print(f"  {source}: {value:.4f}")

# تحلیل چندعاملی
factor_returns = pd.DataFrame({
    'market': benchmark_returns,
    'size': np.random.normal(0, 0.01, 252),
    'value': np.random.normal(0, 0.01, 252),
    'momentum': np.random.normal(0, 0.01, 252)
}, index=dates)

multi_factor_result = engine.multi_factor_attribution(portfolio_returns, factor_returns)
print("نتایج تحلیل چندعاملی:")
print(f"Alpha: {multi_factor_result['alpha']:.4f}")
print(f"R²: {multi_factor_result['r_squared']:.4f}")
```

## 🔧 تنظیمات و پیکربندی

### پارامترهای فیلتر کالمن:
- `process_noise`: نویز فرآیند (0.01)
- `observation_noise`: نویز مشاهده (0.1)

### پارامترهای محاسبه:
- `window`: پنجره زمانی محاسبه (252)
- `min_observations`: حداقل مشاهدات (30)

### تنظیمات ML:
- `model_type`: نوع مدل ('random_forest', 'ridge')
- `n_estimators`: تعداد درخت‌ها (100)
- `random_state`: بذر تصادفی (42)

## 🚨 مدیریت خطا

```python
try:
    result = engine.calculate_alpha_beta(portfolio_returns, benchmark_returns)
    if result.observations < 30:
        print("تعداد مشاهدات کافی نیست")
except ValueError as e:
    print(f"خطا در محاسبه Alpha/Beta: {e}")
except Exception as e:
    print(f"خطای غیرمنتظره: {e}")
```

## 📊 متریک‌های عملکرد

### 1. متریک‌های Alpha/Beta
- **Alpha**: بازده اضافی نسبت به بنچمارک
- **Beta**: حساسیت به حرکات بازار
- **R²**: قدرت توضیح‌دهندگی مدل
- **Tracking Error**: انحراف از بنچمارک

### 2. متریک‌های ریسک-بازده
- **Sharpe Ratio**: بازده تنظیم شده بر ریسک
- **Treynor Ratio**: بازده تنظیم شده بر بتا
- **Information Ratio**: بازده فعال تنظیم شده
- **Sortino Ratio**: بازده تنظیم شده بر ریسک نزولی

### 3. متریک‌های Drawdown
- **Max Drawdown**: حداکثر افت
- **Calmar Ratio**: نسبت بازده به حداکثر افت

## 🔗 اتصال به سیستم اصلی

### فایل‌های مرتبط:
- `utils/alpha_beta_attribution.py`: پیاده‌سازی اصلی
- `api/realtime_dashboard.py`: استفاده در داشبورد
- `tests/test_alpha_beta_attribution.py`: تست‌های جامع
- `examples/alpha_beta_attribution_example.py`: نمونه‌های کاربردی

### وضعیت عملیاتی:
✅ **فعال و عملیاتی** - این ماژول در داشبورد realtime و تحلیل‌های عملکرد استفاده می‌شود

## 🎯 بهترین شیوه‌های استفاده

### 1. انتخاب پنجره زمانی
```python
# برای تحلیل کوتاه‌مدت
result = engine.calculate_alpha_beta(returns, benchmark, window=63)  # 3 ماه

# برای تحلیل بلندمدت
result = engine.calculate_alpha_beta(returns, benchmark, window=252)  # 1 سال
```

### 2. استفاده از فیلتر کالمن
```python
# تنظیم نویز بر اساس نوسان بازار
if market_volatility > 0.02:
    engine.kalman_filter = KalmanFilterAlphaBeta(process_noise=0.02, observation_noise=0.15)
else:
    engine.kalman_filter = KalmanFilterAlphaBeta(process_noise=0.01, observation_noise=0.1)
```

### 3. تفسیر نتایج
```python
# تفسیر Alpha
if result.alpha > 0.01:
    print("Alpha مثبت قابل توجه")
elif result.alpha < -0.01:
    print("Alpha منفی قابل توجه")
else:
    print("Alpha خنثی")

# تفسیر Beta
if result.beta > 1.2:
    print("ریسک بالاتر از بازار")
elif result.beta < 0.8:
    print("ریسک کمتر از بازار")
else:
    print("ریسک مشابه بازار")
```

## 🔮 نمودار معماری

```mermaid
graph TD
    A[AlphaBetaAttributionEngine] --> B[KalmanFilterAlphaBeta]
    A --> C[MultiFactorModel]
    A --> D[AlphaMLPredictor]
    
    B --> E[Real-time Alpha/Beta]
    B --> F[State Estimation]
    B --> G[Noise Handling]
    
    C --> H[Factor Exposure]
    C --> I[Attribution Analysis]
    C --> J[Risk Decomposition]
    
    D --> K[Feature Engineering]
    D --> L[ML Models]
    D --> M[Alpha Prediction]
    
    A --> N[Performance Metrics]
    N --> O[Sharpe Ratio]
    N --> P[Information Ratio]
    N --> Q[Treynor Ratio]
    N --> R[Max Drawdown]
    
    A --> S[Alpha Sources]
    S --> T[Security Selection]
    S --> U[Market Timing]
    S --> V[Sector Allocation]
    S --> W[Momentum Effect]
```

## 📚 مفاهیم تئوری

### 1. Alpha (آلفا)
- بازده اضافی نسبت به بنچمارک
- نشان‌دهنده مهارت مدیریت
- فرمول: α = R_p - (R_f + β(R_m - R_f))

### 2. Beta (بتا)
- حساسیت به حرکات بازار
- معیار ریسک سیستماتیک
- فرمول: β = Cov(R_p, R_m) / Var(R_m)

### 3. Information Ratio
- نسبت بازده فعال به ریسک فعال
- فرمول: IR = (R_p - R_b) / TE

### 4. Tracking Error
- انحراف معیار بازده فعال
- فرمول: TE = σ(R_p - R_b)

---

**نکته**: این سیستم برای تحلیل عملکرد و ریسک پرتفوی طراحی شده و ابزار قدرتمندی برای ارزیابی مهارت مدیریت و منابع بازده محسوب می‌شود. 