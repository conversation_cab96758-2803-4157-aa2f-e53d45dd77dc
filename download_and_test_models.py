#!/usr/bin/env python3
"""
🤖 Model Download and Testing Script
اسکریپت دانلود و تست مدل‌ها

این اسکریپت تمام مدل‌های تعریف شده رو دانلود و تست می‌کنه
"""

import sys
import os
import json
import time
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_environment():
    """تنظیم محیط"""
    print("🔧 Setting up environment...")
    
    # Set proxy if needed
    if os.path.exists("PROXY.json"):
        os.environ["HTTP_PROXY"] = "http://127.0.0.1:10809"
        os.environ["HTTPS_PROXY"] = "http://127.0.0.1:10809"
        print("✅ Proxy configured")
    
    # Create directories
    Path("models").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    Path("reports").mkdir(exist_ok=True)
    
    print("✅ Environment ready")

def test_model_download(model_name: str, model_path: str) -> dict:
    """تست دانلود یک مدل"""
    print(f"\n🔽 Testing download: {model_name}")
    
    start_time = time.time()
    result = {
        "model_name": model_name,
        "model_path": model_path,
        "status": "failed",
        "download_time": 0,
        "model_size": 0,
        "error": None
    }
    
    try:
        # Try to import transformers
        from transformers import AutoTokenizer, AutoModel
        
        print(f"  📥 Downloading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        print(f"  📥 Downloading model...")
        model = AutoModel.from_pretrained(model_path)
        
        end_time = time.time()
        result["download_time"] = end_time - start_time
        result["status"] = "success"
        
        # Try to get model size
        try:
            model_size = sum(p.numel() for p in model.parameters()) * 4 / 1024 / 1024  # MB
            result["model_size"] = model_size
        except:
            result["model_size"] = "unknown"
        
        print(f"  ✅ Download successful! Time: {result['download_time']:.1f}s, Size: {result['model_size']:.1f}MB")
        
        # Test basic functionality
        if hasattr(tokenizer, 'encode'):
            test_text = "Bitcoin price is rising"
            tokens = tokenizer.encode(test_text, return_tensors='pt')
            outputs = model(tokens)
            print(f"  ✅ Basic functionality test passed")
        
    except ImportError as e:
        result["error"] = f"Missing dependencies: {e}"
        print(f"  ❌ Missing dependencies: {e}")
    
    except Exception as e:
        result["error"] = str(e)
        print(f"  ❌ Download failed: {e}")
    
    return result

def test_sentiment_models():
    """تست مدل‌های تحلیل احساسات"""
    print("\n🎭 Testing Sentiment Models")
    print("=" * 50)
    
    sentiment_models = {
        "finbert": "ProsusAI/finbert",
        "cryptobert": "ElKulako/cryptobert",
        "financial_sentiment": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"
    }
    
    results = []
    for name, path in sentiment_models.items():
        result = test_model_download(name, path)
        results.append(result)
        
        # Test sentiment analysis if successful
        if result["status"] == "success":
            try:
                from transformers import pipeline
                classifier = pipeline("sentiment-analysis", model=path)
                
                test_texts = [
                    "Bitcoin reaches new all-time high",
                    "Market crashes due to economic uncertainty",
                    "Stable performance in trading session"
                ]
                
                print(f"  🧪 Testing sentiment analysis...")
                for text in test_texts:
                    sentiment = classifier(text)
                    print(f"    '{text}' -> {sentiment[0]['label']} ({sentiment[0]['score']:.2f})")
                
            except Exception as e:
                print(f"  ⚠️ Sentiment test failed: {e}")
    
    return results

def test_time_series_models():
    """تست مدل‌های time series"""
    print("\n📈 Testing Time Series Models")
    print("=" * 50)
    
    time_series_models = {
        "chronos_tiny": "amazon/chronos-t5-tiny",
        "chronos_small": "amazon/chronos-t5-small"
    }
    
    results = []
    for name, path in time_series_models.items():
        result = test_model_download(name, path)
        results.append(result)
        
        # Test time series prediction if successful
        if result["status"] == "success":
            try:
                import numpy as np
                import torch
                from chronos import ChronosPipeline
                
                print(f"  🧪 Testing time series prediction...")
                
                # Create sample time series data
                sample_data = np.random.randn(100).cumsum()
                
                pipeline = ChronosPipeline.from_pretrained(path)
                forecast = pipeline.predict(
                    context=torch.tensor(sample_data),
                    prediction_length=12
                )
                
                print(f"    ✅ Forecast generated: {len(forecast)} predictions")
                
            except ImportError as e:
                print(f"  ⚠️ Chronos library not available: {e}")
            except Exception as e:
                print(f"  ⚠️ Time series test failed: {e}")
    
    return results

def test_embedding_models():
    """تست مدل‌های embedding"""
    print("\n🔤 Testing Embedding Models")
    print("=" * 50)
    
    embedding_models = {
        "sentence_transformer": "sentence-transformers/all-MiniLM-L6-v2",
        "financial_bert": "nlpaueb/sec-bert-base"
    }
    
    results = []
    for name, path in embedding_models.items():
        result = test_model_download(name, path)
        results.append(result)
        
        # Test embedding generation if successful
        if result["status"] == "success":
            try:
                from sentence_transformers import SentenceTransformer
                
                print(f"  🧪 Testing embedding generation...")
                
                model = SentenceTransformer(path)
                
                test_sentences = [
                    "The stock market is bullish today",
                    "Economic indicators show positive growth",
                    "Trading volume increased significantly"
                ]
                
                embeddings = model.encode(test_sentences)
                print(f"    ✅ Embeddings generated: {embeddings.shape}")
                
                # Test similarity
                similarity = model.similarity(embeddings[0], embeddings[1])
                print(f"    📊 Similarity score: {similarity:.3f}")
                
            except ImportError as e:
                print(f"  ⚠️ SentenceTransformers library not available: {e}")
            except Exception as e:
                print(f"  ⚠️ Embedding test failed: {e}")
    
    return results

def test_specialized_models():
    """تست مدل‌های تخصصی"""
    print("\n🎯 Testing Specialized Models")
    print("=" * 50)
    
    specialized_models = {
        "layoutlm": "microsoft/layoutlmv3-base",
        "bart_cnn": "facebook/bart-large-cnn",
        "distilbert_financial": "ProsusAI/finbert"
    }
    
    results = []
    for name, path in specialized_models.items():
        result = test_model_download(name, path)
        results.append(result)
    
    return results

def benchmark_models(results: list):
    """بنچمارک مدل‌ها"""
    print("\n📊 Model Benchmark Results")
    print("=" * 50)
    
    successful_models = [r for r in results if r["status"] == "success"]
    failed_models = [r for r in results if r["status"] == "failed"]
    
    print(f"📈 Successful downloads: {len(successful_models)}")
    print(f"❌ Failed downloads: {len(failed_models)}")
    
    if successful_models:
        print("\n🏆 Top performing models (by download time):")
        sorted_models = sorted(successful_models, key=lambda x: x["download_time"])
        
        for i, model in enumerate(sorted_models[:5], 1):
            print(f"  {i}. {model['model_name']}: {model['download_time']:.1f}s")
        
        print("\n💾 Model sizes:")
        size_sorted = sorted(successful_models, key=lambda x: x["model_size"] if isinstance(x["model_size"], (int, float)) else 0)
        
        for model in size_sorted[-5:]:
            if isinstance(model["model_size"], (int, float)):
                print(f"  {model['model_name']}: {model['model_size']:.1f}MB")
    
    if failed_models:
        print("\n❌ Failed models:")
        for model in failed_models:
            print(f"  {model['model_name']}: {model['error']}")

def save_results(all_results: list):
    """ذخیره نتایج"""
    report = {
        "timestamp": datetime.now().isoformat(),
        "total_models": len(all_results),
        "successful": len([r for r in all_results if r["status"] == "success"]),
        "failed": len([r for r in all_results if r["status"] == "failed"]),
        "results": all_results
    }
    
    report_file = f"reports/model_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Results saved to: {report_file}")
    return report

def main():
    """تابع اصلی"""
    print("🤖 AI Model Download and Testing Tool")
    print("=" * 60)
    
    # Setup
    setup_environment()
    
    # Test all model categories
    all_results = []
    
    try:
        # Test sentiment models
        sentiment_results = test_sentiment_models()
        all_results.extend(sentiment_results)
        
        # Test time series models
        ts_results = test_time_series_models()
        all_results.extend(ts_results)
        
        # Test embedding models
        embedding_results = test_embedding_models()
        all_results.extend(embedding_results)
        
        # Test specialized models
        specialized_results = test_specialized_models()
        all_results.extend(specialized_results)
        
    except KeyboardInterrupt:
        print("\n⚠️ Testing interrupted by user")
    
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    
    # Benchmark and save results
    benchmark_models(all_results)
    report = save_results(all_results)
    
    print("\n" + "=" * 60)
    print("🎯 Summary:")
    print(f"  Total models tested: {report['total_models']}")
    print(f"  Successful: {report['successful']}")
    print(f"  Failed: {report['failed']}")
    print(f"  Success rate: {(report['successful']/report['total_models']*100):.1f}%")
    
    if report['successful'] > 0:
        print("\n✅ Models are ready for use in the trading system!")
    else:
        print("\n⚠️ No models were successfully downloaded. Check internet connection and proxy settings.")

if __name__ == "__main__":
    main() 