# -*- coding: utf-8 -*-
"""
HuggingFace Warnings Fix
حل هشدارهای HuggingFace
"""

import warnings
import os
import logging

# Suppress HuggingFace warnings
warnings.filterwarnings('ignore', category=UserWarning, message='.*Some weights.*were not initialized.*')
warnings.filterwarnings('ignore', category=UserWarning, message='.*You should probably TRAIN.*')
warnings.filterwarnings('ignore', category=UserWarning, message='.*transformers.*')

# Set environment variables
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

def setup_transformers():
    """تنظیم transformers برای عملکرد بهتر"""
    try:
        # Set logging level for transformers
        logging.getLogger('transformers').setLevel(logging.ERROR)
        logging.getLogger('transformers.tokenization_utils').setLevel(logging.ERROR)
        logging.getLogger('transformers.modeling_utils').setLevel(logging.ERROR)
        
        print("✅ HuggingFace warnings suppressed")
        return True
        
    except Exception as e:
        print(f"❌ HuggingFace setup failed: {e}")
        return False

# Initialize transformers
setup_transformers()
