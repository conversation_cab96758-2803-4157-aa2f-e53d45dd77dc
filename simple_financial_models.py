#!/usr/bin/env python3
"""
🆓 استفاده ساده از مدل‌های مالی بدون پیچیدگی
فقط روش‌های ساده و بدون دردسر + پشتیبانی پروکسی

دو روش اصلی:
1. Hugging Face API رایگان (1000 request/ماه)
2. مدل‌های کوچک محلی روی CPU

استفاده:
python simple_financial_models.py
"""

import requests
import json
import time
import hashlib
import pickle
import os
from typing import List, Dict, Any, Optional
import warnings
warnings.filterwarnings("ignore")

try:
    from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
    HF_AVAILABLE = True
except ImportError:
    print("⚠️  transformers نصب نیست. برای نصب: pip install transformers torch")
    HF_AVAILABLE = False

try:
    from huggingface_hub import InferenceClient
    HF_HUB_AVAILABLE = True
except ImportError:
    print("⚠️  huggingface_hub نصب نیست. برای نصب: pip install huggingface_hub")
    HF_HUB_AVAILABLE = False


class SimpleFinancialAnalyzer:
    """تحلیل‌گر مالی ساده - فقط دو روش اصلی + پروکسی"""
    
    # مدل‌های پیشنهادی بر اساس منابع
    MODELS = {
        "high_resource": "ProsusAI/finbert",  # GPU قوی
        "medium_resource": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis", # GPU/CPU متوسط  
        "low_resource": "nlptown/bert-base-multilingual-uncased-sentiment",  # CPU ضعیف
        "api_fallback": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"  # API
    }
    
    def __init__(self, hf_token: Optional[str] = None, cache_file: str = "financial_cache.pkl"):
        """
        راه‌اندازی سیستم
        
        Args:
            hf_token: Hugging Face API token (اختیاری)
            cache_file: فایل کش برای ذخیره نتایج
        """
        self.hf_token = hf_token
        self.cache_file = cache_file
        self.cache = self._load_cache()
        self.proxies = self._load_proxy_config()
        self.model = None
        self.model_type = None
        
        print(f"🔧 راه‌اندازی سیستم...")
        if self.proxies:
            print(f"🌐 پروکسی فعال: {self.proxies['http']}")
        
        self._setup_model()
    
    def _load_proxy_config(self) -> Optional[Dict[str, str]]:
        """لود تنظیمات پروکسی"""
        try:
            with open("PROXY.json", "r", encoding="utf-8") as f:
                proxy_config = json.load(f)
            
            # استخراج پورت HTTP از تنظیمات
            for inbound in proxy_config.get("inbounds", []):
                if inbound.get("protocol") == "http":
                    port = inbound.get("port", 10809)
                    proxy_url = f"http://127.0.0.1:{port}"
                    return {
                        "http": proxy_url,
                        "https": proxy_url
                    }
            
            # fallback به پورت پیش‌فرض
            return {
                "http": "http://127.0.0.1:10809",
                "https": "http://127.0.0.1:10809"
            }
            
        except Exception as e:
            print(f"⚠️ خطا در لود پروکسی: {e}")
            return None
    
    def _load_cache(self) -> Dict[str, Any]:
        """لود کش"""
        try:
            with open(self.cache_file, "rb") as f:
                return pickle.load(f)
        except:
            return {}
    
    def _save_cache(self):
        """ذخیره کش"""
        with open(self.cache_file, "wb") as f:
            pickle.dump(self.cache, f)
    
    def _setup_model(self):
        """راه‌اندازی مدل بهینه"""
        if not HF_AVAILABLE:
            print("❌ کتابخانه transformers در دسترس نیست")
            self._setup_api_model()
            return
        
        try:
            # اول سعی کن API تست کنی
            if self.hf_token and self._test_api():
                self.model_type = "API"
                print("✅ API با توکن آماده است")
            elif self._test_local():
                self._setup_local_model()
            else:
                self._setup_api_model()
        except Exception as e:
            print(f"⚠️ خطا در راه‌اندازی: {e}")
            self._setup_api_model()
    
    def _test_api(self) -> bool:
        """تست API با پروکسی"""
        try:
            headers = {"Authorization": f"Bearer {self.hf_token}"}
            url = "https://api-inference.huggingface.co/models/mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"
            
            response = requests.post(
                url,
                headers=headers,
                json={"inputs": "test"},
                timeout=10,
                proxies=self.proxies
            )
            
            return response.status_code == 200
        except Exception as e:
            print(f"⚠️ تست API ناموفق: {e}")
            return False
    
    def _test_local(self) -> bool:
        """تست مدل محلی"""
        try:
            # تست import
            from transformers import pipeline
            
            # تست لود مدل کوچک
            model = pipeline(
                "sentiment-analysis",
                model="nlptown/bert-base-multilingual-uncased-sentiment",
                device=-1  # CPU
            )
            
            # تست سریع
            result = model("test")
            return len(result) > 0
            
        except Exception as e:
            print(f"⚠️ تست مدل محلی ناموفق: {e}")
            return False
    
    def _setup_local_model(self):
        """راه‌اندازی مدل محلی"""
        try:
            print("🔧 در حال لود مدل محلی...")
            from transformers import pipeline
            
            self.model = pipeline(
                "sentiment-analysis",
                model="nlptown/bert-base-multilingual-uncased-sentiment",
                device=-1,
                return_all_scores=True
            )
            self.model_type = "LOCAL"
            print("✅ مدل محلی آماده است")
        except Exception as e:
            print(f"❌ خطا در مدل محلی: {e}")
            self._setup_api_model()
    
    def _setup_api_model(self):
        """راه‌اندازی API model"""
        self.model_type = "LIMITED_API"
        print("✅ API محدود آماده است")
    
    def _get_cache_key(self, text: str) -> str:
        """کلید کش"""
        return hashlib.md5(text.encode()).hexdigest()
    
    def analyze_sentiment(self, text: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        تحلیل احساس متن مالی
        
        Args:
            text: متن برای تحلیل
            use_cache: استفاده از کش
            
        Returns:
            نتیجه تحلیل احساس
        """
        # چک کش
        if use_cache:
            cache_key = self._get_cache_key(text)
            if cache_key in self.cache:
                return self.cache[cache_key]
        
        # تحلیل واقعی
        result = self._perform_analysis(text)
        
        # ذخیره در کش
        if use_cache and result and "error" not in result:
            self.cache[cache_key] = result
            self._save_cache()
        
        return result
    
    def _perform_analysis(self, text: str) -> Dict[str, Any]:
        """انجام تحلیل احساس"""
        try:
            if self.model_type == "API":
                return self._analyze_api(text)
            elif self.model_type == "LOCAL":
                return self._analyze_local(text)
            else:
                return self._analyze_limited_api(text)
        except Exception as e:
            return {"error": f"خطا در تحلیل: {str(e)}"}
    
    def _analyze_api(self, text: str) -> Dict[str, Any]:
        """تحلیل با API"""
        try:
            headers = {"Authorization": f"Bearer {self.hf_token}"}
            url = "https://api-inference.huggingface.co/models/mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"
            
            response = requests.post(
                url,
                headers=headers,
                json={"inputs": text},
                timeout=30,
                proxies=self.proxies
            )
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list) and len(data) > 0:
                    result = data[0]
                    return {
                        "method": "API",
                        "sentiment": result["label"].lower(),
                        "confidence": round(result["score"], 3),
                        "success": True
                    }
            
            return {"error": f"API Error: {response.status_code}"}
            
        except Exception as e:
            return {"error": f"خطا در API: {str(e)}"}
    
    def _analyze_local(self, text: str) -> Dict[str, Any]:
        """تحلیل محلی"""
        try:
            result = self.model(text)[0]
            
            # تبدیل به فرمت استاندارد
            label = result["label"]
            if label in ["4 stars", "5 stars"]:
                sentiment = "positive"
            elif label in ["1 star", "2 stars"]:
                sentiment = "negative"
            else:
                sentiment = "neutral"
            
            return {
                "method": "LOCAL",
                "sentiment": sentiment,
                "confidence": round(result["score"], 3),
                "success": True
            }
            
        except Exception as e:
            return {"error": f"خطا در مدل محلی: {str(e)}"}
    
    def _analyze_limited_api(self, text: str) -> Dict[str, Any]:
        """API محدود بدون token"""
        try:
            url = "https://api-inference.huggingface.co/models/cardiffnlp/twitter-roberta-base-sentiment-latest"
            
            response = requests.post(
                url,
                json={"inputs": text},
                timeout=30,
                proxies=self.proxies
            )
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list) and len(data) > 0:
                    result = data[0]
                    return {
                        "method": "LIMITED_API",
                        "sentiment": result["label"].lower(),
                        "confidence": round(result["score"], 3),
                        "success": True
                    }
            
            return {"error": f"محدود API Error: {response.status_code}"}
            
        except Exception as e:
            return {"error": f"خطا در API محدود: {str(e)}"}
    
    def analyze_multiple(self, texts: List[str]) -> List[Dict[str, Any]]:
        """تحلیل دسته‌ای متن‌ها"""
        results = []
        
        print(f"🔄 تحلیل {len(texts)} متن...")
        
        for i, text in enumerate(texts):
            result = self.analyze_sentiment(text)
            results.append(result)
            
            # نمایش پیشرفت
            if (i + 1) % 5 == 0:
                print(f"✅ {i + 1}/{len(texts)} تکمیل شد")
            
            # تاخیر برای API
            if self.model_type in ["API", "LIMITED_API"]:
                time.sleep(0.5)  # جلوگیری از rate limit
        
        return results
    
    def get_market_sentiment(self, news_list: List[str]) -> Dict[str, Any]:
        """محاسبه احساس کلی بازار"""
        if not news_list:
            return {"error": "لیست اخبار خالی"}
        
        results = self.analyze_multiple(news_list)
        
        # شمارش
        positive = 0
        negative = 0
        neutral = 0
        total_confidence = 0
        errors = 0
        
        for result in results:
            if "error" in result:
                errors += 1
                continue
            
            sentiment = result["sentiment"]
            confidence = result["confidence"]
            
            if sentiment == "positive":
                positive += 1
            elif sentiment == "negative":
                negative += 1
            else:
                neutral += 1
            
            total_confidence += confidence
        
        total_valid = positive + negative + neutral
        
        if total_valid == 0:
            return {"error": "هیچ تحلیل معتبری انجام نشد"}
        
        avg_confidence = total_confidence / total_valid
        
        # تشخیص حالت کلی
        if positive > negative and positive > neutral:
            overall = "BULLISH"
        elif negative > positive and negative > neutral:
            overall = "BEARISH"
        else:
            overall = "NEUTRAL"
        
        return {
            "overall_sentiment": overall,
            "positive_ratio": round(positive / total_valid, 3),
            "negative_ratio": round(negative / total_valid, 3),
            "neutral_ratio": round(neutral / total_valid, 3),
            "average_confidence": round(avg_confidence, 3),
            "total_news": total_valid,
            "errors": errors,
            "method_used": self.model_type
        }
    
    def generate_trading_signal(self, news_list: List[str], price_trend: str = "neutral") -> str:
        """تولید سیگنال معاملاتی ساده"""
        market_sentiment = self.get_market_sentiment(news_list)
        
        if "error" in market_sentiment:
            return "HOLD - خطا در تحلیل"
        
        overall = market_sentiment["overall_sentiment"]
        confidence = market_sentiment["average_confidence"]
        
        # تصمیم‌گیری ساده
        if overall == "BULLISH" and confidence > 0.6:
            if price_trend == "up":
                return "STRONG_BUY"
            else:
                return "BUY"
        elif overall == "BEARISH" and confidence > 0.6:
            if price_trend == "down":
                return "STRONG_SELL"
            else:
                return "SELL"
        else:
            return "HOLD"


def quick_setup():
    """راه‌اندازی سریع"""
    print("🚀 راه‌اندازی سریع سیستم مالی")
    print("=" * 40)
    
    # دریافت token
    hf_token = input("🔑 Hugging Face Token (Enter برای رد کردن): ").strip()
    
    if not hf_token:
        print("⚠️ بدون token، قابلیت‌ها محدود خواهد بود")
        hf_token = None
    
    # راه‌اندازی
    analyzer = SimpleFinancialAnalyzer(hf_token)
    
    return analyzer


def demo():
    """نمایش عملکرد"""
    print("🧪 نمایش عملکرد سیستم")
    print("=" * 30)
    
    # راه‌اندازی
    analyzer = quick_setup()
    
    # تست‌های نمونه
    test_news = [
        "Apple reports record quarterly profits, stock soars",
        "Market crash fears grow as inflation data disappoints",
        "Stable trading observed in major indices today",
        "Tech giants lead rally on strong earnings",
        "Banking sector faces regulatory headwinds"
    ]
    
    print(f"\n📰 تحلیل اخبار نمونه:")
    print("-" * 50)
    
    for news in test_news:
        result = analyzer.analyze_sentiment(news)
        if "error" not in result:
            print(f"📊 {result['sentiment'].upper()} ({result['confidence']}) - {news[:60]}...")
        else:
            print(f"❌ خطا: {news[:60]}...")
    
    # احساس کلی بازار
    print(f"\n📈 احساس کلی بازار:")
    print("-" * 30)
    
    market_sentiment = analyzer.get_market_sentiment(test_news)
    if "error" not in market_sentiment:
        print(f"🎯 حالت کلی: {market_sentiment['overall_sentiment']}")
        print(f"📊 مثبت: {market_sentiment['positive_ratio']:.1%}")
        print(f"📊 منفی: {market_sentiment['negative_ratio']:.1%}")
        print(f"📊 خنثی: {market_sentiment['neutral_ratio']:.1%}")
        print(f"🎲 اطمینان: {market_sentiment['average_confidence']:.1%}")
        print(f"⚙️ روش: {market_sentiment['method_used']}")
    
    # سیگنال معاملاتی
    signal = analyzer.generate_trading_signal(test_news, "up")
    print(f"\n🚦 سیگنال معاملاتی: {signal}")
    
    print(f"\n✅ نمایش کامل شد!")


if __name__ == "__main__":
    demo() 