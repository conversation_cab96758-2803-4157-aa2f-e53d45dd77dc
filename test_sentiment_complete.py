#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

# تنظیم پروکسی
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:10809'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:10809'

from utils.sentiment_analyzer import SentimentAnalyzer

def test_sentiment_analyzer():
    """تست کامل sentiment analyzer"""
    print("🔍 شروع تست Sentiment Analyzer...")
    
    try:
        # ایجاد analyzer
        analyzer = SentimentAnalyzer()
        print("✅ SentimentAnalyzer با موفقیت ایجاد شد")
        
        # تست متون مختلف
        test_texts = [
            ("شرکت اپل سود بالایی گزارش داد", "fa"),
            ("بازار سهام امروز افت کرد", "fa"),
            ("Apple reported high profits", "en"),
            ("Stock market fell today", "en"),
            ("این یک متن خنثی است", "fa")
        ]
        
        results = []
        for text, expected_lang in test_texts:
            try:
                result = analyzer.analyze(text)
                results.append({
                    'text': text,
                    'language': result.language,
                    'label': result.label,
                    'score': result.score,
                    'confidence': result.confidence
                })
                print(f"✅ تحلیل موفق: {text[:30]}... -> {result.label} ({result.score:.3f})")
            except Exception as e:
                print(f"❌ خطا در تحلیل: {text[:30]}... -> {e}")
        
        # خلاصه نتایج
        print("\n📊 خلاصه نتایج:")
        for result in results:
            print(f"  • {result['text'][:25]}... -> {result['label']} (امتیاز: {result['score']:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطا در تست: {e}")
        return False

if __name__ == "__main__":
    success = test_sentiment_analyzer()
    if success:
        print("\n🎉 تست Sentiment Analyzer با موفقیت تکمیل شد!")
    else:
        print("\n💥 تست Sentiment Analyzer با خطا مواجه شد!")
        sys.exit(1) 