"""
🤗 HuggingFace Models
کلاس پایه برای مدل‌های HuggingFace

این ماژول شامل پیاده‌سازی مدل‌های HuggingFace با پشتیبانی پروکسی است
"""

import os
import time
import warnings
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
import numpy as np
import torch
from pathlib import Path

# Suppress warnings
warnings.filterwarnings("ignore")

try:
    from transformers import (
        AutoModel, AutoTokenizer, AutoConfig,
        pipeline, Pipeline
    )
    from transformers.pipelines import SUPPORTED_TASKS
    from huggingface_hub import HfApi, snapshot_download
    HF_AVAILABLE = True
except ImportError as e:
    HF_AVAILABLE = False
    print(f"⚠️ HuggingFace libraries not available: {e}")

from core.base import BaseModel, ModelPrediction
from core.logger import get_logger, log_execution_time
from core.exceptions import ModelLoadError, NetworkError, ResourceError
from core.config import ProxyConfig

logger = get_logger(__name__)

class HuggingFaceModel(BaseModel):
    """کلاس پایه مدل‌های HuggingFace"""
    
    def __init__(self, name: str, model_type: str, config: Dict[str, Any] = None):
        super().__init__(name, model_type, config)
        
        # HuggingFace specific attributes
        self.model_path = None
        self.cache_dir = config.get("cache_dir", "models_cache")
        self.device = config.get("device", "auto")
        self.max_memory = config.get("max_memory", 2048)  # MB
        self.timeout = config.get("timeout", 30)
        self.max_retries = config.get("max_retries", 3)
        
        # Pipeline settings
        self.pipeline_task = config.get("pipeline_task", "auto")
        self.pipeline_kwargs = config.get("pipeline_kwargs", {})
        
        # Model parameters
        self.model_kwargs = config.get("model_kwargs", {})
        self.tokenizer_kwargs = config.get("tokenizer_kwargs", {})
        
        # State
        self.api = None
        self.model_config = None
        self.download_path = None
        
        # Setup cache directory
        self.cache_dir = Path(self.cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Check HuggingFace availability
        if not HF_AVAILABLE:
            raise ModelLoadError(
                "HuggingFace libraries not available. Please install: pip install transformers huggingface_hub",
                model_name=self.name
            )
    
    def initialize(self) -> bool:
        """مقداردهی اولیه"""
        try:
            # Initialize HuggingFace API
            self.api = HfApi()
            
            # Setup device
            if self.device == "auto":
                self.device = "cuda" if torch.cuda.is_available() else "cpu"
            
            self.logger.info(f"HuggingFace model initialized: {self.name} (device: {self.device})")
            self._initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize HuggingFace model {self.name}: {e}")
            return False
    
    def start(self) -> bool:
        """شروع مدل"""
        if not self._initialized:
            if not self.initialize():
                return False
        
        self._running = True
        self.logger.info(f"HuggingFace model started: {self.name}")
        return True
    
    def stop(self) -> bool:
        """توقف مدل"""
        try:
            # Cleanup model and tokenizer
            if hasattr(self, 'model') and self.model is not None:
                del self.model
                self.model = None
            
            if hasattr(self, 'tokenizer') and self.tokenizer is not None:
                del self.tokenizer
                self.tokenizer = None
            
            if hasattr(self, 'pipeline') and self.pipeline is not None:
                del self.pipeline
                self.pipeline = None
            
            # Clear GPU cache if using CUDA
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            self._running = False
            self.is_loaded = False
            self.logger.info(f"HuggingFace model stopped: {self.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping model {self.name}: {e}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت مدل"""
        status = {
            "name": self.name,
            "type": self.model_type,
            "initialized": self._initialized,
            "running": self._running,
            "loaded": self.is_loaded,
            "device": self.device,
            "model_path": self.model_path,
            "cache_dir": str(self.cache_dir),
            "timestamp": datetime.now().isoformat()
        }
        
        # Check model availability
        if self.is_loaded:
            status["model_available"] = self.model is not None
            status["tokenizer_available"] = self.tokenizer is not None
            status["pipeline_available"] = self.pipeline is not None
        
        # Check GPU status
        if torch.cuda.is_available():
            status["gpu_available"] = True
            status["gpu_memory"] = torch.cuda.get_device_properties(0).total_memory / (1024**3)  # GB
        else:
            status["gpu_available"] = False
        
        return status
    
    @log_execution_time()
    def load_model(self, model_path: str, **kwargs) -> bool:
        """بارگذاری مدل"""
        try:
            self.model_path = model_path
            
            # Check if model exists in HuggingFace
            if not self._check_model_exists(model_path):
                raise ModelLoadError(f"Model not found: {model_path}", model_name=self.name)
            
            # Download model if not cached
            local_path = self._download_model(model_path)
            
            # Load model components
            self._load_model_components(local_path, **kwargs)
            
            # Create pipeline if specified
            if self.pipeline_task != "none":
                self._create_pipeline()
            
            self.is_loaded = True
            self.logger.info(f"✅ Model loaded successfully: {self.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load model {self.name}: {e}")
            self.is_loaded = False
            raise ModelLoadError(f"Failed to load model {self.name}: {str(e)}", model_name=self.name, model_path=model_path)
    
    def _check_model_exists(self, model_path: str) -> bool:
        """بررسی وجود مدل"""
        try:
            # Check with HuggingFace API
            model_info = self.api.model_info(model_path, timeout=self.timeout)
            return model_info is not None
            
        except Exception as e:
            self.logger.warning(f"Could not verify model existence: {e}")
            return True  # Assume it exists and let the download fail if not
    
    def _download_model(self, model_path: str) -> str:
        """دانلود مدل"""
        try:
            # Set cache directory
            cache_dir = self.cache_dir / model_path.replace("/", "_")
            
            # Check if already downloaded
            if cache_dir.exists() and any(cache_dir.iterdir()):
                self.logger.info(f"Using cached model: {model_path}")
                return str(cache_dir)
            
            # Download model
            self.logger.info(f"Downloading model: {model_path}")
            local_path = snapshot_download(
                model_path,
                cache_dir=str(cache_dir),
                timeout=self.timeout,
                **self.model_kwargs
            )
            
            self.download_path = local_path
            return local_path
            
        except Exception as e:
            raise NetworkError(f"Failed to download model {model_path}: {str(e)}", url=model_path)
    
    def _load_model_components(self, local_path: str, **kwargs):
        """بارگذاری اجزای مدل"""
        try:
            # Load configuration
            self.model_config = AutoConfig.from_pretrained(local_path, **self.model_kwargs)
            
            # Load tokenizer
            self.logger.info(f"Loading tokenizer from: {local_path}")
            self.tokenizer = AutoTokenizer.from_pretrained(
                local_path, 
                **self.tokenizer_kwargs
            )
            
            # Load model
            self.logger.info(f"Loading model from: {local_path}")
            
            # Determine device and memory settings
            device_map = None
            if self.device == "auto" and torch.cuda.is_available():
                device_map = "auto"
            
            model_kwargs = {
                "torch_dtype": torch.float16 if torch.cuda.is_available() else torch.float32,
                "device_map": device_map,
                **self.model_kwargs,
                **kwargs
            }
            
            self.model = AutoModel.from_pretrained(
                local_path,
                config=self.model_config,
                **model_kwargs
            )
            
            # Move to device if not using device_map
            if device_map is None:
                self.model = self.model.to(self.device)
            
            # Set to evaluation mode
            self.model.eval()
            
            self.logger.info(f"Model components loaded successfully")
            
        except Exception as e:
            raise ModelLoadError(f"Failed to load model components: {str(e)}", model_name=self.name)
    
    def _create_pipeline(self):
        """ایجاد pipeline"""
        try:
            # Determine pipeline task
            task = self.pipeline_task
            if task == "auto":
                task = self._infer_pipeline_task()
            
            if task == "none":
                return
            
            # Create pipeline
            self.logger.info(f"Creating pipeline for task: {task}")
            
            pipeline_kwargs = {
                "model": self.model,
                "tokenizer": self.tokenizer,
                "device": 0 if self.device == "cuda" else -1,
                "torch_dtype": torch.float16 if torch.cuda.is_available() else torch.float32,
                **self.pipeline_kwargs
            }
            
            self.pipeline = pipeline(task, **pipeline_kwargs)
            
            self.logger.info(f"Pipeline created successfully for task: {task}")
            
        except Exception as e:
            self.logger.warning(f"Failed to create pipeline: {e}")
            self.pipeline = None
    
    def _infer_pipeline_task(self) -> str:
        """تشخیص نوع pipeline"""
        model_type = self.model_type.lower()
        
        if "sentiment" in model_type:
            return "sentiment-analysis"
        elif "summarization" in model_type:
            return "summarization"
        elif "translation" in model_type:
            return "translation"
        elif "question" in model_type:
            return "question-answering"
        elif "classification" in model_type:
            return "text-classification"
        elif "generation" in model_type:
            return "text-generation"
        else:
            return "feature-extraction"
    
    def predict(self, input_data: Any, **kwargs) -> ModelPrediction:
        """پیش‌بینی"""
        if not self.is_loaded:
            raise ModelLoadError("Model not loaded", model_name=self.name)
        
        start_time = time.time()
        
        try:
            # Preprocess input
            processed_input = self.preprocess(input_data)
            
            # Make prediction
            if self.pipeline is not None:
                raw_prediction = self.pipeline(processed_input, **kwargs)
            else:
                raw_prediction = self._predict_with_model(processed_input, **kwargs)
            
            # Postprocess result
            processed_prediction = self.postprocess(raw_prediction)
            
            # Calculate confidence
            confidence = self._calculate_confidence(raw_prediction)
            
            # Create prediction object
            prediction = ModelPrediction(
                model_name=self.name,
                model_version=getattr(self.model_config, 'version', '1.0.0'),
                prediction=processed_prediction,
                confidence=confidence,
                timestamp=datetime.now(),
                processing_time=time.time() - start_time,
                metadata={
                    "model_type": self.model_type,
                    "model_path": self.model_path,
                    "device": self.device,
                    "input_type": type(input_data).__name__,
                    "raw_prediction": raw_prediction
                }
            )
            
            return prediction
            
        except Exception as e:
            self.logger.error(f"Prediction failed for {self.name}: {e}")
            raise ModelLoadError(f"Prediction failed: {str(e)}", model_name=self.name)
    
    def _predict_with_model(self, input_data: Any, **kwargs) -> Any:
        """پیش‌بینی با مدل مستقیم"""
        # Tokenize input
        inputs = self.tokenizer(
            input_data,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        )
        
        # Move to device
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Forward pass
        with torch.no_grad():
            outputs = self.model(**inputs)
        
        return outputs
    
    def preprocess(self, data: Any) -> Any:
        """پیش‌پردازش داده"""
        if isinstance(data, str):
            return data
        elif isinstance(data, list):
            return data
        elif isinstance(data, dict):
            return data.get("text", str(data))
        else:
            return str(data)
    
    def postprocess(self, prediction: Any) -> Any:
        """پس‌پردازش نتیجه"""
        if isinstance(prediction, torch.Tensor):
            return prediction.cpu().numpy()
        elif isinstance(prediction, list) and len(prediction) > 0:
            if isinstance(prediction[0], dict):
                return prediction[0]
            return prediction
        else:
            return prediction
    
    def _calculate_confidence(self, prediction: Any) -> float:
        """محاسبه اطمینان"""
        try:
            if isinstance(prediction, list) and len(prediction) > 0:
                if isinstance(prediction[0], dict) and "score" in prediction[0]:
                    return float(prediction[0]["score"])
            elif isinstance(prediction, dict) and "score" in prediction:
                return float(prediction["score"])
            elif hasattr(prediction, 'logits'):
                # Calculate softmax confidence
                logits = prediction.logits
                if isinstance(logits, torch.Tensor):
                    probs = torch.softmax(logits, dim=-1)
                    return float(torch.max(probs).cpu().numpy())
            
            return 0.5  # Default confidence
            
        except Exception as e:
            self.logger.warning(f"Could not calculate confidence: {e}")
            return 0.5
    
    def get_model_info(self) -> Dict[str, Any]:
        """اطلاعات مدل"""
        info = super().get_model_info()
        
        # Add HuggingFace specific info
        info.update({
            "model_path": self.model_path,
            "cache_dir": str(self.cache_dir),
            "device": self.device,
            "has_pipeline": self.pipeline is not None,
            "pipeline_task": self.pipeline_task,
            "download_path": self.download_path
        })
        
        # Add model config info
        if self.model_config is not None:
            info["model_config"] = {
                "model_type": getattr(self.model_config, 'model_type', 'unknown'),
                "vocab_size": getattr(self.model_config, 'vocab_size', 0),
                "hidden_size": getattr(self.model_config, 'hidden_size', 0),
                "num_attention_heads": getattr(self.model_config, 'num_attention_heads', 0),
                "num_hidden_layers": getattr(self.model_config, 'num_hidden_layers', 0)
            }
        
        return info
    
    def batch_predict(self, batch_data: List[Any], **kwargs) -> List[ModelPrediction]:
        """پیش‌بینی دسته‌ای"""
        if not self.is_loaded:
            raise ModelLoadError("Model not loaded", model_name=self.name)
        
        results = []
        batch_size = kwargs.get("batch_size", 8)
        
        # Process in batches
        for i in range(0, len(batch_data), batch_size):
            batch = batch_data[i:i + batch_size]
            
            try:
                if self.pipeline is not None:
                    # Use pipeline for batch processing
                    raw_predictions = self.pipeline(batch, **kwargs)
                    
                    # Convert to individual predictions
                    for j, raw_pred in enumerate(raw_predictions):
                        prediction = ModelPrediction(
                            model_name=self.name,
                            model_version=getattr(self.model_config, 'version', '1.0.0'),
                            prediction=self.postprocess(raw_pred),
                            confidence=self._calculate_confidence(raw_pred),
                            timestamp=datetime.now(),
                            processing_time=0.0,  # Not measured per item
                            metadata={
                                "batch_index": i + j,
                                "batch_size": len(batch)
                            }
                        )
                        results.append(prediction)
                else:
                    # Process individually
                    for item in batch:
                        prediction = self.predict(item, **kwargs)
                        results.append(prediction)
                        
            except Exception as e:
                self.logger.error(f"Batch prediction failed: {e}")
                # Add error predictions for failed batch
                for item in batch:
                    error_prediction = ModelPrediction(
                        model_name=self.name,
                        model_version="error",
                        prediction=None,
                        confidence=0.0,
                        timestamp=datetime.now(),
                        processing_time=0.0,
                        metadata={"error": str(e)}
                    )
                    results.append(error_prediction)
        
        return results
    
    def update_config(self, new_config: Dict[str, Any]):
        """به‌روزرسانی تنظیمات"""
        self.config.update(new_config)
        
        # Update specific attributes
        if "device" in new_config:
            self.device = new_config["device"]
        if "max_memory" in new_config:
            self.max_memory = new_config["max_memory"]
        if "timeout" in new_config:
            self.timeout = new_config["timeout"]
        
        self.logger.info(f"Configuration updated for {self.name}")
    
    def clear_cache(self):
        """پاک کردن کش"""
        try:
            if self.cache_dir.exists():
                import shutil
                shutil.rmtree(self.cache_dir)
                self.cache_dir.mkdir(parents=True, exist_ok=True)
                self.logger.info(f"Cache cleared for {self.name}")
        except Exception as e:
            self.logger.error(f"Failed to clear cache: {e}")
    
    def __del__(self):
        """تمیزسازی"""
        try:
            self.stop()
        except Exception:
            pass 