import pytest
from utils.config import get_config

def test_default_config():
    config = get_config('nonexistent.yaml')
    assert 'lot_size' in config
    assert 'indicators' in config
    assert isinstance(config['indicators'], dict)

def test_config_yaml(tmp_path):
    yaml_content = """
lot_size: 0.2
stop_loss: 15
take_profit: 30
indicators:
  rsi:
    period: 10
  ma:
    period: 25
"""
    config_file = tmp_path / "config.yaml"
    config_file.write_text(yaml_content, encoding="utf-8")
    config = get_config(str(config_file))
    assert config['lot_size'] == 0.2
    assert config['indicators']['rsi']['period'] == 10
