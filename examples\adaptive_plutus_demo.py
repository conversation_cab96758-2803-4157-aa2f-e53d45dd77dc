"""
مثال کاربردی سیستم تطبیقی Plutus
Adaptive Plutus System Demo
"""

import os
import sys
import time
import json
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.adaptive_plutus_system import AdaptivePlutusSystem, PerformanceDatabase
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def demo_adaptive_learning():
    """نمایش یادگیری تطبیقی"""
    print("🚀 Adaptive Plutus System Demo")
    print("=" * 50)
    
    # ایجاد سیستم
    system = AdaptivePlutusSystem("demo_adaptive.db")
    
    # 1. اجرای بهینه‌سازی اولیه
    print("\n📊 Running Initial Optimization...")
    print("-" * 30)
    
    optimization_results = system.run_comprehensive_optimization(["EURUSD"])
    
    if "EURUSD" in optimization_results["symbols"]:
        eurusd_result = optimization_results["symbols"]["EURUSD"]
        if not eurusd_result.get("error"):
            metrics = eurusd_result["performance_metrics"]
            print(f"✅ EURUSD Optimization Complete:")
            print(f"   Win Rate: {metrics['win_rate']:.1%}")
            print(f"   Total P&L: {metrics['total_pnl']:.2f}")
            print(f"   Profit Factor: {metrics['profit_factor']:.2f}")
            print(f"   Max Drawdown: {metrics['max_drawdown']:.1%}")
    
    # 2. تست سیگنال‌های تطبیقی
    print("\n🎯 Testing Adaptive Signals...")
    print("-" * 30)
    
    test_symbols = ["EURUSD", "GBPUSD", "USDJPY"]
    
    for symbol in test_symbols:
        signal = system.get_adaptive_signal(symbol)
        
        if not signal.get("error"):
            combined = signal.get("combined_signal", {})
            recommendation = signal.get("recommendation", {})
            
            print(f"\n{symbol}:")
            print(f"  Trend: {combined.get('trend', 'N/A')}")
            print(f"  Confidence: {combined.get('confidence', 0):.1%}")
            print(f"  Action: {recommendation.get('action', 'HOLD')}")
            print(f"  Reason: {recommendation.get('reason', 'No reason')}")
            
            # نمایش وزن‌های تطبیقی
            if "adaptive_weights" in combined:
                weights = combined["adaptive_weights"]
                print(f"  Adaptive Weights:")
                print(f"    Chronos: {weights.get('chronos_weight', 0):.3f}")
                print(f"    FinGPT: {weights.get('fingpt_weight', 0):.3f}")
        else:
            print(f"\n{symbol}: ❌ {signal['error']}")
    
    # 3. شروع یادگیری مداوم
    print("\n🧠 Starting Continuous Learning...")
    print("-" * 30)
    print("System will learn and adapt for 2 minutes...")
    
    system.start_continuous_learning(["EURUSD", "GBPUSD"], update_interval=60)
    
    # نمایش پیشرفت یادگیری
    for i in range(12):  # 2 دقیقه با intervals 10 ثانیه‌ای
        time.sleep(10)
        
        # نمایش سیگنال به‌روزرسانی شده
        signal = system.get_adaptive_signal("EURUSD")
        if not signal.get("error"):
            combined = signal.get("combined_signal", {})
            print(f"⏱️  Update {i+1}/12 - EURUSD Confidence: {combined.get('confidence', 0):.1%}")
    
    # 4. تولید گزارش یادگیری
    print("\n📋 Generating Learning Report...")
    print("-" * 30)
    
    report = system.generate_learning_report("EURUSD", 1)  # گزارش 1 روزه
    print(report)
    
    # 5. توقف سیستم
    system.stop_continuous_learning()
    print("\n✅ Demo completed successfully!")
    
    return optimization_results

def demo_weight_optimization():
    """نمایش بهینه‌سازی وزن‌ها"""
    print("\n🔧 Weight Optimization Demo")
    print("=" * 40)
    
    # ایجاد پایگاه داده
    db = PerformanceDatabase("weight_demo.db")
    
    # شبیه‌سازی داده‌های عملکرد
    from utils.adaptive_plutus_system import ModelPerformanceMetrics, AdaptiveLearningEngine
    
    learning_engine = AdaptiveLearningEngine(db)
    
    # اضافه کردن داده‌های تست
    test_data = [
        # Chronos بهتر در روندهای قوی
        ModelPerformanceMetrics(
            symbol="EURUSD", timeframe="H1", model_name="chronos",
            timestamp=datetime.now() - timedelta(hours=i),
            prediction="bullish", confidence=0.9,
            actual_outcome="win", accuracy=1.0, profit_loss=50.0,
            market_conditions={"volatility": 0.015, "trend_direction": "bullish"},
            technical_indicators={}
        ) for i in range(1, 16)
    ] + [
        # FinGPT بهتر در بازارهای پر نوسان
        ModelPerformanceMetrics(
            symbol="EURUSD", timeframe="H1", model_name="fingpt",
            timestamp=datetime.now() - timedelta(hours=i),
            prediction="bearish", confidence=0.7,
            actual_outcome="win", accuracy=1.0, profit_loss=30.0,
            market_conditions={"volatility": 0.025, "trend_direction": "bearish"},
            technical_indicators={}
        ) for i in range(1, 11)
    ]
    
    # ذخیره داده‌های تست
    for metrics in test_data:
        db.save_performance(metrics)
    
    # بهینه‌سازی وزن‌ها
    print("Optimizing weights based on performance data...")
    
    initial_weights = learning_engine.db.get_latest_weights("EURUSD", "H1")
    if not initial_weights:
        print("No initial weights found - using defaults")
    
    optimized_weights = learning_engine.optimize_model_weights("EURUSD", "H1")
    
    print(f"\n📊 Weight Optimization Results:")
    print(f"   Chronos Weight: {optimized_weights.chronos_weight:.3f}")
    print(f"   FinGPT Weight: {optimized_weights.fingpt_weight:.3f}")
    print(f"   Combined Threshold: {optimized_weights.combined_threshold:.3f}")
    print(f"   Confidence Multiplier: {optimized_weights.confidence_multiplier:.3f}")
    
    # تحلیل عملکرد
    chronos_analysis = learning_engine.analyze_model_performance("EURUSD", "chronos", 1)
    fingpt_analysis = learning_engine.analyze_model_performance("EURUSD", "fingpt", 1)
    
    if not chronos_analysis.get("error"):
        print(f"\n📈 Chronos Analysis:")
        print(f"   Accuracy: {chronos_analysis['avg_accuracy']:.1%}")
        print(f"   Avg Profit: {chronos_analysis['avg_profit']:.2f}")
        print(f"   Win Rate: {chronos_analysis['win_rate']:.1%}")
    
    if not fingpt_analysis.get("error"):
        print(f"\n📈 FinGPT Analysis:")
        print(f"   Accuracy: {fingpt_analysis['avg_accuracy']:.1%}")
        print(f"   Avg Profit: {fingpt_analysis['avg_profit']:.2f}")
        print(f"   Win Rate: {fingpt_analysis['win_rate']:.1%}")

def demo_backtest_with_learning():
    """نمایش بک‌تست با یادگیری"""
    print("\n🔄 Adaptive Backtest Demo")
    print("=" * 40)
    
    system = AdaptivePlutusSystem("backtest_demo.db")
    
    print("Running adaptive backtest with learning...")
    print("This will simulate trading with continuous weight optimization")
    
    # اجرای بک‌تست تطبیقی
    backtest_result = system.backtest_engine.run_adaptive_backtest(
        "EURUSD", "H1", periods=50, update_frequency=10
    )
    
    if not backtest_result.get("error"):
        print(f"\n✅ Adaptive Backtest Results:")
        print(f"   Symbol: {backtest_result['symbol']}")
        print(f"   Periods: {backtest_result['periods']}")
        print(f"   Total Trades: {len(backtest_result['trades'])}")
        print(f"   Weight Updates: {len(backtest_result['weight_updates'])}")
        
        metrics = backtest_result['performance_metrics']
        print(f"\n📊 Performance Metrics:")
        print(f"   Win Rate: {metrics['win_rate']:.1%}")
        print(f"   Total P&L: {metrics['total_pnl']:.2f}")
        print(f"   Profit Factor: {metrics['profit_factor']:.2f}")
        print(f"   Max Drawdown: {metrics['max_drawdown']:.1%}")
        print(f"   Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")
        
        # نمایش تطور وزن‌ها
        if backtest_result['weight_updates']:
            print(f"\n⚖️  Weight Evolution:")
            for update in backtest_result['weight_updates'][:5]:  # نمایش 5 تای اول
                weights = update['weights']
                print(f"   Trade {update['trade_number']}: "
                      f"Chronos={weights['chronos_weight']:.3f}, "
                      f"FinGPT={weights['fingpt_weight']:.3f}")
    else:
        print(f"❌ Backtest failed: {backtest_result['error']}")

def demo_real_time_adaptation():
    """نمایش تطبیق زمان واقعی"""
    print("\n⚡ Real-Time Adaptation Demo")
    print("=" * 40)
    
    system = AdaptivePlutusSystem("realtime_demo.db")
    
    print("Monitoring real-time signal adaptation...")
    print("Signals will be updated every 30 seconds for 3 minutes")
    
    symbol = "EURUSD"
    updates = []
    
    for i in range(6):  # 6 updates در 3 دقیقه
        signal = system.get_adaptive_signal(symbol)
        
        if not signal.get("error"):
            combined = signal.get("combined_signal", {})
            recommendation = signal.get("recommendation", {})
            
            update_info = {
                "time": datetime.now().strftime("%H:%M:%S"),
                "confidence": combined.get("confidence", 0),
                "action": recommendation.get("action", "HOLD"),
                "trend": combined.get("trend", "neutral")
            }
            
            updates.append(update_info)
            
            print(f"⏰ {update_info['time']} - "
                  f"Trend: {update_info['trend']}, "
                  f"Confidence: {update_info['confidence']:.1%}, "
                  f"Action: {update_info['action']}")
        
        if i < 5:  # آخرین iteration منتظر نمی‌مانیم
            time.sleep(30)
    
    # تحلیل تغییرات
    if len(updates) > 1:
        confidence_changes = [updates[i]['confidence'] - updates[i-1]['confidence'] 
                            for i in range(1, len(updates))]
        avg_change = sum(confidence_changes) / len(confidence_changes)
        
        print(f"\n📈 Adaptation Analysis:")
        print(f"   Average Confidence Change: {avg_change:+.1%}")
        print(f"   Confidence Trend: {'Improving' if avg_change > 0 else 'Declining'}")

def main():
    """اجرای کامل دمو"""
    print("🎯 ADAPTIVE PLUTUS SYSTEM - COMPLETE DEMO")
    print("=" * 60)
    
    try:
        # 1. یادگیری تطبیقی
        optimization_results = demo_adaptive_learning()
        
        # 2. بهینه‌سازی وزن‌ها
        demo_weight_optimization()
        
        # 3. بک‌تست با یادگیری
        demo_backtest_with_learning()
        
        # 4. تطبیق زمان واقعی
        demo_real_time_adaptation()
        
        # 5. خلاصه نهایی
        print("\n🎉 DEMO SUMMARY")
        print("=" * 30)
        print("✅ Adaptive learning system demonstrated")
        print("✅ Weight optimization working")
        print("✅ Backtest with learning completed")
        print("✅ Real-time adaptation tested")
        
        # ذخیره نتایج
        results_file = Path(__file__).parent.parent / "logs" / "adaptive_demo_results.json"
        results_file.parent.mkdir(exist_ok=True)
        
        with open(results_file, 'w') as f:
            json.dump(optimization_results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: {results_file}")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 