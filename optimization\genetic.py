
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Callable, Optional
import random

class GeneticOptimizer:
    """بهینه‌ساز ژنتیک"""
    
    def __init__(self, objective_function: Callable,
                 parameter_bounds: Dict[str, tuple],
                 population_size: int = 50,
                 n_generations: int = 100,
                 mutation_rate: float = 0.1,
                 crossover_rate: float = 0.8):
        self.objective_function = objective_function
        self.parameter_bounds = parameter_bounds
        self.population_size = population_size
        self.n_generations = n_generations
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        
        # Store results
        self.best_individual = None
        self.best_fitness = float('-inf')
        self.fitness_history = []
        
    def optimize(self) -> Dict[str, Any]:
        """اجرای بهینه‌سازی ژنتیک"""
        
        # Initialize population
        population = self._initialize_population()
        
        print(f"🧬 Starting genetic optimization with {self.population_size} individuals...")
        
        for generation in range(self.n_generations):
            # Evaluate fitness
            fitness_scores = [self.objective_function(individual) for individual in population]
            
            # Update best individual
            max_fitness_idx = np.argmax(fitness_scores)
            if fitness_scores[max_fitness_idx] > self.best_fitness:
                self.best_fitness = fitness_scores[max_fitness_idx]
                self.best_individual = population[max_fitness_idx].copy()
            
            # Store fitness history
            self.fitness_history.append({
                'generation': generation,
                'best_fitness': max(fitness_scores),
                'avg_fitness': np.mean(fitness_scores),
                'std_fitness': np.std(fitness_scores)
            })
            
            # Selection
            selected_population = self._selection(population, fitness_scores)
            
            # Crossover
            offspring = self._crossover(selected_population)
            
            # Mutation
            offspring = self._mutation(offspring)
            
            # Replace population
            population = offspring
            
            if generation % 10 == 0:
                print(f"Generation {generation}: Best = {self.best_fitness:.4f}, "
                      f"Avg = {np.mean(fitness_scores):.4f}")
        
        # Convert best individual to parameter dict
        param_names = list(self.parameter_bounds.keys())
        best_params = dict(zip(param_names, self.best_individual))
        
        return {
            'best_params': best_params,
            'best_fitness': self.best_fitness,
            'n_evaluations': self.n_generations * self.population_size,
            'fitness_history': self.fitness_history
        }
    
    def _initialize_population(self) -> List[List[float]]:
        """مقداردهی اولیه جمعیت"""
        population = []
        param_names = list(self.parameter_bounds.keys())
        
        for _ in range(self.population_size):
            individual = []
            for param_name in param_names:
                low, high = self.parameter_bounds[param_name]
                value = np.random.uniform(low, high)
                individual.append(value)
            population.append(individual)
        
        return population
    
    def _selection(self, population: List[List[float]], 
                  fitness_scores: List[float]) -> List[List[float]]:
        """انتخاب والدین (Tournament Selection)"""
        selected = []
        
        for _ in range(self.population_size):
            # Tournament selection
            tournament_size = 3
            tournament_indices = random.sample(range(len(population)), tournament_size)
            tournament_fitness = [fitness_scores[i] for i in tournament_indices]
            
            winner_idx = tournament_indices[np.argmax(tournament_fitness)]
            selected.append(population[winner_idx].copy())
        
        return selected
    
    def _crossover(self, population: List[List[float]]) -> List[List[float]]:
        """تولید فرزندان (Uniform Crossover)"""
        offspring = []
        
        for i in range(0, len(population), 2):
            parent1 = population[i]
            parent2 = population[i + 1] if i + 1 < len(population) else population[0]
            
            if random.random() < self.crossover_rate:
                # Uniform crossover
                child1, child2 = [], []
                for j in range(len(parent1)):
                    if random.random() < 0.5:
                        child1.append(parent1[j])
                        child2.append(parent2[j])
                    else:
                        child1.append(parent2[j])
                        child2.append(parent1[j])
                
                offspring.extend([child1, child2])
            else:
                offspring.extend([parent1.copy(), parent2.copy()])
        
        return offspring[:self.population_size]
    
    def _mutation(self, population: List[List[float]]) -> List[List[float]]:
        """جهش (Gaussian Mutation)"""
        param_names = list(self.parameter_bounds.keys())
        
        for individual in population:
            for i, param_name in enumerate(param_names):
                if random.random() < self.mutation_rate:
                    low, high = self.parameter_bounds[param_name]
                    
                    # Gaussian mutation
                    mutation_strength = (high - low) * 0.1
                    mutation = np.random.normal(0, mutation_strength)
                    
                    individual[i] += mutation
                    
                    # Ensure bounds
                    individual[i] = np.clip(individual[i], low, high)
        
        return population
