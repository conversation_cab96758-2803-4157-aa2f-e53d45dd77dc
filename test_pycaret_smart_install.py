#!/usr/bin/env python3
"""
🎯 تست سیستم نصب هوشمند PyCaret
"""

import pandas as pd
import numpy as np
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_smart_install_function():
    """تست تابع نصب هوشمند"""
    print("🎯 TESTING SMART INSTALL FUNCTION")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import smart_install_pycaret
        
        print("🔧 Testing smart_install_pycaret function...")
        success, message = smart_install_pycaret()
        
        print(f"   Result: {success}")
        print(f"   Message: {message}")
        
        if success:
            # تست import بعد از نصب
            try:
                import pycaret
                print(f"   ✅ PyCaret version: {pycaret.__version__}")
                return True
            except ImportError:
                print("   ⚠️ PyCaret installed but import failed")
                return False
        else:
            print("   ⚠️ Smart installation was not successful")
            return False
            
    except Exception as e:
        print(f"❌ Smart install test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ensure_pycaret_function():
    """تست تابع تضمین PyCaret"""
    print("\n🛡️ TESTING ENSURE PYCARET FUNCTION")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import ensure_pycaret_available
        
        print("🔧 Testing ensure_pycaret_available function...")
        success, message = ensure_pycaret_available()
        
        print(f"   Result: {success}")
        print(f"   Message: {message}")
        
        return success
        
    except Exception as e:
        print(f"❌ Ensure PyCaret test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pycaret_brain_smart_install():
    """تست نصب هوشمند در PyCaretBrain"""
    print("\n🧠 TESTING PYCARET BRAIN SMART INSTALL")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import PyCaretBrain, PYCARET_AVAILABLE
        
        print(f"🎯 Initial PyCaret status: {PYCARET_AVAILABLE}")
        
        # ایجاد PyCaretBrain (باید خودکار تلاش برای نصب کند)
        print("🔧 Creating PyCaretBrain instance...")
        pycaret_brain = PyCaretBrain()
        
        print("✅ PyCaretBrain created successfully")
        
        # تست تحلیل داده
        print("📊 Testing data analysis...")
        test_data = pd.DataFrame({
            'close': np.random.uniform(1.1000, 1.1100, 50),
            'volume': np.random.randint(1000, 10000, 50),
            'rsi': np.random.uniform(20, 80, 50)
        })
        
        result = pycaret_brain.analyze_data_patterns(test_data)
        print("✅ Data analysis completed")
        print(f"   Result keys: {list(result.keys())}")
        
        # بررسی کلیدهای مورد انتظار
        expected_keys = ['data_quality', 'feature_importance', 'anomaly_detection', 'trend_analysis']
        missing_keys = [key for key in expected_keys if key not in result]
        
        if missing_keys:
            print(f"⚠️ Missing keys: {missing_keys}")
            return False
        else:
            print("✅ All expected keys present")
            return True
            
    except Exception as e:
        print(f"❌ PyCaretBrain smart install test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multibrain_with_pycaret():
    """تست MultiBrainSystem با PyCaret"""
    print("\n🧠 TESTING MULTIBRAIN WITH PYCARET")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import MultiBrainSystem
        
        print("🔧 Creating MultiBrainSystem...")
        multi_brain = MultiBrainSystem()
        
        print("✅ MultiBrainSystem created")
        
        # بررسی وضعیت PyCaret Brain
        has_pycaret = hasattr(multi_brain, 'pycaret_brain') and multi_brain.pycaret_brain is not None
        print(f"🎯 PyCaret Brain available: {'YES' if has_pycaret else 'NO'}")
        
        if has_pycaret:
            # تست تحلیل کامل
            print("🔍 Testing full analysis...")
            test_data = pd.DataFrame({
                'close': np.random.uniform(1.1000, 1.1100, 100),
                'volume': np.random.randint(1000, 10000, 100),
                'rsi': np.random.uniform(20, 80, 100)
            })
            
            analysis = multi_brain.analyze_training_situation(test_data, "LSTM", "EURUSD")
            print("✅ Full analysis completed")
            
            # بررسی کلیدهای ضروری
            required_keys = ['hyperparameter_suggestions', 'config_suggestions', 'action', 'confidence']
            missing_keys = [key for key in required_keys if key not in analysis]
            
            if missing_keys:
                print(f"❌ Missing required keys: {missing_keys}")
                return False
            else:
                print("✅ All required keys present")
                return True
        else:
            print("⚠️ PyCaret Brain not available in MultiBrainSystem")
            return False
            
    except Exception as e:
        print(f"❌ MultiBrain with PyCaret test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_system():
    """تست سیستم کش PyCaret"""
    print("\n💾 TESTING PYCARET CACHE SYSTEM")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import PyCaretBrain
        
        pycaret_brain = PyCaretBrain()
        
        # تست دو بار تحلیل یکسان (دومی باید از کش باشد)
        test_data = pd.DataFrame({
            'close': np.random.uniform(1.1000, 1.1100, 30),
            'volume': np.random.randint(1000, 10000, 30)
        })
        
        print("🔍 First analysis (should compute)...")
        result1 = pycaret_brain.analyze_data_patterns(test_data)
        
        print("🔍 Second analysis (should use cache)...")
        result2 = pycaret_brain.analyze_data_patterns(test_data)
        
        # مقایسه نتایج
        if result1 == result2:
            print("✅ Cache system working correctly")
            return True
        else:
            print("⚠️ Cache system may not be working")
            return False
            
    except Exception as e:
        print(f"❌ Cache system test failed: {e}")
        return False

if __name__ == "__main__":
    print("🎯 PYCARET SMART INSTALLATION SYSTEM TEST")
    print("=" * 60)
    
    # اجرای تمام تست‌ها
    tests = [
        ("Smart Install Function", test_smart_install_function),
        ("Ensure PyCaret Function", test_ensure_pycaret_function),
        ("PyCaretBrain Smart Install", test_pycaret_brain_smart_install),
        ("MultiBrain with PyCaret", test_multibrain_with_pycaret),
        ("Cache System", test_cache_system)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print(f"{'='*60}")
        results[test_name] = test_func()
    
    # خلاصه نتایج
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ PyCaret smart installation system is working perfectly")
        print("🎯 Features working:")
        print("   - ✅ Smart installation detection")
        print("   - ✅ Automatic installation in Colab")
        print("   - ✅ Fallback analytics when not available")
        print("   - ✅ Cache system like other models")
        print("   - ✅ Integration with MultiBrainSystem")
        print("🚀 Ready for production use!")
    else:
        print(f"\n⚠️ {total - passed} tests failed")
        print("🔧 Please check the failed tests above")
