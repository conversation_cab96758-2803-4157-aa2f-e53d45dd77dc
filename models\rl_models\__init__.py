#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ماژول مدل‌های یادگیری تقویتی
"""

import os
import sys
import warnings
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional
import pickle
import json

# Suppress warnings
warnings.filterwarnings('ignore')

try:
    import gym
    from gym import spaces
    from stable_baselines3 import PPO, A2C, DQN
    from stable_baselines3.common.vec_env import DummyVecEnv
    RL_AVAILABLE = True
except ImportError:
    RL_AVAILABLE = False
    print("Warning: stable_baselines3 not available. RL models will not work.")

class RLModelFactory:
    """کارخانه تولید مدل‌های RL"""
    
    def __init__(self):
        if not RL_AVAILABLE:
            raise ImportError("stable_baselines3 is required for RL models")
    
    def create_model(self, model_type: str, environment, **kwargs):
        """ایجاد مدل RL"""
        if model_type.lower() == 'ppo':
            return PPO("MlpPolicy", environment, verbose=1, **kwargs)
        elif model_type.lower() == 'a2c':
            return A2C("MlpPolicy", environment, verbose=1, **kwargs)
        elif model_type.lower() == 'dqn':
            return DQN("MlpPolicy", environment, verbose=1, **kwargs)
        else:
            raise ValueError(f"Unknown model type: {model_type}")
    
    def save_checkpoint(self, model, path: str):
        """ذخیره مدل"""
        model.save(path)
    
    def load_checkpoint(self, path: str):
        """بارگذاری مدل"""
        # Determine model type from path
        if 'ppo' in path.lower():
            return PPO.load(path)
        elif 'a2c' in path.lower():
            return A2C.load(path)
        elif 'dqn' in path.lower():
            return DQN.load(path)
        else:
            raise ValueError(f"Cannot determine model type from path: {path}")

class PPOAgent:
    """عامل PPO"""
    
    def __init__(self, environment, **kwargs):
        self.model = PPO("MlpPolicy", environment, verbose=1, **kwargs)
    
    def learn(self, total_timesteps: int):
        """آموزش مدل"""
        self.model.learn(total_timesteps=total_timesteps)
    
    def predict(self, observation, deterministic=True):
        """پیش‌بینی اکشن"""
        return self.model.predict(observation, deterministic=deterministic)
    
    def save(self, path: str):
        """ذخیره مدل"""
        self.model.save(path)
    
    @classmethod
    def load(cls, path: str):
        """بارگذاری مدل"""
        agent = cls.__new__(cls)
        agent.model = PPO.load(path)
        return agent

class A2CAgent:
    """عامل A2C"""
    
    def __init__(self, environment, **kwargs):
        self.model = A2C("MlpPolicy", environment, verbose=1, **kwargs)
    
    def learn(self, total_timesteps: int):
        """آموزش مدل"""
        self.model.learn(total_timesteps=total_timesteps)
    
    def predict(self, observation, deterministic=True):
        """پیش‌بینی اکشن"""
        return self.model.predict(observation, deterministic=deterministic)
    
    def save(self, path: str):
        """ذخیره مدل"""
        self.model.save(path)
    
    @classmethod
    def load(cls, path: str):
        """بارگذاری مدل"""
        agent = cls.__new__(cls)
        agent.model = A2C.load(path)
        return agent

class DQNAgent:
    """عامل DQN"""
    
    def __init__(self, environment, **kwargs):
        self.model = DQN("MlpPolicy", environment, verbose=1, **kwargs)
    
    def learn(self, total_timesteps: int):
        """آموزش مدل"""
        self.model.learn(total_timesteps=total_timesteps)
    
    def predict(self, observation, deterministic=True):
        """پیش‌بینی اکشن"""
        return self.model.predict(observation, deterministic=deterministic)
    
    def save(self, path: str):
        """ذخیره مدل"""
        self.model.save(path)
    
    @classmethod
    def load(cls, path: str):
        """بارگذاری مدل"""
        agent = cls.__new__(cls)
        agent.model = DQN.load(path)
        return agent

# Export classes
__all__ = [
    'RLModelFactory',
    'PPOAgent', 
    'A2CAgent',
    'DQNAgent'
] 