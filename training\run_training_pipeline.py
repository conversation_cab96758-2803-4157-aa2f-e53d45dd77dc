#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pipeline اصلی آموزش مدل‌ها برای Pearl-3x7B
این فایل توسط Cursor Agent ساخته شده برای استفاده توسط Pearl-3x7B
"""

import os
import sys
import logging
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional
import json
import pickle
from datetime import datetime
import time

# Suppress warnings
warnings.filterwarnings('ignore')

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from training.train_sentiment import SentimentTrainer
from training.train_timeseries import TimeSeriesTrainer
from training.train_rl import RLTrainer
from evaluation.model_comparator import ModelComparator
from core.logger import get_logger
from core.config import get_config

logger = get_logger(__name__)

class TrainingPipeline:
    """کلاس اصلی pipeline آموزش"""
    
    def __init__(self, config_path: str = "configs/pipeline_configs/main_pipeline.json"):
        self.config = self._load_config(config_path)
        self.trainers = {}
        self.results = {}
        self.comparison_results = {}
        
    def _load_config(self, config_path: str) -> Dict:
        """بارگذاری پیکربندی"""
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # Default config
                return {
                    "pipeline_steps": [
                        "sentiment_training",
                        "timeseries_training", 
                        "rl_training",
                        "model_comparison"
                    ],
                    "parallel_training": False,
                    "save_intermediate_results": True,
                    "final_report_path": "training/pipeline_results.json",
                    "models_to_train": {
                        "sentiment": True,
                        "timeseries": True,
                        "rl": True
                    },
                    "data_paths": {
                        "sentiment": "data/",
                        "timeseries": "data/",
                        "rl": "data/"
                    }
                }
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return {}
    
    def initialize_trainers(self):
        """راه‌اندازی trainer ها"""
        logger.info("Initializing model trainers...")
        
        try:
            models_to_train = self.config.get('models_to_train', {})
            
            if models_to_train.get('sentiment', True):
                self.trainers['sentiment'] = SentimentTrainer()
                logger.info("Sentiment trainer initialized")
            
            if models_to_train.get('timeseries', True):
                self.trainers['timeseries'] = TimeSeriesTrainer()
                logger.info("Time series trainer initialized")
            
            if models_to_train.get('rl', True):
                self.trainers['rl'] = RLTrainer()
                logger.info("RL trainer initialized")
            
            logger.info(f"Initialized {len(self.trainers)} trainers")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize trainers: {e}")
            return False
    
    def run_sentiment_training(self) -> Dict[str, Any]:
        """اجرای آموزش مدل تحلیل احساسات"""
        logger.info("Starting sentiment model training...")
        
        if 'sentiment' not in self.trainers:
            logger.error("Sentiment trainer not initialized")
            return {'success': False}
        
        try:
            trainer = self.trainers['sentiment']
            data_path = self.config.get('data_paths', {}).get('sentiment', 'data/')
            
            results = trainer.run_full_training_pipeline()
            
            if results['success']:
                logger.info("Sentiment training completed successfully")
            else:
                logger.error("Sentiment training failed")
            
            return results
            
        except Exception as e:
            logger.error(f"Sentiment training failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def run_timeseries_training(self) -> Dict[str, Any]:
        """اجرای آموزش مدل سری زمانی"""
        logger.info("Starting time series model training...")
        
        if 'timeseries' not in self.trainers:
            logger.error("Time series trainer not initialized")
            return {'success': False}
        
        try:
            trainer = self.trainers['timeseries']
            data_path = self.config.get('data_paths', {}).get('timeseries', 'data/')
            
            results = trainer.run_full_training_pipeline()
            
            if results['success']:
                logger.info("Time series training completed successfully")
            else:
                logger.error("Time series training failed")
            
            return results
            
        except Exception as e:
            logger.error(f"Time series training failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def run_rl_training(self) -> Dict[str, Any]:
        """اجرای آموزش مدل‌های یادگیری تقویتی"""
        logger.info("Starting RL model training...")
        
        if 'rl' not in self.trainers:
            logger.error("RL trainer not initialized")
            return {'success': False}
        
        try:
            trainer = self.trainers['rl']
            data_path = self.config.get('data_paths', {}).get('rl', 'data/')
            
            results = trainer.run_full_training_pipeline()
            
            if results['success']:
                logger.info("RL training completed successfully")
            else:
                logger.error("RL training failed")
            
            return results
            
        except Exception as e:
            logger.error(f"RL training failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def run_model_comparison(self) -> Dict[str, Any]:
        """اجرای مقایسه مدل‌ها"""
        logger.info("Starting model comparison...")
        
        try:
            comparator = ModelComparator()
            results = comparator.run_full_comparison()
            
            if results['success']:
                logger.info("Model comparison completed successfully")
            else:
                logger.error("Model comparison failed")
            
            return results
            
        except Exception as e:
            logger.error(f"Model comparison failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def run_parallel_training(self) -> Dict[str, Any]:
        """اجرای آموزش موازی"""
        logger.info("Starting parallel training...")
        
        try:
            import concurrent.futures
            
            training_functions = {
                'sentiment': self.run_sentiment_training,
                'timeseries': self.run_timeseries_training,
                'rl': self.run_rl_training
            }
            
            results = {}
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                future_to_model = {
                    executor.submit(func): model_type 
                    for model_type, func in training_functions.items()
                    if model_type in self.trainers
                }
                
                for future in concurrent.futures.as_completed(future_to_model):
                    model_type = future_to_model[future]
                    try:
                        result = future.result()
                        results[model_type] = result
                        logger.info(f"{model_type} training completed")
                    except Exception as e:
                        logger.error(f"{model_type} training failed: {e}")
                        results[model_type] = {'success': False, 'error': str(e)}
            
            return results
            
        except Exception as e:
            logger.error(f"Parallel training failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def run_sequential_training(self) -> Dict[str, Any]:
        """اجرای آموزش متوالی"""
        logger.info("Starting sequential training...")
        
        results = {}
        
        # Run sentiment training
        if 'sentiment' in self.trainers:
            results['sentiment'] = self.run_sentiment_training()
        
        # Run time series training
        if 'timeseries' in self.trainers:
            results['timeseries'] = self.run_timeseries_training()
        
        # Run RL training
        if 'rl' in self.trainers:
            results['rl'] = self.run_rl_training()
        
        return results
    
    def save_intermediate_results(self, results: Dict[str, Any], step: str):
        """ذخیره نتایج میانی"""
        if not self.config.get('save_intermediate_results', True):
            return
        
        try:
            # Create results directory
            results_dir = "training/intermediate_results"
            os.makedirs(results_dir, exist_ok=True)
            
            # Save results
            results_path = os.path.join(results_dir, f"{step}_results.json")
            
            # Convert numpy types to native Python types for JSON serialization
            serializable_results = self._make_json_serializable(results)
            
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Intermediate results saved to {results_path}")
            
        except Exception as e:
            logger.error(f"Failed to save intermediate results: {e}")
    
    def _make_json_serializable(self, obj):
        """تبدیل object به فرمت قابل ذخیره در JSON"""
        if isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj
    
    def generate_final_report(self) -> Dict[str, Any]:
        """تولید گزارش نهایی"""
        logger.info("Generating final pipeline report...")
        
        try:
            report = {
                'pipeline_execution_date': datetime.now().isoformat(),
                'pipeline_config': self.config,
                'training_results': self.results,
                'comparison_results': self.comparison_results,
                'summary': self._generate_summary()
            }
            
            # Save final report
            report_path = self.config.get('final_report_path', 'training/pipeline_results.json')
            os.makedirs(os.path.dirname(report_path), exist_ok=True)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Final report saved to {report_path}")
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate final report: {e}")
            return {}
    
    def _generate_summary(self) -> Dict[str, Any]:
        """تولید خلاصه نتایج"""
        summary = {
            'total_models_trained': 0,
            'successful_trainings': 0,
            'failed_trainings': 0,
            'best_models': {},
            'total_training_time': 0,
            'recommendations': []
        }
        
        # Count training results
        for model_type, result in self.results.items():
            summary['total_models_trained'] += 1
            if result.get('success', False):
                summary['successful_trainings'] += 1
                summary['total_training_time'] += result.get('training_time', 0)
            else:
                summary['failed_trainings'] += 1
        
        # Get best models from comparison
        if self.comparison_results.get('best_models'):
            summary['best_models'] = self.comparison_results['best_models']
        
        # Get recommendations
        if self.comparison_results.get('recommendations'):
            recommendations = self.comparison_results['recommendations']
            if recommendations.get('improvement_suggestions'):
                summary['recommendations'] = recommendations['improvement_suggestions']
        
        return summary
    
    def run_full_pipeline(self) -> Dict[str, Any]:
        """اجرای کامل pipeline"""
        logger.info("Starting full training pipeline...")
        
        pipeline_results = {
            'success': False,
            'pipeline_time': None,
            'steps_completed': [],
            'final_report': {}
        }
        
        start_time = datetime.now()
        
        try:
            # Step 1: Initialize trainers
            if not self.initialize_trainers():
                logger.error("Failed to initialize trainers")
                return pipeline_results
            
            # Step 2: Run training
            pipeline_steps = self.config.get('pipeline_steps', [])
            
            if 'sentiment_training' in pipeline_steps or 'timeseries_training' in pipeline_steps or 'rl_training' in pipeline_steps:
                if self.config.get('parallel_training', False):
                    training_results = self.run_parallel_training()
                else:
                    training_results = self.run_sequential_training()
                
                self.results = training_results
                pipeline_results['steps_completed'].append('training')
                
                # Save intermediate results
                self.save_intermediate_results(training_results, 'training')
            
            # Step 3: Run model comparison
            if 'model_comparison' in pipeline_steps:
                comparison_results = self.run_model_comparison()
                self.comparison_results = comparison_results
                pipeline_results['steps_completed'].append('comparison')
                
                # Save intermediate results
                self.save_intermediate_results(comparison_results, 'comparison')
            
            # Step 4: Generate final report
            final_report = self.generate_final_report()
            pipeline_results['final_report'] = final_report
            
            # Step 5: Update pipeline results
            pipeline_results.update({
                'success': True,
                'pipeline_time': (datetime.now() - start_time).total_seconds(),
                'training_results': self.results,
                'comparison_results': self.comparison_results
            })
            
            logger.info("Full pipeline completed successfully")
            
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            pipeline_results['error'] = str(e)
        
        return pipeline_results


def main():
    """تابع اصلی برای اجرای pipeline"""
    pipeline = TrainingPipeline()
    results = pipeline.run_full_pipeline()
    
    # Print results
    print("\n" + "="*60)
    print("TRAINING PIPELINE RESULTS")
    print("="*60)
    print(f"Success: {results['success']}")
    if results['pipeline_time']:
        print(f"Pipeline Time: {results['pipeline_time']:.2f} seconds")
    print(f"Steps Completed: {results['steps_completed']}")
    
    if results.get('final_report', {}).get('summary'):
        summary = results['final_report']['summary']
        print(f"\nSummary:")
        print(f"  Total Models Trained: {summary['total_models_trained']}")
        print(f"  Successful Trainings: {summary['successful_trainings']}")
        print(f"  Failed Trainings: {summary['failed_trainings']}")
        print(f"  Total Training Time: {summary['total_training_time']:.2f} seconds")
        
        if summary.get('best_models'):
            print(f"\nBest Models:")
            for model_type, metrics in summary['best_models'].items():
                print(f"  {model_type.upper()}: Composite Score {metrics.get('composite_score', 0):.4f}")
        
        if summary.get('recommendations'):
            print(f"\nRecommendations:")
            for rec in summary['recommendations']:
                print(f"  - {rec}")
    
    print("="*60)


if __name__ == "__main__":
    main() 