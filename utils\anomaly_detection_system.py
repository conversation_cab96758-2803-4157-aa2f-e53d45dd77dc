#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from typing import Union, Tuple, Optional

class AnomalyDetectionSystem:
    """سیستم پیشرفته تشخیص ناهنجاری"""
    
    def __init__(self, contamination: float = 0.1, random_state: int = 42):
        """
        Initialize anomaly detection system
        
        :param contamination: درصد تخمینی داده‌های ناهنجار
        :param random_state: تضمین تکرارپذیری نتایج
        """
        self.contamination = contamination
        self.random_state = random_state
        self.scaler = StandardScaler()
        self.detector = None
    
    def detect_anomalies(self, data: Union[pd.DataFrame, np.ndarray], 
                         model_type: str = 'generic') -> Tuple[np.ndarray, np.ndarray]:
        """
        تشخیص ناهنجاری‌ها با استفاده از روش‌های مختلف
        
        :param data: داده‌های ورودی
        :param model_type: نوع مدل برای تنظیم پارامترهای تشخیص ناهنجاری
        :return: آرایه سالم و آرایه ناهنجار
        """
        try:
            # تبدیل به DataFrame اگر NumPy آرایه باشد
            if isinstance(data, np.ndarray):
                data = pd.DataFrame(data)
            
            # اگر داده خالی است، خطا برگردان
            if len(data) == 0:
                raise ValueError("داده‌ای برای تشخیص ناهنجاری وجود ندارد")
            
            # انتخاب ویژگی‌ها
            features = self._select_features(data, model_type)
            
            # مقیاس‌سازی داده‌ها
            X_scaled = self.scaler.fit_transform(features)
            
            # تنظیم پارامترهای Isolation Forest بر اساس نوع مدل
            contamination = self._get_contamination(model_type)
            
            # ایجاد و آموزش مدل
            self.detector = IsolationForest(
                contamination=contamination, 
                random_state=self.random_state
            )
            
            # تشخیص ناهنجاری‌ها
            anomaly_labels = self.detector.fit_predict(X_scaled)
            
            # جداسازی داده‌های سالم و ناهنجار
            normal_data = data[anomaly_labels != -1]
            anomalous_data = data[anomaly_labels == -1]
            
            # گزارش نتایج
            self._log_anomaly_stats(normal_data, anomalous_data, model_type)
            
            return normal_data, anomalous_data
        
        except Exception as e:
            print(f"خطا در تشخیص ناهنجاری: {e}")
            # در صورت خطا، تمام داده‌ها را سالم در نظر بگیر
            return data, pd.DataFrame()
    
    def _select_features(self, data: pd.DataFrame, model_type: str) -> pd.DataFrame:
        """انتخاب ویژگی‌ها بر اساس نوع مدل"""
        feature_map = {
            'sentiment': ['polarity', 'subjectivity', 'text_length'],
            'timeseries': ['close', 'volume', 'sma_20', 'ema_12', 'rsi'],
            'rl': ['reward', 'action_value', 'state_value', 'entropy'],
            'generic': list(data.select_dtypes(include=[np.number]).columns)
        }
        
        # انتخاب ویژگی‌ها
        selected_features = feature_map.get(model_type, feature_map['generic'])
        
        # اگر ویژگی‌های انتخابی در داده وجود ندارند، از تمام ستون‌های عددی استفاده کن
        available_features = [f for f in selected_features if f in data.columns]
        
        if not available_features:
            available_features = list(data.select_dtypes(include=[np.number]).columns)
        
        return data[available_features]
    
    def _get_contamination(self, model_type: str) -> float:
        """تعیین نرخ آلودگی بر اساس نوع مدل"""
        contamination_map = {
            'sentiment': 0.05,  # دقت بیشتر برای داده‌های متنی
            'timeseries': 0.1,  # نرخ استاندارد برای سری زمانی
            'rl': 0.15,  # کمی آزادتر برای مدل‌های تقویتی
            'generic': 0.1  # نرخ پیش‌فرض
        }
        
        return contamination_map.get(model_type, 0.1)
    
    def _log_anomaly_stats(self, normal_data: pd.DataFrame, 
                           anomalous_data: pd.DataFrame, 
                           model_type: str):
        """ثبت آمار ناهنجاری‌ها"""
        total_samples = len(normal_data) + len(anomalous_data)
        anomaly_percentage = (len(anomalous_data) / total_samples) * 100 if total_samples > 0 else 0
        
        print(f"آمار ناهنجاری برای مدل {model_type}:")
        print(f"کل نمونه‌ها: {total_samples}")
        print(f"نمونه‌های سالم: {len(normal_data)}")
        print(f"نمونه‌های ناهنجار: {len(anomalous_data)}")
        print(f"درصد ناهنجاری: {anomaly_percentage:.2f}%")

def main():
    # مثال استفاده
    import pandas as pd
    import numpy as np
    
    # تولید داده مصنوعی
    np.random.seed(42)
    data = pd.DataFrame({
        'close': np.random.normal(100, 10, 1000),
        'volume': np.random.normal(1000, 100, 1000),
        'sma_20': np.random.normal(100, 5, 1000)
    })
    
    # اضافه کردن چند نمونه ناهنجار
    data.loc[np.random.choice(data.index, 50), 'close'] = np.random.normal(200, 50, 50)
    
    anomaly_detector = AnomalyDetectionSystem()
    normal_data, anomalous_data = anomaly_detector.detect_anomalies(data, 'timeseries')

if __name__ == '__main__':
    main() 