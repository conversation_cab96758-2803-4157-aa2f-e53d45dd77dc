# 🛡️ راهنمای جامع مدیریت ریسک

## 📊 معرفی سیستم مدیریت ریسک
سیستم مدیریت ریسک یک کامپوننت حیاتی در معاملات الگوریتمی است که از سرمایه معامله‌گر محافظت می‌کند.

## 🔧 اجزای اصلی

### 1. مدیریت ریسک پایه (RiskManager)

#### 1.1. پارامترهای اصلی
```python
risk_manager = RiskManager(
    initial_balance=1000.0,     # موجودی اولیه
    max_daily_drawdown=0.04,    # حداکثر درادون روزانه (4%)
    max_total_drawdown=0.10,    # حداکثر درادون کلی (10%)
    risk_per_trade=0.01,        # ریسک هر معامله (1%)
    max_open_positions=5,       # حداکثر پوزیشن‌های باز
    max_daily_trades=10         # حداکثر معاملات روزانه
)
```

#### 1.2. محاسبات ریسک
- **درادون روزانه**: `(high - low) / high`
- **درادون کلی**: `(highest_balance - current_balance) / highest_balance`
- **نرخ برد**: `wins / total_trades`
- **نسبت ریسک به ریوارد**: `average_win / average_loss`

#### 1.3. مدیریت پوزیشن
```python
# محاسبه حجم مناسب
lot_size, info = risk_manager.calculate_position_size(
    symbol="EURUSD",
    entry_price=1.2000,
    stop_loss=1.1950
)

# باز کردن پوزیشن
position = risk_manager.open_position(
    symbol="EURUSD",
    direction="buy",
    entry_price=1.2000,
    stop_loss=1.1950,
    take_profit=1.2100,
    lot_size=0.1
)
```

### 2. کنترل حاشیه تطبیقی (AdaptiveMarginControl)

#### 2.1. مدل یادگیری عمیق
```python
class MarginRiskModel(nn.Module):
    def __init__(self, input_dim=18, hidden_dim=64):
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.fc3 = nn.Linear(hidden_dim // 2, 1)
```

#### 2.2. ویژگی‌های ورودی
1. **ویژگی‌های بازار**:
   - نوسانات قیمت
   - تغییرات قیمت
   - حجم پوزیشن
   - احساسات بازار

2. **ویژگی‌های زمانی**:
   - ساعت روز (0-24)
   - روز هفته (0-6)
   - روز ماه (1-31)
   - ماه (1-12)
   - آخر هفته (0/1)

3. **ویژگی‌های نماد**:
   - کدگذاری یک-داغ نماد
   - نوع دارایی
   - گروه ارزی

#### 2.3. محاسبه حاشیه
```python
margin = margin_control.calculate_margin_requirement(
    symbol="EURUSD",
    price=1.2000,
    position_size=1.0,
    leverage=100,
    market_data={
        'sentiment': 0.7,
        'volatility': 0.02
    }
)
```

## 📈 استراتژی‌های مدیریت ریسک

### 1. مدیریت درادون
- **درادون روزانه**: توقف معاملات در 4%
- **درادون کلی**: توقف معاملات در 10%
- **کاهش ریسک**: کاهش 50% حجم در 80% حد درادون

### 2. مدیریت پوزیشن
- **حداکثر پوزیشن‌ها**: 5 پوزیشن همزمان
- **حداکثر معاملات**: 10 معامله روزانه
- **فاصله استاپ لاس**: محاسبه خودکار بر اساس نوسانات

### 3. مدیریت حاشیه
- **حاشیه پایه**: تعیین شده برای هر نماد
- **ضریب حاشیه**: 0.5 تا 3.5 برابر حاشیه پایه
- **تعدیل خودکار**: بر اساس شرایط بازار

## 🔍 نظارت و گزارش‌دهی

### 1. متریک‌های کلیدی
```python
metrics = risk_manager.risk_metrics
{
    'daily_drawdown': 0.02,     # درادون روزانه
    'total_drawdown': 0.05,     # درادون کلی
    'volatility': 0.015,        # نوسانات
    'sharpe_ratio': 1.5,        # نسبت شارپ
    'win_rate': 0.65,           # نرخ برد
    'risk_reward_ratio': 2.1    # نسبت ریسک به ریوارد
}
```

### 2. گزارش روزانه
```python
daily_summary = risk_manager.get_daily_summary()
{
    'total_trades': 8,          # تعداد معاملات
    'winning_trades': 5,        # معاملات سودده
    'losing_trades': 3,         # معاملات زیان‌ده
    'profit_factor': 1.8,       # فاکتور سود
    'max_drawdown': 0.02,       # حداکثر درادون
    'risk_level': 'normal'      # سطح ریسک
}
```

### 3. توضیح تصمیمات
```python
explanation = margin_control.explain_margin_decision(
    symbol="EURUSD",
    market_data={'sentiment': 0.7}
)
{
    'base_margin': 0.02,        # حاشیه پایه
    'multiplier': 1.5,          # ضریب حاشیه
    'final_margin': 0.03,       # حاشیه نهایی
    'factors': {
        'volatility': 'high',   # نوسانات بالا
        'sentiment': 'bullish', # احساسات صعودی
        'time': 'peak_hours'    # ساعات اوج
    }
}
```

## ⚙️ تنظیمات و پیکربندی

### 1. فایل پیکربندی
```yaml
risk_management:
  max_daily_drawdown: 0.04
  max_total_drawdown: 0.10
  risk_per_trade: 0.01
  max_positions: 5
  max_daily_trades: 10
  
margin_control:
  base_margins:
    EURUSD: 0.02
    BTCUSD: 0.50
  volatility_window: 20
  max_multiplier: 3.0
  min_multiplier: 0.5
  use_ml_model: true
```

### 2. مسیرهای ذخیره‌سازی
```python
# ذخیره مدل
margin_control.save_model('models/margin_model.pt')

# ذخیره پیکربندی
margin_control.save_config('config/margin_config.json')

# ذخیره تاریخچه
risk_manager.save_history('data/risk_history.csv')
```

## 🚀 بهترین شیوه‌های استفاده

### 1. راه‌اندازی
```python
# مقداردهی اولیه
risk_manager = RiskManager(initial_balance=1000.0)
margin_control = AdaptiveMarginControl(use_ml_model=True)

# بارگذاری پیکربندی
risk_manager.load_config('config/risk_config.json')
margin_control.load_model('models/margin_model.pt')
```

### 2. به‌روزرسانی مستمر
```python
# به‌روزرسانی موجودی
risk_manager.update_balance(new_balance=1050.0)

# به‌روزرسانی قیمت
margin_control.update_price_history(
    symbol="EURUSD",
    price=1.2000
)
```

### 3. بررسی وضعیت
```python
# وضعیت ریسک
status = risk_manager.get_risk_status()
if status['status'] == 'stop_trading':
    # توقف معاملات
    pass

# وضعیت حاشیه
margin_stats = margin_control.get_margin_stats("EURUSD")
if margin_stats['trend'] == 'increasing':
    # افزایش احتیاط
    pass
```

## 🔄 چرخه بهبود مستمر

### 1. جمع‌آوری داده
- ثبت تمام معاملات
- ثبت تغییرات حاشیه
- ثبت شرایط بازار

### 2. تحلیل عملکرد
- محاسبه متریک‌های کلیدی
- شناسایی نقاط ضعف
- بررسی کارایی مدل

### 3. بهینه‌سازی
- تنظیم پارامترها
- آموزش مجدد مدل
- به‌روزرسانی استراتژی‌ها

## ⚠️ مدیریت خطا

### 1. خطاهای رایج
```python
try:
    position = risk_manager.open_position(...)
except MaxPositionsError:
    # تعداد پوزیشن‌ها به حداکثر رسیده
    pass
except MaxDrawdownError:
    # درادون از حد مجاز بیشتر شده
    pass
except MarginError:
    # حاشیه کافی نیست
    pass
```

### 2. بازیابی خودکار
- ذخیره منظم وضعیت
- بازیابی از آخرین وضعیت معتبر
- ثبت خطاها برای تحلیل

## 📊 نمودارها و تحلیل‌ها

### 1. نمودار درادون
```mermaid
graph TD
    A[موجودی] --> B{درادون روزانه > 4%}
    B -->|بله| C[توقف معاملات]
    B -->|خیر| D{درادون کلی > 10%}
    D -->|بله| C
    D -->|خیر| E[ادامه معاملات]
```

### 2. نمودار مدیریت حاشیه
```mermaid
graph LR
    A[شرایط بازار] --> B[مدل یادگیری عمیق]
    B --> C{ضریب حاشیه}
    C -->|> 3.0| D[کاهش اهرم]
    C -->|< 1.0| E[افزایش اهرم]
    C -->|عادی| F[حفظ اهرم]
```

## 📝 نکات مهم

### 1. امنیت
- بررسی مرتب حدود ریسک
- تایید دوباره معاملات بزرگ
- محدودیت دسترسی به تنظیمات

### 2. کارایی
- به‌روزرسانی آسنکرون قیمت‌ها
- محاسبات موازی متریک‌ها
- کش کردن نتایج پرکاربرد

### 3. مستندسازی
- ثبت تمام تصمیمات
- توضیح دلایل تغییرات
- گزارش‌های دوره‌ای 