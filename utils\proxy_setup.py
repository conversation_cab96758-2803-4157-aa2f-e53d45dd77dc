import os
import requests

def setup_proxy():
    """
    تنظیم پروکسی برای دانلود مدل‌های زبانی
    """
    # تنظیم پروکسی HTTP
    os.environ['HTTP_PROXY'] = 'http://127.0.0.1:10809'
    os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:10809'
    
    # تنظیم پروکسی برای کتابخانه requests
    proxies = {
        'http': 'http://127.0.0.1:10809',
        'https': 'http://127.0.0.1:10809',
    }
    
    # پچ کردن کلاس Session در requests
    original_init = requests.Session.__init__
    
    def patched_init(self, *args, **kwargs):
        original_init(self, *args, **kwargs)
        self.proxies.update(proxies)
    
    requests.Session.__init__ = patched_init
    
    # تنظیم متغیر محیطی برای transformers
    os.environ['TRANSFORMERS_OFFLINE'] = '0'
    
    print("پروکسی با موفقیت تنظیم شد.")
    
    return proxies

if __name__ == "__main__":
    setup_proxy()
    print("تست اتصال به اینترنت...")
    try:
        response = requests.get("https://huggingface.co", timeout=5)
        print(f"اتصال موفق: {response.status_code}")
    except Exception as e:
        print(f"خطا در اتصال: {e}") 