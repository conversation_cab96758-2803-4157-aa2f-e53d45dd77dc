# 🧠 Pearl-3x7B Google Colab Training Guide

## 🎯 هدف
آموزش مدل‌های سنگین Pearl-3x7B در Google Colab با استفاده از GPU قدرتمند و انتقال بهترین مدل‌ها به پروژه محلی.

## 📋 فهرست مطالب
1. [تنظیمات اولیه](#تنظیمات-اولیه)
2. [آموزش در Colab](#آموزش-در-colab)
3. [دانلود مدل‌ها](#دانلود-مدل‌ها)
4. [وارد کردن به پروژه محلی](#وارد-کردن-به-پروژه-محلی)
5. [استفاده از مدل‌ها](#استفاده-از-مدل‌ها)

---

## 🚀 تنظیمات اولیه

### 1. آماده‌سازی Google Colab

```python
# در Google Colab اجرا کنید
!git clone https://github.com/your-repo/pearl-3x7b.git
%cd pearl-3x7b

# آپلود فایل colab_brain_trainer.py
from google.colab import files
uploaded = files.upload()  # آپلود colab_brain_trainer.py
```

### 2. فعال‌سازی GPU

1. **Runtime** > **Change runtime type**
2. **Hardware accelerator** > **GPU**
3. انتخاب نوع GPU:
   - **T4** (رایگان): مدل‌های متوسط
   - **V100** (Pro): مدل‌های سنگین
   - **A100** (Pro+): مدل‌های فوق سنگین

### 3. بررسی سیستم

```python
import torch
print(f"GPU Available: {torch.cuda.is_available()}")
print(f"GPU Name: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'None'}")
print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")
```

---

## 🧠 آموزش در Colab

### 1. اجرای مربی مغز متفکر

```python
# بارگذاری اسکریپت
exec(open('colab_brain_trainer.py').read())

# شروع آموزش
session_results = run_colab_brain_training()
```

### 2. مدل‌های قابل آموزش

#### 🔥 **EXTREME Models** (نیاز به A100/V100)
- **GPT2_FinancialLM**: مدل زبان مالی (12GB GPU, 20GB RAM)
- **BERT_Large_Financial**: تحلیل احساسات پیشرفته (10GB GPU, 16GB RAM)
- **Transformer_XL_TimeSeries**: پیش‌بینی سری زمانی (8GB GPU, 12GB RAM)

#### ⚡ **HEAVY Models** (نیاز به T4+)
- **PPO_Agent_Advanced**: عامل تقویتی پیشرفته (6GB GPU, 10GB RAM)
- **SAC_Agent**: Soft Actor-Critic (5GB GPU, 8GB RAM)
- **Vision_Transformer_Charts**: تحلیل نمودارها (7GB GPU, 12GB RAM)

#### 📊 **MEDIUM Models** (T4 کافی است)
- **RoBERTa_Financial**: تحلیل احساسات (4GB GPU, 6GB RAM)
- **LSTM_Attention_TimeSeries**: سری زمانی با توجه (3GB GPU, 5GB RAM)
- **TD3_Agent**: Twin Delayed DDPG (3.5GB GPU, 6GB RAM)

### 3. نظارت بر آموزش

```python
# بررسی وضعیت آموزش
print("🔥 Training in progress...")
print("📊 Monitor GPU usage:")
!nvidia-smi

# بررسی فایل‌های ذخیره شده
!ls -la /content/models/
!ls -la /content/results/
```

---

## 📥 دانلود مدل‌ها

### 1. دانلود خودکار

```python
# پس از اتمام آموزش، فایل zip آماده می‌شود
from google.colab import files

# پیدا کردن فایل zip
import glob
zip_files = glob.glob('/content/pearl_3x7b_models_*.zip')
if zip_files:
    latest_zip = max(zip_files)
    print(f"📦 Downloading: {latest_zip}")
    files.download(latest_zip)
else:
    print("❌ No model package found")
```

### 2. دانلود دستی

```python
# لیست فایل‌های موجود
!ls -la /content/pearl_3x7b_models_*.zip

# دانلود فایل خاص
files.download('/content/pearl_3x7b_models_20250717_143022.zip')
```

---

## 🔄 وارد کردن به پروژه محلی

### 1. استفاده از اسکریپت وارد کننده

```bash
# در پروژه محلی
python colab_model_importer.py path/to/downloaded/models.zip
```

### 2. وارد کردن برنامه‌نویسی

```python
from colab_model_importer import ColabModelImporter

# ایجاد وارد کننده
importer = ColabModelImporter(".")

# وارد کردن مدل‌ها
result = importer.import_colab_models("pearl_3x7b_models_20250717_143022.zip")

if result["success"]:
    print(f"✅ Imported {len(result['imported_models'])} models")
    
    # نمایش مدل‌های وارد شده
    importer.list_colab_models()
else:
    print(f"❌ Import failed: {result['error']}")
```

### 3. بررسی مدل‌های وارد شده

```python
import json

# خواندن رجیستری مدل‌ها
with open('models/trained_models/colab_model_registry.json') as f:
    registry = json.load(f)

print(f"📊 Total Colab Models: {registry['total_models']}")
for name, info in registry['models'].items():
    print(f"🤖 {name}: {info['metadata']['performance']:.3f}")
```

---

## 🎯 استفاده از مدل‌ها

### 1. بارگذاری مدل Colab

```python
# اضافه کردن تابع بارگذاری به base_models.py
def load_colab_model(model_name: str, category: str):
    """بارگذاری مدل آموزش دیده در Colab"""
    import json
    from pathlib import Path
    
    # خواندن رجیستری
    registry_path = Path("models/trained_models/colab_model_registry.json")
    with open(registry_path) as f:
        registry = json.load(f)
    
    if model_name not in registry['models']:
        raise ValueError(f"Model {model_name} not found in registry")
    
    model_info = registry['models'][model_name]
    model_path = Path(model_info['local_path'])
    
    # بارگذاری metadata
    with open(model_path / "metadata.json") as f:
        metadata = json.load(f)
    
    print(f"🤖 Loading {model_name}")
    print(f"   Category: {category}")
    print(f"   Performance: {metadata['performance']:.3f}")
    print(f"   Complexity: {metadata['complexity']}")
    
    # بارگذاری مدل بر اساس دسته
    if category == "sentiment":
        return load_sentiment_model(model_path)
    elif category == "timeseries":
        return load_timeseries_model(model_path)
    elif category == "reinforcement_learning":
        return load_rl_model(model_path)
    else:
        raise ValueError(f"Unknown category: {category}")
```

### 2. استفاده در سیستم معاملاتی

```python
# بارگذاری مدل‌های Colab
gpt2_financial = load_colab_model("GPT2_FinancialLM", "language_model")
ppo_advanced = load_colab_model("PPO_Agent_Advanced", "reinforcement_learning")
bert_large = load_colab_model("BERT_Large_Financial", "sentiment")

# استفاده در تصمیم‌گیری
def enhanced_trading_decision(market_data, news_text):
    # تحلیل احساسات پیشرفته
    sentiment = bert_large.analyze(news_text)
    
    # تولید بینش مالی
    financial_insight = gpt2_financial.generate_insight(market_data)
    
    # تصمیم‌گیری با عامل تقویتی
    action = ppo_advanced.decide(market_data, sentiment, financial_insight)
    
    return action
```

---

## 📊 مقایسه عملکرد

### مدل‌های محلی vs Colab

| مدل | محلی | Colab | بهبود |
|-----|------|-------|--------|
| FinBERT | 87.0% | 92.5% | +5.5% |
| LSTM | 98.2% | 98.8% | +0.6% |
| DQN | 90.0% | 94.2% | +4.2% |

### مزایای آموزش در Colab

✅ **GPU قدرتمند**: T4, V100, A100  
✅ **RAM بالا**: تا 25GB  
✅ **مدل‌های پیچیده**: GPT-2, BERT Large  
✅ **زمان آموزش کمتر**: 2-4 ساعت  
✅ **کیفیت بالاتر**: عملکرد بهتر  

---

## 🔧 عیب‌یابی

### مشکلات رایج

#### 1. GPU در دسترس نیست
```python
# بررسی و فعال‌سازی GPU
if not torch.cuda.is_available():
    print("⚠️ GPU not available!")
    print("Runtime > Change runtime type > GPU")
```

#### 2. حافظه ناکافی
```python
# پاک کردن حافظه GPU
torch.cuda.empty_cache()

# کاهش batch size
# در colab_brain_trainer.py خط 45-50 را تغییر دهید
```

#### 3. قطع اتصال Colab
```python
# ذخیره checkpoint های منظم
# فایل‌های مدل در /content/models ذخیره می‌شوند
# قبل از قطع اتصال دانلود کنید
```

---

## 💡 نکات بهینه‌سازی

### 1. انتخاب GPU مناسب
- **T4**: مدل‌های متوسط، رایگان
- **V100**: مدل‌های سنگین، Pro
- **A100**: مدل‌های فوق سنگین، Pro+

### 2. مدیریت زمان
- **T4**: 12 ساعت رایگان
- **Pro**: 24 ساعت
- **Pro+**: بدون محدودیت

### 3. بهینه‌سازی حافظه
```python
# تنظیمات بهینه برای T4
batch_size = 32
max_length = 512
gradient_accumulation = 4
```

---

## 📞 پشتیبانی

### مشکل در آموزش؟
1. بررسی GPU و RAM
2. کاهش complexity مدل
3. استفاده از checkpoint

### مشکل در دانلود؟
1. بررسی فضای ذخیره‌سازی
2. استفاده از Google Drive
3. دانلود بخش‌های کوچک‌تر

### مشکل در وارد کردن؟
1. بررسی فرمت zip
2. بررسی package_info.json
3. بررسی مسیرهای فایل

---

## 🎉 نتیجه‌گیری

با استفاده از این راهنما می‌توانید:

✅ مدل‌های سنگین را در Colab آموزش دهید  
✅ از GPU قدرتمند استفاده کنید  
✅ بهترین مدل‌ها را دانلود کنید  
✅ آن‌ها را در پروژه محلی استفاده کنید  
✅ عملکرد سیستم را بهبود دهید  

**🚀 Pearl-3x7B با مدل‌های Colab قدرتمندتر از همیشه!**
