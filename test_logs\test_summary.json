{"timestamp": "2025-07-08T05:52:29.037834", "total_tests": 8, "successful_tests": 0, "failed_tests": 8, "success_rate": 0.0, "total_execution_time": 34.39923810958862, "individual_results": [{"command": "python test_final_integration.py", "timestamp": "2025-07-08T05:51:56.670097", "execution_time": 2.0295026302337646, "return_code": 1, "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"D:\\project\\test_final_integration.py\", line 655, in <module>\n    success = main()\n  File \"D:\\project\\test_final_integration.py\", line 631, in main\n    print(\"\\U0001f680 FINAL ADVANCED TRADING SYSTEM INTEGRATION TEST\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f680' in position 0: character maps to <undefined>\n", "success": false, "test_name": "Final Integration Test"}, {"command": "python utils/advanced_rl_agent.py", "timestamp": "2025-07-08T05:51:57.938504", "execution_time": 1.263535976409912, "return_code": 1, "stdout": "Advanced Reinforcement Learning Trading System\n============================================================\n", "stderr": "INFO:__main__:Q-Learning Agent initialized with state_size=7, action_size=3\nINFO:__main__:Advanced RL Agent initialized\nWARNING:__main__:Model loading not applicable for AdvancedRLAgent. Model file: rl_model.pkl\nWARNING:__main__:Could not load replay buffer: replay_buffer.pkl, error: No module named 'utils'\nINFO:__main__:Advanced RL Trading System initialized\nTraceback (most recent call last):\n  File \"D:\\project\\utils\\advanced_rl_agent.py\", line 732, in <module>\n    main() \n  File \"D:\\project\\utils\\advanced_rl_agent.py\", line 674, in main\n    print(\"\\U0001f4ca Sample Market Data:\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f4ca' in position 0: character maps to <undefined>\n", "success": false, "test_name": "Advanced RL Agent"}, {"command": "python utils/multi_step_prediction_fixed.py", "timestamp": "2025-07-08T05:52:02.495352", "execution_time": 4.554729461669922, "return_code": 1, "stdout": "Multi-Step Prediction System Test\n========================================\nSample data created:\n  Records: 500\n  Price range: 1.08517 - 1.11711\n\nTraining models...\n  1h: MSE=0.000010\n  4h: MSE=0.000016\n  12h: MSE=0.000021\n  24h: MSE=0.000042\n\nGenerating predictions...\n", "stderr": "INFO:__main__:Multi-Step Prediction System initialized\nD:\\project\\utils\\multi_step_prediction_fixed.py:420: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  dates = pd.date_range(start='2023-01-01', periods=500, freq='H')\nINFO:__main__:Multi-Step Predictor initialized for EURUSD\nD:\\project\\utils\\multi_step_prediction_fixed.py:73: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n  features = features.fillna(method='ffill').fillna(0)\nINFO:__main__:Model trained for 1h: MSE=0.000010, MAE=0.002336\nD:\\project\\utils\\multi_step_prediction_fixed.py:73: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n  features = features.fillna(method='ffill').fillna(0)\nINFO:__main__:Model trained for 4h: MSE=0.000016, MAE=0.003363\nD:\\project\\utils\\multi_step_prediction_fixed.py:73: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n  features = features.fillna(method='ffill').fillna(0)\nINFO:__main__:Model trained for 12h: MSE=0.000021, MAE=0.003317\nD:\\project\\utils\\multi_step_prediction_fixed.py:73: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n  features = features.fillna(method='ffill').fillna(0)\nINFO:__main__:Model trained for 24h: MSE=0.000042, MAE=0.005168\nTraceback (most recent call last):\n  File \"D:\\project\\utils\\multi_step_prediction_fixed.py\", line 474, in <module>\n    main() \n  File \"D:\\project\\utils\\multi_step_prediction_fixed.py\", line 457, in main\n    signals = prediction_system.generate_trading_signals(\"EURUSD\", sample_data, current_price)\n  File \"D:\\project\\utils\\multi_step_prediction_fixed.py\", line 364, in generate_trading_signals\n    prediction = self.get_prediction(symbol, data, current_price)\n  File \"D:\\project\\utils\\multi_step_prediction_fixed.py\", line 337, in get_prediction\n    prediction = self.predictors[symbol].predict(data, current_price)\nTypeError: predict() takes 2 positional arguments but 3 were given\n", "success": false, "test_name": "Multi-Step Prediction"}, {"command": "python utils/market_regime_detector.py", "timestamp": "2025-07-08T05:52:06.740733", "execution_time": 4.240382671356201, "return_code": 1, "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"D:\\project\\utils\\market_regime_detector.py\", line 630, in <module>\n    main() \n  File \"D:\\project\\utils\\market_regime_detector.py\", line 566, in main\n    print(\"\\U0001f3ad Market Regime Detection System Demo\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f3ad' in position 0: character maps to <undefined>\n", "success": false, "test_name": "Market Regime Detector"}, {"command": "python utils/federated_learning_system.py", "timestamp": "2025-07-08T05:52:18.956822", "execution_time": 12.21430516242981, "return_code": 1, "stdout": "Federated Learning System Test\n========================================\nCreated server and 4 clients\n\n--- Federated Learning Round 1 ---\n  Client 1 sent RL_Agent update (accuracy: 0.785)\n  Client 1 sent Prediction_Model update (accuracy: 0.748)\n  Client 2 sent RL_Agent update (accuracy: 0.860)\n  Client 2 sent Prediction_Model update (accuracy: 0.752)\n  Client 3 sent RL_Agent update (accuracy: 0.757)\n  Client 3 sent Prediction_Model update (accuracy: 0.749)\n  Client 4 sent RL_Agent update (accuracy: 0.856)\n  Client 4 sent Prediction_Model update (accuracy: 0.873)\n\n  Aggregating updates...\n    RL_Agent v1: 4 clients\n    Prediction_Model v1: 4 clients\n\n--- Federated Learning Round 2 ---\n  Client 1 sent RL_Agent update (accuracy: 0.894)\n  Client 1 sent Prediction_Model update (accuracy: 0.712)\n  Client 2 sent RL_Agent update (accuracy: 0.767)\n  Client 2 sent Prediction_Model update (accuracy: 0.773)\n  Client 3 sent RL_Agent update (accuracy: 0.798)\n  Client 3 sent Prediction_Model update (accuracy: 0.758)\n  Client 4 sent RL_Agent update (accuracy: 0.738)\n  Client 4 sent Prediction_Model update (accuracy: 0.860)\n\n  Aggregating updates...\n    RL_Agent v2: 4 clients\n    Prediction_Model v2: 4 clients\n\n--- Federated Learning Round 3 ---\n  Client 1 sent RL_Agent update (accuracy: 0.820)\n  Client 1 sent Prediction_Model update (accuracy: 0.846)\n  Client 2 sent RL_Agent update (accuracy: 0.721)\n  Client 2 sent Prediction_Model update (accuracy: 0.732)\n  Client 3 sent RL_Agent update (accuracy: 0.716)\n  Client 3 sent Prediction_Model update (accuracy: 0.800)\n  Client 4 sent RL_Agent update (accuracy: 0.832)\n  Client 4 sent Prediction_Model update (accuracy: 0.771)\n\n  Aggregating updates...\n    RL_Agent v3: 4 clients\n    Prediction_Model v3: 4 clients\n\n--- Final Status ---\nTotal rounds: 3\nActive models: ['RL_Agent', 'Prediction_Model']\nTotal federated models: 2\n\nRL_Agent Final Model:\n  Version: 3\n  Participating clients: 4\n  Sample weights: [('q_learning_rate', 0.009062341242226418), ('exploration_rate', 0.16447281615971238), ('discount_factor', 0.9517626106926134)]\n\nPrediction_Model Final Model:\n  Version: 3\n  Participating clients: 4\n  Sample weights: [('feature_weight_1', 0.6937616400366549), ('feature_weight_2', 0.589794845453546), ('bias_term', -0.022212998640454888)]\n", "stderr": "INFO:__main__:Federated Learning Server initialized\nINFO:__main__:Federated Learning Client client_1 initialized\nINFO:__main__:Federated Learning Client client_2 initialized\nINFO:__main__:Federated Learning Client client_3 initialized\nINFO:__main__:Federated Learning Client client_4 initialized\nINFO:__main__:Training local model RL_Agent for client client_1\nINFO:__main__:Received valid update from client client_1 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_1\nINFO:__main__:Received valid update from client client_1 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_2\nINFO:__main__:Received valid update from client client_2 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_2\nINFO:__main__:Received valid update from client client_2 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_3\nINFO:__main__:Received valid update from client client_3 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_3\nINFO:__main__:Received valid update from client client_3 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_4\nINFO:__main__:Received valid update from client client_4 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_4\nINFO:__main__:Received valid update from client client_4 for Prediction_Model\nINFO:__main__:Aggregated 4 updates for RL_Agent v1\nINFO:__main__:Applying global update for RL_Agent v1\nINFO:__main__:Applying global update for RL_Agent v1\nINFO:__main__:Applying global update for RL_Agent v1\nINFO:__main__:Applying global update for RL_Agent v1\nINFO:__main__:Aggregated 4 updates for Prediction_Model v1\nINFO:__main__:Applying global update for Prediction_Model v1\nINFO:__main__:Applying global update for Prediction_Model v1\nINFO:__main__:Applying global update for Prediction_Model v1\nINFO:__main__:Applying global update for Prediction_Model v1\nINFO:__main__:Training local model RL_Agent for client client_1\nINFO:__main__:Received valid update from client client_1 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_1\nINFO:__main__:Received valid update from client client_1 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_2\nINFO:__main__:Received valid update from client client_2 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_2\nINFO:__main__:Received valid update from client client_2 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_3\nINFO:__main__:Received valid update from client client_3 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_3\nINFO:__main__:Received valid update from client client_3 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_4\nINFO:__main__:Received valid update from client client_4 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_4\nINFO:__main__:Received valid update from client client_4 for Prediction_Model\nINFO:__main__:Aggregated 4 updates for RL_Agent v2\nINFO:__main__:Applying global update for RL_Agent v2\nINFO:__main__:Applying global update for RL_Agent v2\nINFO:__main__:Applying global update for RL_Agent v2\nINFO:__main__:Applying global update for RL_Agent v2\nINFO:__main__:Aggregated 4 updates for Prediction_Model v2\nINFO:__main__:Applying global update for Prediction_Model v2\nINFO:__main__:Applying global update for Prediction_Model v2\nINFO:__main__:Applying global update for Prediction_Model v2\nINFO:__main__:Applying global update for Prediction_Model v2\nINFO:__main__:Training local model RL_Agent for client client_1\nINFO:__main__:Received valid update from client client_1 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_1\nINFO:__main__:Received valid update from client client_1 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_2\nINFO:__main__:Received valid update from client client_2 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_2\nINFO:__main__:Received valid update from client client_2 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_3\nINFO:__main__:Received valid update from client client_3 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_3\nINFO:__main__:Received valid update from client client_3 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_4\nINFO:__main__:Received valid update from client client_4 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_4\nINFO:__main__:Received valid update from client client_4 for Prediction_Model\nINFO:__main__:Aggregated 4 updates for RL_Agent v3\nINFO:__main__:Applying global update for RL_Agent v3\nINFO:__main__:Applying global update for RL_Agent v3\nINFO:__main__:Applying global update for RL_Agent v3\nINFO:__main__:Applying global update for RL_Agent v3\nINFO:__main__:Aggregated 4 updates for Prediction_Model v3\nINFO:__main__:Applying global update for Prediction_Model v3\nINFO:__main__:Applying global update for Prediction_Model v3\nINFO:__main__:Applying global update for Prediction_Model v3\nINFO:__main__:Applying global update for Prediction_Model v3\nTraceback (most recent call last):\n  File \"D:\\project\\utils\\federated_learning_system.py\", line 523, in <module>\n    main() \n  File \"D:\\project\\utils\\federated_learning_system.py\", line 520, in main\n    print(f\"\\n\\u2705 Federated Learning System test completed!\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u2705' in position 2: character maps to <undefined>\n", "success": false, "test_name": "Federated Learning System"}, {"command": "python utils/anomaly_detection_system.py", "timestamp": "2025-07-08T05:52:22.364912", "execution_time": 3.391171455383301, "return_code": 1, "stdout": "Anomaly Detection & Adaptation System Test\n==================================================\nGenerating historical data...\nInitializing system...\n", "stderr": "INFO:__main__:Adaptation Engine initialized\nINFO:__main__:Anomaly Detection System initialized\nINFO:__main__:Anomaly detector fitted with 50 samples\nINFO:__main__:System initialized with 50 feature samples\nTraceback (most recent call last):\n  File \"D:\\project\\utils\\anomaly_detection_system.py\", line 858, in <module>\n    main() \n  File \"D:\\project\\utils\\anomaly_detection_system.py\", line 706, in main\n    print(\"\\u2705 System initialized successfully\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u2705' in position 0: character maps to <undefined>\n", "success": false, "test_name": "Anomaly Detection System"}, {"command": "python utils/genetic_strategy_evolution.py", "timestamp": "2025-07-08T05:52:23.948530", "execution_time": 1.5786266326904297, "return_code": 1, "stdout": "Genetic Strategy Evolution System Test\n==================================================\nGenerating historical market data...\nStarting evolution with 20 strategies for 10 generations...\n\n--- Evolution Results ---\nBest Strategy: strategy_338773\n  Fitness Score: 0.0000\n  Generation: 0\n  Performance Metrics:\n    total_return: 0.0000\n    win_rate: 0.0000\n    profit_factor: 0.0000\n    max_drawdown: 0.0000\n    sharpe_ratio: 0.0000\n    total_trades: 8.0000\n  Key Parameters:\n    sma_period_short: 6.9368\n    sma_period_long: 169.7497\n    rsi_period: 24.4152\n    stop_loss: 0.0270\n    take_profit: 0.0652\n  Rules: 5\n    Rule 1: trend_following (weight: 0.81)\n    Rule 2: risk_management (weight: 1.15)\n    Rule 3: breakout (weight: 1.55)\n\n--- Evolution Summary ---\nTotal Generations: 10\nBest Fitness: 0.0000\nFinal Fitness: 0.0000\nImprovement: 0.0000\n\nFitness History:\n  Generation 1: 0.0000\n  Generation 2: 0.0000\n  Generation 3: 0.0000\n  Generation 4: 0.0000\n  Generation 5: 0.0000\n  Generation 6: 0.0000\n  Generation 7: 0.0000\n  Generation 8: 0.0000\n  Generation 9: 0.0000\n  Generation 10: 0.0000\n", "stderr": "INFO:__main__:Genetic Strategy Evolution System initialized\nINFO:__main__:Loaded 1000 historical data points\nINFO:__main__:Starting genetic evolution...\nINFO:__main__:Initialized population with 20 strategies\nINFO:__main__:Generation 1/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 2/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 3/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 4/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 5/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 6/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 7/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 8/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 9/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Generation 10/10\nINFO:__main__:  Best fitness: 0.0000, Average: 0.0000\nINFO:__main__:Evolution completed in 0.32 seconds\nTraceback (most recent call last):\n  File \"D:\\project\\utils\\genetic_strategy_evolution.py\", line 826, in <module>\n    main() \n  File \"D:\\project\\utils\\genetic_strategy_evolution.py\", line 823, in main\n    print(f\"\\n\\u2705 Genetic Strategy Evolution System test completed!\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u2705' in position 2: character maps to <undefined>\n", "success": false, "test_name": "Genetic Strategy Evolution"}, {"command": "python utils/intelligent_memory_system.py", "timestamp": "2025-07-08T05:52:29.035829", "execution_time": 5.0837836265563965, "return_code": 1, "stdout": "Multi-Level Intelligent Memory System Test\n==================================================\nTesting memory storage...\n  Stored 20 memories\n\nTesting memory retrieval...\n  Retrieved memory: memory_490981\n  Content: {'action': 'trade', 'symbol': 'EURUSD', 'price': 1.1001, 'volume': 1010, 'result': 'loss', 'amount': 20}\n  Importance: 0.6\n\nTesting memory search...\n  Found 10 memories matching criteria\n    memory_491982: importance=0.90\n    memory_491982: importance=0.90\n    memory_491982: importance=0.90\n\nTesting pattern detection...\n  Detected 0 patterns:\n\n--- System Statistics ---\nShort-term memory:\n  Total memories: 30\n  Utilization: 0.30\n  Average importance: 0.70\nSystem:\n  Total patterns: 0\n  Last consolidation: 2025-07-08T05:52:26.489982\n\nTesting advanced search...\n  Advanced search found 10 results\n", "stderr": "INFO:__main__:Intelligent Memory System initialized\nINFO:__main__:Stored memory memory_490981 in short-term memory\nINFO:__main__:Stored memory memory_490981 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_493499 in short-term memory\nTraceback (most recent call last):\n  File \"D:\\project\\utils\\intelligent_memory_system.py\", line 970, in <module>\n    main() \n  File \"D:\\project\\utils\\intelligent_memory_system.py\", line 967, in main\n    print(f\"\\n\\u2705 Multi-Level Intelligent Memory System test completed!\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u2705' in position 2: character maps to <undefined>\n", "success": false, "test_name": "Intelligent Memory System"}]}