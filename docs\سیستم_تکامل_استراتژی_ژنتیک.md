# مستندات سیستم تکامل استراتژی ژنتیک (GeneticStrategyEvolution)

## 📋 معرفی کلی

سیستم **GeneticStrategyEvolution** یکی از ماژول‌های پیشرفته پروژه است که استراتژی‌های معاملاتی را با استفاده از الگوریتم ژنتیک تکامل می‌دهد. این سیستم از اصول انتخاب طبیعی، جهش، و تلاقی برای بهبود عملکرد استراتژی‌ها استفاده می‌کند.

## 🎯 مسئولیت‌های اصلی

### 1. تولید استراتژی‌ها
- ایجاد استراتژی‌های تصادفی اولیه
- تعریف پارامترهای استراتژی
- تولید قوانین معاملاتی

### 2. ارزیابی عملکرد
- شبیه‌سازی معاملات
- محاسبه متریک‌های عملکرد
- تعیین امتیاز fitness

### 3. تکامل ژنتیک
- انتخاب والدین
- تلاقی استراتژی‌ها
- جهش پارامترها

## 🔧 کلاس‌های اصلی

### 1. TradingStrategy
```python
@dataclass
class TradingStrategy:
    strategy_id: str
    name: str
    parameters: Dict[str, float]
    rules: List[Dict[str, Any]]
    fitness_score: float
    generation: int
    parent_ids: List[str]
    performance_metrics: Dict[str, float]
    created_at: datetime
```

### 2. EvolutionConfig
```python
@dataclass
class EvolutionConfig:
    population_size: int = 50
    generations: int = 100
    mutation_rate: float = 0.1
    crossover_rate: float = 0.8
    elite_size: int = 5
    tournament_size: int = 3
    fitness_weights: Dict[str, float] = None
```

### 3. StrategyGenePool
```python
class StrategyGenePool:
    def __init__(self):
        self.parameter_ranges = {
            'sma_period_short': (5, 50),
            'sma_period_long': (20, 200),
            'rsi_period': (10, 30),
            'stop_loss': (0.005, 0.05),
            'take_profit': (0.01, 0.1)
        }
```

### 4. StrategyEvaluator
```python
class StrategyEvaluator:
    def __init__(self, historical_data: List[Dict]):
        self.historical_data = historical_data
        self.evaluation_period = 252  # یک سال
    
    def evaluate_strategy(self, strategy: TradingStrategy) -> float:
        """ارزیابی استراتژی"""
```

### 5. GeneticAlgorithm
```python
class GeneticAlgorithm:
    def __init__(self, config: EvolutionConfig, evaluator: StrategyEvaluator):
        self.config = config
        self.evaluator = evaluator
        self.gene_pool = StrategyGenePool()
```

## 📊 متدهای اصلی

### 1. generate_random_strategy()
```python
def generate_random_strategy(self, generation: int = 0) -> TradingStrategy:
    """
    تولید استراتژی تصادفی
    
    پارامترها:
    - generation: نسل استراتژی
    
    خروجی:
    - TradingStrategy: استراتژی جدید
    """
```

### 2. evaluate_strategy()
```python
def evaluate_strategy(self, strategy: TradingStrategy) -> float:
    """
    ارزیابی استراتژی
    
    پارامترها:
    - strategy: استراتژی برای ارزیابی
    
    خروجی:
    - float: امتیاز fitness
    """
```

### 3. evolve()
```python
def evolve(self) -> List[TradingStrategy]:
    """
    تکامل جمعیت
    
    خروجی:
    - List[TradingStrategy]: بهترین استراتژی‌ها
    """
```

### 4. _crossover()
```python
def _crossover(self, parent1: TradingStrategy, parent2: TradingStrategy) -> TradingStrategy:
    """
    تلاقی دو استراتژی
    
    پارامترها:
    - parent1, parent2: استراتژی‌های والد
    
    خروجی:
    - TradingStrategy: استراتژی فرزند
    """
```

## 🎨 پارامترهای استراتژی

### 1. پارامترهای تکنیکال
```python
parameter_ranges = {
    'sma_period_short': (5, 50),
    'sma_period_long': (20, 200),
    'rsi_period': (10, 30),
    'rsi_overbought': (70, 90),
    'rsi_oversold': (10, 30),
    'bollinger_period': (10, 30),
    'bollinger_std': (1.5, 2.5),
    'macd_fast': (8, 15),
    'macd_slow': (20, 30),
    'macd_signal': (5, 15)
}
```

### 2. پارامترهای ریسک
```python
risk_parameters = {
    'stop_loss': (0.005, 0.05),
    'take_profit': (0.01, 0.1),
    'position_size': (0.01, 0.1),
    'max_positions': (1, 10),
    'risk_per_trade': (0.01, 0.05)
}
```

### 3. پارامترهای زمانی
```python
time_parameters = {
    'entry_timeout': (1, 24),
    'exit_timeout': (1, 48),
    'trade_frequency': (0.1, 2.0)
}
```

## 🔧 قوانین استراتژی

### 1. قانون Trend Following
```python
{
    'type': 'trend_following',
    'conditions': ['price_above_sma', 'rsi_not_overbought'],
    'action': 'buy',
    'weight': 1.0
}
```

### 2. قانون Mean Reversion
```python
{
    'type': 'mean_reversion',
    'conditions': ['price_below_bollinger_lower', 'rsi_oversold'],
    'action': 'buy',
    'weight': 1.0
}
```

### 3. قانون Momentum
```python
{
    'type': 'momentum',
    'conditions': ['macd_bullish_crossover', 'volume_above_average'],
    'action': 'buy',
    'weight': 1.0
}
```

## 📈 نمونه کد استفاده

```python
from utils.genetic_strategy_evolution import GeneticStrategyEvolution, EvolutionConfig

# ایجاد سیستم تکامل
evolution = GeneticStrategyEvolution("genetic_evolution.db")

# بارگذاری داده‌های تاریخی
historical_data = [
    {'price': 100, 'volume': 1000, 'timestamp': '2023-01-01'},
    {'price': 101, 'volume': 1100, 'timestamp': '2023-01-02'},
    # ... سایر داده‌ها
]
evolution.load_historical_data(historical_data)

# تنظیمات تکامل
config = EvolutionConfig(
    population_size=30,
    generations=50,
    mutation_rate=0.15,
    crossover_rate=0.8,
    elite_size=3
)

# اجرای تکامل
print("شروع تکامل استراتژی‌ها...")
best_strategies = evolution.run_evolution(config)

# نمایش نتایج
print(f"بهترین استراتژی: {best_strategies[0].name}")
print(f"امتیاز fitness: {best_strategies[0].fitness_score:.4f}")
print(f"بازده کل: {best_strategies[0].performance_metrics.get('total_return', 0):.2f}")
print(f"نرخ برد: {best_strategies[0].performance_metrics.get('win_rate', 0):.2f}")

# دریافت خلاصه تکامل
summary = evolution.get_evolution_summary()
print("خلاصه تکامل:", summary)
```

## 🔧 تنظیمات و پیکربندی

### پارامترهای تکامل:
- `population_size`: اندازه جمعیت (50)
- `generations`: تعداد نسل‌ها (100)
- `mutation_rate`: نرخ جهش (0.1)
- `crossover_rate`: نرخ تلاقی (0.8)
- `elite_size`: تعداد نخبگان (5)
- `tournament_size`: اندازه تورنمنت (3)

### وزن‌های Fitness:
```python
fitness_weights = {
    'total_return': 0.3,
    'win_rate': 0.2,
    'profit_factor': 0.2,
    'sharpe_ratio': 0.2,
    'max_drawdown': -0.1  # منفی چون کمتر بهتر است
}
```

## 🚨 مدیریت خطا

```python
try:
    best_strategies = evolution.run_evolution(config)
    if not best_strategies:
        print("هیچ استراتژی معتبری تولید نشد")
    else:
        print(f"تعداد استراتژی‌های تولید شده: {len(best_strategies)}")
except Exception as e:
    print(f"خطا در تکامل: {e}")
```

## 📊 متریک‌های عملکرد

### 1. متریک‌های مالی
```python
performance_metrics = {
    'total_return': 0.15,      # بازده کل
    'win_rate': 0.65,          # نرخ برد
    'profit_factor': 1.8,      # نسبت سود به زیان
    'max_drawdown': 0.12,      # حداکثر افت
    'sharpe_ratio': 1.2,       # نسبت شارپ
    'total_trades': 45         # تعداد معاملات
}
```

### 2. محاسبه Fitness
```python
def _calculate_fitness_score(self, performance_metrics: Dict[str, float]) -> float:
    fitness = 0.0
    for metric, weight in weights.items():
        value = performance_metrics.get(metric, 0.0)
        normalized_value = self._normalize_metric(metric, value)
        fitness += weight * normalized_value
    return max(0.0, fitness)
```

## 🔗 اتصال به سیستم اصلی

### فایل‌های مرتبط:
- `utils/genetic_strategy_evolution.py`: پیاده‌سازی اصلی
- `tests/test_complete_advanced_system.py`: تست‌های پیشرفته
- `test_genetic_algorithm_deep.py`: تست عمیق
- `debug_fitness_issue.py`: رفع اشکال

### وضعیت عملیاتی:
⚠️ **نیمه‌فعال** - این ماژول در تست‌های پیشرفته استفاده می‌شود اما هنوز به طور کامل در سیستم اصلی ادغام نشده

## 🎯 بهترین شیوه‌های استفاده

### 1. تنظیم پارامترهای تکامل
```python
# برای تکامل سریع
config = EvolutionConfig(population_size=20, generations=30)

# برای تکامل دقیق
config = EvolutionConfig(population_size=100, generations=200)
```

### 2. مدیریت داده‌های تاریخی
```python
# حداقل یک سال داده
if len(historical_data) >= 252:
    evolution.load_historical_data(historical_data)
else:
    print("داده‌های تاریخی کافی نیست")
```

### 3. انتخاب بهترین استراتژی
```python
# فیلتر کردن استراتژی‌های معتبر
valid_strategies = [s for s in best_strategies if s.fitness_score > 0.5]
if valid_strategies:
    best_strategy = valid_strategies[0]
```

## 🔮 نمودار معماری

```mermaid
graph TD
    A[GeneticStrategyEvolution] --> B[StrategyGenePool]
    A --> C[StrategyEvaluator]
    A --> D[GeneticAlgorithm]
    
    B --> E[Parameter Ranges]
    B --> F[Rule Templates]
    B --> G[Random Strategy Generation]
    
    C --> H[Trade Simulation]
    C --> I[Performance Metrics]
    C --> J[Fitness Calculation]
    
    D --> K[Population Management]
    D --> L[Selection]
    D --> M[Crossover]
    D --> N[Mutation]
    
    H --> O[Buy Signals]
    H --> P[Sell Signals]
    H --> Q[Position Management]
    
    I --> R[Total Return]
    I --> S[Win Rate]
    I --> T[Profit Factor]
    I --> U[Sharpe Ratio]
    
    K --> V[Elite Selection]
    K --> W[Tournament Selection]
    K --> X[New Generation]
```

## 📚 الگوریتم ژنتیک

### 1. مراحل تکامل
1. **راه‌اندازی**: تولید جمعیت اولیه
2. **ارزیابی**: محاسبه fitness هر استراتژی
3. **انتخاب**: انتخاب والدین برای تولیدمثل
4. **تلاقی**: ترکیب استراتژی‌های والد
5. **جهش**: تغییر تصادفی پارامترها
6. **جایگزینی**: ایجاد نسل جدید

### 2. انتخاب والدین
```python
def _tournament_selection(self) -> TradingStrategy:
    """انتخاب والد با روش تورنمنت"""
    tournament = random.sample(self.population, self.config.tournament_size)
    return max(tournament, key=lambda x: x.fitness_score)
```

### 3. تلاقی استراتژی‌ها
```python
def _crossover(self, parent1: TradingStrategy, parent2: TradingStrategy) -> TradingStrategy:
    """تلاقی دو استراتژی"""
    child_parameters = {}
    for param in parent1.parameters:
        if random.random() < 0.5:
            child_parameters[param] = parent1.parameters[param]
        else:
            child_parameters[param] = parent2.parameters[param]
```

### 4. جهش پارامترها
```python
def _mutate(self, strategy: TradingStrategy) -> TradingStrategy:
    """جهش استراتژی"""
    for param_name, param_value in strategy.parameters.items():
        if random.random() < self.config.mutation_rate:
            # اعمال جهش
            mutation_strength = 0.1
            strategy.parameters[param_name] *= (1 + random.uniform(-mutation_strength, mutation_strength))
```

## 🏆 مزایا و کاربردها

### مزایا:
1. **خودکار**: عدم نیاز به تنظیم دستی پارامترها
2. **تطبیق‌پذیر**: سازگاری با شرایط مختلف بازار
3. **متنوع**: تولید استراتژی‌های مختلف
4. **بهینه‌سازی**: بهبود مداوم عملکرد

### کاربردها:
1. **بهینه‌سازی پارامترها**: تنظیم خودکار
2. **کشف استراتژی**: یافتن الگوهای جدید
3. **تست استراتژی**: ارزیابی عملکرد
4. **تطبیق بازار**: سازگاری با تغییرات

---

**نکته**: این سیستم برای تکامل و بهینه‌سازی خودکار استراتژی‌های معاملاتی طراحی شده و می‌تواند استراتژی‌های بهتری نسبت به روش‌های سنتی تولید کند. 