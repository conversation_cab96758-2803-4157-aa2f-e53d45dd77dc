"""
🧪 Performance Tests
تست‌های عملکرد

این فایل شامل تست‌های عملکرد برای تمام بخش‌های سیستم است
"""

import pytest
import asyncio
import time
import threading
import multiprocessing
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

# Import modules for testing
try:
    from core.utils import (
        PerformanceMonitor, MemoryManager, CacheManager,
        performance_monitor, memory_manager, cache_manager
    )
    from ai_models import initialize_models, get_available_models
    from utils import initialize_utils
    from env import TradingEnvironmentV2, env_factory
    from portfolio import PortfolioManagerV2, portfolio_factory
    from evaluation import evaluation_engine
    from optimization import optimization_engine
    from api import api_manager
    from main_new import system_manager
    from execution_pipeline import execution_pipeline
    
    MODULES_AVAILABLE = True
    
except ImportError as e:
    print(f"⚠️ Some modules not available: {e}")
    MODULES_AVAILABLE = False

pytestmark = pytest.mark.skipif(not MODULES_AVAILABLE, reason="Required modules not available")

@pytest.mark.performance
class TestSystemPerformance:
    """تست عملکرد کل سیستم"""
    
    def test_system_startup_time(self, performance_tracker):
        """تست زمان راه‌اندازی سیستم"""
        performance_tracker.start()
        
        # Test system initialization
        system_manager.initialize()
        
        performance_tracker.stop()
        
        startup_time = performance_tracker.get_duration()
        
        # System should start within reasonable time
        assert startup_time < 30.0  # 30 seconds max
        print(f"System startup time: {startup_time:.2f}s")
    
    def test_system_memory_usage(self, performance_tracker):
        """تست استفاده از حافظه سیستم"""
        performance_tracker.start()
        
        # Initialize system
        system_manager.initialize()
        
        # Get memory usage
        memory_info = memory_manager.get_memory_info()
        
        performance_tracker.stop()
        
        # Check memory usage is reasonable
        used_mb = memory_info.get('used_mb', 0)
        assert used_mb < 2048  # Less than 2GB
        print(f"System memory usage: {used_mb:.1f}MB")
    
    def test_concurrent_requests(self):
        """تست درخواست‌های همزمان"""
        def make_request():
            # Simulate a system request
            status = system_manager.health_check()
            return status is not None
        
        # Test with multiple concurrent requests
        with ThreadPoolExecutor(max_workers=10) as executor:
            start_time = time.time()
            
            futures = [executor.submit(make_request) for _ in range(50)]
            results = [future.result() for future in futures]
            
            end_time = time.time()
        
        # All requests should succeed
        assert all(results)
        
        # Should handle concurrent requests efficiently
        total_time = end_time - start_time
        assert total_time < 5.0  # Should complete within 5 seconds
        print(f"50 concurrent requests completed in {total_time:.2f}s")

@pytest.mark.performance
class TestAIModelPerformance:
    """تست عملکرد مدل‌های AI"""
    
    @pytest.mark.ai
    def test_model_loading_performance(self, performance_tracker):
        """تست عملکرد بارگذاری مدل"""
        performance_tracker.start()
        
        # Mock model loading to avoid actual download
        with patch('ai_models.model_manager.ModelManager._load_huggingface_model') as mock_load:
            mock_model = Mock()
            mock_model.name = "test_model"
            mock_load.return_value = mock_model
            
            # Test model initialization
            model_manager = initialize_models()
        
        performance_tracker.stop()
        
        loading_time = performance_tracker.get_duration()
        assert loading_time < 10.0  # Should load within 10 seconds
        print(f"Model loading time: {loading_time:.2f}s")
    
    @pytest.mark.ai
    def test_prediction_throughput(self):
        """تست throughput پیش‌بینی"""
        # Create mock model
        mock_model = Mock()
        mock_model.predict.return_value = {
            "prediction": "positive",
            "confidence": 0.8
        }
        
        # Test prediction speed
        texts = ["Test text"] * 100
        
        start_time = time.time()
        
        for text in texts:
            mock_model.predict(text)
        
        end_time = time.time()
        
        total_time = end_time - start_time
        throughput = len(texts) / total_time
        
        # Should achieve reasonable throughput
        assert throughput > 50  # At least 50 predictions per second
        print(f"Prediction throughput: {throughput:.1f} predictions/second")
    
    @pytest.mark.ai
    def test_batch_prediction_performance(self):
        """تست عملکرد پیش‌بینی دسته‌ای"""
        mock_model = Mock()
        mock_model.batch_predict.return_value = [
            {"prediction": "positive", "confidence": 0.8}
            for _ in range(100)
        ]
        
        texts = ["Test text"] * 100
        
        start_time = time.time()
        results = mock_model.batch_predict(texts)
        end_time = time.time()
        
        batch_time = end_time - start_time
        
        # Batch processing should be faster than individual predictions
        assert batch_time < 2.0  # Should complete within 2 seconds
        assert len(results) == 100
        print(f"Batch prediction time: {batch_time:.2f}s for 100 items")

@pytest.mark.performance
class TestTradingEnvironmentPerformance:
    """تست عملکرد محیط معاملاتی"""
    
    def test_environment_creation_speed(self, performance_tracker):
        """تست سرعت ایجاد محیط"""
        performance_tracker.start()
        
        # Create multiple environments
        environments = []
        for symbol in ["EURUSD", "GBPUSD", "USDJPY", "EURJPY", "GBPJPY"]:
            env = env_factory.create_trading_env(symbol, "H1", "v2")
            if env:
                env.initialize()
                environments.append(env)
        
        performance_tracker.stop()
        
        creation_time = performance_tracker.get_duration()
        
        # Should create environments quickly
        assert creation_time < 5.0  # Within 5 seconds
        assert len(environments) > 0
        print(f"Created {len(environments)} environments in {creation_time:.2f}s")
    
    def test_trading_simulation_speed(self):
        """تست سرعت شبیه‌سازی معاملات"""
        env = TradingEnvironmentV2("EURUSD", "H1")
        env.initialize()
        
        start_time = time.time()
        
        # Simulate trading steps
        for i in range(1000):
            action = i % 3  # 0=hold, 1=buy, 2=sell
            state, reward, done, info = env.step(action)
            
            if done:
                env.reset()
        
        end_time = time.time()
        
        simulation_time = end_time - start_time
        steps_per_second = 1000 / simulation_time
        
        # Should handle many steps per second
        assert steps_per_second > 100  # At least 100 steps/second
        print(f"Trading simulation: {steps_per_second:.0f} steps/second")
    
    def test_market_data_processing_speed(self):
        """تست سرعت پردازش داده‌های بازار"""
        import pandas as pd
        import numpy as np
        
        # Generate large market data
        n_points = 10000
        data = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=n_points, freq='T'),
            'open': np.random.uniform(1.09, 1.11, n_points),
            'high': np.random.uniform(1.10, 1.12, n_points),
            'low': np.random.uniform(1.08, 1.10, n_points),
            'close': np.random.uniform(1.09, 1.11, n_points),
            'volume': np.random.randint(100, 1000, n_points)
        })
        
        start_time = time.time()
        
        # Process market data
        processed_data = []
        for _, row in data.iterrows():
            # Simulate data processing
            processed_row = {
                'price_change': row['close'] - row['open'],
                'volatility': (row['high'] - row['low']) / row['open'],
                'volume_normalized': row['volume'] / 1000
            }
            processed_data.append(processed_row)
        
        end_time = time.time()
        
        processing_time = end_time - start_time
        points_per_second = n_points / processing_time
        
        # Should process data quickly
        assert points_per_second > 1000  # At least 1000 points/second
        print(f"Market data processing: {points_per_second:.0f} points/second")

@pytest.mark.performance
class TestPortfolioPerformance:
    """تست عملکرد پورتفولیو"""
    
    def test_portfolio_operations_speed(self):
        """تست سرعت عملیات پورتفولیو"""
        portfolio = PortfolioManagerV2(initial_balance=10000.0)
        portfolio.initialize()
        
        start_time = time.time()
        
        # Perform many portfolio operations
        for i in range(1000):
            symbol = f"SYMBOL{i % 10}"
            price = 1.0 + (i % 100) / 10000
            
            # Update price
            portfolio.update_price(symbol, price)
            
            # Occasionally open/close positions
            if i % 10 == 0:
                if portfolio.can_open_position(symbol, 0.01):
                    portfolio.open_position(symbol, 0.01, "long", price)
            
            if i % 20 == 0 and symbol in portfolio.positions:
                portfolio.close_position(symbol, price)
        
        end_time = time.time()
        
        operations_time = end_time - start_time
        operations_per_second = 1000 / operations_time
        
        # Should handle operations quickly
        assert operations_per_second > 500  # At least 500 operations/second
        print(f"Portfolio operations: {operations_per_second:.0f} ops/second")
    
    def test_portfolio_calculation_performance(self):
        """تست عملکرد محاسبات پورتفولیو"""
        portfolio = PortfolioManagerV2(initial_balance=10000.0)
        portfolio.initialize()
        
        # Add many positions
        for i in range(100):
            symbol = f"SYMBOL{i}"
            portfolio.positions[symbol] = Mock()
            portfolio.positions[symbol].unrealized_pnl = i * 10
        
        start_time = time.time()
        
        # Perform portfolio calculations
        for _ in range(1000):
            portfolio._update_equity()
            portfolio._update_margins()
            performance = portfolio.get_performance_summary()
        
        end_time = time.time()
        
        calculation_time = end_time - start_time
        calculations_per_second = 1000 / calculation_time
        
        # Should calculate quickly
        assert calculations_per_second > 100  # At least 100 calculations/second
        print(f"Portfolio calculations: {calculations_per_second:.0f} calcs/second")

@pytest.mark.performance
class TestOptimizationPerformance:
    """تست عملکرد بهینه‌سازی"""
    
    def test_optimization_algorithm_speed(self):
        """تست سرعت الگوریتم بهینه‌سازی"""
        optimization_engine.initialize()
        
        # Simple objective function
        def objective(params):
            return -(params.get('x', 0) - 5) ** 2
        
        search_space = {
            'x': {'type': 'float', 'min': 0, 'max': 10}
        }
        
        start_time = time.time()
        
        # Run optimization
        result = optimization_engine.optimize(
            objective,
            search_space,
            "random_search"
        )
        
        end_time = time.time()
        
        optimization_time = end_time - start_time
        
        # Should optimize within reasonable time
        assert optimization_time < 10.0  # Within 10 seconds
        assert result.best_score > -1  # Should find good solution
        print(f"Optimization completed in {optimization_time:.2f}s")
    
    def test_parallel_optimization(self):
        """تست بهینه‌سازی موازی"""
        def objective(params):
            # Simulate computation time
            time.sleep(0.01)
            return -(params.get('x', 0) - 5) ** 2
        
        search_space = {
            'x': {'type': 'float', 'min': 0, 'max': 10}
        }
        
        # Sequential optimization
        start_time = time.time()
        for _ in range(10):
            objective({'x': 5})
        sequential_time = time.time() - start_time
        
        # Parallel optimization
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(objective, {'x': 5}) for _ in range(10)]
            results = [future.result() for future in futures]
        parallel_time = time.time() - start_time
        
        # Parallel should be faster
        assert parallel_time < sequential_time
        print(f"Sequential: {sequential_time:.2f}s, Parallel: {parallel_time:.2f}s")

@pytest.mark.performance
class TestAPIPerformance:
    """تست عملکرد API"""
    
    def test_api_response_time(self):
        """تست زمان پاسخ API"""
        api_manager.initialize()
        
        # Mock API responses
        async def mock_endpoint():
            await asyncio.sleep(0.01)  # Simulate processing
            return {"status": "ok"}
        
        start_time = time.time()
        
        # Simulate multiple API calls
        async def make_requests():
            tasks = [mock_endpoint() for _ in range(100)]
            return await asyncio.gather(*tasks)
        
        # Run async requests
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results = loop.run_until_complete(make_requests())
        loop.close()
        
        end_time = time.time()
        
        total_time = end_time - start_time
        requests_per_second = 100 / total_time
        
        # Should handle many requests per second
        assert requests_per_second > 50  # At least 50 requests/second
        assert len(results) == 100
        print(f"API throughput: {requests_per_second:.0f} requests/second")
    
    def test_websocket_message_throughput(self):
        """تست throughput پیام‌های WebSocket"""
        # Mock WebSocket connections
        connections = [Mock() for _ in range(10)]
        
        start_time = time.time()
        
        # Send messages to all connections
        message = {"type": "price_update", "data": {"EURUSD": 1.1000}}
        
        for _ in range(1000):
            for connection in connections:
                connection.send_text.return_value = None
                connection.send_text('{"message": "test"}')
        
        end_time = time.time()
        
        total_time = end_time - start_time
        messages_per_second = (1000 * 10) / total_time
        
        # Should handle high message throughput
        assert messages_per_second > 1000  # At least 1000 messages/second
        print(f"WebSocket throughput: {messages_per_second:.0f} messages/second")

@pytest.mark.performance
class TestMemoryPerformance:
    """تست عملکرد حافظه"""
    
    def test_memory_leak_detection(self):
        """تست تشخیص نشت حافظه"""
        initial_memory = memory_manager.get_memory_info()['used_mb']
        
        # Perform operations that might cause memory leaks
        for i in range(100):
            # Create and destroy objects
            data = [j for j in range(1000)]
            processed_data = [x * 2 for x in data]
            del data, processed_data
            
            # Trigger garbage collection periodically
            if i % 10 == 0:
                import gc
                gc.collect()
        
        final_memory = memory_manager.get_memory_info()['used_mb']
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be minimal
        assert memory_increase < 50  # Less than 50MB increase
        print(f"Memory increase: {memory_increase:.1f}MB")
    
    def test_cache_performance(self):
        """تست عملکرد کش"""
        cache = CacheManager(max_size=1000)
        
        # Test cache write performance
        start_time = time.time()
        
        for i in range(10000):
            cache.set(f"key_{i}", f"value_{i}")
        
        write_time = time.time() - start_time
        
        # Test cache read performance
        start_time = time.time()
        
        for i in range(10000):
            cache.get(f"key_{i}")
        
        read_time = time.time() - start_time
        
        write_ops_per_second = 10000 / write_time
        read_ops_per_second = 10000 / read_time
        
        # Cache should be fast
        assert write_ops_per_second > 1000  # At least 1000 writes/second
        assert read_ops_per_second > 5000   # At least 5000 reads/second
        
        print(f"Cache write: {write_ops_per_second:.0f} ops/s")
        print(f"Cache read: {read_ops_per_second:.0f} ops/s")

@pytest.mark.performance
class TestConcurrencyPerformance:
    """تست عملکرد همزمانی"""
    
    def test_thread_safety(self):
        """تست thread safety"""
        shared_data = {"counter": 0}
        lock = threading.Lock()
        
        def increment_counter():
            for _ in range(1000):
                with lock:
                    shared_data["counter"] += 1
        
        # Create multiple threads
        threads = [threading.Thread(target=increment_counter) for _ in range(10)]
        
        start_time = time.time()
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        
        # Check that all increments were recorded
        assert shared_data["counter"] == 10000  # 10 threads * 1000 increments
        
        total_time = end_time - start_time
        print(f"Thread safety test completed in {total_time:.2f}s")
    
    def test_async_performance(self):
        """تست عملکرد async"""
        async def async_task(task_id):
            # Simulate async work
            await asyncio.sleep(0.01)
            return f"Task {task_id} completed"
        
        async def run_concurrent_tasks():
            tasks = [async_task(i) for i in range(100)]
            return await asyncio.gather(*tasks)
        
        start_time = time.time()
        
        # Run async tasks
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results = loop.run_until_complete(run_concurrent_tasks())
        loop.close()
        
        end_time = time.time()
        
        total_time = end_time - start_time
        
        # Async should be much faster than sequential
        assert total_time < 2.0  # Should complete within 2 seconds
        assert len(results) == 100
        print(f"100 async tasks completed in {total_time:.2f}s")

@pytest.mark.performance
@pytest.mark.slow
class TestStressTest:
    """تست‌های استرس"""
    
    def test_high_load_stress(self):
        """تست استرس بار بالا"""
        start_time = time.time()
        
        # Simulate high load
        def cpu_intensive_task():
            total = 0
            for i in range(100000):
                total += i ** 2
            return total
        
        # Run multiple CPU-intensive tasks
        with ThreadPoolExecutor(max_workers=multiprocessing.cpu_count()) as executor:
            futures = [executor.submit(cpu_intensive_task) for _ in range(20)]
            results = [future.result() for future in futures]
        
        end_time = time.time()
        
        total_time = end_time - start_time
        
        # Should handle high load without crashing
        assert len(results) == 20
        assert all(result > 0 for result in results)
        print(f"High load stress test completed in {total_time:.2f}s")
    
    def test_memory_stress(self):
        """تست استرس حافظه"""
        initial_memory = memory_manager.get_memory_info()['used_mb']
        
        # Create large data structures
        large_data = []
        for i in range(100):
            data_chunk = [j for j in range(10000)]
            large_data.append(data_chunk)
        
        peak_memory = memory_manager.get_memory_info()['used_mb']
        memory_used = peak_memory - initial_memory
        
        # Clean up
        del large_data
        import gc
        gc.collect()
        
        final_memory = memory_manager.get_memory_info()['used_mb']
        memory_recovered = peak_memory - final_memory
        
        # Should handle large memory usage and recover
        assert memory_used < 1000  # Less than 1GB
        assert memory_recovered > memory_used * 0.8  # Recover at least 80%
        
        print(f"Memory stress: Used {memory_used:.1f}MB, Recovered {memory_recovered:.1f}MB")

@pytest.mark.performance
class TestPerformanceRegression:
    """تست رگرسیون عملکرد"""
    
    def test_performance_benchmarks(self):
        """تست benchmark های عملکرد"""
        benchmarks = {
            "system_startup": 30.0,      # seconds
            "model_loading": 10.0,       # seconds
            "prediction_throughput": 50, # predictions/second
            "api_response": 0.1,         # seconds
            "memory_usage": 2048         # MB
        }
        
        # Test system startup
        start_time = time.time()
        system_manager.initialize()
        startup_time = time.time() - start_time
        
        assert startup_time < benchmarks["system_startup"]
        
        # Test memory usage
        memory_info = memory_manager.get_memory_info()
        assert memory_info['used_mb'] < benchmarks["memory_usage"]
        
        print("All performance benchmarks passed!")
    
    def test_performance_monitoring(self):
        """تست مانیتورینگ عملکرد"""
        monitor = PerformanceMonitor()
        
        # Test performance measurement
        with monitor.measure("test_operation"):
            time.sleep(0.1)
        
        # Get performance stats
        stats = monitor.get_stats()
        
        assert "test_operation" in stats
        assert stats["test_operation"]["total_time"] >= 0.1
        assert stats["test_operation"]["call_count"] == 1
        
        print(f"Performance stats: {stats}") 