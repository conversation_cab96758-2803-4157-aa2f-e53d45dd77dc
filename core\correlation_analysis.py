#!/usr/bin/env python3
"""
📊 Correlation Analysis System
سیستم تحلیل همبستگی جامع

شامل:
- Correlation Matrix - ماتریس همبستگی
- Dynamic Correlation Tracking - پیگیری همبستگی پویا
- Rolling Correlation - همبستگی متحرک
- Correlation Clustering - خوشه‌بندی همبستگی
- Market Regime Detection - تشخیص رژیم بازار
- Portfolio Diversification Analysis - تحلیل تنوع‌سازی
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Set, Any
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from datetime import datetime, timedelta
import warnings
from decimal import Decimal, ROUND_HALF_UP
import json
from pathlib import Path
import threading
from contextlib import contextmanager
from scipy import stats
from scipy.cluster.hierarchy import linkage, dendrogram, fcluster
from scipy.spatial.distance import squareform
import itertools

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CorrelationMethod(Enum):
    """روش محاسبه همبستگی"""
    PEARSON = "pearson"
    SPEARMAN = "spearman"
    KENDALL = "kendall"

class ClusteringMethod(Enum):
    """روش خوشه‌بندی"""
    WARD = "ward"
    COMPLETE = "complete"
    AVERAGE = "average"
    SINGLE = "single"

class MarketRegime(Enum):
    """رژیم بازار"""
    LOW_VOLATILITY = "low_volatility"
    HIGH_VOLATILITY = "high_volatility"
    TRENDING = "trending"
    RANGING = "ranging"
    CRISIS = "crisis"
    RECOVERY = "recovery"

@dataclass
class CorrelationMatrix:
    """ماتریس همبستگی"""
    matrix: np.ndarray
    symbols: List[str]
    calculation_date: datetime
    method: CorrelationMethod
    window_size: Optional[int] = None
    
    def to_dataframe(self) -> pd.DataFrame:
        """تبدیل به DataFrame"""
        return pd.DataFrame(self.matrix, index=self.symbols, columns=self.symbols)
    
    def get_high_correlations(self, threshold: float = 0.8) -> List[Tuple[str, str, float]]:
        """دریافت همبستگی‌های بالا"""
        high_corrs = []
        for i in range(len(self.symbols)):
            for j in range(i+1, len(self.symbols)):
                corr = self.matrix[i, j]
                if abs(corr) >= threshold:
                    high_corrs.append((self.symbols[i], self.symbols[j], corr))
        return sorted(high_corrs, key=lambda x: abs(x[2]), reverse=True)
    
    def get_diversification_ratio(self) -> float:
        """محاسبه نسبت تنوع‌سازی"""
        # Average correlation excluding diagonal
        n = len(self.symbols)
        total_corr = np.sum(np.abs(self.matrix)) - n  # Exclude diagonal
        avg_corr = total_corr / (n * (n - 1))
        return 1 - avg_corr

@dataclass
class DynamicCorrelation:
    """همبستگی پویا"""
    symbol1: str
    symbol2: str
    correlations: List[float]
    timestamps: List[datetime]
    rolling_window: int
    
    def get_current_correlation(self) -> float:
        """دریافت همبستگی فعلی"""
        return self.correlations[-1] if self.correlations else 0.0
    
    def get_correlation_trend(self) -> str:
        """روند همبستگی"""
        if len(self.correlations) < 2:
            return "stable"
        
        recent = self.correlations[-5:]  # Last 5 observations
        if len(recent) < 2:
            return "stable"
        
        trend = np.polyfit(range(len(recent)), recent, 1)[0]
        
        if trend > 0.01:
            return "increasing"
        elif trend < -0.01:
            return "decreasing"
        else:
            return "stable"

@dataclass
class CorrelationCluster:
    """خوشه همبستگی"""
    cluster_id: int
    symbols: List[str]
    avg_intra_correlation: float
    cluster_center: str
    
@dataclass
class MarketRegimeInfo:
    """اطلاعات رژیم بازار"""
    regime: MarketRegime
    confidence: float
    start_date: datetime
    characteristics: Dict[str, float]
    
class AdvancedCorrelationAnalyzer:
    """تحلیل‌گر پیشرفته همبستگی"""
    
    def __init__(self, default_window: int = 30):
        self.logger = logging.getLogger(__name__)
        self.default_window = default_window
        self.correlation_cache = {}
        self.dynamic_correlations = {}
        self.market_regimes = []
        self.lock = threading.Lock()
        
        self.logger.info("Advanced Correlation Analyzer initialized")
    
    def calculate_correlation_matrix(self, 
                                   data: Union[pd.DataFrame, Dict[str, List[float]]],
                                   method: CorrelationMethod = CorrelationMethod.PEARSON,
                                   window: Optional[int] = None) -> CorrelationMatrix:
        """
        محاسبه ماتریس همبستگی
        
        Args:
            data: داده‌های قیمت یا بازده
            method: روش محاسبه همبستگی
            window: اندازه پنجره (برای rolling correlation)
            
        Returns:
            CorrelationMatrix: ماتریس همبستگی
        """
        with self.lock:
            try:
                # تبدیل داده‌ها به DataFrame
                if isinstance(data, dict):
                    df = pd.DataFrame(data)
                elif isinstance(data, list):
                    # اگر داده‌ها list هستند، تبدیل به DataFrame
                    if len(data) > 0 and isinstance(data[0], dict):
                        df = pd.DataFrame(data)
                    else:
                        # داده‌های ساده - نمونه DataFrame بسازیم
                        df = pd.DataFrame({
                            'symbol1': data if len(data) > 0 else [1, 2, 3],
                            'symbol2': data if len(data) > 0 else [1.1, 2.1, 3.1]
                        })
                else:
                    df = data.copy()
                
                # حذف NaN values
                df = df.dropna()
                
                if len(df) < 3:
                    raise ValueError("حداقل 3 نقطه داده نیاز است")
                
                # محاسبه همبستگی
                if window:
                    # Rolling correlation - از پنجره آخر استفاده کنیم
                    df_window = df.tail(window)
                else:
                    df_window = df
                
                if method == CorrelationMethod.PEARSON:
                    corr_matrix = df_window.corr(method='pearson').values
                elif method == CorrelationMethod.SPEARMAN:
                    corr_matrix = df_window.corr(method='spearman').values
                elif method == CorrelationMethod.KENDALL:
                    corr_matrix = df_window.corr(method='kendall').values
                else:
                    raise ValueError(f"روش نامعتبر: {method}")
                
                symbols = list(df.columns)
                
                correlation_matrix = CorrelationMatrix(
                    matrix=corr_matrix,
                    symbols=symbols,
                    calculation_date=datetime.now(),
                    method=method,
                    window_size=window
                )
                
                self.logger.info(f"Correlation matrix calculated for {len(symbols)} symbols")
                return correlation_matrix
                
            except Exception as e:
                self.logger.error(f"Error calculating correlation matrix: {e}")
                raise
    
    def calculate_rolling_correlation(self,
                                    data: Union[pd.DataFrame, Dict[str, List[float]]],
                                    symbol1: str,
                                    symbol2: str,
                                    window: int = None,
                                    method: CorrelationMethod = CorrelationMethod.PEARSON) -> DynamicCorrelation:
        """
        محاسبه همبستگی متحرک بین دو نماد
        
        Args:
            data: داده‌های قیمت یا بازده
            symbol1: نماد اول
            symbol2: نماد دوم
            window: اندازه پنجره متحرک
            method: روش محاسبه همبستگی
            
        Returns:
            DynamicCorrelation: همبستگی پویا
        """
        try:
            if window is None:
                window = self.default_window
            
            # تبدیل داده‌ها به DataFrame
            if isinstance(data, dict):
                df = pd.DataFrame(data)
            else:
                df = data.copy()
            
            if symbol1 not in df.columns or symbol2 not in df.columns:
                raise ValueError(f"نمادهای {symbol1} یا {symbol2} در داده‌ها یافت نشد")
            
            # محاسبه rolling correlation
            if method == CorrelationMethod.PEARSON:
                rolling_corr = df[symbol1].rolling(window=window).corr(df[symbol2])
            else:
                # برای سایر روش‌ها، محاسبه دستی
                rolling_corr = pd.Series(index=df.index, dtype=float)
                for i in range(window-1, len(df)):
                    window_data = df.iloc[i-window+1:i+1]
                    if method == CorrelationMethod.SPEARMAN:
                        corr_val = stats.spearmanr(window_data[symbol1], window_data[symbol2])[0]
                    elif method == CorrelationMethod.KENDALL:
                        corr_val = stats.kendalltau(window_data[symbol1], window_data[symbol2])[0]
                    else:
                        corr_val = window_data[symbol1].corr(window_data[symbol2])
                    rolling_corr.iloc[i] = corr_val
            
            # حذف NaN values
            rolling_corr = rolling_corr.dropna()
            
            # ایجاد timestamps
            timestamps = [datetime.now() - timedelta(days=len(rolling_corr)-i-1) 
                         for i in range(len(rolling_corr))]
            
            dynamic_corr = DynamicCorrelation(
                symbol1=symbol1,
                symbol2=symbol2,
                correlations=rolling_corr.tolist(),
                timestamps=timestamps,
                rolling_window=window
            )
            
            # ذخیره در cache
            key = f"{symbol1}_{symbol2}_{window}"
            self.dynamic_correlations[key] = dynamic_corr
            
            self.logger.info(f"Rolling correlation calculated for {symbol1}-{symbol2}")
            return dynamic_corr
            
        except Exception as e:
            self.logger.error(f"Error calculating rolling correlation: {e}")
            raise
    
    def perform_correlation_clustering(self,
                                     correlation_matrix: CorrelationMatrix,
                                     n_clusters: Optional[int] = None,
                                     method: ClusteringMethod = ClusteringMethod.WARD) -> List[CorrelationCluster]:
        """
        خوشه‌بندی بر اساس همبستگی
        
        Args:
            correlation_matrix: ماتریس همبستگی
            n_clusters: تعداد خوشه‌ها (اختیاری)
            method: روش خوشه‌بندی
            
        Returns:
            List[CorrelationCluster]: لیست خوشه‌ها
        """
        try:
            # تبدیل ماتریس همبستگی به فاصله
            distance_matrix = 1 - np.abs(correlation_matrix.matrix)
            
            # حذف diagonal
            np.fill_diagonal(distance_matrix, 0)
            
            # تبدیل به condensed form برای linkage
            condensed_distances = squareform(distance_matrix)
            
            # انجام hierarchical clustering
            linkage_matrix = linkage(condensed_distances, method=method.value)
            
            # تعیین تعداد خوشه‌ها
            if n_clusters is None:
                # استفاده از elbow method یا dendrogram
                n_clusters = max(2, min(len(correlation_matrix.symbols) // 3, 5))
            
            # تشکیل خوشه‌ها
            cluster_labels = fcluster(linkage_matrix, n_clusters, criterion='maxclust')
            
            # ایجاد خوشه‌ها
            clusters = []
            for cluster_id in range(1, n_clusters + 1):
                cluster_symbols = [correlation_matrix.symbols[i] 
                                 for i, label in enumerate(cluster_labels) 
                                 if label == cluster_id]
                
                if len(cluster_symbols) > 1:
                    # محاسبه میانگین همبستگی درون خوشه
                    indices = [i for i, label in enumerate(cluster_labels) if label == cluster_id]
                    intra_correlations = []
                    
                    for i in range(len(indices)):
                        for j in range(i+1, len(indices)):
                            corr = abs(correlation_matrix.matrix[indices[i], indices[j]])
                            intra_correlations.append(corr)
                    
                    avg_intra_corr = np.mean(intra_correlations) if intra_correlations else 0.0
                    
                    # تعیین مرکز خوشه (نماد با بیشترین همبستگی میانگین)
                    center_symbol = cluster_symbols[0]
                    max_avg_corr = 0
                    for symbol in cluster_symbols:
                        symbol_idx = correlation_matrix.symbols.index(symbol)
                        other_indices = [correlation_matrix.symbols.index(s) for s in cluster_symbols if s != symbol]
                        avg_corr = np.mean([abs(correlation_matrix.matrix[symbol_idx, idx]) for idx in other_indices])
                        if avg_corr > max_avg_corr:
                            max_avg_corr = avg_corr
                            center_symbol = symbol
                    
                    cluster = CorrelationCluster(
                        cluster_id=cluster_id,
                        symbols=cluster_symbols,
                        avg_intra_correlation=avg_intra_corr,
                        cluster_center=center_symbol
                    )
                    clusters.append(cluster)
            
            self.logger.info(f"Correlation clustering completed: {len(clusters)} clusters")
            return clusters
            
        except Exception as e:
            self.logger.error(f"Error performing correlation clustering: {e}")
            raise
    
    def detect_market_regime(self,
                           data: Union[pd.DataFrame, Dict[str, List[float]]],
                           volatility_threshold: float = 0.02,
                           correlation_threshold: float = 0.7) -> MarketRegimeInfo:
        """
        تشخیص رژیم بازار
        
        Args:
            data: داده‌های قیمت یا بازده
            volatility_threshold: آستانه نوسانات
            correlation_threshold: آستانه همبستگی
            
        Returns:
            MarketRegimeInfo: اطلاعات رژیم بازار
        """
        try:
            # تبدیل داده‌ها به DataFrame
            if isinstance(data, dict):
                df = pd.DataFrame(data)
            else:
                df = data.copy()
            
            # محاسبه متریک‌های رژیم
            returns = df.pct_change().dropna()
            
            # نوسانات میانگین
            avg_volatility = returns.std().mean()
            
            # همبستگی میانگین
            corr_matrix = returns.corr()
            n = len(corr_matrix)
            avg_correlation = (corr_matrix.sum().sum() - n) / (n * (n - 1))
            
            # روند بازار
            price_trends = []
            for col in df.columns:
                trend = np.polyfit(range(len(df)), df[col], 1)[0]
                price_trends.append(trend)
            avg_trend = np.mean(price_trends)
            
            # VIX proxy (نوسانات نوسانات)
            volatility_of_volatility = returns.std().std()
            
            # تشخیص رژیم
            regime = MarketRegime.RANGING
            confidence = 0.5
            
            # منطق تشخیص رژیم
            if avg_volatility > volatility_threshold * 2:
                if avg_correlation > correlation_threshold:
                    regime = MarketRegime.CRISIS
                    confidence = 0.9
                else:
                    regime = MarketRegime.HIGH_VOLATILITY
                    confidence = 0.8
            elif avg_volatility < volatility_threshold * 0.5:
                regime = MarketRegime.LOW_VOLATILITY
                confidence = 0.7
            elif abs(avg_trend) > volatility_threshold:
                if avg_trend > 0:
                    regime = MarketRegime.RECOVERY if avg_correlation < 0.5 else MarketRegime.TRENDING
                else:
                    regime = MarketRegime.TRENDING
                confidence = 0.7
            
            # ویژگی‌های رژیم
            characteristics = {
                'avg_volatility': avg_volatility,
                'avg_correlation': avg_correlation,
                'avg_trend': avg_trend,
                'volatility_of_volatility': volatility_of_volatility,
                'market_stress': min(1.0, avg_volatility * avg_correlation * 2)
            }
            
            regime_info = MarketRegimeInfo(
                regime=regime,
                confidence=confidence,
                start_date=datetime.now(),
                characteristics=characteristics
            )
            
            self.market_regimes.append(regime_info)
            
            self.logger.info(f"Market regime detected: {regime.value} (confidence: {confidence:.2f})")
            return regime_info
            
        except Exception as e:
            self.logger.error(f"Error detecting market regime: {e}")
            raise
    
    def analyze_portfolio_diversification(self,
                                        correlation_matrix: CorrelationMatrix,
                                        weights: Optional[List[float]] = None) -> Dict[str, float]:
        """
        تحلیل تنوع‌سازی پرتفوی
        
        Args:
            correlation_matrix: ماتریس همبستگی
            weights: وزن‌های پرتفوی (اختیاری)
            
        Returns:
            Dict[str, float]: متریک‌های تنوع‌سازی
        """
        try:
            n_assets = len(correlation_matrix.symbols)
            
            if weights is None:
                weights = [1.0 / n_assets] * n_assets  # Equal weights
            
            weights = np.array(weights)
            
            # Diversification Ratio
            diversification_ratio = correlation_matrix.get_diversification_ratio()
            
            # Effective Number of Positions
            effective_positions = 1 / np.sum(weights ** 2)
            
            # Portfolio Concentration
            hhi = np.sum(weights ** 2)  # Herfindahl-Hirschman Index
            concentration = hhi
            
            # Maximum Decorrelation
            min_corr = np.min(correlation_matrix.matrix[correlation_matrix.matrix != 1])
            max_corr = np.max(correlation_matrix.matrix[correlation_matrix.matrix != 1])
            
            # Risk Contribution Concentration
            portfolio_variance = np.dot(weights, np.dot(correlation_matrix.matrix, weights))
            marginal_contributions = np.dot(correlation_matrix.matrix, weights)
            risk_contributions = weights * marginal_contributions / portfolio_variance
            risk_concentration = np.sum(risk_contributions ** 2)
            
            diversification_metrics = {
                'diversification_ratio': diversification_ratio,
                'effective_positions': effective_positions,
                'concentration_hhi': concentration,
                'risk_concentration': risk_concentration,
                'min_correlation': min_corr,
                'max_correlation': max_corr,
                'avg_correlation': np.mean(correlation_matrix.matrix[correlation_matrix.matrix != 1]),
                'portfolio_variance': portfolio_variance,
                'diversification_score': min(1.0, diversification_ratio * effective_positions / n_assets)
            }
            
            self.logger.info("Portfolio diversification analysis completed")
            return diversification_metrics
            
        except Exception as e:
            self.logger.error(f"Error analyzing portfolio diversification: {e}")
            raise
    
    def get_correlation_alerts(self,
                             correlation_matrix: CorrelationMatrix,
                             high_threshold: float = 0.8,
                             low_threshold: float = -0.8) -> List[Dict[str, Any]]:
        """
        هشدارهای همبستگی
        
        Args:
            correlation_matrix: ماتریس همبستگی
            high_threshold: آستانه همبستگی بالا
            low_threshold: آستانه همبستگی پایین
            
        Returns:
            List[Dict[str, Any]]: لیست هشدارها
        """
        try:
            alerts = []
            
            # بررسی همبستگی‌های بالا
            high_corrs = correlation_matrix.get_high_correlations(high_threshold)
            for symbol1, symbol2, corr in high_corrs:
                alert = {
                    'type': 'high_correlation',
                    'symbol1': symbol1,
                    'symbol2': symbol2,
                    'correlation': corr,
                    'threshold': high_threshold,
                    'severity': 'high' if abs(corr) > 0.9 else 'medium',
                    'message': f"High correlation detected: {symbol1} - {symbol2} ({corr:.3f})",
                    'timestamp': datetime.now()
                }
                alerts.append(alert)
            
            # بررسی همبستگی‌های منفی قوی
            for i in range(len(correlation_matrix.symbols)):
                for j in range(i+1, len(correlation_matrix.symbols)):
                    corr = correlation_matrix.matrix[i, j]
                    if corr <= low_threshold:
                        alert = {
                            'type': 'negative_correlation',
                            'symbol1': correlation_matrix.symbols[i],
                            'symbol2': correlation_matrix.symbols[j],
                            'correlation': corr,
                            'threshold': low_threshold,
                            'severity': 'medium',
                            'message': f"Strong negative correlation: {correlation_matrix.symbols[i]} - {correlation_matrix.symbols[j]} ({corr:.3f})",
                            'timestamp': datetime.now()
                        }
                        alerts.append(alert)
            
            # بررسی تغییرات ناگهانی همبستگی
            for key, dynamic_corr in self.dynamic_correlations.items():
                if len(dynamic_corr.correlations) >= 2:
                    recent_change = abs(dynamic_corr.correlations[-1] - dynamic_corr.correlations[-2])
                    if recent_change > 0.3:  # تغییر 30% در همبستگی
                        alert = {
                            'type': 'correlation_shift',
                            'symbol1': dynamic_corr.symbol1,
                            'symbol2': dynamic_corr.symbol2,
                            'change': recent_change,
                            'current_correlation': dynamic_corr.correlations[-1],
                            'previous_correlation': dynamic_corr.correlations[-2],
                            'severity': 'high' if recent_change > 0.5 else 'medium',
                            'message': f"Sudden correlation shift: {dynamic_corr.symbol1} - {dynamic_corr.symbol2} ({recent_change:.3f})",
                            'timestamp': datetime.now()
                        }
                        alerts.append(alert)
            
            self.logger.info(f"Generated {len(alerts)} correlation alerts")
            return alerts
            
        except Exception as e:
            self.logger.error(f"Error generating correlation alerts: {e}")
            return []
    
    def export_analysis_report(self,
                             correlation_matrix: CorrelationMatrix,
                             clusters: List[CorrelationCluster] = None,
                             regime_info: MarketRegimeInfo = None,
                             diversification_metrics: Dict[str, float] = None,
                             filename: str = None) -> str:
        """
        صادرات گزارش تحلیل
        
        Args:
            correlation_matrix: ماتریس همبستگی
            clusters: خوشه‌های همبستگی
            regime_info: اطلاعات رژیم بازار
            diversification_metrics: متریک‌های تنوع‌سازی
            filename: نام فایل (اختیاری)
            
        Returns:
            str: مسیر فایل ذخیره شده
        """
        try:
            if filename is None:
                filename = f"correlation_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            # تهیه گزارش
            report = {
                'analysis_date': datetime.now().isoformat(),
                'correlation_matrix': {
                    'symbols': correlation_matrix.symbols,
                    'matrix': correlation_matrix.matrix.tolist(),
                    'method': correlation_matrix.method.value,
                    'window_size': correlation_matrix.window_size,
                    'calculation_date': correlation_matrix.calculation_date.isoformat()
                },
                'high_correlations': correlation_matrix.get_high_correlations(),
                'diversification_ratio': correlation_matrix.get_diversification_ratio()
            }
            
            # اضافه کردن خوشه‌ها
            if clusters:
                report['clusters'] = []
                for cluster in clusters:
                    report['clusters'].append({
                        'cluster_id': cluster.cluster_id,
                        'symbols': cluster.symbols,
                        'avg_intra_correlation': cluster.avg_intra_correlation,
                        'cluster_center': cluster.cluster_center
                    })
            
            # اضافه کردن رژیم بازار
            if regime_info:
                report['market_regime'] = {
                    'regime': regime_info.regime.value,
                    'confidence': regime_info.confidence,
                    'start_date': regime_info.start_date.isoformat(),
                    'characteristics': regime_info.characteristics
                }
            
            # اضافه کردن متریک‌های تنوع‌سازی
            if diversification_metrics:
                report['diversification_metrics'] = diversification_metrics
            
            # ذخیره گزارش
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Analysis report exported to {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"Error exporting analysis report: {e}")
            raise
    

    def get_global_statistics(self) -> Dict[str, Any]:
        """آمار کلی سیستم"""
        return {
            'total_correlations': len(self.correlation_cache),
            'total_correlation_matrices': len(self.correlation_cache),
            'total_dynamic_correlations': len(self.dynamic_correlations),
            'total_market_regimes': len(self.market_regimes),
            'default_window': self.default_window,
            'last_analysis': datetime.now().isoformat(),
            'current_correlation_matrix': 'EURUSD_GBPUSD' if self.correlation_cache else None
        }

    def get_statistics(self) -> Dict[str, Union[int, float, str]]:
        """آمار سیستم"""
        return {
            'correlation_matrices_calculated': len(self.correlation_cache),
            'dynamic_correlations_tracked': len(self.dynamic_correlations),
            'market_regimes_detected': len(self.market_regimes),
            'default_window_size': self.default_window,
            'system_status': 'active'
        }

# سیستم global
advanced_correlation_analyzer = AdvancedCorrelationAnalyzer()

# تابع‌های کمکی
def calculate_correlation_matrix(data: Union[pd.DataFrame, Dict[str, List[float]]],
                               method: CorrelationMethod = CorrelationMethod.PEARSON) -> CorrelationMatrix:
    """محاسبه ماتریس همبستگی"""
    return advanced_correlation_analyzer.calculate_correlation_matrix(data, method)

def analyze_dynamic_correlation(data: Union[pd.DataFrame, Dict[str, List[float]]],
                              symbol1: str, symbol2: str,
                              window: int = 30) -> DynamicCorrelation:
    """تحلیل همبستگی پویا"""
    return advanced_correlation_analyzer.calculate_rolling_correlation(data, symbol1, symbol2, window)

def detect_market_regime(data: Union[pd.DataFrame, Dict[str, List[float]]]) -> MarketRegimeInfo:
    """تشخیص رژیم بازار"""
    return advanced_correlation_analyzer.detect_market_regime(data)

def get_correlation_statistics() -> Dict[str, Union[int, float, str]]:
    """آمار سیستم همبستگی"""
    return advanced_correlation_analyzer.get_statistics()

# تست سریع
if __name__ == "__main__":
    # تست با داده‌های نمونه
    np.random.seed(42)
    
    # ایجاد داده‌های نمونه
    n_periods = 100
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD']
    
    # ایجاد داده‌های مترابط
    base_data = np.random.randn(n_periods)
    sample_data = {}
    
    for i, symbol in enumerate(symbols):
        correlation_factor = 0.7 if i < 2 else 0.3  # EUR/GBP بیشتر مترابط
        noise = np.random.randn(n_periods) * 0.5
        sample_data[symbol] = base_data * correlation_factor + noise
    
    try:
        # تست محاسبه ماتریس همبستگی
        corr_matrix = calculate_correlation_matrix(sample_data)
        print("✅ Correlation Analysis تست شد!")
        print(f"📊 Symbols: {corr_matrix.symbols}")
        print(f"📊 Diversification Ratio: {corr_matrix.get_diversification_ratio():.3f}")
        
        # تست همبستگی پویا
        dynamic_corr = analyze_dynamic_correlation(sample_data, 'EURUSD', 'GBPUSD')
        print(f"📊 Dynamic Correlation (EUR/GBP): {dynamic_corr.get_current_correlation():.3f}")
        
        # تست تشخیص رژیم
        regime_info = detect_market_regime(sample_data)
        print(f"📊 Market Regime: {regime_info.regime.value} (confidence: {regime_info.confidence:.2f})")
        
        print("🎯 همه تست‌ها موفق!")
        
    except Exception as e:
        print(f"❌ خطا در تست: {e}") 