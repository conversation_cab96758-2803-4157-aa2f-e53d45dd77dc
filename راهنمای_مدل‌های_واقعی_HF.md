# 🚀 راهنمای کامل مدل‌های مالی واقعی Hugging Face

> **آپدیت شده: ژانویه 2025** - شامل جدیدترین و قدرتمندترین مدل‌های مالی

## 📊 خلاصه آماری

- **تعداد کل مدل‌ها:** ۶۵+ مدل تایید شده
- **دسته‌بندی‌ها:** ۸ دسته اصلی
- **قابلیت‌ها:** پیش‌بینی، تحلیل، تصمیم‌گیری
- **زبان‌ها:** چندزبانه (انگلیسی، فارسی، چینی و...)

---

## 🎯 دسته‌بندی مدل‌ها

### 1️⃣ مدل‌های پیش‌بینی قیمت و Time Series (۱۵ مدل)

#### 🔥 **سری Chronos-Bolt از Amazon (جدیدترین و سریع‌ترین)**
```python
# نصب و استفاده
pip install chronos-forecasting
from chronos import BaseChronosPipeline

# مدل‌های موجود
amazon/chronos-bolt-base    # ۲۰۵M پارامتر - بهترین گزینه
amazon/chronos-bolt-small   # ۴۸M پارامتر - سریع
amazon/chronos-bolt-mini    # ۲۱M پارامتر - سبک
amazon/chronos-bolt-tiny    # ۹M پارامتر - فوق‌سبک
```

**ویژگی‌های Chronos-Bolt:**
- ✅ ۲۵۰ برابر سریع‌تر از نسخه‌های قبلی
- ✅ ۲۰ برابر کم‌مصرف‌تر از نظر حافظه
- ✅ دقت بالاتر از مدل‌های Statistical
- ✅ Zero-shot forecasting (بدون آموزش)

#### 📈 **سری Chronos کلاسیک**
```python
amazon/chronos-t5-large     # ۷۱۰M پارامتر - دقیق‌ترین
amazon/chronos-t5-base      # ۲۰۰M پارامتر - متعادل
amazon/chronos-t5-small     # ۴۶M پارامتر - سریع
```

#### 🧠 **مدل‌های پیشرفته دیگر**
```python
Salesforce/moirai-1.0-R-base        # Foundation Model
google/timesfm-1.0-200m             # Google TimesFM
ibm-granite/granite-timeseries-ttm-v1  # IBM Granite
```

---

### 2️⃣ مدل‌های تحلیل احساسات مالی (۸ مدل)

#### 💰 **بهترین مدل‌ها**
```python
# FinBERT - استاندارد طلایی
ProsusAI/finbert

# FinAI-BERT - تشخیص AI در گزارش‌ها  
bilalzafar/FinAI-BERT

# CryptoBERT - ارزهای دیجیتال
ElKulako/cryptobert

# StockTwits - تحلیل پیام‌های سهام
zhayunduo/roberta-base-stocktwits-finetuned
```

**کاربردها:**
- 📰 تحلیل اخبار مالی
- 💬 نظرات رسانه‌های اجتماعی
- 📊 گزارش‌های شرکت‌ها
- ₿ ارزهای دیجیتال

---

### 3️⃣ مدل‌های زبان بزرگ مالی (۷ مدل)

#### 🔥 **FinGPT و Llama مالی**
```python
# FinGPT برای پیش‌بینی Dow 30
FinGPT/fingpt-forecaster_dow30_llama2-7b_lora

# Fin-Llama بزرگ
bavest/fin-llama-33b-merged

# Llama-3 برای اسناد SEC
arcee-ai/Llama-3-SEC-Base

# FinMA - مدل جامع NLP مالی
ChanceFocus/finma-7b-nlp
ChanceFocus/finma-7b-full
```

---

### 4️⃣ مدل‌های چندحالته (متن + تصویر + جدول) (۶ مدل)

#### 📄 **تحلیل اسناد مالی**
```python
# LayoutLMv3 - بهترین مدل چندحالته
microsoft/layoutlmv3-base

# تحلیل فاکتور و صورتحساب
unstructured-io/layoutlm-invoices

# تشخیص جدول
microsoft/table-transformer-structure-recognition

# تبدیل تصویر به متن
google/pix2struct-base
```

---

### 5️⃣ مدل‌های تحلیل اسناد و گزارش (۶ مدل)

#### 📝 **خلاصه‌سازی و تحلیل**
```python
# BART برای خلاصه‌سازی اخبار
facebook/bart-large-cnn

# Pegasus برای خلاصه‌سازی حرفه‌ای
google/pegasus-xsum

# Longformer برای اسناد طولانی
allenai/longformer-base-4096

# LayoutLMv2 برای ساختار اسناد
microsoft/layoutlmv2-base-uncased
```

---

### 6️⃣ مدل‌های پورتفولیو و ریسک (۴ مدل)

#### ⚖️ **مدیریت ریسک**
```python
# تحلیل همبستگی دارایی‌ها
sentence-transformers/all-MiniLM-L6-v2

# تحلیل الگوریتم‌های مالی
microsoft/codebert-base

# ارزیابی ریسک
microsoft/DialoGPT-small
```

---

### 7️⃣ مدل‌های معاملاتی (۴ مدل)

#### 🤖 **ربات‌های معاملاتی**
```python
# پیش‌بینی روند
microsoft/prophetnet-large-uncased

# تصمیم‌گیری معاملات
facebook/opt-1.3b

# استراتژی معاملات
EleutherAI/gpt-neo-1.3B
```

---

### 8️⃣ مدل‌های ارزهای دیجیتال (۳ مدل)

#### ₿ **تحلیل Crypto**
```python
# CryptoBERT - تخصصی ارز دیجیتال
ElKulako/cryptobert

# DistilBERT سریع
huggingface/distilbert-base-uncased
```

---

## 🚀 نحوه نصب و استفاده

### ۱. نصب کتابخانه‌های پایه
```bash
pip install transformers torch datasets
pip install chronos-forecasting  # برای Chronos
pip install autogluon           # برای استفاده آسان‌تر
```

### ۲. مثال پیش‌بینی قیمت با Chronos-Bolt
```python
import pandas as pd
from chronos import BaseChronosPipeline
import torch

# بارگذاری مدل
pipeline = BaseChronosPipeline.from_pretrained(
    "amazon/chronos-bolt-base",
    device_map="cuda",  # یا "cpu"
    torch_dtype=torch.bfloat16,
)

# پیش‌بینی
forecast = pipeline.predict(
    context=torch.tensor(historical_prices),
    prediction_length=30  # ۳۰ روز آینده
)
```

### ۳. مثال تحلیل احساسات مالی
```python
from transformers import pipeline

# بارگذاری مدل FinBERT
sentiment_analyzer = pipeline(
    "sentiment-analysis",
    model="ProsusAI/finbert"
)

# تحلیل متن
text = "Apple reported strong quarterly earnings"
result = sentiment_analyzer(text)
print(result)  # positive/negative/neutral
```

### ۴. مثال تحلیل اسناد مالی
```python
from transformers import LayoutLMv3Processor, LayoutLMv3ForQuestionAnswering
from PIL import Image

# بارگذاری مدل و پردازشگر
processor = LayoutLMv3Processor.from_pretrained("microsoft/layoutlmv3-base")
model = LayoutLMv3ForQuestionAnswering.from_pretrained("microsoft/layoutlmv3-base")

# تحلیل تصویر سند
image = Image.open("financial_statement.png")
question = "What is the net profit?"

# پردازش
inputs = processor(image, question, return_tensors="pt")
outputs = model(**inputs)

# استخراج پاسخ
answer = processor.decode(outputs.answer.argmax(-1))
```

---

## 🎯 پیشنهادات برای کاربردهای مختلف

### 📈 **پیش‌بینی قیمت سهام**
1. `amazon/chronos-bolt-base` - بهترین دقت
2. `amazon/chronos-t5-large` - دقت بالا
3. `Salesforce/moirai-1.0-R-base` - Foundation Model

### 💬 **تحلیل احساسات**
1. `ProsusAI/finbert` - استاندارد طلایی
2. `bilalzafar/FinAI-BERT` - تشخیص AI
3. `ElKulako/cryptobert` - ارزهای دیجیتال

### 📄 **تحلیل اسناد**
1. `microsoft/layoutlmv3-base` - چندحالته
2. `facebook/bart-large-cnn` - خلاصه‌سازی
3. `google/pegasus-xsum` - گزارش‌سازی

### 🤖 **ربات معاملاتی**
1. `microsoft/prophetnet-large-uncased` - پیش‌بینی روند
2. `facebook/opt-1.3b` - تصمیم‌گیری
3. `EleutherAI/gpt-neo-1.3B` - استراتژی

---

## ⚡ مقایسه عملکرد

| مدل | سرعت | دقت | حافظه | کاربرد |
|-----|------|-----|-------|--------|
| Chronos-Bolt-Base | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | پیش‌بینی |
| FinBERT | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | احساسات |
| LayoutLMv3 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | اسناد |
| FinGPT | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ | LLM مالی |

---

## 🔧 نکات تکنیکی مهم

### سخت‌افزار مورد نیاز
- **CPU:** مدل‌های Bolt و کوچک
- **GPU:** 8GB+ برای مدل‌های بزرگ
- **RAM:** 16GB+ توصیه می‌شود

### بهینه‌سازی
```python
# استفاده از precision کمتر
torch_dtype=torch.bfloat16

# استفاده از device mapping
device_map="auto"

# ذخیره cache
cache_dir="./model_cache"
```

---

## 🌟 مدل‌های جدید و آینده

### در حال توسعه
- Chronos-Bolt Large (قریب‌الوقوع)
- FinGPT-4 (در دست توسعه)
- Multimodal Financial LLMs

### پیشنهاد برای شروع
1. **مبتدی:** `amazon/chronos-bolt-small` + `ProsusAI/finbert`
2. **متوسط:** `amazon/chronos-bolt-base` + `FinGPT/fingpt-forecaster`
3. **پیشرفته:** ترکیب چند مدل + Fine-tuning

---

## 📞 منابع و پشتیبانی

- **GitHub Chronos:** https://github.com/amazon-science/chronos-forecasting
- **Hugging Face Hub:** https://huggingface.co/models
- **AutoGluon Docs:** https://auto.gluon.ai/
- **FinGPT:** https://github.com/AI4Finance-Foundation/FinGPT

---

## ⭐ نتیجه‌گیری

با این مجموعه ۶۵+ مدل تایید شده، شما می‌توانید:

✅ **پیش‌بینی دقیق قیمت** با Chronos-Bolt  
✅ **تحلیل احساسات** با FinBERT  
✅ **تحلیل اسناد** با LayoutLMv3  
✅ **ساخت ربات معاملاتی** با ProphetNet  
✅ **تحلیل ارزهای دیجیتال** با CryptoBERT  

> **توصیه:** شروع کنید با `amazon/chronos-bolt-base` برای پیش‌بینی و `ProsusAI/finbert` برای تحلیل احساسات. 