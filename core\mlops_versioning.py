#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 Model Versioning & MLOps System
سیستم versioning مدل‌ها با MLflow-compatible API + فالو‌بک در صورت نبود MLflow.
- ثبت و ذخیره نسخه‌های مدل
- فراخوانی و مقایسه مدل‌ها
- لاگ متریک‌ها و آرشیو آرتیفکت‌ها
- پشتیبانی از A/B تست (routing ساده)
"""
from __future__ import annotations

import logging
import os
import pickle
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")

try:
    import mlflow  # type: ignore
    from mlflow.tracking import MlflowClient  # type: ignore

    _MLFLOW_AVAILABLE = True
except ImportError:  # pragma: no cover
    _MLFLOW_AVAILABLE = False

    class MlflowClient:  # type: ignore
        """Minimal fallback client (in-memory) when MLflow is not installed"""

        _REGISTRY: Dict[str, Dict[str, Any]] = {}

        def __init__(self, *_, **__):
            pass

        # --------------------------------------------------------
        # Model CRUD
        # --------------------------------------------------------
        def create_registered_model(self, name: str):
            MlflowClient._REGISTRY.setdefault(name, {})

        def rename_registered_model(self, name: str, new_name: str):
            MlflowClient._REGISTRY[new_name] = MlflowClient._REGISTRY.pop(name, {})

        def delete_registered_model(self, name: str):
            MlflowClient._REGISTRY.pop(name, None)

        def create_model_version(self, name: str, source: str, run_id: str):
            versions = MlflowClient._REGISTRY.setdefault(name, {})
            ver = str(len(versions) + 1)
            versions[ver] = {
                "version": ver,
                "source": source,
                "run_id": run_id,
                "registered_at": datetime.utcnow().isoformat(),
            }
            return versions[ver]

        def get_latest_versions(self, name: str, stages: Optional[list] = None):
            versions = MlflowClient._REGISTRY.get(name, {})
            return [v for v in versions.values()]

    import json  # fallback for logging metrics

# ----------------------------------------------------------------------
# Helper functions
# ----------------------------------------------------------------------

_TRACKING_URI = os.getenv("MLFLOW_TRACKING_URI", "file:///tmp/mlruns")
_DEFAULT_STAGE = "None"
_client: Optional[MlflowClient] = None


def _get_client() -> MlflowClient:
    global _client
    if _client is None:
        _client = MlflowClient(tracking_uri=_TRACKING_URI) if _MLFLOW_AVAILABLE else MlflowClient()
    return _client

# ----------------------------------------------------------------------
# Public API
# ----------------------------------------------------------------------

def log_model(model: Any, name: str, metrics: Optional[Dict[str, float]] = None, params: Optional[Dict[str, Any]] = None, tags: Optional[Dict[str, Any]] = None) -> str:
    """Log and register a model version. Returns version string."""
    client = _get_client()

    if _MLFLOW_AVAILABLE:
        import mlflow
        with mlflow.start_run(run_name=f"run_{name}_{datetime.utcnow().isoformat()}") as run:
            run_id = run.info.run_id
            mlflow.log_params(params or {})
            mlflow.log_metrics(metrics or {})
            mlflow.set_tags(tags or {})
            mlflow.sklearn.log_model(model, artifact_path="model", registered_model_name=name)
            logger.info(f"✅ Model logged to MLflow – run_id={run_id}")
            return "auto"  # MLflow assigns version automatically
    else:
        # Serialize model to temp file
        temp_dir = Path(tempfile.mkdtemp())
        file_path = temp_dir / "model.pkl"
        with open(file_path, "wb") as f:
            pickle.dump(model, f)
        client.create_registered_model(name)
        version_info = client.create_model_version(name=name, source=str(file_path), run_id="local-run")
        logger.info(f"✅ Model logged locally – {name} v{version_info['version']}")
        return version_info["version"]


def load_model(name: str, version: str | None = None):
    """Load a model from registry."""
    client = _get_client()
    if _MLFLOW_AVAILABLE:
        model_uri = f"models:/{name}/{version or 'latest'}"
        import mlflow
        return mlflow.sklearn.load_model(model_uri)
    else:
        versions = client.get_latest_versions(name)
        if not versions:
            raise ValueError(f"Model not found: {name}")
        if version is None:
            version = versions[-1]["version"]
        info = next((v for v in versions if v["version"] == str(version)), None)
        if info is None:
            raise ValueError(f"Version {version} not found for model {name}")
        with open(info["source"], "rb") as f:
            return pickle.load(f)


def compare_models(name: str, metric: str = "accuracy") -> Dict[str, float]:
    """Return dict {version: metric_value}. In fallback, random values."""
    client = _get_client()

    results: Dict[str, float] = {}
    if _MLFLOW_AVAILABLE:
        versions = client.get_latest_versions(name)
        for v in versions:
            mv = client.get_model_version(name, v.version)
            metrics = mv.metrics  # type: ignore
            results[str(v.version)] = metrics.get(metric, float("nan"))
    else:
        import random
        versions = client.get_latest_versions(name)
        for v in versions:
            results[v["version"]] = random.random()
    return results


# A/B testing helper – simple traffic splitter
_AB_TEST_REGISTRY: Dict[str, Dict[str, float]] = {}

def configure_ab_test(name: str, versions: Dict[str, float]):
    """Define traffic split e.g., {'1':0.5, '2':0.5}"""
    if abs(sum(versions.values()) - 1.0) > 1e-6:
        raise ValueError("Traffic split must sum to 1.0")
    _AB_TEST_REGISTRY[name] = versions
    logger.info(f"🔀 A/B test configured for {name}: {versions}")


def select_ab_version(name: str) -> str:
    if name not in _AB_TEST_REGISTRY:
        raise KeyError("A/B test not configured for model")
    import random
    r = random.random()
    cumulative = 0.0
    for version, weight in _AB_TEST_REGISTRY[name].items():
        cumulative += weight
        if r <= cumulative:
            return version
    # Fallback (should not reach)
    return next(iter(_AB_TEST_REGISTRY[name]))


class ModelRegistry:
    """Simple in-process model registry wrapper around mlflow/fallback client."""
    def __init__(self, client: MlflowClient):
        self._client = client

    def log(self, model: Any, name: str, **kwargs):
        return log_model(model, name, **kwargs)

    def load(self, name: str, version: str | None = None):
        return load_model(name, version)

    def list_versions(self, name: str):
        return _get_client().get_latest_versions(name)

# Global registry instance
model_registry = ModelRegistry(_get_client())

__all__ = [
    "log_model",
    "load_model",
    "compare_models",
    "configure_ab_test",
    "select_ab_version",
]
__all__ += ["ModelRegistry", "model_registry"] 