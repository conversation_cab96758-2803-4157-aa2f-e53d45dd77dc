#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import yaml
from typing import Dict, Any, Optional
import logging
from pathlib import Path

class ConfigManager:
    """مدیریت پیکربندی پیشرفته"""
    
    def __init__(self, base_config_path: Optional[str] = None):
        """
        Initialize configuration manager
        
        :param base_config_path: مسیر فایل پیکربندی پایه
        """
        self.logger = logging.getLogger(__name__)
        self.base_config_path = base_config_path or self._find_default_config()
        self.config = self._load_base_config()
    
    def _find_default_config(self) -> str:
        """یافتن فایل پیکربندی پیش‌فرض"""
        possible_paths = [
            'config.yaml',
            'config.json',
            'configs/default_config.yaml',
            'configs/default_config.json'
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # اگر فایل پیکربندی پیدا نشد، یک پیکربندی پیش‌فرض ایجاد کن
        default_config_path = 'configs/default_config.yaml'
        os.makedirs(os.path.dirname(default_config_path), exist_ok=True)
        
        default_config = {
            'models': {
                'sentiment': {
                    'model_name': 'mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis',
                    'max_length': 512,
                    'batch_size': 16
                },
                'timeseries': {
                    'sequence_length': 60,
                    'prediction_horizon': 1,
                    'features': ['open', 'high', 'low', 'close', 'volume'],
                    'technical_indicators': ['sma', 'ema', 'rsi', 'macd']
                },
                'rl': {
                    'algorithm': 'PPO',
                    'learning_rate': 0.0003,
                    'gamma': 0.99,
                    'epsilon': 0.2
                }
            },
            'training': {
                'epochs': 50,
                'validation_split': 0.2,
                'test_split': 0.1,
                'random_seed': 42
            },
            'logging': {
                'level': 'INFO',
                'path': 'logs/'
            }
        }
        
        with open(default_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False)
        
        return default_config_path
    
    def _load_base_config(self) -> Dict[str, Any]:
        """بارگذاری پیکربندی پایه"""
        try:
            if self.base_config_path.endswith('.yaml') or self.base_config_path.endswith('.yml'):
                with open(self.base_config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            else:
                with open(self.base_config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"خطا در بارگذاری پیکربندی: {e}")
            return {}
    
    def get_config(self, section: Optional[str] = None, key: Optional[str] = None) -> Any:
        """
        دریافت تنظیمات
        
        :param section: بخش مورد نظر (مثل 'models', 'training')
        :param key: کلید خاص در بخش
        :return: مقدار تنظیمات
        """
        try:
            if section is None:
                return self.config
            
            if section not in self.config:
                raise KeyError(f"بخش {section} در پیکربندی یافت نشد")
            
            if key is None:
                return self.config[section]
            
            if key not in self.config[section]:
                raise KeyError(f"کلید {key} در بخش {section} یافت نشد")
            
            return self.config[section][key]
        
        except Exception as e:
            self.logger.error(f"خطا در دریافت تنظیمات: {e}")
            return None
    
    def update_config(self, updates: Dict[str, Any], save: bool = True) -> bool:
        """
        به‌روزرسانی پیکربندی
        
        :param updates: تنظیمات جدید
        :param save: آیا تغییرات ذخیره شود؟
        :return: آیا به‌روزرسانی موفق بود؟
        """
        try:
            # ادغام تنظیمات جدید
            self._deep_update(self.config, updates)
            
            if save:
                # ذخیره‌سازی تغییرات
                with open(self.base_config_path, 'w', encoding='utf-8') as f:
                    if self.base_config_path.endswith('.yaml') or self.base_config_path.endswith('.yml'):
                        yaml.dump(self.config, f, default_flow_style=False)
                    else:
                        json.dump(self.config, f, indent=4, ensure_ascii=False)
            
            return True
        
        except Exception as e:
            self.logger.error(f"خطا در به‌روزرسانی پیکربندی: {e}")
            return False
    
    def _deep_update(self, original: Dict[str, Any], updates: Dict[str, Any]):
        """
        به‌روزرسانی عمیق دیکشنری
        
        :param original: دیکشنری اصلی
        :param updates: دیکشنری به‌روزرسانی
        """
        for key, value in updates.items():
            if isinstance(value, dict):
                original[key] = self._deep_update(original.get(key, {}), value)
            else:
                original[key] = value
        return original

def get_config(section: Optional[str] = None, key: Optional[str] = None) -> Any:
    """
    تابع سراسری برای دسترسی آسان به پیکربندی
    
    :param section: بخش مورد نظر
    :param key: کلید خاص
    :return: مقدار تنظیمات
    """
    config_manager = ConfigManager()
    return config_manager.get_config(section, key)

def main():
    # مثال استفاده
    config_manager = ConfigManager()
    
    # دریافت تنظیمات کل
    print(config_manager.get_config())
    
    # دریافت تنظیمات مدل سری زمانی
    print(config_manager.get_config('models', 'timeseries'))
    
    # به‌روزرسانی تنظیمات
    updates = {
        'models': {
            'timeseries': {
                'sequence_length': 120
            }
        }
    }
    config_manager.update_config(updates)

if __name__ == '__main__':
    main() 