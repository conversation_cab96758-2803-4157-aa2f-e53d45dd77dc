import os
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import logging
import json
from typing import Dict, List, Tuple, Any, Optional, Union
import matplotlib.pyplot as plt
from datetime import datetime
from collections import defaultdict

from .rl_models import RLModelFactory
from .continual_learning import ContinualLearning

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MarketEmbedding(nn.Module):
    """
    Neural network for embedding market characteristics.
    This network maps market features to a latent space that can be used for zero-shot learning.
    """
    def __init__(self, input_dim=20, embedding_dim=64, hidden_dim=128):
        """
        Initialize market embedding network
        
        Parameters:
        -----------
        input_dim : int
            Dimension of market features
        embedding_dim : int
            Dimension of the embedding space
        hidden_dim : int
            Dimension of hidden layers
        """
        super().__init__()
        
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.<PERSON><PERSON><PERSON>(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, embedding_dim)
        )
        
    def forward(self, x):
        """
        Forward pass through the network
        
        Parameters:
        -----------
        x : torch.Tensor
            Input market features
            
        Returns:
        --------
        torch.Tensor
            Market embedding
        """
        return self.network(x)

class MarketPolicyAdapter(nn.Module):
    """
    Adapter network that maps market embeddings to policy parameters.
    This network enables zero-shot adaptation to new markets.
    """
    def __init__(self, embedding_dim=64, policy_dim=128):
        """
        Initialize policy adapter network
        
        Parameters:
        -----------
        embedding_dim : int
            Dimension of market embeddings
        policy_dim : int
            Dimension of policy parameters
        """
        super().__init__()
        
        self.network = nn.Sequential(
            nn.Linear(embedding_dim, policy_dim),
            nn.ReLU(),
            nn.Linear(policy_dim, policy_dim)
        )
        
    def forward(self, embedding):
        """
        Forward pass through the network
        
        Parameters:
        -----------
        embedding : torch.Tensor
            Market embedding
            
        Returns:
        --------
        torch.Tensor
            Policy parameters
        """
        return self.network(embedding)

class ZeroShotLearning:
    """
    Zero-shot learning for trading in new markets with limited or no data.
    
    This class implements zero-shot and few-shot learning techniques to enable
    a trading model to generalize to new markets with minimal or no training data.
    It uses market embeddings, meta-learning, and knowledge transfer to adapt
    to new markets efficiently.
    """
    
    def __init__(
        self,
        base_model=None,
        embedding_dim=64,
        feature_extractor=None,
        similarity_threshold=0.7,
        use_market_embeddings=True,
        use_meta_learning=True,
        use_prototypes=True,
        adaptation_lr=0.001
    ):
        """
        Initialize ZeroShotLearning
        
        Parameters:
        -----------
        base_model : RL model, optional
            Base model for transfer learning
        embedding_dim : int
            Dimension of market embeddings
        feature_extractor : callable, optional
            Function to extract features from market data
        similarity_threshold : float
            Threshold for market similarity
        use_market_embeddings : bool
            Whether to use market embeddings
        use_meta_learning : bool
            Whether to use meta-learning techniques
        use_prototypes : bool
            Whether to use prototype-based learning
        adaptation_lr : float
            Learning rate for few-shot adaptation
        """
        self.base_model = base_model
        self.embedding_dim = embedding_dim
        self.feature_extractor = feature_extractor or self._default_feature_extractor
        self.similarity_threshold = similarity_threshold
        self.use_market_embeddings = use_market_embeddings
        self.use_meta_learning = use_meta_learning
        self.use_prototypes = use_prototypes
        self.adaptation_lr = adaptation_lr
        
        # Initialize market embeddings
        if self.use_market_embeddings:
            self.market_embedding_network = MarketEmbedding(
                input_dim=20,  # Default market feature dimension
                embedding_dim=embedding_dim
            )
            self.policy_adapter = MarketPolicyAdapter(
                embedding_dim=embedding_dim
            )
            self.embedding_optimizer = optim.Adam(
                list(self.market_embedding_network.parameters()) + 
                list(self.policy_adapter.parameters()),
                lr=0.001
            )
        
        # Store market embeddings
        self.market_embeddings = {}
        self.market_prototypes = {}
        
        # Store market similarity matrix
        self.similarity_matrix = {}
        
        # Store model performance on different markets
        self.market_performance = defaultdict(list)
        
    def _default_feature_extractor(self, market_data):
        """
        Default feature extractor for market data
        
        Parameters:
        -----------
        market_data : pd.DataFrame
            Market data
            
        Returns:
        --------
        np.ndarray
            Market features
        """
        if market_data is None or len(market_data) == 0:
            return np.zeros(20)
        
        # Extract basic statistical features
        features = []
        
        # Price features
        if 'close' in market_data.columns:
            close_prices = market_data['close'].values
            features.extend([
                np.mean(close_prices),
                np.std(close_prices),
                np.min(close_prices),
                np.max(close_prices),
                (close_prices[-1] - close_prices[0]) / close_prices[0]  # Overall return
            ])
        else:
            features.extend([0, 0, 0, 0, 0])
        
        # Volume features
        if 'volume' in market_data.columns:
            volume = market_data['volume'].values
            features.extend([
                np.mean(volume),
                np.std(volume),
                np.min(volume),
                np.max(volume)
            ])
        else:
            features.extend([0, 0, 0, 0])
        
        # Volatility features
        if 'close' in market_data.columns:
            returns = np.diff(close_prices) / close_prices[:-1]
            features.extend([
                np.std(returns),  # Volatility
                np.mean(np.abs(returns)),  # Average absolute return
                np.percentile(returns, 5),  # 5th percentile (downside risk)
                np.percentile(returns, 95),  # 95th percentile (upside potential)
                np.sum(returns > 0) / len(returns)  # Win rate
            ])
        else:
            features.extend([0, 0, 0, 0, 0])
        
        # Time-based features
        try:
            # Check if index is datetime
            if hasattr(market_data.index, 'hour'):
                hour_feature = market_data.index.hour.mean() / 24
            else:
                hour_feature = 0
                
            if hasattr(market_data.index, 'dayofweek'):
                day_feature = market_data.index.dayofweek.mean() / 6
            else:
                day_feature = 0
                
            if hasattr(market_data.index, 'month'):
                month_feature = market_data.index.month.mean() / 12
            else:
                month_feature = 0
                
            if hasattr(market_data.index, 'year'):
                year_feature = market_data.index.year.mean() / 2100
            else:
                year_feature = 0
        except:
            # If any error occurs, use default values
            hour_feature = 0
            day_feature = 0
            month_feature = 0
            year_feature = 0
        
        features.extend([hour_feature, day_feature, month_feature, year_feature])
        
        # Ensure we have exactly 20 features
        features = features[:20]
        features.extend([0] * (20 - len(features)))
        
        return np.array(features, dtype=np.float32)
    
    def compute_market_embedding(self, market_data, market_name=None):
        """
        Compute embedding for a market
        
        Parameters:
        -----------
        market_data : pd.DataFrame
            Market data
        market_name : str, optional
            Market identifier
            
        Returns:
        --------
        torch.Tensor
            Market embedding
        """
        if not self.use_market_embeddings:
            return None
        
        # Extract features
        features = self.feature_extractor(market_data)
        features_tensor = torch.tensor(features, dtype=torch.float32)
        
        # Compute embedding
        with torch.no_grad():
            embedding = self.market_embedding_network(features_tensor)
        
        # Store embedding if market name is provided
        if market_name is not None:
            self.market_embeddings[market_name] = embedding.detach().numpy()
        
        return embedding
    
    def compute_market_similarity(self, market_name1, market_name2):
        """
        Compute similarity between two markets
        
        Parameters:
        -----------
        market_name1 : str
            First market name
        market_name2 : str
            Second market name
            
        Returns:
        --------
        float
            Similarity score (0-1)
        """
        if not self.use_market_embeddings:
            return 0.0
        
        if market_name1 not in self.market_embeddings or market_name2 not in self.market_embeddings:
            return 0.0
        
        # Get embeddings
        embedding1 = self.market_embeddings[market_name1]
        embedding2 = self.market_embeddings[market_name2]
        
        # Compute cosine similarity
        similarity = np.dot(embedding1, embedding2) / (np.linalg.norm(embedding1) * np.linalg.norm(embedding2))
        
        # Store in similarity matrix
        if market_name1 not in self.similarity_matrix:
            self.similarity_matrix[market_name1] = {}
        self.similarity_matrix[market_name1][market_name2] = similarity
        
        return float(similarity)
    
    def find_similar_markets(self, market_name, threshold=None):
        """
        Find markets similar to the given market
        
        Parameters:
        -----------
        market_name : str
            Market name
        threshold : float, optional
            Similarity threshold
            
        Returns:
        --------
        List[Tuple[str, float]]
            List of (market_name, similarity) pairs
        """
        if not self.use_market_embeddings or market_name not in self.market_embeddings:
            return []
        
        threshold = threshold or self.similarity_threshold
        similar_markets = []
        
        for other_market in self.market_embeddings:
            if other_market == market_name:
                continue
            
            similarity = self.compute_market_similarity(market_name, other_market)
            if similarity >= threshold:
                similar_markets.append((other_market, similarity))
        
        # Sort by similarity (descending)
        similar_markets.sort(key=lambda x: x[1], reverse=True)
        
        return similar_markets
    
    def adapt_to_new_market(self, market_data, market_name, num_shots=0, adaptation_steps=100):
        """
        Adapt model to a new market using zero-shot or few-shot learning
        
        Parameters:
        -----------
        market_data : pd.DataFrame
            Market data
        market_name : str
            Market identifier
        num_shots : int
            Number of shots (samples) to use for adaptation
        adaptation_steps : int
            Number of adaptation steps
            
        Returns:
        --------
        model
            Adapted model for the new market
        """
        embedding = self.compute_market_embedding(market_data, market_name)
        similar_markets = self.find_similar_markets(market_name)
        if not similar_markets:
            logger.warning(f"No similar markets found for {market_name}. Using base model.")
            if self.base_model is not None:
                return self.base_model
            # اگر base_model وجود ندارد، یک مدل mock برگردان
            class MockModel:
                def predict(self, obs):
                    return 0, None
            return MockModel()
        if self.base_model is not None:
            adapted_model = self.base_model
        else:
            model_factory = RLModelFactory()
            adapted_model = model_factory.create('ppo', None)
        if num_shots == 0:
            logger.info(f"Performing zero-shot adaptation for {market_name}")
            if self.use_market_embeddings:
                with torch.no_grad():
                    policy_params = self.policy_adapter(embedding)
                self._apply_policy_parameters(adapted_model, policy_params)
            if self.use_prototypes:
                self._adapt_with_prototypes(adapted_model, market_name, similar_markets)
        elif num_shots > 0 and market_data is not None:
            logger.info(f"Performing {num_shots}-shot adaptation for {market_name}")
            few_shot_samples = self._extract_few_shot_samples(market_data, num_shots)
            self._fine_tune_on_samples(adapted_model, few_shot_samples, adaptation_steps)
        return adapted_model
    
    def _apply_policy_parameters(self, model, policy_params):
        """
        Apply policy parameters to model
        
        Parameters:
        -----------
        model : RL model
            Model to update
        policy_params : torch.Tensor
            Policy parameters
        """
        # Implementation depends on model type
        # This is a placeholder that needs to be customized based on the RL framework
        try:
            # For models with a policy network
            if hasattr(model, 'policy') and hasattr(model.policy, 'parameters'):
                policy_params_np = policy_params.detach().numpy()
                
                # Reshape policy parameters to match model parameters
                param_idx = 0
                for param in model.policy.parameters():
                    param_size = np.prod(param.shape)
                    param_slice = policy_params_np[param_idx:param_idx + param_size]
                    param_slice = param_slice.reshape(param.shape)
                    param.data = torch.tensor(param_slice, dtype=param.dtype)
                    param_idx += param_size
        except Exception as e:
            logger.error(f"Error applying policy parameters: {e}")
    
    def _adapt_with_prototypes(self, model, market_name, similar_markets):
        """
        Adapt model using prototypes from similar markets
        
        Parameters:
        -----------
        model : RL model
            Model to adapt
        market_name : str
            Target market name
        similar_markets : List[Tuple[str, float]]
            List of similar markets with similarity scores
        """
        if not self.use_prototypes or not similar_markets:
            return
        
        # Weighted average of prototype parameters
        total_weight = sum(sim for _, sim in similar_markets)
        
        if total_weight == 0:
            return
        
        # Apply weighted prototype parameters
        for similar_market, similarity in similar_markets:
            if similar_market in self.market_prototypes:
                prototype = self.market_prototypes[similar_market]
                weight = similarity / total_weight
                
                # Apply weighted prototype parameters to model
                self._apply_weighted_prototype(model, prototype, weight)
    
    def _apply_weighted_prototype(self, model, prototype, weight):
        """
        Apply weighted prototype parameters to model
        
        Parameters:
        -----------
        model : RL model
            Model to update
        prototype : dict
            Prototype parameters
        weight : float
            Weight to apply
        """
        # Implementation depends on model type
        # This is a placeholder that needs to be customized based on the RL framework
        pass
    
    def _extract_few_shot_samples(self, market_data, num_shots):
        """
        Extract few-shot samples from market data
        
        Parameters:
        -----------
        market_data : pd.DataFrame
            Market data
        num_shots : int
            Number of shots (samples) to extract
            
        Returns:
        --------
        list
            Few-shot samples
        """
        if market_data is None or len(market_data) < num_shots:
            return []
        
        # Extract random samples
        indices = np.random.choice(len(market_data), num_shots, replace=False)
        samples = [market_data.iloc[i] for i in indices]
        
        return samples
    
    def _fine_tune_on_samples(self, model, samples, adaptation_steps):
        """
        Fine-tune model on few-shot samples
        
        Parameters:
        -----------
        model : RL model
            Model to fine-tune
        samples : list
            Few-shot samples
        adaptation_steps : int
            Number of adaptation steps
        """
        # Implementation depends on model type and samples format
        # This is a placeholder that needs to be customized based on the RL framework
        pass
    
    def create_market_prototype(self, market_name, model):
        """
        Create prototype for a market
        
        Parameters:
        -----------
        market_name : str
            Market name
        model : RL model
            Model trained on the market
        """
        if not self.use_prototypes:
            return
        
        # Extract prototype parameters from model
        prototype = self._extract_prototype_from_model(model)
        
        # Store prototype
        self.market_prototypes[market_name] = prototype
    
    def _extract_prototype_from_model(self, model):
        prototype = {}
        try:
            # For models with a policy network
            if hasattr(model, 'policy') and hasattr(model.policy, 'named_parameters'):
                params = model.policy.named_parameters()
                # اگر generator است، به لیست تبدیل کن
                if not isinstance(params, list):
                    params = list(params)
                for name, param in params:
                    if hasattr(param, 'data'):
                        prototype[name] = param.data.clone().detach().numpy()
                    elif isinstance(param, torch.Tensor):
                        prototype[name] = param.clone().detach().numpy()
        except Exception as e:
            logger.error(f"Error extracting prototype: {e}")
        return prototype
    
    def record_performance(self, market_name, performance):
        """
        Record model performance on a market
        
        Parameters:
        -----------
        market_name : str
            Market name
        performance : float
            Performance metric
        """
        self.market_performance[market_name].append(performance)
    
    def plot_market_similarity(self, market_names=None):
        """
        Plot market similarity matrix
        
        Parameters:
        -----------
        market_names : List[str], optional
            List of market names to include
            
        Returns:
        --------
        matplotlib.figure.Figure
            Similarity matrix plot
        """
        if not self.similarity_matrix:
            # اگر similarity_matrix خالی است، برای همه جفت بازارها similarity را محاسبه کن
            all_names = list(self.market_embeddings.keys())
            for i in range(len(all_names)):
                for j in range(len(all_names)):
                    if i != j:
                        self.compute_market_similarity(all_names[i], all_names[j])
        if not self.similarity_matrix:
            logger.warning("No similarity data available")
            return None
        if market_names is None:
            market_names = list(self.market_embeddings.keys())
        n = len(market_names)
        similarity = np.zeros((n, n))
        for i, market1 in enumerate(market_names):
            for j, market2 in enumerate(market_names):
                if i == j:
                    similarity[i, j] = 1.0
                else:
                    sim = self.compute_market_similarity(market1, market2)
                    similarity[i, j] = sim
        plt.figure(figsize=(10, 8))
        plt.imshow(similarity, cmap='viridis', vmin=0, vmax=1)
        plt.colorbar(label='Similarity')
        plt.xticks(range(n), market_names, rotation=90)
        plt.yticks(range(n), market_names)
        plt.title('Market Similarity Matrix')
        plt.tight_layout()
        return plt.gcf()
    
    def plot_performance_transfer(self, source_markets, target_markets):
        """
        Plot performance transfer from source to target markets
        
        Parameters:
        -----------
        source_markets : List[str]
            Source market names
        target_markets : List[str]
            Target market names
            
        Returns:
        --------
        matplotlib.figure.Figure
            Performance transfer plot
        """
        if not self.market_performance:
            logger.warning("No performance data available")
            return None
        
        # Filter markets with performance data
        source_markets = [m for m in source_markets if m in self.market_performance]
        target_markets = [m for m in target_markets if m in self.market_performance]
        
        if not source_markets or not target_markets:
            logger.warning("No performance data for selected markets")
            return None
        
        # Create plot
        plt.figure(figsize=(12, 6))
        
        # Plot source market performance
        for i, market in enumerate(source_markets):
            perf = self.market_performance[market]
            plt.plot(perf, 'o-', label=f"Source: {market}")
        
        # Plot target market performance
        for i, market in enumerate(target_markets):
            perf = self.market_performance[market]
            plt.plot(perf, 's--', label=f"Target: {market}")
        
        plt.title('Performance Transfer')
        plt.xlabel('Training Steps')
        plt.ylabel('Performance')
        plt.legend()
        plt.grid(True)
        
        return plt.gcf()
    
    def save(self, path):
        """
        Save zero-shot learning model
        
        Parameters:
        -----------
        path : str
            Save path
        """
        # Create directory if needed
        os.makedirs(path, exist_ok=True)
        
        # Save market embeddings
        embeddings = {k: v.tolist() if isinstance(v, np.ndarray) else v for k, v in self.market_embeddings.items()}
        
        # Save prototypes (convert numpy arrays to lists)
        prototypes = {}
        for market, prototype in self.market_prototypes.items():
            prototypes[market] = {k: v.tolist() if isinstance(v, np.ndarray) else v for k, v in prototype.items()}
        
        # Save configuration
        config = {
            'embedding_dim': self.embedding_dim,
            'similarity_threshold': self.similarity_threshold,
            'use_market_embeddings': self.use_market_embeddings,
            'use_meta_learning': self.use_meta_learning,
            'use_prototypes': self.use_prototypes,
            'adaptation_lr': self.adaptation_lr,
            'market_embeddings': embeddings,
            'market_prototypes': prototypes,
            'similarity_matrix': self.similarity_matrix,
            'market_performance': {k: v for k, v in self.market_performance.items()},
            'timestamp': datetime.now().isoformat()
        }
        
        with open(os.path.join(path, 'zero_shot_config.json'), 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        # Save neural networks
        if self.use_market_embeddings:
            torch.save(self.market_embedding_network.state_dict(), os.path.join(path, 'market_embedding_network.pth'))
            torch.save(self.policy_adapter.state_dict(), os.path.join(path, 'policy_adapter.pth'))
        
        # Save base model if available
        if self.base_model is not None:
            self.base_model.save(os.path.join(path, 'base_model.zip'))
        
        logger.info(f"Zero-shot learning model saved to {path}")
    
    @classmethod
    def load(cls, path, base_model=None):
        """
        Load zero-shot learning model
        
        Parameters:
        -----------
        path : str
            Load path
        base_model : RL model, optional
            Base model to use
            
        Returns:
        --------
        ZeroShotLearning
            Loaded zero-shot learning model
        """
        config_path = os.path.join(path, 'zero_shot_config.json')
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Zero-shot config not found at {config_path}")
        
        # Load configuration
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Create new instance
        zero_shot = cls(
            base_model=base_model,
            embedding_dim=config.get('embedding_dim', 64),
            similarity_threshold=config.get('similarity_threshold', 0.7),
            use_market_embeddings=config.get('use_market_embeddings', True),
            use_meta_learning=config.get('use_meta_learning', True),
            use_prototypes=config.get('use_prototypes', True),
            adaptation_lr=config.get('adaptation_lr', 0.001)
        )
        
        # Load market embeddings
        embeddings = config.get('market_embeddings', {})
        zero_shot.market_embeddings = {k: np.array(v) for k, v in embeddings.items()}
        
        # Load prototypes
        prototypes = config.get('market_prototypes', {})
        zero_shot.market_prototypes = {}
        for market, prototype in prototypes.items():
            zero_shot.market_prototypes[market] = {k: np.array(v) for k, v in prototype.items()}
        
        # Load similarity matrix
        zero_shot.similarity_matrix = config.get('similarity_matrix', {})
        
        # Load market performance
        zero_shot.market_performance = defaultdict(list)
        for k, v in config.get('market_performance', {}).items():
            zero_shot.market_performance[k] = v
        
        # Load neural networks
        if zero_shot.use_market_embeddings:
            embedding_path = os.path.join(path, 'market_embedding_network.pth')
            adapter_path = os.path.join(path, 'policy_adapter.pth')
            
            if os.path.exists(embedding_path) and os.path.exists(adapter_path):
                zero_shot.market_embedding_network.load_state_dict(torch.load(embedding_path))
                zero_shot.policy_adapter.load_state_dict(torch.load(adapter_path))
        
        # Load base model if not provided
        if base_model is None:
            base_model_path = os.path.join(path, 'base_model.zip')
            if os.path.exists(base_model_path):
                # Create model factory
                model_factory = RLModelFactory()
                # Load base model (environment is needed, placeholder for now)
                zero_shot.base_model = model_factory.load_checkpoint('ppo', None, base_model_path)
        
        logger.info(f"Zero-shot learning model loaded from {path}")
        return zero_shot 