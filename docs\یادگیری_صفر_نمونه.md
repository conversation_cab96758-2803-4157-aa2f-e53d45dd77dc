# مستند جامع: ZeroShotLearning

## مسئولیت
یادگیری صفر نمونه و کم نمونه برای معاملات در بازارهای جدید با استفاده از embedding بازار، meta-learning و انتقال دانش.

## پارامترها
- base_model: مدل پایه برای انتقال
- embedding_dim: بعد embedding بازار
- similarity_threshold: آستانه شباهت بازارها
- use_market_embeddings: استفاده از embedding بازار
- use_meta_learning: استفاده از meta-learning
- use_prototypes: استفاده از prototype-based learning

## متدهای کلیدی
- compute_market_embedding: محاسبه embedding بازار
- compute_market_similarity: محاسبه شباهت بازارها
- find_similar_markets: یافتن بازارهای مشابه
- adapt_to_new_market: تطبیق با بازار جدید
- create_market_prototype: ایجاد prototype بازار

## نمونه کد
```python
from models.zero_shot_learning import ZeroShotLearning
zsl = ZeroShotLearning(base_model=model, embedding_dim=64)
adapted_model = zsl.adapt_to_new_market(market_data, 'NEWMARKET', num_shots=0)
```

## مدیریت خطا
در صورت نبود بازار مشابه، از مدل پایه استفاده می‌شود.

## بهترین شیوه
- ابتدا embedding بازارهای مختلف را محاسبه کنید.
- برای بازارهای جدید، شباهت را بررسی کنید.

## نمودار
- نمودار شباهت بازارها و انتقال عملکرد قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول در تست‌ها و مثال‌ها استفاده شده اما در جریان اصلی معاملات فعال نیست.

## وضعیت عملیاتی
⚠️ فقط در سطح تست و توسعه - نیاز به اتصال به سیستم اصلی دارد. 