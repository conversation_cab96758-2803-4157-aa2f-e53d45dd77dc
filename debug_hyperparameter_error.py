#!/usr/bin/env python3
"""
🔍 Debug script to find the exact source of hyperparameter_suggestions error
"""

import pandas as pd
import numpy as np
import sys
import os
import traceback

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_multi_brain_analysis():
    """Debug Multi-Brain analysis step by step"""
    print("🔍 DEBUGGING MULTI-BRAIN ANALYSIS")
    print("=" * 50)
    
    # Create sample data
    data = pd.DataFrame({
        'close': np.random.uniform(1.1000, 1.1100, 100),
        'volume': np.random.randint(1000, 10000, 100),
        'rsi': np.random.uniform(20, 80, 100),
        'macd': np.random.uniform(-0.01, 0.01, 100)
    })
    
    print(f"📊 Sample data created: {len(data)} rows")
    
    try:
        from fixed_ultimate_main import MultiBrainSystem
        print("✅ MultiBrainSystem imported successfully")
        
        # Initialize system
        multi_brain = MultiBrainSystem()
        print("✅ MultiBrainSystem initialized")
        
        # Test each brain individually
        print("\n🧠 Testing individual brains...")
        
        # Test Optuna Brain
        if multi_brain.optuna_brain:
            print("\n🎯 Testing Optuna Brain...")
            try:
                optuna_result = multi_brain.optuna_brain.suggest_hyperparameters("LSTM", data)
                print(f"✅ Optuna result: {type(optuna_result)}")
                print(f"   Keys: {list(optuna_result.keys()) if isinstance(optuna_result, dict) else 'Not a dict'}")
                if isinstance(optuna_result, dict) and 'error' in optuna_result:
                    print(f"   ⚠️ Error: {optuna_result['error']}")
            except Exception as e:
                print(f"❌ Optuna Brain error: {e}")
                traceback.print_exc()
        
        # Test H2O Brain
        if multi_brain.h2o_brain:
            print("\n📊 Testing H2O Brain...")
            try:
                h2o_result = multi_brain.h2o_brain.analyze_data_patterns(data)
                print(f"✅ H2O result: {type(h2o_result)}")
                print(f"   Keys: {list(h2o_result.keys()) if isinstance(h2o_result, dict) else 'Not a dict'}")
                if isinstance(h2o_result, dict) and 'error' in h2o_result:
                    print(f"   ⚠️ Error: {h2o_result['error']}")
            except Exception as e:
                print(f"❌ H2O Brain error: {e}")
                traceback.print_exc()
        
        # Test full analysis
        print("\n🔍 Testing full analysis...")
        try:
            analysis_results = multi_brain.analyze_training_situation(data, "LSTM", "EURUSD")
            print(f"✅ Full analysis completed")
            print(f"   Result type: {type(analysis_results)}")
            print(f"   Keys: {list(analysis_results.keys()) if isinstance(analysis_results, dict) else 'Not a dict'}")
            
            # Check for the problematic key
            if 'hyperparameter_suggestions' in analysis_results:
                hps = analysis_results['hyperparameter_suggestions']
                print(f"   ✅ hyperparameter_suggestions found: {type(hps)}")
                if isinstance(hps, dict):
                    print(f"      Content: {hps}")
            else:
                print(f"   ❌ hyperparameter_suggestions NOT found")
                
            return True
            
        except Exception as e:
            print(f"❌ Full analysis error: {e}")
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ Import/initialization error: {e}")
        traceback.print_exc()
        return False

def debug_supervisor_analysis():
    """Debug MLflow Supervisor analysis"""
    print("\n🎯 DEBUGGING MLFLOW SUPERVISOR")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import MLflowSupervisor
        
        supervisor = MLflowSupervisor()
        print("✅ MLflow Supervisor initialized")
        
        # Create mock brain results
        mock_brain_results = {
            'optuna': {
                'learning_rate': 0.001,
                'batch_size': 32,
                'reasoning': 'Optuna optimization'
            },
            'h2o': {
                'data_quality': {'completeness': 0.95},
                'reasoning': 'H2O analysis'
            },
            'hyperparameter_suggestions': {
                'learning_rate': 0.001,
                'batch_size': 32
            },
            'data_insights': {
                'quality': 0.95
            }
        }
        
        print("📊 Mock brain results created")
        
        # Test supervisor analysis
        try:
            decision = supervisor.supervise_multi_brain_analysis(mock_brain_results, "LSTM", "EURUSD")
            print(f"✅ Supervisor analysis completed")
            print(f"   Decision type: {type(decision)}")
            print(f"   Keys: {list(decision.keys()) if isinstance(decision, dict) else 'Not a dict'}")
            return True
            
        except Exception as e:
            print(f"❌ Supervisor analysis error: {e}")
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ Supervisor error: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 HYPERPARAMETER_SUGGESTIONS ERROR DEBUG")
    print("=" * 60)
    
    # Test 1: Multi-Brain analysis
    test1 = debug_multi_brain_analysis()
    
    # Test 2: Supervisor analysis
    test2 = debug_supervisor_analysis()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 DEBUG SUMMARY")
    print("=" * 60)
    print(f"🧠 Multi-Brain Analysis: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"🎯 Supervisor Analysis: {'✅ PASSED' if test2 else '❌ FAILED'}")
    
    if test1 and test2:
        print("\n🎉 All tests passed! The error might be elsewhere.")
    else:
        print("\n⚠️ Found issues. Check the output above for details.")
