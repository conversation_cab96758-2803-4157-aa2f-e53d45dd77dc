#!/usr/bin/env python3
"""
🎯 نصب PyCaret در Google Colab
"""

def install_pycaret_in_colab():
    """نصب PyCaret در Google Colab"""
    print("🎯 INSTALLING PYCARET IN GOOGLE COLAB")
    print("=" * 50)
    
    try:
        import subprocess
        import sys
        
        # نصب PyCaret
        print("📦 Installing PyCaret...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            'pycaret[full]', '--quiet'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PyCaret installed successfully!")
        else:
            print(f"⚠️ PyCaret installation warning: {result.stderr}")
            # Try basic installation
            print("📦 Trying basic PyCaret installation...")
            result2 = subprocess.run([
                sys.executable, '-m', 'pip', 'install', 
                'pycaret', '--quiet'
            ], capture_output=True, text=True)
            
            if result2.returncode == 0:
                print("✅ Basic PyCaret installed successfully!")
            else:
                print(f"❌ PyCaret installation failed: {result2.stderr}")
                return False
        
        # تست import
        print("🔍 Testing PyCaret import...")
        try:
            import pycaret
            print("✅ PyCaret import successful!")
            print(f"📊 PyCaret version: {pycaret.__version__}")
            return True
        except ImportError as e:
            print(f"❌ PyCaret import failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Installation failed: {e}")
        return False

def update_pycaret_availability():
    """به‌روزرسانی وضعیت PyCaret در سیستم"""
    print("\n🔄 UPDATING PYCARET AVAILABILITY")
    print("=" * 50)
    
    try:
        # بررسی دوباره وضعیت PyCaret
        try:
            import pycaret
            print("✅ PyCaret is now available!")
            
            # به‌روزرسانی متغیر PYCARET_AVAILABLE
            import fixed_ultimate_main
            fixed_ultimate_main.PYCARET_AVAILABLE = True
            print("✅ PYCARET_AVAILABLE updated to True")
            
            return True
        except ImportError:
            print("❌ PyCaret still not available")
            return False
            
    except Exception as e:
        print(f"❌ Update failed: {e}")
        return False

if __name__ == "__main__":
    print("🎯 PYCARET INSTALLATION FOR GOOGLE COLAB")
    print("=" * 60)
    
    # نصب PyCaret
    install_success = install_pycaret_in_colab()
    
    if install_success:
        # به‌روزرسانی وضعیت
        update_success = update_pycaret_availability()
        
        if update_success:
            print("\n🎉 PYCARET READY!")
            print("=" * 60)
            print("✅ PyCaret successfully installed and configured")
            print("🎯 PyCaret Brain is now available in Multi-Brain System")
            print("🚀 Ready to run ultimate_market_domination_training()")
        else:
            print("\n⚠️ Installation successful but update failed")
    else:
        print("\n❌ PyCaret installation failed")
        print("💡 The system will continue with other brains available")
