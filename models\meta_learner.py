import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from typing import Dict, List, Tuple, Any, Optional, Union
import os
import json
import pickle
from datetime import datetime
import torch

class MetaLearner:
    """
    کلاس MetaLearner برای انتخاب خودکار بهترین الگوریتم RL در هر بازار و شرایط.
    این کلاس از تکنیک‌های meta-learning استفاده می‌کند تا بهترین مدل را برای هر بازار و شرایط انتخاب کند.
    
    ویژگی‌های اصلی:
    1. ثبت و تحلیل عملکرد مدل‌های مختلف در بازارها و شرایط متفاوت
    2. استفاده از الگوریتم‌های انتخاب خودکار برای یافتن بهترین مدل
    3. تطبیق پارامترهای مدل بر اساس شرایط بازار
    """
    
    def __init__(self, history_file: str = "meta_history.pkl", model_factory=None):
        """
        مقداردهی اولیه MetaLearner
        
        پارامترها:
        -----------
        history_file : str
            مسیر فایل برای ذخیره تاریخچه عملکرد مدل‌ها
        model_factory : RLModelFactory
            نمونه‌ای از RLModelFactory برای ایجاد مدل‌ها
        """
        self.history_file = history_file
        self.model_factory = model_factory
        self.history = self._load_history()
        self.market_features = {}  # ویژگی‌های هر بازار
        self.model_performances = {}  # عملکرد هر مدل در هر بازار
        
    def _load_history(self) -> Dict:
        """بارگذاری تاریخچه از فایل"""
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                print(f"خطا در بارگذاری تاریخچه: {e}")
                return {'models': {}, 'markets': {}, 'performances': []}
        return {'models': {}, 'markets': {}, 'performances': []}
    
    def _save_history(self):
        """ذخیره تاریخچه در فایل"""
        with open(self.history_file, 'wb') as f:
            pickle.dump(self.history, f)
    
    def extract_market_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        استخراج ویژگی‌های بازار برای تصمیم‌گیری
        
        پارامترها:
        -----------
        df : pd.DataFrame
            دیتافریم داده‌های بازار
            
        خروجی:
        -------
        Dict[str, float]
            دیکشنری ویژگی‌های بازار
        """
        features = {}
        
        # ویژگی‌های آماری
        features['volatility'] = df['close'].pct_change().std() * np.sqrt(252)
        features['trend'] = df['close'].iloc[-1] / df['close'].iloc[0] - 1
        features['mean_volume'] = df['volume'].mean() if 'volume' in df.columns else 0
        
        # ویژگی‌های فنی
        if len(df) >= 20:
            features['ma_ratio'] = df['close'].iloc[-1] / df['close'].rolling(20).mean().iloc[-1]
        else:
            features['ma_ratio'] = 1.0
            
        # تشخیص رژیم بازار (ساده)
        if features['volatility'] > 0.2:
            features['regime'] = 'high_volatility'
        elif features['trend'] > 0.1:
            features['regime'] = 'bullish'
        elif features['trend'] < -0.1:
            features['regime'] = 'bearish'
        else:
            features['regime'] = 'sideways'
            
        return features
    
    def record_performance(self, model_type: str, symbol: str, timeframe: str, 
                          features: Dict[str, float], metrics: Dict[str, float]):
        """
        ثبت عملکرد مدل برای یادگیری
        
        پارامترها:
        -----------
        model_type : str
            نوع مدل RL (مثلاً PPO، A2C و...)
        symbol : str
            نماد بازار
        timeframe : str
            تایم‌فریم
        features : Dict[str, float]
            ویژگی‌های بازار
        metrics : Dict[str, float]
            معیارهای عملکرد مدل (مثلاً total_reward، sharpe_ratio و...)
        """
        # ثبت در تاریخچه
        performance = {
            'model_type': model_type,
            'symbol': symbol,
            'timeframe': timeframe,
            'features': features,
            'metrics': metrics,
            'timestamp': datetime.now().isoformat()
        }
        
        self.history['performances'].append(performance)
        
        # به‌روزرسانی اطلاعات مدل
        if model_type not in self.history['models']:
            self.history['models'][model_type] = {
                'count': 0,
                'avg_reward': 0,
                'avg_sharpe': 0
            }
        
        model_info = self.history['models'][model_type]
        model_info['count'] += 1
        model_info['avg_reward'] = (model_info['avg_reward'] * (model_info['count'] - 1) + 
                                   metrics.get('total_reward', 0)) / model_info['count']
        model_info['avg_sharpe'] = (model_info['avg_sharpe'] * (model_info['count'] - 1) + 
                                   metrics.get('sharpe_ratio', 0)) / model_info['count']
        
        # به‌روزرسانی اطلاعات بازار
        market_key = f"{symbol}_{timeframe}"
        if market_key not in self.history['markets']:
            self.history['markets'][market_key] = {
                'features': features,
                'best_model': None,
                'best_reward': float('-inf')
            }
        
        if metrics.get('total_reward', float('-inf')) > self.history['markets'][market_key]['best_reward']:
            self.history['markets'][market_key]['best_model'] = model_type
            self.history['markets'][market_key]['best_reward'] = metrics.get('total_reward', 0)
        
        self._save_history()
    
    def select_best_model(self, symbol: str, timeframe: str, df: pd.DataFrame) -> str:
        """
        انتخاب بهترین مدل برای بازار و شرایط خاص
        
        پارامترها:
        -----------
        symbol : str
            نماد بازار
        timeframe : str
            تایم‌فریم
        df : pd.DataFrame
            دیتافریم داده‌های بازار
            
        خروجی:
        -------
        str
            نام بهترین مدل RL برای این بازار و شرایط
        """
        # استخراج ویژگی‌های بازار
        features = self.extract_market_features(df)
        
        # بررسی تاریخچه برای این بازار
        market_key = f"{symbol}_{timeframe}"
        if market_key in self.history['markets']:
            # اگر این بازار را قبلاً دیده‌ایم
            return self.history['markets'][market_key]['best_model'] or 'ppo'
        
        # اگر این بازار جدید است، بررسی بازارهای مشابه
        similar_markets = self._find_similar_markets(features)
        if similar_markets:
            # میانگین وزنی مدل‌های موفق در بازارهای مشابه
            model_scores = {}
            for sim_market, similarity in similar_markets:
                best_model = self.history['markets'][sim_market]['best_model']
                if best_model:
                    model_scores[best_model] = model_scores.get(best_model, 0) + similarity
            
            if model_scores:
                return max(model_scores.items(), key=lambda x: x[1])[0]
        
        # اگر هیچ بازار مشابهی پیدا نشد، انتخاب بر اساس رژیم بازار
        regime = features.get('regime', 'sideways')
        if regime == 'high_volatility':
            return 'sac'  # SAC برای بازارهای پرنوسان
        elif regime == 'bullish':
            return 'ppo'  # PPO برای بازارهای صعودی
        elif regime == 'bearish':
            return 'td3'  # TD3 برای بازارهای نزولی
        else:
            return 'ppo'  # PPO به عنوان انتخاب پیش‌فرض
    
    def _find_similar_markets(self, features: Dict[str, float]) -> List[Tuple[str, float]]:
        """
        یافتن بازارهای مشابه بر اساس ویژگی‌ها
        
        پارامترها:
        -----------
        features : Dict[str, float]
            ویژگی‌های بازار فعلی
            
        خروجی:
        -------
        List[Tuple[str, float]]
            لیستی از (نام_بازار، میزان_شباهت)
        """
        similarities = []
        for market_key, market_info in self.history['markets'].items():
            market_features = market_info.get('features', {})
            if not market_features:
                continue
                
            # محاسبه فاصله اقلیدسی بین ویژگی‌های عددی
            numeric_features = ['volatility', 'trend', 'mean_volume', 'ma_ratio']
            distance = 0
            for feat in numeric_features:
                if feat in features and feat in market_features:
                    distance += (features[feat] - market_features[feat]) ** 2
            
            # محاسبه شباهت (معکوس فاصله)
            similarity = 1 / (1 + np.sqrt(distance))
            
            # افزایش شباهت اگر رژیم بازار یکسان است
            if features.get('regime') == market_features.get('regime'):
                similarity *= 1.5
                
            similarities.append((market_key, similarity))
        
        # مرتب‌سازی بر اساس شباهت (نزولی) و انتخاب 3 بازار مشابه
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:3]
    
    def optimize_hyperparameters(self, model_type: str, features: Dict[str, float]) -> Dict[str, Any]:
        """
        بهینه‌سازی هایپرپارامترها بر اساس ویژگی‌های بازار
        
        پارامترها:
        -----------
        model_type : str
            نوع مدل RL
        features : Dict[str, float]
            ویژگی‌های بازار
            
        خروجی:
        -------
        Dict[str, Any]
            دیکشنری پارامترهای بهینه
        """
        params = {}
        
        # تنظیم learning_rate بر اساس نوسان بازار
        volatility = features.get('volatility', 0.2)
        if volatility > 0.3:
            params['learning_rate'] = 0.0001  # نرخ یادگیری کمتر برای بازارهای پرنوسان
        elif volatility < 0.1:
            params['learning_rate'] = 0.001   # نرخ یادگیری بیشتر برای بازارهای کم‌نوسان
        else:
            params['learning_rate'] = 0.0003  # نرخ یادگیری متوسط
        
        # تنظیم gamma بر اساس روند بازار
        trend = features.get('trend', 0)
        if abs(trend) > 0.2:
            params['gamma'] = 0.98  # ارزش بیشتر برای پاداش‌های آینده در بازارهای با روند قوی
        else:
            params['gamma'] = 0.99  # ارزش متوسط برای پاداش‌های آینده
        
        # تنظیمات خاص هر مدل
        if model_type == 'ppo':
            params['n_steps'] = 2048 if volatility > 0.2 else 1024
            params['batch_size'] = 64
            params['n_epochs'] = 10
            params['ent_coef'] = 0.01 if volatility > 0.2 else 0.0
        
        elif model_type == 'sac':
            params['buffer_size'] = 100000
            params['batch_size'] = 256
            params['tau'] = 0.005
            params['ent_coef'] = 'auto'
            
        elif model_type == 'td3':
            params['buffer_size'] = 100000
            params['batch_size'] = 100
            params['tau'] = 0.005
            
        elif model_type == 'a2c':
            params['n_steps'] = 5
            params['ent_coef'] = 0.01 if volatility > 0.2 else 0.0
            
        return params
    
    def get_model_insights(self, symbol: str = None, timeframe: str = None) -> Dict[str, Any]:
        """
        دریافت تحلیل‌ها و بینش‌ها درباره عملکرد مدل‌ها
        
        پارامترها:
        -----------
        symbol : str, optional
            نماد بازار (اگر None باشد، همه بازارها بررسی می‌شوند)
        timeframe : str, optional
            تایم‌فریم (اگر None باشد، همه تایم‌فریم‌ها بررسی می‌شوند)
            
        خروجی:
        -------
        Dict[str, Any]
            دیکشنری تحلیل‌ها و بینش‌ها
        """
        insights = {
            'best_models_by_market': {},
            'model_performance_summary': {},
            'market_regime_analysis': {
                'high_volatility': {'best_model': None, 'count': 0},
                'bullish': {'best_model': None, 'count': 0},
                'bearish': {'best_model': None, 'count': 0},
                'sideways': {'best_model': None, 'count': 0}
            }
        }
        
        # فیلتر کردن داده‌ها بر اساس symbol و timeframe
        filtered_performances = []
        for perf in self.history['performances']:
            if (symbol is None or perf['symbol'] == symbol) and \
               (timeframe is None or perf['timeframe'] == timeframe):
                filtered_performances.append(perf)
        
        # تحلیل بهترین مدل‌ها برای هر بازار
        market_models = {}
        for perf in filtered_performances:
            market_key = f"{perf['symbol']}_{perf['timeframe']}"
            if market_key not in market_models:
                market_models[market_key] = []
            market_models[market_key].append({
                'model_type': perf['model_type'],
                'reward': perf.get('metrics', {}).get('total_reward', 0),
                'sharpe': perf.get('metrics', {}).get('sharpe_ratio', 0)
            })
        
        for market_key, models in market_models.items():
            if models:
                best_model = max(models, key=lambda x: x['reward'])
                insights['best_models_by_market'][market_key] = {
                    'model_type': best_model['model_type'],
                    'reward': best_model['reward'],
                    'sharpe': best_model['sharpe']
                }
        
        # تحلیل عملکرد کلی مدل‌ها
        model_performances = {}
        for perf in filtered_performances:
            model_type = perf['model_type']
            if model_type not in model_performances:
                model_performances[model_type] = {
                    'count': 0,
                    'total_reward': 0,
                    'total_sharpe': 0,
                    'win_count': 0  # تعداد دفعاتی که این مدل بهترین بوده
                }
            
            mp = model_performances[model_type]
            mp['count'] += 1
            mp['total_reward'] += perf.get('metrics', {}).get('total_reward', 0)
            mp['total_sharpe'] += perf.get('metrics', {}).get('sharpe_ratio', 0)
        
        # محاسبه تعداد برد هر مدل
        for market_key, best in insights['best_models_by_market'].items():
            model_type = best['model_type']
            if model_type in model_performances:
                model_performances[model_type]['win_count'] += 1
        
        # محاسبه میانگین‌ها
        for model_type, mp in model_performances.items():
            if mp['count'] > 0:
                insights['model_performance_summary'][model_type] = {
                    'count': mp['count'],
                    'avg_reward': mp['total_reward'] / mp['count'],
                    'avg_sharpe': mp['total_sharpe'] / mp['count'],
                    'win_rate': mp['win_count'] / mp['count'] if mp['count'] > 0 else 0
                }
        
        # تحلیل رژیم بازار
        regime_models = {
            'high_volatility': {},
            'bullish': {},
            'bearish': {},
            'sideways': {}
        }
        
        for perf in filtered_performances:
            regime = perf.get('features', {}).get('regime', 'sideways')
            model_type = perf['model_type']
            reward = perf.get('metrics', {}).get('total_reward', 0)
            
            if regime not in regime_models:
                continue
                
            if model_type not in regime_models[regime]:
                regime_models[regime][model_type] = {
                    'count': 0,
                    'total_reward': 0
                }
            
            regime_models[regime][model_type]['count'] += 1
            regime_models[regime][model_type]['total_reward'] += reward
            insights['market_regime_analysis'][regime]['count'] += 1
        
        # یافتن بهترین مدل برای هر رژیم
        for regime, models in regime_models.items():
            best_model = None
            best_avg_reward = float('-inf')
            
            for model_type, stats in models.items():
                if stats['count'] > 0:
                    avg_reward = stats['total_reward'] / stats['count']
                    if avg_reward > best_avg_reward:
                        best_avg_reward = avg_reward
                        best_model = model_type
            
            insights['market_regime_analysis'][regime]['best_model'] = best_model
        
        return insights
