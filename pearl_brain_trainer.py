"""
🧠 Pearl-3x7B Brain Trainer
مربی مغز متفکر Pearl-3x7B

این سیستم شامل:
- 🧠 مغز متفکر برای تصمیم‌گیری هوشمند
- 🎯 آموزش تطبیقی
- 📊 نظارت پیشرفته
- 🚀 بهینه‌سازی خودکار
"""

import os
import sys
import time
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from training.train_rl import RLTrainingConfig, EnhancedPearlRLTrainer

@dataclass
class BrainDecision:
    """تصمیم مغز متفکر"""
    action: str
    confidence: float
    reasoning: str
    parameters: Dict[str, Any]
    expected_outcome: str

class PearlBrain:
    """🧠 مغز متفکر Pearl-3x7B"""
    
    def __init__(self):
        self.experience = []
        self.performance_history = []
        self.learning_rate = 0.1
        self.confidence_threshold = 0.7
        
    def analyze_situation(self, metrics: Dict[str, Any]) -> BrainDecision:
        """تحلیل وضعیت و تصمیم‌گیری هوشمند"""
        
        # تحلیل عملکرد فعلی
        current_performance = self._evaluate_performance(metrics)
        
        # تحلیل روند
        trend = self._analyze_trend()
        
        # تصمیم‌گیری بر اساس تحلیل
        if current_performance < 0.3:
            return self._decide_major_adjustment(metrics)
        elif current_performance < 0.6:
            return self._decide_minor_adjustment(metrics)
        elif trend == "declining":
            return self._decide_preventive_action(metrics)
        else:
            return self._decide_optimization(metrics)
    
    def _evaluate_performance(self, metrics: Dict[str, Any]) -> float:
        """ارزیابی عملکرد فعلی"""
        score = 0.0
        
        # ارزیابی reward
        if 'avg_reward' in metrics:
            reward_score = min(max(metrics['avg_reward'] + 1, 0), 2) / 2
            score += reward_score * 0.4
        
        # ارزیابی stability
        if 'reward_std' in metrics:
            stability_score = max(0, 1 - metrics['reward_std'])
            score += stability_score * 0.3
        
        # ارزیابی learning progress
        if len(self.performance_history) > 5:
            recent_avg = sum(self.performance_history[-5:]) / 5
            older_avg = sum(self.performance_history[-10:-5]) / 5 if len(self.performance_history) >= 10 else recent_avg
            progress = (recent_avg - older_avg) / max(abs(older_avg), 0.1)
            progress_score = min(max(progress + 0.5, 0), 1)
            score += progress_score * 0.3
        
        return min(score, 1.0)
    
    def _analyze_trend(self) -> str:
        """تحلیل روند عملکرد"""
        if len(self.performance_history) < 3:
            return "insufficient_data"
        
        recent = self.performance_history[-3:]
        if recent[-1] > recent[0] * 1.1:
            return "improving"
        elif recent[-1] < recent[0] * 0.9:
            return "declining"
        else:
            return "stable"
    
    def _decide_major_adjustment(self, metrics: Dict[str, Any]) -> BrainDecision:
        """تصمیم برای تغییر عمده"""
        return BrainDecision(
            action="major_adjustment",
            confidence=0.9,
            reasoning="عملکرد ضعیف - نیاز به تغییر عمده در پارامترها",
            parameters={
                "learning_rate": 0.001,
                "epsilon": 0.3,
                "batch_size": 32,
                "buffer_size": 50000
            },
            expected_outcome="بهبود قابل توجه در عملکرد"
        )
    
    def _decide_minor_adjustment(self, metrics: Dict[str, Any]) -> BrainDecision:
        """تصمیم برای تغییر جزئی"""
        return BrainDecision(
            action="minor_adjustment",
            confidence=0.7,
            reasoning="عملکرد متوسط - تنظیم جزئی پارامترها",
            parameters={
                "learning_rate": 0.0005,
                "epsilon": 0.2,
                "sentiment_weight": 0.15
            },
            expected_outcome="بهبود تدریجی در عملکرد"
        )
    
    def _decide_preventive_action(self, metrics: Dict[str, Any]) -> BrainDecision:
        """تصمیم برای اقدام پیشگیرانه"""
        return BrainDecision(
            action="preventive_action",
            confidence=0.8,
            reasoning="روند نزولی شناسایی شد - اقدام پیشگیرانه",
            parameters={
                "exploration_boost": True,
                "curiosity_weight": 0.2,
                "replay_priority": 0.8
            },
            expected_outcome="جلوگیری از کاهش بیشتر عملکرد"
        )
    
    def _decide_optimization(self, metrics: Dict[str, Any]) -> BrainDecision:
        """تصمیم برای بهینه‌سازی"""
        return BrainDecision(
            action="optimization",
            confidence=0.6,
            reasoning="عملکرد خوب - بهینه‌سازی برای حداکثر کارایی",
            parameters={
                "fine_tune": True,
                "advanced_features": True,
                "multi_symbol_training": True
            },
            expected_outcome="دستیابی به عملکرد بهینه"
        )
    
    def learn_from_outcome(self, decision: BrainDecision, actual_outcome: Dict[str, Any]):
        """یادگیری از نتایج"""
        self.experience.append({
            "decision": decision,
            "outcome": actual_outcome,
            "timestamp": datetime.now().isoformat()
        })
        
        # تنظیم learning rate بر اساس نتایج
        if actual_outcome.get('success', False):
            self.learning_rate *= 1.05  # افزایش اعتماد
        else:
            self.learning_rate *= 0.95  # کاهش اعتماد
        
        self.learning_rate = max(0.01, min(0.3, self.learning_rate))

class SmartTrainingSession:
    """🎯 جلسه آموزش هوشمند"""
    
    def __init__(self, config: RLTrainingConfig):
        self.config = config
        self.brain = PearlBrain()
        self.trainer = None
        self.session_metrics = []
        self.start_time = None
        
    def initialize(self):
        """راه‌اندازی جلسه آموزش"""
        print("🧠 Initializing Pearl-3x7B Brain Training Session...")
        
        # ایجاد trainer
        self.trainer = EnhancedPearlRLTrainer(self.config)
        
        # آماده‌سازی محیط
        self.trainer.prepare_environment()
        
        # ایجاد agent
        self.trainer.agent = self.trainer.create_agent()
        
        if self.trainer.agent is None:
            raise ValueError("Failed to create agent")
        
        print(f"✅ Brain training session initialized for {self.config.model_name}")
        print(f"🎯 Target: {self.config.num_episodes} episodes")
        print(f"🧠 Brain confidence threshold: {self.brain.confidence_threshold}")
        
    def run_intelligent_training(self):
        """اجرای آموزش هوشمند"""
        print("\n🚀 Starting Intelligent Training with Pearl-3x7B Brain...")
        self.start_time = time.time()
        
        for episode in range(self.config.num_episodes):
            print(f"\n🧠 Episode {episode + 1}/{self.config.num_episodes}")
            
            # اجرای episode
            episode_metrics = self.trainer.train_episode_enhanced(episode)
            self.session_metrics.append(episode_metrics)
            
            # تحلیل توسط مغز
            if episode > 0 and episode % 5 == 0:  # هر 5 episode
                self._brain_analysis(episode)
            
            # گزارش پیشرفت
            self._report_progress(episode, episode_metrics)
        
        # خلاصه نهایی
        self._final_summary()
    
    def _brain_analysis(self, episode: int):
        """تحلیل توسط مغز متفکر"""
        print(f"\n🧠 Brain Analysis at Episode {episode + 1}...")
        
        # محاسبه metrics کلی
        recent_metrics = self.session_metrics[-5:]
        avg_reward = sum(m['total_reward'] for m in recent_metrics) / len(recent_metrics)
        reward_std = (sum((m['total_reward'] - avg_reward) ** 2 for m in recent_metrics) / len(recent_metrics)) ** 0.5
        
        analysis_metrics = {
            'avg_reward': avg_reward,
            'reward_std': reward_std,
            'episode': episode,
            'total_episodes': self.config.num_episodes
        }
        
        # تصمیم‌گیری توسط مغز
        decision = self.brain.analyze_situation(analysis_metrics)
        
        print(f"🎯 Brain Decision: {decision.action}")
        print(f"🔍 Reasoning: {decision.reasoning}")
        print(f"💪 Confidence: {decision.confidence:.2f}")
        print(f"🎲 Expected: {decision.expected_outcome}")
        
        # اعمال تصمیم
        self._apply_brain_decision(decision)
        
        # ذخیره عملکرد برای یادگیری
        self.brain.performance_history.append(avg_reward)
    
    def _apply_brain_decision(self, decision: BrainDecision):
        """اعمال تصمیم مغز"""
        if decision.confidence < self.brain.confidence_threshold:
            print("⚠️ Brain confidence too low - skipping adjustment")
            return
        
        print(f"🔧 Applying brain decision: {decision.action}")
        
        # اعمال پارامترهای جدید
        for param, value in decision.parameters.items():
            if hasattr(self.config, param):
                setattr(self.config, param, value)
                print(f"  📝 {param} = {value}")
        
        # اعمال تغییرات خاص
        if decision.action == "major_adjustment":
            self._major_adjustment()
        elif decision.action == "minor_adjustment":
            self._minor_adjustment()
        elif decision.action == "preventive_action":
            self._preventive_action()
        elif decision.action == "optimization":
            self._optimization()
    
    def _major_adjustment(self):
        """تغییر عمده"""
        print("🔄 Applying major adjustments...")
        # Reset agent with new parameters
        self.trainer.agent = self.trainer.create_agent()
    
    def _minor_adjustment(self):
        """تغییر جزئی"""
        print("🔧 Applying minor adjustments...")
        # Fine-tune existing agent
        pass
    
    def _preventive_action(self):
        """اقدام پیشگیرانه"""
        print("🛡️ Applying preventive actions...")
        # Boost exploration
        pass
    
    def _optimization(self):
        """بهینه‌سازی"""
        print("⚡ Applying optimizations...")
        # Enable advanced features
        pass
    
    def _report_progress(self, episode: int, metrics: Dict[str, Any]):
        """گزارش پیشرفت"""
        reward = metrics['total_reward']
        steps = metrics['steps']
        symbol = metrics['symbol']
        
        print(f"  📊 Reward: {reward:.3f} | Steps: {steps} | Symbol: {symbol}")
        
        if 'avg_sentiment' in metrics:
            print(f"  🤗 Sentiment: {metrics['avg_sentiment']:.3f}")
        
        if 'buffer_size' in metrics:
            print(f"  💾 Buffer: {metrics['buffer_size']} experiences")
    
    def _final_summary(self):
        """خلاصه نهایی"""
        total_time = time.time() - self.start_time
        
        print(f"\n🎉 Pearl-3x7B Brain Training Completed!")
        print(f"⏱️ Total Time: {total_time:.1f}s")
        print(f"📊 Episodes: {len(self.session_metrics)}")
        
        if self.session_metrics:
            avg_reward = sum(m['total_reward'] for m in self.session_metrics) / len(self.session_metrics)
            best_reward = max(m['total_reward'] for m in self.session_metrics)
            print(f"🏆 Average Reward: {avg_reward:.3f}")
            print(f"🥇 Best Reward: {best_reward:.3f}")
        
        print(f"🧠 Brain Experience: {len(self.brain.experience)} decisions")
        print(f"📈 Performance History: {len(self.brain.performance_history)} records")
        
        # ذخیره نتایج
        self._save_results()
    
    def _save_results(self):
        """ذخیره نتایج"""
        results = {
            "config": self.config.__dict__,
            "metrics": self.session_metrics,
            "brain_experience": self.brain.experience,
            "performance_history": self.brain.performance_history,
            "timestamp": datetime.now().isoformat()
        }
        
        filename = f"pearl_brain_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"💾 Results saved to: {filename}")

def create_brain_training_config() -> RLTrainingConfig:
    """ایجاد config برای آموزش با مغز متفکر"""
    return RLTrainingConfig(
        model_name="Pearl_3x7B_Brain_Agent",
        algorithm="dqn",
        num_episodes=50,  # آموزش متوسط
        max_steps_per_episode=100,
        symbols=["EURUSD", "GBPUSD", "USDJPY"],
        symbol_rotation_strategy="adaptive",
        episodes_per_symbol=15,
        
        # Enhanced features
        dynamic_rewards=True,
        universal_features=True,
        enhanced_replay=True,
        prioritized_replay=True,
        multi_step_learning=3,
        curiosity_driven=True,
        
        # Sentiment analysis
        use_sentiment_analysis=True,
        sentiment_weight=0.1,
        
        # Learning parameters
        learning_rate=0.001,
        epsilon_start=0.3,
        epsilon_end=0.05,
        epsilon_decay=0.995,
        
        # Memory
        buffer_size=100000,
        batch_size=64,
        
        # Performance
        target_update_frequency=1000,
        save_frequency=10
    )

def main():
    """🧠 اجرای آموزش با مغز متفکر Pearl-3x7B"""
    print("🧠 PEARL-3X7B BRAIN TRAINER")
    print("=" * 60)
    
    try:
        # ایجاد config
        config = create_brain_training_config()
        
        # ایجاد جلسه آموزش هوشمند
        session = SmartTrainingSession(config)
        
        # راه‌اندازی
        session.initialize()
        
        # اجرای آموزش هوشمند
        session.run_intelligent_training()
        
        print("\n🎉 Brain training completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Brain training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
