#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import logging
from datetime import datetime
import traceback

# تنظیم مسیر پروژه
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from training.train_sentiment import SentimentTrainer
from training.train_timeseries import TimeSeriesTrainer
from training.train_rl import RLTrainer
from evaluation.model_comparator import ModelComparator

class PipelineDebugger:
    def __init__(self, log_dir='logs'):
        # ایجاد پوشه لاگ اگر وجود نداشته باشد
        self.log_dir = os.path.join(project_root, log_dir)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # تنظیم لاگر
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.log_dir, f'debug_pipeline_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # لیست مدل‌ها برای دیباگ
        self.models = {
            'sentiment': SentimentTrainer,
            'timeseries': TimeSeriesTrainer,
            'rl': RLTrainer
        }
        
    def run_comprehensive_debug(self):
        """اجرای دیباگ جامع برای تمام مدل‌ها"""
        debug_results = {
            'timestamp': datetime.now().isoformat(),
            'models': {}
        }
        
        for model_name, ModelClass in self.models.items():
            try:
                self.logger.info(f"Starting debug for {model_name} model")
                
                # ایجاد نمونه مدل
                model = ModelClass()
                
                # آماده‌سازی داده
                data_prep_success = model.prepare_data()
                
                # آموزش مدل
                train_success = model.train_model() if data_prep_success else False
                
                # ارزیابی مدل
                evaluation_results = model.evaluate_model() if train_success else None
                
                # ذخیره‌سازی مدل
                model_save_path = model.save_model()
                
                # ثبت نتایج
                debug_results['models'][model_name] = {
                    'data_preparation': data_prep_success,
                    'training': train_success,
                    'evaluation': evaluation_results,
                    'model_path': model_save_path
                }
                
                self.logger.info(f"Debug completed for {model_name} model")
                
            except Exception as e:
                self.logger.error(f"Error in {model_name} model debug: {e}")
                debug_results['models'][model_name] = {
                    'error': str(e),
                    'traceback': traceback.format_exc()
                }
        
        # مقایسه مدل‌ها
        try:
            comparator = ModelComparator()
            comparison_results = comparator.compare_models()
            debug_results['model_comparison'] = comparison_results
        except Exception as e:
            self.logger.error(f"Error in model comparison: {e}")
            debug_results['model_comparison'] = {
                'error': str(e),
                'traceback': traceback.format_exc()
            }
        
        # ذخیره‌سازی نتایج دیباگ
        debug_output_path = os.path.join(self.log_dir, f'debug_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(debug_output_path, 'w', encoding='utf-8') as f:
            json.dump(debug_results, f, ensure_ascii=False, indent=4)
        
        self.logger.info(f"Debug results saved to {debug_output_path}")
        
        return debug_results

def main():
    debugger = PipelineDebugger()
    debugger.run_comprehensive_debug()

if __name__ == '__main__':
    main() 