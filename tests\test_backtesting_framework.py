#!/usr/bin/env python3
"""
🧪 Advanced Tests for Backtesting Framework
تست‌های پیشرفته برای فریم‌ورک backtesting
"""

import sys
import os
import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.backtesting_framework import (
    BacktestingFramework, 
    BacktestConfig, 
    BacktestResults,
    TradingStrategy,
    run_quick_backtest
)

class TestBacktestingFramework:
    """تست‌های فریم‌ورک backtesting"""
    
    def setup_method(self):
        """راه‌اندازی قبل از هر تست"""
        self.config = BacktestConfig(
            start_date="2023-01-01",
            end_date="2023-12-31",
            initial_capital=10000.0,
            symbol="EURUSD",
            timeframe="H1"
        )
        self.framework = BacktestingFramework(self.config)
    
    def test_config_initialization(self):
        """تست راه‌اندازی تنظیمات"""
        assert self.config.initial_capital == 10000.0
        assert self.config.commission == 0.001
        assert self.config.symbol == "EURUSD"
        assert self.config.timeframe == "H1"
        
    def test_framework_initialization(self):
        """تست راه‌اندازی فریم‌ورک"""
        assert self.framework.config is not None
        assert self.framework.logger is not None
        assert self.framework.cerebro is None  # Should be None before run
        
    def test_prepare_data_with_mock(self):
        """تست آماده‌سازی داده‌ها با mock"""
        # Create mock data
        mock_data = pd.DataFrame({
            'Date': pd.date_range('2023-01-01', periods=100, freq='H'),
            'Open': np.random.uniform(1.0, 1.2, 100),
            'High': np.random.uniform(1.0, 1.2, 100),
            'Low': np.random.uniform(1.0, 1.2, 100),
            'Close': np.random.uniform(1.0, 1.2, 100),
            'Volume': np.random.randint(100, 1000, 100)
        })
        
        # Mock file existence and reading
        with patch('pathlib.Path.exists', return_value=True):
            with patch('pandas.read_csv', return_value=mock_data):
                data = self.framework.prepare_data()
                
                assert data is not None
                assert len(data) > 0
                assert 'Open' in data.columns
                assert 'High' in data.columns
                assert 'Low' in data.columns
                assert 'Close' in data.columns
    
    def test_backtest_results_initialization(self):
        """تست راه‌اندازی نتایج"""
        results = BacktestResults()
        
        assert results.total_return == 0.0
        assert results.total_trades == 0
        assert results.win_rate == 0.0
        assert results.trade_history == []
        assert results.daily_returns == []
        assert results.portfolio_value == []
    
    def test_trading_strategy_initialization(self):
        """تست راه‌اندازی استراتژی"""
        # This is a mock test since we can't easily test strategy without full backtest
        import backtrader as bt
        
        # Create a simple mock cerebro for testing
        cerebro = bt.Cerebro()
        
        # Test strategy parameters
        strategy_params = {
            'sma_fast': 20,
            'sma_slow': 50,
            'rsi_period': 14,
            'stop_loss': 0.02,
            'take_profit': 0.04,
        }
        
        assert strategy_params['sma_fast'] < strategy_params['sma_slow']
        assert strategy_params['stop_loss'] < strategy_params['take_profit']
        assert strategy_params['rsi_period'] > 0
    
    def test_run_quick_backtest_function(self):
        """تست تابع backtesting سریع"""
        # Mock the data preparation to avoid file dependency
        mock_data = pd.DataFrame({
            'Date': pd.date_range('2023-01-01', periods=100, freq='H'),
            'Open': np.random.uniform(1.0, 1.2, 100),
            'High': np.random.uniform(1.0, 1.2, 100),
            'Low': np.random.uniform(1.0, 1.2, 100),
            'Close': np.random.uniform(1.0, 1.2, 100),
            'Volume': np.random.randint(100, 1000, 100)
        })
        
        with patch('pathlib.Path.exists', return_value=True):
            with patch('pandas.read_csv', return_value=mock_data):
                # This should not crash
                config = BacktestConfig(
                    start_date="2023-01-01",
                    end_date="2023-01-31",  # Short period
                    initial_capital=1000.0
                )
                framework = BacktestingFramework(config)
                
                # Test that framework can be initialized
                assert framework is not None
                assert framework.config.initial_capital == 1000.0
    
    def test_save_results(self):
        """تست ذخیره نتایج"""
        results = BacktestResults(
            total_return=0.15,
            annual_return=0.12,
            max_drawdown=0.08,
            sharpe_ratio=1.5,
            total_trades=50,
            winning_trades=30,
            losing_trades=20,
            win_rate=0.6
        )
        
        # Mock file operations
        with patch('pathlib.Path.mkdir'):
            with patch('builtins.open', create=True):
                with patch('json.dump'):
                    filepath = self.framework.save_results(results)
                    assert filepath is not None
                    assert "backtest_results" in filepath
    
    def test_get_performance_report(self):
        """تست گزارش عملکرد"""
        # Set mock results
        self.framework.results = BacktestResults(
            total_return=0.15,
            annual_return=0.12,
            max_drawdown=0.08,
            sharpe_ratio=1.5,
            total_trades=50,
            winning_trades=30,
            losing_trades=20,
            win_rate=0.6
        )
        
        report = self.framework.get_performance_report()
        
        assert report is not None
        assert "summary" in report
        assert "trades" in report
        assert "config" in report
        assert "15.00%" in report["summary"]["total_return"]
        assert report["summary"]["total_trades"] == 50
    
    def test_position_sizing(self):
        """تست محاسبه اندازه موقعیت"""
        # Test position sizing logic
        initial_capital = 10000.0
        position_size_pct = 0.1  # 10%
        current_price = 1.1000
        
        expected_position_value = initial_capital * position_size_pct
        expected_quantity = expected_position_value / current_price
        
        assert expected_position_value == 1000.0
        assert abs(expected_quantity - 909.09) < 0.1  # Approximate
    
    def test_risk_management_parameters(self):
        """تست پارامترهای مدیریت ریسک"""
        stop_loss = 0.02  # 2%
        take_profit = 0.04  # 4%
        
        # Risk/reward ratio should be 2:1
        risk_reward_ratio = take_profit / stop_loss
        assert risk_reward_ratio == 2.0
        
        # Test profit/loss calculation
        entry_price = 1.1000
        stop_loss_price = entry_price * (1 - stop_loss)
        take_profit_price = entry_price * (1 + take_profit)
        
        assert abs(stop_loss_price - 1.0780) < 0.001
        assert abs(take_profit_price - 1.1440) < 0.001
    
    def test_trade_analysis_metrics(self):
        """تست معیارهای تحلیل معاملات"""
        # Mock trade data
        winning_trades = 30
        losing_trades = 20
        total_trades = winning_trades + losing_trades
        
        win_rate = winning_trades / total_trades
        assert win_rate == 0.6
        
        avg_win = 50.0
        avg_loss = 30.0
        profit_factor = avg_win / avg_loss
        
        assert profit_factor == 50.0 / 30.0
        assert abs(profit_factor - 1.67) < 0.01

class TestAdvancedBacktestingMetrics:
    """تست‌های پیشرفته معیارهای backtesting"""
    
    def test_sharpe_ratio_calculation(self):
        """تست محاسبه نسبت شارپ"""
        # Mock returns
        returns = [0.01, 0.02, -0.01, 0.03, 0.01, -0.02, 0.02]
        risk_free_rate = 0.02  # 2% annual
        
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return > 0:
            sharpe_ratio = (mean_return - risk_free_rate/252) / std_return
        else:
            sharpe_ratio = 0
        
        assert sharpe_ratio is not None
        assert isinstance(sharpe_ratio, float)
    
    def test_max_drawdown_calculation(self):
        """تست محاسبه حداکثر افت"""
        # Mock portfolio values
        portfolio_values = [10000, 10500, 10200, 9800, 9500, 10100, 11000]
        
        # Calculate running maximum
        running_max = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - running_max) / running_max
        max_drawdown = np.min(drawdown)
        
        assert max_drawdown <= 0  # Drawdown should be negative
        assert abs(max_drawdown - (-0.095)) < 0.001  # Approximately 9.5%
    
    def test_annual_return_calculation(self):
        """تست محاسبه بازده سالانه"""
        initial_value = 10000.0
        final_value = 11500.0
        days = 365
        
        total_return = (final_value - initial_value) / initial_value
        annual_return = (1 + total_return) ** (365/days) - 1
        
        assert annual_return > 0
        assert abs(annual_return - 0.15) < 0.001  # 15% return

def run_advanced_backtesting_tests():
    """اجرای تست‌های پیشرفته"""
    print("🧪 Running Advanced Backtesting Tests...")
    print("=" * 50)
    
    # Test 1: Framework initialization
    try:
        framework = BacktestingFramework()
        assert framework is not None
        print("✅ Framework initialization - PASSED")
    except Exception as e:
        print(f"❌ Framework initialization - FAILED: {e}")
        return False
    
    # Test 2: Config validation
    try:
        config = BacktestConfig(
            initial_capital=5000.0,
            commission=0.002,
            stop_loss=0.01,
            take_profit=0.03
        )
        assert config.initial_capital == 5000.0
        assert config.commission == 0.002
        assert config.stop_loss < config.take_profit
        print("✅ Config validation - PASSED")
    except Exception as e:
        print(f"❌ Config validation - FAILED: {e}")
        return False
    
    # Test 3: Results structure
    try:
        results = BacktestResults()
        assert hasattr(results, 'total_return')
        assert hasattr(results, 'sharpe_ratio')
        assert hasattr(results, 'max_drawdown')
        assert hasattr(results, 'trade_history')
        print("✅ Results structure - PASSED")
    except Exception as e:
        print(f"❌ Results structure - FAILED: {e}")
        return False
    
    # Test 4: Risk management logic
    try:
        stop_loss = 0.02
        take_profit = 0.04
        risk_reward = take_profit / stop_loss
        assert risk_reward == 2.0
        print("✅ Risk management logic - PASSED")
    except Exception as e:
        print(f"❌ Risk management logic - FAILED: {e}")
        return False
    
    print("\n🎯 All advanced tests completed!")
    return True

if __name__ == "__main__":
    """اجرای مستقل تست‌ها"""
    success = run_advanced_backtesting_tests()
    if success:
        print("\n🎉 All backtesting tests passed!")
    else:
        print("\n❌ Some tests failed!")
    
    exit(0 if success else 1) 