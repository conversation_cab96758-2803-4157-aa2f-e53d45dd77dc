{"command": "python utils/advanced_rl_agent.py", "timestamp": "2025-07-08T05:51:57.938504", "execution_time": 1.263535976409912, "return_code": 1, "stdout": "Advanced Reinforcement Learning Trading System\n============================================================\n", "stderr": "INFO:__main__:Q-Learning Agent initialized with state_size=7, action_size=3\nINFO:__main__:Advanced RL Agent initialized\nWARNING:__main__:Model loading not applicable for AdvancedRLAgent. Model file: rl_model.pkl\nWARNING:__main__:Could not load replay buffer: replay_buffer.pkl, error: No module named 'utils'\nINFO:__main__:Advanced RL Trading System initialized\nTraceback (most recent call last):\n  File \"D:\\project\\utils\\advanced_rl_agent.py\", line 732, in <module>\n    main() \n  File \"D:\\project\\utils\\advanced_rl_agent.py\", line 674, in main\n    print(\"\\U0001f4ca Sample Market Data:\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f4ca' in position 0: character maps to <undefined>\n", "success": false}