# مستند جامع: AdaptiveMarginControl و MarginRiskModel

## مسئولیت
تنظیم هوشمند حاشیه معاملات با یادگیری عمیق و تحلیل نوسان بازار.

## پارامترها
- base_margin_rates: نرخ پایه حاشیه
- volatility_window: پنجره محاسبه نوسان
- max/min_margin_multiplier: محدوده ضریب حاشیه
- learning_rate: نرخ یادگیری مدل
- use_ml_model: فعال‌سازی مدل یادگیری ماشین

## متدهای کلیدی
- update_price_history: بروزرسانی قیمت
- update_position: بروزرسانی پوزیشن
- calculate_volatility: محاسبه نوسان
- calculate_position_risk: محاسبه ریسک پوزیشن
- calculate_margin_requirement: محاسبه نیاز حاشیه
- explain_margin_decision: توضیح تصمیم حاشیه

## نمونه کد
```python
from utils.adaptive_margin_control import AdaptiveMarginControl
amc = AdaptiveMarginControl()
amc.update_price_history('EURUSD', 1.1)
margin_info = amc.calculate_margin_requirement('EURUSD', price=1.1, position_size=10000)
```

## مدیریت خطا
در صورت نبود داده کافی، مقدار پیش‌فرض یا خطا برمی‌گرداند.

## بهترین شیوه
همیشه قبل از باز کردن پوزیشن، margin requirement را چک کنید.

## نمودار
- نمودار تغییرات margin multiplier و volatility قابل ترسیم است.

## اتصال به اسکریپت اصلی
- فقط در تست‌ها استفاده شده و در جریان اصلی معاملات یا unified_trading_system فعلاً فراخوانی نشده است.
- پیشنهاد: برای بهره‌برداری کامل، باید در سیستم معاملاتی یکپارچه یا محیط معاملاتی فراخوانی شود. 