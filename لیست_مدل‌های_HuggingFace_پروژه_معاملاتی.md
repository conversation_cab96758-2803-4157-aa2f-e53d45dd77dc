# 🤖 **لیست جامع مدل‌های Hugging Face برای پروژه معاملاتی**

## 📋 **فهرست کلی بر اساس ماژول‌های پروژه**

### 🎯 **1. مدل‌های کنترل هسته و مدیریت ربات**

#### **🔥 مدل‌های اولویت بالا:**

**1. HedgeAgents (arxiv:2502.13165)**
- **توضیح**: سیستم multi-agent هوشمند برای معاملات با hedging strategies
- **ویژگی‌ها**: fund manager + multiple hedging experts، 70% بازده سالانه، 400% بازده کل
- **استفاده**: کنترل کلی سیستم معاملاتی، تصمیم‌گیری هوشمند
- **وضعیت**: پذیرفته شده در WWW 2025

**2. TradingAgents (arxiv:2412.20138)**
- **توضیح**: فریمورک multi-agent LLM برای معاملات مالی
- **ویژگی‌ها**: fundamental analysts, sentiment analysts, technical analysts, traders
- **استفاده**: هماهنگی بین ماژول‌های مختلف، تحلیل جامع
- **دسترسی**: [GitHub](https://github.com/TauricResearch)

**3. MASS: Multi-Agent Simulation Scaling (arxiv:2505.10278)**
- **توضیح**: مقیاس‌بندی simulation multi-agent برای ساخت پورتفولیو
- **ویژگی‌ها**: بازده مداوم، بهینه‌سازی توزیع agent ها
- **استفاده**: شبیه‌سازی بزرگ مقیاس، ساخت پورتفولیو
- **کد**: [منبع باز](https://github.com/EA-Labs)

### 🛠️ **2. مدل‌های بهینه‌سازی پارامترها و AutoML**

#### **🚀 ابزارهای کلیدی:**

**1. AIAlpha (GitHub: dgwarlug47/AIAlpha)**
- **توضیح**: فریمورک AutoML کامل برای استراتژی‌های معاملاتی
- **قابلیت‌ها**: EDA، feature preprocessing، انتخاب مدل، hyperparameter optimization
- **استفاده**: بهینه‌سازی خودکار تمام پارامترهای سیستم
- **وضعیت**: منبع باز، آماده استفاده

**2. DeepScalper (arxiv:2201.09058)**
- **توضیح**: فریمورک DRL برای معاملات intraday با risk-aware optimization
- **ویژگی‌ها**: hindsight reward، volatility prediction، encoder-decoder architecture
- **استفاده**: بهینه‌سازی معاملات کوتاه‌مدت، مدیریت ریسک
- **نتایج**: بهبود قابل توجه در Sharpe ratio و maximum drawdown

### 📊 **3. مدل‌های پیش‌بینی قیمت و Time Series**

#### **⭐ مدل‌های برتر:**

**1. Chronos Models (Amazon)**
- `amazon/chronos-t5-large` (710M parameters) ⭐⭐⭐⭐⭐
- `amazon/chronos-t5-small` (46M parameters)
- `amazon/chronos-t5-tiny` (8M parameters)
- **ویژگی**: پیش‌بینی time series با tokenization، zero-shot capability
- **استفاده**: جایگزین یا تقویت‌کننده Plutus، پیش‌بینی قیمت

**2. TimesFM (Google)**
- `google/timesfm-2.0-500m-pytorch` (500M parameters)
- `google/timesfm-1.0-200m` 
- **ویژگی**: مدل foundation برای time series forecasting
- **استفاده**: پیش‌بینی طولانی‌مدت، تحلیل روند

**3. FinGPT Forecaster**
- `FinGPT/fingpt-forecaster_dow30_llama2-7b_lora`
- **ویژگی**: LoRA adapter روی Llama-2 برای پیش‌بینی DOW30
- **استفاده**: پیش‌بینی قیمت سهام با context بیشتر
- **Downloads**: 1,742/ماه، 23 Spaces

**4. Moirai Models (Salesforce)**
- `Salesforce/moirai-1.1-R-large` (300M parameters)
- `Salesforce/moirai-1.0-R-base`
- **ویژگی**: مدل‌های چندمقیاسه برای time series
- **استفاده**: پیش‌بینی چندگانه، تحلیل multi-horizon

### 📰 **4. مدل‌های تحلیل احساسات مالی**

#### **🎯 مدل‌های تخصصی:**

**1. DistilRoBERTa Financial Sentiment**
- `mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis`
- **دقت**: 98.23% روی financial_phrasebank
- **Downloads**: 241,257/ماه، 86 Spaces
- **استفاده**: تحلیل اخبار مالی، sentiment trading signals

**2. Financial Sentiment Analysis Papers**
- "Backtesting Sentiment Signals" (arxiv:2507.03350)
- **نتایج**: 50.63% بازده در 28 ماه با regression model
- **استفاده**: ساخت signals مبتنی بر sentiment

### 🔄 **5. مدل‌های Regime Detection و تشخیص الگو**

#### **📈 ابزارهای تحلیل بازار:**

**1. Market Regime Detector (GitHub: Melique/Market-Regime-Detector)**
- **روش**: Hidden Markov Model برای تشخیص Bull/Bear states
- **استفاده**: تشخیص وضعیت بازار، تنظیم استراتژی

**2. Market Regime Classification (GitHub: mkhamisnab/)**
- **پروژه**: MSc Financial Engineering از WorldQuant University
- **روش**: HMM برای طبقه‌بندی regime states
- **استفاده**: swing trading، moving average strategies

**3. ElliottAgents (arxiv:2507.03435)**
- **توضیح**: سیستم multi-agent برای تحلیل Elliott Wave
- **ویژگی‌ها**: NLP-driven analysis، pattern recognition
- **استفاده**: تشخیص الگوهای پیچیده بازار

### ⚡ **6. مدل‌های Reinforcement Learning**

#### **🎮 مدل‌های موجود در HuggingFace:**

**1. Stable-Baselines3 Models:**
- `Andyrasika/a2c-PandaReachDense-v3` (A2C)
- `delmaksym/ppo-Huggy` (PPO)
- **استفاده**: آموزش agent های RL، تصمیم‌گیری خودکار

**2. Deep RL Leaderboard**
- `huggingface-projects/Deep-Reinforcement-Learning-Leaderboard`
- **استفاده**: مقایسه و انتخاب بهترین مدل‌های RL

### 🛡️ **7. مدل‌های مدیریت ریسک**

#### **🔒 ابزارهای حیاتی:**

**1. RiskEmbed (arxiv:2504.06293)**
- **توضیح**: مدل embedding تخصصی برای risk management
- **دیتاست**: RiskData از 94 guideline های OSFI
- **استفاده**: بازیابی اطلاعات ریسک، RAG systems

**2. FinHEAR (arxiv:2506.09080)**
- **توضیح**: Human Expertise and Adaptive Risk-aware reasoning
- **ویژگی‌ها**: expert-guided retrieval، confidence-adjusted position sizing
- **استفاده**: تصمیم‌گیری مالی با در نظر گیری ریسک

### 🎯 **8. مدل‌های تخصصی و کاربردی**

#### **💼 مدل‌های صنعتی:**

**1. FinCon (arxiv:2407.06567)**
- **توضیح**: LLM multi-agent با conceptual verbal reinforcement
- **ساختار**: manager-analyst hierarchy، risk-control component
- **استفاده**: تصمیم‌گیری مالی پیشرفته، portfolio management

**2. FinArena (arxiv:2503.02692)**
- **توضیح**: Human-Agent collaboration framework
- **ویژگی‌ها**: multimodal data analysis، personalized investment
- **استفاده**: تحلیل بازار، پیش‌بینی روند

**3. Shai (arxiv:2312.14203)**
- **توضیح**: مدل LLM 10B برای asset management
- **ویژگی‌ها**: continuous pre-training، safety assessments
- **استفاده**: مدیریت دارایی، ارزیابی عملکرد

### 🧮 **9. مدل‌های Backtesting و ارزیابی**

#### **📊 ابزارهای تست:**

**1. Backtesting Research Papers:**
- "Backtesting Sentiment Signals" (arxiv:2507.03350)
- "AI for Investment Strategy Backtesting" (متعدد منابع)
- **استفاده**: ارزیابی استراتژی‌ها، اندازه‌گیری عملکرد

**2. Performance Evaluation:**
- CFA Level III LLM Evaluation (arxiv:2507.02954)
- **نتایج**: 79.1% (o4-mini), 77.3% (Gemini 2.5 Flash)
- **استفاده**: سنجش کیفیت تصمیم‌گیری مالی

---

## 🚀 **توصیه‌های پیاده‌سازی بر اساس اولویت**

### **⚡ فاز اول (فوری):**
1. **Chronos Models** → جایگزینی/تقویت Plutus
2. **DistilRoBERTa Sentiment** → تحلیل اخبار
3. **Market Regime Detector** → تشخیص وضعیت بازار

### **🔧 فاز دوم (میان‌مدت):**
1. **TradingAgents Framework** → ساختار multi-agent
2. **AIAlpha AutoML** → بهینه‌سازی پارامترها
3. **RiskEmbed** → مدیریت ریسک پیشرفته

### **🎯 فاز سوم (طولانی‌مدت):**
1. **HedgeAgents** → سیستم کنترل کامل
2. **FinCon** → تصمیم‌گیری هوشمند
3. **MASS** → مقیاس‌بندی سیستم

---

## 💡 **نکات مهم برای استفاده:**

### **✅ مزایای استفاده:**
- **رایگان**: تمام مدل‌ها منبع باز هستند
- **تست شده**: عملکرد اثبات شده در papers
- **جامعه**: پشتیبانی active community
- **انعطاف**: قابلیت تنظیم و سفارشی‌سازی

### **⚠️ نکات احتیاط:**
- برخی مدل‌ها نیاز به fine-tuning دارند
- مدل‌های بزرگ نیاز به GPU قوی دارند  
- باید با داده‌های خودت test کنی
- ترکیب مدل‌ها بهتر از استفاده تکی است

### **🔧 نیازمندی‌های فنی:**
- Python 3.8+
- transformers, torch, huggingface_hub
- GPU برای مدل‌های بزرگ (>1B parameters)
- حداقل 16GB RAM برای مدل‌های متوسط

این لیست کامل رو داری و می‌تونی بر اساس نیازهات شروع کنی! [[memory:2518263]] 