"""
🔍 Pearl-3x7B Data Finder
ابزار پیدا کردن و بررسی دیتاهای شما
"""

import os
import pandas as pd

def find_your_data():
    """پیدا کردن دیتاهای شما در Google Drive"""
    print("🔍 SEARCHING FOR YOUR DATA FILES")
    print("=" * 50)
    
    # Mount Google Drive
    try:
        from google.colab import drive
        drive.mount('/content/drive')
        print("✅ Google Drive mounted")
    except:
        print("⚠️ Not in Colab or Drive already mounted")
    
    # Search paths
    search_paths = [
        "/content/drive/MyDrive/project2/data_new",
        "/content/drive/MyDrive/project2/data",
        "/content/drive/MyDrive/project2",
        "/content/drive/MyDrive/data_new",
        "/content/drive/MyDrive/data",
        "/content/drive/MyDrive"
    ]
    
    found_data = []
    
    for search_path in search_paths:
        print(f"\n🔍 Checking: {search_path}")
        
        if not os.path.exists(search_path):
            print("   ❌ Path not found")
            continue
        
        print("   ✅ Path exists")
        
        try:
            files = os.listdir(search_path)
            data_files = []
            
            for file in files:
                file_path = os.path.join(search_path, file)
                
                if os.path.isfile(file_path):
                    file_size = os.path.getsize(file_path)
                    
                    if file.endswith(('.csv', '.pkl', '.parquet', '.json', '.xlsx', '.xls')):
                        data_files.append({
                            'file': file,
                            'path': file_path,
                            'size': file_size,
                            'type': file.split('.')[-1].upper()
                        })
                        print(f"   📄 {file} ({file_size:,} bytes) - {file.split('.')[-1].upper()}")
                    else:
                        print(f"   📄 {file} ({file_size:,} bytes)")
                else:
                    print(f"   📁 {file}/")
            
            if data_files:
                found_data.extend(data_files)
                print(f"   ✅ Found {len(data_files)} data files")
            else:
                print("   ⚠️ No data files found")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Summary
    print(f"\n📊 SEARCH SUMMARY")
    print("=" * 30)
    
    if found_data:
        print(f"✅ Found {len(found_data)} data files:")
        
        for i, data_file in enumerate(found_data, 1):
            print(f"{i}. {data_file['file']}")
            print(f"   📁 Path: {data_file['path']}")
            print(f"   📊 Size: {data_file['size']:,} bytes")
            print(f"   📋 Type: {data_file['type']}")
            print()
        
        # Test loading the largest file
        largest_file = max(found_data, key=lambda x: x['size'])
        print(f"🔬 TESTING LARGEST FILE: {largest_file['file']}")
        print("=" * 40)
        
        try:
            test_load_file(largest_file['path'], largest_file['type'])
        except Exception as e:
            print(f"❌ Error testing file: {e}")
    
    else:
        print("❌ No data files found!")
        print("\n💡 SUGGESTIONS:")
        print("1. Check if your data files are uploaded to Google Drive")
        print("2. Make sure files are in CSV, PKL, Parquet, JSON, or Excel format")
        print("3. Try uploading files to /content/drive/MyDrive/project2/data_new")
        print("4. Check file permissions")
    
    return found_data

def test_load_file(file_path, file_type):
    """تست بارگذاری فایل"""
    print(f"🔄 Loading {os.path.basename(file_path)}...")
    
    try:
        if file_type.lower() == 'csv':
            df = pd.read_csv(file_path)
        elif file_type.lower() == 'pkl':
            df = pd.read_pickle(file_path)
        elif file_type.lower() == 'parquet':
            df = pd.read_parquet(file_path)
        elif file_type.lower() == 'json':
            df = pd.read_json(file_path)
        elif file_type.lower() in ['xlsx', 'xls']:
            df = pd.read_excel(file_path)
        else:
            print(f"⚠️ Unsupported file type: {file_type}")
            return
        
        print(f"✅ Successfully loaded!")
        print(f"   📊 Shape: {df.shape}")
        print(f"   📋 Columns: {len(df.columns)}")
        
        # Show first few columns
        print(f"   📊 Column names: {list(df.columns[:10])}")
        if len(df.columns) > 10:
            print(f"   📊 ... and {len(df.columns) - 10} more columns")
        
        # Show data types
        numeric_cols = df.select_dtypes(include=['number']).columns
        text_cols = df.select_dtypes(include=['object']).columns
        date_cols = df.select_dtypes(include=['datetime']).columns
        
        print(f"   🔢 Numeric columns: {len(numeric_cols)}")
        print(f"   📝 Text columns: {len(text_cols)}")
        print(f"   📅 Date columns: {len(date_cols)}")
        
        # Look for price-like columns
        price_cols = []
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ['price', 'close', 'value', 'amount']):
                price_cols.append(col)
        
        if price_cols:
            print(f"   💰 Price-like columns: {price_cols}")
        
        # Look for volume-like columns
        volume_cols = []
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ['volume', 'vol', 'quantity', 'qty']):
                volume_cols.append(col)
        
        if volume_cols:
            print(f"   📊 Volume-like columns: {volume_cols}")
        
        # Show sample data
        print(f"\n📋 SAMPLE DATA (first 3 rows):")
        print(df.head(3).to_string())
        
        # Check for missing values
        missing_pct = (df.isnull().sum() / len(df) * 100).round(2)
        high_missing = missing_pct[missing_pct > 10]
        
        if len(high_missing) > 0:
            print(f"\n⚠️ Columns with >10% missing values:")
            for col, pct in high_missing.items():
                print(f"   {col}: {pct}%")
        else:
            print(f"\n✅ Data quality looks good (low missing values)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return False

def create_sample_data():
    """ایجاد دیتای نمونه در صورت عدم وجود دیتا"""
    print("\n🔧 CREATING SAMPLE DATA")
    print("=" * 30)
    
    try:
        import numpy as np
        from datetime import datetime, timedelta
        
        # Create sample trading data
        dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='H')
        n_samples = len(dates)
        
        # Generate realistic price data
        np.random.seed(42)
        price_base = 50000  # Base price
        returns = np.random.normal(0, 0.02, n_samples)  # 2% volatility
        prices = [price_base]
        
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)
        
        # Create DataFrame
        df = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.lognormal(10, 1, n_samples)
        })
        
        # Ensure high >= close >= low and high >= open >= low
        df['high'] = df[['open', 'close', 'high']].max(axis=1)
        df['low'] = df[['open', 'close', 'low']].min(axis=1)
        
        # Create directory
        sample_dir = "/content/drive/MyDrive/project2/data_new"
        os.makedirs(sample_dir, exist_ok=True)
        
        # Save sample data
        sample_file = os.path.join(sample_dir, "sample_trading_data.csv")
        df.to_csv(sample_file, index=False)
        
        print(f"✅ Sample data created!")
        print(f"   📁 Path: {sample_file}")
        print(f"   📊 Shape: {df.shape}")
        print(f"   📅 Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        
        return sample_file
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        return None

# Main execution
if __name__ == "__main__":
    try:
        import google.colab
        print("🚀 Running in Google Colab")
        
        # Find data
        found_data = find_your_data()
        
        # If no data found, offer to create sample data
        if not found_data:
            print("\n" + "="*50)
            response = input("❓ No data found. Create sample data? (y/n): ")
            if response.lower() == 'y':
                create_sample_data()
        
    except ImportError:
        print("⚠️ This script is designed for Google Colab")
        find_your_data()
