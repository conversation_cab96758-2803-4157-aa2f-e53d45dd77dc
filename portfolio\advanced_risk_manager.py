#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 Advanced Risk Management System
سیستم مدیریت ریسک پیشرفته با اهداف سود مشخص
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class RiskLevel(Enum):
    """سطح ریسک"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"

@dataclass
class RiskParameters:
    """پارامترهای ریسک"""
    initial_capital: float = 1000.0  # سرمایه اولیه
    max_drawdown_percent: float = 10.0  # حداکثر ضرر کلی (%)
    daily_loss_limit_percent: float = 4.0  # حداکثر ضرر روزانه (%)
    
    # اهداف سود
    daily_profit_target_per_symbol: float = 5.0  # حداقل سود روزانه هر نماد
    weekly_profit_target: float = 30.0  # حداقل سود هفتگی
    monthly_profit_target: float = 80.0  # حداقل سود ماهانه
    
    # تنظیمات position sizing
    risk_per_trade_percent: float = 2.0  # ریسک هر معامله (%)
    max_positions: int = 5  # حداکثر تعداد موقعیت‌ها
    
    # Stop loss و take profit
    stop_loss_percent: float = 1.5  # Stop loss (%)
    take_profit_percent: float = 3.0  # Take profit (%)
    
    # تنظیمات پیشرفته
    correlation_limit: float = 0.7  # حد همبستگی بین نمادها
    volatility_adjustment: bool = True  # تنظیم بر اساس نوسان
    
    @property
    def max_drawdown_amount(self) -> float:
        """حداکثر ضرر کلی به دلار"""
        return self.initial_capital * (self.max_drawdown_percent / 100)
    
    @property
    def daily_loss_limit_amount(self) -> float:
        """حداکثر ضرر روزانه به دلار"""
        return self.initial_capital * (self.daily_loss_limit_percent / 100)
    
    @property
    def risk_per_trade_amount(self) -> float:
        """ریسک هر معامله به دلار"""
        return self.initial_capital * (self.risk_per_trade_percent / 100)

@dataclass
class Position:
    """موقعیت معاملاتی"""
    symbol: str
    side: str  # "buy" or "sell"
    quantity: float
    entry_price: float
    stop_loss: float
    take_profit: float
    timestamp: datetime
    risk_amount: float
    expected_profit: float
    
    @property
    def current_risk_reward_ratio(self) -> float:
        """نسبت ریسک به سود"""
        if self.risk_amount == 0:
            return 0
        return self.expected_profit / self.risk_amount

@dataclass
class PerformanceMetrics:
    """معیارهای عملکرد"""
    current_capital: float
    total_pnl: float
    daily_pnl: float
    weekly_pnl: float
    monthly_pnl: float
    current_drawdown: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    profitable_trades: int
    avg_profit_per_trade: float
    sharpe_ratio: float
    
    @property
    def current_drawdown_percent(self) -> float:
        """درصد ضرر فعلی"""
        if self.current_capital == 0:
            return 0
        return (self.current_drawdown / self.current_capital) * 100

class AdvancedRiskManager:
    """مدیر ریسک پیشرفته"""
    
    def __init__(self, risk_params: RiskParameters = None):
        self.risk_params = risk_params or RiskParameters()
        self.positions: Dict[str, Position] = {}
        self.daily_pnl_history: List[float] = []
        self.trade_history: List[Dict] = []
        self.performance_metrics = PerformanceMetrics(
            current_capital=self.risk_params.initial_capital,
            total_pnl=0.0,
            daily_pnl=0.0,
            weekly_pnl=0.0,
            monthly_pnl=0.0,
            current_drawdown=0.0,
            max_drawdown=0.0,
            win_rate=0.0,
            total_trades=0,
            profitable_trades=0,
            avg_profit_per_trade=0.0,
            sharpe_ratio=0.0
        )
        self.last_reset_date = datetime.now().date()
        
    def calculate_position_size(self, symbol: str, entry_price: float, 
                              stop_loss_price: float, side: str = "buy") -> Tuple[float, float]:
        """محاسبه اندازه موقعیت"""
        
        # محاسبه ریسک بر اساس stop loss
        if side == "buy":
            risk_per_share = abs(entry_price - stop_loss_price)
        else:  # sell
            risk_per_share = abs(stop_loss_price - entry_price)
        
        if risk_per_share == 0:
            return 0.0, 0.0
        
        # محاسبه تعداد سهام بر اساس ریسک
        max_risk_amount = self.risk_params.risk_per_trade_amount
        quantity = max_risk_amount / risk_per_share
        
        # بررسی محدودیت سرمایه
        position_value = quantity * entry_price
        available_capital = self.performance_metrics.current_capital * 0.8  # 80% سرمایه
        
        if position_value > available_capital:
            quantity = available_capital / entry_price
            risk_amount = quantity * risk_per_share
        else:
            risk_amount = max_risk_amount
        
        return quantity, risk_amount
    
    def calculate_stop_loss_take_profit(self, symbol: str, entry_price: float, 
                                       side: str = "buy") -> Tuple[float, float]:
        """محاسبه stop loss و take profit"""
        
        if side == "buy":
            stop_loss = entry_price * (1 - self.risk_params.stop_loss_percent / 100)
            take_profit = entry_price * (1 + self.risk_params.take_profit_percent / 100)
        else:  # sell
            stop_loss = entry_price * (1 + self.risk_params.stop_loss_percent / 100)
            take_profit = entry_price * (1 - self.risk_params.take_profit_percent / 100)
        
        return stop_loss, take_profit
    
    def check_risk_limits(self, symbol: str = None) -> Dict[str, bool]:
        """بررسی محدودیت‌های ریسک"""
        checks = {
            "max_drawdown_ok": True,
            "daily_loss_ok": True,
            "max_positions_ok": True,
            "capital_sufficient": True,
            "daily_target_reached": False,
            "weekly_target_reached": False,
            "monthly_target_reached": False
        }
        
        # بررسی حداکثر ضرر کلی
        if abs(self.performance_metrics.current_drawdown) >= self.risk_params.max_drawdown_amount:
            checks["max_drawdown_ok"] = False
        
        # بررسی ضرر روزانه
        if abs(self.performance_metrics.daily_pnl) >= self.risk_params.daily_loss_limit_amount:
            checks["daily_loss_ok"] = False
        
        # بررسی تعداد موقعیت‌ها
        if len(self.positions) >= self.risk_params.max_positions:
            checks["max_positions_ok"] = False
        
        # بررسی سرمایه کافی
        if self.performance_metrics.current_capital < self.risk_params.initial_capital * 0.1:
            checks["capital_sufficient"] = False
        
        # بررسی اهداف سود
        if self.performance_metrics.daily_pnl >= self.risk_params.daily_profit_target_per_symbol:
            checks["daily_target_reached"] = True
        
        if self.performance_metrics.weekly_pnl >= self.risk_params.weekly_profit_target:
            checks["weekly_target_reached"] = True
        
        if self.performance_metrics.monthly_pnl >= self.risk_params.monthly_profit_target:
            checks["monthly_target_reached"] = True
        
        return checks
    
    def can_open_position(self, symbol: str, entry_price: float, side: str = "buy") -> Tuple[bool, str]:
        """بررسی امکان باز کردن موقعیت"""
        
        # بررسی موقعیت موجود
        if symbol in self.positions:
            return False, f"Position already exists for {symbol}"
        
        # بررسی محدودیت‌های ریسک
        risk_checks = self.check_risk_limits(symbol)
        
        if not risk_checks["max_drawdown_ok"]:
            return False, "Maximum drawdown limit reached"
        
        if not risk_checks["daily_loss_ok"]:
            return False, "Daily loss limit reached"
        
        if not risk_checks["max_positions_ok"]:
            return False, "Maximum positions limit reached"
        
        if not risk_checks["capital_sufficient"]:
            return False, "Insufficient capital"
        
        # اگر هدف روزانه رسید، فقط موقعیت‌های کم ریسک
        if risk_checks["daily_target_reached"]:
            return False, "Daily target reached - reducing risk"
        
        return True, "Position can be opened"
    
    def open_position(self, symbol: str, entry_price: float, side: str = "buy", 
                     custom_stop_loss: float = None, custom_take_profit: float = None) -> bool:
        """باز کردن موقعیت"""
        
        can_open, reason = self.can_open_position(symbol, entry_price, side)
        if not can_open:
            print(f"❌ Cannot open position for {symbol}: {reason}")
            return False
        
        # محاسبه stop loss و take profit
        if custom_stop_loss and custom_take_profit:
            stop_loss, take_profit = custom_stop_loss, custom_take_profit
        else:
            stop_loss, take_profit = self.calculate_stop_loss_take_profit(symbol, entry_price, side)
        
        # محاسبه اندازه موقعیت
        quantity, risk_amount = self.calculate_position_size(symbol, entry_price, stop_loss, side)
        
        if quantity == 0:
            print(f"❌ Cannot calculate position size for {symbol}")
            return False
        
        # محاسبه سود مورد انتظار
        if side == "buy":
            expected_profit = quantity * (take_profit - entry_price)
        else:
            expected_profit = quantity * (entry_price - take_profit)
        
        # ایجاد موقعیت
        position = Position(
            symbol=symbol,
            side=side,
            quantity=quantity,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            timestamp=datetime.now(),
            risk_amount=risk_amount,
            expected_profit=expected_profit
        )
        
        self.positions[symbol] = position
        
        # بروزرسانی سرمایه
        position_value = quantity * entry_price
        self.performance_metrics.current_capital -= position_value
        
        print(f"✅ Position opened for {symbol}:")
        print(f"   Side: {side}")
        print(f"   Quantity: {quantity:.4f}")
        print(f"   Entry: ${entry_price:.2f}")
        print(f"   Stop Loss: ${stop_loss:.2f}")
        print(f"   Take Profit: ${take_profit:.2f}")
        print(f"   Risk: ${risk_amount:.2f}")
        print(f"   Expected Profit: ${expected_profit:.2f}")
        print(f"   Risk/Reward: {position.current_risk_reward_ratio:.2f}")
        
        return True
    
    def close_position(self, symbol: str, exit_price: float, reason: str = "manual") -> bool:
        """بستن موقعیت"""
        
        if symbol not in self.positions:
            print(f"❌ No position found for {symbol}")
            return False
        
        position = self.positions[symbol]
        
        # محاسبه سود/ضرر
        if position.side == "buy":
            pnl = position.quantity * (exit_price - position.entry_price)
        else:
            pnl = position.quantity * (position.entry_price - exit_price)
        
        # بروزرسانی سرمایه
        position_value = position.quantity * exit_price
        self.performance_metrics.current_capital += position_value
        
        # بروزرسانی PnL
        self.performance_metrics.total_pnl += pnl
        self.performance_metrics.daily_pnl += pnl
        self.performance_metrics.weekly_pnl += pnl
        self.performance_metrics.monthly_pnl += pnl
        
        # بروزرسانی drawdown
        if pnl < 0:
            self.performance_metrics.current_drawdown += abs(pnl)
            if self.performance_metrics.current_drawdown > self.performance_metrics.max_drawdown:
                self.performance_metrics.max_drawdown = self.performance_metrics.current_drawdown
        else:
            self.performance_metrics.current_drawdown = max(0, self.performance_metrics.current_drawdown - pnl)
        
        # بروزرسانی آمار معاملات
        self.performance_metrics.total_trades += 1
        if pnl > 0:
            self.performance_metrics.profitable_trades += 1
        
        # محاسبه win rate
        self.performance_metrics.win_rate = (self.performance_metrics.profitable_trades / 
                                           self.performance_metrics.total_trades * 100)
        
        # محاسبه متوسط سود
        self.performance_metrics.avg_profit_per_trade = (self.performance_metrics.total_pnl / 
                                                       self.performance_metrics.total_trades)
        
        # ذخیره تاریخچه
        trade_record = {
            "symbol": symbol,
            "side": position.side,
            "quantity": position.quantity,
            "entry_price": position.entry_price,
            "exit_price": exit_price,
            "pnl": pnl,
            "pnl_percent": (pnl / (position.quantity * position.entry_price)) * 100,
            "duration": datetime.now() - position.timestamp,
            "reason": reason,
            "timestamp": datetime.now()
        }
        
        self.trade_history.append(trade_record)
        
        # حذف موقعیت
        del self.positions[symbol]
        
        print(f"✅ Position closed for {symbol}:")
        print(f"   Exit Price: ${exit_price:.2f}")
        print(f"   PnL: ${pnl:.2f}")
        print(f"   PnL%: {trade_record['pnl_percent']:.2f}%")
        print(f"   Reason: {reason}")
        print(f"   New Capital: ${self.performance_metrics.current_capital:.2f}")
        
        return True
    
    def update_daily_reset(self):
        """بازنشانی روزانه"""
        today = datetime.now().date()
        
        if today != self.last_reset_date:
            self.daily_pnl_history.append(self.performance_metrics.daily_pnl)
            self.performance_metrics.daily_pnl = 0.0
            self.last_reset_date = today
            
            print(f"📅 Daily reset completed for {today}")
    
    def get_risk_level(self) -> RiskLevel:
        """تعیین سطح ریسک فعلی"""
        
        drawdown_percent = self.performance_metrics.current_drawdown_percent
        
        if drawdown_percent >= 8:
            return RiskLevel.EXTREME
        elif drawdown_percent >= 6:
            return RiskLevel.HIGH
        elif drawdown_percent >= 3:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def get_portfolio_status(self) -> Dict:
        """دریافت وضعیت پرتفولیو"""
        
        risk_checks = self.check_risk_limits()
        risk_level = self.get_risk_level()
        
        # محاسبه سود/ضرر موقعیت‌های باز
        unrealized_pnl = 0.0
        for symbol, position in self.positions.items():
            # فرض می‌کنیم قیمت فعلی برابر قیمت ورود است (برای محاسبه دقیق نیاز به قیمت real-time)
            # در اجرای واقعی، باید قیمت فعلی را دریافت کنیم
            pass
        
        return {
            "capital": {
                "initial": self.risk_params.initial_capital,
                "current": self.performance_metrics.current_capital,
                "total_pnl": self.performance_metrics.total_pnl,
                "daily_pnl": self.performance_metrics.daily_pnl,
                "weekly_pnl": self.performance_metrics.weekly_pnl,
                "monthly_pnl": self.performance_metrics.monthly_pnl,
                "unrealized_pnl": unrealized_pnl
            },
            "risk": {
                "level": risk_level.value,
                "current_drawdown": self.performance_metrics.current_drawdown,
                "current_drawdown_percent": self.performance_metrics.current_drawdown_percent,
                "max_drawdown": self.performance_metrics.max_drawdown,
                "risk_checks": risk_checks
            },
            "positions": {
                "open_positions": len(self.positions),
                "max_positions": self.risk_params.max_positions,
                "positions_detail": {symbol: {
                    "side": pos.side,
                    "quantity": pos.quantity,
                    "entry_price": pos.entry_price,
                    "stop_loss": pos.stop_loss,
                    "take_profit": pos.take_profit,
                    "risk_amount": pos.risk_amount,
                    "expected_profit": pos.expected_profit
                } for symbol, pos in self.positions.items()}
            },
            "performance": {
                "total_trades": self.performance_metrics.total_trades,
                "profitable_trades": self.performance_metrics.profitable_trades,
                "win_rate": self.performance_metrics.win_rate,
                "avg_profit_per_trade": self.performance_metrics.avg_profit_per_trade,
                "sharpe_ratio": self.performance_metrics.sharpe_ratio
            },
            "targets": {
                "daily_target": self.risk_params.daily_profit_target_per_symbol,
                "weekly_target": self.risk_params.weekly_profit_target,
                "monthly_target": self.risk_params.monthly_profit_target,
                "daily_achieved": risk_checks["daily_target_reached"],
                "weekly_achieved": risk_checks["weekly_target_reached"],
                "monthly_achieved": risk_checks["monthly_target_reached"]
            },
            "timestamp": datetime.now()
        }
    
    def save_state(self, filename: str = "risk_manager_state.json"):
        """ذخیره وضعیت"""
        state = {
            "risk_params": {
                "initial_capital": self.risk_params.initial_capital,
                "max_drawdown_percent": self.risk_params.max_drawdown_percent,
                "daily_loss_limit_percent": self.risk_params.daily_loss_limit_percent,
                "daily_profit_target_per_symbol": self.risk_params.daily_profit_target_per_symbol,
                "weekly_profit_target": self.risk_params.weekly_profit_target,
                "monthly_profit_target": self.risk_params.monthly_profit_target
            },
            "performance_metrics": {
                "current_capital": self.performance_metrics.current_capital,
                "total_pnl": self.performance_metrics.total_pnl,
                "daily_pnl": self.performance_metrics.daily_pnl,
                "weekly_pnl": self.performance_metrics.weekly_pnl,
                "monthly_pnl": self.performance_metrics.monthly_pnl,
                "current_drawdown": self.performance_metrics.current_drawdown,
                "max_drawdown": self.performance_metrics.max_drawdown,
                "total_trades": self.performance_metrics.total_trades,
                "profitable_trades": self.performance_metrics.profitable_trades,
                "win_rate": self.performance_metrics.win_rate,
                "avg_profit_per_trade": self.performance_metrics.avg_profit_per_trade
            },
            "positions": {symbol: {
                "side": pos.side,
                "quantity": pos.quantity,
                "entry_price": pos.entry_price,
                "stop_loss": pos.stop_loss,
                "take_profit": pos.take_profit,
                "risk_amount": pos.risk_amount,
                "expected_profit": pos.expected_profit,
                "timestamp": pos.timestamp.isoformat()
            } for symbol, pos in self.positions.items()},
            "trade_history": self.trade_history[-100:],  # آخرین 100 معامله
            "daily_pnl_history": self.daily_pnl_history[-30:],  # آخرین 30 روز
            "timestamp": datetime.now().isoformat()
        }
        
        with open(filename, 'w') as f:
            json.dump(state, f, indent=2, default=str)
        
        print(f"✅ Risk manager state saved to {filename}")

# مثال استفاده
if __name__ == "__main__":
    # تنظیمات ریسک بر اساس درخواست کاربر
    risk_params = RiskParameters(
        initial_capital=1000.0,
        max_drawdown_percent=10.0,
        daily_loss_limit_percent=4.0,
        daily_profit_target_per_symbol=5.0,
        weekly_profit_target=30.0,
        monthly_profit_target=80.0
    )
    
    # ایجاد مدیر ریسک
    risk_manager = AdvancedRiskManager(risk_params)
    
    print("🎯 ADVANCED RISK MANAGER INITIALIZED")
    print("=" * 50)
    print(f"💰 Initial Capital: ${risk_params.initial_capital}")
    print(f"📉 Max Drawdown: {risk_params.max_drawdown_percent}% (${risk_params.max_drawdown_amount})")
    print(f"📊 Daily Loss Limit: {risk_params.daily_loss_limit_percent}% (${risk_params.daily_loss_limit_amount})")
    print(f"🎯 Daily Profit Target: ${risk_params.daily_profit_target_per_symbol} per symbol")
    print(f"📈 Weekly Target: ${risk_params.weekly_profit_target}")
    print(f"🚀 Monthly Target: ${risk_params.monthly_profit_target}")
    
    # نمایش وضعیت اولیه
    status = risk_manager.get_portfolio_status()
    print(f"\n📊 Portfolio Status:")
    print(f"   Current Capital: ${status['capital']['current']:.2f}")
    print(f"   Risk Level: {status['risk']['level'].upper()}")
    print(f"   Open Positions: {status['positions']['open_positions']}/{status['positions']['max_positions']}")
    
    # تست باز کردن موقعیت
    print("\n🔍 Testing position opening...")
    risk_manager.open_position("EURUSD", 1.0850, "buy")
    risk_manager.open_position("GBPUSD", 1.2650, "buy")
    
    # نمایش وضعیت بعد از باز کردن موقعیت‌ها
    status = risk_manager.get_portfolio_status()
    print(f"\n📊 Updated Portfolio Status:")
    print(f"   Current Capital: ${status['capital']['current']:.2f}")
    print(f"   Open Positions: {status['positions']['open_positions']}")
    
    # ذخیره وضعیت
    risk_manager.save_state("risk_manager_demo.json") 