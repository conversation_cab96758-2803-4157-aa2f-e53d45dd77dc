#!/usr/bin/env python3
"""
🧪 تست سیستم کش هوشمند
"""

import pandas as pd
import numpy as np
import sys
import os
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_smart_cache():
    """تست سیستم کش هوشمند"""
    print("🧪 Testing Smart Cache System")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import MultiBrainSystem, SMART_CACHE, clear_error_cache, show_cache_stats
        print("✅ Successfully imported smart cache system")
        
        # Show initial cache stats
        print("\n📊 Initial cache stats:")
        show_cache_stats()
        
        # Create test data
        data = pd.DataFrame({
            'close': np.random.uniform(1.1000, 1.1100, 50),
            'volume': np.random.randint(1000, 10000, 50),
            'rsi': np.random.uniform(20, 80, 50),
            'macd': np.random.uniform(-0.01, 0.01, 50),
            'sma_20': np.random.uniform(1.1000, 1.1100, 50)
        })
        
        print(f"\n📊 Test data: {len(data)} rows, {len(data.columns)} columns")
        
        # Initialize system
        multi_brain = MultiBrainSystem()
        print("✅ MultiBrainSystem initialized")
        
        # Test 1: First analysis (should be fresh)
        print("\n🔍 Test 1: First analysis (fresh)...")
        start_time = time.time()
        analysis1 = multi_brain.analyze_training_situation(data, "LSTM", "EURUSD")
        time1 = time.time() - start_time
        
        print(f"✅ First analysis completed in {time1:.2f}s")
        print(f"   Keys: {list(analysis1.keys())}")
        
        # Test 2: Second analysis (should use cache)
        print("\n🔍 Test 2: Second analysis (should use cache)...")
        start_time = time.time()
        analysis2 = multi_brain.analyze_training_situation(data, "LSTM", "EURUSD")
        time2 = time.time() - start_time
        
        print(f"✅ Second analysis completed in {time2:.2f}s")
        print(f"   Keys: {list(analysis2.keys())}")
        
        # Check if cache was used (should be much faster)
        if time2 < time1 * 0.5:
            print("🎉 Cache is working! Second run was much faster.")
        else:
            print("⚠️ Cache might not be working properly.")
        
        # Test 3: Check cache stats
        print("\n📊 Final cache stats:")
        show_cache_stats()
        
        # Test 4: Check required keys
        required_keys = ['hyperparameter_suggestions', 'config_suggestions', 'action', 'confidence']
        all_present = all(key in analysis2 for key in required_keys)
        
        print(f"\n🔑 All required keys present: {'✅ YES' if all_present else '❌ NO'}")
        
        if 'hyperparameter_suggestions' in analysis2:
            hps = analysis2['hyperparameter_suggestions']
            print(f"   ✅ hyperparameter_suggestions: {type(hps)}")
            if isinstance(hps, dict):
                print(f"      Content: {list(hps.keys())}")
        
        return all_present
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_caching():
    """تست کش کردن خطاها"""
    print("\n🛡️ Testing Error Caching")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import SMART_CACHE
        
        # Simulate an error
        test_params = {'test': 'error_simulation'}
        test_error = "Simulated error for testing"
        
        # Cache the error
        SMART_CACHE.cache_error('test_operation', test_params, test_error)
        print("✅ Error cached successfully")
        
        # Try to get cached result (should return None due to recent error)
        cached_result = SMART_CACHE.get_cached_result('test_operation', test_params)
        if cached_result is None:
            print("✅ Error cache is working - operation skipped due to recent error")
        else:
            print("⚠️ Error cache might not be working")
        
        # Clear error cache
        SMART_CACHE.clear_error_cache()
        print("✅ Error cache cleared")
        
        # Try again (should not be skipped now)
        cached_result = SMART_CACHE.get_cached_result('test_operation', test_params)
        if cached_result is None:
            print("✅ After clearing error cache, operation is no longer skipped")
        
        return True
        
    except Exception as e:
        print(f"❌ Error caching test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 SMART CACHE SYSTEM TEST")
    print("=" * 60)
    
    # Test 1: Smart cache functionality
    test1 = test_smart_cache()
    
    # Test 2: Error caching
    test2 = test_error_caching()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 SMART CACHE TEST SUMMARY")
    print("=" * 60)
    print(f"🧠 Smart Cache Test: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"🛡️ Error Cache Test: {'✅ PASSED' if test2 else '❌ FAILED'}")
    
    overall = test1 and test2
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if overall else '❌ SOME TESTS FAILED'}")
    
    if overall:
        print("\n🎉 Smart Cache System is working perfectly!")
        print("💡 Benefits:")
        print("   - ✅ Successful results are cached and reused")
        print("   - 🚫 Failed operations are avoided for 30 minutes")
        print("   - 🧹 Error cache can be cleared manually")
        print("   - 📊 Cache statistics are available")
        print("\n🚀 Ready for Google Colab deployment!")
    else:
        print("\n⚠️ Some issues with smart cache system.")
