# 🆓 راهنمای استفاده ساده از مدل‌های مالی

## 🎯 **فقط دو مرحله:**

### 1️⃣ **نصب:**
```bash
pip install requests transformers torch
```

### 2️⃣ **استفاده:**
```bash
python simple_financial_models.py
```

---

## 🔑 **دریافت Token رایگان (اختیاری):**

1. برو به: https://huggingface.co/settings/tokens
2. Create new token کن
3. نوع Read انتخاب کن  
4. کپی کن

**بدون token هم کار می‌کنه ولی محدودتره**

---

## 📝 **نمونه کد:**

```python
from simple_financial_models import SimpleFinancialAnalyzer

# راه‌اندازی
analyzer = SimpleFinancialAnalyzer(hf_token="اختیاری")

# تحلیل یک خبر
result = analyzer.analyze_sentiment("Apple stock rises 10%")
print(result)

# تحلیل چند خبر
news_list = [
    "Market rallies on positive earnings",
    "Economic data shows growth concerns"
]

market_sentiment = analyzer.get_market_sentiment(news_list)
print(f"احساس بازار: {market_sentiment['overall_sentiment']}")

# سیگنال معاملاتی
signal = analyzer.generate_trading_signal(news_list)
print(f"سیگنال: {signal}")
```

---

## ⚡ **مزایا:**

- ✅ **رایگان**: بدون هزینه
- ✅ **ساده**: بدون پیچیدگی
- ✅ **سریع**: نصب آسان
- ✅ **قابل اعتماد**: کش کردن نتایج
- ✅ **انعطاف‌پذیر**: API + مدل محلی

---

## 🔧 **عیب‌یابی:**

### اگر خطا گرفتی:
```bash
pip install --upgrade pip
pip install torch --index-url https://download.pytorch.org/whl/cpu
pip install transformers requests
```

### اگر مدل محلی کار نکرد:
- فقط از API استفاده می‌کنه
- Token لازم نیست
- محدودیت: چند request در روز

---

## 🎉 **تمام!**

همین! خیلی سادست. هر مشکلی داشتی بپرس. 