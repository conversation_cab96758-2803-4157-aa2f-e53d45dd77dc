"""
Real Sentiment Models for Financial Analysis
مدل‌های واقعی تحلیل احساسات مالی
"""

import logging
from typing import Dict, Any, List
from transformers import pipeline
import torch
from datetime import datetime

from core.base import BaseModel, ModelPrediction
from core.exceptions import ModelLoadError, ModelError

logger = logging.getLogger(__name__)

class BaseSentimentModel(BaseModel):
    """Base class for sentiment analysis models."""

    def __init__(self, model_name: str, model_path: str = None, config: dict = None):
        super().__init__(model_name, "sentiment_analysis", config=config)
        self.model_name = model_name # Keep for internal use if needed
        self.pipeline = None
        self.device = 0 if torch.cuda.is_available() else -1

    def load_model(self):
        """Loads the sentiment analysis model and tokenizer."""
        try:
            self.pipeline = pipeline(
                'sentiment-analysis',
                model=self.model_name,
                device=self.device
            )
            self.is_loaded = True
            logger.info(f"✅ Sentiment model loaded successfully: {self.model_name}")
        except Exception as e:
            self.is_loaded = False
            raise ModelLoadError(f"❌ Failed to load sentiment model {self.model_name}: {e}")

    def predict(self, data: Any, **kwargs) -> ModelPrediction:
        """Analyzes the sentiment of a given text."""
        if not self.is_loaded or not self.pipeline:
            raise ModelError("Model is not loaded. Call load() first.")
        
        try:
            if not isinstance(data, str):
                raise TypeError("Input data for sentiment analysis must be a string.")

            result = self.pipeline(data)[0]
            prediction = ModelPrediction(
                model_name=self.model_name,
                timestamp=datetime.now(),
                prediction=result['label'],
                confidence=result['score'],
                metadata={'raw_output': result}
            )
            return prediction
        except Exception as e:
            logger.error(f"Error during sentiment analysis for model {self.model_name}: {e}")
            raise ModelError(f"Error during prediction: {e}")

    def get_info(self) -> Dict[str, Any]:
        """Returns information about the model."""
        return {
            'model_name': self.model_name,
            'model_type': 'SentimentAnalysis',
            'is_loaded': self.is_loaded,
            'device': self.device,
            'config': self.config
        }

class FinBERTModel(BaseSentimentModel):
    """FinBERT Model for financial sentiment analysis."""
    def __init__(self, model_name: str = "ProsusAI/finbert", config: dict = None):
        super().__init__(model_name, "sentiment_analysis", config=config)

class CryptoBERTModel(BaseSentimentModel):
    """CryptoBERT Model for cryptocurrency sentiment analysis."""
    def __init__(self, model_name: str = "ElKulako/cryptobert", config: dict = None):
        super().__init__(name=model_name, config=config)

class FinancialSentimentModel(BaseSentimentModel):
    """General financial sentiment analysis model."""
    def __init__(self, model_name: str = "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis", config: dict = None):
        super().__init__(name=model_name, config=config)

__all__ = ['FinBERTModel', 'CryptoBERTModel', 'FinancialSentimentModel']