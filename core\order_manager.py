#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ Advanced Order Management System
سیستم پیشرفته مدیریت سفارشات با Position Tracking و Order Validation
"""

import os
import sys
import time
import uuid
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from decimal import Decimal, getcontext
import threading
from contextlib import contextmanager
import logging

# Set decimal precision for financial calculations
getcontext().prec = 28

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .exceptions import TradingSystemError, ValidationError
from .shared_types import (
    Order, OrderType, OrderSide, OrderStatus, Fill, TimeInForce, PositionSide
)

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class OrderValidator:
    """اعتبارسنجی سفارشات"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Validation rules
        self.min_quantity = Decimal('0.001')
        self.max_quantity = Decimal('1000000')
        self.min_price = Decimal('0.0001')
        self.max_price = Decimal('1000000')
        self.max_orders_per_symbol = 100
        
        # Supported symbols
        self.supported_symbols = {
            'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
            'EURJPY', 'GBPJPY', 'AUDJPY', 'XAUUSD', 'XAGUSD', 'BTCUSD', 'ETHUSD'
        }
    
    def validate_order(self, order: Order) -> List[str]:
        """اعتبارسنجی سفارش"""
        errors = []
        
        # Symbol validation
        if order.symbol not in self.supported_symbols:
            errors.append(f"Unsupported symbol: {order.symbol}")
        
        # Quantity validation
        if order.quantity <= 0:
            errors.append("Quantity must be positive")
        elif order.quantity < self.min_quantity:
            errors.append(f"Quantity below minimum: {self.min_quantity}")
        elif order.quantity > self.max_quantity:
            errors.append(f"Quantity above maximum: {self.max_quantity}")
        
        # Price validation
        if order.price is not None:
            if order.price <= 0:
                errors.append("Price must be positive")
            elif order.price < self.min_price:
                errors.append(f"Price below minimum: {self.min_price}")
            elif order.price > self.max_price:
                errors.append(f"Price above maximum: {self.max_price}")
        
        # Stop price validation
        if order.stop_price is not None:
            if order.stop_price <= 0:
                errors.append("Stop price must be positive")
            elif order.stop_price < self.min_price:
                errors.append(f"Stop price below minimum: {self.min_price}")
            elif order.stop_price > self.max_price:
                errors.append(f"Stop price above maximum: {self.max_price}")
        
        # Order type specific validation
        if order.order_type == OrderType.LIMIT and order.price is None:
            errors.append("Limit order requires price")
        elif order.order_type == OrderType.STOP and order.stop_price is None:
            errors.append("Stop order requires stop price")
        elif order.order_type == OrderType.STOP_LIMIT:
            if order.price is None or order.stop_price is None:
                errors.append("Stop limit order requires both price and stop price")
        
        # Time in force validation
        if order.time_in_force == TimeInForce.GTD and order.expires_at is None:
            errors.append("GTD order requires expiration date")
        elif order.expires_at is not None and order.expires_at <= datetime.now():
            errors.append("Expiration date must be in the future")
        
        return errors
    
    def validate_position_size(self, symbol: str, new_quantity: Decimal, 
                             current_position: Optional[PositionSide] = None) -> List[str]:
        """اعتبارسنجی سایز پوزیشن"""
        errors = []
        
        # Calculate total position size
        total_quantity = new_quantity
        if current_position and current_position != PositionSide.FLAT:
            total_quantity += current_position.quantity
        
        # Max position size check
        max_position_size = self.max_quantity
        if total_quantity > max_position_size:
            errors.append(f"Position size exceeds maximum: {max_position_size}")
        
        return errors

class AdvancedOrderManager:
    """مدیر پیشرفته سفارشات"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.validator = OrderValidator()
        
        # Storage
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, PositionSide] = {}
        self.order_history: List[Order] = []
        self.trade_history: List[Fill] = []
        
        # Configuration
        self.max_orders = 1000
        self.max_history_size = 10000
        self.auto_cleanup = True
        self.cleanup_interval = 3600  # 1 hour
        
        # Threading
        self.lock = threading.RLock()
        
        # Statistics
        self.stats = {
            "total_orders": 0,
            "filled_orders": 0,
            "cancelled_orders": 0,
            "rejected_orders": 0,
            "total_volume": Decimal('0'),
            "total_commission": Decimal('0'),
            "active_positions": 0,
            "total_pnl": Decimal('0')
        }
        
        # Order execution callbacks
        self.execution_callbacks: List[Callable] = []
        
        # Start cleanup thread
        if self.auto_cleanup:
            self._start_cleanup_thread()
    
    def create_order(self, symbol: str, side: OrderSide, order_type: OrderType,
                    quantity: Union[Decimal, float], price: Optional[Union[Decimal, float]] = None,
                    stop_price: Optional[Union[Decimal, float]] = None,
                    time_in_force: TimeInForce = TimeInForce.GTC,
                    expires_at: Optional[datetime] = None,
                    metadata: Optional[Dict[str, Any]] = None) -> Order:
        """ایجاد سفارش جدید"""
        
        with self.lock:
            # Generate order ID
            order_id = str(uuid.uuid4())
            
            # Create order
            order = Order(
                order_id=order_id,
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=Decimal(str(quantity)),
                price=Decimal(str(price)) if price is not None else None,
                stop_price=Decimal(str(stop_price)) if stop_price is not None else None,
                time_in_force=time_in_force,
                expires_at=expires_at,
                metadata=metadata or {}
            )
            
            # Validate order
            validation_errors = self.validator.validate_order(order)
            if validation_errors:
                order.status = OrderStatus.REJECTED
                order.metadata['rejection_reason'] = validation_errors
                self.logger.error(f"Order validation failed: {validation_errors}")
                raise ValidationError(f"Order validation failed: {', '.join(validation_errors)}")
            
            # Check position size
            current_position = self.positions.get(symbol)
            position_errors = self.validator.validate_position_size(symbol, order.quantity, current_position)
            if position_errors:
                order.status = OrderStatus.REJECTED
                order.metadata['rejection_reason'] = position_errors
                self.logger.error(f"Position size validation failed: {position_errors}")
                raise ValidationError(f"Position size validation failed: {', '.join(position_errors)}")
            
            # Store order
            self.orders[order_id] = order
            self.stats["total_orders"] += 1
            
            self.logger.info(f"Order created: {order_id} - {symbol} {side.value} {quantity} @ {price}")
            return order
    
    def submit_order(self, order_id: str) -> bool:
        """ارسال سفارش"""
        with self.lock:
            order = self.orders.get(order_id)
            if not order:
                self.logger.error(f"Order not found: {order_id}")
                return False
            
            if order.status != OrderStatus.PENDING:
                self.logger.error(f"Order not in pending status: {order_id}")
                return False
            
            # Submit order (simulate)
            order.status = OrderStatus.SUBMITTED
            order.updated_at = datetime.now()
            
            self.logger.info(f"Order submitted: {order_id}")
            return True
    
    def cancel_order(self, order_id: str) -> bool:
        """لغو سفارش"""
        with self.lock:
            order = self.orders.get(order_id)
            if not order:
                self.logger.error(f"Order not found: {order_id}")
                return False
            
            if not order.is_active():
                self.logger.error(f"Order not active: {order_id}")
                return False
            
            # Cancel order
            order.status = OrderStatus.CANCELLED
            order.updated_at = datetime.now()
            
            self.stats["cancelled_orders"] += 1
            self.logger.info(f"Order cancelled: {order_id}")
            return True
    
    def fill_order(self, order_id: str, fill_price: Union[Decimal, float], 
                  fill_quantity: Union[Decimal, float], commission: Union[Decimal, float] = 0,
                  exchange: str = "") -> bool:
        """پر کردن سفارش"""
        with self.lock:
            order = self.orders.get(order_id)
            if not order:
                self.logger.error(f"Order not found: {order_id}")
                return False
            
            if not order.is_active():
                self.logger.error(f"Order not active: {order_id}")
                return False
            
            # Create fill
            fill = Fill(
                fill_id=str(uuid.uuid4()),
                order_id=order_id,
                price=Decimal(str(fill_price)),
                quantity=Decimal(str(fill_quantity)),
                timestamp=datetime.now(),
                commission=Decimal(str(commission)),
                exchange=exchange
            )
            
            # Add fill to order
            order.add_fill(fill)
            
            # Update statistics
            self.stats["total_volume"] += fill.quantity
            self.stats["total_commission"] += fill.commission
            
            if order.is_filled():
                self.stats["filled_orders"] += 1
                self.order_history.append(order)
                
                # Update position
                self._update_position(order)
            
            # Store in trade history
            self.trade_history.append(fill)
            
            # Execute callbacks
            for callback in self.execution_callbacks:
                try:
                    callback(order, fill)
                except Exception as e:
                    self.logger.error(f"Callback error: {e}")
            
            self.logger.info(f"Order filled: {order_id} - {fill_quantity} @ {fill_price}")
            return True
    
    def _update_position(self, order: Order):
        """بروزرسانی پوزیشن"""
        symbol = order.symbol
        current_position = self.positions.get(symbol)
        
        if not current_position:
            # Create new position
            side = PositionSide.LONG if order.side == OrderSide.BUY else PositionSide.SHORT
            position = PositionSide(
                symbol=symbol,
                side=side,
                quantity=order.filled_quantity,
                entry_price=order.avg_fill_price,
                current_price=order.avg_fill_price,
                orders=[order.order_id]
            )
            self.positions[symbol] = position
            self.stats["active_positions"] += 1
        else:
            # Update existing position
            if order.side == OrderSide.BUY:
                if current_position.side == PositionSide.SHORT:
                    # Reducing short position
                    if order.filled_quantity >= current_position.quantity:
                        # Close short and open long
                        remaining = order.filled_quantity - current_position.quantity
                        current_position.realized_pnl += (current_position.entry_price - order.avg_fill_price) * current_position.quantity
                        
                        if remaining > 0:
                            current_position.side = PositionSide.LONG
                            current_position.quantity = remaining
                            current_position.entry_price = order.avg_fill_price
                        else:
                            current_position.side = PositionSide.FLAT
                            current_position.quantity = Decimal('0')
                            self.stats["active_positions"] -= 1
                    else:
                        # Partial close
                        current_position.quantity -= order.filled_quantity
                        current_position.realized_pnl += (current_position.entry_price - order.avg_fill_price) * order.filled_quantity
                else:
                    # Adding to long position
                    total_value = (current_position.entry_price * current_position.quantity) + (order.avg_fill_price * order.filled_quantity)
                    current_position.quantity += order.filled_quantity
                    current_position.entry_price = total_value / current_position.quantity
                    current_position.side = PositionSide.LONG
            else:  # SELL
                if current_position.side == PositionSide.LONG:
                    # Reducing long position
                    if order.filled_quantity >= current_position.quantity:
                        # Close long and open short
                        remaining = order.filled_quantity - current_position.quantity
                        current_position.realized_pnl += (order.avg_fill_price - current_position.entry_price) * current_position.quantity
                        
                        if remaining > 0:
                            current_position.side = PositionSide.SHORT
                            current_position.quantity = remaining
                            current_position.entry_price = order.avg_fill_price
                        else:
                            current_position.side = PositionSide.FLAT
                            current_position.quantity = Decimal('0')
                            self.stats["active_positions"] -= 1
                    else:
                        # Partial close
                        current_position.quantity -= order.filled_quantity
                        current_position.realized_pnl += (order.avg_fill_price - current_position.entry_price) * order.filled_quantity
                else:
                    # Adding to short position
                    total_value = (current_position.entry_price * current_position.quantity) + (order.avg_fill_price * order.filled_quantity)
                    current_position.quantity += order.filled_quantity
                    current_position.entry_price = total_value / current_position.quantity
                    current_position.side = PositionSide.SHORT
            
            current_position.orders.append(order.order_id)
            current_position.updated_at = datetime.now()
        
        # Update total PnL
        self._update_total_pnl()
    
    def _update_total_pnl(self):
        """بروزرسانی کل سود و زیان"""
        total_pnl = Decimal('0')
        for position in self.positions.values():
            if position.side != PositionSide.FLAT:
                total_pnl += position.get_total_pnl()
        self.stats["total_pnl"] = total_pnl
    
    def update_market_price(self, symbol: str, price: Union[Decimal, float]):
        """بروزرسانی قیمت بازار"""
        with self.lock:
            position = self.positions.get(symbol)
            if position and position.side != PositionSide.FLAT:
                position.update_price(Decimal(str(price)))
                self._update_total_pnl()
    
    def get_order(self, order_id: str) -> Optional[Order]:
        """دریافت سفارش"""
        return self.orders.get(order_id)
    
    def get_active_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """دریافت سفارشات فعال"""
        orders = [order for order in self.orders.values() if order.is_active()]
        if symbol:
            orders = [order for order in orders if order.symbol == symbol]
        return orders
    
    def get_position(self, symbol: str) -> Optional[PositionSide]:
        """دریافت پوزیشن"""
        return self.positions.get(symbol)
    
    def get_all_positions(self) -> Dict[str, PositionSide]:
        """دریافت همه پوزیشن‌ها"""
        return self.positions.copy()
    
    def get_order_history(self, symbol: Optional[str] = None, limit: int = 100) -> List[Order]:
        """دریافت تاریخچه سفارشات"""
        history = self.order_history
        if symbol:
            history = [order for order in history if order.symbol == symbol]
        return history[-limit:]
    
    def get_trade_history(self, symbol: Optional[str] = None, limit: int = 100) -> List[Fill]:
        """دریافت تاریخچه معاملات"""
        history = self.trade_history
        if symbol:
            history = [fill for fill in history if fill.order_id in self.orders and self.orders[fill.order_id].symbol == symbol]
        return history[-limit:]
    
    def validate_order(self, order: Order) -> bool:
        """اعتبارسنجی سفارش"""
        try:
            validation_errors = self.validator.validate_order(order)
            return len(validation_errors) == 0
        except Exception as e:
            self.logger.error(f"Order validation error: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """دریافت آمار"""
        return self.stats.copy()
    
    def add_execution_callback(self, callback: Callable):
        """اضافه کردن callback اجرا"""
        self.execution_callbacks.append(callback)
    
    def export_trading_report(self, filename: str = None) -> str:
        """گزارش معاملات"""
        if filename is None:
            filename = f"trading_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "statistics": {
                "total_orders": self.stats["total_orders"],
                "filled_orders": self.stats["filled_orders"],
                "cancelled_orders": self.stats["cancelled_orders"],
                "rejected_orders": self.stats["rejected_orders"],
                "total_volume": str(self.stats["total_volume"]),
                "total_commission": str(self.stats["total_commission"]),
                "active_positions": self.stats["active_positions"],
                "total_pnl": str(self.stats["total_pnl"])
            },
            "active_orders": [
                {
                    "order_id": order.order_id,
                    "symbol": order.symbol,
                    "side": order.side.value,
                    "type": order.order_type.value,
                    "quantity": str(order.quantity),
                    "price": str(order.price) if order.price else None,
                    "status": order.status.value,
                    "filled_quantity": str(order.filled_quantity),
                    "created_at": order.created_at.isoformat()
                }
                for order in self.get_active_orders()
            ],
            "positions": [
                {
                    "symbol": pos.symbol,
                    "side": pos.side.value,
                    "quantity": str(pos.quantity),
                    "entry_price": str(pos.entry_price),
                    "current_price": str(pos.current_price),
                    "unrealized_pnl": str(pos.unrealized_pnl),
                    "realized_pnl": str(pos.realized_pnl),
                    "total_pnl": str(pos.get_total_pnl()),
                    "roi": pos.get_roi()
                }
                for pos in self.positions.values() if pos.side != PositionSide.FLAT
            ]
        }
        
        try:
            import json
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Trading report exported to {filename}")
            return filename
        except Exception as e:
            self.logger.error(f"Failed to export trading report: {e}")
            return ""
    
    def _start_cleanup_thread(self):
        """شروع thread پاک‌سازی"""
        def cleanup_worker():
            while self.auto_cleanup:
                try:
                    with self.lock:
                        # Clean up old orders
                        cutoff_time = datetime.now() - timedelta(hours=24)
                        
                        # Move old completed orders to history
                        old_orders = [
                            order for order in self.orders.values()
                            if not order.is_active() and order.updated_at < cutoff_time
                        ]
                        
                        for order in old_orders:
                            if order not in self.order_history:
                                self.order_history.append(order)
                            del self.orders[order.order_id]
                        
                        # Limit history size
                        if len(self.order_history) > self.max_history_size:
                            self.order_history = self.order_history[-self.max_history_size:]
                        
                        if len(self.trade_history) > self.max_history_size:
                            self.trade_history = self.trade_history[-self.max_history_size:]
                        
                        if old_orders:
                            self.logger.info(f"Cleaned up {len(old_orders)} old orders")
                    
                    time.sleep(self.cleanup_interval)
                except Exception as e:
                    self.logger.error(f"Cleanup error: {e}")
                    time.sleep(self.cleanup_interval)
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        self.logger.info("Order cleanup thread started")
    
    def stop_cleanup(self):
        """توقف پاک‌سازی"""
        self.auto_cleanup = False
        self.logger.info("Order cleanup stopped")

# Global instance
order_manager = AdvancedOrderManager()

@contextmanager
def trading_session(symbol: str):
    """Context manager برای جلسه معاملاتی"""
    session_start = datetime.now()
    initial_position = order_manager.get_position(symbol)
    
    try:
        yield order_manager
    finally:
        session_end = datetime.now()
        final_position = order_manager.get_position(symbol)
        
        session_duration = session_end - session_start
        logger.info(f"Trading session completed for {symbol} - Duration: {session_duration}")
        
        if initial_position and final_position:
            pnl_change = final_position.get_total_pnl() - initial_position.get_total_pnl()
            logger.info(f"Session PnL change: {pnl_change}")

# Test and examples
if __name__ == "__main__":
    # Test order management
    print("📊 Testing Order Management System...")
    
    # Test 1: Create orders
    print("\n1. Creating orders...")
    try:
        # Market buy order
        order1 = order_manager.create_order(
            symbol="EURUSD",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100000
        )
        print(f"✓ Market order created: {order1.order_id}")
        
        # Limit sell order
        order2 = order_manager.create_order(
            symbol="EURUSD",
            side=OrderSide.SELL,
            order_type=OrderType.LIMIT,
            quantity=50000,
            price=1.1000
        )
        print(f"✓ Limit order created: {order2.order_id}")
        
        # Submit orders
        order_manager.submit_order(order1.order_id)
        order_manager.submit_order(order2.order_id)
        print("✓ Orders submitted")
        
        # Fill market order
        order_manager.fill_order(order1.order_id, 1.0950, 100000)
        print("✓ Market order filled")
        
        # Check position
        position = order_manager.get_position("EURUSD")
        if position:
            print(f"✓ Position created: {position.side.value} {position.quantity}")
        
        # Update market price
        order_manager.update_market_price("EURUSD", 1.1000)
        print("✓ Market price updated")
        
        # Get statistics
        stats = order_manager.get_statistics()
        print(f"✓ Statistics: {stats}")
        
        # Export report
        report_file = order_manager.export_trading_report()
        if report_file:
            print(f"✓ Report exported: {report_file}")
            # Clean up
            import os
            if os.path.exists(report_file):
                os.remove(report_file)
        
        print("\n✅ Order Management System test completed!")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc() 