#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سیستم آموزش مدل‌های یادگیری تقویتی
"""

import os
import sys
import warnings
import numpy as np
import pandas as pd
import torch
import json
import pickle
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import logging

# Suppress warnings
warnings.filterwarnings('ignore')

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import gym
    from gym import spaces
    from stable_baselines3 import PPO, A2C, DQN, SAC
    from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize
    from stable_baselines3.common.callbacks import EvalCallback, CheckpointCallback
    from stable_baselines3.common.monitor import Monitor
    RL_AVAILABLE = True
except ImportError:
    RL_AVAILABLE = False
    print("⚠️ stable_baselines3 not available. Installing...")
    try:
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "stable-baselines3[extra]"])
        import gym
        from gym import spaces
        from stable_baselines3 import PPO, A2C, DQN, SAC
        from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize
        from stable_baselines3.common.callbacks import EvalCallback, CheckpointCallback
        from stable_baselines3.common.monitor import Monitor
        RL_AVAILABLE = True
        print("✅ stable_baselines3 installed successfully")
    except Exception as e:
        print(f"❌ Failed to install stable_baselines3: {e}")
        RL_AVAILABLE = False

from env.trading_env import TradingEnvV2
from utils.logger import setup_logger
from utils.technical_indicators import TechnicalIndicators

class RLTrainer:
    """سیستم آموزش مدل‌های RL"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or self._get_default_config()
        self.logger = setup_logger('rl_trainer')
        self.models = {}
        self.training_history = {}
        
        if not RL_AVAILABLE:
            raise ImportError("stable_baselines3 is required for RL training")
        
        # Create directories
        os.makedirs('models/trained_models', exist_ok=True)
        os.makedirs('models/checkpoints', exist_ok=True)
        os.makedirs('training/logs', exist_ok=True)
        
        self.logger.info("✅ RL Trainer initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """تنظیمات پیش‌فرض"""
        return {
            'models': {
                'ppo': {
                    'learning_rate': 0.0003,
                    'n_steps': 2048,
                    'batch_size': 64,
                    'n_epochs': 10,
                    'gamma': 0.99,
                    'gae_lambda': 0.95,
                    'clip_range': 0.2,
                    'clip_range_vf': None,
                    'ent_coef': 0.0,
                    'vf_coef': 0.5,
                    'max_grad_norm': 0.5,
                    'use_sde': False,
                    'sde_sample_freq': -1,
                    'target_kl': None,
                    'tensorboard_log': None,
                    'policy_kwargs': None,
                    'verbose': 1,
                    'seed': None,
                    'device': 'auto',
                    '_init_setup_model': True
                },
                'a2c': {
                    'learning_rate': 0.0007,
                    'n_steps': 5,
                    'gamma': 0.99,
                    'gae_lambda': 1.0,
                    'ent_coef': 0.0,
                    'vf_coef': 0.5,
                    'max_grad_norm': 0.5,
                    'rms_prop_eps': 1e-05,
                    'use_rms_prop': True,
                    'use_sde': False,
                    'sde_sample_freq': -1,
                    'normalize_advantage': False,
                    'tensorboard_log': None,
                    'policy_kwargs': None,
                    'verbose': 1,
                    'seed': None,
                    'device': 'auto',
                    '_init_setup_model': True
                },
                'dqn': {
                    'learning_rate': 0.0001,
                    'buffer_size': 1000000,
                    'learning_starts': 50000,
                    'batch_size': 32,
                    'tau': 1.0,
                    'gamma': 0.99,
                    'train_freq': 4,
                    'gradient_steps': 1,
                    'target_update_interval': 10000,
                    'exploration_fraction': 0.1,
                    'exploration_initial_eps': 1.0,
                    'exploration_final_eps': 0.05,
                    'max_grad_norm': 10,
                    'tensorboard_log': None,
                    'policy_kwargs': None,
                    'verbose': 1,
                    'seed': None,
                    'device': 'auto',
                    '_init_setup_model': True
                }
            },
            'training': {
                'total_timesteps': 100000,
                'eval_freq': 10000,
                'save_freq': 50000,
                'n_eval_episodes': 10,
                'early_stopping_patience': 5
            },
            'environment': {
                'indicators': {
                    'rsi': {'period': 14},
                    'ma': {'period': 20},
                    'macd': {'fast': 12, 'slow': 26, 'signal': 9},
                    'bollinger': {'period': 20, 'std': 2},
                    'stochastic': {'period': 14}
                },
                'sentiment_enabled': True,
                'sentiment_weight': 0.2,
                'lot_size': 0.1,
                'stop_loss': 10,
                'take_profit': 20,
                'initial_balance': 10000,
                'max_daily_drawdown': 0.04,
                'max_total_drawdown': 0.10
            }
        }
    
    def load_data(self, symbol: str, timeframe: str = 'D1') -> pd.DataFrame:
        """بارگذاری داده‌های بازار"""
        try:
            data_path = f'data/{symbol}/{timeframe}.csv'
            if not os.path.exists(data_path):
                data_path = f'data_new/{symbol}/{timeframe}.csv'
            
            if not os.path.exists(data_path):
                raise FileNotFoundError(f"Data not found for {symbol} {timeframe}")
            
            df = pd.read_csv(data_path)
            df['datetime'] = pd.to_datetime(df['datetime'])
            df = df.sort_values('datetime').reset_index(drop=True)
            
            # Add technical indicators
            ti = TechnicalIndicators()
            df = ti.calculate_all(df)
            
            self.logger.info(f"✅ Loaded {len(df)} records for {symbol} {timeframe}")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ Failed to load data: {e}")
            raise
    
    def create_environment(self, df: pd.DataFrame, symbol: str, 
                          env_type: str = 'trading') -> Any:
        """ایجاد محیط معاملاتی"""
        try:
            if env_type == 'trading':
                env = TradingEnvV2(
                    df=df,
                    symbol=symbol,
                    style='RL',
                    timeframe='D1',
                    indicators=self.config['environment']['indicators'],
                    sentiment_enabled=self.config['environment']['sentiment_enabled'],
                    sentiment_weight=self.config['environment']['sentiment_weight'],
                    lot_size=self.config['environment']['lot_size'],
                    stop_loss=self.config['environment']['stop_loss'],
                    take_profit=self.config['environment']['take_profit'],
                    initial_balance=self.config['environment']['initial_balance'],
                    max_daily_drawdown=self.config['environment']['max_daily_drawdown'],
                    max_total_drawdown=self.config['environment']['max_total_drawdown']
                )
                
                # Wrap with Monitor and DummyVecEnv
                env = Monitor(env)
                env = DummyVecEnv([lambda: env])
                
                # Normalize observations and rewards
                env = VecNormalize(env, norm_obs=True, norm_reward=True)
                
                self.logger.info(f"✅ Created trading environment for {symbol}")
                return env
            else:
                raise ValueError(f"Unknown environment type: {env_type}")
                
        except Exception as e:
            self.logger.error(f"❌ Failed to create environment: {e}")
            raise
    
    def create_model(self, model_type: str, env: Any, **kwargs) -> Any:
        """ایجاد مدل RL"""
        try:
            model_config = self.config['models'].get(model_type, {})
            model_config.update(kwargs)
            
            if model_type.lower() == 'ppo':
                model = PPO("MlpPolicy", env, **model_config)
            elif model_type.lower() == 'a2c':
                model = A2C("MlpPolicy", env, **model_config)
            elif model_type.lower() == 'dqn':
                model = DQN("MlpPolicy", env, **model_config)
            elif model_type.lower() == 'sac':
                model = SAC("MlpPolicy", env, **model_config)
            else:
                raise ValueError(f"Unknown model type: {model_type}")
            
            self.models[model_type] = model
            self.logger.info(f"✅ Created {model_type.upper()} model")
            return model
            
        except Exception as e:
            self.logger.error(f"❌ Failed to create {model_type} model: {e}")
            raise
    
    def train_model(self, model_type: str, symbol: str, 
                   total_timesteps: int = None, **kwargs) -> Dict[str, Any]:
        """آموزش مدل"""
        try:
            # Load data
            df = self.load_data(symbol)
            
            # Create environment
            env = self.create_environment(df, symbol)
            
            # Create model
            model = self.create_model(model_type, env, **kwargs)
            
            # Setup callbacks
            eval_env = self.create_environment(df, symbol)
            eval_callback = EvalCallback(
                eval_env,
                best_model_save_path=f'models/checkpoints/{model_type}_{symbol}_best',
                log_path=f'training/logs/{model_type}_{symbol}',
                eval_freq=self.config['training']['eval_freq'],
                n_eval_episodes=self.config['training']['n_eval_episodes'],
                deterministic=True,
                render=False
            )
            
            checkpoint_callback = CheckpointCallback(
                save_freq=self.config['training']['save_freq'],
                save_path=f'models/checkpoints/{model_type}_{symbol}',
                name_prefix=f'{model_type}_{symbol}'
            )
            
            # Training
            total_timesteps = total_timesteps or self.config['training']['total_timesteps']
            
            self.logger.info(f"🚀 Starting {model_type.upper()} training for {symbol}")
            self.logger.info(f"📊 Total timesteps: {total_timesteps}")
            
            model.learn(
                total_timesteps=total_timesteps,
                callback=[eval_callback, checkpoint_callback],
                progress_bar=True
            )
            
            # Save final model
            model_path = f'models/trained_models/{model_type}_{symbol}_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
            model.save(model_path)
            
            # Save training history
            training_info = {
                'model_type': model_type,
                'symbol': symbol,
                'total_timesteps': total_timesteps,
                'training_date': datetime.now().isoformat(),
                'model_path': model_path,
                'config': self.config
            }
            
            self.training_history[f"{model_type}_{symbol}"] = training_info
            
            # Save training history
            with open('training/training_history.json', 'w') as f:
                json.dump(self.training_history, f, indent=2)
            
            self.logger.info(f"✅ {model_type.upper()} training completed for {symbol}")
            self.logger.info(f"💾 Model saved to: {model_path}")
            
            return training_info
            
        except Exception as e:
            self.logger.error(f"❌ Training failed: {e}")
            raise
    
    def evaluate_model(self, model_type: str, symbol: str, 
                      n_episodes: int = 10) -> Dict[str, Any]:
        """ارزیابی مدل"""
        try:
            # Load data
            df = self.load_data(symbol)
            
            # Create environment
            env = self.create_environment(df, symbol)
            
            # Load model
            model_path = f'models/trained_models/{model_type}_{symbol}'
            if not os.path.exists(model_path + '.zip'):
                # Try to find latest model
                import glob
                model_files = glob.glob(f'models/trained_models/{model_type}_{symbol}_*.zip')
                if not model_files:
                    raise FileNotFoundError(f"No trained model found for {model_type}_{symbol}")
                model_path = model_files[-1].replace('.zip', '')
            
            model = self.create_model(model_type, env)
            model = model.load(model_path)
            
            # Evaluation
            results = []
            for episode in range(n_episodes):
                obs = env.reset()
                done = False
                episode_reward = 0
                episode_length = 0
                
                while not done:
                    action, _ = model.predict(obs, deterministic=True)
                    obs, reward, done, _ = env.step(action)
                    episode_reward += reward[0]
                    episode_length += 1
                
                results.append({
                    'episode': episode + 1,
                    'reward': episode_reward,
                    'length': episode_length
                })
            
            # Calculate metrics
            rewards = [r['reward'] for r in results]
            lengths = [r['length'] for r in results]
            
            evaluation_metrics = {
                'model_type': model_type,
                'symbol': symbol,
                'n_episodes': n_episodes,
                'mean_reward': np.mean(rewards),
                'std_reward': np.std(rewards),
                'min_reward': np.min(rewards),
                'max_reward': np.max(rewards),
                'mean_length': np.mean(lengths),
                'std_length': np.std(lengths),
                'results': results
            }
            
            # Save evaluation results
            eval_path = f'training/evaluation_{model_type}_{symbol}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(eval_path, 'w') as f:
                json.dump(evaluation_metrics, f, indent=2)
            
            self.logger.info(f"✅ Evaluation completed for {model_type}_{symbol}")
            self.logger.info(f"📊 Mean reward: {evaluation_metrics['mean_reward']:.2f}")
            self.logger.info(f"📊 Mean length: {evaluation_metrics['mean_length']:.2f}")
            
            return evaluation_metrics
            
        except Exception as e:
            self.logger.error(f"❌ Evaluation failed: {e}")
            raise
    
    def train_all_models(self, symbols: List[str], 
                        model_types: List[str] = None) -> Dict[str, Any]:
        """آموزش همه مدل‌ها برای همه نمادها"""
        if model_types is None:
            model_types = ['ppo', 'a2c', 'dqn']
        
        results = {}
        
        for symbol in symbols:
            results[symbol] = {}
            for model_type in model_types:
                try:
                    self.logger.info(f"🔄 Training {model_type.upper()} for {symbol}")
                    result = self.train_model(model_type, symbol)
                    results[symbol][model_type] = result
                except Exception as e:
                    self.logger.error(f"❌ Failed to train {model_type} for {symbol}: {e}")
                    results[symbol][model_type] = {'error': str(e)}
        
        return results
    
    def compare_models(self, symbol: str, model_types: List[str] = None) -> Dict[str, Any]:
        """مقایسه عملکرد مدل‌ها"""
        if model_types is None:
            model_types = ['ppo', 'a2c', 'dqn']
        
        comparison = {}
        
        for model_type in model_types:
            try:
                eval_result = self.evaluate_model(model_type, symbol)
                comparison[model_type] = {
                    'mean_reward': eval_result['mean_reward'],
                    'std_reward': eval_result['std_reward'],
                    'mean_length': eval_result['mean_length']
                }
            except Exception as e:
                self.logger.error(f"❌ Failed to evaluate {model_type}: {e}")
                comparison[model_type] = {'error': str(e)}
        
        # Find best model
        valid_models = {k: v for k, v in comparison.items() if 'error' not in v}
        if valid_models:
            best_model = max(valid_models.items(), key=lambda x: x[1]['mean_reward'])
            comparison['best_model'] = {
                'model_type': best_model[0],
                'mean_reward': best_model[1]['mean_reward']
            }
        
        return comparison

def main():
    """تابع اصلی برای تست"""
    # Initialize trainer
    trainer = RLTrainer()
    
    # Test with EURUSD
    symbols = ['EURUSD', 'GBPUSD']
    
    # Train PPO model
    try:
        result = trainer.train_model('ppo', 'EURUSD', total_timesteps=50000)
        print(f"✅ Training completed: {result}")
        
        # Evaluate
        eval_result = trainer.evaluate_model('ppo', 'EURUSD')
        print(f"✅ Evaluation completed: {eval_result}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main() 