"""
🔄 Pearl-3x7B Colab Model Importer
وارد کننده مدل‌های Colab به پروژه محلی

این اسکریپت مدل‌های آموزش دیده در Colab را به پروژه محلی منتقل می‌کند
"""

import os
import sys
import json
import zipfile
import shutil
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

class ColabModelImporter:
    """🔄 وارد کننده مدل‌های Colab"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.models_dir = self.project_root / "models" / "trained_models"
        self.colab_models_dir = self.models_dir / "colab_trained"
        
        # Create directories
        self.models_dir.mkdir(parents=True, exist_ok=True)
        self.colab_models_dir.mkdir(parents=True, exist_ok=True)
        
    def import_colab_models(self, zip_path: str) -> Dict[str, Any]:
        """وارد کردن مدل‌های Colab از فایل zip"""
        print("🔄 IMPORTING COLAB MODELS")
        print("=" * 50)
        
        if not os.path.exists(zip_path):
            print(f"❌ Zip file not found: {zip_path}")
            return {"success": False, "error": "File not found"}
        
        try:
            # Extract zip file
            extract_dir = self.colab_models_dir / f"import_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            extract_dir.mkdir(exist_ok=True)
            
            print(f"📦 Extracting {zip_path}...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            # Read package info
            package_info_path = extract_dir / "package_info.json"
            if package_info_path.exists():
                with open(package_info_path, 'r') as f:
                    package_info = json.load(f)
                
                print(f"📊 Package Info:")
                print(f"   Name: {package_info.get('package_name', 'Unknown')}")
                print(f"   Created: {package_info.get('created_at', 'Unknown')}")
                print(f"   Total Models: {package_info.get('total_models', 0)}")
                print()
                
                # Import each model
                imported_models = []
                failed_imports = []
                
                for model_info in package_info.get('models', []):
                    result = self._import_single_model(extract_dir, model_info)
                    if result["success"]:
                        imported_models.append(result)
                        print(f"✅ {model_info['name']} imported successfully")
                    else:
                        failed_imports.append(result)
                        print(f"❌ {model_info['name']} import failed: {result.get('error', 'Unknown')}")
                
                # Update model registry
                self._update_model_registry(imported_models)
                
                # Generate import report
                report = self._generate_import_report(imported_models, failed_imports, package_info)
                
                print(f"\n🎉 Import completed!")
                print(f"✅ Successfully imported: {len(imported_models)}")
                print(f"❌ Failed imports: {len(failed_imports)}")
                
                return {
                    "success": True,
                    "imported_models": imported_models,
                    "failed_imports": failed_imports,
                    "report": report
                }
            
            else:
                print("❌ Package info not found in zip file")
                return {"success": False, "error": "Invalid package format"}
                
        except Exception as e:
            print(f"❌ Import failed: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    def _import_single_model(self, extract_dir: Path, model_info: Dict[str, Any]) -> Dict[str, Any]:
        """وارد کردن یک مدل"""
        model_name = model_info["name"]
        category = model_info["category"]
        
        try:
            # Create model directory
            model_dir = self.colab_models_dir / category / f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            model_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy model files
            source_model_dir = extract_dir / "models"
            if source_model_dir.exists():
                for model_subdir in source_model_dir.iterdir():
                    if model_name in model_subdir.name:
                        shutil.copytree(model_subdir, model_dir / "model_files", dirs_exist_ok=True)
                        break
            
            # Create model metadata
            metadata = {
                "model_name": model_name,
                "category": category,
                "complexity": model_info.get("complexity", "unknown"),
                "performance": model_info.get("performance", 0),
                "training_hours": model_info.get("training_hours", 0),
                "gpu_used": model_info.get("gpu_used", "unknown"),
                "metrics": model_info.get("metrics", {}),
                "imported_at": datetime.now().isoformat(),
                "source": "colab_training",
                "local_path": str(model_dir)
            }
            
            with open(model_dir / "metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Create usage instructions
            usage_instructions = f"""# {model_name} Usage Instructions

## Model Information
- **Category**: {category}
- **Complexity**: {model_info.get('complexity', 'unknown')}
- **Performance**: {model_info.get('performance', 0):.3f}
- **Training Time**: {model_info.get('training_hours', 0):.2f} hours

## Usage Example
```python
# Load the model
from models.base_models import load_colab_model

model = load_colab_model('{model_name}', '{category}')

# Use the model
result = model.predict(your_data)
```

## Performance Metrics
{json.dumps(model_info.get('metrics', {}), indent=2)}

## Notes
- Trained on Google Colab with GPU acceleration
- Optimized for {category} tasks
- Ready for production use
"""
            
            with open(model_dir / "USAGE.md", 'w') as f:
                f.write(usage_instructions)
            
            return {
                "success": True,
                "model_name": model_name,
                "category": category,
                "local_path": str(model_dir),
                "metadata": metadata
            }
            
        except Exception as e:
            return {
                "success": False,
                "model_name": model_name,
                "error": str(e)
            }
    
    def _update_model_registry(self, imported_models: List[Dict[str, Any]]):
        """به‌روزرسانی رجیستری مدل‌ها"""
        registry_path = self.models_dir / "colab_model_registry.json"
        
        # Load existing registry
        if registry_path.exists():
            with open(registry_path, 'r') as f:
                registry = json.load(f)
        else:
            registry = {
                "version": "1.0",
                "created_at": datetime.now().isoformat(),
                "models": {}
            }
        
        # Add imported models
        for model in imported_models:
            model_name = model["model_name"]
            registry["models"][model_name] = {
                "category": model["category"],
                "local_path": model["local_path"],
                "metadata": model["metadata"],
                "imported_at": datetime.now().isoformat(),
                "status": "ready"
            }
        
        registry["last_updated"] = datetime.now().isoformat()
        registry["total_models"] = len(registry["models"])
        
        # Save updated registry
        with open(registry_path, 'w') as f:
            json.dump(registry, f, indent=2)
        
        print(f"📝 Model registry updated: {len(imported_models)} models added")
    
    def _generate_import_report(self, imported_models: List[Dict[str, Any]], 
                              failed_imports: List[Dict[str, Any]], 
                              package_info: Dict[str, Any]) -> str:
        """تولید گزارش وارد کردن"""
        report = f"""# Colab Model Import Report

## Import Summary
- **Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Package**: {package_info.get('package_name', 'Unknown')}
- **Total Models in Package**: {package_info.get('total_models', 0)}
- **Successfully Imported**: {len(imported_models)}
- **Failed Imports**: {len(failed_imports)}

## Successfully Imported Models

"""
        
        for model in imported_models:
            metadata = model["metadata"]
            report += f"""### {model['model_name']}
- **Category**: {model['category']}
- **Performance**: {metadata.get('performance', 0):.3f}
- **Training Hours**: {metadata.get('training_hours', 0):.2f}
- **Local Path**: `{model['local_path']}`

"""
        
        if failed_imports:
            report += "\n## Failed Imports\n\n"
            for model in failed_imports:
                report += f"- **{model['model_name']}**: {model.get('error', 'Unknown error')}\n"
        
        report += f"""
## Usage Instructions

1. **Load Models**:
   ```python
   from models.base_models import load_colab_model
   model = load_colab_model('model_name', 'category')
   ```

2. **Check Registry**:
   ```python
   import json
   with open('models/trained_models/colab_model_registry.json') as f:
       registry = json.load(f)
   ```

3. **Integration**:
   - Models are ready for use in Pearl-3x7B
   - Check individual USAGE.md files for specific instructions
   - Performance metrics available in metadata.json

## Next Steps
- Test imported models with your data
- Integrate best models into trading system
- Monitor performance in production
"""
        
        # Save report
        report_path = self.colab_models_dir / f"import_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_path, 'w') as f:
            f.write(report)
        
        print(f"📄 Import report saved: {report_path}")
        return report
    
    def list_colab_models(self) -> Dict[str, Any]:
        """لیست مدل‌های وارد شده از Colab"""
        registry_path = self.models_dir / "colab_model_registry.json"
        
        if not registry_path.exists():
            return {"models": {}, "total": 0}
        
        with open(registry_path, 'r') as f:
            registry = json.load(f)
        
        print("📋 COLAB MODELS REGISTRY")
        print("=" * 40)
        print(f"Total Models: {registry.get('total_models', 0)}")
        print(f"Last Updated: {registry.get('last_updated', 'Unknown')}")
        print()
        
        for model_name, model_info in registry.get('models', {}).items():
            metadata = model_info.get('metadata', {})
            print(f"🤖 {model_name}")
            print(f"   Category: {model_info.get('category', 'unknown')}")
            print(f"   Performance: {metadata.get('performance', 0):.3f}")
            print(f"   Status: {model_info.get('status', 'unknown')}")
            print(f"   Path: {model_info.get('local_path', 'unknown')}")
            print()
        
        return registry

def import_colab_models_cli():
    """رابط خط فرمان برای وارد کردن مدل‌ها"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Import Colab trained models")
    parser.add_argument("zip_file", help="Path to Colab models zip file")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    
    args = parser.parse_args()
    
    importer = ColabModelImporter(args.project_root)
    result = importer.import_colab_models(args.zip_file)
    
    if result["success"]:
        print("\n✅ Import completed successfully!")
    else:
        print(f"\n❌ Import failed: {result.get('error', 'Unknown error')}")
        sys.exit(1)

if __name__ == "__main__":
    import_colab_models_cli()
