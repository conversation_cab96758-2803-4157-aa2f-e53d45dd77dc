# 📚 مستندات ساختار پروژه

## 📂 ساختار کلی پروژه
این پروژه یک سیستم معاملاتی هوشمند است که از هوش مصنوعی و یادگیری ماشین برای انجام معاملات خودکار استفاده می‌کند.

## 🗂️ پوشه‌های اصلی

### 📁 api/
مسئول ارائه API‌های سیستم و داشبورد بلادرنگ

#### فایل‌ها:
- `__init__.py`: تنظیمات اولیه ماژول
- `endpoints.py`: تعریف نقاط پایانی API
- `realtime_dashboard.py`: پیاده‌سازی داشبورد بلادرنگ

### 📁 core/
هسته اصلی سیستم شامل کامپوننت‌های پایه

#### فایل‌ها:
- `__init__.py`: تعریف متغیرهای در دسترس بودن کامپوننت‌ها
- `configuration_management.py`: مدیریت تنظیمات سیستم
- `error_handler.py`: مدیریت خطاها و circuit breaker
- `multi_exchange.py`: مدیریت چند صرافی
- `shared_types.py`: انواع داده مشترک
- `simple_config.py`: پیکربندی ساده سیستم
- `simple_database_manager.py`: مدیریت ساده دیتابیس

### 📁 data/
داده‌های تاریخی و ابزارهای مدیریت داده

#### فایل‌ها و پوشه‌ها:
- پوشه‌های نمادهای معاملاتی (EURUSD, BTCUSD و...)
- `fetcher.py`: دریافت داده‌های تاریخی
- `storage/`: ذخیره‌سازی داده‌های پردازش شده

### 📁 env/
محیط معاملاتی و مدیریت پورتفولیو

#### فایل‌ها:
- `__init__.py`: تنظیمات محیط
- `portfolio.py`: مدیریت پورتفولیو
- `trading_env.py`: محیط معاملاتی

### 📁 evaluation/
ارزیابی عملکرد و محاسبه متریک‌ها

#### فایل‌ها:
- `__init__.py`: تنظیمات ماژول
- `comparison.py`: مقایسه استراتژی‌ها
- `metrics.py`: محاسبه متریک‌های عملکرد

### 📁 models/
مدل‌های یادگیری ماشین و هوش مصنوعی

#### فایل‌ها:
- `__init__.py`: تنظیمات ماژول
- `continual_learning.py`: یادگیری مستمر
- `ensemble_model.py`: مدل‌های ترکیبی

### 📁 optimization/
بهینه‌سازی پارامترها و استراتژی‌ها

#### فایل‌ها:
- `__init__.py`: تنظیمات ماژول
- `bayesian.py`: بهینه‌سازی بیزین
- `genetic.py`: الگوریتم ژنتیک

### 📁 portfolio/
مدیریت پورتفولیو و سفارشات

#### فایل‌ها:
- `__init__.py`: تنظیمات ماژول
- `portfolio_manager.py`: مدیریت پورتفولیو

### 📁 tests/
تست‌های واحد و یکپارچگی

#### فایل‌ها:
- `conftest.py`: تنظیمات pytest
- تست‌های مختلف برای هر ماژول

### 📁 utils/
ابزارها و توابع کمکی

#### فایل‌ها:
- `adaptive_margin_control.py`: کنترل حاشیه تطبیقی
- `advanced_reward_system.py`: سیستم پاداش پیشرفته
- `auto_hyperparameter_tuning.py`: تنظیم خودکار پارامترها
- `backtesting_framework.py`: فریم‌ورک بک‌تست
- `federated_learning_system.py`: یادگیری فدرال
- `genetic_strategy_evolution.py`: تکامل ژنتیکی استراتژی‌ها
- `market_regime_detector.py`: تشخیص رژیم بازار
- `risk_manager.py`: مدیریت ریسک
- `sentiment_analyzer.py`: تحلیل احساسات
- و سایر ابزارهای کمکی...

## 📄 فایل‌های اصلی در ریشه پروژه

### 🚀 main_new.py
فایل اصلی اجرای سیستم که شامل:
- راه‌اندازی کامپوننت‌ها
- مدیریت چرخه حیات سیستم
- هماهنگی بین ماژول‌ها

### ⚙️ config.yaml
تنظیمات سیستم شامل:
- پیکربندی معاملات
- تنظیمات صرافی‌ها
- پارامترهای مدل‌ها
- تنظیمات محیط

### 🔒 PROXY.json
تنظیمات پروکسی برای اتصال به اینترنت

## 🔄 جریان کار سیستم

1. **راه‌اندازی**:
   - بارگذاری تنظیمات
   - راه‌اندازی دیتابیس
   - راه‌اندازی مدیریت خطا

2. **آماده‌سازی داده**:
   - دریافت داده‌های تاریخی
   - پردازش و نرمال‌سازی
   - ذخیره در دیتابیس

3. **آموزش مدل‌ها**:
   - تکامل استراتژی‌ها
   - بهینه‌سازی پارامترها
   - یادگیری فدرال

4. **معاملات**:
   - تشخیص سیگنال‌ها
   - مدیریت ریسک
   - اجرای سفارشات

5. **نظارت و گزارش**:
   - پایش عملکرد
   - به‌روزرسانی مدل‌ها
   - تولید گزارش‌ها

## 🔗 وابستگی‌های اصلی

- `pytest`, `coverage`: تست و پوشش کد
- `backtrader`, `zipline`: بک‌تست
- `pydantic`: اعتبارسنجی تنظیمات
- `sqlalchemy`: مدیریت دیتابیس
- `mlflow`: نسخه‌بندی مدل
- `evidently`, `whylogs`: نظارت بر مدل
- `shap`, `lime`: توضیح‌پذیری هوش مصنوعی
- `optuna`: تنظیم هایپرپارامتر
- `asyncio`: برنامه‌نویسی ناهمگام
- `numpy`, `pandas`: پردازش داده

## 🎯 نقاط قوت سیستم

1. **معماری ماژولار**:
   - جداسازی مسئولیت‌ها
   - قابلیت توسعه آسان
   - تست‌پذیری بالا

2. **مقیاس‌پذیری**:
   - پشتیبانی از چند صرافی
   - یادگیری توزیع شده
   - مدیریت همزمان چند استراتژی

3. **انعطاف‌پذیری**:
   - قابلیت سفارشی‌سازی استراتژی‌ها
   - تنظیم خودکار پارامترها
   - سازگاری با شرایط مختلف بازار

4. **قابلیت اطمینان**:
   - مدیریت خطای پیشرفته
   - پشتیبان‌گیری خودکار
   - نظارت بلادرنگ

5. **هوشمندی**:
   - یادگیری مستمر
   - تحلیل احساسات
   - تشخیص رژیم بازار 