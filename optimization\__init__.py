"""
🎯 Optimization Package - Refactored for v2.0
پکیج بهینه‌سازی - بازسازی شده برای نسخه 2.0

این پکیج شامل الگوریتم‌های بهینه‌سازی سازگار با معماری جدید است
"""

# Import from core
from core.base import BaseComponent
from core.logger import get_logger
from core.config import get_config
from core.exceptions import TradingSystemError, ValidationError
from core.utils import monitor_performance

# Legacy imports - maintained for backward compatibility
try:
    from .bayesian import (
        BayesianOptimizer,
        BayesianSearch,
        GaussianProcessOptimizer
    )
except ImportError:
    pass

try:
    from .genetic import (
        GeneticOptimizer,
        GeneticAlgorithm,
        EvolutionaryOptimizer
    )
except ImportError:
    pass

try:
    from .grid_search import (
        GridSearchOptimizer,
        RandomSearchOptimizer,
        HyperparameterTuner
    )
except ImportError:
    pass

# New optimization classes compatible with v2.0
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import numpy as np
from dataclasses import dataclass

@dataclass
class OptimizationResult:
    """نتیجه بهینه‌سازی"""
    algorithm: str
    best_params: Dict[str, Any]
    best_score: float
    iterations: int
    convergence_time: float
    search_space: Dict[str, Any]
    all_results: List[Dict[str, Any]]
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        return {
            "algorithm": self.algorithm,
            "best_params": self.best_params,
            "best_score": self.best_score,
            "iterations": self.iterations,
            "convergence_time": self.convergence_time,
            "search_space": self.search_space,
            "results_count": len(self.all_results)
        }

class OptimizationEngine(BaseComponent):
    """موتور بهینه‌سازی نسخه 2.0"""
    
    def __init__(self, name: str = "optimization_engine", config: Dict[str, Any] = None):
        super().__init__(name, config)
        
        self.config = config or {}
        self.optimizers = {}
        self.results = []
        
        # Optimization settings
        self.max_iterations = self.config.get("max_iterations", 100)
        self.convergence_threshold = self.config.get("convergence_threshold", 1e-6)
        self.parallel_jobs = self.config.get("parallel_jobs", 1)
        
        self.logger = get_logger(__name__)
        
        # Initialize BaseComponent status variables
        self._initialized = False
        self._running = False
    
    def initialize(self) -> bool:
        """مقداردهی اولیه"""
        try:
            # Initialize default optimizers
            self._initialize_default_optimizers()
            
            self.logger.info("Optimization engine initialized")
            self._initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize optimization engine: {e}")
            return False
    
    def start(self) -> bool:
        """شروع موتور بهینه‌سازی"""
        if not self._initialized:
            if not self.initialize():
                return False
        
        self._running = True
        self.logger.info("Optimization engine started")
        return True
    
    def stop(self) -> bool:
        """توقف موتور بهینه‌سازی"""
        try:
            self._running = False
            self.logger.info("Optimization engine stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping optimization engine: {e}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت موتور بهینه‌سازی"""
        return {
            "initialized": self._initialized,
            "running": self._running,
            "optimizers_count": len(self.optimizers),
            "results_count": len(self.results),
            "max_iterations": self.max_iterations
        }
    
    def register_optimizer(self, name: str, optimizer: Callable):
        """ثبت بهینه‌ساز جدید"""
        self.optimizers[name] = optimizer
        self.logger.info(f"Optimizer registered: {name}")
    
    def optimize(self, objective_function: Callable, search_space: Dict[str, Any], 
                algorithm: str = "bayesian", **kwargs) -> OptimizationResult:
        """بهینه‌سازی تابع هدف"""
        if algorithm not in self.optimizers:
            raise ValueError(f"Optimizer not found: {algorithm}")
        
        optimizer = self.optimizers[algorithm]
        start_time = datetime.now()
        
        try:
            # Run optimization
            result = optimizer(
                objective_function=objective_function,
                search_space=search_space,
                max_iterations=self.max_iterations,
                **kwargs
            )
            
            # Calculate convergence time
            convergence_time = (datetime.now() - start_time).total_seconds()
            
            # Create optimization result
            optimization_result = OptimizationResult(
                algorithm=algorithm,
                best_params=result.get("best_params", {}),
                best_score=result.get("best_score", 0.0),
                iterations=result.get("iterations", 0),
                convergence_time=convergence_time,
                search_space=search_space,
                all_results=result.get("all_results", [])
            )
            
            # Store result
            self.results.append(optimization_result)
            
            # Log result
            self.logger.info(f"Optimization completed: {algorithm} - Score: {optimization_result.best_score:.4f}")
            
            return optimization_result
            
        except Exception as e:
            self.logger.error(f"Optimization failed: {algorithm} - {e}")
            
            # Create error result
            error_result = OptimizationResult(
                algorithm=algorithm,
                best_params={},
                best_score=0.0,
                iterations=0,
                convergence_time=(datetime.now() - start_time).total_seconds(),
                search_space=search_space,
                all_results=[]
            )
            
            self.results.append(error_result)
            return error_result
    
    def _initialize_default_optimizers(self):
        """مقداردهی اولیه بهینه‌سازهای پیش‌فرض"""
        # Bayesian optimization
        self.register_optimizer("bayesian", self._bayesian_optimizer)
        
        # Genetic algorithm
        self.register_optimizer("genetic", self._genetic_optimizer)
        
        # Grid search
        self.register_optimizer("grid_search", self._grid_search_optimizer)
        
        # Random search
        self.register_optimizer("random_search", self._random_search_optimizer)
    
    def _bayesian_optimizer(self, objective_function: Callable, search_space: Dict[str, Any], 
                           max_iterations: int = 100, **kwargs) -> Dict[str, Any]:
        """بهینه‌سازی بیزی"""
        try:
            # Simplified Bayesian optimization
            best_params = {}
            best_score = float('-inf')
            all_results = []
            
            for iteration in range(max_iterations):
                # Generate candidate parameters
                params = self._generate_candidate_params(search_space)
                
                # Evaluate objective function
                score = objective_function(params)
                
                # Update best
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                
                # Store result
                all_results.append({
                    "iteration": iteration,
                    "params": params,
                    "score": score
                })
                
                # Check convergence
                if iteration > 10:
                    recent_scores = [r["score"] for r in all_results[-10:]]
                    if max(recent_scores) - min(recent_scores) < self.convergence_threshold:
                        break
            
            return {
                "best_params": best_params,
                "best_score": best_score,
                "iterations": iteration + 1,
                "all_results": all_results
            }
            
        except Exception as e:
            self.logger.error(f"Bayesian optimization failed: {e}")
            return {
                "best_params": {},
                "best_score": 0.0,
                "iterations": 0,
                "all_results": []
            }
    
    def _genetic_optimizer(self, objective_function: Callable, search_space: Dict[str, Any], 
                          max_iterations: int = 100, population_size: int = 50, **kwargs) -> Dict[str, Any]:
        """الگوریتم ژنتیک"""
        try:
            # Initialize population
            population = [self._generate_candidate_params(search_space) for _ in range(population_size)]
            
            best_params = {}
            best_score = float('-inf')
            all_results = []
            
            for generation in range(max_iterations):
                # Evaluate population
                fitness_scores = []
                for individual in population:
                    score = objective_function(individual)
                    fitness_scores.append(score)
                    
                    # Update best
                    if score > best_score:
                        best_score = score
                        best_params = individual.copy()
                
                # Store generation results
                all_results.append({
                    "generation": generation,
                    "best_score": max(fitness_scores),
                    "avg_score": np.mean(fitness_scores),
                    "population_size": len(population)
                })
                
                # Selection, crossover, and mutation
                population = self._evolve_population(population, fitness_scores, search_space)
                
                # Check convergence
                if generation > 10:
                    recent_best = [r["best_score"] for r in all_results[-10:]]
                    if max(recent_best) - min(recent_best) < self.convergence_threshold:
                        break
            
            return {
                "best_params": best_params,
                "best_score": best_score,
                "iterations": generation + 1,
                "all_results": all_results
            }
            
        except Exception as e:
            self.logger.error(f"Genetic optimization failed: {e}")
            return {
                "best_params": {},
                "best_score": 0.0,
                "iterations": 0,
                "all_results": []
            }
    
    def _grid_search_optimizer(self, objective_function: Callable, search_space: Dict[str, Any], 
                              max_iterations: int = 100, **kwargs) -> Dict[str, Any]:
        """جستجوی شبکه‌ای"""
        try:
            # Generate grid points
            grid_points = self._generate_grid_points(search_space, max_iterations)
            
            best_params = {}
            best_score = float('-inf')
            all_results = []
            
            for i, params in enumerate(grid_points):
                # Evaluate objective function
                score = objective_function(params)
                
                # Update best
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                
                # Store result
                all_results.append({
                    "iteration": i,
                    "params": params,
                    "score": score
                })
            
            return {
                "best_params": best_params,
                "best_score": best_score,
                "iterations": len(grid_points),
                "all_results": all_results
            }
            
        except Exception as e:
            self.logger.error(f"Grid search optimization failed: {e}")
            return {
                "best_params": {},
                "best_score": 0.0,
                "iterations": 0,
                "all_results": []
            }
    
    def _random_search_optimizer(self, objective_function: Callable, search_space: Dict[str, Any], 
                                max_iterations: int = 100, **kwargs) -> Dict[str, Any]:
        """جستجوی تصادفی"""
        try:
            best_params = {}
            best_score = float('-inf')
            all_results = []
            
            for iteration in range(max_iterations):
                # Generate random parameters
                params = self._generate_candidate_params(search_space)
                
                # Evaluate objective function
                score = objective_function(params)
                
                # Update best
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                
                # Store result
                all_results.append({
                    "iteration": iteration,
                    "params": params,
                    "score": score
                })
            
            return {
                "best_params": best_params,
                "best_score": best_score,
                "iterations": max_iterations,
                "all_results": all_results
            }
            
        except Exception as e:
            self.logger.error(f"Random search optimization failed: {e}")
            return {
                "best_params": {},
                "best_score": 0.0,
                "iterations": 0,
                "all_results": []
            }
    
    def _generate_candidate_params(self, search_space: Dict[str, Any]) -> Dict[str, Any]:
        """تولید پارامترهای کاندید"""
        params = {}
        
        for param_name, param_config in search_space.items():
            if isinstance(param_config, dict):
                param_type = param_config.get("type", "float")
                
                if param_type == "float":
                    min_val = param_config.get("min", 0.0)
                    max_val = param_config.get("max", 1.0)
                    params[param_name] = np.random.uniform(min_val, max_val)
                
                elif param_type == "int":
                    min_val = param_config.get("min", 0)
                    max_val = param_config.get("max", 100)
                    params[param_name] = np.random.randint(min_val, max_val + 1)
                
                elif param_type == "categorical":
                    choices = param_config.get("choices", [])
                    if choices:
                        params[param_name] = np.random.choice(choices)
                
                elif param_type == "bool":
                    params[param_name] = np.random.choice([True, False])
            
            else:
                # Simple list of values
                if isinstance(param_config, list):
                    params[param_name] = np.random.choice(param_config)
        
        return params
    
    def _evolve_population(self, population: List[Dict[str, Any]], fitness_scores: List[float], 
                          search_space: Dict[str, Any]) -> List[Dict[str, Any]]:
        """تکامل جمعیت"""
        # Simple evolutionary operations
        new_population = []
        
        # Keep best individuals (elitism)
        sorted_indices = sorted(range(len(fitness_scores)), key=lambda i: fitness_scores[i], reverse=True)
        elite_size = max(1, len(population) // 10)
        
        for i in range(elite_size):
            new_population.append(population[sorted_indices[i]])
        
        # Generate new individuals
        while len(new_population) < len(population):
            # Simple mutation
            parent = population[np.random.choice(sorted_indices[:len(population)//2])]
            child = self._mutate_individual(parent, search_space)
            new_population.append(child)
        
        return new_population
    
    def _mutate_individual(self, individual: Dict[str, Any], search_space: Dict[str, Any]) -> Dict[str, Any]:
        """جهش فرد"""
        mutated = individual.copy()
        
        # Randomly mutate some parameters
        for param_name in mutated:
            if np.random.random() < 0.1:  # 10% mutation rate
                if param_name in search_space:
                    param_config = search_space[param_name]
                    
                    if isinstance(param_config, dict):
                        param_type = param_config.get("type", "float")
                        
                        if param_type == "float":
                            min_val = param_config.get("min", 0.0)
                            max_val = param_config.get("max", 1.0)
                            mutated[param_name] = np.random.uniform(min_val, max_val)
                        
                        elif param_type == "int":
                            min_val = param_config.get("min", 0)
                            max_val = param_config.get("max", 100)
                            mutated[param_name] = np.random.randint(min_val, max_val + 1)
                        
                        elif param_type == "categorical":
                            choices = param_config.get("choices", [])
                            if choices:
                                mutated[param_name] = np.random.choice(choices)
                        
                        elif param_type == "bool":
                            mutated[param_name] = not mutated[param_name]
        
        return mutated
    
    def _generate_grid_points(self, search_space: Dict[str, Any], max_points: int) -> List[Dict[str, Any]]:
        """تولید نقاط شبکه"""
        # Simple grid generation
        grid_points = []
        
        # Calculate grid size per dimension
        dims = len(search_space)
        points_per_dim = max(2, int(max_points ** (1/dims)))
        
        # Generate grid for each parameter
        param_grids = {}
        
        for param_name, param_config in search_space.items():
            if isinstance(param_config, dict):
                param_type = param_config.get("type", "float")
                
                if param_type == "float":
                    min_val = param_config.get("min", 0.0)
                    max_val = param_config.get("max", 1.0)
                    param_grids[param_name] = np.linspace(min_val, max_val, points_per_dim)
                
                elif param_type == "int":
                    min_val = param_config.get("min", 0)
                    max_val = param_config.get("max", 100)
                    param_grids[param_name] = np.linspace(min_val, max_val, points_per_dim, dtype=int)
                
                elif param_type == "categorical":
                    choices = param_config.get("choices", [])
                    param_grids[param_name] = choices
                
                elif param_type == "bool":
                    param_grids[param_name] = [True, False]
        
        # Generate all combinations (simplified)
        import itertools
        
        param_names = list(param_grids.keys())
        param_values = list(param_grids.values())
        
        for combination in itertools.product(*param_values):
            if len(grid_points) >= max_points:
                break
            
            point = dict(zip(param_names, combination))
            grid_points.append(point)
        
        return grid_points

# Legacy compatibility wrapper
class LegacyOptimizer:
    """بهینه‌ساز قدیمی برای backward compatibility"""
    
    def __init__(self):
        self.optimization_engine = OptimizationEngine("legacy_optimizer")
        self.optimization_engine.initialize()
    
    def optimize(self, objective_function, search_space, algorithm="bayesian", **kwargs):
        """بهینه‌سازی"""
        return self.optimization_engine.optimize(objective_function, search_space, algorithm, **kwargs)

# Create global instances
optimization_engine = OptimizationEngine("global_optimization_engine")
legacy_optimizer = LegacyOptimizer()

# Export all available functions and classes
__all__ = [
    # New classes
    "OptimizationResult",
    "OptimizationEngine",
    "optimization_engine",
    
    # Base classes
    "BaseComponent"
]

# Version info
__version__ = "2.0.0"
__author__ = "Trading System Team"

# Initialize optimization package
def initialize_optimization_package():
    """مقداردهی اولیه پکیج بهینه‌سازی"""
    logger = get_logger(__name__)
    
    try:
        # Initialize optimization engine
        optimization_engine.initialize()
        
        logger.info("✅ Optimization package initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Optimization initialization failed: {e}")
        return False

# Auto-initialize when imported
if __name__ != "__main__":
    initialize_optimization_package()
