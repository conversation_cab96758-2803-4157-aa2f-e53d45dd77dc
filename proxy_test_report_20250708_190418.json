{"available": 21, "loadable": 8, "failed": 21, "total": 42, "details": {"amazon/chronos-t5-large": {"available": true, "downloads": 221867, "likes": 150, "size": 708963328, "library": "transformers", "tags": ["transformers", "safetensors", "t5"], "last_modified": "2025-02-17 10:43:18+00:00"}, "amazon/chronos-t5-small": {"available": true, "downloads": 57221279, "likes": 111, "size": 46154240, "library": "transformers", "tags": ["transformers", "safetensors", "t5"], "last_modified": "2025-02-17 10:42:38+00:00", "loadable": true, "test_result": "Model exists but requires chronos library", "method": "existence_check"}, "google/timesfm-1.0-200m": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "Salesforce/moirai-1.0-R-base": {"available": true, "downloads": 2577, "likes": 30, "size": 91357728, "library": "transformers", "tags": ["transformers", "safetensors", "time series"], "last_modified": "2025-01-21 09:10:49+00:00"}, "amazon/chronos-t5-tiny": {"available": true, "downloads": 861021, "likes": 110, "size": 8394496, "library": "transformers", "tags": ["transformers", "safetensors", "t5"], "last_modified": "2025-02-17 10:42:03+00:00", "loadable": true, "test_result": "Model exists but requires chronos library", "method": "existence_check"}, "amazon/chronos-t5-mini": {"available": true, "downloads": 98616, "likes": 16, "size": 20456192, "library": "transformers", "tags": ["transformers", "safetensors", "t5"], "last_modified": "2025-02-17 10:42:28+00:00"}, "ProsusAI/finbert": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "ElKulako/cryptobert": {"available": true, "downloads": 218106, "likes": 166, "size": 124648453, "library": "transformers", "tags": ["transformers", "pytorch", "safetensors"], "last_modified": "2025-05-26 11:52:00+00:00"}, "nlptown/bert-base-multilingual-uncased-sentiment": {"available": true, "downloads": 1554325, "likes": 401, "size": 167360261, "library": "transformers", "tags": ["transformers", "pytorch", "tf"], "last_modified": "2025-01-02 20:13:01+00:00", "loadable": false, "error": "local variable 'pipeline' referenced before assignment", "method": "failed"}, "zhayunduo/roberta-base-stocktwits-finetuned": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "cardiffnlp/twitter-roberta-base-sentiment-latest": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "ahmedrachid/FinancialBERT-Sentiment-Analysis": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "StephanAkkerman/FinTwitBERT-sentiment": {"available": true, "downloads": 32132, "likes": 17, "size": 109755651, "library": "transformers", "tags": ["transformers", "safetensors", "bert"], "last_modified": "2024-02-21 11:33:22+00:00"}, "bilalzafar/FinAI-BERT": {"available": true, "downloads": 85, "likes": 0, "size": 109483778, "library": "transformers", "tags": ["transformers", "safetensors", "bert"], "last_modified": "2025-07-04 01:31:14+00:00"}, "FinGPT/fingpt-forecaster_dow30_llama2-7b_lora": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "bavest/fin-llama-33b-merged": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "arcee-ai/Llama-3-SEC-Base": {"available": true, "downloads": 137, "likes": 12, "size": 70553706496, "library": "transformers", "tags": ["transformers", "safetensors", "llama"], "last_modified": "2024-06-19 18:25:12+00:00"}, "amazon/chronos-t5-base": {"available": true, "downloads": 244695, "likes": 32, "size": 201374976, "library": "transformers", "tags": ["transformers", "safetensors", "t5"], "last_modified": "2025-02-17 10:43:04+00:00", "loadable": true, "test_result": "Model exists but requires chronos library", "method": "existence_check"}, "ChanceFocus/finma-7b-nlp": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "microsoft/table-transformer-structure-recognition": {"available": true, "downloads": 1276062, "likes": 195, "size": 28847819, "library": "transformers", "tags": ["transformers", "pytorch", "safetensors"], "last_modified": "2023-09-06 14:50:49+00:00", "loadable": false, "error": "<class 'transformers.models.table_transformer.configuration_table_transformer.TableTransformerConfig'>", "method": "failed"}, "microsoft/layoutlmv3-base": {"available": true, "downloads": 1427887, "likes": 418, "size": 125327490, "library": "transformers", "tags": ["transformers", "pytorch", "tf"], "last_modified": "2024-04-10 14:20:22+00:00", "loadable": true, "test_result": "Tokenizer loaded - vocab size: 50265", "method": "tokenizer_only"}, "ChanceFocus/finma-7b-full": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "google/pix2struct-base": {"available": true, "downloads": 5766, "likes": 76, "size": 282285696, "library": "transformers", "tags": ["transformers", "pytorch", "safetensors"], "last_modified": "2023-12-24 21:14:37+00:00"}, "unstructured-io/layoutlm-invoices": {"available": false, "error": "401 Client Error. (Request ID: Root=1-686d3a69-138eeb4132de477364029c57;656626ef-939d-481a-8632-85b5", "downloads": 0, "likes": 0}, "microsoft/dit-base-finetuned-rvlcdip": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "google/pegasus-xsum": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "allenai/longformer-base-4096": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "facebook/bart-large-cnn": {"available": true, "downloads": 3300419, "likes": 1422, "size": 406290432, "library": "transformers", "tags": ["transformers", "pytorch", "tf"], "last_modified": "2024-02-13 18:02:05+00:00", "loadable": true, "test_result": "Tokenizer loaded - vocab size: 50265", "method": "tokenizer_only"}, "impira/layoutlm-document-qa": {"available": true, "downloads": 26777, "likes": 1119, "size": 127793412, "library": "transformers", "tags": ["transformers", "pytorch", "tf"], "last_modified": "2023-03-18 00:54:24+00:00"}, "microsoft/layoutlmv2-base-uncased": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "philschmid/bart-large-cnn-samsum": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "microsoft/prophetnet-large-uncased": {"available": true, "downloads": 4358, "likes": 5, "size": 391321600, "library": "transformers", "tags": ["transformers", "pytorch", "rust"], "last_modified": "2023-04-27 09:41:22+00:00"}, "EleutherAI/gpt-neo-1.3B": {"available": true, "downloads": 271207, "likes": 309, "size": 1365907456, "library": "transformers", "tags": ["transformers", "pytorch", "jax"], "last_modified": "2024-01-31 20:30:21+00:00", "loadable": true, "test_result": "Tokenizer loaded - vocab size: 50257", "method": "tokenizer_only"}, "microsoft/DialoGPT-medium": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "microsoft/codebert-base": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "huggingface/CodeBERTa-small-v1": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "sentence-transformers/all-MiniLM-L6-v2": {"available": true, "downloads": 92849311, "likes": 3635, "size": 22713728, "library": "sentence-transformers", "tags": ["sentence-transformers", "pytorch", "tf"], "last_modified": "2025-03-06 13:37:44+00:00", "loadable": true, "test_result": "Tokenizer loaded - vocab size: 30522", "method": "tokenizer_only"}, "facebook/opt-1.3b": {"available": false, "error": "'NoneType' object has no attribute 'get'", "downloads": 0, "likes": 0}, "microsoft/DialoGPT-small": {"available": true, "downloads": 226499, "likes": 129, "size": 175620096, "library": "transformers", "tags": ["transformers", "pytorch", "tf"], "last_modified": "2024-02-29 15:48:41+00:00"}, "huggingface/distilbert-base-uncased": {"available": false, "error": "401 Client Error. (Request ID: Root=1-686d3a6a-6c5a416326d0f73b6bfd0eb6;dada63eb-36bf-4061-b1a8-9bf2", "downloads": 0, "likes": 0}, "google/t5-small": {"available": false, "error": "401 Client Error. (Request ID: Root=1-686d3a6a-5b1df0231b0d096b0f90a85d;e500a890-5155-4e69-985a-133a", "downloads": 0, "likes": 0}, "facebook/bart-base": {"available": true, "downloads": 2219779, "likes": 189, "size": 139420416, "library": "transformers", "tags": ["transformers", "pytorch", "tf"], "last_modified": "2022-11-16 23:23:10+00:00", "loadable": true, "test_result": "Tokenizer loaded - vocab size: 50265", "method": "tokenizer_only"}}, "proxy_used": "http://127.0.0.1:10809", "test_time": "2025-07-08T19:04:00.949354"}