#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import logging
from datetime import datetime
import traceback
import numpy as np
import pandas as pd

# تنظیم مسیر پروژه
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

# ماژول‌های اصلی
from training.train_sentiment import Sentiment<PERSON>rainer
from training.train_timeseries import TimeSeriesTrainer
from training.train_rl import RLTrainer
from evaluation.model_comparator import ModelComparator
from utils.anomaly_detection_system import AnomalyDetectionSystem
from core.config import ConfigManager
from core.logger import setup_logging

class FullPipeline:
    def __init__(self):
        # تنظیم لاگینگ
        self.logger = setup_logging()
        
        # مدیریت پیکربندی
        self.config_manager = ConfigManager()
        
        # لیست مدل‌ها برای آموزش
        self.models = {
            'sentiment': SentimentTrainer,
            'timeseries': TimeSeriesTrainer,
            'reinforcement_learning': RLTrainer
        }
        
        # سیستم تشخیص ناهنجاری
        self.anomaly_detector = AnomalyDetectionSystem()
    
    def run_full_pipeline(self):
        """اجرای کامل پایپلاین آموزش و ارزیابی"""
        # نتایج نهایی
        pipeline_results = {
            'timestamp': datetime.now().isoformat(),
            'models': {},
            'overall_status': 'pending'
        }
        
        try:
            # آموزش مدل‌ها
            for model_name, ModelClass in self.models.items():
                self.logger.info(f"شروع آموزش مدل: {model_name}")
                
                try:
                    # ایجاد نمونه مدل
                    model = ModelClass()
                    
                    # آماده‌سازی داده
                    data_prep_success = model.prepare_data()
                    if not data_prep_success:
                        self.logger.warning(f"آماده‌سازی داده برای مدل {model_name} شکست خورد")
                        continue
                    
                    # تشخیص ناهنجاری
                    cleaned_data = self._detect_and_clean_anomalies(model.training_data, model_name)
                    
                    # آموزش مدل
                    train_success = model.train_model()
                    
                    # ارزیابی مدل
                    evaluation_results = model.evaluate_model()
                    
                    # ذخیره‌سازی مدل
                    model_path = model.save_model()
                    
                    # ثبت نتایج
                    pipeline_results['models'][model_name] = {
                        'data_preparation': data_prep_success,
                        'training': train_success,
                        'evaluation': evaluation_results,
                        'model_path': model_path
                    }
                    
                except Exception as model_error:
                    self.logger.error(f"خطا در آموزش مدل {model_name}: {model_error}")
                    pipeline_results['models'][model_name] = {
                        'error': str(model_error),
                        'traceback': traceback.format_exc()
                    }
            
            # مقایسه مدل‌ها
            try:
                comparator = ModelComparator()
                comparison_results = comparator.compare_models()
                pipeline_results['model_comparison'] = comparison_results
            except Exception as comparison_error:
                self.logger.error(f"خطا در مقایسه مدل‌ها: {comparison_error}")
                pipeline_results['model_comparison'] = {
                    'error': str(comparison_error),
                    'traceback': traceback.format_exc()
                }
            
            # تعیین وضعیت کلی
            pipeline_results['overall_status'] = self._determine_overall_status(pipeline_results)
            
            # ذخیره‌سازی نتایج
            self._save_pipeline_results(pipeline_results)
            
            return pipeline_results
        
        except Exception as e:
            self.logger.critical(f"خطای کلی در اجرای پایپلاین: {e}")
            pipeline_results['overall_status'] = 'failed'
            pipeline_results['critical_error'] = {
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            self._save_pipeline_results(pipeline_results)
            return pipeline_results
    
    def _detect_and_clean_anomalies(self, data, model_type):
        """تشخیص و حذف ناهنجاری‌ها"""
        try:
            # اگر data یک tuple است، آن را به DataFrame تبدیل کن
            if isinstance(data, tuple):
                # احتمالاً (X, y) است
                if len(data) == 2 and isinstance(data[0], np.ndarray):
                    # تبدیل به DataFrame
                    X, y = data
                    if len(X.shape) == 3:  # (samples, sequence_length, features)
                        # تبدیل به شکل 2D
                        X_2d = X.reshape(X.shape[0], -1)
                        feature_names = [f'feature_{i}' for i in range(X_2d.shape[1])]
                        data_df = pd.DataFrame(X_2d, columns=feature_names)
                        data_df['target'] = y
                    else:
                        data_df = pd.DataFrame(X)
                        data_df['target'] = y
                else:
                    self.logger.warning(f"Unknown tuple format for {model_type}, skipping anomaly detection")
                    return data
            else:
                data_df = data
            
            normal_data, anomalous_data = self.anomaly_detector.detect_anomalies(data_df, model_type)
            
            if len(anomalous_data) > 0:
                self.logger.info(f"تعداد نمونه‌های ناهنجار حذف شده برای مدل {model_type}: {len(anomalous_data)}")
            
            return normal_data
        except Exception as e:
            self.logger.warning(f"خطا در تشخیص ناهنجاری برای مدل {model_type}: {e}")
            return data
    
    def _determine_overall_status(self, results):
        """تعیین وضعیت کلی پایپلاین"""
        if not results['models']:
            return 'failed'
        
        statuses = [model_info.get('training', False) for model_info in results['models'].values()]
        
        if all(statuses):
            return 'success'
        elif any(statuses):
            return 'partial_success'
        else:
            return 'failed'
    
    def _save_pipeline_results(self, results):
        """ذخیره‌سازی نتایج پایپلاین"""
        try:
            # ایجاد پوشه نتایج اگر وجود نداشته باشد
            results_dir = os.path.join(project_root, 'results')
            os.makedirs(results_dir, exist_ok=True)
            
            # نام فایل با برچسب زمان
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = os.path.join(results_dir, f'pipeline_results_{timestamp}.json')
            
            # ذخیره‌سازی نتایج
            import json
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=4)
            
            self.logger.info(f"نتایج پایپلاین در {results_file} ذخیره شد")
        
        except Exception as e:
            self.logger.error(f"خطا در ذخیره‌سازی نتایج: {e}")

def main():
    # اجرای پایپلاین
    pipeline = FullPipeline()
    results = pipeline.run_full_pipeline()
    
    # چاپ نتایج
    print("نتایج پایپلاین:")
    print(f"وضعیت کلی: {results.get('overall_status', 'نامشخص')}")
    print("جزئیات مدل‌ها:")
    for model_name, model_info in results.get('models', {}).items():
        print(f"- {model_name}: {model_info}")

if __name__ == '__main__':
    main() 