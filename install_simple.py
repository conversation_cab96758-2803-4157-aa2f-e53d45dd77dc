#!/usr/bin/env python3
"""
🔧 نصب ساده سیستم مدل‌های مالی
فقط ضروری‌ها - بدون پیچیدگی

استفاده:
python install_simple.py
"""

import subprocess
import sys
import os

def install_package(package):
    """نصب پکیج"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} نصب شد")
        return True
    except:
        print(f"❌ خطا در نصب {package}")
        return False

def main():
    print("🚀 نصب سیستم مالی ساده")
    print("=" * 30)
    
    # پکیج‌های ضروری
    packages = [
        "requests",
        "transformers", 
        "torch"
    ]
    
    print("📦 نصب پکیج‌های ضروری...")
    
    for package in packages:
        install_package(package)
    
    # تست
    print("\n🧪 تست سیستم...")
    
    try:
        exec(open("simple_financial_models.py").read())
        print("✅ همه چیز آماده!")
    except Exception as e:
        print(f"❌ خطا: {e}")
    
    print("\n🎯 برای استفاده:")
    print("python simple_financial_models.py")

if __name__ == "__main__":
    main() 