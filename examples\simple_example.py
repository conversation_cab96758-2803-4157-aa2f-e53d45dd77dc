#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📝 Simple Trading System Example
مثال ساده سیستم معاملاتی
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# اضافه کردن مسیر پروژه
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# تنظیم logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleTradingExample:
    """مثال ساده سیستم معاملاتی"""
    
    def __init__(self):
        self.balance = 10000.0
        self.positions = {}
        self.trade_history = []
        
    def load_sample_data(self) -> pd.DataFrame:
        """بارگذاری داده‌های نمونه"""
        # تولید داده‌های نمونه
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='1H')
        np.random.seed(42)
        
        # شبیه‌سازی قیمت با random walk
        price_changes = np.random.normal(0, 0.001, len(dates))
        prices = 1.2000 + np.cumsum(price_changes)
        
        # تولید OHLCV
        data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            high = price + np.random.uniform(0, 0.005)
            low = price - np.random.uniform(0, 0.005)
            close = price + np.random.uniform(-0.002, 0.002)
            volume = np.random.randint(1000, 10000)
            
            data.append({
                'timestamp': date,
                'open': price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        return pd.DataFrame(data)
    
    def simple_moving_average_strategy(self, data: pd.DataFrame, 
                                     short_window: int = 20, 
                                     long_window: int = 50) -> pd.DataFrame:
        """استراتژی میانگین متحرک ساده"""
        
        # محاسبه میانگین‌های متحرک
        data['sma_short'] = data['close'].rolling(window=short_window).mean()
        data['sma_long'] = data['close'].rolling(window=long_window).mean()
        
        # تولید سیگنال‌ها
        data['signal'] = 0
        data['signal'][short_window:] = np.where(
            data['sma_short'][short_window:] > data['sma_long'][short_window:], 1, 0
        )
        
        # تولید موقعیت‌ها
        data['position'] = data['signal'].diff()
        
        return data
    
    def calculate_returns(self, data: pd.DataFrame) -> pd.DataFrame:
        """محاسبه بازده‌ها"""
        
        # بازده‌های قیمت
        data['price_return'] = data['close'].pct_change()
        
        # بازده‌های استراتژی
        data['strategy_return'] = data['signal'].shift(1) * data['price_return']
        
        # بازده‌های تجمعی
        data['cumulative_price_return'] = (1 + data['price_return']).cumprod()
        data['cumulative_strategy_return'] = (1 + data['strategy_return']).cumprod()
        
        return data
    
    def simulate_trading(self, data: pd.DataFrame):
        """شبیه‌سازی معاملات"""
        
        current_position = 0
        
        for i, row in data.iterrows():
            if pd.isna(row['position']):
                continue
                
            # سیگنال خرید
            if row['position'] == 1 and current_position == 0:
                self.buy_signal(row['timestamp'], row['close'])
                current_position = 1
            
            # سیگنال فروش
            elif row['position'] == -1 and current_position == 1:
                self.sell_signal(row['timestamp'], row['close'])
                current_position = 0
    
    def buy_signal(self, timestamp: datetime, price: float):
        """سیگنال خرید"""
        trade_amount = self.balance * 0.1  # 10% of balance
        shares = trade_amount / price
        
        trade = {
            'timestamp': timestamp,
            'action': 'BUY',
            'price': price,
            'shares': shares,
            'amount': trade_amount,
            'balance_before': self.balance
        }
        
        self.balance -= trade_amount
        self.positions['EURUSD'] = self.positions.get('EURUSD', 0) + shares
        
        trade['balance_after'] = self.balance
        self.trade_history.append(trade)
        
        logger.info(f"BUY: {shares:.2f} shares at {price:.4f}")
    
    def sell_signal(self, timestamp: datetime, price: float):
        """سیگنال فروش"""
        if 'EURUSD' not in self.positions or self.positions['EURUSD'] <= 0:
            return
        
        shares = self.positions['EURUSD']
        trade_amount = shares * price
        
        trade = {
            'timestamp': timestamp,
            'action': 'SELL',
            'price': price,
            'shares': shares,
            'amount': trade_amount,
            'balance_before': self.balance
        }
        
        self.balance += trade_amount
        self.positions['EURUSD'] = 0
        
        trade['balance_after'] = self.balance
        self.trade_history.append(trade)
        
        logger.info(f"SELL: {shares:.2f} shares at {price:.4f}")
    
    def calculate_performance_metrics(self, data: pd.DataFrame) -> dict:
        """محاسبه متریک‌های عملکرد"""
        
        # بازده‌های استراتژی
        strategy_returns = data['strategy_return'].dropna()
        
        if len(strategy_returns) == 0:
            return {"error": "No strategy returns available"}
        
        # محاسبه متریک‌ها
        total_return = data['cumulative_strategy_return'].iloc[-1] - 1
        volatility = strategy_returns.std() * np.sqrt(252 * 24)  # Annualized (hourly data)
        
        # Sharpe Ratio
        excess_returns = strategy_returns - 0.02/252/24  # Risk-free rate
        sharpe_ratio = excess_returns.mean() / strategy_returns.std() if strategy_returns.std() > 0 else 0
        
        # Maximum Drawdown
        cumulative_returns = data['cumulative_strategy_return']
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # Win Rate
        positive_returns = strategy_returns[strategy_returns > 0]
        win_rate = len(positive_returns) / len(strategy_returns) if len(strategy_returns) > 0 else 0
        
        return {
            'total_return': total_return,
            'annualized_volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': len(self.trade_history),
            'final_balance': self.balance
        }
    
    def run_example(self):
        """اجرای مثال کامل"""
        print("🚀 Running Simple Trading Example")
        print("=" * 50)
        
        # بارگذاری داده‌ها
        print("📊 Loading sample data...")
        data = self.load_sample_data()
        print(f"   Loaded {len(data)} data points")
        
        # اعمال استراتژی
        print("🎯 Applying moving average strategy...")
        data = self.simple_moving_average_strategy(data)
        
        # محاسبه بازده‌ها
        print("📈 Calculating returns...")
        data = self.calculate_returns(data)
        
        # شبیه‌سازی معاملات
        print("💰 Simulating trading...")
        self.simulate_trading(data)
        
        # محاسبه عملکرد
        print("📊 Calculating performance metrics...")
        metrics = self.calculate_performance_metrics(data)
        
        # نمایش نتایج
        print("\n📋 Results:")
        print(f"   Initial Balance: ${10000:.2f}")
        print(f"   Final Balance: ${metrics['final_balance']:.2f}")
        print(f"   Total Return: {metrics['total_return']:.2%}")
        print(f"   Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
        print(f"   Max Drawdown: {metrics['max_drawdown']:.2%}")
        print(f"   Win Rate: {metrics['win_rate']:.2%}")
        print(f"   Total Trades: {metrics['total_trades']}")
        
        # ذخیره نتایج
        results = {
            'performance_metrics': metrics,
            'trade_history': self.trade_history,
            'final_data_sample': data.tail(10).to_dict('records')
        }
        
        import json
        with open('simple_trading_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print("\n💾 Results saved to simple_trading_results.json")
        
        return results

def main():
    """اجرای اصلی"""
    try:
        # ایجاد مثال
        example = SimpleTradingExample()
        
        # اجرای مثال
        results = example.run_example()
        
        print("\n🎉 Example completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Error running example: {e}")
        raise

if __name__ == "__main__":
    main()
