"""
Deep Test for Genetic Algorithm
تست عمیق برای بررسی مشکل الگوریتم ژنتیک
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import json

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_genetic_algorithm_fitness():
    """تست عمیق الگوریتم ژنتیک"""
    
    print("=== GENETIC ALGORITHM DEEP TEST ===")
    
    try:
        from utils.genetic_strategy_evolution import GeneticStrategyEvolution, EvolutionConfig, TradingStrategy
        
        # Initialize system
        evolution = GeneticStrategyEvolution("test_genetic_deep.db")
        
        # Create realistic market data
        print("Creating realistic market data...")
        market_data = []
        base_price = 1.1000
        
        for i in range(1000):
            # Add realistic price movement
            price_change = np.random.normal(0, 0.0005)  # Small random movements
            if i > 0:
                # Add some trend
                if i % 100 < 50:  # Uptrend for 50 periods
                    price_change += 0.0001
                else:  # Downtrend for 50 periods
                    price_change -= 0.0001
            
            base_price += price_change
            
            market_data.append({
                'timestamp': datetime.now() - timedelta(hours=1000-i),
                'price': base_price,
                'volume': 1000 + np.random.randint(-200, 200),
                'high': base_price + abs(np.random.normal(0, 0.0002)),
                'low': base_price - abs(np.random.normal(0, 0.0002))
            })
        
        print(f"Market data created: {len(market_data)} points")
        print(f"Price range: {min(d['price'] for d in market_data):.5f} - {max(d['price'] for d in market_data):.5f}")
        
        # Load data
        evolution.load_historical_data(market_data)
        
        # Test individual strategy fitness calculation
        print("\nTesting individual strategy fitness...")
        
        # Create a simple test strategy
        test_strategy = TradingStrategy(
            strategy_id="test_strategy",
            name="Test Strategy",
            parameters={
                'sma_period_short': 10,
                'sma_period_long': 20,
                'rsi_period': 14,
                'stop_loss': 0.02,
                'take_profit': 0.04
            },
            rules=[
                {'type': 'trend_following', 'weight': 1.0},
                {'type': 'risk_management', 'weight': 1.0}
            ],
            fitness_score=0.0,
            generation=0,
            parent_ids=[],
            performance_metrics={},
            created_at=datetime.now()
        )
        
        # Test fitness calculation directly
        fitness_score = evolution._calculate_fitness(test_strategy, market_data[:500])
        print(f"Test strategy fitness: {fitness_score:.6f}")
        
        # Analyze why fitness might be zero
        print("\nAnalyzing fitness calculation...")
        
        # Test with different parameters
        test_strategies = []
        for sma_short in [5, 10, 15]:
            for sma_long in [20, 30, 40]:
                if sma_short < sma_long:
                    strategy = TradingStrategy(
                        strategy_id=f"test_{sma_short}_{sma_long}",
                        name=f"Test SMA({sma_short},{sma_long})",
                        parameters={
                            'sma_period_short': sma_short,
                            'sma_period_long': sma_long,
                            'rsi_period': 14,
                            'stop_loss': 0.01,
                            'take_profit': 0.02
                        },
                        rules=[
                            {'type': 'trend_following', 'weight': 1.0},
                            {'type': 'risk_management', 'weight': 1.0}
                        ],
                        fitness_score=0.0,
                        generation=0,
                        parent_ids=[],
                        performance_metrics={},
                        created_at=datetime.now()
                    )
                    
                    fitness = evolution._calculate_fitness(strategy, market_data[:200])
                    test_strategies.append({
                        'strategy': f"SMA({sma_short},{sma_long})",
                        'fitness': fitness
                    })
                    print(f"  {strategy.strategy_id}: fitness = {fitness:.6f}")
        
        # Find best performing strategy
        best_strategy = max(test_strategies, key=lambda x: x['fitness'])
        print(f"\nBest strategy: {best_strategy['strategy']} with fitness: {best_strategy['fitness']:.6f}")
        
        # Test evolution with smaller population and generations
        print("\nTesting evolution with small population...")
        
        config = EvolutionConfig(
            population_size=5,
            generations=3,
            mutation_rate=0.2,
            crossover_rate=0.8
        )
        
        strategies = evolution.run_evolution(config)
        
        if strategies:
            best_evolved = evolution.get_best_strategy()
            print(f"Evolution completed. Best evolved fitness: {best_evolved.fitness_score:.6f}")
            
            # Analyze the best strategy
            print(f"Best strategy parameters:")
            for key, value in best_evolved.parameters.items():
                print(f"  {key}: {value}")
            
            return {
                'status': 'PASSED',
                'test_fitness': fitness_score,
                'best_manual_fitness': best_strategy['fitness'],
                'best_evolved_fitness': best_evolved.fitness_score,
                'strategies_tested': len(test_strategies),
                'evolution_successful': best_evolved.fitness_score > 0
            }
        else:
            return {
                'status': 'FAILED',
                'error': 'No strategies generated from evolution'
            }
            
    except Exception as e:
        print(f"Error in genetic algorithm test: {e}")
        return {
            'status': 'FAILED',
            'error': str(e)
        }

def test_fitness_components():
    """تست جزئیات محاسبه fitness"""
    
    print("\n=== FITNESS COMPONENTS TEST ===")
    
    try:
        # Create simple test data
        test_data = []
        prices = [1.1000, 1.1010, 1.1005, 1.1015, 1.1020, 1.1018, 1.1025, 1.1030, 1.1028, 1.1035]
        
        for i, price in enumerate(prices):
            test_data.append({
                'timestamp': datetime.now() - timedelta(hours=len(prices)-i),
                'price': price,
                'volume': 1000,
                'high': price + 0.0002,
                'low': price - 0.0002
            })
        
        print(f"Test data: {len(test_data)} points")
        print(f"Prices: {[d['price'] for d in test_data]}")
        
        # Simulate simple trading strategy
        trades = []
        position = None
        
        for i in range(2, len(test_data)):
            current_price = test_data[i]['price']
            prev_price = test_data[i-1]['price']
            
            # Simple strategy: buy if price going up, sell if going down
            if position is None:
                if current_price > prev_price:
                    # Buy
                    position = {
                        'type': 'buy',
                        'entry_price': current_price,
                        'entry_time': i
                    }
            else:
                # Check exit conditions
                if position['type'] == 'buy':
                    profit_pct = (current_price - position['entry_price']) / position['entry_price']
                    
                    if profit_pct >= 0.001 or profit_pct <= -0.001:  # 0.1% profit/loss
                        # Close position
                        trades.append({
                            'entry_price': position['entry_price'],
                            'exit_price': current_price,
                            'profit_pct': profit_pct,
                            'duration': i - position['entry_time']
                        })
                        position = None
        
        print(f"\nTrades generated: {len(trades)}")
        for i, trade in enumerate(trades):
            print(f"  Trade {i+1}: {trade['profit_pct']*100:.2f}% profit")
        
        # Calculate performance metrics
        if trades:
            total_return = sum(trade['profit_pct'] for trade in trades)
            win_trades = [t for t in trades if t['profit_pct'] > 0]
            win_rate = len(win_trades) / len(trades)
            
            print(f"\nPerformance Metrics:")
            print(f"  Total Return: {total_return*100:.2f}%")
            print(f"  Win Rate: {win_rate*100:.1f}%")
            print(f"  Total Trades: {len(trades)}")
            
            # Calculate fitness score
            fitness = total_return * win_rate * len(trades) / 10
            print(f"  Calculated Fitness: {fitness:.6f}")
            
            return {
                'status': 'PASSED',
                'total_return': total_return,
                'win_rate': win_rate,
                'total_trades': len(trades),
                'fitness': fitness
            }
        else:
            print("No trades generated - strategy too conservative")
            return {
                'status': 'FAILED',
                'error': 'No trades generated'
            }
            
    except Exception as e:
        print(f"Error in fitness components test: {e}")
        return {
            'status': 'FAILED',
            'error': str(e)
        }

def main():
    """اجرای تست‌های عمیق"""
    
    print("Starting Deep Genetic Algorithm Test...")
    
    # Test genetic algorithm
    ga_result = test_genetic_algorithm_fitness()
    
    # Test fitness components
    fitness_result = test_fitness_components()
    
    # Generate report
    report = {
        'timestamp': datetime.now().isoformat(),
        'genetic_algorithm_test': ga_result,
        'fitness_components_test': fitness_result
    }
    
    # Convert numpy types to Python types for JSON serialization
    def convert_numpy_types(obj):
        if hasattr(obj, 'item'):
            return obj.item()
        elif isinstance(obj, dict):
            return {k: convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(v) for v in obj]
        else:
            return obj
    
    # Save report
    with open('genetic_test_report.json', 'w') as f:
        json.dump(convert_numpy_types(report), f, indent=2)
    
    print(f"\n=== FINAL ANALYSIS ===")
    print(f"Genetic Algorithm Test: {ga_result['status']}")
    print(f"Fitness Components Test: {fitness_result['status']}")
    
    if ga_result['status'] == 'PASSED':
        print(f"Best evolved fitness: {ga_result.get('best_evolved_fitness', 0):.6f}")
    
    if fitness_result['status'] == 'PASSED':
        print(f"Manual fitness calculation: {fitness_result.get('fitness', 0):.6f}")
    
    print(f"\nReport saved to: genetic_test_report.json")
    
    # Clean up
    try:
        if os.path.exists("test_genetic_deep.db"):
            os.remove("test_genetic_deep.db")
    except:
        pass
    
    return ga_result['status'] == 'PASSED' and fitness_result['status'] == 'PASSED'

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 