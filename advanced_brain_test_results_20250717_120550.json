{"timestamp": "2025-07-17T12:05:50.855422", "available_modules": {"memory_manager": {"success": true, "stats": "MemoryStats(timestamp=datetime.datetime(2025, 7, 17, 12, 5, 48, 807210), total_memory=8105.42578125, used_memory=6386.1328125, available_memory=1719.29296875, memory_percent=78.8, swap_memory=2588.421875, process_memory=1741.8671875, memory_level=<MemoryLevel.HIGH: 'high'>, gc_count=0, active_objects=0)"}, "enhanced_replay": {"success": true, "buffer_size": 1000}, "genetic_evolution": {"success": true}, "continual_learning": {"success": true, "ewc_lambda": 0.4}, "backtesting": {"success": false, "error": "Not available"}}, "brain_decisions": [{"action": "train", "model": "FinBERT", "reasoning": "Selected FinBERT based on priority 1", "confidence": 0.8879623325744243, "expected_performance": 0.7935417958810567}, {"action": "train", "model": "EnhancedDQNAgent", "reasoning": "Selected EnhancedDQNAgent based on priority 1", "confidence": 0.8791150145297981, "expected_performance": 0.8524844282005726}, {"action": "train", "model": "DQN_Agent", "reasoning": "Selected DQN_Agent based on priority 1", "confidence": 0.7736118523423724, "expected_performance": 0.8933200677424252}], "resource_predictions": {"FinBERT": {"memory_mb": 2457.6, "training_time_min": 49.50000000000001, "confidence": 0.765652287965395}, "LSTM_TimeSeries": {"memory_mb": 1228.8, "training_time_min": 33.0, "confidence": 0.8302540750605478}, "DQN_Agent": {"memory_mb": 1440.0, "training_time_min": 66.0, "confidence": 0.7863309848153137}, "PPO_Agent": {"memory_mb": 1320.0, "training_time_min": 60.50000000000001, "confidence": 0.7245784364896686}, "EnhancedDQNAgent": {"memory_mb": 1800.0, "training_time_min": 88.0, "confidence": 0.7947550286279549}}, "integration": {"success": false}, "advanced_features": {"genetic_optimization": {"success": true, "optimized_params": {"learning_rate": 0.001, "batch_size": 64, "hidden_size": 128}}, "continual_learning": {"success": true, "ewc_lambda": 0.4, "replay_buffer_size": 10000}, "enhanced_replay": {"success": true, "buffer_size": 1000, "experiences_added": 10, "batch_size": 5}}}