#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Real-time Dashboard System
سیستم داشبورد زمان واقعی

این ماژول شامل:
- WebSocket برای به‌روزرسانی لحظه‌ای
- ویجت‌های تعاملی سفارشی
- سیستم هشدار پیشرفته
- گزارش‌گیری خودکار
- رابط موبایل responsive
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import uuid
import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.alpha_beta_attribution import AlphaBetaAttributionEngine
from utils.auto_drawdown_control import AutoDrawdownController
from utils.sentiment_analyzer import AdvancedSentimentAnalyzer
from utils.hft_modeling import HFTModelingSystem
from utils.multi_exchange_routing import MultiExchangeRouter
from utils.reward_redistribution import AdvancedRewardRedistributor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AlertSeverity(Enum):
    """شدت هشدار"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class DashboardTheme(Enum):
    """تم داشبورد"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"


@dataclass
class Alert:
    """هشدار داشبورد"""
    id: str
    title: str
    message: str
    severity: AlertSeverity
    timestamp: datetime
    source: str
    acknowledged: bool = False
    auto_dismiss: bool = True
    dismiss_time: Optional[datetime] = None


@dataclass
class DashboardConfig:
    """تنظیمات داشبورد"""
    theme: DashboardTheme = DashboardTheme.DARK
    refresh_interval: int = 1000  # milliseconds
    max_data_points: int = 1000
    enable_notifications: bool = True
    enable_sound_alerts: bool = False
    auto_save_reports: bool = True
    report_interval: int = 3600  # seconds
    
    # Widget settings
    show_performance_chart: bool = True
    show_drawdown_chart: bool = True
    show_alpha_beta_chart: bool = True
    show_sentiment_gauge: bool = True
    show_hft_metrics: bool = True
    show_exchange_routing: bool = True
    
    # Alert settings
    max_alerts: int = 100
    alert_retention_hours: int = 24


class ConnectionManager:
    """مدیریت اتصالات WebSocket"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_preferences: Dict[str, Dict] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """اتصال کلاینت جدید"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"Client {client_id} connected")
    
    def disconnect(self, client_id: str):
        """قطع اتصال کلاینت"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"Client {client_id} disconnected")
    
    async def send_personal_message(self, message: dict, client_id: str):
        """ارسال پیام شخصی"""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                self.disconnect(client_id)
    
    async def broadcast(self, message: dict):
        """پخش پیام به همه کلاینت‌ها"""
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting to {client_id}: {e}")
                disconnected_clients.append(client_id)
        
        # Remove disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    def get_connected_clients(self) -> List[str]:
        """دریافت لیست کلاینت‌های متصل"""
        return list(self.active_connections.keys())


class DataAggregator:
    """تجمیع داده‌ها از سیستم‌های مختلف"""
    
    def __init__(self):
        self.alpha_beta_engine = AlphaBetaAttributionEngine()
        self.drawdown_controller = AutoDrawdownController()
        self.sentiment_analyzer = AdvancedSentimentAnalyzer(['en'], enable_cache=True)
        self.hft_system = HFTModelingSystem(symbols=['EURUSD', 'GBPUSD'])
        self.exchange_router = MultiExchangeRouter(exchanges=[])
        self.reward_system = AdvancedRewardRedistributor()
        
        # Data storage
        self.portfolio_data = []
        self.market_data = []
        self.performance_metrics = {}
        self.alerts = []
        
        # Cache
        self.cache = {}
        self.cache_expiry = {}
    
    async def collect_real_time_data(self) -> Dict[str, Any]:
        """جمع‌آوری داده‌های زمان واقعی"""
        
        current_time = datetime.now()
        
        # Simulate real-time data (در محیط واقعی از API های مختلف دریافت می‌شود)
        portfolio_value = self._simulate_portfolio_value()
        market_data = self._simulate_market_data()
        
        # Update systems
        drawdown_result = self.drawdown_controller.update(
            portfolio_value, current_time, market_data
        )
        
        # Alpha/Beta metrics
        if len(self.portfolio_data) > 1:
            portfolio_returns = pd.Series([p['return'] for p in self.portfolio_data[-50:]])
            market_returns = pd.Series([m['return'] for m in self.market_data[-50:]])
            
            if len(portfolio_returns) > 10:
                try:
                    alpha_beta_result = self.alpha_beta_engine.calculate_alpha_beta(
                        portfolio_returns, market_returns
                    )
                except:
                    alpha_beta_result = None
            else:
                alpha_beta_result = None
        else:
            alpha_beta_result = None
        
        # Sentiment analysis
        sentiment_result = await self._get_market_sentiment()
        
        # HFT metrics
        hft_metrics = self._get_hft_metrics()
        
        # Exchange routing status
        routing_status = self._get_routing_status()
        
        # Aggregate data
        aggregated_data = {
            'timestamp': current_time.isoformat(),
            'portfolio': {
                'value': portfolio_value,
                'daily_return': self.portfolio_data[-1]['return'] if self.portfolio_data else 0,
                'total_return': (portfolio_value / 1000000 - 1) if portfolio_value else 0,
                'volatility': self._calculate_volatility(),
                'sharpe_ratio': self._calculate_sharpe_ratio()
            },
            'drawdown': {
                'current': drawdown_result['current_drawdown'],
                'max': drawdown_result['metrics'].max_drawdown if drawdown_result['metrics'] else 0,
                'duration': drawdown_result['metrics'].drawdown_duration if drawdown_result['metrics'] else 0,
                'brake_active': drawdown_result['brake_active'],
                'recommended_action': drawdown_result['recommended_action']
            },
            'alpha_beta': {
                'alpha': alpha_beta_result.alpha if alpha_beta_result else 0,
                'beta': alpha_beta_result.beta if alpha_beta_result else 1,
                'r_squared': alpha_beta_result.r_squared if alpha_beta_result else 0,
                'information_ratio': alpha_beta_result.information_ratio if alpha_beta_result else 0
            },
            'sentiment': sentiment_result,
            'hft': hft_metrics,
            'routing': routing_status,
            'market': market_data,
            'alerts': [asdict(alert) for alert in self.alerts[-10:]]  # Last 10 alerts
        }
        
        # Store data
        self.portfolio_data.append({
            'timestamp': current_time,
            'value': portfolio_value,
            'return': self.portfolio_data[-1]['return'] if self.portfolio_data else 0
        })
        
        self.market_data.append({
            'timestamp': current_time,
            'return': market_data.get('return', 0),
            'volatility': market_data.get('volatility', 0.2)
        })
        
        # Keep only recent data
        if len(self.portfolio_data) > 1000:
            self.portfolio_data = self.portfolio_data[-1000:]
        if len(self.market_data) > 1000:
            self.market_data = self.market_data[-1000:]
        
        return aggregated_data
    
    def _simulate_portfolio_value(self) -> float:
        """شبیه‌سازی مقدار portfolio (در محیط واقعی از database می‌آید)"""
        if not self.portfolio_data:
            return 1000000.0  # Starting value
        
        # Simulate random walk with slight positive drift
        last_value = self.portfolio_data[-1]['value']
        daily_return = np.random.normal(0.0005, 0.02)  # 0.05% daily return, 2% volatility
        new_value = last_value * (1 + daily_return)
        
        # Store return for later use
        if self.portfolio_data:
            self.portfolio_data[-1]['return'] = daily_return
        
        return new_value
    
    def _simulate_market_data(self) -> Dict[str, Any]:
        """شبیه‌سازی داده‌های بازار"""
        return {
            'return': np.random.normal(0.0003, 0.015),
            'volatility': np.random.normal(0.2, 0.05),
            'volume': np.random.normal(1000000, 200000),
            'bid_ask_spread': np.random.normal(0.001, 0.0005),
            'correlation': np.random.normal(0.6, 0.2)
        }
    
    async def _get_market_sentiment(self) -> Dict[str, Any]:
        """دریافت تحلیل احساسات بازار"""
        
        # Sample news texts (در محیط واقعی از news APIs می‌آید)
        sample_texts = [
            "Market shows strong bullish momentum",
            "Economic indicators suggest positive outlook",
            "Trading volumes increase significantly"
        ]
        
        try:
            sentiments = []
            for text in sample_texts:
                result = self.sentiment_analyzer.analyze(text)
                sentiments.append({
                    'text': text[:50] + '...',
                    'sentiment': result.label,
                    'score': result.score,
                    'confidence': result.confidence
                })
            
            # Aggregate sentiment
            avg_score = np.mean([s['score'] for s in sentiments])
            overall_sentiment = 'positive' if avg_score > 0.6 else 'negative' if avg_score < 0.4 else 'neutral'
            
            return {
                'overall_sentiment': overall_sentiment,
                'average_score': avg_score,
                'confidence': np.mean([s['confidence'] for s in sentiments]),
                'recent_analysis': sentiments[:3]
            }
        
        except Exception as e:
            logger.error(f"Sentiment analysis error: {e}")
            return {
                'overall_sentiment': 'neutral',
                'average_score': 0.5,
                'confidence': 0.0,
                'recent_analysis': []
            }
    
    def _get_hft_metrics(self) -> Dict[str, Any]:
        """دریافت معیارهای HFT"""
        return {
            'latency_ms': np.random.normal(2.5, 0.5),
            'order_fill_rate': np.random.normal(0.95, 0.02),
            'market_impact': np.random.normal(0.001, 0.0002),
            'profit_factor': np.random.normal(1.15, 0.1),
            'active_orders': np.random.randint(50, 200),
            'executed_trades': np.random.randint(100, 500)
        }
    
    def _get_routing_status(self) -> Dict[str, Any]:
        """دریافت وضعیت routing"""
        exchanges = ['Binance', 'Coinbase', 'Kraken', 'Bitfinex']
        
        return {
            'active_exchanges': len(exchanges),
            'total_volume': np.random.normal(5000000, 1000000),
            'best_execution_rate': np.random.normal(0.98, 0.01),
            'arbitrage_opportunities': np.random.randint(5, 25),
            'exchange_status': {
                exchange: {
                    'status': np.random.choice(['online', 'online', 'online', 'degraded']),
                    'latency': np.random.normal(100, 20),
                    'volume_24h': np.random.normal(1000000, 200000)
                }
                for exchange in exchanges
            }
        }
    
    def _calculate_volatility(self) -> float:
        """محاسبه نوسانات"""
        if len(self.portfolio_data) < 10:
            return 0.2
        
        returns = [p['return'] for p in self.portfolio_data[-21:]]
        return np.std(returns) * np.sqrt(252)  # Annualized
    
    def _calculate_sharpe_ratio(self) -> float:
        """محاسبه نسبت شارپ"""
        if len(self.portfolio_data) < 10:
            return 0.0
        
        returns = [p['return'] for p in self.portfolio_data[-252:]]
        if not returns:
            return 0.0
        
        avg_return = np.mean(returns) * 252  # Annualized
        volatility = np.std(returns) * np.sqrt(252)  # Annualized
        
        return (avg_return - 0.02) / volatility if volatility > 0 else 0  # Assuming 2% risk-free rate
    
    def add_alert(self, title: str, message: str, severity: AlertSeverity, source: str):
        """افزودن هشدار جدید"""
        alert = Alert(
            id=str(uuid.uuid4()),
            title=title,
            message=message,
            severity=severity,
            timestamp=datetime.now(),
            source=source
        )
        
        self.alerts.append(alert)
        
        # Keep only recent alerts
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
        
        logger.info(f"Alert added: {title} - {severity.value}")
        return alert


class ReportGenerator:
    """تولید گزارش‌های خودکار"""
    
    def __init__(self, data_aggregator: DataAggregator):
        self.data_aggregator = data_aggregator
        self.reports_dir = "reports"
        os.makedirs(self.reports_dir, exist_ok=True)
    
    async def generate_daily_report(self) -> Dict[str, Any]:
        """تولید گزارش روزانه"""
        
        current_time = datetime.now()
        
        # Get recent data
        if len(self.data_aggregator.portfolio_data) < 2:
            return {"error": "Insufficient data for report"}
        
        # Calculate daily metrics
        today_data = [p for p in self.data_aggregator.portfolio_data 
                     if p['timestamp'].date() == current_time.date()]
        
        if not today_data:
            return {"error": "No data for today"}
        
        daily_return = (today_data[-1]['value'] / today_data[0]['value'] - 1) if len(today_data) > 1 else 0
        daily_volatility = np.std([p['return'] for p in today_data]) if len(today_data) > 1 else 0
        
        # Generate report
        report = {
            'date': current_time.date().isoformat(),
            'summary': {
                'daily_return': daily_return,
                'daily_volatility': daily_volatility,
                'total_trades': len(today_data),
                'portfolio_value': today_data[-1]['value'],
                'alerts_count': len([a for a in self.data_aggregator.alerts 
                                   if a.timestamp.date() == current_time.date()])
            },
            'performance': {
                'best_return': max([p['return'] for p in today_data], default=0),
                'worst_return': min([p['return'] for p in today_data], default=0),
                'avg_return': np.mean([p['return'] for p in today_data]),
                'total_return': daily_return
            },
            'risk_metrics': {
                'volatility': daily_volatility,
                'max_drawdown': 0,  # Would calculate from drawdown controller
                'var_95': np.percentile([p['return'] for p in today_data], 5) if today_data else 0
            }
        }
        
        # Save report
        filename = f"daily_report_{current_time.strftime('%Y%m%d')}.json"
        filepath = os.path.join(self.reports_dir, filename)
        
        try:
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info(f"Daily report saved: {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving daily report: {e}")
        
        return report
    
    async def generate_weekly_report(self) -> Dict[str, Any]:
        """تولید گزارش هفتگی"""
        # Implementation similar to daily report but for weekly data
        return {"status": "Weekly report generated"}
    
    async def generate_monthly_report(self) -> Dict[str, Any]:
        """تولید گزارش ماهانه"""
        # Implementation similar to daily report but for monthly data
        return {"status": "Monthly report generated"}


class RealTimeDashboard:
    """داشبورد اصلی زمان واقعی"""
    
    def __init__(self):
        self.app = FastAPI(title="Trading Dashboard", version="1.0.0")
        self.connection_manager = ConnectionManager()
        self.data_aggregator = DataAggregator()
        self.report_generator = ReportGenerator(self.data_aggregator)
        self.config = DashboardConfig()
        
        self.setup_middleware()
        self.setup_routes()
        self.setup_background_tasks()
    
    def setup_middleware(self):
        """تنظیم middleware"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def setup_routes(self):
        """تنظیم routes"""
        
        @self.app.get("/")
        async def get_dashboard():
            """صفحه اصلی داشبورد"""
            return HTMLResponse(content=self.get_dashboard_html())
        
        @self.app.websocket("/ws/{client_id}")
        async def websocket_endpoint(websocket: WebSocket, client_id: str):
            """WebSocket endpoint"""
            await self.connection_manager.connect(websocket, client_id)
            try:
                while True:
                    # Keep connection alive and handle client messages
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    if message.get('type') == 'config_update':
                        await self.handle_config_update(client_id, message.get('config', {}))
                    elif message.get('type') == 'alert_acknowledge':
                        await self.handle_alert_acknowledge(message.get('alert_id'))
                    
            except WebSocketDisconnect:
                self.connection_manager.disconnect(client_id)
        
        @self.app.get("/api/data")
        async def get_current_data():
            """دریافت داده‌های فعلی"""
            try:
                data = await self.data_aggregator.collect_real_time_data()
                return JSONResponse(content=data)
            except Exception as e:
                logger.error(f"Error getting current data: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/config")
        async def get_config():
            """دریافت تنظیمات"""
            return JSONResponse(content=asdict(self.config))
        
        @self.app.post("/api/config")
        async def update_config(config_data: dict):
            """به‌روزرسانی تنظیمات"""
            try:
                # Update config
                for key, value in config_data.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)
                
                return JSONResponse(content={"status": "success"})
            except Exception as e:
                raise HTTPException(status_code=400, detail=str(e))
        
        @self.app.get("/api/alerts")
        async def get_alerts():
            """دریافت هشدارها"""
            alerts = [asdict(alert) for alert in self.data_aggregator.alerts]
            return JSONResponse(content=alerts)
        
        @self.app.post("/api/alerts/{alert_id}/acknowledge")
        async def acknowledge_alert(alert_id: str):
            """تایید هشدار"""
            await self.handle_alert_acknowledge(alert_id)
            return JSONResponse(content={"status": "success"})
        
        @self.app.get("/api/reports/daily")
        async def get_daily_report():
            """دریافت گزارش روزانه"""
            report = await self.report_generator.generate_daily_report()
            return JSONResponse(content=report)
        
        @self.app.get("/api/reports/weekly")
        async def get_weekly_report():
            """دریافت گزارش هفتگی"""
            report = await self.report_generator.generate_weekly_report()
            return JSONResponse(content=report)
        
        @self.app.get("/api/reports/monthly")
        async def get_monthly_report():
            """دریافت گزارش ماهانه"""
            report = await self.report_generator.generate_monthly_report()
            return JSONResponse(content=report)
        
        @self.app.get("/api/health")
        async def health_check():
            """بررسی سلامت سیستم"""
            return JSONResponse(content={
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "connected_clients": len(self.connection_manager.get_connected_clients()),
                "active_alerts": len([a for a in self.data_aggregator.alerts if not a.acknowledged])
            })
    
    def setup_background_tasks(self):
        """تنظیم وظایف پس‌زمینه"""
        
        @self.app.on_event("startup")
        async def startup_event():
            """راه‌اندازی اولیه"""
            logger.info("Dashboard starting up...")
            
            # Start data collection task
            asyncio.create_task(self.data_collection_task())
            
            # Start report generation task
            if self.config.auto_save_reports:
                asyncio.create_task(self.report_generation_task())
            
            logger.info("Dashboard started successfully")
        
        @self.app.on_event("shutdown")
        async def shutdown_event():
            """خاموش کردن"""
            logger.info("Dashboard shutting down...")
    
    async def data_collection_task(self):
        """وظیفه جمع‌آوری داده‌ها"""
        while True:
            try:
                # Collect real-time data
                data = await self.data_aggregator.collect_real_time_data()
                
                # Check for alerts
                await self.check_and_generate_alerts(data)
                
                # Broadcast to all connected clients
                await self.connection_manager.broadcast({
                    'type': 'data_update',
                    'data': data
                })
                
                # Wait for next update
                await asyncio.sleep(self.config.refresh_interval / 1000)
                
            except Exception as e:
                logger.error(f"Error in data collection task: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def report_generation_task(self):
        """وظیفه تولید گزارش"""
        while True:
            try:
                # Generate daily report
                await self.report_generator.generate_daily_report()
                
                # Wait for next report
                await asyncio.sleep(self.config.report_interval)
                
            except Exception as e:
                logger.error(f"Error in report generation task: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying
    
    async def check_and_generate_alerts(self, data: Dict[str, Any]):
        """بررسی و تولید هشدارها"""
        
        # Check drawdown alerts
        if data['drawdown']['current'] < -0.05:  # 5% drawdown
            severity = AlertSeverity.HIGH if data['drawdown']['current'] < -0.10 else AlertSeverity.MEDIUM
            self.data_aggregator.add_alert(
                "High Drawdown Alert",
                f"Portfolio drawdown: {data['drawdown']['current']:.2%}",
                severity,
                "Drawdown Controller"
            )
        
        # Check performance alerts
        if data['portfolio']['daily_return'] < -0.03:  # 3% daily loss
            self.data_aggregator.add_alert(
                "Daily Loss Alert",
                f"Daily return: {data['portfolio']['daily_return']:.2%}",
                AlertSeverity.MEDIUM,
                "Performance Monitor"
            )
        
        # Check system alerts
        if data['hft']['latency_ms'] > 5.0:  # High latency
            self.data_aggregator.add_alert(
                "High Latency Alert",
                f"HFT latency: {data['hft']['latency_ms']:.1f}ms",
                AlertSeverity.LOW,
                "HFT System"
            )
    
    async def handle_config_update(self, client_id: str, config_data: Dict[str, Any]):
        """مدیریت به‌روزرسانی تنظیمات"""
        try:
            # Update user preferences
            self.connection_manager.user_preferences[client_id] = config_data
            
            # Send confirmation
            await self.connection_manager.send_personal_message(
                {'type': 'config_updated', 'status': 'success'},
                client_id
            )
            
        except Exception as e:
            await self.connection_manager.send_personal_message(
                {'type': 'config_updated', 'status': 'error', 'message': str(e)},
                client_id
            )
    
    async def handle_alert_acknowledge(self, alert_id: str):
        """مدیریت تایید هشدار"""
        for alert in self.data_aggregator.alerts:
            if alert.id == alert_id:
                alert.acknowledged = True
                alert.dismiss_time = datetime.now()
                break
    
    def get_dashboard_html(self) -> str:
        """تولید HTML داشبورد"""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Trading Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .alert-high { border-left: 4px solid #ef4444; }
        .alert-medium { border-left: 4px solid #f59e0b; }
        .alert-low { border-left: 4px solid #10b981; }
        .widget { transition: all 0.3s ease; }
        .widget:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto px-4 py-6">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">Trading Dashboard</h1>
            <div class="flex items-center space-x-4">
                <span id="connection-status" class="px-3 py-1 rounded-full bg-green-500 text-sm">Connected</span>
                <span id="last-update" class="text-gray-400 text-sm">--</span>
            </div>
        </div>

        <!-- Main Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="widget bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-2">Portfolio Value</h3>
                <p id="portfolio-value" class="text-2xl font-bold text-green-400">$0</p>
                <p id="daily-return" class="text-sm text-gray-400">Daily: 0%</p>
            </div>
            
            <div class="widget bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-2">Drawdown</h3>
                <p id="current-drawdown" class="text-2xl font-bold text-red-400">0%</p>
                <p id="max-drawdown" class="text-sm text-gray-400">Max: 0%</p>
            </div>
            
            <div class="widget bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-2">Alpha/Beta</h3>
                <p id="alpha-beta" class="text-2xl font-bold text-blue-400">α: 0 β: 1</p>
                <p id="information-ratio" class="text-sm text-gray-400">IR: 0</p>
            </div>
            
            <div class="widget bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-2">Sentiment</h3>
                <p id="market-sentiment" class="text-2xl font-bold text-yellow-400">Neutral</p>
                <p id="sentiment-score" class="text-sm text-gray-400">Score: 0.5</p>
            </div>
        </div>

        <!-- Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div class="widget bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">Portfolio Performance</h3>
                <canvas id="performance-chart" width="400" height="200"></canvas>
            </div>
            
            <div class="widget bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">Drawdown Chart</h3>
                <canvas id="drawdown-chart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Alerts -->
        <div class="widget bg-gray-800 p-6 rounded-lg mb-6">
            <h3 class="text-lg font-semibold mb-4">Recent Alerts</h3>
            <div id="alerts-container" class="space-y-2">
                <!-- Alerts will be populated here -->
            </div>
        </div>

        <!-- System Status -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="widget bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">HFT Metrics</h3>
                <div class="space-y-2">
                    <p>Latency: <span id="hft-latency" class="text-green-400">0ms</span></p>
                    <p>Fill Rate: <span id="hft-fill-rate" class="text-green-400">0%</span></p>
                    <p>Active Orders: <span id="hft-orders" class="text-blue-400">0</span></p>
                </div>
            </div>
            
            <div class="widget bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">Exchange Routing</h3>
                <div class="space-y-2">
                    <p>Active Exchanges: <span id="active-exchanges" class="text-green-400">0</span></p>
                    <p>Execution Rate: <span id="execution-rate" class="text-green-400">0%</span></p>
                    <p>Arbitrage Ops: <span id="arbitrage-ops" class="text-yellow-400">0</span></p>
                </div>
            </div>
            
            <div class="widget bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">Risk Metrics</h3>
                <div class="space-y-2">
                    <p>Volatility: <span id="portfolio-volatility" class="text-yellow-400">0%</span></p>
                    <p>Sharpe Ratio: <span id="sharpe-ratio" class="text-blue-400">0</span></p>
                    <p>VaR 95%: <span id="var-95" class="text-red-400">0%</span></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // WebSocket connection
        const clientId = 'client_' + Math.random().toString(36).substr(2, 9);
        const ws = new WebSocket(`ws://localhost:8000/ws/${clientId}`);
        
        // Chart instances
        let performanceChart, drawdownChart;
        
        // Data storage
        let portfolioData = [];
        let drawdownData = [];
        
        // WebSocket event handlers
        ws.onopen = function() {
            document.getElementById('connection-status').textContent = 'Connected';
            document.getElementById('connection-status').className = 'px-3 py-1 rounded-full bg-green-500 text-sm';
        };
        
        ws.onclose = function() {
            document.getElementById('connection-status').textContent = 'Disconnected';
            document.getElementById('connection-status').className = 'px-3 py-1 rounded-full bg-red-500 text-sm';
        };
        
        ws.onmessage = function(event) {
            const message = JSON.parse(event.data);
            
            if (message.type === 'data_update') {
                updateDashboard(message.data);
            }
        };
        
        // Initialize charts
        function initCharts() {
            const ctx1 = document.getElementById('performance-chart').getContext('2d');
            performanceChart = new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Portfolio Value',
                        data: [],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: { legend: { labels: { color: 'white' } } },
                    scales: {
                        x: { ticks: { color: 'white' }, grid: { color: 'rgba(255,255,255,0.1)' } },
                        y: { ticks: { color: 'white' }, grid: { color: 'rgba(255,255,255,0.1)' } }
                    }
                }
            });
            
            const ctx2 = document.getElementById('drawdown-chart').getContext('2d');
            drawdownChart = new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Drawdown %',
                        data: [],
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: { legend: { labels: { color: 'white' } } },
                    scales: {
                        x: { ticks: { color: 'white' }, grid: { color: 'rgba(255,255,255,0.1)' } },
                        y: { ticks: { color: 'white' }, grid: { color: 'rgba(255,255,255,0.1)' } }
                    }
                }
            });
        }
        
        // Update dashboard with new data
        function updateDashboard(data) {
            // Update timestamp
            document.getElementById('last-update').textContent = new Date(data.timestamp).toLocaleTimeString();
            
            // Update main metrics
            document.getElementById('portfolio-value').textContent = 
                '$' + data.portfolio.value.toLocaleString(undefined, {maximumFractionDigits: 0});
            document.getElementById('daily-return').textContent = 
                'Daily: ' + (data.portfolio.daily_return * 100).toFixed(2) + '%';
            
            document.getElementById('current-drawdown').textContent = 
                (data.drawdown.current * 100).toFixed(2) + '%';
            document.getElementById('max-drawdown').textContent = 
                'Max: ' + (data.drawdown.max * 100).toFixed(2) + '%';
            
            document.getElementById('alpha-beta').textContent = 
                `α: ${data.alpha_beta.alpha.toFixed(3)} β: ${data.alpha_beta.beta.toFixed(2)}`;
            document.getElementById('information-ratio').textContent = 
                'IR: ' + data.alpha_beta.information_ratio.toFixed(2);
            
            document.getElementById('market-sentiment').textContent = 
                data.sentiment.overall_sentiment.charAt(0).toUpperCase() + data.sentiment.overall_sentiment.slice(1);
            document.getElementById('sentiment-score').textContent = 
                'Score: ' + data.sentiment.average_score.toFixed(2);
            
            // Update HFT metrics
            document.getElementById('hft-latency').textContent = data.hft.latency_ms.toFixed(1) + 'ms';
            document.getElementById('hft-fill-rate').textContent = (data.hft.order_fill_rate * 100).toFixed(1) + '%';
            document.getElementById('hft-orders').textContent = data.hft.active_orders;
            
            // Update routing metrics
            document.getElementById('active-exchanges').textContent = data.routing.active_exchanges;
            document.getElementById('execution-rate').textContent = (data.routing.best_execution_rate * 100).toFixed(1) + '%';
            document.getElementById('arbitrage-ops').textContent = data.routing.arbitrage_opportunities;
            
            // Update risk metrics
            document.getElementById('portfolio-volatility').textContent = (data.portfolio.volatility * 100).toFixed(1) + '%';
            document.getElementById('sharpe-ratio').textContent = data.portfolio.sharpe_ratio.toFixed(2);
            
            // Update charts
            updateCharts(data);
            
            // Update alerts
            updateAlerts(data.alerts);
        }
        
        // Update charts
        function updateCharts(data) {
            const now = new Date(data.timestamp);
            const timeLabel = now.toLocaleTimeString();
            
            // Portfolio chart
            portfolioData.push({x: timeLabel, y: data.portfolio.value});
            if (portfolioData.length > 50) portfolioData.shift();
            
            performanceChart.data.labels = portfolioData.map(d => d.x);
            performanceChart.data.datasets[0].data = portfolioData.map(d => d.y);
            performanceChart.update('none');
            
            // Drawdown chart
            drawdownData.push({x: timeLabel, y: data.drawdown.current * 100});
            if (drawdownData.length > 50) drawdownData.shift();
            
            drawdownChart.data.labels = drawdownData.map(d => d.x);
            drawdownChart.data.datasets[0].data = drawdownData.map(d => d.y);
            drawdownChart.update('none');
        }
        
        // Update alerts
        function updateAlerts(alerts) {
            const container = document.getElementById('alerts-container');
            container.innerHTML = '';
            
            alerts.slice(-5).forEach(alert => {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert-${alert.severity} bg-gray-700 p-3 rounded`;
                alertDiv.innerHTML = `
                    <div class="flex justify-between items-start">
                        <div>
                            <h4 class="font-semibold">${alert.title}</h4>
                            <p class="text-sm text-gray-300">${alert.message}</p>
                            <p class="text-xs text-gray-400">${new Date(alert.timestamp).toLocaleString()}</p>
                        </div>
                        <span class="text-xs bg-gray-600 px-2 py-1 rounded">${alert.severity}</span>
                    </div>
                `;
                container.appendChild(alertDiv);
            });
        }
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
        });
    </script>
</body>
</html>
        """


# Create global dashboard instance
dashboard = RealTimeDashboard()
app = dashboard.app

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 