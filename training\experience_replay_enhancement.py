"""
⚡ Experience Replay Enhancement for Pearl-3x7B RL Training
بهبود Experience Replay برای آموزش یادگیری تقویتی Pearl-3x7B

این فایل شامل:
1. Prioritized Experience Replay (PER)
2. Multi-Step Learning
3. Distributional RL Support
4. Hindsight Experience Replay (HER)
5. Curiosity-Driven Exploration
"""

import os
import sys
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Any, Optional, Tuple, Union
from collections import deque, namedtuple
import random
import heapq
from dataclasses import dataclass
import time
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logger = logging.getLogger(__name__)

# Experience tuple for enhanced replay
Experience = namedtuple('Experience', [
    'state', 'action', 'reward', 'next_state', 'done', 
    'priority', 'timestamp', 'episode_id', 'step_id'
])

@dataclass
class ReplayConfig:
    """پیکربندی Experience Replay"""
    buffer_size: int = 100000
    batch_size: int = 64
    prioritized: bool = True
    alpha: float = 0.6  # Prioritization exponent
    beta: float = 0.4   # Importance sampling exponent
    beta_increment: float = 0.001
    epsilon: float = 1e-6  # Small constant for numerical stability
    multi_step: int = 3  # Multi-step learning
    gamma: float = 0.99  # Discount factor
    curiosity_driven: bool = True
    her_enabled: bool = False  # Hindsight Experience Replay

class SumTree:
    """Sum Tree for Prioritized Experience Replay"""
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.tree = np.zeros(2 * capacity - 1)
        self.data = np.zeros(capacity, dtype=object)
        self.write = 0
        self.n_entries = 0
    
    def _propagate(self, idx: int, change: float):
        """Propagate change through tree"""
        parent = (idx - 1) // 2
        self.tree[parent] += change
        if parent != 0:
            self._propagate(parent, change)
    
    def _retrieve(self, idx: int, s: float):
        """Retrieve sample index"""
        left = 2 * idx + 1
        right = left + 1
        
        if left >= len(self.tree):
            return idx
        
        if s <= self.tree[left]:
            return self._retrieve(left, s)
        else:
            return self._retrieve(right, s - self.tree[left])
    
    def total(self) -> float:
        """Get total priority"""
        return self.tree[0]
    
    def add(self, priority: float, data: Any):
        """Add new experience"""
        idx = self.write + self.capacity - 1
        self.data[self.write] = data
        self.update(idx, priority)
        
        self.write += 1
        if self.write >= self.capacity:
            self.write = 0
        
        if self.n_entries < self.capacity:
            self.n_entries += 1
    
    def update(self, idx: int, priority: float):
        """Update priority"""
        change = priority - self.tree[idx]
        self.tree[idx] = priority
        self._propagate(idx, change)
    
    def get(self, s: float) -> Tuple[int, float, Any]:
        """Get experience by priority"""
        idx = self._retrieve(0, s)
        dataIdx = idx - self.capacity + 1
        return (idx, self.tree[idx], self.data[dataIdx])

class PrioritizedReplayBuffer:
    """⚡ Prioritized Experience Replay Buffer"""
    
    def __init__(self, config: ReplayConfig):
        self.config = config
        self.tree = SumTree(config.buffer_size)
        self.max_priority = 1.0
        self.beta = config.beta
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Multi-step learning
        self.multi_step_buffer = deque(maxlen=config.multi_step)
        
        # Curiosity tracking
        self.state_visits = {}
        self.curiosity_bonus = 0.1
        
        self.logger.info(f"⚡ PrioritizedReplayBuffer initialized with capacity {config.buffer_size}")
    
    def push(self, state: np.ndarray, action: int, reward: float, 
             next_state: np.ndarray, done: bool, episode_id: int = 0, step_id: int = 0):
        """Add experience to buffer"""
        
        # Calculate curiosity bonus
        if self.config.curiosity_driven:
            state_key = self._state_to_key(state)
            visit_count = self.state_visits.get(state_key, 0)
            curiosity_reward = self.curiosity_bonus / (1 + visit_count)
            reward += curiosity_reward
            self.state_visits[state_key] = visit_count + 1
        
        # Multi-step learning
        self.multi_step_buffer.append((state, action, reward, next_state, done))
        
        if len(self.multi_step_buffer) == self.config.multi_step:
            # Calculate multi-step return
            multi_step_reward = 0
            gamma_power = 1
            
            for i, (_, _, r, _, d) in enumerate(self.multi_step_buffer):
                multi_step_reward += gamma_power * r
                gamma_power *= self.config.gamma
                if d:  # Episode ended
                    break
            
            # Get first and last states
            first_state, first_action = self.multi_step_buffer[0][:2]
            last_next_state, last_done = self.multi_step_buffer[-1][3:]
            
            # Create experience
            experience = Experience(
                state=first_state,
                action=first_action,
                reward=multi_step_reward,
                next_state=last_next_state,
                done=last_done,
                priority=self.max_priority,
                timestamp=time.time(),
                episode_id=episode_id,
                step_id=step_id
            )
            
            # Add to tree
            self.tree.add(self.max_priority, experience)
    
    def sample(self, batch_size: Optional[int] = None) -> Tuple[List[Experience], np.ndarray, np.ndarray]:
        """Sample batch with importance sampling"""
        if batch_size is None:
            batch_size = self.config.batch_size
        
        batch = []
        indices = []
        priorities = []
        
        # Sample from tree
        priority_segment = self.tree.total() / batch_size
        
        for i in range(batch_size):
            a = priority_segment * i
            b = priority_segment * (i + 1)
            s = random.uniform(a, b)
            
            idx, priority, experience = self.tree.get(s)
            if experience is not None:
                batch.append(experience)
                indices.append(idx)
                priorities.append(priority)
        
        # Calculate importance sampling weights
        sampling_probabilities = np.array(priorities) / self.tree.total()
        is_weights = np.power(self.tree.n_entries * sampling_probabilities, -self.beta)
        is_weights /= is_weights.max()
        
        # Increment beta
        self.beta = min(1.0, self.beta + self.config.beta_increment)
        
        return batch, np.array(indices), is_weights
    
    def update_priorities(self, indices: np.ndarray, priorities: np.ndarray):
        """Update priorities based on TD errors"""
        for idx, priority in zip(indices, priorities):
            priority = abs(priority) + self.config.epsilon
            self.max_priority = max(self.max_priority, priority)
            self.tree.update(idx, priority)
    
    def _state_to_key(self, state: np.ndarray) -> str:
        """Convert state to hashable key for curiosity tracking"""
        # Simple hash based on state values
        return str(hash(tuple(state.flatten()[:10])))  # Use first 10 elements
    
    def __len__(self) -> int:
        return self.tree.n_entries
    
    def get_stats(self) -> Dict[str, Any]:
        """Get buffer statistics"""
        return {
            "size": len(self),
            "capacity": self.config.buffer_size,
            "max_priority": self.max_priority,
            "beta": self.beta,
            "unique_states_visited": len(self.state_visits),
            "total_priority": self.tree.total()
        }

class CuriosityModule(nn.Module):
    """🧠 Curiosity-driven exploration module"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 256):
        super(CuriosityModule, self).__init__()
        
        # Inverse model (predicts action from state transitions)
        self.inverse_model = nn.Sequential(
            nn.Linear(state_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, action_dim)
        )
        
        # Forward model (predicts next state from current state and action)
        self.forward_model = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, state_dim)
        )
        
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def forward(self, state: torch.Tensor, action: torch.Tensor, next_state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Forward pass"""
        # Inverse model
        state_pair = torch.cat([state, next_state], dim=1)
        predicted_action = self.inverse_model(state_pair)
        
        # Forward model
        action_one_hot = F.one_hot(action.long(), num_classes=predicted_action.size(1)).float()
        state_action = torch.cat([state, action_one_hot], dim=1)
        predicted_next_state = self.forward_model(state_action)
        
        # Intrinsic reward (prediction error)
        intrinsic_reward = F.mse_loss(predicted_next_state, next_state, reduction='none').mean(dim=1)
        
        return predicted_action, predicted_next_state, intrinsic_reward
    
    def compute_curiosity_loss(self, state: torch.Tensor, action: torch.Tensor, next_state: torch.Tensor) -> torch.Tensor:
        """Compute curiosity loss"""
        predicted_action, predicted_next_state, _ = self.forward(state, action, next_state)
        
        # Inverse loss
        inverse_loss = F.cross_entropy(predicted_action, action.long())
        
        # Forward loss
        forward_loss = F.mse_loss(predicted_next_state, next_state)
        
        return inverse_loss + forward_loss

class EnhancedDQNAgent:
    """🚀 Enhanced DQN Agent with advanced experience replay"""
    
    def __init__(self, state_dim: int, action_dim: int, config: ReplayConfig):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Networks
        from training.train_rl import DQNNetwork  # Import from existing file
        self.q_network = DQNNetwork(state_dim, action_dim).to(self.device)
        self.target_network = DQNNetwork(state_dim, action_dim).to(self.device)
        self.target_network.load_state_dict(self.q_network.state_dict())
        
        # Optimizer
        self.optimizer = torch.optim.Adam(self.q_network.parameters(), lr=3e-4)
        
        # Enhanced replay buffer
        self.replay_buffer = PrioritizedReplayBuffer(config)
        
        # Curiosity module
        if config.curiosity_driven:
            self.curiosity_module = CuriosityModule(state_dim, action_dim).to(self.device)
            self.curiosity_optimizer = torch.optim.Adam(self.curiosity_module.parameters(), lr=1e-4)
        else:
            self.curiosity_module = None
        
        # Training stats
        self.training_step = 0
        self.logger = logging.getLogger(self.__class__.__name__)
        
        self.logger.info(f"🚀 EnhancedDQNAgent initialized with {config}")
    
    def select_action(self, state: np.ndarray, epsilon: float = 0.1) -> int:
        """Select action with epsilon-greedy policy"""
        if random.random() < epsilon:
            return random.randint(0, self.action_dim - 1)
        
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            q_values = self.q_network(state_tensor)
            return q_values.argmax().item()
    
    def train_step(self) -> Dict[str, float]:
        """Enhanced training step with prioritized replay"""
        if len(self.replay_buffer) < self.config.batch_size:
            return {}
        
        # Sample batch
        batch, indices, is_weights = self.replay_buffer.sample()
        
        # Convert to tensors
        states = torch.FloatTensor([e.state for e in batch]).to(self.device)
        actions = torch.LongTensor([e.action for e in batch]).to(self.device)
        rewards = torch.FloatTensor([e.reward for e in batch]).to(self.device)
        next_states = torch.FloatTensor([e.next_state for e in batch]).to(self.device)
        dones = torch.BoolTensor([e.done for e in batch]).to(self.device)
        is_weights = torch.FloatTensor(is_weights).to(self.device)
        
        # Current Q values
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # Next Q values (Double DQN)
        with torch.no_grad():
            next_actions = self.q_network(next_states).argmax(1)
            next_q_values = self.target_network(next_states).gather(1, next_actions.unsqueeze(1))
            target_q_values = rewards.unsqueeze(1) + (self.config.gamma ** self.config.multi_step) * next_q_values * (~dones).unsqueeze(1)
        
        # TD errors for priority update
        td_errors = (current_q_values - target_q_values).detach().cpu().numpy().flatten()
        
        # Weighted loss
        loss = (is_weights * F.mse_loss(current_q_values, target_q_values, reduction='none').squeeze()).mean()
        
        # Curiosity loss
        curiosity_loss = 0
        if self.curiosity_module:
            curiosity_loss = self.curiosity_module.compute_curiosity_loss(states, actions, next_states)
            total_loss = loss + 0.1 * curiosity_loss
        else:
            total_loss = loss
        
        # Optimize
        self.optimizer.zero_grad()
        if self.curiosity_module:
            self.curiosity_optimizer.zero_grad()
        
        total_loss.backward()
        
        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), 1.0)
        if self.curiosity_module:
            torch.nn.utils.clip_grad_norm_(self.curiosity_module.parameters(), 1.0)
        
        self.optimizer.step()
        if self.curiosity_module:
            self.curiosity_optimizer.step()
        
        # Update priorities
        self.replay_buffer.update_priorities(indices, td_errors)
        
        # Update target network
        if self.training_step % 100 == 0:
            self.target_network.load_state_dict(self.q_network.state_dict())
        
        self.training_step += 1
        
        return {
            "loss": loss.item(),
            "curiosity_loss": curiosity_loss.item() if isinstance(curiosity_loss, torch.Tensor) else curiosity_loss,
            "mean_q_value": current_q_values.mean().item(),
            "buffer_size": len(self.replay_buffer)
        }

def create_enhanced_replay_config(
    buffer_size: int = 100000,
    prioritized: bool = True,
    multi_step: int = 3,
    curiosity_driven: bool = True
) -> ReplayConfig:
    """Create enhanced replay configuration"""
    return ReplayConfig(
        buffer_size=buffer_size,
        batch_size=64,
        prioritized=prioritized,
        alpha=0.6,
        beta=0.4,
        multi_step=multi_step,
        curiosity_driven=curiosity_driven,
        her_enabled=False
    )
