#!/usr/bin/env python3
"""
🔍 Comprehensive System Check
بررسی جامع وضعیت سیستم
"""

import sys
import os
sys.path.insert(0, '.')

def check_data_loading():
    """بررسی بارگذاری داده‌ها"""
    print("🔍 Checking data loading...")
    
    try:
        # بررسی وجود فایل‌های داده
        data_files = []
        if os.path.exists('data'):
            for root, dirs, files in os.walk('data'):
                for file in files:
                    if file.endswith('.csv'):
                        data_files.append(os.path.join(root, file))
        
        print(f"📊 Found {len(data_files)} data files")
        
        # بررسی بارگذاری داده‌ها
        if data_files:
            import pandas as pd
            sample_file = data_files[0]
            df = pd.read_csv(sample_file)
            print(f"✅ Sample data loaded: {sample_file} ({len(df)} rows)")
            return True
        else:
            print("❌ No data files found")
            return False
            
    except Exception as e:
        print(f"❌ Data loading error: {e}")
        return False

def check_indicators():
    """بررسی اندیکاتورها"""
    print("🔍 Checking indicators...")
    
    try:
        # بررسی فایل‌های اندیکاتور
        indicator_files = []
        if os.path.exists('utils'):
            for file in os.listdir('utils'):
                if 'indicator' in file.lower() or 'technical' in file.lower():
                    indicator_files.append(file)
        
        print(f"📈 Found {len(indicator_files)} indicator files")
        
        # تست بارگذاری اندیکاتور
        try:
            from utils.technical_indicators import TechnicalIndicators
            indicators = TechnicalIndicators()
            available_indicators = indicators.get_available_indicators()
            print(f"✅ Available indicators: {len(available_indicators)}")
            return True
        except ImportError:
            print("⚠️ TechnicalIndicators not available, checking alternatives...")
            return len(indicator_files) > 0
            
    except Exception as e:
        print(f"❌ Indicators check error: {e}")
        return False

def check_strategies():
    """بررسی استراتژی‌ها"""
    print("🔍 Checking strategies...")
    
    try:
        # بررسی فایل‌های استراتژی
        strategy_files = []
        
        # بررسی در دایرکتوری utils
        if os.path.exists('utils'):
            for file in os.listdir('utils'):
                if 'strategy' in file.lower() or 'trading' in file.lower():
                    strategy_files.append(f"utils/{file}")
        
        # بررسی در دایرکتوری strategies
        if os.path.exists('strategies'):
            for file in os.listdir('strategies'):
                if file.endswith('.py'):
                    strategy_files.append(f"strategies/{file}")
        
        print(f"🎯 Found {len(strategy_files)} strategy files")
        
        # تست بارگذاری استراتژی
        try:
            from utils.genetic_strategy_evolution import GeneticStrategyEvolution
            strategy_system = GeneticStrategyEvolution()
            print("✅ Genetic Strategy Evolution available")
            return True
        except ImportError:
            print("⚠️ Advanced strategies not available")
            return len(strategy_files) > 0
            
    except Exception as e:
        print(f"❌ Strategies check error: {e}")
        return False

def check_models():
    """بررسی مدل‌ها"""
    print("🔍 Checking AI models...")
    
    try:
        # بررسی مدل‌های AI
        from models.ensemble_model import EnsembleModel
        from models.continual_learning import ContinualLearningSystem
        
        # تست مدل ensemble
        ensemble = EnsembleModel()
        print("✅ Ensemble Model available")
        
        # تست continual learning
        continual = ContinualLearningSystem()
        print("✅ Continual Learning System available")
        
        return True
        
    except Exception as e:
        print(f"❌ Models check error: {e}")
        return False

def check_backtesting():
    """بررسی بک‌تست"""
    print("🔍 Checking backtesting...")
    
    try:
        from core.backtesting_framework import BacktestingFramework
        
        backtester = BacktestingFramework()
        print("✅ Backtesting Framework available")
        
        # تست بک‌تست ساده
        # این بخش نیاز به داده‌های واقعی دارد
        print("⚠️ Backtesting requires real data for full test")
        return True
        
    except Exception as e:
        print(f"❌ Backtesting check error: {e}")
        return False

def check_optimization():
    """بررسی بهینه‌سازی"""
    print("🔍 Checking optimization...")
    
    try:
        from optimization.bayesian import BayesianOptimizer
        from optimization.genetic import GeneticOptimizer
        
        print("✅ Bayesian Optimizer available")
        print("✅ Genetic Optimizer available")
        
        return True
        
    except Exception as e:
        print(f"❌ Optimization check error: {e}")
        return False

def main():
    """اجرای بررسی جامع سیستم"""
    print("🔍 Comprehensive System Check")
    print("=" * 50)
    
    checks = [
        ("Data Loading", check_data_loading),
        ("Indicators", check_indicators),
        ("Strategies", check_strategies),
        ("AI Models", check_models),
        ("Backtesting", check_backtesting),
        ("Optimization", check_optimization)
    ]
    
    results = []
    for check_name, check_func in checks:
        print(f"\n🔍 Checking: {check_name}")
        result = check_func()
        results.append(result)
        print(f"{'✅' if result else '❌'} {check_name}: {'PASS' if result else 'FAIL'}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 System Check Results: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("🎉 System is fully operational!")
    elif success_count >= total_count * 0.8:
        print("⚠️ System is mostly operational with minor issues")
    else:
        print("❌ System has significant issues")
    
    return success_count / total_count

if __name__ == "__main__":
    score = main()
    exit(0 if score >= 0.8 else 1)
