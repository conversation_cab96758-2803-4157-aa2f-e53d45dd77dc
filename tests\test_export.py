import json
import tempfile

def test_export_results():
    results = [
        {"symbol": "EURUSD", "win_rate": 0.6, "sharpe": 1.2},
        {"symbol": "GBPUSD", "win_rate": 0.7, "sharpe": 1.5},
    ]
    with tempfile.NamedTemporaryFile(suffix=".json", delete=False, mode="w", encoding="utf-8") as f:
        json.dump(results, f)
        f.close()
        with open(f.name, "r", encoding="utf-8") as fr:
            loaded = json.load(fr)
        assert loaded == results
