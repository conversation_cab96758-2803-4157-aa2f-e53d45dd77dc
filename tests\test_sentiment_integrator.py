import unittest
import datetime
from unittest.mock import MagicMock, patch
import numpy as np

from utils.sentiment_integrator import SentimentIntegrator
from utils.sentiment_analyzer import SentimentAnalyzer
from utils.source_credibility import SourceCredibility
from utils.news_volume_analyzer import NewsVolumeAnalyzer

class TestSentimentIntegrator(unittest.TestCase):
    """Test cases for the SentimentIntegrator class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create mock objects for dependencies
        self.mock_sentiment_analyzer = MagicMock(spec=SentimentAnalyzer)
        self.mock_volume_analyzer = MagicMock(spec=NewsVolumeAnalyzer)
        
        # Configure mock behavior
        self.mock_sentiment_analyzer.analyze.return_value = {
            'score': 0.75,
            'label': 'positive',
            'confidence': 0.85,
            'weighted_score': 0.7
        }
        
        self.mock_volume_analyzer.add_news.return_value = None
        self.mock_volume_analyzer.detect_volume_spike.return_value = (
            True,  # is_spike
            {
                'ratio': 2.5,
                'short_volume': 15,
                'long_volume': 6
            }
        )
        
        # Set properties on mock objects
        self.mock_volume_analyzer.threshold_ratio = 2.0
        self.mock_volume_analyzer.min_news_count = 5
        
        # Create integrator with mocked dependencies
        self.integrator = SentimentIntegrator(
            sentiment_analyzer=self.mock_sentiment_analyzer,
            volume_analyzer=self.mock_volume_analyzer
        )
    
    def test_initialization(self):
        """Test that the SentimentIntegrator initializes correctly."""
        # Test with default parameters
        integrator = SentimentIntegrator()
        self.assertIsInstance(integrator.sentiment_analyzer, SentimentAnalyzer)
        self.assertIsInstance(integrator.volume_analyzer, NewsVolumeAnalyzer)
        
        # Check default weights
        self.assertEqual(integrator.weights['sentiment'], 0.3)
        self.assertEqual(integrator.weights['volume_shock'], 0.15)
        self.assertEqual(integrator.weights['technical'], 0.35)
        self.assertEqual(integrator.weights['fundamental'], 0.2)
        
        # Test with custom weights
        custom_weights = {
            'sentiment': 0.5,
            'volume_shock': 0.1,
            'technical': 0.3,
            'fundamental': 0.1
        }
        integrator = SentimentIntegrator(weights=custom_weights)
        self.assertEqual(integrator.weights['sentiment'], 0.5)
        self.assertEqual(integrator.weights['volume_shock'], 0.1)
        self.assertEqual(integrator.weights['technical'], 0.3)
        self.assertEqual(integrator.weights['fundamental'], 0.1)
    
    def test_weight_normalization(self):
        """Test that weights are properly normalized."""
        # Test with weights that don't sum to 1.0
        non_normalized_weights = {
            'sentiment': 2.0,
            'volume_shock': 1.0,
            'technical': 3.0,
            'fundamental': 4.0
        }
        
        integrator = SentimentIntegrator(weights=non_normalized_weights)
        
        # Check that weights are normalized
        total = sum(integrator.weights[w] for w in 
                   ['sentiment', 'volume_shock', 'technical', 'fundamental'])
        self.assertAlmostEqual(total, 1.0, places=5)
        
        # Check relative proportions are maintained
        self.assertAlmostEqual(integrator.weights['sentiment'] / integrator.weights['volume_shock'], 2.0, places=5)
        self.assertAlmostEqual(integrator.weights['technical'] / integrator.weights['sentiment'], 1.5, places=5)
        self.assertAlmostEqual(integrator.weights['fundamental'] / integrator.weights['technical'], 4.0/3.0, places=5)
    
    def test_set_weights(self):
        """Test that set_weights updates weights correctly."""
        new_weights = {
            'sentiment': 0.6,
            'volume_shock': 0.1,
            'technical': 0.2,
            'fundamental': 0.1
        }
        
        self.integrator.set_weights(new_weights)
        
        # Check that weights are updated
        self.assertEqual(self.integrator.weights['sentiment'], 0.6)
        self.assertEqual(self.integrator.weights['volume_shock'], 0.1)
        self.assertEqual(self.integrator.weights['technical'], 0.2)
        self.assertEqual(self.integrator.weights['fundamental'], 0.1)
        
        # Test partial update
        partial_weights = {
            'sentiment': 0.8,
            'technical': 0.1
        }
        
        self.integrator.set_weights(partial_weights)
        
        # Check that weights are updated and normalized
        total = sum(self.integrator.weights[w] for w in 
                   ['sentiment', 'volume_shock', 'technical', 'fundamental'])
        self.assertAlmostEqual(total, 1.0, places=5)
    
    def test_process_news(self):
        """Test processing a news item."""
        # Process a news item
        result = self.integrator.process_news(
            asset="AAPL",
            news_text="Apple reports record quarterly earnings.",
            source="Reuters"
        )
        
        # Check that sentiment analyzer was called
        self.mock_sentiment_analyzer.analyze.assert_called_once_with(
            "Apple reports record quarterly earnings.",
            source="Reuters"
        )
        
        # Check that volume analyzer was called
        self.mock_volume_analyzer.add_news.assert_called_once()
        self.assertEqual(self.mock_volume_analyzer.add_news.call_args[0][0], "AAPL")
        
        # Check result
        self.assertEqual(result['asset'], "AAPL")
        self.assertEqual(result['source'], "Reuters")
        self.assertEqual(result['sentiment']['score'], 0.75)
        self.assertEqual(result['sentiment']['label'], 'positive')
        self.assertTrue(result['processed'])
    
    def test_get_volume_shock_signal(self):
        """Test getting volume shock signal."""
        # Test with spike detected
        signal = self.integrator.get_volume_shock_signal("AAPL")
        
        # Check that volume analyzer was called
        self.mock_volume_analyzer.detect_volume_spike.assert_called_once_with("AAPL", None)
        
        # Check signal calculation
        # With ratio=2.5, threshold=2.0, short_volume=15, min_count=5
        # normalized_ratio = (2.5 - 2.0) / (2 * 2.0) = 0.125
        # normalized_volume = (15 - 5) / (4 * 5) = 0.5
        # signal = 0.7 * 0.125 + 0.3 * 0.5 = 0.0875 + 0.15 = 0.2375
        self.assertAlmostEqual(signal, 0.2375, places=5)
        
        # Test with no spike
        self.mock_volume_analyzer.detect_volume_spike.return_value = (False, {})
        signal = self.integrator.get_volume_shock_signal("AAPL")
        self.assertEqual(signal, 0.0)
    
    def test_get_technical_signal(self):
        """Test getting technical signal."""
        technical_data = {
            'trend': 0.6,
            'momentum': 0.3,
            'volatility': -0.2,
            'volume': 0.1
        }
        
        signal = self.integrator.get_technical_signal("AAPL", technical_data)
        
        # Expected calculation:
        # 0.4 * 0.6 + 0.3 * 0.3 + 0.2 * (-0.2) + 0.1 * 0.1 = 0.24 + 0.09 - 0.04 + 0.01 = 0.3
        self.assertAlmostEqual(signal, 0.3, places=5)
    
    def test_get_fundamental_signal(self):
        """Test getting fundamental signal."""
        fundamental_data = {
            'valuation': -0.4,
            'growth': 0.7,
            'quality': 0.5
        }
        
        signal = self.integrator.get_fundamental_signal("AAPL", fundamental_data)
        
        # Expected calculation:
        # 0.5 * (-0.4) + 0.3 * 0.7 + 0.2 * 0.5 = -0.2 + 0.21 + 0.1 = 0.11
        self.assertAlmostEqual(signal, 0.11, places=5)
    
    @patch('numpy.random.uniform')
    def test_get_sentiment_signal(self, mock_uniform):
        """Test getting sentiment signal."""
        mock_uniform.return_value = 0.5
        
        signal = self.integrator.get_sentiment_signal("AAPL")
        
        # Check that random function was called
        mock_uniform.assert_called_once_with(-0.8, 0.8)
        
        # Check signal
        self.assertEqual(signal, 0.5)
    
    def test_get_integrated_signal(self):
        """Test getting integrated signal."""
        # Setup
        with patch.object(self.integrator, 'get_sentiment_signal', return_value=0.6) as mock_sentiment:
            with patch.object(self.integrator, 'get_volume_shock_signal', return_value=0.3) as mock_volume:
                technical_data = {'trend': 0.5, 'momentum': 0.2}
                fundamental_data = {'valuation': -0.3, 'growth': 0.6}
                
                # Mock technical and fundamental signals
                self.integrator.get_technical_signal = MagicMock(return_value=-0.1)
                self.integrator.get_fundamental_signal = MagicMock(return_value=0.2)
                
                # Get integrated signal
                result = self.integrator.get_integrated_signal(
                    asset="AAPL",
                    technical_data=technical_data,
                    fundamental_data=fundamental_data,
                    lookback_hours=48
                )
                
                # Check that component methods were called
                mock_sentiment.assert_called_once_with("AAPL", 48, None)
                mock_volume.assert_called_once_with("AAPL", None)
                self.integrator.get_technical_signal.assert_called_once_with("AAPL", technical_data)
                self.integrator.get_fundamental_signal.assert_called_once_with("AAPL", fundamental_data)
                
                # Check result structure
                self.assertEqual(result['asset'], "AAPL")
                self.assertIn('timestamp', result)
                self.assertIn('overall_signal', result)
                self.assertIn('component_signals', result)
                self.assertIn('confidence', result)
                self.assertIn('weights', result)
                
                # Check component signals
                self.assertEqual(result['component_signals']['sentiment'], 0.6)
                self.assertEqual(result['component_signals']['volume_shock'], 0.3)
                self.assertEqual(result['component_signals']['technical'], -0.1)
                self.assertEqual(result['component_signals']['fundamental'], 0.2)
                
                # Check overall signal calculation
                # 0.3 * 0.6 + 0.15 * 0.3 + 0.35 * (-0.1) + 0.2 * 0.2 = 0.18 + 0.045 - 0.035 + 0.04 = 0.23
                self.assertAlmostEqual(result['overall_signal'], 0.23, places=5)

if __name__ == '__main__':
    unittest.main() 