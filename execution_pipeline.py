#!/usr/bin/env python3
"""
🔄 Execution Pipeline - v2.0 Architecture
پایپ‌لاین اجرایی - معماری نسخه 2.0

این فایل ترتیب اجرای منطقی و pipeline کامل سیستم را تعریف می‌کند
"""

import asyncio
import sys
import os
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import core components
from core.base import BaseComponent
from core.logger import get_logger
from core.config import get_config
from core.exceptions import TradingSystemError
from core.utils import monitor_performance

# Import modules
from ai_models import initialize_models, model_registry, get_available_models
from utils import initialize_utils, setup_system_monitoring
from models import initialize_models_package
from env import env_factory
from portfolio import portfolio_factory
from evaluation import evaluation_engine
from optimization import optimization_engine
from api import api_manager

class PipelineStage(Enum):
    """مراحل pipeline"""
    INITIALIZATION = "initialization"
    PREPARATION = "preparation"
    LOADING = "loading"
    VALIDATION = "validation"
    EXECUTION = "execution"
    MONITORING = "monitoring"
    CLEANUP = "cleanup"

@dataclass
class StageResult:
    """نتیجه مرحله"""
    stage: PipelineStage
    success: bool
    duration: float
    message: str
    data: Dict[str, Any]
    timestamp: datetime

class ExecutionPipeline(BaseComponent):
    """پایپ‌لاین اجرایی اصلی"""
    
    def __init__(self, name: str = "execution_pipeline", config: Dict[str, Any] = None):
        super().__init__(name, config)
        
        self.config = config or {}
        self.stages = []
        self.stage_results = []
        self.current_stage = None
        
        # Pipeline settings
        self.parallel_execution = self.config.get("parallel_execution", True)
        self.fail_fast = self.config.get("fail_fast", True)
        self.retry_attempts = self.config.get("retry_attempts", 3)
        self.stage_timeout = self.config.get("stage_timeout", 300)  # seconds
        
        # System components
        self.system_components = {}
        self.models_loaded = False
        self.environments_ready = False
        self.portfolios_initialized = False
        
        self.logger = get_logger(__name__)
        
        # Initialize BaseComponent status variables
        self._initialized = False
        self._running = False
    
    def initialize(self) -> bool:
        """مقداردهی اولیه pipeline"""
        try:
            # Define pipeline stages
            self._define_pipeline_stages()
            
            self.logger.info("📋 Execution pipeline initialized")
            self._initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Pipeline initialization failed: {e}")
            return False
    
    def start(self) -> bool:
        """شروع pipeline"""
        if not self._initialized:
            if not self.initialize():
                return False
        
        self._running = True
        self.logger.info("🚀 Starting execution pipeline...")
        return True
    
    def stop(self) -> bool:
        """توقف pipeline"""
        try:
            self._running = False
            self.logger.info("⏹️ Execution pipeline stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Pipeline stop failed: {e}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت pipeline"""
        return {
            "initialized": self._initialized,
            "running": self._running,
            "current_stage": self.current_stage.value if self.current_stage else None,
            "stages_completed": len(self.stage_results),
            "total_stages": len(self.stages),
            "models_loaded": self.models_loaded,
            "environments_ready": self.environments_ready,
            "portfolios_initialized": self.portfolios_initialized
        }
    
    async def execute_full_pipeline(self) -> bool:
        """اجرای کامل pipeline"""
        self.logger.info("🎯 Starting full pipeline execution...")
        
        start_time = datetime.now()
        total_success = True
        
        try:
            for stage_info in self.stages:
                result = await self._execute_stage(stage_info)
                self.stage_results.append(result)
                
                if not result.success:
                    if self.fail_fast:
                        self.logger.error(f"❌ Pipeline failed at stage: {result.stage.value}")
                        return False
                    else:
                        total_success = False
                        self.logger.warning(f"⚠️ Stage failed but continuing: {result.stage.value}")
            
            # Calculate total time
            total_time = (datetime.now() - start_time).total_seconds()
            
            if total_success:
                self.logger.info(f"✅ Pipeline execution completed successfully in {total_time:.2f}s")
            else:
                self.logger.warning(f"⚠️ Pipeline completed with errors in {total_time:.2f}s")
            
            return total_success
            
        except Exception as e:
            self.logger.error(f"❌ Pipeline execution failed: {e}")
            return False
    
    async def execute_stage(self, stage: PipelineStage) -> StageResult:
        """اجرای یک مرحله خاص"""
        stage_info = next((s for s in self.stages if s["stage"] == stage), None)
        
        if not stage_info:
            raise ValueError(f"Stage not found: {stage}")
        
        return await self._execute_stage(stage_info)
    
    def _define_pipeline_stages(self):
        """تعریف مراحل pipeline"""
        self.stages = [
            {
                "stage": PipelineStage.INITIALIZATION,
                "name": "System Initialization",
                "description": "Initialize core system components",
                "handler": self._stage_initialization,
                "dependencies": [],
                "timeout": 60,
                "critical": True
            },
            {
                "stage": PipelineStage.PREPARATION,
                "name": "System Preparation",
                "description": "Prepare system for execution",
                "handler": self._stage_preparation,
                "dependencies": [PipelineStage.INITIALIZATION],
                "timeout": 30,
                "critical": True
            },
            {
                "stage": PipelineStage.LOADING,
                "name": "Component Loading",
                "description": "Load AI models and components",
                "handler": self._stage_loading,
                "dependencies": [PipelineStage.PREPARATION],
                "timeout": 180,
                "critical": True
            },
            {
                "stage": PipelineStage.VALIDATION,
                "name": "System Validation",
                "description": "Validate system components",
                "handler": self._stage_validation,
                "dependencies": [PipelineStage.LOADING],
                "timeout": 120,
                "critical": True
            },
            {
                "stage": PipelineStage.EXECUTION,
                "name": "Main Execution",
                "description": "Execute main trading logic",
                "handler": self._stage_execution,
                "dependencies": [PipelineStage.VALIDATION],
                "timeout": 0,  # No timeout for main execution
                "critical": True
            },
            {
                "stage": PipelineStage.MONITORING,
                "name": "System Monitoring",
                "description": "Monitor system performance",
                "handler": self._stage_monitoring,
                "dependencies": [PipelineStage.EXECUTION],
                "timeout": 30,
                "critical": False
            },
            {
                "stage": PipelineStage.CLEANUP,
                "name": "System Cleanup",
                "description": "Clean up resources",
                "handler": self._stage_cleanup,
                "dependencies": [],
                "timeout": 30,
                "critical": False
            }
        ]
    
    async def _execute_stage(self, stage_info: Dict[str, Any]) -> StageResult:
        """اجرای یک مرحله"""
        stage = stage_info["stage"]
        handler = stage_info["handler"]
        timeout = stage_info.get("timeout", self.stage_timeout)
        
        self.current_stage = stage
        self.logger.info(f"🔄 Executing stage: {stage.value} - {stage_info['name']}")
        
        start_time = datetime.now()
        
        try:
            # Execute stage with timeout
            if timeout > 0:
                result = await asyncio.wait_for(handler(), timeout=timeout)
            else:
                result = await handler()
            
            # Calculate duration
            duration = (datetime.now() - start_time).total_seconds()
            
            # Create result
            stage_result = StageResult(
                stage=stage,
                success=result.get("success", False),
                duration=duration,
                message=result.get("message", ""),
                data=result.get("data", {}),
                timestamp=datetime.now()
            )
            
            if stage_result.success:
                self.logger.info(f"✅ Stage completed: {stage.value} ({duration:.2f}s)")
            else:
                self.logger.error(f"❌ Stage failed: {stage.value} - {stage_result.message}")
            
            return stage_result
            
        except asyncio.TimeoutError:
            duration = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"⏰ Stage timeout: {stage.value} ({duration:.2f}s)")
            
            return StageResult(
                stage=stage,
                success=False,
                duration=duration,
                message=f"Stage timeout after {timeout}s",
                data={},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"❌ Stage error: {stage.value} - {e}")
            
            return StageResult(
                stage=stage,
                success=False,
                duration=duration,
                message=str(e),
                data={},
                timestamp=datetime.now()
            )
    
    # Stage handlers
    async def _stage_initialization(self) -> Dict[str, Any]:
        """مرحله مقداردهی اولیه"""
        try:
            self.logger.info("🔧 Initializing system components...")
            
            # Initialize core utilities
            if not initialize_utils():
                raise TradingSystemError("Failed to initialize utilities")
            
            # Setup system monitoring
            setup_system_monitoring()
            
            # Initialize configuration
            config = get_config()
            self.logger.info(f"📋 Configuration loaded: {config.trading.initial_balance}")
            
            # Initialize system components registry
            self.system_components = {
                "utils": True,
                "config": True,
                "logger": True,
                "monitoring": True
            }
            
            return {
                "success": True,
                "message": "System initialization completed",
                "data": {
                    "components": list(self.system_components.keys()),
                    "config_loaded": True
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"System initialization failed: {e}",
                "data": {}
            }
    
    async def _stage_preparation(self) -> Dict[str, Any]:
        """مرحله آماده‌سازی"""
        try:
            self.logger.info("🔨 Preparing system for execution...")
            
            # Prepare directories
            os.makedirs("logs", exist_ok=True)
            os.makedirs("reports", exist_ok=True)
            os.makedirs("temp", exist_ok=True)
            
            # Initialize package modules
            if not initialize_models_package():
                raise TradingSystemError("Failed to initialize models package")
            
            # Initialize evaluation engine
            if not evaluation_engine.initialize():
                raise TradingSystemError("Failed to initialize evaluation engine")
            
            # Initialize optimization engine
            if not optimization_engine.initialize():
                raise TradingSystemError("Failed to initialize optimization engine")
            
            self.system_components.update({
                "models_package": True,
                "evaluation": True,
                "optimization": True
            })
            
            return {
                "success": True,
                "message": "System preparation completed",
                "data": {
                    "directories_created": ["logs", "reports", "temp"],
                    "engines_initialized": ["evaluation", "optimization"]
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"System preparation failed: {e}",
                "data": {}
            }
    
    async def _stage_loading(self) -> Dict[str, Any]:
        """مرحله بارگذاری اجزا"""
        try:
            self.logger.info("📦 Loading AI models and components...")
            
            # Initialize AI models
            model_manager = initialize_models()
            if not model_manager:
                raise TradingSystemError("Failed to initialize AI models")
            
            # Get available models
            available_models = get_available_models()
            self.logger.info(f"🤖 Available models: {len(available_models)}")
            
            # Load essential models
            essential_models = ["finbert", "cryptobert", "chronos"]
            loaded_models = []
            
            for model_name in essential_models:
                try:
                    if model_name in available_models:
                        model = model_registry.get_model(model_name)
                        if model:
                            loaded_models.append(model_name)
                            self.logger.info(f"✅ Model loaded: {model_name}")
                        else:
                            self.logger.warning(f"⚠️ Model not available: {model_name}")
                except Exception as e:
                    self.logger.warning(f"⚠️ Model loading failed: {model_name} - {e}")
            
            # Create trading environments
            try:
                env_eurusd = env_factory.create_trading_env("EURUSD", "H1", "v2")
                env_gbpusd = env_factory.create_trading_env("GBPUSD", "H1", "v2")
                
                if env_eurusd and env_eurusd.initialize():
                    self.environments_ready = True
                    self.logger.info("🌍 Trading environments initialized")
                
            except Exception as e:
                self.logger.warning(f"⚠️ Environment initialization failed: {e}")
            
            # Initialize portfolio manager
            try:
                portfolio_manager = portfolio_factory.create_portfolio_manager(10000.0, "v2")
                
                if portfolio_manager and portfolio_manager.initialize():
                    self.portfolios_initialized = True
                    self.logger.info("💼 Portfolio manager initialized")
                
            except Exception as e:
                self.logger.warning(f"⚠️ Portfolio initialization failed: {e}")
            
            # Initialize API manager
            try:
                if api_manager.initialize():
                    self.logger.info("🌐 API manager initialized")
                
            except Exception as e:
                self.logger.warning(f"⚠️ API initialization failed: {e}")
            
            self.models_loaded = len(loaded_models) > 0
            
            self.system_components.update({
                "ai_models": True,
                "environments": self.environments_ready,
                "portfolio": self.portfolios_initialized,
                "api": True
            })
            
            return {
                "success": True,
                "message": "Component loading completed",
                "data": {
                    "loaded_models": loaded_models,
                    "available_models": len(available_models),
                    "environments_ready": self.environments_ready,
                    "portfolios_initialized": self.portfolios_initialized
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Component loading failed: {e}",
                "data": {}
            }
    
    async def _stage_validation(self) -> Dict[str, Any]:
        """مرحله اعتبارسنجی"""
        try:
            self.logger.info("🔍 Validating system components...")
            
            validation_results = {}
            
            # Validate models
            if self.models_loaded:
                available_models = get_available_models()
                validation_results["models"] = {
                    "available": len(available_models),
                    "status": "ready" if available_models else "empty"
                }
            
            # Validate environments
            if self.environments_ready:
                # Test environment creation
                test_env = env_factory.create_trading_env("EURUSD", "H1", "v2")
                validation_results["environments"] = {
                    "test_creation": test_env is not None,
                    "status": "ready" if test_env else "failed"
                }
            
            # Validate portfolio
            if self.portfolios_initialized:
                test_portfolio = portfolio_factory.create_portfolio_manager(1000.0, "v2")
                validation_results["portfolio"] = {
                    "test_creation": test_portfolio is not None,
                    "status": "ready" if test_portfolio else "failed"
                }
            
            # Validate evaluation engine
            eval_health = evaluation_engine.health_check()
            validation_results["evaluation"] = {
                "health": eval_health,
                "status": "ready" if eval_health.get("initialized") else "failed"
            }
            
            # Validate optimization engine
            opt_health = optimization_engine.health_check()
            validation_results["optimization"] = {
                "health": opt_health,
                "status": "ready" if opt_health.get("initialized") else "failed"
            }
            
            # Check overall validation
            all_valid = all(
                result.get("status") == "ready" 
                for result in validation_results.values()
            )
            
            return {
                "success": all_valid,
                "message": "System validation completed" if all_valid else "Some components failed validation",
                "data": validation_results
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"System validation failed: {e}",
                "data": {}
            }
    
    async def _stage_execution(self) -> Dict[str, Any]:
        """مرحله اجرای اصلی"""
        try:
            self.logger.info("🎯 Starting main execution...")
            
            # Start evaluation engine
            if not evaluation_engine.start():
                raise TradingSystemError("Failed to start evaluation engine")
            
            # Start optimization engine
            if not optimization_engine.start():
                raise TradingSystemError("Failed to start optimization engine")
            
            # Start API manager
            if not api_manager.start():
                self.logger.warning("⚠️ API manager start failed")
            
            # Main execution loop would go here
            # For now, just indicate readiness
            
            execution_data = {
                "evaluation_engine": evaluation_engine.health_check(),
                "optimization_engine": optimization_engine.health_check(),
                "api_manager": api_manager.health_check(),
                "system_ready": True
            }
            
            return {
                "success": True,
                "message": "Main execution started",
                "data": execution_data
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Main execution failed: {e}",
                "data": {}
            }
    
    async def _stage_monitoring(self) -> Dict[str, Any]:
        """مرحله نظارت"""
        try:
            self.logger.info("📊 Setting up system monitoring...")
            
            # Get system status
            system_status = {}
            
            # Monitor evaluation engine
            if evaluation_engine._running:
                system_status["evaluation"] = evaluation_engine.health_check()
            
            # Monitor optimization engine
            if optimization_engine._running:
                system_status["optimization"] = optimization_engine.health_check()
            
            # Monitor API manager
            if api_manager._running:
                system_status["api"] = api_manager.health_check()
            
            # Monitor system resources
            from core.utils import get_system_info, get_resource_usage
            system_status["resources"] = get_resource_usage()
            system_status["system_info"] = get_system_info()
            
            return {
                "success": True,
                "message": "System monitoring active",
                "data": system_status
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"System monitoring failed: {e}",
                "data": {}
            }
    
    async def _stage_cleanup(self) -> Dict[str, Any]:
        """مرحله پاکسازی"""
        try:
            self.logger.info("🧹 Cleaning up system resources...")
            
            cleanup_results = {}
            
            # Cleanup evaluation engine
            if evaluation_engine._running:
                cleanup_results["evaluation"] = evaluation_engine.stop()
            
            # Cleanup optimization engine
            if optimization_engine._running:
                cleanup_results["optimization"] = optimization_engine.stop()
            
            # Cleanup API manager
            if api_manager._running:
                cleanup_results["api"] = api_manager.stop()
            
            # Cleanup resources
            from core.utils import cleanup_resources
            cleanup_resources()
            cleanup_results["resources"] = True
            
            return {
                "success": True,
                "message": "System cleanup completed",
                "data": cleanup_results
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"System cleanup failed: {e}",
                "data": {}
            }
    
    def get_pipeline_report(self) -> Dict[str, Any]:
        """گزارش pipeline"""
        total_duration = sum(r.duration for r in self.stage_results)
        successful_stages = [r for r in self.stage_results if r.success]
        
        return {
            "total_stages": len(self.stages),
            "completed_stages": len(self.stage_results),
            "successful_stages": len(successful_stages),
            "total_duration": total_duration,
            "success_rate": len(successful_stages) / len(self.stage_results) if self.stage_results else 0,
            "stage_results": [
                {
                    "stage": r.stage.value,
                    "success": r.success,
                    "duration": r.duration,
                    "message": r.message,
                    "timestamp": r.timestamp.isoformat()
                }
                for r in self.stage_results
            ]
        }

# Global instance
execution_pipeline = ExecutionPipeline("global_execution_pipeline")

# Main execution functions
async def run_pipeline():
    """اجرای pipeline"""
    if not execution_pipeline.initialize():
        return False
    
    if not execution_pipeline.start():
        return False
    
    result = await execution_pipeline.execute_full_pipeline()
    
    # Generate report
    report = execution_pipeline.get_pipeline_report()
    
    logger = get_logger(__name__)
    logger.info("📊 Pipeline Execution Report:")
    logger.info(f"Total Stages: {report['total_stages']}")
    logger.info(f"Completed: {report['completed_stages']}")
    logger.info(f"Successful: {report['successful_stages']}")
    logger.info(f"Success Rate: {report['success_rate']:.1%}")
    logger.info(f"Total Duration: {report['total_duration']:.2f}s")
    
    return result

async def run_stage(stage_name: str):
    """اجرای یک مرحله خاص"""
    try:
        stage = PipelineStage(stage_name)
        result = await execution_pipeline.execute_stage(stage)
        
        logger = get_logger(__name__)
        logger.info(f"Stage Result: {result.stage.value}")
        logger.info(f"Success: {result.success}")
        logger.info(f"Duration: {result.duration:.2f}s")
        logger.info(f"Message: {result.message}")
        
        return result.success
        
    except ValueError:
        logger = get_logger(__name__)
        logger.error(f"Invalid stage name: {stage_name}")
        return False

# Export all
__all__ = [
    "ExecutionPipeline",
    "PipelineStage",
    "StageResult",
    "execution_pipeline",
    "run_pipeline",
    "run_stage"
]

# Main execution
if __name__ == "__main__":
    # Run the pipeline
    result = asyncio.run(run_pipeline())
    
    if result:
        print("✅ Pipeline execution completed successfully")
    else:
        print("❌ Pipeline execution failed")
        sys.exit(1) 