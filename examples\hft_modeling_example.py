"""مثال عملی برای استفاده از HFT Modeling

این مثال نحوه استفاده از سیستم HFT Modeling را برای معاملات با فرکانس بالا نشان می‌دهد.
"""

import time
import numpy as np
import logging

from utils.hft_modeling import (
    OrderBookLevel, Trade, HFTModelingSystem,
    OrderBookAnalyzer, LatencyOptimizer, ShortTermPredictor
)

# تنظیم logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_sample_market_data(symbol: str, num_ticks: int = 1000) -> tuple:
    """تولید داده‌های نمونه بازار برای تست
    
    Parameters
    ----------
    symbol : str
        نماد معاملاتی
    num_ticks : int
        تعداد tick های تولیدی
        
    Returns
    -------
    tuple
        (قیمت‌ها، حجم‌ها، order_books, معاملات)
    """
    np.random.seed(42)
    
    # تولید قیمت‌ها با random walk
    base_price = 50000 if symbol == 'BTCUSD' else 3000
    price_changes = np.random.normal(0, 0.1, num_ticks)
    prices = [base_price]
    
    for change in price_changes:
        new_price = prices[-1] + change
        prices.append(max(new_price, base_price * 0.8))  # جلوگیری از قیمت‌های منفی
    
    # تولید حجم‌ها
    volumes = np.random.lognormal(mean=5, sigma=0.5, size=num_ticks)
    
    # تولید order books
    order_books = []
    trades = []
    
    for i in range(num_ticks):
        current_price = prices[i]
        
        # تولید bid levels
        bids = []
        for j in range(5):
            bid_price = current_price - (j + 1) * 0.1
            bid_volume = np.random.exponential(scale=10)
            bids.append(OrderBookLevel(bid_price, bid_volume, time.time()))
        
        # تولید ask levels
        asks = []
        for j in range(5):
            ask_price = current_price + (j + 1) * 0.1
            ask_volume = np.random.exponential(scale=10)
            asks.append(OrderBookLevel(ask_price, ask_volume, time.time()))
        
        order_books.append((bids, asks))
        
        # تولید معاملات
        if i > 0:
            trade_price = current_price + np.random.normal(0, 0.05)
            trade_volume = np.random.exponential(scale=2)
            trade_side = 'buy' if np.random.random() > 0.5 else 'sell'
            trades.append(Trade(trade_price, trade_volume, time.time(), trade_side))
    
    return prices, volumes, order_books, trades


def demonstrate_order_book_analysis():
    """نمایش تحلیل order book"""
    print("=" * 50)
    print("نمایش تحلیل Order Book")
    print("=" * 50)
    
    analyzer = OrderBookAnalyzer(max_levels=10, history_size=100)
    
    # تولید داده‌های نمونه
    prices, volumes, order_books, trades = generate_sample_market_data('BTCUSD', 50)
    
    # پردازش order books
    for i, (bids, asks) in enumerate(order_books[:10]):
        analyzer.update_order_book(bids, asks)
        
        if i % 5 == 0:  # نمایش هر 5 tick
            microstructure = analyzer.get_market_microstructure()
            print(f"\nTick {i}:")
            print(f"  Bid-Ask Spread: {microstructure.bid_ask_spread:.4f}")
            print(f"  Market Depth: {microstructure.market_depth:.2f}")
            print(f"  Order Flow Imbalance: {microstructure.order_flow_imbalance:.4f}")
            buy_impact = analyzer.calculate_price_impact(1000, 'buy')
            sell_impact = analyzer.calculate_price_impact(1000, 'sell')
            print(f"  Price Impact (1000 units buy): {buy_impact:.6f}")
            print(f"  Price Impact (1000 units sell): {sell_impact:.6f}")


def demonstrate_latency_optimization():
    """نمایش بهینه‌سازی تأخیر"""
    print("\n" + "=" * 50)
    print("نمایش بهینه‌سازی تأخیر")
    print("=" * 50)
    
    optimizer = LatencyOptimizer()
    
    # تست اندازه‌گیری زمان اجرا
    def sample_calculation():
        """محاسبه نمونه برای تست"""
        data = np.random.randn(10000)
        return np.mean(data), np.std(data)
    
    print("\nاندازه‌گیری زمان اجرا:")
    for i in range(5):
        result, exec_time = optimizer.measure_execution_time(sample_calculation)
        print(f"  اجرا {i+1}: {exec_time:.2f} ms")
    
    # نمایش آمار
    stats = optimizer.get_latency_stats()
    print("\nآمار تأخیر:")
    for key, value in stats.items():
        print(f"  {key}: {value:.2f} ms")
    
    # تست بهینه‌سازی ساختار داده
    print("\nبهینه‌سازی ساختار داده:")
    original_data = list(range(1000))
    optimized_data = optimizer.optimize_data_structures(original_data)
    print(f"  نوع اصلی: {type(original_data)}")
    print(f"  نوع بهینه‌شده: {type(optimized_data)}")


def demonstrate_short_term_prediction():
    """نمایش پیش‌بینی کوتاه‌مدت"""
    print("\n" + "=" * 50)
    print("نمایش پیش‌بینی کوتاه‌مدت")
    print("=" * 50)
    
    predictor = ShortTermPredictor(feature_window=50)
    
    # تولید داده‌های آموزش
    prices, volumes, order_books, trades = generate_sample_market_data('BTCUSD', 500)
    
    # محاسبه spread ها
    spreads = []
    for bids, asks in order_books:
        if bids and asks:
            spread = asks[0].price - bids[0].price
            spreads.append(spread)
        else:
            spreads.append(0.1)  # مقدار پیش‌فرض
    
    print("آموزش مدل پیش‌بینی...")
    predictor.train_model(prices[:-100], volumes[:-100], spreads[:-100])
    
    # تست پیش‌بینی
    print("\nنتایج پیش‌بینی:")
    for i in range(5):
        start_idx = 400 + i * 10
        end_idx = start_idx + 50
        
        recent_prices = prices[start_idx:end_idx]
        recent_volumes = volumes[start_idx:end_idx]
        recent_spreads = spreads[start_idx:end_idx]
        
        prediction, confidence = predictor.predict_direction(
            recent_prices, recent_volumes, recent_spreads
        )
        
        actual_direction = 1 if prices[end_idx + 5] > prices[end_idx] else 0
        
        print(f"  پیش‌بینی {i+1}:")
        print(f"    احتمال صعودی: {prediction:.3f}")
        print(f"    اطمینان: {confidence:.3f}")
        print(f"    جهت واقعی: {'صعودی' if actual_direction == 1 else 'نزولی'}")
        print(f"    صحت: {'✓' if (prediction > 0.5) == (actual_direction == 1) else '✗'}")


def demonstrate_hft_strategy():
    """نمایش استراتژی HFT"""
    print("\n" + "=" * 50)
    print("نمایش استراتژی HFT")
    print("=" * 50)
    
    # ایجاد سیستم HFT
    system = HFTModelingSystem(['BTCUSD'])
    strategy = system.strategies['BTCUSD']
    
    # شروع سیستم
    system.start()
    print("سیستم HFT شروع شد")
    
    # تولید داده‌های بازار
    prices, volumes, order_books, trades = generate_sample_market_data('BTCUSD', 100)
    
    # آموزش predictor
    spreads = []
    for bids, asks in order_books:
        if bids and asks:
            spread = asks[0].price - bids[0].price
            spreads.append(spread)
        else:
            spreads.append(0.1)
    
    strategy.predictor.train_model(prices[:80], volumes[:80], spreads[:80])
    
    # شبیه‌سازی معاملات
    print("\nشبیه‌سازی معاملات:")
    for i in range(10):
        idx = 80 + i
        bids, asks = order_books[idx]
        
        # به‌روزرسانی order book
        strategy.order_book_analyzer.update_order_book(bids, asks)
        
        # دریافت میکروساختار
        market_data = strategy.order_book_analyzer.get_market_microstructure()
        
        # پیش‌بینی
        recent_prices = prices[idx-50:idx]
        recent_volumes = volumes[idx-50:idx]
        recent_spreads = spreads[idx-50:idx]
        
        prediction, confidence = strategy.predictor.predict_direction(
            recent_prices, recent_volumes, recent_spreads
        )
        
        # تصمیم‌گیری
        should_enter, direction, size = strategy.should_enter_position(
            market_data, prediction, confidence
        )
        
        print(f"  Tick {i+1}:")
        print(f"    قیمت: {prices[idx]:.2f}")
        print(f"    پیش‌بینی: {prediction:.3f}, اطمینان: {confidence:.3f}")
        print(f"    تصمیم: {direction} ({size:.0f} واحد)" if should_enter else "    تصمیم: انتظار")
        
        # اجرای معامله
        if should_enter:
            current_price = (bids[0].price + asks[0].price) / 2
            success = strategy.execute_trade(direction, size, current_price)
            if success:
                print(f"    معامله اجرا شد: {direction} {size:.0f} @ {current_price:.2f}")
        
        # بررسی خروج
        if strategy.current_position != 0:
            current_price = (bids[0].price + asks[0].price) / 2
            should_exit = strategy.should_exit_position(current_price)
            if should_exit:
                strategy.execute_trade('close', abs(strategy.current_position), current_price)
                print(f"    پوزیشن بسته شد @ {current_price:.2f}")
    
    # نمایش آمار نهایی
    print(f"\nآمار نهایی:")
    stats = strategy.get_performance_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # توقف سیستم
    system.stop()
    print("\nسیستم HFT متوقف شد")


def main():
    """تابع اصلی برای اجرای تمام مثال‌ها"""
    print("مثال جامع HFT Modeling")
    print("=" * 80)
    
    try:
        # نمایش تحلیل order book
        demonstrate_order_book_analysis()
        
        # نمایش بهینه‌سازی تأخیر
        demonstrate_latency_optimization()
        
        # نمایش پیش‌بینی کوتاه‌مدت
        demonstrate_short_term_prediction()
        
        # نمایش استراتژی HFT
        demonstrate_hft_strategy()
        
        print("\n" + "=" * 80)
        print("همه مثال‌ها با موفقیت اجرا شدند!")
        print("=" * 80)
        
    except Exception as e:
        logger.error(f"خطا در اجرای مثال: {e}")
        raise


if __name__ == "__main__":
    main() 