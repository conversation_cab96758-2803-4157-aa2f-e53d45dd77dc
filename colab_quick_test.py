"""
🧪 Pearl-3x7B Colab Quick Test
تست سریع مغز متفکر در Google Colab

این اسکریپت برای تست سریع قابلیت‌های سیستم در Colab طراحی شده
"""

import torch
import time
import json
from datetime import datetime

def test_colab_system():
    """🔍 تست سیستم Colab"""
    print("🧪 PEARL-3X7B COLAB SYSTEM TEST")
    print("=" * 50)
    
    # Test GPU
    print("🖥️ GPU Test:")
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        print(f"   ✅ GPU Available: {gpu_name}")
        print(f"   💾 GPU Memory: {gpu_memory:.1f}GB")
        
        # GPU capability test
        if "T4" in gpu_name:
            capability = "Medium to Heavy models"
            max_model_size = "6GB"
        elif "V100" in gpu_name:
            capability = "Heavy to Extreme models"
            max_model_size = "12GB"
        elif "A100" in gpu_name:
            capability = "All models including Extreme"
            max_model_size = "20GB"
        else:
            capability = "Unknown GPU"
            max_model_size = "Unknown"
        
        print(f"   🎯 Capability: {capability}")
        print(f"   📊 Max Model Size: {max_model_size}")
    else:
        print("   ❌ GPU Not Available!")
        print("   💡 Enable GPU: Runtime > Change runtime type > GPU")
        return False
    
    # Test RAM
    try:
        import psutil
        ram_gb = psutil.virtual_memory().total / (1024**3)
        print(f"\n🧠 RAM Test:")
        print(f"   💾 Total RAM: {ram_gb:.1f}GB")
        
        if ram_gb >= 20:
            ram_capability = "Extreme models supported"
        elif ram_gb >= 12:
            ram_capability = "Heavy models supported"
        elif ram_gb >= 8:
            ram_capability = "Medium models supported"
        else:
            ram_capability = "Light models only"
        
        print(f"   🎯 RAM Capability: {ram_capability}")
    except:
        print("\n🧠 RAM Test: Unable to determine")
    
    # Test PyTorch
    print(f"\n🔥 PyTorch Test:")
    print(f"   📦 Version: {torch.__version__}")
    print(f"   🔥 CUDA Version: {torch.version.cuda}")
    
    # Simple GPU computation test
    try:
        print(f"\n⚡ GPU Computation Test:")
        start_time = time.time()
        
        # Create test tensors
        a = torch.randn(1000, 1000).cuda()
        b = torch.randn(1000, 1000).cuda()
        
        # Matrix multiplication
        c = torch.matmul(a, b)
        torch.cuda.synchronize()
        
        computation_time = time.time() - start_time
        print(f"   ✅ Matrix multiplication (1000x1000): {computation_time:.3f}s")
        
        if computation_time < 0.1:
            performance = "Excellent"
        elif computation_time < 0.5:
            performance = "Good"
        else:
            performance = "Slow"
        
        print(f"   📊 Performance: {performance}")
        
    except Exception as e:
        print(f"   ❌ GPU computation failed: {e}")
        return False
    
    print(f"\n✅ System test completed successfully!")
    return True

def test_brain_decision_making():
    """🧠 تست تصمیم‌گیری مغز متفکر"""
    print("\n🧠 BRAIN DECISION MAKING TEST")
    print("=" * 40)
    
    # Import brain components
    try:
        # Try different possible paths
        possible_paths = [
            '/content/colab_brain_trainer.py',
            'colab_brain_trainer.py',
            './colab_brain_trainer.py'
        ]

        trainer_loaded = False
        for path in possible_paths:
            try:
                exec(open(path).read())
                print(f"✅ Brain trainer imported successfully from: {path}")
                trainer_loaded = True
                break
            except FileNotFoundError:
                continue

        if not trainer_loaded:
            print("❌ Brain trainer import failed")
            print("💡 Make sure colab_brain_trainer.py is uploaded")
            print("📁 Checking available files:")
            import os
            files = os.listdir('/content/')
            colab_files = [f for f in files if 'colab' in f.lower()]
            print(f"   Found files: {colab_files}")
            return False

    except Exception as e:
        print(f"❌ Brain trainer import failed: {e}")
        print("💡 Make sure colab_brain_trainer.py is uploaded")
        return False
    
    # Test brain initialization
    try:
        trainer = ColabModelTrainer()
        print("✅ Brain trainer initialized")
        
        # Test system analysis
        system_info = trainer.brain.system_info
        print(f"✅ System analysis completed")
        print(f"   GPU: {system_info.gpu_name}")
        print(f"   GPU Memory: {system_info.gpu_memory_gb:.1f}GB")
        print(f"   RAM: {system_info.ram_gb:.1f}GB")
        
        # Test decision making
        available_models = trainer.heavy_models[:3]  # Test with first 3 models
        decision = trainer.brain.analyze_colab_training_situation(available_models)
        
        print(f"\n🧠 Brain Decision Test:")
        print(f"   Action: {decision['action']}")
        print(f"   Confidence: {decision['confidence']:.1%}")
        print(f"   Reasoning: {decision['reasoning']}")
        
        if decision['action'] == 'train':
            model = decision['model']
            print(f"   Selected Model: {model.name}")
            print(f"   Complexity: {model.complexity}")
            print(f"   GPU Usage: {decision['system_utilization']['gpu_usage']:.1%}")
            print(f"   RAM Usage: {decision['system_utilization']['ram_usage']:.1%}")
        
        print("✅ Brain decision making test passed")
        return True
        
    except Exception as e:
        print(f"❌ Brain test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_categories():
    """📊 تست دسته‌بندی مدل‌ها"""
    print("\n📊 MODEL CATEGORIES TEST")
    print("=" * 35)
    
    try:
        # Try different possible paths
        possible_paths = [
            '/content/colab_brain_trainer.py',
            'colab_brain_trainer.py',
            './colab_brain_trainer.py'
        ]

        trainer_loaded = False
        for path in possible_paths:
            try:
                exec(open(path).read())
                trainer_loaded = True
                break
            except FileNotFoundError:
                continue

        if not trainer_loaded:
            print("❌ colab_brain_trainer.py not found")
            return False

        trainer = ColabModelTrainer()
        
        # Group models by complexity
        complexity_groups = {}
        for model in trainer.heavy_models:
            if model.complexity not in complexity_groups:
                complexity_groups[model.complexity] = []
            complexity_groups[model.complexity].append(model)
        
        print("🎯 Available Model Categories:")
        for complexity, models in complexity_groups.items():
            print(f"\n🔥 {complexity.upper()} Models ({len(models)}):")
            for model in models:
                feasible = (model.gpu_memory_gb <= trainer.brain.system_info.gpu_memory_gb and 
                           model.ram_gb <= trainer.brain.system_info.ram_gb)
                status = "✅ Feasible" if feasible else "❌ Too demanding"
                print(f"   • {model.name} ({model.category}) - {status}")
                print(f"     GPU: {model.gpu_memory_gb}GB, RAM: {model.ram_gb}GB")
        
        # Recommend best models for current system
        suitable_models = []
        for model in trainer.heavy_models:
            if (model.gpu_memory_gb <= trainer.brain.system_info.gpu_memory_gb and 
                model.ram_gb <= trainer.brain.system_info.ram_gb):
                suitable_models.append(model)
        
        print(f"\n🎯 Recommended Models for Your System ({len(suitable_models)}):")
        for model in suitable_models[:5]:  # Top 5
            print(f"   🚀 {model.name} ({model.complexity})")
            print(f"      Category: {model.category}")
            print(f"      Training Time: {model.training_hours:.1f}h")
        
        return True
        
    except Exception as e:
        print(f"❌ Model categories test failed: {e}")
        return False

def run_quick_test():
    """🧪 اجرای تست سریع کامل"""
    print("🧪 PEARL-3X7B COLAB QUICK TEST")
    print("=" * 60)
    print("🎯 Testing system capabilities for brain-guided training")
    print()
    
    test_results = {
        "timestamp": datetime.now().isoformat(),
        "tests": {}
    }
    
    # Test 1: System capabilities
    print("📋 Test 1: System Capabilities")
    system_ok = test_colab_system()
    test_results["tests"]["system"] = system_ok
    
    if not system_ok:
        print("\n❌ System test failed. Please check GPU settings.")
        return test_results
    
    # Test 2: Brain decision making
    print("\n📋 Test 2: Brain Decision Making")
    brain_ok = test_brain_decision_making()
    test_results["tests"]["brain"] = brain_ok
    
    # Test 3: Model categories
    print("\n📋 Test 3: Model Categories")
    models_ok = test_model_categories()
    test_results["tests"]["models"] = models_ok
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎉 QUICK TEST SUMMARY")
    print("=" * 60)
    
    all_passed = all(test_results["tests"].values())
    
    if all_passed:
        print("✅ All tests passed! System ready for brain-guided training.")
        print("\n🚀 Next steps:")
        print("   1. Run: run_colab_brain_training()")
        print("   2. Wait for training completion")
        print("   3. Download trained models")
        print("   4. Import to local project")
    else:
        print("❌ Some tests failed. Please check the issues above.")
        print("\n🔧 Troubleshooting:")
        if not test_results["tests"]["system"]:
            print("   • Enable GPU in Runtime settings")
        if not test_results["tests"]["brain"]:
            print("   • Upload colab_brain_trainer.py")
        if not test_results["tests"]["models"]:
            print("   • Check system requirements")
    
    # Save test results
    with open('/content/quick_test_results.json', 'w') as f:
        json.dump(test_results, f, indent=2)
    
    print(f"\n📄 Test results saved to: /content/quick_test_results.json")
    
    return test_results

def show_colab_setup_instructions():
    """📋 نمایش دستورالعمل‌های تنظیم Colab"""
    print("""
🚀 COLAB SETUP INSTRUCTIONS
===========================

📋 Step 1: Enable GPU
   Runtime > Change runtime type > Hardware accelerator > GPU

📋 Step 2: Upload Files
   Upload colab_brain_trainer.py to Colab

📋 Step 3: Install Dependencies
   !pip install torch transformers datasets accelerate

📋 Step 4: Run Quick Test
   exec(open('colab_quick_test.py').read())
   run_quick_test()

📋 Step 5: Start Training
   exec(open('colab_brain_trainer.py').read())
   run_colab_brain_training()

🎯 Expected Results:
   • System test: ✅ GPU available
   • Brain test: ✅ Decision making works
   • Models test: ✅ Suitable models found
   • Training: 2-4 hours for heavy models
   • Download: Zip file with best models

💡 Tips:
   • Use T4 for testing (free)
   • Upgrade to V100/A100 for best results
   • Monitor GPU usage with !nvidia-smi
   • Save progress regularly
""")

# Main execution
if __name__ == "__main__":
    try:
        import google.colab
        print("🚀 Running in Google Colab")
        show_colab_setup_instructions()
        print("\n" + "="*50)
        print("🧪 Ready to run quick test!")
        print("Execute: run_quick_test()")
    except ImportError:
        print("⚠️ This script is designed for Google Colab")
        print("Please run in Colab environment")
        
        # Run test anyway for local testing
        run_quick_test()
