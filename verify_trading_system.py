#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
✅ Verify Trading System
تایید وجود methods در Trading System
"""

import os
import sys
import warnings
warnings.filterwarnings('ignore')

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_trading_system():
    """تایید Trading System"""
    print("🔍 Verifying Trading System Methods...")
    
    try:
        # Import the class
        from models.unified_trading_system import UnifiedTradingSystem
        
        # Check methods by inspecting the class
        methods = dir(UnifiedTradingSystem)
        
        # Check for required methods
        required_methods = [
            'get_adaptive_signal',
            'get_unified_signal',
            'calculate_combined_signal'
        ]
        
        found_methods = []
        missing_methods = []
        
        for method in required_methods:
            if method in methods:
                found_methods.append(method)
                print(f"✅ {method} - Found")
            else:
                missing_methods.append(method)
                print(f"❌ {method} - Missing")
        
        # Try to create instance
        try:
            system = UnifiedTradingSystem()
            print("✅ Instance creation: Success")
            
            # Try to call methods
            if hasattr(system, 'get_adaptive_signal'):
                print("✅ get_adaptive_signal: Callable")
            else:
                print("❌ get_adaptive_signal: Not callable")
            
            if hasattr(system, 'get_unified_signal'):
                print("✅ get_unified_signal: Callable")
            else:
                print("❌ get_unified_signal: Not callable")
            
        except Exception as e:
            print(f"⚠️ Instance creation failed: {e}")
        
        # Summary
        success_rate = len(found_methods) / len(required_methods) * 100
        print(f"\n📊 Trading System Methods: {success_rate:.1f}% available")
        
        if success_rate >= 80:
            print("✅ Trading System: READY")
            return True
        else:
            print("❌ Trading System: NEEDS ATTENTION")
            return False
        
    except Exception as e:
        print(f"❌ Trading System verification failed: {e}")
        return False

def main():
    print("✅ TRADING SYSTEM VERIFICATION")
    print("=" * 40)
    
    if verify_trading_system():
        print("\n🎉 TRADING SYSTEM VERIFIED!")
        print("🚀 All methods available and working")
    else:
        print("\n⚠️ Trading System needs review")
    
    return True

if __name__ == "__main__":
    main() 