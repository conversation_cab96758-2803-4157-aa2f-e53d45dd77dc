"""
🔧 Quick Fixes for Ultimate Trading System
حل سریع مشکلات شناسایی شده
"""

def apply_quick_fixes():
    """اعمال تصحیحات سریع"""
    print("🔧 Applying quick fixes...")
    
    # Fix 1: Ensure CUDA is properly detected
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA Available: {torch.cuda.get_device_name(0)}")
            print(f"✅ GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            
            # Clear cache and optimize
            torch.cuda.empty_cache()
            torch.backends.cudnn.enabled = True
            torch.backends.cudnn.benchmark = True
            
            return True
        else:
            print("⚠️ CUDA not available")
            return False
    except Exception as e:
        print(f"❌ CUDA check failed: {e}")
        return False

def check_data_quality(data):
    """بررسی کیفیت داده‌ها"""
    import numpy as np
    import pandas as pd
    
    print("🔍 Checking data quality...")
    
    # Check for NaN values
    nan_count = data.isnull().sum().sum()
    if nan_count > 0:
        print(f"⚠️ Found {nan_count} NaN values - cleaning...")
        data = data.fillna(method='ffill').fillna(method='bfill')
    
    # Check for infinite values
    inf_count = np.isinf(data.select_dtypes(include=[np.number])).sum().sum()
    if inf_count > 0:
        print(f"⚠️ Found {inf_count} infinite values - cleaning...")
        data = data.replace([np.inf, -np.inf], np.nan).fillna(method='ffill')
    
    print("✅ Data quality check complete")
    return data

def optimize_memory():
    """بهینه‌سازی حافظه"""
    import gc
    import torch
    
    print("🧹 Optimizing memory...")
    
    # Garbage collection
    collected = gc.collect()
    print(f"   🗑️ Collected {collected} objects")
    
    # Clear GPU cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        print("   🔥 GPU cache cleared")
    
    print("✅ Memory optimization complete")

if __name__ == "__main__":
    apply_quick_fixes()
