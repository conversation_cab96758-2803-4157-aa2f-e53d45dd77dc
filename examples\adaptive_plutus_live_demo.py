"""
مثال زنده سیستم تطبیقی Plutus
Live Demo of Adaptive Plutus System
"""

import os
import sys
import time
import json
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.adaptive_plutus_system import AdaptivePlutusSystem
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def live_adaptive_demo():
    """مثال زنده سیستم تطبیقی"""
    print("🚀 LIVE ADAPTIVE PLUTUS SYSTEM DEMO")
    print("=" * 60)
    print("This demo shows the adaptive system learning and improving in real-time")
    print("Press Ctrl+C to stop the demo at any time")
    print()
    
    # ایجاد سیستم تطبیقی
    system = AdaptivePlutusSystem("live_demo.db")
    
    # نمادهای تست
    symbols = ["EURUSD", "GBPUSD"]
    
    try:
        # مرحله 1: بهینه‌سازی اولیه
        print("📊 Phase 1: Initial Optimization")
        print("-" * 40)
        
        optimization_results = system.run_comprehensive_optimization(symbols)
        
        for symbol in symbols:
            if symbol in optimization_results["symbols"]:
                result = optimization_results["symbols"][symbol]
                if not result.get("error"):
                    metrics = result["performance_metrics"]
                    print(f"✅ {symbol}: Win Rate {metrics['win_rate']:.1%}, "
                          f"Profit Factor {metrics['profit_factor']:.2f}")
                else:
                    print(f"❌ {symbol}: {result['error']}")
        
        # مرحله 2: شروع یادگیری مداوم
        print("\n🧠 Phase 2: Starting Continuous Learning")
        print("-" * 40)
        print("Learning system will adapt every 2 minutes...")
        
        system.start_continuous_learning(symbols, update_interval=120)
        
        # مرحله 3: نظارت بر سیگنال‌ها
        print("\n📡 Phase 3: Real-time Signal Monitoring")
        print("-" * 40)
        
        iteration = 0
        signal_history = {symbol: [] for symbol in symbols}
        
        while iteration < 10:  # 10 iterations = ~5 minutes
            iteration += 1
            print(f"\n⏱️  Iteration {iteration}/10 - {datetime.now().strftime('%H:%M:%S')}")
            
            for symbol in symbols:
                signal = system.get_adaptive_signal(symbol, "H1")
                
                if not signal.get("error"):
                    combined = signal.get("combined_signal", {})
                    recommendation = signal.get("recommendation", {})
                    
                    # ذخیره تاریخچه سیگنال
                    signal_data = {
                        "time": datetime.now().isoformat(),
                        "trend": combined.get("trend", "neutral"),
                        "confidence": combined.get("confidence", 0),
                        "action": recommendation.get("action", "HOLD")
                    }
                    signal_history[symbol].append(signal_data)
                    
                    # نمایش سیگنال
                    print(f"  {symbol}: {signal_data['trend'].upper()} "
                          f"({signal_data['confidence']:.1%}) → {signal_data['action']}")
                    
                    # نمایش وزن‌های تطبیقی
                    if "adaptive_weights" in combined:
                        weights = combined["adaptive_weights"]
                        print(f"    Weights: Chronos={weights.get('chronos_weight', 0):.3f}, "
                              f"FinGPT={weights.get('fingpt_weight', 0):.3f}")
                else:
                    print(f"  {symbol}: ERROR - {signal['error']}")
            
            # وقفه بین iterations
            time.sleep(30)
        
        # مرحله 4: تحلیل تطور سیگنال‌ها
        print("\n📈 Phase 4: Signal Evolution Analysis")
        print("-" * 40)
        
        for symbol in symbols:
            if len(signal_history[symbol]) > 1:
                first_signal = signal_history[symbol][0]
                last_signal = signal_history[symbol][-1]
                
                confidence_change = (last_signal["confidence"] - first_signal["confidence"]) * 100
                
                print(f"{symbol} Evolution:")
                print(f"  Initial: {first_signal['trend']} ({first_signal['confidence']:.1%})")
                print(f"  Final: {last_signal['trend']} ({last_signal['confidence']:.1%})")
                print(f"  Confidence Change: {confidence_change:+.1f}%")
                
                # تحلیل ثبات
                confidences = [s["confidence"] for s in signal_history[symbol]]
                stability = 1 - (max(confidences) - min(confidences))
                print(f"  Signal Stability: {stability:.1%}")
        
        # مرحله 5: تولید گزارش یادگیری
        print("\n📋 Phase 5: Learning Report Generation")
        print("-" * 40)
        
        for symbol in symbols:
            report = system.generate_learning_report(symbol, 1)
            print(f"\n{symbol} Learning Report:")
            print("-" * 20)
            # نمایش خلاصه گزارش
            report_lines = report.split('\n')
            for line in report_lines[:15]:  # نمایش 15 خط اول
                if line.strip():
                    print(line)
            print("... (truncated)")
        
        # مرحله 6: خلاصه نهایی
        print("\n🎯 Phase 6: Final Summary")
        print("-" * 40)
        
        print("Demo completed successfully!")
        print("Key achievements:")
        print("✅ Adaptive learning system demonstrated")
        print("✅ Real-time signal generation working")
        print("✅ Weight optimization functioning")
        print("✅ Performance monitoring active")
        
        # ذخیره نتایج
        results = {
            "demo_date": datetime.now().isoformat(),
            "optimization_results": optimization_results,
            "signal_history": signal_history,
            "summary": {
                "total_iterations": iteration,
                "symbols_tested": len(symbols),
                "learning_active": True
            }
        }
        
        results_file = Path(__file__).parent.parent / "logs" / "adaptive_live_demo.json"
        results_file.parent.mkdir(exist_ok=True)
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: {results_file}")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # توقف یادگیری مداوم
        system.stop_continuous_learning()
        print("\n🔄 Learning system stopped")

def quick_performance_test():
    """تست سریع عملکرد"""
    print("\n🏃 QUICK PERFORMANCE TEST")
    print("=" * 40)
    
    system = AdaptivePlutusSystem("perf_test.db")
    
    # تست سرعت تولید سیگنال
    start_time = time.time()
    
    signals = {}
    for symbol in ["EURUSD", "GBPUSD", "USDJPY"]:
        signal = system.get_adaptive_signal(symbol, "H1")
        signals[symbol] = signal
    
    end_time = time.time()
    elapsed = end_time - start_time
    
    print(f"⏱️  Signal generation time: {elapsed:.2f} seconds")
    print(f"📊 Signals per second: {len(signals)/elapsed:.1f}")
    
    # نمایش نتایج
    for symbol, signal in signals.items():
        if not signal.get("error"):
            combined = signal.get("combined_signal", {})
            print(f"✅ {symbol}: {combined.get('trend', 'N/A')} "
                  f"({combined.get('confidence', 0):.1%})")
        else:
            print(f"❌ {symbol}: {signal['error']}")
    
    return elapsed

def main():
    """اجرای مثال زنده"""
    try:
        # تست سریع عملکرد
        performance_time = quick_performance_test()
        
        # اگر عملکرد خوب بود، ادامه دهیم
        if performance_time < 30:  # کمتر از 30 ثانیه
            print("\n✅ Performance test passed - proceeding with live demo")
            live_adaptive_demo()
        else:
            print(f"\n⚠️  Performance test slow ({performance_time:.1f}s) - skipping live demo")
            print("Consider optimizing the system or using fewer symbols")
        
    except Exception as e:
        print(f"\n❌ Main demo failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 