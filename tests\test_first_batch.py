"""Test suite for first batch of features

تست‌های کامل برای دسته اول قابلیت‌های پیاده‌سازی شده:
- Reward Redistribution
- Alpha/Beta Attribution
- Auto Drawdown Control
- Real-time Dashboard
"""

import pytest
import numpy as np
import asyncio
from datetime import datetime
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.reward_redistribution import RewardRedistributor, redistribute
from utils.alpha_beta_attribution import AlphaBetaAttributor, AlphaBetaResult
from utils.auto_drawdown_control import AutoDrawdownController, DrawdownState
from api.realtime_dashboard import (
    DashboardServer, TradingMetrics, Alert, ConnectionManager
)


class TestRewardRedistribution:
    """تست‌های مربوط به Reward Redistribution"""
    
    def test_basic_redistribution(self):
        """تست عملکرد پایه redistribution"""
        redistributor = RewardRedistributor()
        
        # حالت عادی (بدون drawdown)
        rewards = [0.01, 0.02, -0.01, 0.03]
        equity_curve = [100, 101, 103, 102, 105]
        
        adjusted = redistributor.redistribute(rewards, equity_curve)
        
        # در حالت عادی، ضریب باید 1.0 باشد
        assert adjusted == rewards
        
    def test_drawdown_redistribution(self):
        """تست redistribution در حالت drawdown"""
        redistributor = RewardRedistributor(
            thresholds=[0.05, 0.10, 0.20],
            multipliers=[1.0, 1.2, 1.5, 2.0]
        )
        
        # ایجاد drawdown 10%
        rewards = [0.01, 0.02, -0.01, 0.03]
        equity_curve = [100, 95, 94, 92, 90]  # 10% drawdown
        
        adjusted = redistributor.redistribute(rewards, equity_curve)
        
        # ضریب باید 1.5 باشد (برای drawdown 10%)
        expected = [r * 1.5 for r in rewards]
        assert adjusted == expected
        
    def test_module_function(self):
        """تست تابع redistribute ماژول"""
        rewards = [0.01, -0.02, 0.03]
        equity_curve = [100, 98, 96, 99]
        
        adjusted = redistribute(rewards, equity_curve)
        assert len(adjusted) == len(rewards)
        

class TestAlphaBetaAttribution:
    """تست‌های مربوط به Alpha/Beta Attribution"""
    
    def test_calculate_alpha_beta(self):
        """تست محاسبه آلفا و بتا"""
        attributor = AlphaBetaAttributor()
        
        # داده‌های نمونه
        np.random.seed(42)
        market_returns = np.random.normal(0.001, 0.02, 100)
        portfolio_returns = 0.5 * market_returns + np.random.normal(0.0005, 0.01, 100)
        
        result = attributor.calculate_alpha_beta(portfolio_returns, market_returns)
        
        assert isinstance(result, AlphaBetaResult)
        assert 0.4 < result.beta < 0.6  # باید نزدیک 0.5 باشد
        assert result.r_squared > 0  # باید مثبت باشد
        
    def test_rolling_alpha_beta(self):
        """تست محاسبه آلفا و بتای متحرک"""
        attributor = AlphaBetaAttributor()
        
        # داده‌های نمونه
        np.random.seed(42)
        market_returns = np.random.normal(0.001, 0.02, 200)
        portfolio_returns = market_returns + np.random.normal(0, 0.01, 200)
        
        rolling = attributor.calculate_rolling_alpha_beta(
            portfolio_returns, market_returns, window=60
        )
        
        assert 'alpha' in rolling
        assert 'beta' in rolling
        assert len(rolling['alpha']) == len(portfolio_returns)
        
    def test_decompose_returns(self):
        """تست تجزیه بازده"""
        attributor = AlphaBetaAttributor()
        
        # داده‌های نمونه
        np.random.seed(42)
        market_returns = np.random.normal(0.001, 0.02, 100)
        portfolio_returns = market_returns + 0.0005  # آلفای ثابت
        
        decomposition = attributor.decompose_returns(portfolio_returns, market_returns)
        
        assert 'total_return' in decomposition
        assert 'market_contribution' in decomposition
        assert 'alpha_contribution' in decomposition
        assert decomposition['alpha_contribution'] > 0  # آلفا باید مثبت باشد
        
    def test_kalman_filter(self):
        """تست فیلتر کالمن"""
        attributor = AlphaBetaAttributor()
        
        # داده‌های نمونه با تغییر رژیم
        np.random.seed(42)
        n1, n2 = 100, 100
        
        # رژیم اول: بتا = 0.5
        market_returns1 = np.random.normal(0.001, 0.02, n1)
        portfolio_returns1 = 0.5 * market_returns1 + np.random.normal(0, 0.01, n1)
        
        # رژیم دوم: بتا = 1.5
        market_returns2 = np.random.normal(0.001, 0.02, n2)
        portfolio_returns2 = 1.5 * market_returns2 + np.random.normal(0, 0.01, n2)
        
        # ترکیب
        market_returns = np.concatenate([market_returns1, market_returns2])
        portfolio_returns = np.concatenate([portfolio_returns1, portfolio_returns2])
        
        filtered = attributor.kalman_filter_alpha_beta(
            portfolio_returns, market_returns
        )
        
        assert 'alpha' in filtered
        assert 'beta' in filtered
        
        # بتا در انتها باید نزدیک 1.5 باشد (با tolerance بیشتر)
        final_beta = filtered['beta'][-1]
        assert 0.8 < final_beta < 2.0, f"Beta {final_beta} not in expected range"
        

class TestAutoDrawdownControl:
    """تست‌های مربوط به Auto Drawdown Control"""
    
    def test_update_equity(self):
        """تست به‌روزرسانی equity"""
        controller = AutoDrawdownController()
        
        # شبیه‌سازی حرکت قیمت
        equities = [100, 105, 103, 98, 95, 97, 100]
        
        for equity in equities:
            controller.update_equity(equity)
            
        # بررسی محاسبه drawdown
        assert controller.current_state.max_drawdown > 0
        assert controller.current_state.peak_value == 105
        assert controller.current_state.trough_value == 95
        
    def test_position_multiplier(self):
        """تست ضریب اندازه پوزیشن"""
        controller = AutoDrawdownController(
            drawdown_levels=[0.05, 0.10, 0.15, 0.20],
            position_multipliers=[1.0, 0.7, 0.4, 0.1]
        )
        
        # بدون drawdown
        controller.current_state.current_drawdown = 0.02
        assert controller.get_position_multiplier() == 1.0
        
        # drawdown 7%
        controller.current_state.current_drawdown = 0.07
        assert controller.get_position_multiplier() == 0.7
        
        # drawdown 25%
        controller.current_state.current_drawdown = 0.25
        assert controller.get_position_multiplier() == 0.1
        
    def test_adaptive_position_size(self):
        """تست محاسبه اندازه پوزیشن تطبیقی"""
        controller = AutoDrawdownController()
        
        base_size = 1000
        volatility = 0.02
        confidence = 0.8
        
        adjusted_size = controller.calculate_adaptive_position_size(
            base_size, volatility, confidence
        )
        
        assert adjusted_size < base_size  # باید کاهش یابد
        assert adjusted_size > 0
        
    def test_predict_drawdown_risk(self):
        """تست پیش‌بینی ریسک drawdown"""
        controller = AutoDrawdownController()
        
        # بازده‌های نمونه
        returns = np.random.normal(-0.001, 0.02, 100)  # بازده منفی
        
        risk = controller.predict_drawdown_risk(returns, horizon=20)
        
        assert 'expected_drawdown' in risk
        assert 'drawdown_probability' in risk
        assert 'var_95' in risk
        assert risk['expected_drawdown'] > 0
        
    def test_recovery_strategy(self):
        """تست استراتژی بازیابی"""
        controller = AutoDrawdownController()
        
        # حالت عادی
        controller.current_state.current_drawdown = 0.03
        strategy = controller.get_recovery_strategy()
        assert strategy['position_size_factor'] == 1.0
        
        # drawdown متوسط
        controller.current_state.current_drawdown = 0.08
        strategy = controller.get_recovery_strategy()
        assert strategy['position_size_factor'] == 0.7
        
        # drawdown شدید
        controller.current_state.current_drawdown = 0.15
        strategy = controller.get_recovery_strategy()
        assert strategy['position_size_factor'] == 0.3
        

class TestRealtimeDashboard:
    """تست‌های مربوط به Real-time Dashboard"""
    
    def test_dashboard_server_init(self):
        """تست ایجاد سرور داشبورد"""
        server = DashboardServer()
        
        assert server.manager is not None
        assert len(server.metrics_history) == 0
        assert len(server.alerts) == 0
        
    def test_update_metrics(self):
        """تست به‌روزرسانی معیارها"""
        server = DashboardServer()
        
        metrics = TradingMetrics(
            timestamp=datetime.now().isoformat(),
            equity=10000,
            balance=10000,
            drawdown=0.05,
            open_positions=2,
            total_trades=50,
            win_rate=0.6,
            sharpe_ratio=1.5,
            current_pnl=100,
            daily_pnl=50
        )
        
        # تست sync - فقط اضافه کردن به history
        server.metrics_history.append(metrics)
        
        assert len(server.metrics_history) == 1
        assert server.metrics_history[0] == metrics
        
    def test_send_alert(self):
        """تست ارسال هشدار"""
        server = DashboardServer()
        
        alert = Alert(
            id="test-1",
            timestamp=datetime.now().isoformat(),
            level="warning",
            message="Test alert"
        )
        
        # تست sync - فقط اضافه کردن به alerts
        server.alerts.append(alert)
        
        assert len(server.alerts) == 1
        assert server.alerts[0] == alert
        
    def test_get_chart_data(self):
        """تست دریافت داده‌های نمودار"""
        server = DashboardServer()
        
        # افزودن چند معیار
        for i in range(5):
            metrics = TradingMetrics(
                timestamp=f"2024-01-01T10:0{i}:00",
                equity=10000 + i * 100,
                balance=10000,
                drawdown=0.05,
                open_positions=2,
                total_trades=50,
                win_rate=0.6,
                sharpe_ratio=1.5,
                current_pnl=100 * i,
                daily_pnl=50
            )
            server.metrics_history.append(metrics)
            
        chart_data = server.get_chart_data(metric="equity", limit=3)
        
        assert len(chart_data["labels"]) == 3
        assert len(chart_data["data"]) == 3
        assert chart_data["data"][-1] == 10400  # آخرین equity
        
    def test_connection_manager(self):
        """تست مدیریت اتصالات"""
        manager = ConnectionManager()
        
        assert len(manager.active_connections) == 0
        
        # نمی‌توانیم WebSocket واقعی ایجاد کنیم، فقط ساختار را تست می‌کنیم
        assert hasattr(manager, 'connect')
        assert hasattr(manager, 'disconnect')
        assert hasattr(manager, 'broadcast')


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 