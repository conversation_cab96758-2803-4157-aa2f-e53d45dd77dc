"""
تست ساده سیستم تطبیقی Plutus
Simple Test for Adaptive Plutus System
"""

import os
import sys
import json
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from utils.adaptive_plutus_system import (
        AdaptivePlutusSystem, 
        PerformanceDatabase, 
        ModelPerformanceMetrics,
        AdaptiveWeights
    )
    print("✅ Successfully imported adaptive system components")
except ImportError as e:
    print(f"❌ Import error: {str(e)}")
    sys.exit(1)

def test_database_operations():
    """تست عملیات پایگاه داده"""
    print("\n🗄️  Testing Database Operations...")
    print("-" * 40)
    
    try:
        # ایجاد پایگاه داده تست
        db = PerformanceDatabase("test_adaptive.db")
        
        # تست ذخیره عملکرد
        test_metrics = ModelPerformanceMetrics(
            symbol="EURUSD",
            timeframe="H1", 
            model_name="chronos",
            timestamp=datetime.now(),
            prediction="bullish",
            confidence=0.85,
            actual_outcome="win",
            accuracy=1.0,
            profit_loss=50.0,
            market_conditions={"volatility": 0.015},
            technical_indicators={"rsi": 65.0}
        )
        
        db.save_performance(test_metrics)
        print("✅ Performance data saved successfully")
        
        # تست دریافت عملکرد
        recent_performance = db.get_recent_performance("EURUSD", "chronos", 1)
        print(f"✅ Retrieved {len(recent_performance)} performance records")
        
        # تست ذخیره وزن‌ها
        test_weights = AdaptiveWeights(
            chronos_weight=0.7,
            fingpt_weight=0.3,
            combined_threshold=0.65,
            confidence_multiplier=1.1
        )
        
        db.save_adaptive_weights("EURUSD", "H1", test_weights)
        print("✅ Adaptive weights saved successfully")
        
        # تست دریافت وزن‌ها
        latest_weights = db.get_latest_weights("EURUSD", "H1")
        if latest_weights:
            print(f"✅ Retrieved weights: Chronos={latest_weights.chronos_weight:.3f}")
        else:
            print("❌ Failed to retrieve weights")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {str(e)}")
        return False

def test_weight_optimization():
    """تست بهینه‌سازی وزن‌ها"""
    print("\n⚖️  Testing Weight Optimization...")
    print("-" * 40)
    
    try:
        from utils.adaptive_plutus_system import AdaptiveLearningEngine
        
        # ایجاد پایگاه داده و موتور یادگیری
        db = PerformanceDatabase("test_learning.db")
        learning_engine = AdaptiveLearningEngine(db)
        
        # اضافه کردن داده‌های تست
        test_data = []
        
        # Chronos با عملکرد بهتر
        for i in range(10):
            metrics = ModelPerformanceMetrics(
                symbol="EURUSD", timeframe="H1", model_name="chronos",
                timestamp=datetime.now() - timedelta(hours=i),
                prediction="bullish", confidence=0.9,
                actual_outcome="win", accuracy=1.0, profit_loss=40.0,
                market_conditions={"volatility": 0.01},
                technical_indicators={}
            )
            test_data.append(metrics)
            db.save_performance(metrics)
        
        # FinGPT با عملکرد متوسط
        for i in range(10):
            metrics = ModelPerformanceMetrics(
                symbol="EURUSD", timeframe="H1", model_name="fingpt",
                timestamp=datetime.now() - timedelta(hours=i),
                prediction="bearish", confidence=0.7,
                actual_outcome="win", accuracy=0.8, profit_loss=25.0,
                market_conditions={"volatility": 0.02},
                technical_indicators={}
            )
            test_data.append(metrics)
            db.save_performance(metrics)
        
        print(f"✅ Added {len(test_data)} test performance records")
        
        # بهینه‌سازی وزن‌ها
        optimized_weights = learning_engine.optimize_model_weights("EURUSD", "H1")
        
        print(f"✅ Weight optimization completed:")
        print(f"   Chronos Weight: {optimized_weights.chronos_weight:.3f}")
        print(f"   FinGPT Weight: {optimized_weights.fingpt_weight:.3f}")
        print(f"   Combined Threshold: {optimized_weights.combined_threshold:.3f}")
        
        # انتظار: Chronos باید وزن بیشتری داشته باشد
        if optimized_weights.chronos_weight > optimized_weights.fingpt_weight:
            print("✅ Weight optimization working correctly (Chronos > FinGPT)")
        else:
            print("⚠️  Unexpected weight distribution")
        
        return True
        
    except Exception as e:
        print(f"❌ Weight optimization test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_generation():
    """تست تولید سیگنال تطبیقی"""
    print("\n📡 Testing Adaptive Signal Generation...")
    print("-" * 40)
    
    try:
        # ایجاد سیستم تطبیقی
        system = AdaptivePlutusSystem("test_signals.db")
        
        # تست دریافت سیگنال
        signal = system.get_adaptive_signal("EURUSD", "H1")
        
        if not signal.get("error"):
            print("✅ Signal generation successful")
            
            # بررسی اجزای سیگنال
            if "combined_signal" in signal:
                combined = signal["combined_signal"]
                print(f"   Trend: {combined.get('trend', 'N/A')}")
                print(f"   Confidence: {combined.get('confidence', 0):.1%}")
                
                if "adaptive_weights" in combined:
                    weights = combined["adaptive_weights"]
                    print(f"   Adaptive Weights Applied: ✅")
                    print(f"     Chronos: {weights.get('chronos_weight', 0):.3f}")
                    print(f"     FinGPT: {weights.get('fingpt_weight', 0):.3f}")
                else:
                    print("   Using default weights")
            
            if "recommendation" in signal:
                rec = signal["recommendation"]
                print(f"   Recommendation: {rec.get('action', 'HOLD')}")
                print(f"   Reason: {rec.get('reason', 'No reason')}")
            
            return True
        else:
            print(f"❌ Signal generation failed: {signal['error']}")
            return False
        
    except Exception as e:
        print(f"❌ Signal generation test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_analysis():
    """تست تحلیل عملکرد"""
    print("\n📊 Testing Performance Analysis...")
    print("-" * 40)
    
    try:
        from utils.adaptive_plutus_system import AdaptiveLearningEngine
        
        db = PerformanceDatabase("test_analysis.db")
        learning_engine = AdaptiveLearningEngine(db)
        
        # اضافه کردن داده‌های متنوع
        for i in range(20):
            # نیمی موفق، نیمی ناموفق
            outcome = "win" if i % 2 == 0 else "loss"
            accuracy = 1.0 if outcome == "win" else 0.0
            profit = 30.0 if outcome == "win" else -15.0
            
            metrics = ModelPerformanceMetrics(
                symbol="EURUSD", timeframe="H1", model_name="chronos",
                timestamp=datetime.now() - timedelta(hours=i),
                prediction="bullish", confidence=0.8,
                actual_outcome=outcome, accuracy=accuracy, profit_loss=profit,
                market_conditions={"volatility": 0.015 + (i * 0.001)},
                technical_indicators={}
            )
            db.save_performance(metrics)
        
        # تحلیل عملکرد
        analysis = learning_engine.analyze_model_performance("EURUSD", "chronos", 1)
        
        if not analysis.get("error"):
            print("✅ Performance analysis completed:")
            print(f"   Total Predictions: {analysis['total_predictions']}")
            print(f"   Average Accuracy: {analysis['avg_accuracy']:.1%}")
            print(f"   Win Rate: {analysis['win_rate']:.1%}")
            print(f"   Average Profit: {analysis['avg_profit']:.2f}")
            
            if "performance_trend" in analysis:
                trend = analysis["performance_trend"]
                print(f"   Performance Trend: {trend.get('accuracy_trend', 'N/A')}")
            
            return True
        else:
            print(f"❌ Performance analysis failed: {analysis['error']}")
            return False
        
    except Exception as e:
        print(f"❌ Performance analysis test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """اجرای تست‌های ساده"""
    print("🧪 ADAPTIVE PLUTUS SYSTEM - SIMPLE TESTS")
    print("=" * 60)
    
    tests = [
        ("Database Operations", test_database_operations),
        ("Weight Optimization", test_weight_optimization),
        ("Signal Generation", test_signal_generation),
        ("Performance Analysis", test_performance_analysis)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results[test_name] = False
    
    # خلاصه نتایج
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Adaptive system is working correctly.")
    elif passed > total // 2:
        print("⚠️  Most tests passed. System mostly functional.")
    else:
        print("❌ Multiple test failures. System needs debugging.")
    
    # ذخیره نتایج
    try:
        results_file = Path(__file__).parent.parent / "logs" / "adaptive_test_results.json"
        results_file.parent.mkdir(exist_ok=True)
        
        test_summary = {
            "test_date": datetime.now().isoformat(),
            "total_tests": total,
            "passed_tests": passed,
            "success_rate": passed / total,
            "detailed_results": results
        }
        
        with open(results_file, 'w') as f:
            json.dump(test_summary, f, indent=2, default=str)
        
        print(f"\n💾 Test results saved to: {results_file}")
        
    except Exception as e:
        print(f"⚠️  Could not save results: {str(e)}")

if __name__ == "__main__":
    main() 