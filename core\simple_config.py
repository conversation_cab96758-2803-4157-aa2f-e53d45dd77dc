#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚙️ Simple Advanced Configuration Management
سیستم ساده مدیریت پیکربندی پیشرفته
"""

import os
import sys
import json
import yaml
from typing import Dict, List, Optional, Any, Literal
from pathlib import Path
from datetime import datetime
from pydantic import BaseModel, Field, field_validator
from pydantic_settings import BaseSettings
from enum import Enum

class LogLevel(str, Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class TradingMode(str, Enum):
    DEMO = "demo"
    LIVE = "live"
    BACKTEST = "backtest"

class DatabaseType(str, Enum):
    SQLITE = "sqlite"
    POSTGRESQL = "postgresql"
    MYSQL = "mysql"

class ProxyConfig(BaseModel):
    """تنظیمات پروکسی"""
    enabled: bool = Field(default=False, description="فعال/غیرفعال بودن پروکسی")
    http_url: str = Field(default="http://127.0.0.1:10809", description="آدرس HTTP پروکسی")
    socks_url: str = Field(default="socks5://127.0.0.1:10808", description="آدرس SOCKS پروکسی")
    timeout: int = Field(default=30, ge=1, le=300, description="زمان انتظار (ثانیه)")

class LoggingConfig(BaseModel):
    """تنظیمات لاگ"""
    level: LogLevel = Field(default=LogLevel.INFO, description="سطح لاگ")
    file_enabled: bool = Field(default=True, description="فعال بودن لاگ فایل")
    console_enabled: bool = Field(default=True, description="فعال بودن لاگ کنسول")
    file_path: str = Field(default="logs/trading_system.log", description="مسیر فایل لاگ")

class DatabaseConfig(BaseModel):
    """تنظیمات دیتابیس"""
    type: DatabaseType = Field(default=DatabaseType.SQLITE, description="نوع دیتابیس")
    host: str = Field(default="localhost", description="آدرس سرور")
    port: int = Field(default=5432, ge=1, le=65535, description="پورت اتصال")
    database: str = Field(default="trading_system.db", description="نام دیتابیس")
    username: Optional[str] = Field(default=None, description="نام کاربری")
    password: Optional[str] = Field(default=None, description="رمز عبور")
    
    def get_connection_string(self) -> str:
        """رشته اتصال دیتابیس"""
        if self.type == DatabaseType.SQLITE:
            return f"sqlite:///{self.database}"
        elif self.type == DatabaseType.POSTGRESQL:
            auth = f"{self.username}:{self.password}@" if self.username else ""
            return f"postgresql://{auth}{self.host}:{self.port}/{self.database}"
        elif self.type == DatabaseType.MYSQL:
            auth = f"{self.username}:{self.password}@" if self.username else ""
            return f"mysql+pymysql://{auth}{self.host}:{self.port}/{self.database}"
        else:
            raise ValueError(f"Unsupported database type: {self.type}")

class TradingConfig(BaseModel):
    """تنظیمات معاملاتی"""
    mode: TradingMode = Field(default=TradingMode.DEMO, description="حالت معاملاتی")
    symbols: List[str] = Field(default=["EURUSD", "GBPUSD", "USDJPY"], description="نمادهای معاملاتی")
    timeframes: List[str] = Field(default=["H1", "H4", "D1"], description="بازه‌های زمانی")
    initial_capital: float = Field(default=10000.0, ge=100.0, description="سرمایه اولیه")
    max_positions: int = Field(default=5, ge=1, le=50, description="حداکثر موقعیت‌ها")
    risk_per_trade: float = Field(default=0.02, ge=0.001, le=0.1, description="ریسک هر معامله")
    commission: float = Field(default=0.001, ge=0.0, le=0.01, description="کمیسیون")
    
    @field_validator('symbols')
    @classmethod
    def validate_symbols(cls, v):
        if not v:
            raise ValueError('At least one symbol must be specified')
        return v

class BacktestingConfig(BaseModel):
    """تنظیمات بک‌تست"""
    enabled: bool = Field(default=True, description="فعال بودن بک‌تست")
    start_date: str = Field(default="2023-01-01", description="تاریخ شروع")
    end_date: str = Field(default="2024-01-01", description="تاریخ پایان")
    data_path: str = Field(default="data", description="مسیر داده‌ها")
    
    @field_validator('start_date', 'end_date')
    @classmethod
    def validate_date_format(cls, v):
        try:
            datetime.strptime(v, '%Y-%m-%d')
        except ValueError:
            raise ValueError('Date must be in YYYY-MM-DD format')
        return v

class AIModelConfig(BaseModel):
    """تنظیمات مدل‌های AI"""
    enabled: bool = Field(default=True, description="فعال بودن AI")
    sentiment_model: str = Field(
        default="mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
        description="مدل تحلیل احساسات"
    )
    max_memory_usage: int = Field(default=4096, ge=512, description="حداکثر مصرف حافظه (MB)")
    device: Literal["cpu", "cuda", "auto"] = Field(default="auto", description="دستگاه محاسباتی")

class SecurityConfig(BaseModel):
    """تنظیمات امنیتی"""
    api_key_encryption: bool = Field(default=True, description="رمزنگاری کلید API")
    session_timeout: int = Field(default=3600, ge=300, le=86400, description="زمان انقضای جلسه")
    max_login_attempts: int = Field(default=5, ge=1, le=20, description="حداکثر تلاش ورود")

class SimpleAdvancedConfig(BaseSettings):
    """پیکربندی ساده سیستم معاملاتی"""
    
    # Basic settings
    app_name: str = Field(default="Advanced Trading System", description="نام برنامه")
    version: str = Field(default="2.0.0", description="نسخه")
    debug: bool = Field(default=False, description="حالت دیباگ")
    environment: Literal["development", "staging", "production"] = Field(
        default="development", description="محیط اجرا"
    )
    
    # Advanced settings
    auto_save_config: bool = Field(default=True, description="ذخیره خودکار تنظیمات")
    config_backup: bool = Field(default=True, description="بک‌آپ تنظیمات")
    validate_on_load: bool = Field(default=True, description="اعتبارسنجی در هنگام بارگذاری")
    
    # Component configurations
    proxy: ProxyConfig = Field(default_factory=ProxyConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    trading: TradingConfig = Field(default_factory=TradingConfig)
    backtesting: BacktestingConfig = Field(default_factory=BacktestingConfig)
    ai_models: AIModelConfig = Field(default_factory=AIModelConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    
    model_config = {
        'env_file': '.env',
        'env_file_encoding': 'utf-8',
        'case_sensitive': False,
        'env_nested_delimiter': '__'
    }
    
    @field_validator('version')
    @classmethod
    def validate_version(cls, v):
        import re
        if not re.match(r'^\d+\.\d+\.\d+$', v):
            raise ValueError('Version must be in format X.Y.Z')
        return v
    
    def save_to_file(self, file_path: str = "config.yaml") -> None:
        """ذخیره در فایل"""
        config_dict = self.model_dump()
        
        file_path = Path(file_path)
        
        if file_path.suffix.lower() == '.json':
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False, default=str)
        else:  # YAML
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
        
        print(f"✅ Configuration saved to: {file_path}")
    
    @classmethod
    def load_from_file(cls, file_path: str = "config.yaml") -> 'SimpleAdvancedConfig':
        """بارگذاری از فایل"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            print(f"⚠️ Config file not found: {file_path}, using defaults")
            return cls()
        
        try:
            if file_path.suffix.lower() == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
            else:  # YAML
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_dict = yaml.safe_load(f)
            
            return cls(**config_dict)
            
        except Exception as e:
            print(f"❌ Error loading config: {e}")
            print("Using default configuration...")
            return cls()
    
    def validate_configuration(self) -> Dict[str, Any]:
        """اعتبارسنجی کامل تنظیمات"""
        validation_results = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": []
        }
        
        # Check database connection
        try:
            connection_string = self.database.get_connection_string()
            validation_results["suggestions"].append("Database connection string generated successfully")
        except Exception as e:
            validation_results["errors"].append(f"Database configuration error: {e}")
            validation_results["is_valid"] = False
        
        # Check trading symbols
        if not self.trading.symbols:
            validation_results["errors"].append("No trading symbols specified")
            validation_results["is_valid"] = False
        
        # Security checks
        if self.environment == "production" and not self.security.api_key_encryption:
            validation_results["warnings"].append("API key encryption should be enabled in production")
        
        return validation_results
    
    def get_component_config(self, component_name: str) -> Optional[BaseModel]:
        """دریافت تنظیمات جزء خاص"""
        return getattr(self, component_name, None)
    
    def export_config_report(self) -> Dict[str, Any]:
        """صادرات گزارش تنظیمات"""
        return {
            "timestamp": datetime.now().isoformat(),
            "config": self.model_dump(),
            "validation": self.validate_configuration(),
            "environment": self.environment,
            "components": {
                "proxy": self.proxy.enabled,
                "logging": self.logging.file_enabled,
                "ai_models": self.ai_models.enabled,
                "backtesting": self.backtesting.enabled,
            }
        }

# Global configuration instance
config = SimpleAdvancedConfig()

def get_config() -> SimpleAdvancedConfig:
    """دریافت تنظیمات سراسری"""
    return config

def load_config(file_path: str = "config.yaml") -> SimpleAdvancedConfig:
    """بارگذاری تنظیمات از فایل"""
    global config
    config = SimpleAdvancedConfig.load_from_file(file_path)
    return config

def save_config(file_path: str = "config.yaml") -> None:
    """ذخیره تنظیمات در فایل"""
    config.save_to_file(file_path)

def validate_config() -> bool:
    """اعتبارسنجی تنظیمات"""
    results = config.validate_configuration()
    return results["is_valid"]

if __name__ == "__main__":
    """تست سیستم پیکربندی"""
    print("⚙️ Testing Simple Advanced Configuration Management...")
    
    # Create default config
    test_config = SimpleAdvancedConfig()
    
    # Test validation
    validation_results = test_config.validate_configuration()
    print(f"✅ Configuration valid: {validation_results['is_valid']}")
    
    # Test save/load
    test_config.save_to_file("test_simple_config.yaml")
    loaded_config = SimpleAdvancedConfig.load_from_file("test_simple_config.yaml")
    
    print("🎉 Simple Advanced Configuration Management is ready!") 