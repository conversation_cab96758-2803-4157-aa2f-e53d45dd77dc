import numpy as np
import pandas as pd
from sklearn.base import BaseEstimator, TransformerMixin
from sklearn.feature_selection import SelectKBest, f_classif

class AutoFeatureEngineering(BaseEstimator, TransformerMixin):
    """
    ماژول مهندسی ویژگی خودکار و active learning برای معاملات الگوریتمی
    """
    def __init__(self, n_features=10, add_stat_features=True, active_learning=False):
        self.n_features = n_features
        self.add_stat_features = add_stat_features
        self.active_learning = active_learning
        self.selected_features_ = None
        self.selector_ = None

    def fit(self, X, y=None):
        X_ = self._generate_features(X)
        if y is not None:
            self.selector_ = SelectKBest(f_classif, k=min(self.n_features, X_.shape[1]))
            self.selector_.fit(X_, y)
            self.selected_features_ = self.selector_.get_support(indices=True)
        else:
            self.selected_features_ = np.arange(X_.shape[1])
        return self

    def transform(self, X):
        X_ = self._generate_features(X)
        return X_[:, self.selected_features_]

    def fit_transform(self, X, y=None):
        self.fit(X, y)
        return self.transform(X)

    def _generate_features(self, X):
        X = np.asarray(X)
        features = [X]
        if self.add_stat_features:
            # ویژگی‌های آماری پایه
            features.append(np.mean(X, axis=1, keepdims=True))
            features.append(np.std(X, axis=1, keepdims=True))
            features.append(np.min(X, axis=1, keepdims=True))
            features.append(np.max(X, axis=1, keepdims=True))
            features.append(np.median(X, axis=1, keepdims=True))
        return np.concatenate(features, axis=1)

    def suggest_new_features(self, X, y=None):
        """
        پیشنهاد ویژگی‌های جدید بر اساس همبستگی و اهمیت آماری
        """
        X_ = self._generate_features(X)
        suggestions = []
        if y is not None:
            for i in range(X_.shape[1]):
                corr = np.corrcoef(X_[:, i], y)[0, 1]
                suggestions.append((f'feature_{i}', abs(corr)))
            suggestions = sorted(suggestions, key=lambda x: -x[1])
        else:
            for i in range(X_.shape[1]):
                suggestions.append((f'feature_{i}', np.std(X_[:, i])))
            suggestions = sorted(suggestions, key=lambda x: -x[1])
        return suggestions[:self.n_features]

    def select_samples_for_labeling(self, X, model, n_samples=10):
        """
        انتخاب نمونه‌های داده برای برچسب‌گذاری فعال (active learning)
        """
        # فرض: model دارای متد predict_proba است
        proba = model.predict_proba(X)
        uncertainty = 1 - np.max(proba, axis=1)
        idx = np.argsort(-uncertainty)[:n_samples]
        return idx 