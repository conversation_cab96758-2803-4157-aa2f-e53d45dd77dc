#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 Advanced Model Cache System with Proxy Support
سیستم کش پیشرفته مدل‌ها با پشتیبانی پروکسی

This system provides:
1. Internal cache for downloaded models
2. Proxy support for model downloads
3. Intelligent model management
4. Error handling and fallback mechanisms
"""

import os
import json
import pickle
import hashlib
import requests
import torch
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging
from transformers import AutoTokenizer, AutoModelForSequenceClassification, AutoModelForCausalLM
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedModelCacheSystem:
    """Advanced caching system for pre-trained models"""
    
    def __init__(self, cache_dir="./model_cache", proxy_config_path="D:/project/PROXY.json"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.proxy_config_path = proxy_config_path
        self.proxy_config = self._load_proxy_config()
        self.cached_models = {}
        self.model_registry = self._initialize_model_registry()
        
        # Create subdirectories
        (self.cache_dir / "models").mkdir(exist_ok=True)
        (self.cache_dir / "tokenizers").mkdir(exist_ok=True)
        (self.cache_dir / "metadata").mkdir(exist_ok=True)
        
        print(f"🗄️ Model cache initialized at: {self.cache_dir}")
        
    def _load_proxy_config(self) -> Optional[Dict]:
        """Load proxy configuration from file"""
        try:
            if os.path.exists(self.proxy_config_path):
                with open(self.proxy_config_path, 'r', encoding='utf-8') as f:
                    proxy_config = json.load(f)
                print(f"✅ Proxy config loaded from: {self.proxy_config_path}")
                return proxy_config
            else:
                print(f"⚠️ Proxy config not found at: {self.proxy_config_path}")
                return None
        except Exception as e:
            print(f"❌ Error loading proxy config: {e}")
            return None
    
    def _setup_proxy_environment(self):
        """Setup proxy environment variables"""
        if not self.proxy_config:
            return
        
        try:
            # Set proxy environment variables
            if 'http_proxy' in self.proxy_config:
                os.environ['HTTP_PROXY'] = self.proxy_config['http_proxy']
                os.environ['http_proxy'] = self.proxy_config['http_proxy']
                
            if 'https_proxy' in self.proxy_config:
                os.environ['HTTPS_PROXY'] = self.proxy_config['https_proxy']
                os.environ['https_proxy'] = self.proxy_config['https_proxy']
                
            if 'no_proxy' in self.proxy_config:
                os.environ['NO_PROXY'] = self.proxy_config['no_proxy']
                os.environ['no_proxy'] = self.proxy_config['no_proxy']
                
            print("🌐 Proxy environment configured")
            
        except Exception as e:
            print(f"❌ Error setting up proxy: {e}")
    
    def _initialize_model_registry(self) -> Dict[str, Dict]:
        """Initialize registry of all models with their configurations"""
        return {
            'finbert': {
                'model_name': 'ProsusAI/finbert',
                'model_type': 'AutoModelForSequenceClassification',
                'tokenizer_type': 'AutoTokenizer',
                'size_mb': 440,
                'description': 'Financial sentiment analysis',
                'category': 'sentiment_analysis'
            },
            'cryptobert': {
                'model_name': 'ElKulako/cryptobert',
                'model_type': 'AutoModelForSequenceClassification',
                'tokenizer_type': 'AutoTokenizer',
                'size_mb': 440,
                'description': 'Cryptocurrency sentiment analysis',
                'category': 'sentiment_analysis'
            },
            'light_sentiment': {
                'model_name': 'mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis',
                'model_type': 'AutoModelForSequenceClassification',
                'tokenizer_type': 'AutoTokenizer',
                'size_mb': 82,
                'description': 'Lightweight financial sentiment',
                'category': 'sentiment_analysis'
            },
            'chronos': {
                'model_name': 'amazon/chronos-t5-small',
                'model_type': 'AutoModelForCausalLM',
                'tokenizer_type': None,
                'size_mb': 250,
                'description': 'Time series forecasting',
                'category': 'time_series'
            },
            'distilbert': {
                'model_name': 'distilbert-base-uncased',
                'model_type': 'AutoModelForSequenceClassification',
                'tokenizer_type': 'AutoTokenizer',
                'size_mb': 255,
                'description': 'Lightweight BERT alternative',
                'category': 'nlp'
            },
            't5_small': {
                'model_name': 'google/flan-t5-small',
                'model_type': 'AutoModelForCausalLM',
                'tokenizer_type': 'AutoTokenizer',
                'size_mb': 242,
                'description': 'Text generation and instruction following',
                'category': 'nlp'
            },
            'bart_base': {
                'model_name': 'facebook/bart-base',
                'model_type': 'AutoModelForSequenceClassification',
                'tokenizer_type': 'AutoTokenizer',
                'size_mb': 558,
                'description': 'Text summarization and generation',
                'category': 'nlp'
            }
        }
    
    def _get_model_hash(self, model_name: str) -> str:
        """Generate hash for model caching"""
        return hashlib.md5(model_name.encode()).hexdigest()
    
    def _is_model_cached(self, model_key: str) -> bool:
        """Check if model is already cached"""
        model_hash = self._get_model_hash(model_key)
        model_path = self.cache_dir / "models" / f"{model_hash}.pkl"
        metadata_path = self.cache_dir / "metadata" / f"{model_hash}.json"
        
        return model_path.exists() and metadata_path.exists()
    
    def _save_model_to_cache(self, model_key: str, model, tokenizer=None):
        """Save model and tokenizer to cache"""
        try:
            model_hash = self._get_model_hash(model_key)
            
            # Save model
            model_path = self.cache_dir / "models" / f"{model_hash}.pkl"
            with open(model_path, 'wb') as f:
                pickle.dump(model.state_dict(), f)
            
            # Save tokenizer if exists
            if tokenizer:
                tokenizer_path = self.cache_dir / "tokenizers" / f"{model_hash}.pkl"
                with open(tokenizer_path, 'wb') as f:
                    pickle.dump(tokenizer, f)
            
            # Save metadata
            metadata = {
                'model_key': model_key,
                'model_name': self.model_registry[model_key]['model_name'],
                'cached_at': str(pd.Timestamp.now()),
                'has_tokenizer': tokenizer is not None,
                'size_mb': self.model_registry[model_key]['size_mb']
            }
            
            metadata_path = self.cache_dir / "metadata" / f"{model_hash}.json"
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            print(f"💾 Model {model_key} cached successfully")
            
        except Exception as e:
            print(f"❌ Error caching model {model_key}: {e}")
    
    def _load_model_from_cache(self, model_key: str):
        """Load model and tokenizer from cache"""
        try:
            model_hash = self._get_model_hash(model_key)
            model_config = self.model_registry[model_key]
            
            # Load metadata
            metadata_path = self.cache_dir / "metadata" / f"{model_hash}.json"
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            
            # Initialize model architecture
            model_class = getattr(__import__('transformers'), model_config['model_type'])
            model = model_class.from_pretrained(model_config['model_name'])
            
            # Load cached state dict
            model_path = self.cache_dir / "models" / f"{model_hash}.pkl"
            with open(model_path, 'rb') as f:
                state_dict = pickle.load(f)
            model.load_state_dict(state_dict)
            
            # Load tokenizer if exists
            tokenizer = None
            if metadata['has_tokenizer']:
                tokenizer_path = self.cache_dir / "tokenizers" / f"{model_hash}.pkl"
                with open(tokenizer_path, 'rb') as f:
                    tokenizer = pickle.load(f)
            
            print(f"📂 Model {model_key} loaded from cache")
            return model, tokenizer
            
        except Exception as e:
            print(f"❌ Error loading model {model_key} from cache: {e}")
            return None, None
    
    def download_and_cache_model(self, model_key: str, force_download: bool = False):
        """Download and cache a model with proxy support"""
        if not force_download and self._is_model_cached(model_key):
            print(f"✅ Model {model_key} already cached, loading from cache...")
            return self._load_model_from_cache(model_key)
        
        if model_key not in self.model_registry:
            raise ValueError(f"Unknown model key: {model_key}")
        
        model_config = self.model_registry[model_key]
        model_name = model_config['model_name']
        
        print(f"📥 Downloading model: {model_name} ({model_config['size_mb']}MB)")
        
        # Setup proxy if needed
        self._setup_proxy_environment()
        
        try:
            # Download model
            model_class = getattr(__import__('transformers'), model_config['model_type'])
            model = model_class.from_pretrained(model_name)
            
            # Download tokenizer if needed
            tokenizer = None
            if model_config['tokenizer_type']:
                tokenizer_class = getattr(__import__('transformers'), model_config['tokenizer_type'])
                tokenizer = tokenizer_class.from_pretrained(model_name)
            
            # Cache the model
            self._save_model_to_cache(model_key, model, tokenizer)
            
            print(f"✅ Model {model_key} downloaded and cached successfully")
            return model, tokenizer
            
        except Exception as e:
            print(f"❌ Error downloading model {model_key}: {e}")
            
            # Try with proxy if not already used
            if self.proxy_config and not os.environ.get('HTTP_PROXY'):
                print("🌐 Retrying with proxy...")
                self._setup_proxy_environment()
                try:
                    model_class = getattr(__import__('transformers'), model_config['model_type'])
                    model = model_class.from_pretrained(model_name)
                    
                    tokenizer = None
                    if model_config['tokenizer_type']:
                        tokenizer_class = getattr(__import__('transformers'), model_config['tokenizer_type'])
                        tokenizer = tokenizer_class.from_pretrained(model_name)
                    
                    self._save_model_to_cache(model_key, model, tokenizer)
                    print(f"✅ Model {model_key} downloaded with proxy and cached")
                    return model, tokenizer
                    
                except Exception as proxy_error:
                    print(f"❌ Proxy download also failed: {proxy_error}")
            
            return None, None
    
    def get_model(self, model_key: str, device: str = 'auto'):
        """Get model with automatic device placement"""
        model, tokenizer = self.download_and_cache_model(model_key)
        
        if model is None:
            return None, None
        
        # Auto device selection
        if device == 'auto':
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # Move model to device
        model.to(device)
        print(f"🔥 Model {model_key} moved to {device}")
        
        return model, tokenizer
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about cached models"""
        cache_info = {
            'total_models': 0,
            'total_size_mb': 0,
            'models': []
        }
        
        metadata_dir = self.cache_dir / "metadata"
        if metadata_dir.exists():
            for metadata_file in metadata_dir.glob("*.json"):
                try:
                    with open(metadata_file, 'r') as f:
                        metadata = json.load(f)
                    
                    cache_info['models'].append(metadata)
                    cache_info['total_models'] += 1
                    cache_info['total_size_mb'] += metadata.get('size_mb', 0)
                    
                except Exception as e:
                    print(f"Error reading metadata {metadata_file}: {e}")
        
        return cache_info
    
    def clear_cache(self, model_key: str = None):
        """Clear cache for specific model or all models"""
        if model_key:
            # Clear specific model
            model_hash = self._get_model_hash(model_key)
            
            model_path = self.cache_dir / "models" / f"{model_hash}.pkl"
            tokenizer_path = self.cache_dir / "tokenizers" / f"{model_hash}.pkl"
            metadata_path = self.cache_dir / "metadata" / f"{model_hash}.json"
            
            for path in [model_path, tokenizer_path, metadata_path]:
                if path.exists():
                    path.unlink()
            
            print(f"🗑️ Cache cleared for model: {model_key}")
        else:
            # Clear all cache
            import shutil
            if self.cache_dir.exists():
                shutil.rmtree(self.cache_dir)
                self.cache_dir.mkdir(exist_ok=True)
                (self.cache_dir / "models").mkdir(exist_ok=True)
                (self.cache_dir / "tokenizers").mkdir(exist_ok=True)
                (self.cache_dir / "metadata").mkdir(exist_ok=True)
            
            print("🗑️ All cache cleared")
    
    def list_available_models(self):
        """List all available models"""
        print("📋 Available Models:")
        print("=" * 60)
        
        for key, config in self.model_registry.items():
            cached = "✅ Cached" if self._is_model_cached(key) else "📥 Not Cached"
            print(f"🔹 {key}:")
            print(f"   Name: {config['model_name']}")
            print(f"   Size: {config['size_mb']}MB")
            print(f"   Category: {config['category']}")
            print(f"   Status: {cached}")
            print(f"   Description: {config['description']}")
            print()

def main():
    """Test the cache system"""
    cache_system = AdvancedModelCacheSystem()
    
    # List available models
    cache_system.list_available_models()
    
    # Get cache info
    cache_info = cache_system.get_cache_info()
    print(f"📊 Cache Info: {cache_info['total_models']} models, {cache_info['total_size_mb']}MB")

if __name__ == "__main__":
    main()
