{"command": "python utils/intelligent_memory_system.py", "timestamp": "2025-07-08T05:52:29.035829", "execution_time": 5.0837836265563965, "return_code": 1, "stdout": "Multi-Level Intelligent Memory System Test\n==================================================\nTesting memory storage...\n  Stored 20 memories\n\nTesting memory retrieval...\n  Retrieved memory: memory_490981\n  Content: {'action': 'trade', 'symbol': 'EURUSD', 'price': 1.1001, 'volume': 1010, 'result': 'loss', 'amount': 20}\n  Importance: 0.6\n\nTesting memory search...\n  Found 10 memories matching criteria\n    memory_491982: importance=0.90\n    memory_491982: importance=0.90\n    memory_491982: importance=0.90\n\nTesting pattern detection...\n  Detected 0 patterns:\n\n--- System Statistics ---\nShort-term memory:\n  Total memories: 30\n  Utilization: 0.30\n  Average importance: 0.70\nSystem:\n  Total patterns: 0\n  Last consolidation: 2025-07-08T05:52:26.489982\n\nTesting advanced search...\n  Advanced search found 10 results\n", "stderr": "INFO:__main__:Intelligent Memory System initialized\nINFO:__main__:Stored memory memory_490981 in short-term memory\nINFO:__main__:Stored memory memory_490981 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_491982 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_492981 in short-term memory\nINFO:__main__:Stored memory memory_493499 in short-term memory\nTraceback (most recent call last):\n  File \"D:\\project\\utils\\intelligent_memory_system.py\", line 970, in <module>\n    main() \n  File \"D:\\project\\utils\\intelligent_memory_system.py\", line 967, in main\n    print(f\"\\n\\u2705 Multi-Level Intelligent Memory System test completed!\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u2705' in position 2: character maps to <undefined>\n", "success": false}