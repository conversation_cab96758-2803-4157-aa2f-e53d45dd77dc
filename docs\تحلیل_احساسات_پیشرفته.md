# مستند جامع: AdvancedSentimentAnalyzer و SentimentAnalyzer

## مسئولیت
تحلیل احساسات پیشرفته چندزبانه برای متون مالی، اخبار، شبکه‌های اجتماعی و بازار.

## پارامترها
- languages: لیست زبان‌های فعال
- enable_cache: فعال‌سازی کش
- مدل‌های مختلف برای هر زبان (HuggingFace, VADER, ...)

## متدهای کلیدی
- analyze: تحلیل احساسات متن
- get_market_sentiment: تحلیل احساسات کلی بازار
- analyze_aspect_sentiment: تحلیل جنبه‌ای
- analyze_social_media: تحلیل شبکه اجتماعی
- batch_analyze: تحلیل گروهی

## نمونه کد
```python
from utils.sentiment_analyzer import AdvancedSentimentAnalyzer
analyzer = AdvancedSentimentAnalyzer(languages=['en','fa'])
result = analyzer.analyze('بازار امروز مثبت بود')
```

## مدیریت خطا
در صورت عدم دسترسی به مدل، از VADER یا fallback فارسی استفاده می‌شود.
در صورت خطا، خروجی پیش‌فرض یا پیام هشدار لاگ می‌شود.

## بهترین شیوه
- قبل از تحلیل، زبان متن را تشخیص دهید.
- برای تحلیل بازار، از get_market_sentiment استفاده کنید.

## نمودار
- نمودار توزیع احساسات بازار و روند زمانی قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول در سیستم معاملاتی یکپارچه (models/unified_trading_system.py)، محیط معاملاتی (env/trading_env.py)، داشبورد (api/realtime_dashboard.py) و مدیریت پورتفوی (portfolio/portfolio_manager.py) به صورت عملیاتی و واقعی استفاده می‌شود.
- همچنین در تست‌ها و مثال‌ها به طور گسترده تست شده است.

## وضعیت عملیاتی
✅ کاملاً عملیاتی و در جریان اصلی پروژه فعال است. 