#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 Shared Types and Enums
انواع داده و شمارشگرهای مشترک
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from decimal import Decimal

class ExchangeType(str, Enum):
    """نوع صرافی"""
    FOREX = "forex"
    CRYPTO = "crypto"
    STOCK = "stock"
    FUTURES = "futures"
    OPTIONS = "options"

class OrderType(str, Enum):
    """نوع سفارش"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"

class OrderSide(str, Enum):
    """سمت سفارش"""
    BUY = "buy"
    SELL = "sell"

class OrderStatus(str, Enum):
    """وضعیت سفارش"""
    PENDING = "pending"
    OPEN = "open"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"

class TimeInForce(str, Enum):
    """زمان اعتبار سفارش"""
    GTC = "gtc"  # Good Till Cancelled
    IOC = "ioc"  # Immediate Or Cancel
    FOK = "fok"  # Fill Or Kill
    GTD = "gtd"  # Good Till Date
    DAY = "day"  # Day Order

class PositionSide(str, Enum):
    """سمت پوزیشن"""
    LONG = "long"
    SHORT = "short"
    BOTH = "both"

@dataclass
class Order:
    """سفارش"""
    order_id: str
    symbol: str
    order_type: OrderType
    side: OrderSide
    quantity: Decimal
    price: Optional[Decimal] = None
    stop_price: Optional[Decimal] = None
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: Decimal = Decimal('0')
    average_price: Optional[Decimal] = None
    commission: Optional[Decimal] = None
    time_in_force: TimeInForce = TimeInForce.GTC
    expires_at: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    metadata: Dict[str, Any] = None

@dataclass
class Fill:
    """اجرای سفارش"""
    fill_id: str
    order_id: str
    symbol: str
    quantity: Decimal
    price: Decimal
    commission: Optional[Decimal] = None
    timestamp: Optional[str] = None

@dataclass
class ExchangeConfig:
    """تنظیمات صرافی"""
    exchange_id: str
    name: str
    exchange_type: ExchangeType
    api_url: str
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    supported_symbols: List[str] = None
    trading_enabled: bool = True
    enabled: bool = True
    sandbox: bool = False
    timeout: int = 30
    rate_limits: Dict[str, Any] = None
    metadata: Dict[str, Any] = None 