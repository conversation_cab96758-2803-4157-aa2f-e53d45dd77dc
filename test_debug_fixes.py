"""
🔧 Debug Fixes Test
تست اصلاحات دیباگ

این تست شامل:
1. تست اصلاح Sentiment Analysis
2. تست اصلاح Agent Creation
3. تست Integration مجدد
"""

import os
import sys
import time
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_sentiment_analysis_fix():
    """تست اصلاح sentiment analysis"""
    print("🔧 Testing Sentiment Analysis Fix...")
    
    try:
        from ai_models.sentiment_models import LocalHuggingFaceModel
        
        # Test with financial_roberta
        print("🔄 Testing Financial RoBERTa...")
        model = LocalHuggingFaceModel("financial_roberta")
        
        if model.load():
            print("✅ Model loaded successfully")
            
            # Test sentiment analysis
            test_text = "The market is showing strong bullish momentum"
            result = model.analyze_sentiment(test_text)
            
            print(f"✅ Sentiment analysis successful:")
            print(f"    Text: {test_text}")
            print(f"    Sentiment: {result.sentiment}")
            print(f"    Confidence: {result.confidence:.3f}")
            print(f"    Probabilities: {result.probabilities}")
            
            return True
        else:
            print("❌ Failed to load model")
            return False
            
    except Exception as e:
        print(f"❌ Sentiment analysis fix test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ensemble_sentiment_fix():
    """تست اصلاح ensemble sentiment"""
    print("\n🎯 Testing Ensemble Sentiment Fix...")
    
    try:
        from ai_models.sentiment_models import HuggingFaceSentimentManager
        
        # Initialize manager
        manager = HuggingFaceSentimentManager(prefer_local=True)
        summary = manager.initialize_all_models()
        
        if summary["initialization_successful"]:
            print(f"✅ Manager initialized: {summary['active_model_type']}")
            
            # Test analysis
            test_text = "The financial markets are experiencing unprecedented growth"
            result = manager.analyze_with_best_model(test_text)
            
            print(f"✅ Ensemble analysis successful:")
            print(f"    Text: {test_text}")
            print(f"    Model: {result.model_name}")
            print(f"    Sentiment: {result.sentiment}")
            print(f"    Confidence: {result.confidence:.3f}")
            
            return True
        else:
            print("❌ Manager initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ Ensemble sentiment fix test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_creation_fix():
    """تست اصلاح agent creation"""
    print("\n🚀 Testing Agent Creation Fix...")
    
    try:
        from training.train_rl import RLTrainingConfig, EnhancedPearlRLTrainer
        
        # Create test config
        config = RLTrainingConfig(
            model_name="Debug_Test_Agent",
            algorithm="dqn",
            num_episodes=5,
            symbols=["EURUSD"],
            enhanced_replay=True,
            use_sentiment_analysis=False  # Disable for this test
        )
        
        print("✅ Config created")
        
        # Initialize trainer
        trainer = EnhancedPearlRLTrainer(config)
        print("✅ Trainer initialized")
        
        # Test agent creation
        agent = trainer.create_agent()
        
        if agent is not None:
            print("✅ Agent created successfully")
            print(f"    Agent type: {type(agent).__name__}")
            
            # Test agent methods
            if hasattr(agent, 'act'):
                print("✅ Agent has 'act' method")
            elif hasattr(agent, 'select_action'):
                print("✅ Agent has 'select_action' method")
            else:
                print("⚠️ Agent missing action methods")
            
            return True
        else:
            print("❌ Agent is None")
            return False
            
    except Exception as e:
        print(f"❌ Agent creation fix test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_episode():
    """تست enhanced episode"""
    print("\n🎯 Testing Enhanced Episode...")
    
    try:
        from training.train_rl import RLTrainingConfig, EnhancedPearlRLTrainer
        
        # Create minimal config
        config = RLTrainingConfig(
            model_name="Debug_Episode_Agent",
            algorithm="dqn",
            num_episodes=1,
            max_steps_per_episode=5,  # Very short
            symbols=["EURUSD"],
            enhanced_replay=True,
            use_sentiment_analysis=False  # Disable for debugging
        )
        
        # Initialize trainer
        trainer = EnhancedPearlRLTrainer(config)
        
        # Prepare environment
        trainer.prepare_environment()
        print("✅ Environment prepared")
        
        # Create agent
        trainer.agent = trainer.create_agent()
        
        if trainer.agent is None:
            print("❌ Agent creation failed")
            return False
        
        print("✅ Agent created")
        
        # Test enhanced episode (if method exists)
        if hasattr(trainer, 'train_episode_enhanced'):
            print("🔄 Testing enhanced episode...")
            metrics = trainer.train_episode_enhanced(0)
            
            print("✅ Enhanced episode completed:")
            print(f"    Reward: {metrics['total_reward']:.3f}")
            print(f"    Steps: {metrics['steps']}")
            print(f"    Symbol: {metrics['symbol']}")
            
            return True
        else:
            print("⚠️ Enhanced episode method not found")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced episode test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_integration_fix():
    """تست ادغام کامل اصلاح شده"""
    print("\n🎯 Testing Complete Integration Fix...")
    
    try:
        from training.train_rl import RLTrainingConfig, EnhancedPearlRLTrainer
        
        # Create comprehensive but minimal config
        config = RLTrainingConfig(
            model_name="Debug_Integration_Agent",
            algorithm="dqn",
            num_episodes=2,
            max_steps_per_episode=3,
            symbols=["EURUSD"],
            enhanced_replay=True,
            use_sentiment_analysis=True,  # Enable for full test
            sentiment_weight=0.1
        )
        
        print("✅ Integration config created")
        
        # Initialize trainer
        trainer = EnhancedPearlRLTrainer(config)
        print("✅ Enhanced trainer initialized")
        
        # Check features
        features = []
        if hasattr(trainer, 'sentiment_manager') and trainer.sentiment_manager:
            features.append("Sentiment Analysis")
        if hasattr(trainer, 'enhanced_replay_config') and trainer.enhanced_replay_config:
            features.append("Enhanced Replay")
        
        print(f"✅ Features enabled: {', '.join(features) if features else 'None'}")
        
        # Prepare environment
        trainer.prepare_environment()
        print(f"✅ Environment prepared for {trainer.current_symbol}")
        
        # Create agent
        trainer.agent = trainer.create_agent()
        
        if trainer.agent is None:
            print("❌ Agent creation failed")
            return False
        
        print("✅ Agent created successfully")
        
        # Test one episode
        if hasattr(trainer, 'train_episode_enhanced'):
            try:
                metrics = trainer.train_episode_enhanced(0)
                print("✅ Integration test successful:")
                print(f"    Reward: {metrics['total_reward']:.3f}")
                print(f"    Steps: {metrics['steps']}")
                print(f"    Symbol: {metrics['symbol']}")
                if 'avg_sentiment' in metrics:
                    print(f"    Avg Sentiment: {metrics['avg_sentiment']:.3f}")
                
                return True
            except Exception as e:
                print(f"❌ Enhanced episode failed: {e}")
                return False
        else:
            print("⚠️ Enhanced episode method not available")
            return False
            
    except Exception as e:
        print(f"❌ Complete integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """اجرای تمام تست‌های دیباگ"""
    print("🔧 DEBUG FIXES TESTS")
    print("=" * 60)
    
    tests = [
        ("Sentiment Analysis Fix", test_sentiment_analysis_fix),
        ("Ensemble Sentiment Fix", test_ensemble_sentiment_fix),
        ("Agent Creation Fix", test_agent_creation_fix),
        ("Enhanced Episode", test_enhanced_episode),
        ("Complete Integration Fix", test_complete_integration_fix)
    ]
    
    results = {}
    start_time = time.time()
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    total_time = time.time() - start_time
    
    # Summary
    print("\n🔧 DEBUG FIXES TEST RESULTS")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    success_rate = (passed / total) * 100
    
    for test_name, result in results.items():
        status = "✅ FIXED" if result else "❌ STILL BROKEN"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Debug Success Rate: {success_rate:.1f}% ({passed}/{total})")
    print(f"⏱️ Total Debug Time: {total_time:.1f}s")
    
    if success_rate >= 80:
        print("\n🎉 DEBUG FIXES SUCCESSFUL!")
        print("✅ Major issues have been resolved")
        print("🚀 System is ready for full testing")
    elif success_rate >= 60:
        print("\n⚠️ PARTIAL DEBUG SUCCESS")
        print("🔧 Some issues remain but progress made")
    else:
        print("\n❌ DEBUG FIXES NEED MORE WORK")
        print("🛠️ Significant issues still present")
    
    return success_rate

if __name__ == "__main__":
    success_rate = main()
    exit(0 if success_rate >= 70 else 1)
