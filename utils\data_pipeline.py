import pandas as pd
from typing import List, Dict, Any

class DataPipeline:
    """
    Pipeline برای پردازش و نرمال‌سازی داده‌های معاملاتی.
    """
    def __init__(self, steps: List = None):
        self.steps = steps or []

    def add_step(self, func):
        self.steps.append(func)

    def run(self, df: pd.DataFrame) -> pd.DataFrame:
        for step in self.steps:
            df = step(df)
        return df

# نمونه‌ای از یک مرحله نرمال‌سازی

def normalize_features(df: pd.DataFrame, columns: List[str] = None) -> pd.DataFrame:
    """نرمال‌سازی ستون‌های عددی داده بین ۰ و ۱"""
    columns = columns or df.select_dtypes(include='number').columns.tolist()
    for col in columns:
        min_val = df[col].min()
        max_val = df[col].max()
        if max_val > min_val:
            df[col] = (df[col] - min_val) / (max_val - min_val)
    return df
