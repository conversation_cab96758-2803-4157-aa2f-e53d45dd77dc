#!/usr/bin/env python3
"""
🧪 تست نهایی برای بررسی حل مشکل hyperparameter_suggestions
"""

import pandas as pd
import numpy as np
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_final_fix():
    """تست نهایی"""
    print("🧪 FINAL FIX TEST")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import MultiBrainSystem, ensure_analysis_keys
        print("✅ Successfully imported with ensure_analysis_keys")
        
        # Test ensure_analysis_keys function
        print("\n🛡️ Testing ensure_analysis_keys...")
        
        # Test with empty dict
        empty_analysis = {}
        fixed_analysis = ensure_analysis_keys(empty_analysis)
        
        required_keys = ['hyperparameter_suggestions', 'config_suggestions', 'action', 'confidence']
        all_present = all(key in fixed_analysis for key in required_keys)
        
        print(f"✅ ensure_analysis_keys test: {'PASSED' if all_present else 'FAILED'}")
        print(f"   Keys added: {list(fixed_analysis.keys())}")
        
        # Test with MultiBrainSystem
        print("\n🧠 Testing MultiBrainSystem...")
        data = pd.DataFrame({
            'close': np.random.uniform(1.1000, 1.1100, 50),
            'volume': np.random.randint(1000, 10000, 50),
            'rsi': np.random.uniform(20, 80, 50)
        })
        
        multi_brain = MultiBrainSystem()
        print("✅ MultiBrainSystem initialized")
        
        # Test analysis
        analysis = multi_brain.analyze_training_situation(data, "LSTM", "EURUSD")
        print("✅ analyze_training_situation completed")
        
        # Check all required keys
        all_keys_present = all(key in analysis for key in required_keys)
        print(f"✅ All required keys present: {'YES' if all_keys_present else 'NO'}")
        
        # Check types
        hps_ok = isinstance(analysis.get('hyperparameter_suggestions'), dict)
        cs_ok = isinstance(analysis.get('config_suggestions'), dict)
        
        print(f"✅ hyperparameter_suggestions is dict: {'YES' if hps_ok else 'NO'}")
        print(f"✅ config_suggestions is dict: {'YES' if cs_ok else 'NO'}")
        
        return all_keys_present and hps_ok and cs_ok
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_problematic_data():
    """تست با داده‌های مشکل‌ساز"""
    print("\n🛡️ Testing with problematic data...")
    
    try:
        from fixed_ultimate_main import ensure_analysis_keys
        
        # Test with None
        result1 = ensure_analysis_keys(None)
        print(f"✅ None input handled: {isinstance(result1, dict)}")
        
        # Test with non-dict
        result2 = ensure_analysis_keys("not a dict")
        print(f"✅ String input handled: {isinstance(result2, dict)}")
        
        # Test with partial dict
        partial = {'action': 'test'}
        result3 = ensure_analysis_keys(partial)
        required_keys = ['hyperparameter_suggestions', 'config_suggestions', 'action', 'confidence']
        all_present = all(key in result3 for key in required_keys)
        print(f"✅ Partial dict fixed: {all_present}")
        
        return True
        
    except Exception as e:
        print(f"❌ Problematic data test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 FINAL HYPERPARAMETER_SUGGESTIONS FIX TEST")
    print("=" * 60)
    
    # Test 1: Main functionality
    test1 = test_final_fix()
    
    # Test 2: Edge cases
    test2 = test_with_problematic_data()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 FINAL TEST SUMMARY")
    print("=" * 60)
    print(f"🧠 Main functionality: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"🛡️ Edge cases: {'✅ PASSED' if test2 else '❌ FAILED'}")
    
    overall = test1 and test2
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if overall else '❌ SOME TESTS FAILED'}")
    
    if overall:
        print("\n🎉 FINAL FIX SUCCESSFUL!")
        print("💡 The hyperparameter_suggestions error is now COMPLETELY FIXED!")
        print("🛡️ All analysis results are guaranteed to have required keys.")
        print("🚀 Ready for Google Colab deployment!")
    else:
        print("\n⚠️ Some issues remain. Check the output above.")
