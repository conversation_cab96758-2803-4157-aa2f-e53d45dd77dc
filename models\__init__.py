"""
🤖 Models Package - Refactored for v2.0
پکیج مدل‌ها - بازسازی شده برای نسخه 2.0

این پکیج شامل مدل‌های AI و ML سازگار با معماری جدید است
"""

# Import from base_models (formerly ai_models)
from .base_models import (
    # Model management
    ModelManager,
    ModelRegistry,
    model_registry,
    initialize_models,

    # Base models
    HuggingFaceModel,

    # Sentiment models
    FinBERTModel,
    CryptoBERTModel,
    FinancialSentimentModel,

    # Time series models
    ChronosModel,
    TimeSeriesModel,
    TimeSeriesEnsemble,

    # Document models
    LayoutLMModel,
    BARTModel,
    DocumentAnalyzer,

    # Ensemble models
    ModelEnsemble,
    WeightedEnsemble,
    VotingEnsemble,

    # Convenience functions
    get_model,
    get_available_models,
    create_sentiment_ensemble,
    create_timeseries_ensemble
)

# Import from core
from core.base import BaseModel, ModelPrediction
from core.logger import get_logger
from core.exceptions import ModelLoadError

# Legacy imports - maintained for backward compatibility
try:
    from .rl_models import (
        RLModelFactory,
        PPOModel,
        SACModel,
        A2CModel,
        DQNModel,
        TD3Model,
        DDPGModel
    )
except ImportError:
    # Will be created if doesn't exist
    pass

try:
    from .ensemble_model import (
        EnsembleModel,
        VotingEnsemble as LegacyVotingEnsemble,
        WeightedEnsemble as LegacyWeightedEnsemble
    )
except ImportError:
    pass

try:
    from .continual_learning import (
        ContinualLearningModel,
        OnlineLearningModel,
        AdaptiveLearningModel
    )
except ImportError:
    pass

try:
    from .regime_detection import (
        RegimeDetectionModel,
        MarketRegimeAnalyzer,
        RegimeClassifier
    )
except ImportError:
    pass

try:
    from .advanced_models import (
        AdvancedTradingModel,
        HybridModel,
        MultitaskModel
    )
except ImportError:
    pass

# Legacy model factory for backward compatibility
class LegacyModelFactory:
    """کارخانه مدل‌های قدیمی برای backward compatibility"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.model_registry = model_registry
        self.model_manager = None
    
    def create_sentiment_model(self, model_type="finbert"):
        """ایجاد مدل تحلیل احساسات"""
        try:
            if model_type.lower() == "finbert":
                return FinBERTModel()
            elif model_type.lower() == "cryptobert":
                return CryptoBERTModel()
            else:
                return FinancialSentimentModel(name=model_type)
        except Exception as e:
            self.logger.error(f"Error creating sentiment model: {e}")
            return None
    
    def create_timeseries_model(self, model_type="chronos"):
        """ایجاد مدل time series"""
        try:
            if model_type.lower() == "chronos":
                return ChronosModel()
            else:
                return TimeSeriesModel(name=model_type)
        except Exception as e:
            self.logger.error(f"Error creating time series model: {e}")
            return None
    
    def create_ensemble_model(self, model_types=None):
        """ایجاد مدل ensemble"""
        try:
            if model_types is None:
                model_types = ["finbert", "cryptobert"]
            
            models = []
            for model_type in model_types:
                if "sentiment" in model_type.lower():
                    model = self.create_sentiment_model(model_type)
                    if model:
                        models.append(model)
                elif "timeseries" in model_type.lower():
                    model = self.create_timeseries_model(model_type)
                    if model:
                        models.append(model)
            
            if models:
                return WeightedEnsemble(models=models)
            else:
                return None
        except Exception as e:
            self.logger.error(f"Error creating ensemble model: {e}")
            return None
    
    def load_model(self, model_path, model_type="auto"):
        """بارگذاری مدل از فایل"""
        try:
            # Try to load from new registry first
            model = self.model_registry.get_model(model_path)
            if model:
                return model
            
            # Try to load from file
            if model_type == "auto":
                # Try to infer model type from path
                if "finbert" in model_path.lower():
                    model_type = "sentiment"
                elif "chronos" in model_path.lower():
                    model_type = "timeseries"
                else:
                    model_type = "huggingface"
            
            if model_type == "sentiment":
                model = FinancialSentimentModel(name=model_path)
            elif model_type == "timeseries":
                model = TimeSeriesModel(name=model_path)
            else:
                model = HuggingFaceModel(name=model_path, model_type=model_type)
            
            # Load the model
            if model.load_model(model_path):
                return model
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Error loading model: {e}")
            return None
    
    def get_available_models(self):
        """دریافت مدل‌های موجود"""
        return get_available_models()
    
    def get_model_info(self, model_name):
        """دریافت اطلاعات مدل"""
        model = self.model_registry.get_model(model_name)
        if model:
            return model.get_model_info()
        return None

# Model adapter for legacy code
class ModelAdapter:
    """آداپتور مدل برای حفظ سازگاری"""
    
    def __init__(self):
        self.factory = LegacyModelFactory()
        self.logger = get_logger(__name__)
    
    def predict(self, model_name, input_data, **kwargs):
        """پیش‌بینی با مدل"""
        try:
            model = model_registry.get_model(model_name)
            if not model:
                # Try to load model
                model = self.factory.load_model(model_name)
            
            if model:
                return model.predict(input_data, **kwargs)
            else:
                self.logger.error(f"Model not found: {model_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"Prediction error: {e}")
            return None
    
    def batch_predict(self, model_name, batch_data, **kwargs):
        """پیش‌بینی دسته‌ای"""
        try:
            model = model_registry.get_model(model_name)
            if not model:
                model = self.factory.load_model(model_name)
            
            if model:
                return model.batch_predict(batch_data, **kwargs)
            else:
                self.logger.error(f"Model not found: {model_name}")
                return []
                
        except Exception as e:
            self.logger.error(f"Batch prediction error: {e}")
            return []
    
    def get_model_status(self, model_name):
        """وضعیت مدل"""
        try:
            model_info = model_registry.get_model_info(model_name)
            if model_info:
                return {
                    "name": model_info.name,
                    "status": model_info.status,
                    "type": model_info.model_type,
                    "loaded": model_info.status == "loaded",
                    "memory_usage": model_info.memory_usage,
                    "usage_count": model_info.usage_count
                }
            return None
        except Exception as e:
            self.logger.error(f"Error getting model status: {e}")
            return None

# Create global instances
legacy_factory = LegacyModelFactory()
model_adapter = ModelAdapter()

# Wrapper functions for backward compatibility
def create_model(model_type, **kwargs):
    """ایجاد مدل - wrapper برای backward compatibility"""
    if model_type in ["sentiment", "finbert"]:
        return legacy_factory.create_sentiment_model(model_type)
    elif model_type in ["timeseries", "chronos"]:
        return legacy_factory.create_timeseries_model(model_type)
    elif model_type == "ensemble":
        return legacy_factory.create_ensemble_model()
    else:
        return HuggingFaceModel(name=model_type, model_type=model_type, config=kwargs)

def load_model(model_path, **kwargs):
    """بارگذاری مدل - wrapper برای backward compatibility"""
    return legacy_factory.load_model(model_path, **kwargs)

def predict(model_name, input_data, **kwargs):
    """پیش‌بینی - wrapper برای backward compatibility"""
    return model_adapter.predict(model_name, input_data, **kwargs)

def batch_predict(model_name, batch_data, **kwargs):
    """پیش‌بینی دسته‌ای - wrapper برای backward compatibility"""
    return model_adapter.batch_predict(model_name, batch_data, **kwargs)

# Export all available functions and classes
__all__ = [
    # New AI models
    "ModelManager",
    "ModelRegistry", 
    "model_registry",
    "initialize_models",
    
    # Base models
    "BaseModel",
    "ModelPrediction",
    "HuggingFaceModel",
    
    # Sentiment models
    "FinBERTModel",
    "CryptoBERTModel",
    "FinancialSentimentModel",
    
    # Time series models
    "ChronosModel",
    "TimeSeriesModel",
    "TimeSeriesEnsemble",
    
    # Document models
    "LayoutLMModel",
    "BARTModel",
    "DocumentAnalyzer",
    
    # Ensemble models
    "ModelEnsemble",
    "WeightedEnsemble",
    "VotingEnsemble",
    
    # Convenience functions
    "get_model",
    "get_available_models",
    "create_sentiment_ensemble",
    "create_timeseries_ensemble",
    
    # Legacy compatibility
    "LegacyModelFactory",
    "ModelAdapter",
    "legacy_factory",
    "model_adapter",
    
    # Wrapper functions
    "create_model",
    "load_model",
    "predict",
    "batch_predict"
]

# Version info
__version__ = "2.0.0"
__author__ = "Trading System Team"

# Migration message
import warnings
warnings.warn(
    "The models package has been refactored for v2.0. "
    "Please update your imports to use the new ai_models module. "
    "Legacy imports are maintained for backward compatibility but may be removed in future versions.",
    DeprecationWarning,
    stacklevel=2
)

# Initialize models
def initialize_models_package():
    """مقداردهی اولیه پکیج مدل‌ها"""
    logger = get_logger(__name__)
    
    try:
        # Initialize AI models
        model_manager = initialize_models()
        
        # Update legacy factory
        legacy_factory.model_manager = model_manager
        
        logger.info("✅ Models package initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Models initialization failed: {e}")
        return False

# Auto-initialize when imported
if __name__ != "__main__":
    initialize_models_package()
