#!/usr/bin/env python3
"""
🧪 تست سیستم مدل‌های مالی ساده
تست خودکار بدون نیاز به input کاربر
"""

from simple_financial_models import SimpleFinancialAnalyzer
import time

def test_without_token():
    """تست بدون token"""
    print("🧪 تست بدون Token")
    print("=" * 30)
    
    # راه‌اندازی بدون token
    analyzer = SimpleFinancialAnalyzer(hf_token=None)
    
    # تست تحلیل تکی
    print("\n📰 تست تحلیل تکی:")
    test_text = "Apple stock rises 10% on strong earnings report"
    
    start_time = time.time()
    result = analyzer.analyze_sentiment(test_text)
    end_time = time.time()
    
    print(f"متن: {test_text}")
    print(f"نتیجه: {result}")
    print(f"زمان: {end_time - start_time:.2f} ثانیه")
    
    return result, analyzer

def test_multiple_analysis(analyzer):
    """تست تحلیل چندگانه"""
    print("\n📊 تست تحلیل چندگانه:")
    print("-" * 40)
    
    test_news = [
        "Apple stock jumps 15% after record quarterly earnings",
        "Market crashes amid economic uncertainty fears",
        "Stable trading observed in major indices today",
        "Tech sector leads market rally with innovation",
        "Banking sector faces regulatory challenges"
    ]
    
    start_time = time.time()
    results = analyzer.analyze_multiple(test_news)
    end_time = time.time()
    
    print(f"تحلیل {len(test_news)} خبر در {end_time - start_time:.2f} ثانیه")
    
    for i, (news, result) in enumerate(zip(test_news, results)):
        if "error" not in result:
            print(f"{i+1}. {result['sentiment'].upper()} ({result['confidence']:.2f}) - {news[:50]}...")
        else:
            print(f"{i+1}. ❌ خطا: {news[:50]}...")
    
    return results

def test_market_sentiment(analyzer):
    """تست احساس بازار"""
    print("\n📈 تست احساس کلی بازار:")
    print("-" * 35)
    
    news_data = [
        "Federal Reserve signals dovish stance on rates",
        "Strong corporate earnings drive optimism",
        "Economic indicators show steady growth",
        "Market volatility increases on geopolitical tensions",
        "Technology sector shows mixed signals"
    ]
    
    start_time = time.time()
    market_sentiment = analyzer.get_market_sentiment(news_data)
    end_time = time.time()
    
    print(f"تحلیل بازار در {end_time - start_time:.2f} ثانیه")
    
    if "error" not in market_sentiment:
        print(f"🎯 حالت کلی: {market_sentiment['overall_sentiment']}")
        print(f"📊 مثبت: {market_sentiment['positive_ratio']:.1%}")
        print(f"📊 منفی: {market_sentiment['negative_ratio']:.1%}")
        print(f"📊 خنثی: {market_sentiment['neutral_ratio']:.1%}")
        print(f"🎲 میانگین اطمینان: {market_sentiment['average_confidence']:.2f}")
        print(f"⚙️ روش: {market_sentiment['method_used']}")
    else:
        print(f"❌ خطا: {market_sentiment['error']}")
    
    return market_sentiment

def test_trading_signals(analyzer):
    """تست سیگنال معاملاتی"""
    print("\n🚦 تست سیگنال‌های معاملاتی:")
    print("-" * 38)
    
    scenarios = [
        {
            "name": "صعودی قوی",
            "news": [
                "Record corporate profits boost market confidence",
                "Economic growth accelerates beyond expectations",
                "Central bank maintains accommodative policy"
            ],
            "price_trend": "up"
        },
        {
            "name": "نزولی قوی", 
            "news": [
                "Market panic spreads on inflation concerns",
                "Corporate earnings disappoint across sectors",
                "Economic indicators signal recession risk"
            ],
            "price_trend": "down"
        },
        {
            "name": "مختلط",
            "news": [
                "Mixed signals from economic data",
                "Some sectors show strength while others struggle",
                "Market sentiment remains uncertain"
            ],
            "price_trend": "neutral"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📈 سناریو: {scenario['name']}")
        signal = analyzer.generate_trading_signal(scenario['news'], scenario['price_trend'])
        print(f"🎯 سیگنال: {signal}")
    
    return True

def test_caching(analyzer):
    """تست کش کردن"""
    print("\n💾 تست کش کردن:")
    print("-" * 25)
    
    test_text = "Apple stock performance shows strong momentum"
    
    # اولین بار
    start_time = time.time()
    result1 = analyzer.analyze_sentiment(test_text)
    time1 = time.time() - start_time
    
    # دومین بار (از کش)
    start_time = time.time()
    result2 = analyzer.analyze_sentiment(test_text)
    time2 = time.time() - start_time
    
    print(f"اولین بار: {time1:.3f} ثانیه")
    print(f"دومین بار: {time2:.3f} ثانیه")
    print(f"بهبود سرعت: {(time1-time2)/time1*100:.1f}%")
    print(f"نتایج یکسان: {result1 == result2}")
    
    return time1, time2

def comprehensive_test():
    """تست جامع"""
    print("🚀 تست جامع سیستم مدل‌های مالی ساده")
    print("=" * 50)
    
    # تست اصلی
    result, analyzer = test_without_token()
    
    if "error" in result:
        print(f"❌ سیستم کار نمی‌کند: {result['error']}")
        return False
    
    print("✅ سیستم اصلی کار می‌کند")
    
    # تست‌های تکمیلی
    test_multiple_analysis(analyzer)
    test_market_sentiment(analyzer)
    test_trading_signals(analyzer)
    test_caching(analyzer)
    
    print("\n" + "=" * 50)
    print("🎉 همه تست‌ها با موفقیت انجام شد!")
    print("✅ سیستم آماده استفاده است")
    
    return True

if __name__ == "__main__":
    comprehensive_test() 