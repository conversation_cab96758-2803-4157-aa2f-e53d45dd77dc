# -*- coding: utf-8 -*-
"""
Encoding Configuration
تنظیمات کدگذاری برای پروژه
"""

import sys
import os

# Set default encoding
if sys.version_info[0] >= 3:
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# Environment variables for encoding
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'

print("✅ Encoding configuration loaded")
