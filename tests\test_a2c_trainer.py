import unittest
import pandas as pd
import numpy as np
from models.rl_models.a2c_trainer import A2CTrainer, TradingEnvironment

class TestA2CTrainer(unittest.TestCase):
    def setUp(self):
        # بارگذاری داده‌های تستی
        self.market_data = pd.DataFrame({
            'open': np.random.random(100) + 100,
            'close': np.random.random(100) + 100,
            'volume': np.random.randint(1000, 10000, 100)
        })
    
    def test_environment_creation(self):
        """تست ایجاد محیط معاملاتی"""
        env = TradingEnvironment(self.market_data)
        obs, _ = env.reset()
        
        # بررسی ابعاد و محدوده مشاهدات
        self.assertEqual(len(obs), 10)  # 10 ویژگی
        self.assertTrue(np.all(np.isfinite(obs)))
    
    def test_a2c_training(self):
        """تست آموزش مدل A2C"""
        trainer = A2CTrainer(self.market_data)
        result = trainer.train(total_timesteps=1000)
        
        # بررسی موفقیت آموزش
        self.assertTrue(result)
    
    def test_performance_metrics(self):
        """تست محاسبه معیارهای عملکرد"""
        trainer = A2CTrainer(self.market_data)
        trainer.train(total_timesteps=1000)
        
        performance = trainer._evaluate_performance()
        
        # بررسی معیارها
        self.assertIn('total_reward', performance)
        self.assertIn('sharpe_ratio', performance)
        self.assertIn('max_drawdown', performance)
        
        # بررسی محدوده معیارها
        self.assertTrue(performance['sharpe_ratio'] is not None)
        self.assertTrue(-1 <= performance['max_drawdown'] <= 0)
    
    def test_pearl_integration(self):
        """تست یکپارچه‌سازی با Pearl"""
        class MockPearl:
            def log_model_performance(self, model_name, metrics):
                self.logged = True
        
        pearl = MockPearl()
        trainer = A2CTrainer(self.market_data, pearl_agent=pearl)
        trainer.train(total_timesteps=1000)
        
        # بررسی ثبت عملکرد در Pearl
        self.assertTrue(hasattr(pearl, 'logged'))

if __name__ == '__main__':
    unittest.main() 