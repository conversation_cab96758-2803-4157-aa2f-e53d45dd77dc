#!/usr/bin/env python3
"""
🧪 Comprehensive Test for Integrated AI Trading System
تست جامع برای سیستم یکپارچه معاملاتی با هوش مصنوعی

✅ Tests:
- Core modules initialization
- AI models integration
- Sentiment analysis
- Unified trading system
- Portfolio management
- Environment integration
"""

import sys
import os
import asyncio
from datetime import datetime
import pandas as pd
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_core_modules():
    """تست ماژول‌های هسته"""
    print("🏗️ Testing Core Modules...")
    
    try:
        # Test config
        from core.config import get_config, validate_config
        config = get_config()
        print(f"✅ Config loaded: {config.version}")
        
        # Test logger
        from core.logger import get_logger
        logger = get_logger("test")
        logger.info("Test logging system")
        print("✅ Logger system working")
        
        # Test registry
        from core.base import registry
        print(f"✅ Component registry initialized: {len(registry.components)} components")
        
        # Test utilities
        from core.utils import get_system_info, performance_monitor
        system_info = get_system_info()
        print(f"✅ System info: {system_info.get('platform', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Core modules test failed: {e}")
        return False

def test_ai_models():
    """تست مدل‌های هوش مصنوعی"""
    print("\n🤖 Testing AI Models...")
    
    try:
        # Test AI models initialization
        from ai_models import initialize_models, create_sentiment_ensemble
        
        model_manager = initialize_models()
        print("✅ AI model manager initialized")
        
        # Test sentiment ensemble
        sentiment_ensemble = create_sentiment_ensemble()
        if sentiment_ensemble:
            print("✅ Sentiment ensemble created")
            
            # Test sentiment analysis
            test_text = "Bitcoin shows strong upward momentum in trading"
            result = sentiment_ensemble.analyze(test_text)
            if result:
                print(f"✅ Sentiment analysis working: {result.label} ({result.score:.2f})")
            else:
                print("⚠️ Sentiment analysis returned None")
            
            # Test market sentiment
            market_sentiment = sentiment_ensemble.get_market_sentiment()
            print(f"✅ Market sentiment: {market_sentiment}")
        else:
            print("⚠️ Sentiment ensemble not available")
        
        return True
        
    except Exception as e:
        print(f"❌ AI models test failed: {e}")
        return False

def test_unified_trading_system():
    """تست سیستم یکپارچه معاملاتی"""
    print("\n💹 Testing Unified Trading System...")
    
    try:
        from models.unified_trading_system import UnifiedTradingSystem
        
        # Initialize trading system
        trading_system = UnifiedTradingSystem()
        print("✅ Unified trading system initialized")
        
        # Initialize models
        symbols = ['EURUSD', 'GBPUSD']
        trading_system.initialize_models(symbols)
        print(f"✅ Models initialized for {symbols}")
        
        # Test signal generation
        test_news = [
            "EURUSD shows bullish momentum in European session",
            "Economic indicators support EUR strength",
            "Technical analysis suggests EURUSD uptrend"
        ]
        
        signal = trading_system.get_unified_signal(
            symbol='EURUSD',
            timeframe='H1',
            news_texts=test_news
        )
        
        print(f"✅ Unified signal generated:")
        print(f"   Symbol: {signal.symbol}")
        print(f"   Action: {signal.final_action}")
        print(f"   Confidence: {signal.final_confidence:.2f}")
        print(f"   Sentiment Score: {signal.sentiment_score:.2f}")
        print(f"   Reasoning: {signal.reasoning}")
        
        # Test performance summary
        performance = trading_system.get_performance_summary()
        print(f"✅ Performance summary retrieved: {len(performance)} metrics")
        
        return True
        
    except Exception as e:
        print(f"❌ Unified trading system test failed: {e}")
        return False

def test_trading_environment():
    """تست محیط معاملاتی"""
    print("\n🎯 Testing Trading Environment...")
    
    try:
        from env.trading_env import TradingEnvV2
        
        # Create sample data
        dates = pd.date_range(start='2023-01-01', periods=100, freq='H')
        data = pd.DataFrame({
            'open': np.random.randn(100).cumsum() + 1.1000,
            'high': np.random.randn(100).cumsum() + 1.1050,
            'low': np.random.randn(100).cumsum() + 1.0950,
            'close': np.random.randn(100).cumsum() + 1.1000,
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        # Initialize environment with sentiment
        env = TradingEnvV2(
            df=data,
            symbol='EURUSD',
            style='scalping',
            timeframe='H1',
            sentiment_enabled=True,
            indicators={'rsi': {'period': 14}, 'ma': {'period': 20}}
        )
        
        print("✅ Trading environment initialized with sentiment")
        
        # Test environment
        obs = env.reset()
        print(f"✅ Environment reset, observation shape: {obs.shape}")
        
        # Test a few steps
        for i in range(5):
            action = np.array([np.random.uniform(-1, 1)])
            obs, reward, done, info = env.step(action)
            
            if info.get('trade_executed', False):
                print(f"✅ Trade executed at step {i}: sentiment={info.get('sentiment_score', 0.0):.2f}")
        
        # Test sentiment stats
        sentiment_stats = env.get_sentiment_stats()
        print(f"✅ Sentiment stats: {sentiment_stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Trading environment test failed: {e}")
        return False

def test_portfolio_manager():
    """تست مدیر پورتفولیو"""
    print("\n💼 Testing Portfolio Manager...")
    
    try:
        from portfolio.portfolio_manager import PortfolioManagerV2
        
        # Initialize portfolio manager with sentiment
        portfolio = PortfolioManagerV2(
            initial_balance=10000,
            sentiment_enabled=True
        )
        
        print("✅ Portfolio manager initialized with sentiment")
        
        # Test opening position with sentiment
        test_news = [
            "EURUSD technical analysis shows bullish breakout",
            "European economic data supports EUR strength"
        ]
        
        result = portfolio.open_position(
            symbol='EURUSD',
            quantity=0.1,
            entry_price=1.1000,
            position_type='long',
            news_texts=test_news
        )
        
        print(f"✅ Position opened:")
        print(f"   Original quantity: {result['original_quantity']}")
        print(f"   Adjusted quantity: {result['adjusted_quantity']:.4f}")
        print(f"   Sentiment score: {result['sentiment_data']['sentiment_score']:.2f}")
        
        # Test closing position
        close_result = portfolio.close_position(
            symbol='EURUSD',
            exit_price=1.1050,
            news_texts=test_news
        )
        
        print(f"✅ Position closed:")
        print(f"   Profit: {close_result['profit']:.2f}")
        print(f"   Sentiment impact: {close_result['sentiment_impact']:.2f}")
        
        # Test portfolio analysis
        portfolio_analysis = portfolio.get_sentiment_portfolio_analysis()
        print(f"✅ Portfolio sentiment analysis: {portfolio_analysis}")
        
        # Test metrics
        metrics = portfolio.get_portfolio_metrics()
        print(f"✅ Portfolio metrics: Balance={metrics['balance']:.2f}, Return={metrics['total_return']:.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ Portfolio manager test failed: {e}")
        return False

def test_main_system():
    """تست سیستم اصلی"""
    print("\n🚀 Testing Main System...")
    
    try:
        from main_new import TradingSystemManager
        
        # Initialize system manager
        system_manager = TradingSystemManager()
        print("✅ System manager initialized")
        
        # Initialize system
        if system_manager.initialize():
            print("✅ System initialized successfully")
            
            # Start system
            if system_manager.start():
                print("✅ System started successfully")
                
                # Test system status
                status = system_manager.get_system_status()
                print(f"✅ System status: Running={status['running']}, Components={len(status['components'])}")
                
                # Test health check
                health = system_manager.run_health_check()
                print(f"✅ Health check: {health['overall_health']}")
                
                # Test trading signal
                signal = system_manager.get_trading_signal(
                    symbol='EURUSD',
                    timeframe='H1',
                    news_texts=[
                        "EURUSD shows strong momentum",
                        "Technical indicators bullish"
                    ]
                )
                
                if signal:
                    print(f"✅ Trading signal generated:")
                    print(f"   Action: {signal.final_action}")
                    print(f"   Confidence: {signal.final_confidence:.2f}")
                    print(f"   Sentiment: {signal.sentiment_score:.2f}")
                
                # Stop system
                system_manager.stop()
                print("✅ System stopped successfully")
                
                return True
            else:
                print("❌ Failed to start system")
                return False
        else:
            print("❌ Failed to initialize system")
            return False
            
    except Exception as e:
        print(f"❌ Main system test failed: {e}")
        return False

def generate_test_report(results):
    """تولید گزارش تست"""
    print("\n" + "="*50)
    print("📊 TEST REPORT - گزارش تست")
    print("="*50)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    failed_tests = total_tests - passed_tests
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests} ✅")
    print(f"Failed: {failed_tests} ❌")
    print(f"Success Rate: {passed_tests/total_tests:.1%}")
    
    print("\nDetailed Results:")
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print("\n" + "="*50)
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! System is ready for production.")
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
    
    return passed_tests / total_tests

async def main():
    """تابع اصلی تست"""
    print("🧪 Starting Comprehensive AI Trading System Test")
    print("="*60)
    
    # Run all tests
    test_results = {}
    
    test_results["Core Modules"] = test_core_modules()
    test_results["AI Models"] = test_ai_models()
    test_results["Unified Trading System"] = test_unified_trading_system()
    test_results["Trading Environment"] = test_trading_environment()
    test_results["Portfolio Manager"] = test_portfolio_manager()
    test_results["Main System"] = test_main_system()
    
    # Generate report
    success_rate = generate_test_report(test_results)
    
    return success_rate

if __name__ == "__main__":
    try:
        success_rate = asyncio.run(main())
        
        # Exit with appropriate code
        sys.exit(0 if success_rate == 1.0 else 1)
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1) 