#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
from datetime import datetime

# تنظیم مسیر پروژه
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

def run_debug_pipeline():
    """اجرای پایپلاین دیباگ به صورت خودکار"""
    # ایجاد پوشه لاگ اگر وجود نداشته باشد
    log_dir = os.path.join(project_root, 'logs', 'auto_debug')
    os.makedirs(log_dir, exist_ok=True)
    
    # نام فایل لاگ با برچسب زمان
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f'auto_debug_{timestamp}.log')
    
    # دستور اجرای اسکریپت دیباگ
    debug_script = os.path.join(project_root, 'utils', 'debug_pipeline.py')
    
    try:
        # اجرای اسکریپت دیباگ با ذخیره‌سازی خروجی
        with open(log_file, 'w', encoding='utf-8') as log:
            result = subprocess.run(
                [sys.executable, debug_script], 
                capture_output=True, 
                text=True, 
                check=True
            )
            log.write(result.stdout)
            log.write("\n--- ERROR OUTPUT ---\n")
            log.write(result.stderr)
        
        print(f"Debug completed. Log saved to {log_file}")
        return True
    
    except subprocess.CalledProcessError as e:
        print(f"Debug failed with error: {e}")
        with open(log_file, 'a', encoding='utf-8') as log:
            log.write(f"\nError: {e}\n")
            log.write(f"STDOUT: {e.stdout}\n")
            log.write(f"STDERR: {e.stderr}\n")
        return False

def main():
    run_debug_pipeline()

if __name__ == '__main__':
    main() 