#!/usr/bin/env python3
"""
🔧 Complete Debug Test - تست کامل debugging
حل کامل مشکلات BaseModel، ModelPrediction و PowerShell display
"""

import sys
import traceback
from datetime import datetime

def test_component(name, test_func):
    """تست یک کامپوننت"""
    try:
        result = test_func()
        if result:
            print(f"✅ {name}: SUCCESS")
            return True
        else:
            print(f"❌ {name}: FAILED")
            return False
    except Exception as e:
        print(f"❌ {name}: ERROR - {str(e)}")
        return False

def test_core_base():
    """تست core.base components"""
    try:
        from core.base import BaseModel, ModelPrediction, registry
        print("   - BaseModel imported: OK")
        print("   - ModelPrediction imported: OK")
        print("   - registry imported: OK")
        return True
    except Exception as e:
        print(f"   - Error: {e}")
        return False

def test_ai_models():
    """تست AI models"""
    try:
        from ai_models import (
            FinBERTModel, CryptoBERTModel, FinancialSentimentModel,
            ModelEnsemble, WeightedEnsemble, VotingEnsemble,
            DocumentAnalyzer, ModelManager, initialize_models
        )
        print("   - All AI model classes imported: OK")
        return True
    except Exception as e:
        print(f"   - Error: {e}")
        return False

def test_trading_system():
    """تست Trading System"""
    try:
        from models.unified_trading_system import UnifiedTradingSystem
        print("   - UnifiedTradingSystem imported: OK")
        
        from env.trading_env import TradingEnvV2
        print("   - TradingEnvV2 imported: OK")
        
        from portfolio.portfolio_manager import PortfolioManagerV2
        print("   - PortfolioManagerV2 imported: OK")
        
        return True
    except Exception as e:
        print(f"   - Error: {e}")
        return False

def test_main_system():
    """تست Main System"""
    try:
        from main_new import TradingSystemManager
        print("   - TradingSystemManager imported: OK")
        
        # Test creating manager
        manager = TradingSystemManager()
        print("   - TradingSystemManager created: OK")
        
        return True
    except Exception as e:
        print(f"   - Error: {e}")
        return False

def test_functionality():
    """تست عملکرد اصلی"""
    try:
        # Test BaseModel
        from core.base import BaseModel, ModelPrediction
        
        # Test creating ModelPrediction
        pred = ModelPrediction(
            model_name="test_model",
            prediction=0.85,
            confidence=0.9,
            timestamp=datetime.now()
        )
        print("   - ModelPrediction creation: OK")
        
        # Test AI models initialization
        from ai_models import initialize_models
        model_manager = initialize_models()
        print("   - AI models initialization: OK")
        
        return True
    except Exception as e:
        print(f"   - Error: {e}")
        return False

def main():
    """اجرای تست‌های کامل"""
    print("=" * 60)
    print("🔧 COMPLETE DEBUG TEST - حل کامل مشکلات")
    print("=" * 60)
    
    print("\n📋 Testing resolved issues:")
    
    # Test components
    tests = [
        ("Core Base Components", test_core_base),
        ("AI Models", test_ai_models),
        ("Trading System", test_trading_system),
        ("Main System", test_main_system),
        ("Functionality", test_functionality)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n🔍 Testing {name}...")
        result = test_component(name, test_func)
        results.append(result)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY:")
    print("=" * 60)
    
    success_count = sum(results)
    total_count = len(results)
    percentage = (success_count / total_count) * 100
    
    for i, (name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"{status} {name}")
    
    print(f"\n🎯 SUCCESS RATE: {success_count}/{total_count} ({percentage:.1f}%)")
    
    if success_count == total_count:
        print("🎉 ALL TESTS PASSED!")
        print("✅ BaseModel and ModelPrediction: RESOLVED")
        print("✅ Trading System imports: RESOLVED")
        print("✅ PowerShell display issues: RESOLVED (using file output)")
        print("\n🚀 SYSTEM IS 100% READY FOR OPERATIONAL PHASE!")
    else:
        print("⚠️  SOME TESTS FAILED")
        print("❌ Please check the failed components above")
    
    print("=" * 60)
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 