import logging
from logging.handlers import RotatingFileHandler
import os

def setup_logging(
    log_dir: str = "logs",
    log_file: str = "app.log",
    max_bytes: int = 2 * 1024 * 1024,
    backup_count: int = 5,
) -> None:
    """
    راه‌اندازی logging چرخشی برای کل پروژه.

    Args:
        log_dir (str): مسیر پوشه لاگ
        log_file (str): نام فایل لاگ
        max_bytes (int): حداکثر حجم هر فایل لاگ
        backup_count (int): تعداد فایل‌های پشتیبان
    """
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    log_path = os.path.join(log_dir, log_file)
    handler = RotatingFileHandler(log_path, maxBytes=max_bytes, backupCount=backup_count, encoding="utf-8")
    formatter = logging.Formatter('%(asctime)s %(levelname)s %(name)s: %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    handler.setFormatter(formatter)
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    if not any(isinstance(h, RotatingFileHandler) for h in root_logger.handlers):
        root_logger.addHandler(handler)
