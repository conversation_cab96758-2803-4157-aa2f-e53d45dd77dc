"""Proxy Manager for stable connection

مدیریت اتصال پروکسی برای پایداری ارتباط اینترنت
"""

import os
import json
import requests
import urllib3
from typing import Dict, Optional
import time
import logging

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logger = logging.getLogger(__name__)


class ProxyManager:
    """مدیریت پروکسی برای اتصال پایدار"""
    
    def __init__(self, proxy_config_path: str = "PROXY.json"):
        self.proxy_config_path = proxy_config_path
        self.proxy_settings = self._load_proxy_config()
        self.session = None
        self._setup_session()
        
    def _load_proxy_config(self) -> Dict:
        """بارگذاری تنظیمات پروکسی از فایل"""
        try:
            with open(self.proxy_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # استخراج تنظیمات پروکسی
            socks_port = config['inbounds'][0]['port']  # 10808
            http_port = config['inbounds'][1]['port']   # 10809
            
            return {
                'http': f'http://127.0.0.1:{http_port}',
                'https': f'http://127.0.0.1:{http_port}',
                'socks': f'socks5://127.0.0.1:{socks_port}'
            }
        except Exception as e:
            logger.error(f"Error loading proxy config: {e}")
            return {}
            
    def _setup_session(self):
        """راه‌اندازی session با تنظیمات پروکسی"""
        self.session = requests.Session()
        
        if self.proxy_settings:
            self.session.proxies = {
                'http': self.proxy_settings['http'],
                'https': self.proxy_settings['https']
            }
            
        # تنظیمات اضافی برای پایداری
        self.session.verify = False  # غیرفعال کردن SSL verification
        self.session.timeout = 30    # timeout 30 ثانیه
        
        # Headers برای شبیه‌سازی مرورگر
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        
    def test_connection(self) -> bool:
        """تست اتصال پروکسی"""
        test_urls = [
            'http://www.google.com',
            'http://www.github.com',
            'http://httpbin.org/ip'
        ]
        
        for url in test_urls:
            try:
                response = self.session.get(url, timeout=10)
                if response.status_code == 200:
                    logger.info(f"Successfully connected to {url}")
                    return True
            except Exception as e:
                logger.warning(f"Failed to connect to {url}: {e}")
                continue
                
        return False
        
    def get_stable_session(self) -> requests.Session:
        """دریافت session پایدار با retry logic"""
        max_retries = 3
        retry_delay = 2
        
        for i in range(max_retries):
            if self.test_connection():
                return self.session
            
            logger.warning(f"Connection test failed, retry {i+1}/{max_retries}")
            time.sleep(retry_delay)
            self._setup_session()  # بازسازی session
            
        # اگر پروکسی کار نکرد، بدون پروکسی امتحان کن
        logger.warning("Proxy connection failed, falling back to direct connection")
        self.session.proxies = {}
        return self.session
        
    def download_with_retry(self, url: str, max_retries: int = 3) -> Optional[bytes]:
        """دانلود با retry و مدیریت خطا"""
        session = self.get_stable_session()
        
        for i in range(max_retries):
            try:
                response = session.get(url, timeout=30)
                response.raise_for_status()
                return response.content
            except Exception as e:
                logger.error(f"Download failed (attempt {i+1}): {e}")
                if i < max_retries - 1:
                    time.sleep(2 ** i)  # exponential backoff
                    
        return None
        

# تنظیمات محیطی برای پروکسی
def setup_proxy_env():
    """تنظیم متغیرهای محیطی برای پروکسی"""
    proxy_manager = ProxyManager()
    
    if proxy_manager.proxy_settings:
        os.environ['HTTP_PROXY'] = proxy_manager.proxy_settings['http']
        os.environ['HTTPS_PROXY'] = proxy_manager.proxy_settings['https']
        os.environ['http_proxy'] = proxy_manager.proxy_settings['http']
        os.environ['https_proxy'] = proxy_manager.proxy_settings['https']
        
        # برای requests
        os.environ['REQUESTS_CA_BUNDLE'] = ''
        os.environ['CURL_CA_BUNDLE'] = ''
        
        logger.info("Proxy environment variables set successfully")
        

# Singleton instance
_proxy_manager = None

def get_proxy_manager() -> ProxyManager:
    """دریافت instance از ProxyManager"""
    global _proxy_manager
    if _proxy_manager is None:
        _proxy_manager = ProxyManager()
    return _proxy_manager 