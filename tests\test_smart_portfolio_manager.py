#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Smart Portfolio Manager Unit Tests
تست‌های جامع برای مدیر پرتفولیو هوشمند
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch, MagicMock
import pytest
from datetime import datetime, timedelta

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from portfolio.smart_portfolio_manager import SmartPortfolioManager, ProfitTarget, TradingOpportunity
from portfolio.advanced_risk_manager import RiskParameters
from core.base import TradingSignal

class TestProfitTarget:
    """تست‌های ProfitTarget"""
    
    def test_profit_target_creation(self):
        """تست ایجاد هدف سود"""
        target = ProfitTarget(
            target_type="daily",
            target_amount=5.0,
            current_progress=2.5,
            achieved=False
        )
        
        assert target.target_type == "daily"
        assert target.target_amount == 5.0
        assert target.current_progress == 2.5
        assert target.achieved == False
        assert target.achievement_date is None
    
    def test_profit_target_achieved(self):
        """تست هدف سود دستیابی شده"""
        achievement_time = datetime.now()
        target = ProfitTarget(
            target_type="weekly",
            target_amount=30.0,
            current_progress=35.0,
            achieved=True,
            achievement_date=achievement_time
        )
        
        assert target.achieved == True
        assert target.achievement_date == achievement_time

class TestTradingOpportunity:
    """تست‌های TradingOpportunity"""
    
    def test_trading_opportunity_creation(self):
        """تست ایجاد فرصت معاملاتی"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.85,
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        opportunity = TradingOpportunity(
            symbol="EURUSD",
            signal=signal,
            risk_reward_ratio=2.0,
            position_size=1000.0,
            expected_profit=40.0,
            confidence_score=0.85,
            priority=1
        )
        
        assert opportunity.symbol == "EURUSD"
        assert opportunity.signal == signal
        assert opportunity.risk_reward_ratio == 2.0
        assert opportunity.position_size == 1000.0
        assert opportunity.expected_profit == 40.0
        assert opportunity.confidence_score == 0.85
        assert opportunity.priority == 1

class TestSmartPortfolioManager:
    """تست‌های SmartPortfolioManager"""
    
    def setup_method(self):
        """تنظیم اولیه برای هر تست"""
        self.risk_params = RiskParameters(
            initial_capital=1000.0,
            daily_profit_target_per_symbol=5.0,
            weekly_profit_target=30.0,
            monthly_profit_target=80.0
        )
        self.portfolio_manager = SmartPortfolioManager(self.risk_params)
    
    def test_initialization(self):
        """تست مقداردهی اولیه"""
        assert self.portfolio_manager.risk_params.initial_capital == 1000.0
        assert self.portfolio_manager.profit_targets["daily"].target_amount == 5.0
        assert self.portfolio_manager.profit_targets["weekly"].target_amount == 30.0
        assert self.portfolio_manager.profit_targets["monthly"].target_amount == 80.0
        assert self.portfolio_manager.auto_trading_enabled == True
        assert self.portfolio_manager.aggressive_mode == False
        assert len(self.portfolio_manager.signals_queue) == 0
        assert len(self.portfolio_manager.opportunities_queue) == 0
    
    def test_default_initialization(self):
        """تست مقداردهی پیش‌فرض"""
        manager = SmartPortfolioManager()
        assert manager.risk_params.initial_capital == 1000.0
        assert manager.profit_targets["daily"].target_amount == 5.0
    
    def test_add_trading_signal_success(self):
        """تست افزودن سیگنال معاملاتی موفق"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.85,
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        with patch.object(self.portfolio_manager, '_analyze_signal') as mock_analyze:
            mock_opportunity = Mock()
            mock_opportunity.priority = 1
            mock_analyze.return_value = mock_opportunity
            
            success = self.portfolio_manager.add_trading_signal(signal)
            
            assert success == True
            assert len(self.portfolio_manager.signals_queue) == 1
            assert len(self.portfolio_manager.opportunities_queue) == 1
            assert self.portfolio_manager.analysis_stats["total_signals_received"] == 1
    
    def test_add_trading_signal_failure(self):
        """تست افزودن سیگنال معاملاتی ناموفق"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.50,  # Low confidence
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        with patch.object(self.portfolio_manager, '_analyze_signal') as mock_analyze:
            mock_analyze.return_value = None
            
            success = self.portfolio_manager.add_trading_signal(signal)
            
            assert success == False
            assert len(self.portfolio_manager.signals_queue) == 1
            assert len(self.portfolio_manager.opportunities_queue) == 0
            assert self.portfolio_manager.analysis_stats["total_signals_received"] == 1
    
    def test_analyze_signal_success(self):
        """تست تحلیل سیگنال موفق"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.85,
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        with patch.object(self.portfolio_manager.risk_manager, 'can_open_position') as mock_can_open:
            mock_can_open.return_value = (True, "OK")
            
            with patch.object(self.portfolio_manager.risk_manager, 'calculate_stop_loss_take_profit') as mock_calc_sl_tp:
                mock_calc_sl_tp.return_value = (1.0700, 1.1000)
                
                with patch.object(self.portfolio_manager.risk_manager, 'calculate_position_size') as mock_calc_size:
                    mock_calc_size.return_value = (1000.0, 20.0)
                    
                    opportunity = self.portfolio_manager._analyze_signal(signal)
                    
                    assert opportunity is not None
                    assert opportunity.symbol == "EURUSD"
                    assert opportunity.signal == signal
                    assert opportunity.position_size == 1000.0
                    assert opportunity.expected_profit > 0
    
    def test_analyze_signal_low_confidence(self):
        """تست تحلیل سیگنال با confidence پایین"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.50,  # Below threshold
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        opportunity = self.portfolio_manager._analyze_signal(signal)
        
        assert opportunity is None
    
    def test_analyze_signal_cannot_open_position(self):
        """تست تحلیل سیگنال که نمی‌تواند موقعیت باز کند"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.85,
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        with patch.object(self.portfolio_manager.risk_manager, 'can_open_position') as mock_can_open:
            mock_can_open.return_value = (False, "Risk limit reached")
            
            opportunity = self.portfolio_manager._analyze_signal(signal)
            
            assert opportunity is None
            assert self.portfolio_manager.analysis_stats["rejected_signals"] == 1
    
    def test_analyze_signal_zero_position_size(self):
        """تست تحلیل سیگنال با اندازه موقعیت صفر"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.85,
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        with patch.object(self.portfolio_manager.risk_manager, 'can_open_position') as mock_can_open:
            mock_can_open.return_value = (True, "OK")
            
            with patch.object(self.portfolio_manager.risk_manager, 'calculate_position_size') as mock_calc_size:
                mock_calc_size.return_value = (0.0, 0.0)  # Zero size
                
                opportunity = self.portfolio_manager._analyze_signal(signal)
                
                assert opportunity is None
    
    def test_calculate_priority_high(self):
        """تست محاسبه اولویت بالا"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.95,  # High confidence
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        priority = self.portfolio_manager._calculate_priority(signal, 3.0, 25.0)
        
        assert priority == 1  # Highest priority
    
    def test_calculate_priority_low(self):
        """تست محاسبه اولویت پایین"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.65,  # Low confidence
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        priority = self.portfolio_manager._calculate_priority(signal, 1.0, 5.0)
        
        assert priority >= 4  # Lower priority
    
    def test_execute_best_opportunity_success(self):
        """تست اجرای بهترین فرصت موفق"""
        # Create mock opportunity
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.85,
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        opportunity = TradingOpportunity(
            symbol="EURUSD",
            signal=signal,
            risk_reward_ratio=2.0,
            position_size=1000.0,
            expected_profit=40.0,
            confidence_score=0.85,
            priority=1
        )
        
        self.portfolio_manager.opportunities_queue.append(opportunity)
        
        with patch.object(self.portfolio_manager.risk_manager, 'can_open_position') as mock_can_open:
            mock_can_open.return_value = (True, "OK")
            
            with patch.object(self.portfolio_manager.risk_manager, 'open_position') as mock_open:
                mock_open.return_value = True
                
                success = self.portfolio_manager.execute_best_opportunity()
                
                assert success == True
                assert len(self.portfolio_manager.opportunities_queue) == 0
                assert self.portfolio_manager.analysis_stats["signals_acted_on"] == 1
    
    def test_execute_best_opportunity_no_opportunities(self):
        """تست اجرای بهترین فرصت بدون فرصت موجود"""
        success = self.portfolio_manager.execute_best_opportunity()
        
        assert success == False
    
    def test_execute_best_opportunity_cannot_open(self):
        """تست اجرای بهترین فرصت که نمی‌تواند باز شود"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.85,
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        opportunity = TradingOpportunity(
            symbol="EURUSD",
            signal=signal,
            risk_reward_ratio=2.0,
            position_size=1000.0,
            expected_profit=40.0,
            confidence_score=0.85,
            priority=1
        )
        
        self.portfolio_manager.opportunities_queue.append(opportunity)
        
        with patch.object(self.portfolio_manager.risk_manager, 'can_open_position') as mock_can_open:
            mock_can_open.return_value = (False, "Risk limit reached")
            
            success = self.portfolio_manager.execute_best_opportunity()
            
            assert success == False
            assert len(self.portfolio_manager.opportunities_queue) == 0
    
    def test_execute_best_opportunity_failed_execution(self):
        """تست اجرای بهترین فرصت با شکست در اجرا"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.85,
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        opportunity = TradingOpportunity(
            symbol="EURUSD",
            signal=signal,
            risk_reward_ratio=2.0,
            position_size=1000.0,
            expected_profit=40.0,
            confidence_score=0.85,
            priority=1
        )
        
        self.portfolio_manager.opportunities_queue.append(opportunity)
        
        with patch.object(self.portfolio_manager.risk_manager, 'can_open_position') as mock_can_open:
            mock_can_open.return_value = (True, "OK")
            
            with patch.object(self.portfolio_manager.risk_manager, 'open_position') as mock_open:
                mock_open.return_value = False
                
                success = self.portfolio_manager.execute_best_opportunity()
                
                assert success == False
    
    def test_check_profit_targets_daily_achieved(self):
        """تست بررسی دستیابی به هدف روزانه"""
        with patch.object(self.portfolio_manager.risk_manager, 'get_portfolio_status') as mock_status:
            mock_status.return_value = {
                "capital": {
                    "daily_pnl": 6.0,  # Above target
                    "weekly_pnl": 20.0,
                    "monthly_pnl": 50.0
                }
            }
            
            results = self.portfolio_manager.check_profit_targets()
            
            assert results["daily"] == True
            assert self.portfolio_manager.profit_targets["daily"].achieved == True
            assert self.portfolio_manager.profit_targets["daily"].current_progress == 6.0
    
    def test_check_profit_targets_not_achieved(self):
        """تست بررسی عدم دستیابی به اهداف"""
        with patch.object(self.portfolio_manager.risk_manager, 'get_portfolio_status') as mock_status:
            mock_status.return_value = {
                "capital": {
                    "daily_pnl": 2.0,  # Below target
                    "weekly_pnl": 15.0,
                    "monthly_pnl": 40.0
                }
            }
            
            results = self.portfolio_manager.check_profit_targets()
            
            assert results["daily"] == False
            assert results["weekly"] == False
            assert results["monthly"] == False
    
    def test_adjust_trading_strategy_high_risk(self):
        """تست تنظیم استراتژی معاملاتی با ریسک بالا"""
        with patch.object(self.portfolio_manager.risk_manager, 'get_risk_level') as mock_risk_level:
            from portfolio.advanced_risk_manager import RiskLevel
            mock_risk_level.return_value = RiskLevel.HIGH
            
            with patch.object(self.portfolio_manager, 'get_comprehensive_report') as mock_report:
                mock_report.return_value = {
                    "system_status": {"risk_level": "high"}
                }
                
                self.portfolio_manager.adjust_trading_strategy()
                
                assert self.portfolio_manager.aggressive_mode == False
    
    def test_adjust_trading_strategy_target_close(self):
        """تست تنظیم استراتژی معاملاتی نزدیک به هدف"""
        # Set progress to 80% of target
        self.portfolio_manager.profit_targets["daily"].current_progress = 4.0  # 80% of 5.0
        
        self.portfolio_manager.adjust_trading_strategy()
        
        assert self.portfolio_manager.aggressive_mode == False
    
    def test_adjust_trading_strategy_behind_target(self):
        """تست تنظیم استراتژی معاملاتی عقب از هدف"""
        # Set progress to 20% of target
        self.portfolio_manager.profit_targets["daily"].current_progress = 1.0  # 20% of 5.0
        
        with patch('portfolio.smart_portfolio_manager.datetime') as mock_datetime:
            mock_datetime.now.return_value.hour = 19  # After 6 PM
            
            self.portfolio_manager.adjust_trading_strategy()
            
            assert self.portfolio_manager.aggressive_mode == True
    
    def test_monitor_positions_stop_loss_buy(self):
        """تست نظارت موقعیت‌ها - stop loss برای خرید"""
        # Create mock position
        mock_position = Mock()
        mock_position.side = "buy"
        mock_position.entry_price = 1.0850
        mock_position.stop_loss = 1.0700
        mock_position.take_profit = 1.1000
        
        self.portfolio_manager.risk_manager.positions = {"EURUSD": mock_position}
        
        with patch.object(self.portfolio_manager.risk_manager, 'close_position') as mock_close:
            self.portfolio_manager.monitor_positions()
            
            # Since current_price = entry_price, no action should be taken
            mock_close.assert_not_called()
    
    def test_start_auto_trading(self):
        """تست شروع معاملات خودکار"""
        with patch('threading.Thread') as mock_thread:
            mock_thread_instance = Mock()
            mock_thread.return_value = mock_thread_instance
            
            self.portfolio_manager.start_auto_trading()
            
            assert self.portfolio_manager.running == True
            mock_thread_instance.start.assert_called_once()
    
    def test_stop_auto_trading(self):
        """تست توقف معاملات خودکار"""
        self.portfolio_manager.running = True
        
        self.portfolio_manager.stop_auto_trading()
        
        assert self.portfolio_manager.running == False
    
    def test_get_comprehensive_report(self):
        """تست گزارش جامع"""
        with patch.object(self.portfolio_manager.risk_manager, 'get_portfolio_status') as mock_status:
            mock_status.return_value = {
                "capital": {"current": 1000.0, "daily_pnl": 5.0},
                "positions": {"open_positions": 1},
                "risk": {"level": "low"}
            }
            
            with patch.object(self.portfolio_manager.risk_manager, 'get_risk_level') as mock_risk_level:
                from portfolio.advanced_risk_manager import RiskLevel
                mock_risk_level.return_value = RiskLevel.LOW
                
                report = self.portfolio_manager.get_comprehensive_report()
                
                assert "portfolio_status" in report
                assert "profit_targets" in report
                assert "trading_stats" in report
                assert "system_status" in report
                assert "timestamp" in report
                
                assert report["profit_targets"]["daily"]["target"] == 5.0
                assert report["trading_stats"]["signals_received"] == 0
                assert report["system_status"]["auto_trading_enabled"] == True
    
    def test_save_comprehensive_state(self):
        """تست ذخیره وضعیت جامع"""
        with patch.object(self.portfolio_manager.risk_manager, 'save_state') as mock_save_risk:
            with patch('builtins.open', mock_open=True) as mock_file:
                with patch('json.dump') as mock_json:
                    
                    self.portfolio_manager.save_comprehensive_state("test_state.json")
                    
                    mock_save_risk.assert_called_once()
                    mock_file.assert_called_once_with("test_state.json", 'w')
                    mock_json.assert_called_once()

class TestIntegrationScenarios:
    """تست‌های سناریوهای یکپارچه"""
    
    def setup_method(self):
        """تنظیم اولیه"""
        self.portfolio_manager = SmartPortfolioManager()
    
    def test_signal_to_execution_flow(self):
        """تست جریان از سیگنال تا اجرا"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.85,
            price=1.0850,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
        
        with patch.object(self.portfolio_manager.risk_manager, 'can_open_position') as mock_can_open:
            mock_can_open.return_value = (True, "OK")
            
            with patch.object(self.portfolio_manager.risk_manager, 'calculate_stop_loss_take_profit') as mock_calc_sl_tp:
                mock_calc_sl_tp.return_value = (1.0700, 1.1000)
                
                with patch.object(self.portfolio_manager.risk_manager, 'calculate_position_size') as mock_calc_size:
                    mock_calc_size.return_value = (1000.0, 20.0)
                    
                    with patch.object(self.portfolio_manager.risk_manager, 'open_position') as mock_open:
                        mock_open.return_value = True
                        
                        # Add signal
                        success = self.portfolio_manager.add_trading_signal(signal)
                        assert success == True
                        
                        # Execute opportunity
                        success = self.portfolio_manager.execute_best_opportunity()
                        assert success == True
                        
                        # Verify stats
                        assert self.portfolio_manager.analysis_stats["total_signals_received"] == 1
                        assert self.portfolio_manager.analysis_stats["signals_acted_on"] == 1
    
    def test_multiple_signals_prioritization(self):
        """تست اولویت‌بندی چندین سیگنال"""
        signals = [
            TradingSignal("EURUSD", "buy", 0.95, 1.0850, datetime.now(), "High conf"),
            TradingSignal("GBPUSD", "buy", 0.75, 1.2650, datetime.now(), "Med conf"),
            TradingSignal("USDJPY", "buy", 0.85, 150.50, datetime.now(), "Good conf")
        ]
        
        with patch.object(self.portfolio_manager.risk_manager, 'can_open_position') as mock_can_open:
            mock_can_open.return_value = (True, "OK")
            
            with patch.object(self.portfolio_manager.risk_manager, 'calculate_stop_loss_take_profit') as mock_calc_sl_tp:
                mock_calc_sl_tp.return_value = (1.0700, 1.1000)
                
                with patch.object(self.portfolio_manager.risk_manager, 'calculate_position_size') as mock_calc_size:
                    mock_calc_size.return_value = (1000.0, 20.0)
                    
                    # Add all signals
                    for signal in signals:
                        self.portfolio_manager.add_trading_signal(signal)
                    
                    # Check prioritization
                    assert len(self.portfolio_manager.opportunities_queue) == 3
                    
                    # First opportunity should be highest priority (lowest number)
                    first_opportunity = self.portfolio_manager.opportunities_queue[0]
                    assert first_opportunity.priority <= 2  # Should be high priority due to high confidence

# Test runner
if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"]) 