"""Multi-exchange Auto-routing

این ماژول برای مسیریابی خودکار بین صرافی‌های مختلف طراحی شده است.
شامل آربیتراژ، بهینه‌سازی هزینه اجرا، مدیریت نقدینگی و سیستم failover.
"""

import asyncio
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Set
from dataclasses import dataclass, field
from collections import defaultdict, deque
from concurrent.futures import ThreadPoolExecutor
import logging
from datetime import datetime, timedelta
from enum import Enum
import threading
import json

logger = logging.getLogger(__name__)


class ExchangeStatus(Enum):
    """وضعیت صرافی"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    MAINTENANCE = "maintenance"
    ERROR = "error"


class OrderType(Enum):
    """نوع سفارش"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"


@dataclass
class ExchangeInfo:
    """اطلاعات صرافی"""
    name: str
    api_endpoint: str
    trading_fee: float  # درصد کارمزد
    withdrawal_fee: float  # کارمزد برداشت
    min_order_size: float
    max_order_size: float
    status: ExchangeStatus = ExchangeStatus.ACTIVE
    latency: float = 0.0  # میلی‌ثانیه
    reliability_score: float = 1.0  # امتیاز قابلیت اطمینان (0-1)
    supported_pairs: Set[str] = field(default_factory=set)


@dataclass
class OrderBookData:
    """داده‌های order book"""
    exchange: str
    symbol: str
    bids: List[Tuple[float, float]]  # (قیمت، حجم)
    asks: List[Tuple[float, float]]  # (قیمت، حجم)
    timestamp: float
    

@dataclass
class ArbitrageOpportunity:
    """فرصت آربیتراژ"""
    buy_exchange: str
    sell_exchange: str
    symbol: str
    buy_price: float
    sell_price: float
    volume: float
    profit: float
    profit_percentage: float
    timestamp: float
    

@dataclass
class ExecutionOrder:
    """سفارش اجرا"""
    id: str
    exchange: str
    symbol: str
    side: str  # 'buy' یا 'sell'
    order_type: OrderType
    volume: float
    price: Optional[float] = None
    status: str = "pending"
    created_at: float = field(default_factory=time.time)
    executed_at: Optional[float] = None
    execution_price: Optional[float] = None
    fees: float = 0.0


class ExchangeConnector:
    """کانکتور صرافی (شبیه‌ساز)"""
    
    def __init__(self, exchange_info: ExchangeInfo):
        self.exchange_info = exchange_info
        self.connected = False
        self.order_books: Dict[str, OrderBookData] = {}
        self.balances: Dict[str, float] = defaultdict(float)
        
    async def connect(self) -> bool:
        """اتصال به صرافی"""
        try:
            # شبیه‌سازی اتصال
            await asyncio.sleep(0.1)
            self.connected = True
            logger.info(f"Connected to {self.exchange_info.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to {self.exchange_info.name}: {e}")
            return False
            
    async def disconnect(self):
        """قطع اتصال"""
        self.connected = False
        logger.info(f"Disconnected from {self.exchange_info.name}")
        
    async def get_order_book(self, symbol: str) -> Optional[OrderBookData]:
        """دریافت order book"""
        if not self.connected:
            return None
            
        # شبیه‌سازی order book
        base_price = 50000 if symbol == 'BTCUSD' else 3000
        spread = base_price * 0.001  # 0.1% spread
        
        # تولید bids و asks
        bids = []
        asks = []
        
        for i in range(10):
            bid_price = base_price - spread/2 - i * 0.1
            ask_price = base_price + spread/2 + i * 0.1
            
            bid_volume = np.random.exponential(scale=5)
            ask_volume = np.random.exponential(scale=5)
            
            bids.append((bid_price, bid_volume))
            asks.append((ask_price, ask_volume))
            
        return OrderBookData(
            exchange=self.exchange_info.name,
            symbol=symbol,
            bids=bids,
            asks=asks,
            timestamp=time.time()
        )
        
    async def place_order(self, order: ExecutionOrder) -> bool:
        """ثبت سفارش"""
        if not self.connected:
            return False
            
        try:
            # شبیه‌سازی اجرای سفارش
            await asyncio.sleep(0.05)  # تأخیر شبکه
            
            # محاسبه کارمزد
            order.fees = order.volume * self.exchange_info.trading_fee / 100
            
            # تنظیم قیمت اجرا
            if order.order_type == OrderType.MARKET:
                order_book = await self.get_order_book(order.symbol)
                if order_book:
                    if order.side == 'buy':
                        order.execution_price = order_book.asks[0][0]
                    else:
                        order.execution_price = order_book.bids[0][0]
            else:
                order.execution_price = order.price
                
            order.status = "executed"
            order.executed_at = time.time()
            
            logger.info(f"Order executed on {self.exchange_info.name}: {order.id}")
            return True
            
        except Exception as e:
            logger.error(f"Order execution failed on {self.exchange_info.name}: {e}")
            order.status = "failed"
            return False
            
    def get_balance(self, asset: str) -> float:
        """دریافت موجودی"""
        return self.balances.get(asset, 0.0)
        
    def update_balance(self, asset: str, amount: float):
        """به‌روزرسانی موجودی"""
        self.balances[asset] += amount


class ArbitrageEngine:
    """موتور آربیتراژ"""
    
    def __init__(self, min_profit_threshold: float = 0.5):
        """
        Parameters
        ----------
        min_profit_threshold : float
            حداقل درصد سود برای آربیتراژ
        """
        self.min_profit_threshold = min_profit_threshold
        self.opportunities: List[ArbitrageOpportunity] = []
        self.executed_arbitrages: List[ArbitrageOpportunity] = []
        
    def find_arbitrage_opportunities(self, 
                                   order_books: List[OrderBookData],
                                   symbol: str) -> List[ArbitrageOpportunity]:
        """یافتن فرصت‌های آربیتراژ
        
        Parameters
        ----------
        order_books : List[OrderBookData]
            order book های صرافی‌های مختلف
        symbol : str
            نماد معاملاتی
            
        Returns
        -------
        List[ArbitrageOpportunity]
            لیست فرصت‌های آربیتراژ
        """
        opportunities = []
        
        # مقایسه همه جفت صرافی‌ها
        for i in range(len(order_books)):
            for j in range(i + 1, len(order_books)):
                book1 = order_books[i]
                book2 = order_books[j]
                
                if not (book1.bids and book1.asks and book2.bids and book2.asks):
                    continue
                    
                # بهترین قیمت‌های خرید و فروش
                best_bid_1 = book1.bids[0][0]
                best_ask_1 = book1.asks[0][0]
                best_bid_2 = book2.bids[0][0]
                best_ask_2 = book2.asks[0][0]
                
                # بررسی آربیتراژ: خرید از صرافی 1، فروش در صرافی 2
                if best_bid_2 > best_ask_1:
                    volume = min(book1.asks[0][1], book2.bids[0][1])
                    profit = (best_bid_2 - best_ask_1) * volume
                    profit_percentage = ((best_bid_2 - best_ask_1) / best_ask_1) * 100
                    
                    if profit_percentage >= self.min_profit_threshold:
                        opportunities.append(ArbitrageOpportunity(
                            buy_exchange=book1.exchange,
                            sell_exchange=book2.exchange,
                            symbol=symbol,
                            buy_price=best_ask_1,
                            sell_price=best_bid_2,
                            volume=volume,
                            profit=profit,
                            profit_percentage=profit_percentage,
                            timestamp=time.time()
                        ))
                
                # بررسی آربیتراژ: خرید از صرافی 2، فروش در صرافی 1
                if best_bid_1 > best_ask_2:
                    volume = min(book2.asks[0][1], book1.bids[0][1])
                    profit = (best_bid_1 - best_ask_2) * volume
                    profit_percentage = ((best_bid_1 - best_ask_2) / best_ask_2) * 100
                    
                    if profit_percentage >= self.min_profit_threshold:
                        opportunities.append(ArbitrageOpportunity(
                            buy_exchange=book2.exchange,
                            sell_exchange=book1.exchange,
                            symbol=symbol,
                            buy_price=best_ask_2,
                            sell_price=best_bid_1,
                            volume=volume,
                            profit=profit,
                            profit_percentage=profit_percentage,
                            timestamp=time.time()
                        ))
        
        return opportunities
        
    def rank_opportunities(self, opportunities: List[ArbitrageOpportunity]) -> List[ArbitrageOpportunity]:
        """رتبه‌بندی فرصت‌های آربیتراژ بر اساس سود"""
        return sorted(opportunities, key=lambda x: x.profit_percentage, reverse=True)


class ExecutionCostOptimizer:
    """بهینه‌ساز هزینه اجرا"""
    
    def __init__(self):
        self.exchange_costs: Dict[str, Dict[str, float]] = {}
        self.latency_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
    def calculate_total_cost(self, 
                           exchange: str,
                           volume: float,
                           trading_fee: float,
                           slippage: float = 0.0,
                           latency_penalty: float = 0.0) -> float:
        """محاسبه کل هزینه اجرا
        
        Parameters
        ----------
        exchange : str
            نام صرافی
        volume : float
            حجم سفارش
        trading_fee : float
            کارمزد معاملات (درصد)
        slippage : float
            لغزش قیمت (درصد)
        latency_penalty : float
            جریمه تأخیر (درصد)
            
        Returns
        -------
        float
            کل هزینه به درصد
        """
        fee_cost = trading_fee
        slippage_cost = slippage
        latency_cost = latency_penalty
        
        total_cost = fee_cost + slippage_cost + latency_cost
        return total_cost
        
    def estimate_slippage(self, 
                         order_book: OrderBookData,
                         volume: float,
                         side: str) -> float:
        """تخمین لغزش قیمت
        
        Parameters
        ----------
        order_book : OrderBookData
            order book صرافی
        volume : float
            حجم سفارش
        side : str
            طرف سفارش ('buy' یا 'sell')
            
        Returns
        -------
        float
            درصد لغزش تخمینی
        """
        if side == 'buy':
            levels = order_book.asks
        else:
            levels = order_book.bids
            
        if not levels:
            return 0.0
            
        best_price = levels[0][0]
        remaining_volume = volume
        total_cost = 0.0
        
        for price, available_volume in levels:
            consumed = min(remaining_volume, available_volume)
            total_cost += consumed * price
            remaining_volume -= consumed
            
            if remaining_volume <= 0:
                break
                
        if volume > 0:
            avg_price = total_cost / volume
            slippage = abs(avg_price - best_price) / best_price * 100
            return slippage
            
        return 0.0
        
    def select_optimal_exchange(self,
                              exchanges: List[str],
                              exchange_infos: Dict[str, ExchangeInfo],
                              order_books: Dict[str, OrderBookData],
                              volume: float,
                              side: str) -> Optional[str]:
        """انتخاب صرافی بهینه بر اساس کمترین هزینه
        
        Parameters
        ----------
        exchanges : List[str]
            لیست صرافی‌های در دسترس
        exchange_infos : Dict[str, ExchangeInfo]
            اطلاعات صرافی‌ها
        order_books : Dict[str, OrderBookData]
            order book های صرافی‌ها
        volume : float
            حجم سفارش
        side : str
            طرف سفارش
            
        Returns
        -------
        Optional[str]
            نام صرافی بهینه
        """
        best_exchange = None
        lowest_cost = float('inf')
        
        for exchange in exchanges:
            if exchange not in exchange_infos or exchange not in order_books:
                continue
                
            info = exchange_infos[exchange]
            order_book = order_books[exchange]
            
            # محاسبه هزینه‌ها
            slippage = self.estimate_slippage(order_book, volume, side)
            latency_penalty = info.latency * 0.001  # تبدیل ms به درصد
            
            total_cost = self.calculate_total_cost(
                exchange, volume, info.trading_fee, slippage, latency_penalty
            )
            
            # در نظر گیری امتیاز قابلیت اطمینان
            adjusted_cost = total_cost / info.reliability_score
            
            if adjusted_cost < lowest_cost:
                lowest_cost = adjusted_cost
                best_exchange = exchange
                
        return best_exchange


class LiquidityManager:
    """مدیر نقدینگی توزیع‌شده"""
    
    def __init__(self, max_order_size_ratio: float = 0.1):
        """
        Parameters
        ----------
        max_order_size_ratio : float
            حداکثر نسبت اندازه سفارش به عمق بازار
        """
        self.max_order_size_ratio = max_order_size_ratio
        
    def split_large_order(self,
                         total_volume: float,
                         order_books: Dict[str, OrderBookData],
                         side: str) -> Dict[str, float]:
        """تقسیم سفارش بزرگ بین صرافی‌ها
        
        Parameters
        ----------
        total_volume : float
            کل حجم سفارش
        order_books : Dict[str, OrderBookData]
            order book های صرافی‌ها
        side : str
            طرف سفارش
            
        Returns
        -------
        Dict[str, float]
            تخصیص حجم به هر صرافی
        """
        allocation = {}
        remaining_volume = total_volume
        
        # محاسبه ظرفیت هر صرافی
        exchange_capacities = {}
        for exchange, order_book in order_books.items():
            if side == 'buy':
                levels = order_book.asks
            else:
                levels = order_book.bids
                
            # محاسبه عمق قابل استفاده
            available_depth = sum(volume for _, volume in levels[:5])  # 5 سطح اول
            max_volume = available_depth * self.max_order_size_ratio
            
            exchange_capacities[exchange] = max_volume
            
        # تخصیص متناسب با ظرفیت
        total_capacity = sum(exchange_capacities.values())
        
        if total_capacity == 0:
            return allocation
            
        for exchange, capacity in exchange_capacities.items():
            ratio = capacity / total_capacity
            allocated_volume = min(remaining_volume, total_volume * ratio)
            
            if allocated_volume > 0:
                allocation[exchange] = allocated_volume
                remaining_volume -= allocated_volume
                
        return allocation
        
    def calculate_market_impact(self,
                              order_book: OrderBookData,
                              volume: float,
                              side: str) -> float:
        """محاسبه تأثیر بازار
        
        Parameters
        ----------
        order_book : OrderBookData
            order book صرافی
        volume : float
            حجم سفارش
        side : str
            طرف سفارش
            
        Returns
        -------
        float
            درصد تأثیر بازار
        """
        if side == 'buy':
            levels = order_book.asks
        else:
            levels = order_book.bids
            
        if not levels:
            return 0.0
            
        best_price = levels[0][0]
        remaining_volume = volume
        
        for price, available_volume in levels:
            if remaining_volume <= available_volume:
                # سفارش کاملاً در این سطح اجرا می‌شود
                impact = abs(price - best_price) / best_price * 100
                return impact
                
            remaining_volume -= available_volume
            
        # اگر حجم بیش از کل عمق باشد
        if levels:
            last_price = levels[-1][0]
            impact = abs(last_price - best_price) / best_price * 100
            return impact
            
        return 0.0


class FailoverSystem:
    """سیستم failover هوشمند"""
    
    def __init__(self, health_check_interval: int = 30):
        """
        Parameters
        ----------
        health_check_interval : int
            فاصله زمانی بررسی سلامت (ثانیه)
        """
        self.health_check_interval = health_check_interval
        self.exchange_health: Dict[str, Dict[str, Any]] = {}
        self.backup_exchanges: Dict[str, List[str]] = {}
        self.is_monitoring = False
        
    def add_backup_exchanges(self, primary: str, backups: List[str]):
        """اضافه کردن صرافی‌های پشتیبان"""
        self.backup_exchanges[primary] = backups
        
    async def monitor_exchange_health(self, connectors: Dict[str, ExchangeConnector]):
        """مانیتورینگ سلامت صرافی‌ها"""
        self.is_monitoring = True
        
        while self.is_monitoring:
            for exchange, connector in connectors.items():
                try:
                    # بررسی اتصال
                    start_time = time.time()
                    order_book = await connector.get_order_book('BTCUSD')
                    response_time = (time.time() - start_time) * 1000  # ms
                    
                    if order_book:
                        self.exchange_health[exchange] = {
                            'status': ExchangeStatus.ACTIVE,
                            'last_check': time.time(),
                            'response_time': response_time,
                            'consecutive_failures': 0
                        }
                    else:
                        self._record_failure(exchange)
                        
                except Exception as e:
                    logger.error(f"Health check failed for {exchange}: {e}")
                    self._record_failure(exchange)
                    
            await asyncio.sleep(self.health_check_interval)
            
    def _record_failure(self, exchange: str):
        """ثبت خرابی صرافی"""
        if exchange not in self.exchange_health:
            self.exchange_health[exchange] = {
                'consecutive_failures': 0
            }
            
        self.exchange_health[exchange]['consecutive_failures'] += 1
        self.exchange_health[exchange]['last_check'] = time.time()
        
        # تغییر وضعیت بر اساس تعداد خرابی‌ها
        failures = self.exchange_health[exchange]['consecutive_failures']
        if failures >= 3:
            self.exchange_health[exchange]['status'] = ExchangeStatus.ERROR
        elif failures >= 1:
            self.exchange_health[exchange]['status'] = ExchangeStatus.INACTIVE
            
    def get_healthy_exchanges(self, exchanges: List[str]) -> List[str]:
        """دریافت صرافی‌های سالم"""
        healthy = []
        
        for exchange in exchanges:
            health = self.exchange_health.get(exchange, {})
            status = health.get('status', ExchangeStatus.ACTIVE)
            
            if status == ExchangeStatus.ACTIVE:
                healthy.append(exchange)
                
        return healthy
        
    def get_failover_exchange(self, failed_exchange: str) -> Optional[str]:
        """دریافت صرافی جایگزین"""
        backups = self.backup_exchanges.get(failed_exchange, [])
        healthy_backups = self.get_healthy_exchanges(backups)
        
        if healthy_backups:
            return healthy_backups[0]
            
        return None
        
    def stop_monitoring(self):
        """توقف مانیتورینگ"""
        self.is_monitoring = False


class MultiExchangeRouter:
    """مسیریاب اصلی چندصرافی"""
    
    def __init__(self, exchanges: List[ExchangeInfo]):
        """
        Parameters
        ----------
        exchanges : List[ExchangeInfo]
            لیست اطلاعات صرافی‌ها
        """
        self.exchanges = {ex.name: ex for ex in exchanges}
        self.connectors: Dict[str, ExchangeConnector] = {}
        self.arbitrage_engine = ArbitrageEngine()
        self.cost_optimizer = ExecutionCostOptimizer()
        self.liquidity_manager = LiquidityManager()
        self.failover_system = FailoverSystem()
        
        # آمار
        self.total_arbitrages = 0
        self.total_profit = 0.0
        self.executed_orders = 0
        
    async def initialize(self):
        """مقداردهی اولیه سیستم"""
        # ایجاد کانکتورها
        for exchange_name, exchange_info in self.exchanges.items():
            connector = ExchangeConnector(exchange_info)
            self.connectors[exchange_name] = connector
            
            # اتصال به صرافی
            success = await connector.connect()
            if not success:
                logger.warning(f"Failed to connect to {exchange_name}")
                
        # تنظیم failover
        exchange_names = list(self.exchanges.keys())
        for i, primary in enumerate(exchange_names):
            backups = exchange_names[:i] + exchange_names[i+1:]
            self.failover_system.add_backup_exchanges(primary, backups)
            
        # شروع مانیتورینگ
        asyncio.create_task(
            self.failover_system.monitor_exchange_health(self.connectors)
        )
        
        logger.info("Multi-exchange router initialized")
        
    async def execute_arbitrage(self, opportunity: ArbitrageOpportunity) -> bool:
        """اجرای آربیتراژ
        
        Parameters
        ----------
        opportunity : ArbitrageOpportunity
            فرصت آربیتراژ
            
        Returns
        -------
        bool
            موفقیت اجرا
        """
        try:
            # ایجاد سفارشات
            buy_order = ExecutionOrder(
                id=f"arb_buy_{int(time.time())}",
                exchange=opportunity.buy_exchange,
                symbol=opportunity.symbol,
                side='buy',
                order_type=OrderType.MARKET,
                volume=opportunity.volume
            )
            
            sell_order = ExecutionOrder(
                id=f"arb_sell_{int(time.time())}",
                exchange=opportunity.sell_exchange,
                symbol=opportunity.symbol,
                side='sell',
                order_type=OrderType.MARKET,
                volume=opportunity.volume
            )
            
            # اجرای همزمان سفارشات
            buy_connector = self.connectors[opportunity.buy_exchange]
            sell_connector = self.connectors[opportunity.sell_exchange]
            
            buy_task = buy_connector.place_order(buy_order)
            sell_task = sell_connector.place_order(sell_order)
            
            buy_success, sell_success = await asyncio.gather(buy_task, sell_task)
            
            if buy_success and sell_success:
                self.total_arbitrages += 1
                self.total_profit += opportunity.profit
                logger.info(f"Arbitrage executed: {opportunity.profit:.2f} profit")
                return True
            else:
                logger.error("Arbitrage execution failed")
                return False
                
        except Exception as e:
            logger.error(f"Arbitrage execution error: {e}")
            return False
            
    async def execute_order(self,
                          symbol: str,
                          side: str,
                          volume: float,
                          order_type: OrderType = OrderType.MARKET,
                          price: Optional[float] = None) -> List[ExecutionOrder]:
        """اجرای سفارش با بهینه‌سازی
        
        Parameters
        ----------
        symbol : str
            نماد معاملاتی
        side : str
            طرف سفارش
        volume : float
            حجم سفارش
        order_type : OrderType
            نوع سفارش
        price : Optional[float]
            قیمت (برای سفارش limit)
            
        Returns
        -------
        List[ExecutionOrder]
            لیست سفارشات اجرا شده
        """
        # دریافت صرافی‌های سالم
        healthy_exchanges = self.failover_system.get_healthy_exchanges(
            list(self.exchanges.keys())
        )
        
        if not healthy_exchanges:
            logger.error("No healthy exchanges available")
            return []
            
        # دریافت order books
        order_books = {}
        for exchange in healthy_exchanges:
            connector = self.connectors[exchange]
            order_book = await connector.get_order_book(symbol)
            if order_book:
                order_books[exchange] = order_book
                
        if not order_books:
            logger.error("No order books available")
            return []
            
        # تقسیم سفارش بزرگ
        allocation = self.liquidity_manager.split_large_order(
            volume, order_books, side
        )
        
        # اجرای سفارشات
        executed_orders = []
        tasks = []
        
        for exchange, allocated_volume in allocation.items():
            if allocated_volume <= 0:
                continue
                
            order = ExecutionOrder(
                id=f"order_{exchange}_{int(time.time())}",
                exchange=exchange,
                symbol=symbol,
                side=side,
                order_type=order_type,
                volume=allocated_volume,
                price=price
            )
            
            connector = self.connectors[exchange]
            task = connector.place_order(order)
            tasks.append((order, task))
            
        # اجرای همزمان
        for order, task in tasks:
            try:
                success = await task
                if success:
                    executed_orders.append(order)
                    self.executed_orders += 1
            except Exception as e:
                logger.error(f"Order execution failed: {e}")
                
        return executed_orders
        
    async def scan_arbitrage_opportunities(self, symbols: List[str]) -> List[ArbitrageOpportunity]:
        """اسکن فرصت‌های آربیتراژ
        
        Parameters
        ----------
        symbols : List[str]
            لیست نمادهای معاملاتی
            
        Returns
        -------
        List[ArbitrageOpportunity]
            لیست فرصت‌های آربیتراژ
        """
        all_opportunities = []
        
        for symbol in symbols:
            # دریافت order books
            order_books = []
            for exchange, connector in self.connectors.items():
                order_book = await connector.get_order_book(symbol)
                if order_book:
                    order_books.append(order_book)
                    
            # یافتن فرصت‌های آربیتراژ
            opportunities = self.arbitrage_engine.find_arbitrage_opportunities(
                order_books, symbol
            )
            
            all_opportunities.extend(opportunities)
            
        # رتبه‌بندی فرصت‌ها
        ranked_opportunities = self.arbitrage_engine.rank_opportunities(all_opportunities)
        
        return ranked_opportunities
        
    def get_aggregated_order_book(self, symbol: str) -> Optional[OrderBookData]:
        """تولید order book تجمیعی
        
        Parameters
        ----------
        symbol : str
            نماد معاملاتی
            
        Returns
        -------
        Optional[OrderBookData]
            order book تجمیعی
        """
        all_bids = []
        all_asks = []
        
        # جمع‌آوری تمام bids و asks
        for exchange, connector in self.connectors.items():
            if exchange in connector.order_books:
                order_book = connector.order_books[exchange]
                
                for price, volume in order_book.bids:
                    all_bids.append((price, volume))
                    
                for price, volume in order_book.asks:
                    all_asks.append((price, volume))
                    
        if not all_bids or not all_asks:
            return None
            
        # مرتب‌سازی و تجمیع
        all_bids.sort(key=lambda x: x[0], reverse=True)  # نزولی
        all_asks.sort(key=lambda x: x[0])  # صعودی
        
        # تجمیع حجم‌ها در قیمت‌های مشابه
        aggregated_bids = self._aggregate_levels(all_bids)
        aggregated_asks = self._aggregate_levels(all_asks)
        
        return OrderBookData(
            exchange="aggregated",
            symbol=symbol,
            bids=aggregated_bids[:20],  # 20 سطح اول
            asks=aggregated_asks[:20],
            timestamp=time.time()
        )
        
    def _aggregate_levels(self, levels: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """تجمیع سطوح قیمت مشابه"""
        aggregated = {}
        
        for price, volume in levels:
            # گرد کردن قیمت برای تجمیع
            rounded_price = round(price, 2)
            
            if rounded_price in aggregated:
                aggregated[rounded_price] += volume
            else:
                aggregated[rounded_price] = volume
                
        return list(aggregated.items())
        
    def get_statistics(self) -> Dict[str, Any]:
        """دریافت آمار سیستم"""
        return {
            'total_exchanges': len(self.exchanges),
            'connected_exchanges': len([c for c in self.connectors.values() if c.connected]),
            'total_arbitrages': self.total_arbitrages,
            'total_profit': self.total_profit,
            'executed_orders': self.executed_orders,
            'exchange_health': self.failover_system.exchange_health
        }
        
    async def shutdown(self):
        """خاموش کردن سیستم"""
        # توقف مانیتورینگ
        self.failover_system.stop_monitoring()
        
        # قطع اتصال صرافی‌ها
        for connector in self.connectors.values():
            await connector.disconnect()
            
        logger.info("Multi-exchange router shutdown complete") 