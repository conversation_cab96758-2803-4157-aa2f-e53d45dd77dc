import yaml
import os


def get_config(config_path: str = "config.yaml") -> dict:
    """
    بارگذاری تنظیمات پروژه از فایل YAML یا بازگرداندن مقدار پیش‌فرض.

    Args:
        config_path (str): مسیر فایل تنظیمات YAML
    Returns:
        dict: دیکشنری تنظیمات
    """
    if os.path.exists(config_path):
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)
    # تنظیمات پیش‌فرض
    return {
        "lot_size": 0.1,
        "stop_loss": 10,
        "take_profit": 20,
        "indicators": {
            "rsi": {"period": 14},
            "ma": {"period": 20},
            "stochastic": {"period": 14},
        },
    }
