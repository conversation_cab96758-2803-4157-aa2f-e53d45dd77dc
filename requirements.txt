# Trading System v2.0 Requirements
# ═══════════════════════════════════════════════════════════
# تمام وابستگی‌های مورد نیاز سیستم معاملاتی نسخه 2.0

# Core Python packages
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0
scikit-learn>=1.3.0

# Machine Learning & AI
torch>=2.0.0
transformers>=4.30.0
huggingface_hub>=0.16.0
sentence-transformers>=2.2.0
tokenizers>=0.13.0
accelerate>=0.20.0

# Deep Learning (Optional - for advanced features)
tensorflow>=2.12.0
keras>=2.12.0

# Data Processing
pandas-ta>=0.3.14b
yfinance>=0.2.18
python-dateutil>=2.8.2
pytz>=2023.3

# Database
sqlite3  # Built-in Python
sqlalchemy>=2.0.0
alembic>=1.11.0

# Async & Networking
asyncio  # Built-in Python
aiohttp>=3.8.0
websockets>=11.0.0
requests>=2.31.0
urllib3>=2.0.0

# Configuration & Logging
PyYAML>=6.0
configparser>=5.3.0
python-json-logger>=2.0.7
colorlog>=6.7.0
rich>=13.4.0

# System Monitoring
psutil>=5.9.0
GPUtil>=1.4.0  # For GPU monitoring
py-cpuinfo>=9.0.0

# File Processing
openpyxl>=3.1.0
xlsxwriter>=3.1.0
python-dotenv>=1.0.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
pytest-cov>=4.1.0
mock>=5.1.0

# Code Quality
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0
mypy>=1.4.0

# Security
cryptography>=41.0.0
bcrypt>=4.0.0

# Web Framework (for API)
fastapi>=0.100.0
uvicorn>=0.23.0
pydantic>=2.0.0
starlette>=0.27.0

# Visualization (Optional)
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
bokeh>=3.2.0

# Time Series Analysis
statsmodels>=0.14.0
pmdarima>=2.0.0
prophet>=1.1.0  # Facebook Prophet

# Technical Analysis
TA-Lib>=0.4.25  # Technical Analysis Library
talib>=0.4.25

# Optimization
scipy>=1.10.0
cvxpy>=1.3.0
optuna>=3.2.0

# Utilities
tqdm>=4.65.0
click>=8.1.0
tabulate>=0.9.0
humanize>=4.7.0
pathlib2>=2.3.7

# Caching
redis>=4.6.0
diskcache>=5.6.0

# Serialization
pickle5>=0.0.12
joblib>=1.3.0
cloudpickle>=2.2.0

# Development Tools
jupyter>=1.0.0
ipython>=8.14.0
notebook>=6.5.0

# Environment & Deployment
docker>=6.1.0
kubernetes>=27.2.0
gunicorn>=21.0.0

# Monitoring & Logging
prometheus_client>=0.17.0
grafana-api>=1.0.3
elasticsearch>=8.8.0
loguru>=0.7.0

# Financial Data
alpha_vantage>=2.3.1
ccxt>=4.0.0  # Cryptocurrency exchange trading library
python-binance>=1.0.16

# Natural Language Processing
nltk>=3.8.0
spacy>=3.6.0
textblob>=0.17.1

# Image Processing (for multimodal models)
Pillow>=10.0.0
opencv-python>=4.8.0

# Audio Processing (for future features)
librosa>=0.10.0
soundfile>=0.12.1

# PDF Processing
PyPDF2>=3.0.0
pdfplumber>=0.9.0

# Excel Processing
openpyxl>=3.1.0
xlrd>=2.0.1

# HTTP Client
httpx>=0.24.0
requests-oauthlib>=1.3.1

# Timezone handling
pytz>=2023.3
tzdata>=2023.3

# Memory profiling
memory-profiler>=0.60.0
pympler>=0.9

# Process monitoring
supervisor>=4.2.5

# Configuration management
dynaconf>=3.2.0
python-decouple>=3.8

# Event handling
APScheduler>=3.10.0
celery>=5.3.0

# Message queues
kombu>=5.3.0
redis>=4.6.0

# Backup & Recovery
boto3>=1.28.0  # AWS SDK
google-cloud-storage>=2.10.0  # GCP
azure-storage-blob>=12.17.0  # Azure

# Notification
twilio>=8.5.0
slack-sdk>=3.21.0
discord.py>=2.3.0

# Multi-processing
multiprocessing-logging>=0.3.4
pathos>=0.3.0

# Mathematical Libraries
sympy>=1.12
mpmath>=1.3.0

# Graph processing
networkx>=3.1
python-igraph>=0.10.0

# Distributed computing
dask>=2023.7.0
ray>=2.5.0

# GPU acceleration (optional)
cupy-cuda11x>=12.0.0  # For CUDA 11.x
cupy-cuda12x>=12.0.0  # For CUDA 12.x

# Debugging & Profiling
pdb++>=0.10.3
line_profiler>=4.1.0
py-spy>=0.3.14

# Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
mkdocs>=1.5.0

# Version control
GitPython>=3.1.32

# Benchmarking
benchmark-runner>=0.5.0
perfplot>=0.10.0

# Extra utilities
more-itertools>=10.0.0
python-magic>=0.4.27
watchdog>=3.0.0

# Cross-platform support
psutil>=5.9.0
plyer>=2.1.0

# Backup dependencies (in case of import issues)
importlib-metadata>=6.8.0
packaging>=23.1
wheel>=0.41.0
setuptools>=68.0.0

# Persian text processing
hazm>=0.7.0
persian>=0.3.0

# Error tracking
sentry-sdk>=1.29.0
bugsnag>=4.6.0

# Health checks
healthcheck>=1.3.3

# Command line interface
typer>=0.9.0
rich-click>=1.6.0

# Data validation
cerberus>=1.3.4
marshmallow>=3.20.0

# Real-time data streaming
kafka-python>=2.0.2
confluent-kafka>=2.2.0

# Blockchain integration (for crypto features)
web3>=6.8.0
eth-account>=0.9.0

# API rate limiting
ratelimit>=2.2.1
slowapi>=0.1.9

# Background tasks
rq>=1.15.0
dramatiq>=1.13.0

# Metrics collection
statsd>=4.0.1
datadog>=0.47.0

# Email notifications
sendgrid>=6.10.0
mailgun3>=1.0.9

# SMS notifications
nexmo>=2.5.2
vonage>=3.8.0

# Push notifications
pyfcm>=1.5.4

# File watching
watchfiles>=0.19.0

# Compression
lz4>=4.3.0
zstandard>=0.21.0

# Encryption
cryptography>=41.0.0
pycryptodome>=3.18.0

# Performance monitoring
py-spy>=0.3.14
scalene>=1.5.0

# Memory optimization
pymalloc>=1.0.0

# Jupyter extensions
jupyterlab>=4.0.0
ipywidgets>=8.0.0

# Interactive plotting
plotly-dash>=2.13.0
streamlit>=1.25.0

# Development server
uvicorn[standard]>=0.23.0

# Load testing
locust>=2.16.0

# API documentation
redoc>=2.1.0
swagger-ui-bundle>=0.0.9 