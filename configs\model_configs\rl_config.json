{"models": ["ppo", "a2c", "dqn"], "timesteps": 10000, "learning_rate": 0.0003, "batch_size": 64, "gamma": 0.99, "save_path": "models/trained_models/", "validation_split": 0.2, "test_split": 0.1, "environment": {"initial_balance": 10000, "transaction_fee": 0.001, "max_position_size": 0.5}, "training": {"episodes_per_model": 100, "evaluation_episodes": 10, "early_stopping_patience": 5, "save_best_only": true}, "hyperparameters": {"ppo": {"learning_rate": 0.0003, "n_steps": 2048, "batch_size": 64, "n_epochs": 10, "gamma": 0.99, "gae_lambda": 0.95, "clip_range": 0.2, "ent_coef": 0.01}, "a2c": {"learning_rate": 0.0007, "n_steps": 5, "gamma": 0.99, "gae_lambda": 1.0, "ent_coef": 0.01, "vf_coef": 0.5, "max_grad_norm": 0.5}, "dqn": {"learning_rate": 0.0001, "buffer_size": 1000000, "learning_starts": 50000, "batch_size": 32, "gamma": 0.99, "target_update_interval": 1000, "exploration_fraction": 0.1, "exploration_initial_eps": 1.0, "exploration_final_eps": 0.05}}}