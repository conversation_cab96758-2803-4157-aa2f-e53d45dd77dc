import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from collections import deque
import logging
import json
import os
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MarginRiskModel(nn.Module):
    """
    Deep learning model for predicting optimal margin requirements
    """
    def __init__(self, input_dim, hidden_dim=64, output_dim=1):
        super(MarginRiskModel, self).__init__()
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.fc3 = nn.Linear(hidden_dim // 2, output_dim)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        # Output is margin multiplier (1.0 = standard margin)
        return torch.sigmoid(self.fc3(x)) * 3.0 + 0.5  # Range: 0.5 to 3.5

class AdaptiveMarginControl:
    """
    Adaptive margin control system that dynamically adjusts margin requirements
    based on market conditions, volatility, and position risk.
    """
    def __init__(self, 
                 base_margin_rates=None,
                 volatility_window=20,
                 max_margin_multiplier=3.0,
                 min_margin_multiplier=0.5,
                 learning_rate=0.001,
                 use_ml_model=True,
                 config_path=None):
        """
        Initialize the adaptive margin control system.
        
        Args:
            base_margin_rates (dict): Base margin rates by symbol (e.g. {'EURUSD': 0.02})
            volatility_window (int): Window size for volatility calculation
            max_margin_multiplier (float): Maximum margin multiplier
            min_margin_multiplier (float): Minimum margin multiplier
            learning_rate (float): Learning rate for ML model
            use_ml_model (bool): Whether to use ML model for margin prediction
            config_path (str): Path to configuration file
        """
        # Default base margin rates if not provided
        self.base_margin_rates = base_margin_rates or {
            'EURUSD': 0.02,  # 2% margin requirement
            'GBPUSD': 0.03,  # 3% margin requirement
            'USDJPY': 0.025, # 2.5% margin requirement
            'BTCUSD': 0.50,  # 50% margin requirement
            'XAUUSD': 0.05   # 5% margin requirement
        }
        
        self.volatility_window = volatility_window
        self.max_margin_multiplier = max_margin_multiplier
        self.min_margin_multiplier = min_margin_multiplier
        self.learning_rate = learning_rate
        self.use_ml_model = use_ml_model
        
        # Price history for volatility calculation
        self.price_history = {}
        
        # Position history for risk calculation
        self.position_history = {}
        
        # Margin adjustment history
        self.margin_history = {}
        
        # Load configuration if provided
        if config_path and os.path.exists(config_path):
            self.load_config(config_path)
        
        # Initialize ML model if enabled
        if self.use_ml_model:
            self._init_ml_model()
    
    def _init_ml_model(self):
        """
        Initialize the machine learning model for margin prediction.
        """
        # Feature dimensions: 
        # [volatility, price_change, position_size, market_sentiment, time_features(5), symbol_features(10)]
        self.input_dim = 18
        self.model = MarginRiskModel(input_dim=self.input_dim)
        self.optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
        self.criterion = nn.MSELoss()
        
        # Experience replay buffer
        self.memory = deque(maxlen=10000)
        
        # Target network for stable learning
        self.target_model = MarginRiskModel(input_dim=self.input_dim)
        self.target_model.load_state_dict(self.model.state_dict())
        
        # Training parameters
        self.batch_size = 64
        self.update_target_every = 100
        self.step_count = 0
    
    def update_price_history(self, symbol, price):
        """
        Update price history for a symbol.
        
        Args:
            symbol (str): Trading symbol
            price (float): Current price
        """
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        
        self.price_history[symbol].append(price)
        
        # Keep only the last window_size prices
        if len(self.price_history[symbol]) > self.volatility_window * 5:  # Keep more for better stats
            self.price_history[symbol] = self.price_history[symbol][-self.volatility_window * 5:]
    
    def update_position(self, symbol, position_size, leverage=1.0):
        """
        Update position information for a symbol.
        
        Args:
            symbol (str): Trading symbol
            position_size (float): Current position size
            leverage (float): Current leverage
        """
        if symbol not in self.position_history:
            self.position_history[symbol] = []
        
        self.position_history[symbol].append({
            'size': position_size,
            'leverage': leverage,
            'timestamp': datetime.now().timestamp()
        })
        
        # Keep only recent position history
        if len(self.position_history[symbol]) > 100:
            self.position_history[symbol] = self.position_history[symbol][-100:]
    
    def calculate_volatility(self, symbol):
        """
        Calculate price volatility for a symbol.
        
        Args:
            symbol (str): Trading symbol
            
        Returns:
            float: Volatility measure
        """
        if symbol not in self.price_history or len(self.price_history[symbol]) < self.volatility_window:
            return 0.0
        
        # Use recent prices for volatility calculation
        recent_prices = self.price_history[symbol][-self.volatility_window:]
        
        # Calculate returns
        returns = np.diff(recent_prices) / recent_prices[:-1]
        
        # Calculate volatility (standard deviation of returns)
        volatility = np.std(returns)
        
        return volatility
    
    def calculate_position_risk(self, symbol):
        """
        Calculate position risk based on position history.
        
        Args:
            symbol (str): Trading symbol
            
        Returns:
            float: Position risk score (0-1)
        """
        if symbol not in self.position_history or not self.position_history[symbol]:
            return 0.0
        
        # Get the latest position
        latest_position = self.position_history[symbol][-1]
        position_size = abs(latest_position['size'])
        leverage = latest_position['leverage']
        
        # Calculate risk based on position size and leverage
        # Higher leverage and larger position size increase risk
        risk_score = min(1.0, (position_size * leverage) / 1000)  # Normalize to 0-1
        
        return risk_score
    
    def get_market_features(self, symbol, market_data=None):
        """
        Extract market features for margin calculation.
        
        Args:
            symbol (str): Trading symbol
            market_data (dict): Additional market data
            
        Returns:
            dict: Market features
        """
        features = {
            'volatility': self.calculate_volatility(symbol),
            'position_risk': self.calculate_position_risk(symbol),
            'market_sentiment': 0.0,  # Default neutral
            'time_features': self._get_time_features(),
        }
        
        # Add market sentiment if available
        if market_data and 'sentiment' in market_data:
            features['market_sentiment'] = market_data['sentiment']
        
        return features
    
    def _get_time_features(self):
        """
        Extract time-based features (hour of day, day of week, etc.)
        
        Returns:
            list: Time features
        """
        now = datetime.now()
        
        # Normalize time features
        hour = now.hour / 24.0
        day_of_week = now.weekday() / 6.0
        day_of_month = (now.day - 1) / 30.0
        month = (now.month - 1) / 11.0
        is_weekend = 1.0 if now.weekday() >= 5 else 0.0
        
        return [hour, day_of_week, day_of_month, month, is_weekend]
    
    def _get_symbol_features(self, symbol):
        """
        Convert symbol to one-hot encoding.
        
        Args:
            symbol (str): Trading symbol
            
        Returns:
            list: One-hot encoded symbol features
        """
        # List of supported symbols
        symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'BTCUSD', 'XAUUSD', 
                   'AUDUSD', 'USDCAD', 'NZDUSD', 'USDCHF', 'EURJPY']
        
        # Create one-hot encoding
        features = [0] * len(symbols)
        if symbol in symbols:
            features[symbols.index(symbol)] = 1
        
        return features
    
    def _prepare_model_input(self, symbol, market_data=None):
        """
        Prepare input features for the ML model.
        
        Args:
            symbol (str): Trading symbol
            market_data (dict): Additional market data
            
        Returns:
            torch.Tensor: Model input tensor
        """
        features = self.get_market_features(symbol, market_data)
        
        # Extract individual features
        volatility = features['volatility']
        position_risk = features['position_risk']
        market_sentiment = features['market_sentiment']
        time_features = features['time_features']
        
        # Calculate price change if possible
        price_change = 0.0
        if symbol in self.price_history and len(self.price_history[symbol]) >= 2:
            recent_prices = self.price_history[symbol][-2:]
            price_change = (recent_prices[1] - recent_prices[0]) / recent_prices[0]
        
        # Get symbol features
        symbol_features = self._get_symbol_features(symbol)
        
        # Combine all features
        combined_features = [volatility, price_change, position_risk, market_sentiment] + \
                           time_features + symbol_features
        
        # Convert to tensor
        return torch.FloatTensor(combined_features).unsqueeze(0)
    
    def calculate_margin_requirement(self, symbol, price=None, position_size=0.0, 
                                     leverage=1.0, market_data=None):
        """
        Calculate adaptive margin requirement based on market conditions.
        
        Args:
            symbol (str): Trading symbol
            price (float): Current price (optional)
            position_size (float): Position size (optional)
            leverage (float): Leverage (optional)
            market_data (dict): Additional market data (optional)
            
        Returns:
            dict: Margin requirement details
        """
        # Update histories if price is provided
        if price is not None:
            self.update_price_history(symbol, price)
        
        # Update position if provided
        if position_size != 0.0:
            self.update_position(symbol, position_size, leverage)
        
        # Get base margin rate for the symbol
        base_margin = self.base_margin_rates.get(symbol, 0.05)  # Default 5% if symbol not found
        
        # Calculate margin multiplier
        if self.use_ml_model and len(self.price_history.get(symbol, [])) >= self.volatility_window:
            # Use ML model for prediction
            model_input = self._prepare_model_input(symbol, market_data)
            
            with torch.no_grad():
                margin_multiplier = self.model(model_input).item()
        else:
            # Use rule-based approach
            volatility = self.calculate_volatility(symbol)
            position_risk = self.calculate_position_risk(symbol)
            
            # Base multiplier from volatility (higher volatility = higher margin)
            volatility_factor = min(2.0, 1.0 + volatility * 20.0)
            
            # Adjust for position risk
            risk_factor = 1.0 + position_risk
            
            # Combine factors
            margin_multiplier = volatility_factor * risk_factor
        
        # Ensure multiplier is within bounds
        margin_multiplier = max(self.min_margin_multiplier, 
                               min(self.max_margin_multiplier, margin_multiplier))
        
        # Calculate final margin requirement
        margin_requirement = base_margin * margin_multiplier
        
        # Store in history
        if symbol not in self.margin_history:
            self.margin_history[symbol] = []
        
        self.margin_history[symbol].append({
            'timestamp': datetime.now().timestamp(),
            'base_margin': base_margin,
            'multiplier': margin_multiplier,
            'requirement': margin_requirement
        })
        
        # Keep history manageable
        if len(self.margin_history[symbol]) > 1000:
            self.margin_history[symbol] = self.margin_history[symbol][-1000:]
        
        return {
            'symbol': symbol,
            'base_margin_rate': base_margin,
            'margin_multiplier': margin_multiplier,
            'margin_requirement': margin_requirement,
            'timestamp': datetime.now().isoformat()
        }
    
    def store_experience(self, symbol, margin_multiplier, actual_risk, next_volatility):
        """
        Store experience for model training.
        
        Args:
            symbol (str): Trading symbol
            margin_multiplier (float): Applied margin multiplier
            actual_risk (float): Actual realized risk
            next_volatility (float): Next period volatility
        """
        if not self.use_ml_model:
            return
        
        # Create state representation
        state = self._prepare_model_input(symbol).squeeze(0).numpy()
        
        # Calculate target margin based on actual risk and next volatility
        # Higher actual risk should have led to higher margin
        target_margin = margin_multiplier * (1.0 + (actual_risk - margin_multiplier * 0.1))
        
        # Adjust for next period volatility
        target_margin *= (1.0 + (next_volatility - self.calculate_volatility(symbol)))
        
        # Ensure target is within bounds
        target_margin = max(self.min_margin_multiplier, 
                           min(self.max_margin_multiplier, target_margin))
        
        # Store experience
        self.memory.append((state, target_margin))
    
    def train_model(self, epochs=1):
        """
        Train the ML model using stored experiences.
        
        Args:
            epochs (int): Number of training epochs
            
        Returns:
            float: Training loss
        """
        if not self.use_ml_model or len(self.memory) < self.batch_size:
            return 0.0
        
        total_loss = 0.0
        
        for _ in range(epochs):
            # Sample batch from memory
            indices = np.random.choice(len(self.memory), self.batch_size, replace=False)
            batch = [self.memory[i] for i in indices]
            
            # Separate states and targets
            states = torch.FloatTensor(np.array([exp[0] for exp in batch]))
            targets = torch.FloatTensor(np.array([exp[1] for exp in batch])).unsqueeze(1)
            
            # Forward pass
            predictions = self.model(states)
            
            # Calculate loss
            loss = self.criterion(predictions, targets)
            total_loss += loss.item()
            
            # Backward pass and optimize
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            # Update target network if needed
            self.step_count += 1
            if self.step_count % self.update_target_every == 0:
                self.target_model.load_state_dict(self.model.state_dict())
        
        return total_loss / epochs
    
    def get_margin_history(self, symbol, limit=100):
        """
        Get margin history for a symbol.
        
        Args:
            symbol (str): Trading symbol
            limit (int): Maximum number of records to return
            
        Returns:
            list: Margin history
        """
        if symbol not in self.margin_history:
            return []
        
        return self.margin_history[symbol][-limit:]
    
    def get_margin_stats(self, symbol):
        """
        Get statistics about margin history for a symbol.
        
        Args:
            symbol (str): Trading symbol
            
        Returns:
            dict: Margin statistics
        """
        if symbol not in self.margin_history or not self.margin_history[symbol]:
            return {
                'mean': 0.0,
                'max': 0.0,
                'min': 0.0,
                'current': 0.0,
                'volatility': 0.0
            }
        
        history = self.margin_history[symbol]
        requirements = [entry['requirement'] for entry in history]
        
        return {
            'mean': np.mean(requirements),
            'max': np.max(requirements),
            'min': np.min(requirements),
            'current': requirements[-1],
            'volatility': np.std(requirements)
        }
    
    def explain_margin_decision(self, symbol, market_data=None):
        """
        Provide detailed explanation for margin requirement decisions.
        
        Args:
            symbol (str): Trading symbol
            market_data (dict): Additional market data
            
        Returns:
            dict: Detailed explanation of margin decision
        """
        # Get current margin requirement
        base_margin = self.base_margin_rates.get(symbol, 0.02)
        
        # Get market features
        features = self.get_market_features(symbol, market_data)
        volatility = features['volatility']
        position_risk = features['position_risk']
        market_sentiment = features['market_sentiment']
        
        # Calculate margin multiplier
        if self.use_ml_model:
            model_input = self._prepare_model_input(symbol, market_data)
            model_input_tensor = torch.FloatTensor(model_input)
            with torch.no_grad():
                margin_multiplier = self.model(model_input_tensor).item()
        else:
            # Calculate margin multiplier based on volatility and position risk
            volatility_factor = min(3.0, 1.0 + volatility * 20)  # Scale volatility impact
            risk_factor = 1.0 + position_risk * 2.0  # Scale position risk impact
            sentiment_factor = 1.0 - market_sentiment * 0.2  # Negative sentiment increases margin
            
            # Combine factors with weights
            margin_multiplier = (volatility_factor * 0.6 + risk_factor * 0.3 + sentiment_factor * 0.1)
            margin_multiplier = max(self.min_margin_multiplier, min(self.max_margin_multiplier, margin_multiplier))
        
        # Calculate actual margin requirement
        margin_requirement = base_margin * margin_multiplier
        
        # Determine primary factors influencing the decision
        factors = []
        
        # Volatility factor
        if volatility > 0.02:
            factors.append({
                "factor": "high_volatility",
                "importance": 0.8,
                "impact": "increase",
                "description": f"High market volatility ({volatility:.4f}) increases risk"
            })
        elif volatility < 0.005:
            factors.append({
                "factor": "low_volatility",
                "importance": 0.4,
                "impact": "decrease",
                "description": f"Low market volatility ({volatility:.4f}) reduces risk"
            })
        
        # Position risk factor
        if position_risk > 0.7:
            factors.append({
                "factor": "high_position_risk",
                "importance": 0.9,
                "impact": "increase",
                "description": f"High position risk ({position_risk:.2f}) due to large position size or leverage"
            })
        elif position_risk > 0.3:
            factors.append({
                "factor": "moderate_position_risk",
                "importance": 0.6,
                "impact": "increase",
                "description": f"Moderate position risk ({position_risk:.2f})"
            })
        
        # Market sentiment factor
        if market_sentiment < -0.5:
            factors.append({
                "factor": "negative_sentiment",
                "importance": 0.5,
                "impact": "increase",
                "description": f"Negative market sentiment ({market_sentiment:.2f}) increases risk"
            })
        elif market_sentiment > 0.5:
            factors.append({
                "factor": "positive_sentiment",
                "importance": 0.3,
                "impact": "decrease",
                "description": f"Positive market sentiment ({market_sentiment:.2f}) reduces risk"
            })
        
        # Time-based factors
        time_features = features['time_features']
        if time_features[4] > 0.5:  # Weekend
            factors.append({
                "factor": "weekend_trading",
                "importance": 0.7,
                "impact": "increase",
                "description": "Weekend trading has higher risk due to lower liquidity"
            })
        
        # Sort factors by importance
        factors.sort(key=lambda x: x["importance"], reverse=True)
        
        # Generate risk assessment
        if margin_multiplier > 2.5:
            risk_assessment = "Very high risk conditions"
            recommendation = "Consider reducing position size or closing positions"
        elif margin_multiplier > 1.7:
            risk_assessment = "High risk conditions"
            recommendation = "Monitor positions closely"
        elif margin_multiplier > 1.2:
            risk_assessment = "Moderate risk conditions"
            recommendation = "Standard risk management procedures"
        elif margin_multiplier > 0.8:
            risk_assessment = "Normal market conditions"
            recommendation = "Standard trading operations"
        else:
            risk_assessment = "Low risk conditions"
            recommendation = "Potential opportunity for increased position sizing"
        
        # Get historical context
        margin_history = self.get_margin_history(symbol, limit=10)
        if margin_history and len(margin_history) > 1:
            prev_margin = margin_history[-2]['margin_requirement']
            change = margin_requirement - prev_margin
            if abs(change) > 0.001:
                change_pct = (change / prev_margin) * 100
                change_description = f"{change_pct:.1f}% {'increase' if change > 0 else 'decrease'} from previous requirement"
            else:
                change_description = "No significant change from previous requirement"
        else:
            change_description = "No historical data available for comparison"
        
        # Create detailed explanation
        explanation = {
            "symbol": symbol,
            "base_margin_rate": base_margin,
            "margin_multiplier": margin_multiplier,
            "margin_requirement": margin_requirement,
            "margin_requirement_pct": margin_requirement * 100,
            "risk_assessment": risk_assessment,
            "primary_factors": factors,
            "historical_context": {
                "change_description": change_description,
                "recent_trend": self._get_margin_trend(symbol)
            },
            "model_confidence": 0.85 if self.use_ml_model else 0.7,
            "recommendation": recommendation,
            "timestamp": datetime.now().timestamp()
        }
        
        return explanation
    
    def _get_margin_trend(self, symbol):
        """
        Analyze recent margin requirement trend.
        
        Args:
            symbol (str): Trading symbol
            
        Returns:
            str: Description of recent trend
        """
        history = self.get_margin_history(symbol, limit=20)
        
        if not history or len(history) < 5:
            return "Insufficient historical data"
        
        # Extract margin requirements
        margins = [item['margin_requirement'] for item in history]
        
        # Calculate trend
        if len(margins) >= 10:
            recent_avg = sum(margins[-5:]) / 5
            older_avg = sum(margins[-10:-5]) / 5
            
            if recent_avg > older_avg * 1.1:
                return "Strongly increasing margin requirements"
            elif recent_avg > older_avg * 1.02:
                return "Gradually increasing margin requirements"
            elif recent_avg < older_avg * 0.9:
                return "Strongly decreasing margin requirements"
            elif recent_avg < older_avg * 0.98:
                return "Gradually decreasing margin requirements"
            else:
                return "Stable margin requirements"
        else:
            # Simple trend analysis for limited data
            if margins[-1] > margins[0] * 1.1:
                return "Increasing margin requirements"
            elif margins[-1] < margins[0] * 0.9:
                return "Decreasing margin requirements"
            else:
                return "Stable margin requirements"
    
    def save_config(self, path):
        """
        Save configuration to file.
        
        Args:
            path (str): File path
            
        Returns:
            bool: Success flag
        """
        try:
            config = {
                'base_margin_rates': self.base_margin_rates,
                'volatility_window': self.volatility_window,
                'max_margin_multiplier': self.max_margin_multiplier,
                'min_margin_multiplier': self.min_margin_multiplier,
                'use_ml_model': self.use_ml_model
            }
            
            with open(path, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info(f"Configuration saved to {path}")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            return False
    
    def load_config(self, path):
        """
        Load configuration from file.
        
        Args:
            path (str): File path
            
        Returns:
            bool: Success flag
        """
        try:
            with open(path, 'r') as f:
                config = json.load(f)
            
            self.base_margin_rates = config.get('base_margin_rates', self.base_margin_rates)
            self.volatility_window = config.get('volatility_window', self.volatility_window)
            self.max_margin_multiplier = config.get('max_margin_multiplier', self.max_margin_multiplier)
            self.min_margin_multiplier = config.get('min_margin_multiplier', self.min_margin_multiplier)
            self.use_ml_model = config.get('use_ml_model', self.use_ml_model)
            
            logger.info(f"Configuration loaded from {path}")
            return True
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return False
    
    def save_model(self, path):
        """
        Save the ML model to file.
        
        Args:
            path (str): File path
            
        Returns:
            bool: Success flag
        """
        if not self.use_ml_model:
            logger.warning("ML model is not enabled")
            return False
        
        try:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'target_model_state_dict': self.target_model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'step_count': self.step_count
            }, path)
            
            logger.info(f"Model saved to {path}")
            return True
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            return False
    
    def load_model(self, path):
        """
        Load the ML model from file.
        
        Args:
            path (str): File path
            
        Returns:
            bool: Success flag
        """
        if not self.use_ml_model:
            logger.warning("ML model is not enabled")
            return False
        
        try:
            checkpoint = torch.load(path)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.target_model.load_state_dict(checkpoint['target_model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.step_count = checkpoint['step_count']
            
            logger.info(f"Model loaded from {path}")
            return True
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False 