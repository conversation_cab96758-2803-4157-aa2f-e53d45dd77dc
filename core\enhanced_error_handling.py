"""
Enhanced Error Handling Module
سیستم مدیریت خطای پیشرفته با circuit breaker، retry logic، و logging

Features:
- Circuit Breaker Pattern
- Retry Logic with Exponential Backoff
- Advanced Error Classification
- Custom Error Types
- Error Reporting and Analytics
- Context-Aware Error Handling
"""

import logging
import time
import asyncio
import functools
import threading
from datetime import datetime, timedelta
from typing import (
    Any, Dict, List, Optional, Callable, Union, 
    Type, Tuple, Set, ClassVar
)
from dataclasses import dataclass, field
from enum import Enum, auto
from collections import defaultdict, deque
import json
import traceback
import sys
from contextlib import contextmanager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Custom Exception Classes
class TradingSystemError(Exception):
    """Base exception for trading system errors"""
    
    def __init__(self, message: str, error_code: str = None, context: Dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.context = context or {}
        self.timestamp = datetime.now()
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            "message": self.message,
            "error_code": self.error_code,
            "context": self.context,
            "timestamp": self.timestamp.isoformat(),
            "type": self.__class__.__name__
        }

class ConnectionError(TradingSystemError):
    """Connection-related errors"""
    pass

class DataError(TradingSystemError):
    """Data-related errors"""
    pass

class OrderError(TradingSystemError):
    """Order-related errors"""
    pass

class AuthenticationError(TradingSystemError):
    """Authentication errors"""
    pass

class RateLimitError(TradingSystemError):
    """Rate limiting errors"""
    pass

class ValidationError(TradingSystemError):
    """Validation errors"""
    pass

class ConfigurationError(TradingSystemError):
    """Configuration errors"""
    pass

class TimeoutError(TradingSystemError):
    """Timeout errors"""
    pass

# Error Severity Levels
class ErrorSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

# Error Categories
class ErrorCategory(Enum):
    NETWORK = "network"
    DATA = "data"
    AUTHENTICATION = "authentication"
    VALIDATION = "validation"
    BUSINESS_LOGIC = "business_logic"
    SYSTEM = "system"
    EXTERNAL_API = "external_api"

# Circuit Breaker States
class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

# Retry Strategies
class RetryStrategy(Enum):
    FIXED = "fixed"
    EXPONENTIAL = "exponential"
    LINEAR = "linear"
    FIBONACCI = "fibonacci"

@dataclass
class ErrorReport:
    """Error report data structure"""
    error_id: str
    timestamp: datetime
    error_type: str
    message: str
    severity: ErrorSeverity
    category: ErrorCategory
    context: Dict[str, Any]
    stack_trace: str
    retry_count: int = 0
    resolved: bool = False
    resolution_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "error_id": self.error_id,
            "timestamp": self.timestamp.isoformat(),
            "error_type": self.error_type,
            "message": self.message,
            "severity": self.severity.value,
            "category": self.category.value,
            "context": self.context,
            "stack_trace": self.stack_trace,
            "retry_count": self.retry_count,
            "resolved": self.resolved,
            "resolution_time": self.resolution_time.isoformat() if self.resolution_time else None
        }

@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration"""
    failure_threshold: int = 5
    recovery_timeout: int = 60
    expected_exception: Type[Exception] = Exception
    name: str = "default"

@dataclass
class RetryConfig:
    """Retry configuration"""
    max_attempts: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0
    jitter: bool = True

class CircuitBreaker:
    """Circuit breaker implementation"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = None
        self.success_count = 0
        self.lock = threading.Lock()
        
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection"""
        with self.lock:
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                    logger.info(f"Circuit breaker {self.config.name} moved to HALF_OPEN")
                else:
                    raise TradingSystemError(
                        f"Circuit breaker {self.config.name} is OPEN",
                        error_code="CIRCUIT_BREAKER_OPEN"
                    )
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
            except self.config.expected_exception as e:
                self._on_failure()
                raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset"""
        if self.last_failure_time is None:
            return True
        return (datetime.now() - self.last_failure_time).total_seconds() > self.config.recovery_timeout
    
    def _on_success(self):
        """Handle successful execution"""
        if self.state == CircuitState.HALF_OPEN:
            self.state = CircuitState.CLOSED
            self.failure_count = 0
            logger.info(f"Circuit breaker {self.config.name} moved to CLOSED")
        self.success_count += 1
    
    def _on_failure(self):
        """Handle failed execution"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitState.OPEN
            logger.warning(f"Circuit breaker {self.config.name} moved to OPEN")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get circuit breaker statistics"""
        return {
            "name": self.config.name,
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "failure_threshold": self.config.failure_threshold,
            "last_failure_time": self.last_failure_time.isoformat() if self.last_failure_time else None
        }

class RetryHandler:
    """Retry handler with different strategies"""
    
    def __init__(self, config: RetryConfig):
        self.config = config
        
    def execute(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with retry logic"""
        last_exception = None
        
        for attempt in range(self.config.max_attempts):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt == self.config.max_attempts - 1:
                    # Last attempt failed
                    break
                
                delay = self._calculate_delay(attempt)
                logger.info(f"Retry attempt {attempt + 1} failed, retrying in {delay:.2f}s: {e}")
                time.sleep(delay)
        
        # All attempts failed
        raise TradingSystemError(
            f"All {self.config.max_attempts} retry attempts failed. Last error: {last_exception}",
            error_code="RETRY_EXHAUSTED",
            context={"original_error": str(last_exception)}
        )
    
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt"""
        if self.config.strategy == RetryStrategy.FIXED:
            delay = self.config.base_delay
        elif self.config.strategy == RetryStrategy.EXPONENTIAL:
            delay = self.config.base_delay * (self.config.backoff_factor ** attempt)
        elif self.config.strategy == RetryStrategy.LINEAR:
            delay = self.config.base_delay * (attempt + 1)
        elif self.config.strategy == RetryStrategy.FIBONACCI:
            delay = self.config.base_delay * self._fibonacci(attempt + 1)
        else:
            delay = self.config.base_delay
        
        # Apply max delay limit
        delay = min(delay, self.config.max_delay)
        
        # Add jitter if enabled
        if self.config.jitter:
            import random
            delay = delay * (0.5 + random.random() * 0.5)
        
        return delay
    
    @staticmethod
    def _fibonacci(n: int) -> int:
        """Calculate fibonacci number"""
        if n <= 1:
            return n
        return RetryHandler._fibonacci(n - 1) + RetryHandler._fibonacci(n - 2)

class ErrorClassifier:
    """Error classification system"""
    
    ERROR_MAPPING = {
        ConnectionError: (ErrorSeverity.HIGH, ErrorCategory.NETWORK),
        DataError: (ErrorSeverity.MEDIUM, ErrorCategory.DATA),
        OrderError: (ErrorSeverity.HIGH, ErrorCategory.BUSINESS_LOGIC),
        AuthenticationError: (ErrorSeverity.CRITICAL, ErrorCategory.AUTHENTICATION),
        RateLimitError: (ErrorSeverity.MEDIUM, ErrorCategory.EXTERNAL_API),
        ValidationError: (ErrorSeverity.LOW, ErrorCategory.VALIDATION),
        ConfigurationError: (ErrorSeverity.HIGH, ErrorCategory.SYSTEM),
        TimeoutError: (ErrorSeverity.MEDIUM, ErrorCategory.NETWORK),
    }
    
    @classmethod
    def classify(cls, error: Exception) -> Tuple[ErrorSeverity, ErrorCategory]:
        """Classify error by type"""
        for error_type, (severity, category) in cls.ERROR_MAPPING.items():
            if isinstance(error, error_type):
                return severity, category
        
        # Default classification
        return ErrorSeverity.MEDIUM, ErrorCategory.SYSTEM

class EnhancedErrorHandler:
    """Enhanced error handling system"""
    
    def __init__(self, name: str = "default"):
        self.name = name
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.retry_handlers: Dict[str, RetryHandler] = {}
        self.error_reports: List[ErrorReport] = []
        self.error_stats: Dict[str, int] = defaultdict(int)
        self.alert_thresholds: Dict[ErrorSeverity, int] = {
            ErrorSeverity.LOW: 50,
            ErrorSeverity.MEDIUM: 20,
            ErrorSeverity.HIGH: 10,
            ErrorSeverity.CRITICAL: 1
        }
        self.logger = logging.getLogger(f"{__name__}.{name}")
        
    def register_circuit_breaker(self, name: str, config: CircuitBreakerConfig) -> None:
        """Register a circuit breaker"""
        self.circuit_breakers[name] = CircuitBreaker(config)
        self.logger.info(f"Circuit breaker '{name}' registered")
    
    def register_retry_handler(self, name: str, config: RetryConfig) -> None:
        """Register a retry handler"""
        self.retry_handlers[name] = RetryHandler(config)
        self.logger.info(f"Retry handler '{name}' registered")
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> ErrorReport:
        """Handle and classify an error"""
        error_id = f"ERR_{int(time.time() * 1000)}"
        severity, category = ErrorClassifier.classify(error)
        
        error_report = ErrorReport(
            error_id=error_id,
            timestamp=datetime.now(),
            error_type=type(error).__name__,
            message=str(error),
            severity=severity,
            category=category,
            context=context or {},
            stack_trace=traceback.format_exc()
        )
        
        self.error_reports.append(error_report)
        self.error_stats[error_report.error_type] += 1
        
        # Log error
        self.logger.error(f"Error handled: {error_report.error_id} - {error_report.message}")
        
        # Check for alerts
        self._check_alert_thresholds(error_report)
        
        return error_report
    
    def _check_alert_thresholds(self, error_report: ErrorReport) -> None:
        """Check if error count exceeds alert thresholds"""
        threshold = self.alert_thresholds.get(error_report.severity, 100)
        recent_errors = self._get_recent_errors(error_report.error_type, minutes=10)
        
        if len(recent_errors) >= threshold:
            self._send_alert(error_report, len(recent_errors))
    
    def _get_recent_errors(self, error_type: str, minutes: int = 10) -> List[ErrorReport]:
        """Get recent errors of specific type"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [
            report for report in self.error_reports
            if report.error_type == error_type and report.timestamp >= cutoff_time
        ]
    
    def _send_alert(self, error_report: ErrorReport, count: int) -> None:
        """Send alert for error threshold breach"""
        self.logger.critical(
            f"ALERT: {error_report.error_type} occurred {count} times in last 10 minutes. "
            f"Severity: {error_report.severity.value}"
        )
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics"""
        total_errors = len(self.error_reports)
        if total_errors == 0:
            return {"total_errors": 0, "error_types": {}, "severity_breakdown": {}}
        
        severity_breakdown = defaultdict(int)
        category_breakdown = defaultdict(int)
        
        for report in self.error_reports:
            severity_breakdown[report.severity.value] += 1
            category_breakdown[report.category.value] += 1
        
        return {
            "total_errors": total_errors,
            "error_types": dict(self.error_stats),
            "severity_breakdown": dict(severity_breakdown),
            "category_breakdown": dict(category_breakdown),
            "recent_errors": len(self._get_recent_errors("", minutes=60))
        }
    
    def export_error_report(self, filename: str = None) -> str:
        """Export error report to JSON"""
        if filename is None:
            filename = f"error_report_{self.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report_data = {
            "handler_name": self.name,
            "export_timestamp": datetime.now().isoformat(),
            "statistics": self.get_error_statistics(),
            "circuit_breakers": {
                name: breaker.get_stats() 
                for name, breaker in self.circuit_breakers.items()
            },
            "error_reports": [report.to_dict() for report in self.error_reports[-100:]]  # Last 100 errors
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Error report exported to {filename}")
            return filename
        except Exception as e:
            self.logger.error(f"Failed to export error report: {e}")
            return ""

# Decorators
def handle_error(handler: EnhancedErrorHandler, raise_on_error: bool = False):
    """Decorator to handle errors with enhanced error handler"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_report = handler.handle_error(e, {
                    "function": func.__name__,
                    "args": str(args),
                    "kwargs": str(kwargs)
                })
                
                if raise_on_error:
                    raise e
                
                return None
        return wrapper
    return decorator

def with_circuit_breaker(handler: EnhancedErrorHandler, breaker_name: str):
    """Decorator to add circuit breaker protection"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if breaker_name not in handler.circuit_breakers:
                raise ConfigurationError(f"Circuit breaker '{breaker_name}' not found")
            
            breaker = handler.circuit_breakers[breaker_name]
            return breaker.call(func, *args, **kwargs)
        return wrapper
    return decorator

def with_retry(handler: EnhancedErrorHandler, retry_name: str):
    """Decorator to add retry logic"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if retry_name not in handler.retry_handlers:
                raise ConfigurationError(f"Retry handler '{retry_name}' not found")
            
            retry_handler = handler.retry_handlers[retry_name]
            return retry_handler.execute(func, *args, **kwargs)
        return wrapper
    return decorator

@contextmanager
def error_context(handler: EnhancedErrorHandler, context: Dict[str, Any] = None):
    """Context manager for error handling"""
    try:
        yield
    except Exception as e:
        handler.handle_error(e, context)
        raise

# Global instance
enhanced_error_handler = EnhancedErrorHandler("global")

# Configure default circuit breakers and retry handlers
enhanced_error_handler.register_circuit_breaker("default", CircuitBreakerConfig())
enhanced_error_handler.register_circuit_breaker("api_calls", CircuitBreakerConfig(
    failure_threshold=3,
    recovery_timeout=30,
    name="api_calls"
))

enhanced_error_handler.register_retry_handler("default", RetryConfig())
enhanced_error_handler.register_retry_handler("network", RetryConfig(
    max_attempts=5,
    strategy=RetryStrategy.EXPONENTIAL,
    base_delay=1.0,
    max_delay=30.0
))

# Convenience functions
def handle_trading_error(error: Exception, context: Dict[str, Any] = None) -> ErrorReport:
    """Handle trading system error"""
    return enhanced_error_handler.handle_error(error, context)

def get_error_stats() -> Dict[str, Any]:
    """Get error statistics"""
    return enhanced_error_handler.get_error_statistics()

def export_error_report(filename: str = None) -> str:
    """Export error report"""
    return enhanced_error_handler.export_error_report(filename)

# Example usage and testing
if __name__ == "__main__":
    print("🛡️ Testing Enhanced Error Handling System...")
    
    # Test error handling
    try:
        raise ConnectionError("Test connection error", "CONN_001")
    except Exception as e:
        report = handle_trading_error(e, {"test": "example"})
        print(f"Error handled: {report.error_id}")
    
    # Test circuit breaker
    @with_circuit_breaker(enhanced_error_handler, "default")
    def failing_function():
        raise Exception("Always fails")
    
    for i in range(7):
        try:
            failing_function()
        except Exception as e:
            print(f"Attempt {i+1}: {e}")
    
    # Test retry logic
    @with_retry(enhanced_error_handler, "default")
    def sometimes_failing_function():
        import random
        if random.random() < 0.7:
            raise Exception("Random failure")
        return "Success!"
    
    try:
        result = sometimes_failing_function()
        print(f"Retry test result: {result}")
    except Exception as e:
        print(f"Retry test failed: {e}")
    
    # Export report
    report_file = export_error_report()
    print(f"Error report exported: {report_file}")
    
    # Get statistics
    stats = get_error_stats()
    print(f"Error statistics: {stats}")
    
    print("✅ Enhanced Error Handling System test completed!") 