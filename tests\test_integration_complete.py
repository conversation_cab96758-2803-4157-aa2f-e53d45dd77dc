#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Complete Integration Tests
تست‌های ادغام کامل سیستم
"""

import os
import sys
import unittest
import asyncio
import pytest
from datetime import datetime, timedelta
import json
import tempfile

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestSystemIntegration:
    """تست‌های ادغام سیستم"""
    
    def setup_method(self):
        """تنظیم اولیه"""
        self.test_config = {
            'database': {
                'url': 'sqlite:///test_integration.db'
            },
            'exchanges': {
                'forex_main': {
                    'enabled': True,
                    'sandbox': True
                },
                'crypto_main': {
                    'enabled': True,
                    'sandbox': True
                }
            }
        }
    
    def test_system_initialization(self):
        """تست مقداردهی اولیه سیستم"""
        try:
            from main_new import TradingSystemManager
            manager = TradingSystemManager()
            assert manager is not None
            assert hasattr(manager, 'initialize_system')
        except Exception as e:
            pytest.fail(f"System initialization failed: {e}")
    
    def test_config_loading(self):
        """تست بارگذاری تنظیمات"""
        try:
            from core.config_manager import ConfigManager
            config_manager = ConfigManager()
            assert config_manager is not None
        except Exception as e:
            pytest.fail(f"Config loading failed: {e}")
    
    def test_database_connection(self):
        """تست اتصال پایگاه داده"""
        try:
            from core.simple_database_manager import SimpleDatabaseManager
            db_manager = SimpleDatabaseManager("test_integration.db")
            assert db_manager is not None
            db_manager.close()
        except Exception as e:
            pytest.fail(f"Database connection failed: {e}")
    
    def test_error_handling(self):
        """تست مدیریت خطا"""
        try:
            from core.error_handler import ErrorHandler
            error_handler = ErrorHandler()
            assert error_handler is not None
        except Exception as e:
            pytest.fail(f"Error handling failed: {e}")
    
    def test_backtesting_framework(self):
        """تست چارچوب بک‌تستینگ"""
        try:
            from utils.backtesting_framework import BacktestingFramework
            framework = BacktestingFramework()
            assert framework is not None
        except Exception as e:
            pytest.fail(f"Backtesting framework failed: {e}")
    
    def test_sentiment_analyzer(self):
        """تست تحلیل‌گر احساسات"""
        try:
            from utils.sentiment_analyzer import SentimentAnalyzer
            analyzer = SentimentAnalyzer()
            assert analyzer is not None
            
            # Test basic sentiment analysis
            result = analyzer.analyze("This is a positive news for the market.")
            assert result is not None
            assert hasattr(result, 'label')
        except Exception as e:
            pytest.fail(f"Sentiment analyzer failed: {e}")
    
    def test_risk_manager(self):
        """تست مدیر ریسک"""
        try:
            from portfolio.advanced_risk_manager import AdvancedRiskManager
            risk_manager = AdvancedRiskManager(initial_capital=10000)
            assert risk_manager is not None
            assert risk_manager.initial_capital == 10000
        except Exception as e:
            pytest.fail(f"Risk manager failed: {e}")
    
    def test_multi_exchange(self):
        """تست چند صرافی"""
        try:
            from core.multi_exchange import MultiExchangeManager
            manager = MultiExchangeManager()
            assert manager is not None
        except Exception as e:
            pytest.fail(f"Multi-exchange manager failed: {e}")
    
    def test_model_registry(self):
        """تست ثبت‌نام مدل"""
        try:
            from ai_models import ModelRegistry
            registry = ModelRegistry()
            assert registry is not None
        except Exception as e:
            pytest.fail(f"Model registry failed: {e}")
    
    def test_portfolio_manager(self):
        """تست مدیر پورتفولیو"""
        try:
            from portfolio.portfolio_manager import PortfolioManager
            portfolio_manager = PortfolioManager()
            assert portfolio_manager is not None
        except Exception as e:
            pytest.fail(f"Portfolio manager failed: {e}")

class TestDataFlow:
    """تست جریان داده"""
    
    def test_data_pipeline(self):
        """تست خط لوله داده"""
        try:
            from data.fetcher import DataFetcher
            fetcher = DataFetcher()
            assert fetcher is not None
        except Exception as e:
            pytest.fail(f"Data pipeline failed: {e}")
    
    def test_data_cleaning(self):
        """تست پاک‌سازی داده"""
        try:
            from utils.data_cleaning_pipeline import DataCleaningPipeline
            pipeline = DataCleaningPipeline()
            assert pipeline is not None
        except Exception as e:
            pytest.fail(f"Data cleaning failed: {e}")

class TestModelIntegration:
    """تست ادغام مدل‌ها"""
    
    def test_sentiment_ensemble(self):
        """تست مجموعه تحلیل احساسات"""
        try:
            from ai_models import SentimentEnsemble
            ensemble = SentimentEnsemble()
            assert ensemble is not None
        except Exception as e:
            pytest.fail(f"Sentiment ensemble failed: {e}")
    
    def test_timeseries_ensemble(self):
        """تست مجموعه سری زمانی"""
        try:
            from ai_models import TimeSeriesEnsemble
            ensemble = TimeSeriesEnsemble()
            assert ensemble is not None
        except Exception as e:
            pytest.fail(f"Time series ensemble failed: {e}")

class TestAPIIntegration:
    """تست ادغام API"""
    
    def test_api_endpoints(self):
        """تست نقاط پایانی API"""
        try:
            from api.endpoints import app
            assert app is not None
        except Exception as e:
            pytest.fail(f"API endpoints failed: {e}")

if __name__ == "__main__":
    # Run all tests
    pytest.main([__file__, "-v"]) 