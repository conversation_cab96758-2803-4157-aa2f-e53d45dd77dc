import pandas as pd
import numpy as np
from utils.data_pipeline import DataPipeline, normalize_features

def test_normalize_features():
    df = pd.DataFrame({
        'a': [1, 2, 3, 4],
        'b': [10, 20, 30, 40],
        'c': ['x', 'y', 'z', 'w']
    })
    norm_df = normalize_features(df.copy(), columns=['a', 'b'])
    assert np.isclose(norm_df['a'].min(), 0)
    assert np.isclose(norm_df['a'].max(), 1)
    assert np.isclose(norm_df['b'].min(), 0)
    assert np.isclose(norm_df['b'].max(), 1)

def test_pipeline_run():
    df = pd.DataFrame({'x': [1, 2, 3]})
    pipeline = DataPipeline([lambda d: d.assign(x2=d['x']*2)])
    out = pipeline.run(df)
    assert 'x2' in out.columns
    assert (out['x2'] == df['x']*2).all()
