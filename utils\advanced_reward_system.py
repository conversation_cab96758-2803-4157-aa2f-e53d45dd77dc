"""Advanced Reward System

سیستم پاداش پیشرفته با قابلیت‌های:
- سیستم پاداش تطبیقی پویا
- مکانیزم حافظه drawdown
- پاداش‌دهی مبتنی بر بازیابی
- تنظیم پاداش بر اساس نوع بازار
- سیستم پاداش چندسطحی
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, field
from collections import deque
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class MarketRegime(Enum):
    """انواع رژیم‌های بازار"""
    BULL = "bull"          # صعودی
    BEAR = "bear"          # نزولی
    SIDEWAYS = "sideways"  # خنثی
    VOLATILE = "volatile"  # پرنوسان


@dataclass
class DrawdownMemory:
    """حافظه drawdown برای یادگیری از تجربیات گذشته"""
    start_time: int
    end_time: Optional[int]
    max_depth: float
    duration: int
    recovery_time: Optional[int]
    cause: Optional[str] = None
    lessons_learned: Dict[str, float] = field(default_factory=dict)


@dataclass
class RewardAdjustment:
    """تنظیمات پاداش برای شرایط مختلف"""
    base_multiplier: float = 1.0
    drawdown_multiplier: float = 1.0
    regime_multiplier: float = 1.0
    recovery_bonus: float = 0.0
    volatility_penalty: float = 0.0
    final_multiplier: float = 1.0


class AdaptiveRewardSystem:
    """سیستم پاداش تطبیقی پیشرفته"""
    
    def __init__(self,
                 memory_size: int = 1000,
                 learning_rate: float = 0.01,
                 volatility_threshold: float = 0.02,
                 recovery_bonus_factor: float = 0.5):
        """
        Parameters
        ----------
        memory_size : int
            اندازه حافظه برای ذخیره تجربیات
        learning_rate : float
            نرخ یادگیری برای تطبیق پارامترها
        volatility_threshold : float
            آستانه نوسان برای اعمال جریمه
        recovery_bonus_factor : float
            ضریب پاداش بازیابی
        """
        self.memory_size = memory_size
        self.learning_rate = learning_rate
        self.volatility_threshold = volatility_threshold
        self.recovery_bonus_factor = recovery_bonus_factor
        
        # حافظه تجربیات
        self.drawdown_memories: deque = deque(maxlen=memory_size)
        self.performance_history: deque = deque(maxlen=memory_size)
        
        # پارامترهای تطبیقی
        self.adaptive_params = {
            'bull_multiplier': 1.0,
            'bear_multiplier': 1.2,
            'sideways_multiplier': 1.1,
            'volatile_multiplier': 1.3,
            'recovery_sensitivity': 0.5,
            'drawdown_sensitivity': 2.0
        }
        
        # وضعیت فعلی
        self.current_drawdown = 0.0
        self.current_regime = MarketRegime.SIDEWAYS
        self.in_recovery = False
        self.recovery_start_time = None
        
    def detect_market_regime(self, 
                           returns: np.ndarray, 
                           window: int = 20) -> MarketRegime:
        """تشخیص رژیم بازار بر اساس بازده‌های اخیر
        
        Parameters
        ----------
        returns : np.ndarray
            بازده‌های اخیر
        window : int
            پنجره زمانی برای تحلیل
            
        Returns
        -------
        MarketRegime
            رژیم تشخیص داده شده
        """
        if len(returns) < window:
            return MarketRegime.SIDEWAYS
            
        recent_returns = returns[-window:]
        
        # محاسبه آمار
        mean_return = np.mean(recent_returns)
        volatility = np.std(recent_returns)
        trend_strength = abs(mean_return) / (volatility + 1e-8)
        
        # تشخیص رژیم
        if volatility > self.volatility_threshold * 2:
            return MarketRegime.VOLATILE
        elif trend_strength > 0.5:
            if mean_return > 0:
                return MarketRegime.BULL
            else:
                return MarketRegime.BEAR
        else:
            return MarketRegime.SIDEWAYS
            
    def calculate_dynamic_multiplier(self,
                                   current_drawdown: float,
                                   market_regime: MarketRegime,
                                   volatility: float,
                                   time_step: int) -> RewardAdjustment:
        """محاسبه ضریب پاداش پویا
        
        Parameters
        ----------
        current_drawdown : float
            drawdown فعلی
        market_regime : MarketRegime
            رژیم فعلی بازار
        volatility : float
            نوسان فعلی
        time_step : int
            گام زمانی فعلی
            
        Returns
        -------
        RewardAdjustment
            تنظیمات پاداش محاسبه شده
        """
        adjustment = RewardAdjustment()
        
        # 1. ضریب پایه بر اساس drawdown
        if current_drawdown > 0:
            # استفاده از تابع غیرخطی برای drawdown
            dd_factor = np.exp(current_drawdown * self.adaptive_params['drawdown_sensitivity'])
            adjustment.drawdown_multiplier = min(dd_factor, 3.0)
        
        # 2. ضریب رژیم بازار
        regime_multipliers = {
            MarketRegime.BULL: self.adaptive_params['bull_multiplier'],
            MarketRegime.BEAR: self.adaptive_params['bear_multiplier'],
            MarketRegime.SIDEWAYS: self.adaptive_params['sideways_multiplier'],
            MarketRegime.VOLATILE: self.adaptive_params['volatile_multiplier']
        }
        adjustment.regime_multiplier = regime_multipliers[market_regime]
        
        # 3. پاداش بازیابی
        if self.in_recovery:
            recovery_progress = self._calculate_recovery_progress()
            adjustment.recovery_bonus = recovery_progress * self.recovery_bonus_factor
        
        # 4. جریمه نوسان
        if volatility > self.volatility_threshold:
            vol_penalty = (volatility - self.volatility_threshold) * 0.5
            adjustment.volatility_penalty = min(vol_penalty, 0.3)
        
        # 5. محاسبه ضریب نهایی
        adjustment.final_multiplier = (
            adjustment.base_multiplier *
            adjustment.drawdown_multiplier *
            adjustment.regime_multiplier *
            (1 + adjustment.recovery_bonus) *
            (1 - adjustment.volatility_penalty)
        )
        
        return adjustment
        
    def _calculate_recovery_progress(self) -> float:
        """محاسبه پیشرفت بازیابی از drawdown"""
        if not self.in_recovery or self.recovery_start_time is None:
            return 0.0
            
        # پیشرفت بر اساس کاهش drawdown
        if len(self.performance_history) > 0:
            recent_performance = list(self.performance_history)[-10:]
            if len(recent_performance) > 1:
                improvement = recent_performance[-1] - recent_performance[0]
                return max(0, min(1, improvement * 10))  # نرمال‌سازی
        
        return 0.0
        
    def learn_from_experience(self, 
                            drawdown_memory: DrawdownMemory,
                            performance_outcome: float):
        """یادگیری از تجربیات گذشته
        
        Parameters
        ----------
        drawdown_memory : DrawdownMemory
            حافظه drawdown
        performance_outcome : float
            نتیجه عملکرد (-1 تا 1)
        """
        # ذخیره تجربه
        self.drawdown_memories.append(drawdown_memory)
        self.performance_history.append(performance_outcome)
        
        # یادگیری تطبیقی
        if len(self.drawdown_memories) > 10:
            self._update_adaptive_parameters()
            
    def _update_adaptive_parameters(self):
        """به‌روزرسانی پارامترهای تطبیقی بر اساس تجربیات"""
        recent_memories = list(self.drawdown_memories)[-10:]
        recent_performance = list(self.performance_history)[-10:]
        
        # تحلیل الگوهای موفق
        successful_cases = []
        failed_cases = []
        
        for memory, performance in zip(recent_memories, recent_performance):
            if performance > 0.5:
                successful_cases.append(memory)
            elif performance < -0.5:
                failed_cases.append(memory)
        
        # تنظیم پارامترها بر اساس موفقیت‌ها
        if successful_cases:
            avg_successful_depth = np.mean([m.max_depth for m in successful_cases])
            if avg_successful_depth > 0.1:  # drawdown بالا اما موفق
                self.adaptive_params['drawdown_sensitivity'] *= (1 - self.learning_rate)
                
        # تنظیم بر اساس شکست‌ها
        if failed_cases:
            avg_failed_depth = np.mean([m.max_depth for m in failed_cases])
            if avg_failed_depth > 0.05:  # drawdown کم اما ناموفق
                self.adaptive_params['drawdown_sensitivity'] *= (1 + self.learning_rate)
                
    def get_multilevel_reward(self,
                            base_reward: float,
                            drawdown_level: float,
                            confidence: float = 1.0) -> Dict[str, float]:
        """سیستم پاداش چندسطحی
        
        Parameters
        ----------
        base_reward : float
            پاداش پایه
        drawdown_level : float
            سطح drawdown (0-1)
        confidence : float
            سطح اطمینان تصمیم (0-1)
            
        Returns
        -------
        Dict[str, float]
            پاداش‌های مختلف سطوح
        """
        # تعریف سطوح drawdown
        levels = {
            'safe': (0.0, 0.05),      # کم‌ریسک
            'moderate': (0.05, 0.10),  # متوسط
            'high': (0.10, 0.20),     # پرریسک
            'extreme': (0.20, 1.0)    # بحرانی
        }
        
        # تشخیص سطح فعلی
        current_level = 'safe'
        for level_name, (min_dd, max_dd) in levels.items():
            if min_dd <= drawdown_level < max_dd:
                current_level = level_name
                break
                
        # ضرایب پاداش برای هر سطح
        level_multipliers = {
            'safe': 1.0,
            'moderate': 1.2,
            'high': 1.5,
            'extreme': 2.0
        }
        
        # محاسبه پاداش‌های مختلف
        base_multiplier = level_multipliers[current_level]
        
        rewards = {
            'immediate': base_reward * base_multiplier,
            'risk_adjusted': base_reward * base_multiplier * confidence,
            'conservative': base_reward * base_multiplier * 0.8,
            'aggressive': base_reward * base_multiplier * 1.3,
            'adaptive': base_reward * self.adaptive_params.get('drawdown_sensitivity', 1.0)
        }
        
        return rewards
        
    def update_state(self,
                    equity_curve: np.ndarray,
                    returns: np.ndarray,
                    time_step: int):
        """به‌روزرسانی وضعیت سیستم
        
        Parameters
        ----------
        equity_curve : np.ndarray
            منحنی سرمایه
        returns : np.ndarray
            بازده‌ها
        time_step : int
            گام زمانی فعلی
        """
        # محاسبه drawdown فعلی
        if len(equity_curve) > 0:
            peak = np.max(equity_curve)
            current_value = equity_curve[-1]
            self.current_drawdown = (peak - current_value) / peak if peak > 0 else 0
            
        # تشخیص رژیم بازار
        self.current_regime = self.detect_market_regime(returns)
        
        # بررسی وضعیت بازیابی
        if self.current_drawdown < 0.02 and self.in_recovery:
            # پایان بازیابی
            self.in_recovery = False
            self.recovery_start_time = None
        elif self.current_drawdown > 0.05 and not self.in_recovery:
            # شروع بازیابی
            self.in_recovery = True
            self.recovery_start_time = time_step
            
    def get_system_status(self) -> Dict[str, Union[float, str, bool]]:
        """دریافت وضعیت کامل سیستم
        
        Returns
        -------
        Dict[str, Union[float, str, bool]]
            وضعیت سیستم
        """
        return {
            'current_drawdown': self.current_drawdown,
            'market_regime': self.current_regime.value,
            'in_recovery': self.in_recovery,
            'memory_size': len(self.drawdown_memories),
            'adaptive_params': self.adaptive_params.copy(),
            'recovery_progress': self._calculate_recovery_progress() if self.in_recovery else 0.0
        } 


class AdvancedRewardSystem:
    """سیستم پاداش پیشرفته - wrapper برای AdaptiveRewardSystem"""
    
    def __init__(self):
        self.adaptive_system = AdaptiveRewardSystem()
        self.logger = logging.getLogger(__name__)
        
    def calculate_reward(self, action: Dict, result: Dict) -> float:
        """محاسبه پاداش برای یک اقدام"""
        try:
            base_reward = result.get('profit', 0) / 100.0  # نرمال‌سازی
            drawdown = result.get('drawdown', 0)
            
            # استفاده از سیستم تطبیقی
            adjustment = self.adaptive_system.calculate_dynamic_multiplier(
                current_drawdown=drawdown,
                market_regime=self.adaptive_system.current_regime,
                volatility=result.get('volatility', 0.02),
                time_step=result.get('time_step', 0)
            )
            
            final_reward = base_reward * adjustment.final_multiplier
            
            self.logger.debug(f"Reward calculated: {base_reward} -> {final_reward}")
            return final_reward
            
        except Exception as e:
            self.logger.error(f"Error calculating reward: {e}")
            return 0.0
    
    def get_status(self) -> Dict:
        """دریافت وضعیت سیستم"""
        return self.adaptive_system.get_system_status() 