"""
Configuration Management Module
مدیریت پیکربندی با pydantic، environment variables، و dynamic config loading

Features:
- Pydantic-based configuration models
- Environment variable loading
- Configuration validation
- Dynamic configuration reloading
- Configuration templates
- Environment-specific configurations
- Configuration versioning
- Configuration backup and restore
"""

import os
import json
import yaml
import logging
from datetime import datetime
from typing import (
    Any, Dict, List, Optional, Union, Type, 
    ClassVar, Set, Callable
)
from pathlib import Path
from dataclasses import dataclass
from enum import Enum, auto
import asyncio
from contextlib import asynccontextmanager

try:
    from pydantic import (
        BaseModel, Field, validator, root_validator, 
        ValidationError, BaseSettings
    )
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False
    # Fallback classes for when pydantic is not available
    class BaseModel:
        """Very light replacement for pydantic.BaseModel – **only** for runtime without pydantic.
        It simply stores incoming keyword arguments as attributes so that the rest of
        the code that instantiates config classes with **kwargs continues to work.
        """
        def __init__(self, **data):
            for key, value in data.items():
                setattr(self, key, value)
        # Minimal API used elsewhere
        def dict(self):
            return self.__dict__
        # Newer code sometimes calls model_dump() (pydantic v2)
        def model_dump(self):
            return self.__dict__
    class BaseSettings:
        pass
    class Field:
        """Mimic pydantic Field – returns the default value when accessed as a descriptor."""
        def __init__(self, default=None, *args, **kwargs):
            self.default = default
        def __get__(self, instance, owner):
            return self.default
    def validator(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    def root_validator(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    ValidationError = Exception

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration Types
class ConfigFormat(Enum):
    JSON = "json"
    YAML = "yaml"
    TOML = "toml"
    INI = "ini"
    ENV = "env"

class Environment(Enum):
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"

class ConfigSource(Enum):
    FILE = "file"
    ENVIRONMENT = "environment"
    DATABASE = "database"
    REMOTE = "remote"
    DEFAULT = "default"

@dataclass
class ConfigChange:
    """Configuration change record"""
    timestamp: datetime
    key: str
    old_value: Any
    new_value: Any
    source: ConfigSource
    user: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "timestamp": self.timestamp.isoformat(),
            "key": self.key,
            "old_value": self.old_value,
            "new_value": self.new_value,
            "source": self.source.value,
            "user": self.user
        }

# Base Configuration Models
class DatabaseConfig(BaseModel):
    """Database configuration"""
    host: str = Field(default="localhost", description="Database host")
    port: int = Field(default=5432, description="Database port")
    name: str = Field(default="trading_db", description="Database name")
    username: str = Field(default="admin", description="Database username")
    password: str = Field(default="", description="Database password")
    ssl_mode: str = Field(default="prefer", description="SSL mode")
    pool_size: int = Field(default=10, description="Connection pool size")
    max_overflow: int = Field(default=20, description="Max pool overflow")
    echo: bool = Field(default=False, description="Enable SQL logging")
    
    @validator('port')
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError('Port must be between 1 and 65535')
        return v
    
    @validator('pool_size')
    def validate_pool_size(cls, v):
        if v < 1:
            raise ValueError('Pool size must be at least 1')
        return v
    
    class Config:
        env_prefix = "DB_"

class ExchangeConfig(BaseModel):
    """Exchange configuration"""
    name: str = Field(..., description="Exchange name")
    api_key: str = Field(default="", description="API key")
    secret_key: str = Field(default="", description="Secret key")
    sandbox: bool = Field(default=True, description="Use sandbox mode")
    rate_limit: int = Field(default=10, description="Rate limit per second")
    timeout: float = Field(default=30.0, description="Request timeout")
    retry_attempts: int = Field(default=3, description="Retry attempts")
    enabled: bool = Field(default=True, description="Exchange enabled")
    
    @validator('rate_limit')
    def validate_rate_limit(cls, v):
        if v < 1:
            raise ValueError('Rate limit must be at least 1')
        return v
    
    @validator('timeout')
    def validate_timeout(cls, v):
        if v <= 0:
            raise ValueError('Timeout must be positive')
        return v

class TradingConfig(BaseModel):
    """Trading configuration"""
    max_positions: int = Field(default=10, description="Maximum open positions")
    max_risk_per_trade: float = Field(default=0.02, description="Max risk per trade (0.02 = 2%)")
    stop_loss_percentage: float = Field(default=0.05, description="Stop loss percentage")
    take_profit_percentage: float = Field(default=0.10, description="Take profit percentage")
    min_trade_amount: float = Field(default=100.0, description="Minimum trade amount")
    max_trade_amount: float = Field(default=10000.0, description="Maximum trade amount")
    trading_hours_start: str = Field(default="09:00", description="Trading start time")
    trading_hours_end: str = Field(default="17:00", description="Trading end time")
    allow_weekend_trading: bool = Field(default=False, description="Allow weekend trading")

    # Additional fields expected by higher-level application
    mode: str = Field(default="LIVE", description="Trading mode: LIVE or PAPER")
    initial_capital: float = Field(default=10000.0, description="Initial account capital")
    symbols: List[str] = Field(default_factory=lambda: ["EURUSD"], description="Default symbols list")
    
    @validator('max_risk_per_trade')
    def validate_max_risk(cls, v):
        if not 0 < v <= 1:
            raise ValueError('Max risk per trade must be between 0 and 1')
        return v
    
    @validator('min_trade_amount')
    def validate_min_amount(cls, v):
        if v <= 0:
            raise ValueError('Minimum trade amount must be positive')
        return v
    
    @root_validator
    def validate_amounts(cls, values):
        min_amount = values.get('min_trade_amount', 0)
        max_amount = values.get('max_trade_amount', 0)
        if min_amount >= max_amount:
            raise ValueError('Minimum trade amount must be less than maximum')
        return values

class LoggingConfig(BaseModel):
    """Logging configuration"""
    level: str = Field(default="INFO", description="Log level")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format"
    )
    file_path: Optional[str] = Field(default=None, description="Log file path")
    max_file_size: str = Field(default="10MB", description="Max log file size")
    backup_count: int = Field(default=5, description="Number of backup files")
    console_logging: bool = Field(default=True, description="Enable console logging")
    
    @validator('level')
    def validate_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of {valid_levels}')
        return v.upper()

class SecurityConfig(BaseModel):
    """Security configuration"""
    encryption_key: str = Field(default="", description="Encryption key")
    jwt_secret: str = Field(default="", description="JWT secret key")
    jwt_expire_minutes: int = Field(default=30, description="JWT expiration in minutes")
    api_rate_limit: int = Field(default=100, description="API rate limit per minute")
    allowed_ips: List[str] = Field(default_factory=list, description="Allowed IP addresses")
    require_https: bool = Field(default=True, description="Require HTTPS")
    
    @validator('jwt_expire_minutes')
    def validate_jwt_expire(cls, v):
        if v <= 0:
            raise ValueError('JWT expiration must be positive')
        return v

class MonitoringConfig(BaseModel):
    """Monitoring configuration"""
    enabled: bool = Field(default=True, description="Enable monitoring")
    metrics_interval: int = Field(default=60, description="Metrics collection interval")
    health_check_interval: int = Field(default=30, description="Health check interval")
    alert_threshold_cpu: float = Field(default=80.0, description="CPU alert threshold")
    alert_threshold_memory: float = Field(default=80.0, description="Memory alert threshold")
    alert_threshold_disk: float = Field(default=90.0, description="Disk alert threshold")
    
    @validator('metrics_interval')
    def validate_metrics_interval(cls, v):
        if v <= 0:
            raise ValueError('Metrics interval must be positive')
        return v

# --------------------------------------------------------------
# Main trading system configuration model
# --------------------------------------------------------------

class TradingSystemConfig(BaseModel):
    """Main trading system configuration"""
    
    # Application settings
    app_name: str = Field(default="Advanced Trading System", description="Application name")
    app_version: str = Field(default="1.0.0", description="Application version")
    environment: Environment = Field(default=Environment.DEVELOPMENT, description="Environment")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Component configurations
    database: DatabaseConfig = Field(default_factory=DatabaseConfig, description="Database config")
    exchanges: List[ExchangeConfig] = Field(default_factory=list, description="Exchange configs")
    trading: TradingConfig = Field(default_factory=TradingConfig, description="Trading config")
    logging: LoggingConfig = Field(default_factory=LoggingConfig, description="Logging config")
    security: SecurityConfig = Field(default_factory=SecurityConfig, description="Security config")
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig, description="Monitoring config")
    
    # Advanced settings
    data_retention_days: int = Field(default=365, description="Data retention period")
    backup_enabled: bool = Field(default=True, description="Enable backups")
    backup_interval_hours: int = Field(default=24, description="Backup interval")
    max_memory_usage_mb: int = Field(default=2048, description="Max memory usage in MB")

    # --------------------------------------------------
    # Runtime validation hook used by main_new.py
    # --------------------------------------------------
    def validate_configuration(self) -> Dict[str, Any]:
        """Runtime validation expected by main_new.py
        Returns a dict with keys:
          - is_valid: bool
          - errors: list[str]
          - warnings: list[str]
        """
        errors: List[str] = []
        warnings: List[str] = []

        # Ensure at least one enabled exchange
        if not self.exchanges or not any(getattr(exc, "enabled", True) for exc in self.exchanges):
            warnings.append("No enabled exchanges configured")

        # Ensure trading attributes expected by main_new.py exist
        if not hasattr(self.trading, "mode"):
            warnings.append("trading.mode not set (using runtime default 'LIVE')")

        if not hasattr(self.trading, "initial_capital"):
            warnings.append("trading.initial_capital not set (using runtime default 10000.0)")

        if not hasattr(self.trading, "symbols"):
            warnings.append("trading.symbols not set (using runtime default ['EURUSD'])")

        is_valid = len(errors) == 0
        return {"is_valid": is_valid, "errors": errors, "warnings": warnings}

    # Compatibility property expected by main_new.py
    @property
    def version(self) -> str:
        return self.app_version

    @version.setter
    def version(self, value: str):
        # Allow pydantic (or manual code) to set version without raising AttributeError
        object.__setattr__(self, 'app_version', value)
    
    def __init__(self, **data):
        super().__init__(**data)
    
    @validator('environment', pre=True)
    def validate_environment(cls, v):
        if isinstance(v, str):
            try:
                return Environment(v.lower())
            except ValueError:
                raise ValueError(f'Invalid environment: {v}')
        return v
    
    @root_validator
    def validate_config(cls, values):
        """Cross-field validation without mutating the input dict."""
        env = values.get('environment')
        debug_val = values.get('debug')

        # Emit warning if debug is True in production, but do NOT modify the field
        if env == Environment.PRODUCTION and debug_val:
            logger.warning("Debug mode True in production – ignored at runtime but left unmodified to avoid validation errors.")

        return values
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        allow_population_by_field_name = True

class ConfigurationManager:
    """Advanced configuration management system"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.config: Optional[TradingSystemConfig] = None
        self.config_history: List[ConfigChange] = []
        self.watchers: List[Callable] = []
        self.logger = logging.getLogger(__name__)
        
        # Configuration cache
        self._config_cache: Dict[str, Any] = {}
        self._last_reload = datetime.now()
        
    def load_config(
        self, 
        config_file: Optional[str] = None,
        environment: Optional[Environment] = None
    ) -> TradingSystemConfig:
        """Load configuration from file and environment"""
        
        # Determine config file
        if config_file is None:
            env_name = environment.value if environment else "development"
            config_file = self.config_dir / f"config_{env_name}.yaml"
        else:
            config_file = Path(config_file)
        
        # Load from file if exists
        config_data = {}
        if config_file.exists():
            config_data = self._load_config_file(config_file)
            self.logger.info(f"Loaded configuration from {config_file}")
        else:
            self.logger.warning(f"Configuration file {config_file} not found, using defaults")
        
        # Override with environment variables
        env_config = self._load_environment_config()
        config_data.update(env_config)
        
        # Create configuration object
        try:
            self.config = TradingSystemConfig(**config_data)
            self.logger.info("Configuration loaded successfully")
            
            # Cache the configuration
            self._config_cache = config_data
            self._last_reload = datetime.now()
            
            # Notify watchers
            self._notify_watchers()
            
            return self.config
            
        except ValidationError as e:
            self.logger.error(f"Configuration validation error: {e}")
            raise
    
    def _load_config_file(self, config_file: Path) -> Dict[str, Any]:
        """Load configuration from file"""
        with open(config_file, 'r', encoding='utf-8') as f:
            if config_file.suffix.lower() == '.json':
                return json.load(f)
            elif config_file.suffix.lower() in ['.yaml', '.yml']:
                return yaml.safe_load(f) or {}
            else:
                raise ValueError(f"Unsupported config file format: {config_file.suffix}")
    
    def _load_environment_config(self) -> Dict[str, Any]:
        """Load configuration from environment variables"""
        env_config = {}
        
        # Common environment variables
        env_mapping = {
            'TRADING_ENVIRONMENT': 'environment',
            'TRADING_DEBUG': 'debug',
            'TRADING_APP_NAME': 'app_name',
            'TRADING_APP_VERSION': 'app_version',
        }
        
        for env_var, config_key in env_mapping.items():
            value = os.getenv(env_var)
            if value is not None:
                # Convert string values to appropriate types
                if config_key == 'debug':
                    value = value.lower() in ('true', '1', 'yes', 'on')
                env_config[config_key] = value
        
        return env_config
    
    def save_config(
        self, 
        config_file: Optional[str] = None,
        format: ConfigFormat = ConfigFormat.YAML
    ) -> None:
        """Save current configuration to file"""
        if self.config is None:
            raise ValueError("No configuration loaded")
        
        if config_file is None:
            env_name = self.config.environment.value
            suffix = f".{format.value}"
            config_file = self.config_dir / f"config_{env_name}{suffix}"
        else:
            config_file = Path(config_file)
        
        # Convert config to dict
        config_dict = self._safe_dict(self.config)
        
        # Save to file
        with open(config_file, 'w', encoding='utf-8') as f:
            if format == ConfigFormat.JSON:
                json.dump(config_dict, f, indent=2, default=str)
            elif format == ConfigFormat.YAML:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
            else:
                raise ValueError(f"Unsupported save format: {format}")
        
        self.logger.info(f"Configuration saved to {config_file}")
    
    def _safe_dict(self, config_obj):
        """Safe method to get dict from config object"""
        if hasattr(config_obj, 'model_dump'):
            return config_obj.model_dump()
        elif hasattr(config_obj, 'dict'):
            return config_obj.dict()
        else:
            return dict(config_obj)

    def update_config(self, updates: Dict[str, Any]) -> None:
        """Update configuration with new values"""
        if self.config is None:
            raise ValueError("Configuration not loaded.")
        
        # Convert to dict for manipulation
        config_dict = self._safe_dict(self.config)
        
        # Apply updates
        for key, value in updates.items():
            self._set_nested_value(config_dict, key, value)
        
        # Recreate config object
        self.config = TradingSystemConfig(**config_dict)
        
        self.logger.info(f"Configuration updated with {len(updates)} changes")

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get a specific configuration value using dot notation"""
        if self.config is None:
            raise ValueError("Configuration not loaded.")
        
        return self._get_nested_value(self._safe_dict(self.config), key, default)

    def _get_nested_value(self, data: Dict[str, Any], key: str, default: Any = None) -> Any:
        """Get nested value from dictionary using dot notation"""
        keys = key.split('.')
        current = data
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return default
        
        return current
    
    def _set_nested_value(self, data: Dict[str, Any], key: str, value: Any) -> None:
        """Set nested value in dictionary using dot notation"""
        keys = key.split('.')
        current = data
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
    
    def validate_config(self) -> Dict[str, Any]:
        """Validate current configuration"""
        if self.config is None:
            return {"valid": False, "errors": ["No configuration loaded"]}
        
        try:
            # Re-validate the configuration
            TradingSystemConfig(**self._safe_dict(self.config))
            return {"valid": True, "errors": []}
        except ValidationError as e:
            return {"valid": False, "errors": [str(err) for err in e.errors()]}
    
    def create_template(self, template_name: str, environment: Environment) -> None:
        """Create configuration template"""
        template_config = TradingSystemConfig(environment=environment)
        template_file = self.config_dir / f"template_{template_name}.yaml"
        
        with open(template_file, 'w', encoding='utf-8') as f:
            yaml.dump(self._safe_dict(template_config), f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"Configuration template created: {template_file}")
    
    def backup_config(self, backup_name: Optional[str] = None) -> str:
        """Backup current configuration"""
        if self.config is None:
            raise ValueError("No configuration loaded")
            
        # Generate backup filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if backup_name:
            backup_filename = f"config_backup_{backup_name}_{timestamp}.yaml"
        else:
            backup_filename = f"config_backup_{timestamp}.yaml"
        
        backup_file = self.config_dir / backup_filename
        
        # Ensure config directory exists
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Convert config to dict safely
        config_dict = self._safe_dict(self.config)
        
        # Save backup
        with open(backup_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"Configuration backed up to {backup_file}")
        return str(backup_file)
    
    def restore_config(self, backup_file: str) -> None:
        """Restore configuration from backup"""
        backup_path = Path(backup_file)
        if not backup_path.exists():
            raise FileNotFoundError(f"Backup file not found: {backup_file}")
        
        self.load_config(backup_file)
        self.logger.info(f"Configuration restored from {backup_file}")
    
    def add_watcher(self, callback: Callable) -> None:
        """Add configuration change watcher"""
        self.watchers.append(callback)
        self.logger.debug(f"Configuration watcher added: {callback.__name__}")
    
    def remove_watcher(self, callback: Callable) -> None:
        """Remove configuration change watcher"""
        if callback in self.watchers:
            self.watchers.remove(callback)
            self.logger.debug(f"Configuration watcher removed: {callback.__name__}")
    
    def _notify_watchers(self) -> None:
        """Notify all watchers of configuration change"""
        for watcher in self.watchers:
            try:
                watcher(self.config)
            except Exception as e:
                self.logger.error(f"Error in config watcher {watcher.__name__}: {e}")
    
    def get_config_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get configuration change history"""
        return [change.to_dict() for change in self.config_history[-limit:]]
    
    def export_config(self, export_file: str, include_secrets: bool = False) -> None:
        """Export current config to a file"""
        if self.config is None:
            raise ValueError("No configuration loaded")
            
        # Use _safe_dict for compatibility
        config_to_export = self._safe_dict(self.config)

        if not include_secrets:
            self._remove_secrets(config_to_export)
            
        with open(export_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_to_export, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"Configuration exported to: {export_file}")
    
    def _remove_secrets(self, config_dict: Dict[str, Any]) -> None:
        """Remove sensitive information from config"""
        sensitive_keys = [
            'password', 'secret', 'key', 'token', 'api_key', 'secret_key',
            'jwt_secret', 'encryption_key'
        ]
        
        def remove_recursive(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if any(sensitive in key.lower() for sensitive in sensitive_keys):
                        obj[key] = "***REDACTED***"
                    else:
                        remove_recursive(value)
            elif isinstance(obj, list):
                for item in obj:
                    remove_recursive(item)
        
        remove_recursive(config_dict)
    
    async def auto_reload(self, interval: int = 60) -> None:
        """Auto-reload configuration at intervals"""
        while True:
            try:
                await asyncio.sleep(interval)
                
                # Check if config file has been modified
                config_file = self.config_dir / f"config_{self.config.environment.value}.yaml"
                if config_file.exists():
                    file_mtime = datetime.fromtimestamp(config_file.stat().st_mtime)
                    if file_mtime > self._last_reload:
                        self.logger.info("Configuration file changed, reloading...")
                        self.load_config()
                
            except Exception as e:
                self.logger.error(f"Error in auto-reload: {e}")

    def reload_config(self, config_file: str = None) -> None:
        """Reload configuration from file"""
        if config_file is None:
            config_file = self.config_file
        
        if config_file and os.path.exists(config_file):
            self.load_config(config_file)
        else:
            self.logger.warning(f"Config file not found: {config_file}")
            
        # Convert to dict for manipulation
        config_dict = self._safe_dict(self.config)
        
        # Update cache
        self._config_cache = config_dict.copy()
        
        self.logger.info("Configuration reloaded")

    def to_dict(self, config_obj=None) -> Dict[str, Any]:
        """Convert config object to dictionary"""
        if config_obj is None:
            config_obj = self.config
        if config_obj is None:
            raise ConfigurationError("Configuration not loaded.")
        
        return self._safe_dict(config_obj)

    def clone_config(self) -> 'TradingSystemConfig':
        """Create a copy of the current configuration"""
        if self.config is None:
            raise ConfigurationError("Configuration not loaded.")
        
        return TradingSystemConfig(**self._safe_dict(self.config))

    def create_config_template(self, template_file: str = "config_template.yaml") -> None:
        """Create a configuration template file"""
        template_config = TradingSystemConfig()
        
        with open(template_file, 'w', encoding='utf-8') as f:
            yaml.dump(self._safe_dict(template_config), f, default_flow_style=False, allow_unicode=True)

# Global configuration manager instance
config_manager = ConfigurationManager()

# Convenience functions
def load_config(config_file: Optional[str] = None, environment: Optional[Environment] = None) -> TradingSystemConfig:
    """Load configuration"""
    return config_manager.load_config(config_file, environment)

def get_config() -> Optional[TradingSystemConfig]:
    """Get current configuration"""
    return config_manager.config

def get_config_value(key: str, default: Any = None) -> Any:
    """Get configuration value"""
    return config_manager.get_config_value(key, default)

def update_config(key: str, value: Any) -> None:
    """Update configuration value"""
    config_manager.update_config(key, value)

def save_config(config_file: Optional[str] = None) -> None:
    """Save configuration"""
    config_manager.save_config(config_file)

def backup_config(backup_name: Optional[str] = None) -> str:
    """Backup configuration"""
    return config_manager.backup_config(backup_name)

def validate_config() -> Dict[str, Any]:
    """Validate configuration"""
    return config_manager.validate_config()

# Example usage and testing
if __name__ == "__main__":
    print("⚙️ Testing Configuration Management System...")
    
    # Create sample configuration
    config = TradingSystemConfig(
        app_name="Test Trading System",
        environment=Environment.DEVELOPMENT,
        debug=True
    )
    
    # Test configuration manager
    manager = ConfigurationManager()
    
    # Create template
    manager.create_template("development", Environment.DEVELOPMENT)
    
    # Load configuration
    loaded_config = manager.load_config()
    print(f"Loaded config: {loaded_config.app_name}")
    
    # Update configuration
    manager.update_config("trading.max_positions", 20)
    print(f"Updated max_positions: {manager.get_config_value('trading.max_positions')}")
    
    # Validate configuration
    validation_result = manager.validate_config()
    print(f"Configuration valid: {validation_result['valid']}")
    
    # Backup configuration
    backup_file = manager.backup_config()
    print(f"Configuration backed up to: {backup_file}")
    
    # Export configuration
    manager.export_config("config_export.yaml", include_secrets=False)
    print("Configuration exported")
    
    print("✅ Configuration Management System test completed!") 