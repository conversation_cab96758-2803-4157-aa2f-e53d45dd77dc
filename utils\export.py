import pandas as pd
import json
from typing import List, Dict, Any

def export_to_csv(results: List[Dict[str, Any]], path: str) -> None:
    """ذخیره نتایج به فرمت CSV"""
    df = pd.DataFrame(results)
    df.to_csv(path, index=False)

def export_to_json(results: List[Dict[str, Any]], path: str) -> None:
    """ذخیره نتایج به فرمت JSON"""
    with open(path, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

def export_to_excel(results: List[Dict[str, Any]], path: str) -> None:
    """ذخیره نتایج به فرمت Excel"""
    df = pd.DataFrame(results)
    df.to_excel(path, index=False)
