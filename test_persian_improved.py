#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

# تنظیم پروکسی
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:10809'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:10809'

from utils.sentiment_analyzer import SentimentAnalyzer

def test_improved_persian():
    """تست مدل بهبود یافته فارسی"""
    print("🔍 شروع تست مدل بهبود یافته فارسی...")
    
    try:
        # ایجاد analyzer
        analyzer = SentimentAnalyzer()
        print("✅ SentimentAnalyzer با موفقیت ایجاد شد")
        
        # تست متون فارسی با کلمات کلیدی
        test_texts = [
            ("شرکت اپل سود بالایی گزارش داد و سهام آن رشد کرد", "fa"),
            ("بازار سهام امروز افت کرد و سرمایه‌گذاران نگران شدند", "fa"),
            ("شرکت ما درآمد زیادی کسب کرد و سودآوری بالایی داشت", "fa"),
            ("قیمت نفت کاهش یافت و بازار انرژی دچار مشکل شد", "fa"),
            ("اقتصاد کشور رشد مثبتی نشان داد و امیدواری افزایش یافت", "fa"),
            ("بحران مالی باعث افت شدید بازار شد", "fa"),
            ("این یک متن خنثی بدون کلمات احساسی است", "fa")
        ]
        
        results = []
        for text, expected_lang in test_texts:
            try:
                result = analyzer.analyze(text)
                results.append({
                    'text': text,
                    'language': result.language,
                    'label': result.label,
                    'score': result.score,
                    'confidence': result.confidence
                })
                print(f"✅ تحلیل موفق: {text[:40]}... -> {result.label} ({result.score:.3f})")
            except Exception as e:
                print(f"❌ خطا در تحلیل: {text[:40]}... -> {e}")
        
        # خلاصه نتایج
        print("\n📊 خلاصه نتایج بهبود یافته:")
        positive_count = sum(1 for r in results if r['label'] == 'positive')
        negative_count = sum(1 for r in results if r['label'] == 'negative')
        neutral_count = sum(1 for r in results if r['label'] == 'neutral')
        
        print(f"  • مثبت: {positive_count}")
        print(f"  • منفی: {negative_count}")
        print(f"  • خنثی: {neutral_count}")
        
        for result in results:
            print(f"  • {result['text'][:35]}... -> {result['label']} (امتیاز: {result['score']:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطا در تست: {e}")
        return False

if __name__ == "__main__":
    success = test_improved_persian()
    if success:
        print("\n🎉 تست مدل بهبود یافته فارسی با موفقیت تکمیل شد!")
    else:
        print("\n💥 تست با خطا مواجه شد!")
        sys.exit(1) 