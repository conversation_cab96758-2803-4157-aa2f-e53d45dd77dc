#!/usr/bin/env python3
"""
✅ لیست مدل‌های تایید شده و کارآمد HuggingFace
مدل‌هایی که در تست عملکرد داشتند
"""

import requests
import json
import time
from typing import List, Dict, Any

class VerifiedModelManager:
    """مدیر مدل‌های تایید شده"""
    
    # مدل‌های تایید شده که کار می‌کنن
    VERIFIED_WORKING_MODELS = {
        # Sentiment Analysis - تایید شده ✅
        "financial_sentiment": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
        "finbert": "ProsusAI/finbert", 
        "financial_bert": "nlptown/bert-base-multilingual-uncased-sentiment",
        "technical_analysis": "yiyanghkust/finbert-tone",
        
        # Financial Models - تست بیشتر نیاز
        "finbert_esg": "yiyanghkust/finbert-esg",
        "finbert_qa": "deepset/roberta-base-squad2",
        "crypto_sentiment": "ElKulako/cryptobert",
        
        # Time Series - نیاز به test بیشتر
        "time_series_transformer": "huggingface/CodeBERTa-small-v1",
        "financial_transformer": "microsoft/DialoGPT-medium",
        
        # کاندیدهای بیشتر برای test
        "economics_bert": "gtfintechlab/FOMC-RoBERTa",
        "trading_bert": "nlptown/bert-base-multilingual-uncased-sentiment",
        "market_analysis": "microsoft/DialoGPT-small"
    }
    
    # مدل‌های جدید برای کشف
    DISCOVERY_CANDIDATES = [
        # Financial Language Models
        "microsoft/DialoGPT-large",
        "microsoft/DialoGPT-small", 
        "facebook/bart-large",
        "google/flan-t5-base",
        "google/flan-t5-small",
        
        # BERT Variants
        "bert-base-uncased",
        "roberta-base",
        "distilbert-base-uncased",
        "albert-base-v2",
        
        # Financial Specific
        "nlptown/bert-base-multilingual-uncased-sentiment",
        "cardiffnlp/twitter-roberta-base-sentiment-latest",
        "j-hartmann/emotion-english-distilroberta-base",
        
        # Time Series Candidates  
        "facebook/prophet",
        "microsoft/forecasting-tcn",
        
        # Persian/Multilingual
        "HooshvareLab/bert-fa-base-uncased",
        "m3hrdadfi/bert-fa-base-uncased-sentiment-digikala",
        
        # Economics/Finance Specific
        "nlptown/bert-base-multilingual-uncased-sentiment",
        "ProsusAI/finbert-tone",
        "gtfintechlab/FOMC-RoBERTa"
    ]
    
    def __init__(self, hf_token: str = None):
        self.hf_token = hf_token
        self.proxies = self._load_proxy_config()
        self.working_models = []
        self.failed_models = []
        
        print("🔍 شروع کشف مدل‌های کارآمد...")
        
    def _load_proxy_config(self):
        """لود پروکسی"""
        try:
            with open("PROXY.json", "r", encoding="utf-8") as f:
                proxy_config = json.load(f)
            for inbound in proxy_config.get("inbounds", []):
                if inbound.get("protocol") == "http":
                    port = inbound.get("port", 10809)
                    return {"http": f"http://127.0.0.1:{port}", "https": f"http://127.0.0.1:{port}"}
        except:
            pass
        return {"http": "http://127.0.0.1:10809", "https": "http://127.0.0.1:10809"}
    
    def test_model(self, model_id: str, timeout: int = 10) -> Dict[str, Any]:
        """تست یک مدل"""
        
        url = f"https://api-inference.huggingface.co/models/{model_id}"
        headers = {"Content-Type": "application/json"}
        
        if self.hf_token:
            headers["Authorization"] = f"Bearer {self.hf_token}"
        
        # تست با چند نوع payload
        test_payloads = [
            {"inputs": "Apple stock shows strong performance"},  # Sentiment
            {"inputs": [100, 101, 102, 103, 104]},             # Time series
            {"inputs": "What is the market outlook?"},           # Q&A
            {"inputs": {"text": "Financial markets today"}}     # Alternative format
        ]
        
        for i, payload in enumerate(test_payloads):
            try:
                response = requests.post(
                    url, 
                    headers=headers, 
                    json=payload,
                    timeout=timeout,
                    proxies=self.proxies
                )
                
                result = {
                    "model_id": model_id,
                    "status_code": response.status_code,
                    "test_type": i + 1,
                    "success": False,
                    "response_data": None,
                    "error": None
                }
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        result["success"] = True
                        result["response_data"] = data
                        return result
                    except:
                        result["error"] = "Invalid JSON response"
                        
                elif response.status_code == 503:
                    result["error"] = "Model loading"
                    # بعضی وقت‌ها مدل لود می‌شه، پس بعداً کار می‌کنه
                    return result
                    
                else:
                    result["error"] = f"HTTP {response.status_code}: {response.text[:100]}"
                    
            except Exception as e:
                result = {
                    "model_id": model_id,
                    "status_code": None,
                    "test_type": i + 1,
                    "success": False,
                    "error": f"Request failed: {str(e)[:100]}"
                }
                
        return result
    
    def discover_working_models(self) -> Dict[str, List[str]]:
        """کشف مدل‌های کارآمد"""
        
        print("🚀 شروع کشف مدل‌های جدید...")
        
        # ترکیب مدل‌های verified + candidates
        all_models = {**self.VERIFIED_WORKING_MODELS}
        
        for candidate in self.DISCOVERY_CANDIDATES:
            if candidate not in all_models.values():
                model_key = candidate.split('/')[-1].lower().replace('-', '_')
                all_models[model_key] = candidate
        
        working = []
        potentially_working = []  # 503 status = loading
        failed = []
        
        print(f"🔍 تست {len(all_models)} مدل...")
        
        for i, (model_name, model_id) in enumerate(all_models.items(), 1):
            print(f"  [{i:2d}/{len(all_models)}] تست {model_name}...")
            
            result = self.test_model(model_id)
            
            if result["success"]:
                working.append((model_name, model_id))
                print(f"    ✅ کار می‌کند!")
                
            elif result.get("error") == "Model loading":
                potentially_working.append((model_name, model_id))
                print(f"    🟡 در حال لود...")
                
            else:
                failed.append((model_name, model_id, result.get("error", "Unknown")))
                print(f"    ❌ خطا: {result.get('error', 'Unknown')[:50]}")
            
            # کمی استراحت برای جلوگیری از rate limit
            time.sleep(1)
        
        self.working_models = working
        self.potentially_working = potentially_working
        self.failed_models = failed
        
        return {
            "working": working,
            "potentially_working": potentially_working, 
            "failed": failed
        }
    
    def test_working_models(self) -> Dict[str, Any]:
        """تست دقیق‌تر مدل‌های کارآمد"""
        
        if not self.working_models:
            print("❌ هیچ مدل کارآمدی پیدا نشد")
            return {}
        
        print(f"\n🧪 تست دقیق {len(self.working_models)} مدل کارآمد...")
        
        detailed_results = {}
        
        for model_name, model_id in self.working_models:
            print(f"\n📊 تست دقیق {model_name}...")
            
            # تست‌های مختلف
            tests = {
                "sentiment_positive": "Apple stock rallies on strong earnings beat",
                "sentiment_negative": "Market crash wipes out billions in value", 
                "sentiment_neutral": "Trading volume was average today",
                "financial_news": "Fed announces interest rate decision",
                "stock_mention": "AAPL, TSLA, MSFT showing strong performance"
            }
            
            test_results = {}
            
            for test_name, test_input in tests.items():
                result = self.test_model_with_input(model_id, test_input)
                test_results[test_name] = result
                
                if result["success"]:
                    print(f"  ✅ {test_name}: {self._extract_key_info(result['response_data'])}")
                else:
                    print(f"  ❌ {test_name}: {result.get('error', 'Unknown')}")
                
                time.sleep(0.5)
            
            detailed_results[model_name] = {
                "model_id": model_id,
                "tests": test_results,
                "capabilities": self._analyze_capabilities(test_results)
            }
        
        return detailed_results
    
    def test_model_with_input(self, model_id: str, text_input: str) -> Dict[str, Any]:
        """تست مدل با input مشخص"""
        
        url = f"https://api-inference.huggingface.co/models/{model_id}"
        headers = {"Content-Type": "application/json"}
        
        if self.hf_token:
            headers["Authorization"] = f"Bearer {self.hf_token}"
        
        payload = {"inputs": text_input}
        
        try:
            response = requests.post(
                url,
                headers=headers,
                json=payload,
                timeout=15,
                proxies=self.proxies
            )
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "response_data": response.json(),
                    "error": None
                }
            else:
                return {
                    "success": False,
                    "response_data": None,
                    "error": f"HTTP {response.status_code}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "response_data": None,
                "error": str(e)
            }
    
    def _extract_key_info(self, response_data) -> str:
        """استخراج اطلاعات کلیدی از response"""
        
        if not response_data:
            return "No data"
        
        if isinstance(response_data, list) and len(response_data) > 0:
            item = response_data[0]
            if isinstance(item, dict):
                if "label" in item and "score" in item:
                    return f"{item['label']} ({item['score']:.2f})"
                elif "generated_text" in item:
                    return item["generated_text"][:50] + "..."
        
        return str(response_data)[:50] + "..."
    
    def _analyze_capabilities(self, test_results: Dict) -> List[str]:
        """تحلیل قابلیت‌های مدل"""
        
        capabilities = []
        
        # بررسی sentiment analysis
        sentiment_tests = ["sentiment_positive", "sentiment_negative", "sentiment_neutral"]
        if any(test_results.get(test, {}).get("success", False) for test in sentiment_tests):
            capabilities.append("sentiment_analysis")
        
        # بررسی financial understanding
        financial_tests = ["financial_news", "stock_mention"]
        if any(test_results.get(test, {}).get("success", False) for test in financial_tests):
            capabilities.append("financial_analysis")
        
        # بررسی text generation
        for test_name, result in test_results.items():
            if result.get("success") and "generated_text" in str(result.get("response_data", "")):
                capabilities.append("text_generation")
                break
        
        return capabilities
    
    def generate_report(self, detailed_results: Dict) -> str:
        """تولید گزارش جامع"""
        
        report = []
        report.append("# 📊 گزارش کشف مدل‌های HuggingFace")
        report.append("=" * 50)
        report.append(f"📅 تاریخ: {time.strftime('%Y/%m/%d %H:%M:%S')}")
        report.append("")
        
        # خلاصه
        report.append("## 📈 خلاصه نتایج")
        report.append(f"✅ مدل‌های کارآمد: {len(self.working_models)}")
        report.append(f"🟡 مدل‌های در حال لود: {len(getattr(self, 'potentially_working', []))}")
        report.append(f"❌ مدل‌های ناموفق: {len(self.failed_models)}")
        report.append("")
        
        # جزئیات مدل‌های کارآمد
        report.append("## 🚀 مدل‌های کارآمد")
        for model_name, results in detailed_results.items():
            model_id = results["model_id"]
            capabilities = results["capabilities"]
            
            report.append(f"### {model_name}")
            report.append(f"**ID:** `{model_id}`")
            report.append(f"**قابلیت‌ها:** {', '.join(capabilities)}")
            
            # نمونه نتایج
            working_tests = [test for test, result in results["tests"].items() 
                           if result.get("success", False)]
            if working_tests:
                report.append(f"**تست‌های موفق:** {', '.join(working_tests)}")
            
            report.append("")
        
        # کد نمونه
        report.append("## 💻 کد نمونه استفاده")
        report.append("```python")
        report.append("# استفاده از مدل‌های کشف شده")
        report.append("working_models = {")
        for model_name, model_id in self.working_models:
            report.append(f'    "{model_name}": "{model_id}",')
        report.append("}")
        report.append("")
        report.append("# تست مدل")
        if self.working_models:
            first_model = self.working_models[0]
            report.append(f'model_id = working_models["{first_model[0]}"]')
            report.append("# فراخوانی API...")
        report.append("```")
        
        return "\n".join(report)


def main():
    """اجرای کشف مدل‌ها"""
    
    print("🔍 کشف مدل‌های کارآمد HuggingFace برای تحلیل مالی")
    print("=" * 60)
    
    # توکن (اختیاری)
    hf_token = "*************************************"
    
    # کشف
    manager = VerifiedModelManager(hf_token)
    discovery_results = manager.discover_working_models()
    
    print(f"\n📊 نتایج کشف:")
    print(f"✅ {len(discovery_results['working'])} مدل کارآمد")
    print(f"🟡 {len(discovery_results['potentially_working'])} مدل در حال لود")
    print(f"❌ {len(discovery_results['failed'])} مدل ناموفق")
    
    if discovery_results['working']:
        print(f"\n🚀 مدل‌های کارآمد:")
        for model_name, model_id in discovery_results['working']:
            print(f"  ✅ {model_name}: {model_id}")
        
        # تست دقیق
        detailed_results = manager.test_working_models()
        
        # تولید گزارش
        report = manager.generate_report(detailed_results)
        
        # ذخیره گزارش
        with open("verified_models_report.md", "w", encoding="utf-8") as f:
            f.write(report)
        
        print(f"\n📁 گزارش کامل در verified_models_report.md ذخیره شد")
        
        # ذخیره لیست JSON
        final_data = {
            "working_models": dict(discovery_results['working']),
            "detailed_results": detailed_results,
            "discovery_timestamp": time.time()
        }
        
        with open("verified_models.json", "w", encoding="utf-8") as f:
            json.dump(final_data, f, ensure_ascii=False, indent=2)
        
        print(f"📁 داده‌های JSON در verified_models.json ذخیره شد")
    
    else:
        print("\n❌ هیچ مدل کارآمدی پیدا نشد")
        print("💡 توصیه‌ها:")
        print("  - توکن HuggingFace معتبر استفاده کنید")
        print("  - اتصال اینترنت و پروکسی را بررسی کنید")
        print("  - بعداً مجدداً تلاش کنید")

if __name__ == "__main__":
    main() 