{"session_info": {"start_time": "2025-07-17T12:21:11.607393", "end_time": "2025-07-17T12:22:24.004950", "duration_seconds": 72.394589, "config": {"use_memory_optimization": true, "use_enhanced_replay": true, "use_genetic_optimization": true, "use_continual_learning": true, "use_advanced_backtesting": true, "memory_optimization_level": "aggressive", "replay_buffer_size": 10000, "prioritized_replay": true, "genetic_population_size": 20, "genetic_generations": 10, "ewc_lambda": 0.4, "use_experience_replay": true, "auto_backtest_after_training": true, "backtest_validation_split": 0.2}}, "training_results": {"completed": [{"success": true, "performance": 0.8999999999999999, "metrics": {"avg_reward": 0.18, "success_rate": 0.72, "sharpe_ratio": 2.0, "max_drawdown": 0.07}, "episodes_completed": 1000, "best_episode": 850, "advanced_metrics": {"memory_efficiency": 0.85, "training_stability": 0.9700000000000001, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.93589937559322, "sharpe_ratio": 1.2678027094920243, "max_drawdown": 0.12370989083302297, "win_rate": 0.732039076502798}, "model_saved": true, "model_name": "PPO_Agent", "category": "reinforcement_learning", "training_duration": 11.05301, "start_time": "2025-07-17T12:21:12.653027", "end_time": "2025-07-17T12:21:23.706037", "preprocessing_results": {"memory_optimized": false, "genetic_optimization": {"learning_rate": 0.0015486323503977931, "batch_size": 64, "hidden_size": 64}, "continual_learning_ready": true}, "advanced_features_applied": {"memory_optimization": false, "genetic_optimization": true, "enhanced_replay": false, "continual_learning": true}}, {"success": true, "performance": 0.8999999999999999, "metrics": {"avg_reward": 0.18, "success_rate": 0.72, "sharpe_ratio": 2.0, "max_drawdown": 0.07}, "episodes_completed": 1000, "best_episode": 850, "advanced_metrics": {"memory_efficiency": 0.85, "training_stability": 0.9700000000000001, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.95, "sharpe_ratio": 1.6787743882651744, "max_drawdown": 0.08366898900309004, "win_rate": 0.7347756772680503}, "model_saved": true, "model_name": "DQN_Agent", "category": "reinforcement_learning", "training_duration": 11.02837, "start_time": "2025-07-17T12:21:28.800942", "end_time": "2025-07-17T12:21:39.829312", "preprocessing_results": {"memory_optimized": false, "genetic_optimization": {"learning_rate": 0.0026705968450106356, "batch_size": 64, "hidden_size": 64}, "continual_learning_ready": true}, "advanced_features_applied": {"memory_optimization": false, "genetic_optimization": true, "enhanced_replay": false, "continual_learning": true}}, {"success": true, "performance": 0.98, "metrics": {"rmse": 0.02, "mae": 0.013999999999999999, "mape": 1.6, "directional_accuracy": 0.8300000000000001}, "epochs_completed": 50, "best_epoch": 42, "advanced_metrics": {"memory_efficiency": 0.85, "training_stability": 0.9700000000000001, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.95, "sharpe_ratio": 1.8480786337723025, "max_drawdown": 0.06616579317568184, "win_rate": 0.7251931766017127}, "model_saved": true, "model_name": "LSTM_TimeSeries", "category": "timeseries", "training_duration": 11.026505, "start_time": "2025-07-17T12:21:44.854469", "end_time": "2025-07-17T12:21:55.880974", "preprocessing_results": {"memory_optimized": false, "genetic_optimization": {"learning_rate": 0.0014250305377980178, "batch_size": 128, "hidden_size": 64}, "continual_learning_ready": true}, "advanced_features_applied": {"memory_optimization": false, "genetic_optimization": true, "enhanced_replay": false, "continual_learning": true}}, {"success": true, "performance": 0.98, "metrics": {"rmse": 0.02, "mae": 0.013999999999999999, "mape": 1.6, "directional_accuracy": 0.8300000000000001}, "epochs_completed": 50, "best_epoch": 42, "advanced_metrics": {"memory_efficiency": 0.85, "training_stability": 0.9700000000000001, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.95, "sharpe_ratio": 1.7485721541218142, "max_drawdown": 0.09252855670498514, "win_rate": 0.6545633886622567}, "model_saved": true, "model_name": "GRU_TimeSeries", "category": "timeseries", "training_duration": 11.022721, "start_time": "2025-07-17T12:22:00.900317", "end_time": "2025-07-17T12:22:11.923038", "preprocessing_results": {"memory_optimized": false, "genetic_optimization": {"learning_rate": 0.008405560109287594, "batch_size": 32, "hidden_size": 64}, "continual_learning_ready": true}, "advanced_features_applied": {"memory_optimization": false, "genetic_optimization": true, "enhanced_replay": false, "continual_learning": true}}], "failed": [{"success": false, "error": "Prerequisites not met", "details": {"trainer_available": true, "data_available": true, "dependencies_installed": true, "memory_sufficient": false, "ready_to_train": false}, "model_name": "CryptoBERT"}, {"success": false, "error": "Prerequisites not met", "details": {"trainer_available": true, "data_available": true, "dependencies_installed": true, "memory_sufficient": false, "ready_to_train": false}, "model_name": "FinBERT"}], "success_rate": 66.66666666666666}, "brain_analytics": {"decisions_made": 6, "performance_memory": {"PPO_Agent": [{"success": true, "performance": 0.8999999999999999, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 12:21:25.776882"}], "DQN_Agent": [{"success": true, "performance": 0.8999999999999999, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 12:21:41.833168"}], "LSTM_TimeSeries": [{"success": true, "performance": 0.98, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 12:21:57.889012"}], "GRU_TimeSeries": [{"success": true, "performance": 0.98, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 12:22:13.937915"}], "CryptoBERT": [{"success": false, "performance": 0.0, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 12:22:17.961361"}], "FinBERT": [{"success": false, "performance": 0.0, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 12:22:22.000046"}]}, "decision_history": [{"decision": {"action": "train", "model": "RealModelInfo(name='PPO_Agent', category='reinforcement_learning', priority=1, trainer_module='training.train_rl', trainer_class='PearlRLTrainer', config_class='RLTrainingConfig', data_requirements=['trading_environment', 'price_data'], estimated_time_hours=2.5, memory_gb=2.2)", "reasoning": "انتخاب PPO_Agent با امتیاز 1.160", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": true, "timestamp": "2025-07-17T12:21:25.776882"}, {"decision": {"action": "train", "model": "RealModelInfo(name='DQN_Agent', category='reinforcement_learning', priority=1, trainer_module='training.train_rl', trainer_class='PearlRLTrainer', config_class='RLTrainingConfig', data_requirements=['trading_environment', 'price_data'], estimated_time_hours=3.0, memory_gb=2.5)", "reasoning": "انتخاب DQN_Agent با امتیاز 1.133", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": true, "timestamp": "2025-07-17T12:21:41.833168"}, {"decision": {"action": "train", "model": "RealModelInfo(name='LSTM_TimeSeries', category='timeseries', priority=1, trainer_module='training.train_timeseries', trainer_class='PearlTimeSeriesTrainer', config_class='TimeSeriesTrainingConfig', data_requirements=['price_data', 'technical_indicators'], estimated_time_hours=1.0, memory_gb=2.0)", "reasoning": "انتخاب LSTM_TimeSeries با امتیاز 1.100", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": true, "timestamp": "2025-07-17T12:21:57.889012"}, {"decision": {"action": "train", "model": "RealModelInfo(name='GRU_TimeSeries', category='timeseries', priority=1, trainer_module='training.train_timeseries', trainer_class='PearlTimeSeriesTrainer', config_class='TimeSeriesTrainingConfig', data_requirements=['price_data', 'technical_indicators'], estimated_time_hours=0.8, memory_gb=1.8)", "reasoning": "انتخاب GRU_TimeSeries با امتیاز 1.100", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": true, "timestamp": "2025-07-17T12:22:13.937915"}, {"decision": {"action": "train", "model": "RealModelInfo(name='CryptoBERT', category='sentiment', priority=1, trainer_module='training.train_sentiment', trainer_class='PearlSentimentTrainer', config_class='SentimentTrainingConfig', data_requirements=['crypto_news', 'sentiment_labels'], estimated_time_hours=1.5, memory_gb=3.5)", "reasoning": "انتخاب CryptoBERT با امتیاز 1.038", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": false, "timestamp": "2025-07-17T12:22:17.961361"}, {"decision": {"action": "train", "model": "RealModelInfo(name='FinBERT', category='sentiment', priority=1, trainer_module='training.train_sentiment', trainer_class='PearlSentimentTrainer', config_class='SentimentTrainingConfig', data_requirements=['financial_news', 'sentiment_labels'], estimated_time_hours=2.0, memory_gb=4.0)", "reasoning": "انتخاب FinBERT با امتیاز 1.008", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": false, "timestamp": "2025-07-17T12:22:22.000046"}]}, "advanced_features_summary": {"memory_optimization_available": true, "enhanced_replay_available": true, "genetic_evolution_available": true, "continual_learning_available": true, "backtesting_available": true}}