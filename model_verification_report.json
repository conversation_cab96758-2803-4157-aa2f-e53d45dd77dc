{"timestamp": "2025-07-08T20:53:44.174727", "cache_models": 13, "test_results": [{"status": "failed", "model_name": "DistilBERT", "model_path": "distilbert-base-uncased", "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/distilbert-base-uncased/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ProxyError('Unable to connect to proxy', FileNotFoundError(2, 'No such file or directory')))\"), '(Request ID: 6167928d-1aa1-417e-ac8b-bb55d7070922)')"}, {"status": "failed", "model_name": "FinBERT", "model_path": "ProsusAI/finbert", "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/ProsusAI/finbert/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ProxyError('Unable to connect to proxy', FileNotFoundError(2, 'No such file or directory')))\"), '(Request ID: 4ae0575a-f3d1-4bdf-b0aa-e381f1a6b6b2)')"}, {"status": "failed", "model_name": "BERT Tiny", "model_path": "prajjwal1/bert-tiny", "error": "(MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /prajjwal1/bert-tiny/resolve/main/tokenizer_config.json (Caused by ProxyError('Unable to connect to proxy', FileNotFoundError(2, 'No such file or directory')))\"), '(Request ID: 0f46be00-660b-4792-a355-95012d525449)')"}], "benchmark_results": [], "summary": {"total_models": 3, "successful": 0, "failed": 3}}