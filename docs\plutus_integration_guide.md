# راهنمای استفاده از مدل‌های مالی Plutus
# Plutus Financial Models Integration Guide

## مقدمه

این راهنما نحوه استفاده از مدل‌های مالی مختلف بدون نیاز به نصب محلی را توضیح می‌دهد.

## مدل‌های موجود

### 1. PLUTUS - Financial Time Series Forecasting
- **کاربرد**: پیش‌بینی سری زمانی مالی
- **ویژگی‌ها**: 
  - مدل Transformer با +1B پارامتر
  - آموزش روی 100 میلیارد مشاهده مالی
  - پیش‌بینی چند مرحله‌ای

### 2. FinGPT - Financial Forecasting
- **کاربرد**: پیش‌بینی قیمت سهام
- **ویژگی‌ها**:
  - مبتنی بر LLaMA-2
  - آموزش روی داده‌های Dow 30
  - پیش‌بینی با توضیح

### 3. Chronos-Bolt - Time Series Forecasting
- **کاربرد**: پیش‌بینی سری زمانی عمومی
- **ویژگی‌ها**:
  - سرعت بالا (250x سریع‌تر)
  - کم‌حافظه (20x کمتر)
  - پیش‌بینی چندک (quantile)

## نصب و راه‌اندازی

### 1. نصب کتابخانه‌های مورد نیاز

```bash
# نصب از فایل requirements
pip install -r requirements_plutus.txt

# یا نصب دستی
pip install requests transformers huggingface-hub yfinance pandas numpy
```

### 2. دریافت API Keys

#### Hugging Face Token:
1. به https://huggingface.co بروید
2. حساب کاربری ایجاد کنید
3. به Settings > Access Tokens بروید
4. توکن جدید ایجاد کنید

#### Alpha Vantage Key (اختیاری):
1. به https://www.alphavantage.co بروید
2. حساب رایگان ایجاد کنید
3. API Key خود را کپی کنید

### 3. تنظیم متغیرهای محیطی

```bash
# ایجاد فایل .env
echo "HUGGINGFACE_TOKEN=your_token_here" >> .env
echo "ALPHA_VANTAGE_KEY=your_key_here" >> .env
```

## استفاده از API ها

### 1. استفاده از Hugging Face Inference API

```python
from utils.plutus_integration import PlutusFinancialForecaster, PlutusConfig

# تنظیمات
config = PlutusConfig(
    api_key="your_huggingface_token",
    model_name="amazon/chronos-bolt-base",
    use_proxy=True
)

# ایجاد نمونه
forecaster = PlutusFinancialForecaster(config)

# پیش‌بینی
import pandas as pd
price_data = pd.read_csv("data/EURUSD/H1.csv")
predictions = forecaster.predict_price_movement(price_data, "EURUSD")
```

### 2. استفاده از مدل‌های مختلف

```python
from examples.plutus_api_example import FinancialAnalysisIntegration

# ایجاد تحلیل‌گر
analyzer = FinancialAnalysisIntegration()

# تحلیل جامع
results = analyzer.comprehensive_analysis(
    symbol="AAPL",
    news_text="Apple reported strong earnings...",
    alpha_vantage_key="your_key"
)

print(f"Recommendation: {results['recommendation']['action']}")
print(f"Confidence: {results['recommendation']['confidence']:.1%}")
```

## مدل‌های قابل استفاده

### 1. Chronos-Bolt (Amazon)
```python
# استفاده مستقیم از Chronos
from chronos import BaseChronosPipeline
import torch

pipeline = BaseChronosPipeline.from_pretrained(
    "amazon/chronos-bolt-base",
    device_map="cpu",
    torch_dtype=torch.float32
)

# پیش‌بینی
forecast = pipeline.predict(
    context=torch.tensor(price_data["close"].values),
    prediction_length=24
)
```

### 2. FinGPT Forecaster
```python
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel

# بارگذاری مدل
base_model = AutoModelForCausalLM.from_pretrained(
    'meta-llama/Llama-2-7b-chat-hf',
    device_map="auto"
)

model = PeftModel.from_pretrained(
    base_model, 
    'FinGPT/fingpt-forecaster_dow30_llama2-7b_lora'
)
```

### 3. Financial Sentiment Analysis
```python
from transformers import pipeline

# تحلیل احساسات مالی
sentiment_pipeline = pipeline(
    "text-classification",
    model="Sigma/financial-sentiment-analysis"
)

result = sentiment_pipeline("Apple stock shows strong growth potential")
```

## ادغام با سیستم موجود

### 1. اضافه کردن به Real-time Dashboard

```python
# در فایل api/realtime_dashboard.py
from utils.plutus_integration import PlutusIntegrationExample

class RealTimeDashboard:
    def __init__(self):
        # کد موجود...
        self.plutus_integration = PlutusIntegrationExample("your_api_key")
    
    async def get_enhanced_data(self):
        # دریافت داده‌های پیش‌بینی Plutus
        plutus_data = await self.get_plutus_predictions()
        
        # ترکیب با داده‌های موجود
        combined_data = {
            **self.current_data,
            "plutus_predictions": plutus_data
        }
        
        return combined_data
```

### 2. استفاده در استراتژی معاملاتی

```python
class EnhancedTradingStrategy:
    def __init__(self):
        self.plutus_forecaster = PlutusFinancialForecaster(config)
    
    def make_trading_decision(self, symbol, price_data):
        # پیش‌بینی Plutus
        plutus_pred = self.plutus_forecaster.predict_price_movement(
            price_data, symbol
        )
        
        # تحلیل تکنیکال موجود
        technical_signals = self.calculate_technical_indicators(price_data)
        
        # ترکیب سیگنال‌ها
        final_decision = self.combine_signals(plutus_pred, technical_signals)
        
        return final_decision
```

## مثال‌های کاربردی

### 1. پیش‌بینی قیمت روزانه

```python
import pandas as pd
from datetime import datetime, timedelta

# بارگذاری داده‌ها
df = pd.read_csv("data/EURUSD/D1.csv")
df['timestamp'] = pd.to_datetime(df['timestamp'])
df.set_index('timestamp', inplace=True)

# پیش‌بینی
predictions = forecaster.predict_price_movement(
    df.tail(100),  # آخرین 100 روز
    "EURUSD",
    prediction_horizon=5  # پیش‌بینی 5 روز آینده
)

print(f"Predicted trend: {predictions['signals']['trend']}")
print(f"Confidence: {predictions['signals']['confidence']:.1%}")
```

### 2. تحلیل احساسات اخبار

```python
news_headlines = [
    "Federal Reserve raises interest rates by 0.25%",
    "Tech stocks surge on AI optimism",
    "Oil prices decline amid supply concerns"
]

for headline in news_headlines:
    sentiment = analyzer.api_client.use_huggingface_financial_sentiment(headline)
    print(f"News: {headline}")
    print(f"Sentiment: {sentiment}")
    print("-" * 50)
```

### 3. مقایسه مدل‌های مختلف

```python
def compare_models(symbol, price_data):
    results = {}
    
    # Chronos-Bolt
    chronos_pred = use_chronos_model(price_data)
    results['chronos'] = chronos_pred
    
    # FinGPT
    fingpt_pred = use_fingpt_model(price_data, symbol)
    results['fingpt'] = fingpt_pred
    
    # مدل داخلی
    internal_pred = use_internal_model(price_data)
    results['internal'] = internal_pred
    
    return results

# مقایسه
comparison = compare_models("EURUSD", price_data)
for model, pred in comparison.items():
    print(f"{model}: {pred['trend']} ({pred['confidence']:.1%})")
```

## بهینه‌سازی و نکات مهم

### 1. مدیریت Rate Limits

```python
import time
from functools import wraps

def rate_limit(calls_per_minute=60):
    def decorator(func):
        last_called = [0.0]
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = 60.0 / calls_per_minute - elapsed
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            ret = func(*args, **kwargs)
            last_called[0] = time.time()
            return ret
        return wrapper
    return decorator

@rate_limit(calls_per_minute=30)
def make_api_call(data):
    # API call logic
    pass
```

### 2. Cache کردن نتایج

```python
from functools import lru_cache
import hashlib

class CachedForecaster:
    def __init__(self):
        self.cache = {}
    
    def get_cache_key(self, data, symbol):
        data_str = str(data.tail(10).values.tolist())
        return hashlib.md5(f"{symbol}_{data_str}".encode()).hexdigest()
    
    def predict_with_cache(self, data, symbol):
        cache_key = self.get_cache_key(data, symbol)
        
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        result = self.forecaster.predict_price_movement(data, symbol)
        self.cache[cache_key] = result
        
        return result
```

### 3. Error Handling

```python
from tenacity import retry, stop_after_attempt, wait_exponential

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
def robust_api_call(data):
    try:
        return make_api_call(data)
    except requests.exceptions.RequestException as e:
        logger.error(f"API call failed: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise
```

## عیب‌یابی و مسائل رایج

### 1. مشکلات اتصال

```python
# تست اتصال
def test_connection():
    try:
        response = requests.get(
            "https://api-inference.huggingface.co/",
            proxies=PROXY_CONFIG,
            timeout=10
        )
        return response.status_code == 200
    except:
        return False

if not test_connection():
    print("Connection failed. Check proxy settings.")
```

### 2. مشکلات API Token

```python
def validate_token(token):
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(
        "https://huggingface.co/api/whoami",
        headers=headers
    )
    return response.status_code == 200
```

### 3. مشکلات مدل

```python
def check_model_status(model_name):
    url = f"https://api-inference.huggingface.co/models/{model_name}"
    response = requests.get(url)
    
    if response.status_code == 503:
        return "Model is loading"
    elif response.status_code == 200:
        return "Model is ready"
    else:
        return f"Error: {response.status_code}"
```

## نتیجه‌گیری

با استفاده از این راهنما، می‌توانید:

1. **مدل‌های مالی پیشرفته** را بدون نصب محلی استفاده کنید
2. **API های مختلف** را در سیستم معاملاتی خود ادغام کنید
3. **پیش‌بینی‌های دقیق‌تر** با ترکیب چندین مدل داشته باشید
4. **کارایی بالا** با استفاده از تکنیک‌های بهینه‌سازی حفظ کنید

برای سوالات بیشتر یا مشکلات فنی، به مستندات هر مدل مراجعه کنید یا Issues ایجاد کنید. 