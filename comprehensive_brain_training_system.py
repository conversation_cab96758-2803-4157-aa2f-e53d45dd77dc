"""
🧠 Comprehensive Brain Training System for Pearl-3x7B
سیستم جامع آموزش با مغز متفکر برای Pearl-3x7B

این سیستم شامل:
1. آموزش هوشمند تمام 29 مدل
2. مغز متفکر برای تصمیم‌گیری
3. بهینه‌سازی منابع و زمان
4. نظارت پیشرفته و گزارش‌دهی
"""

import os
import sys
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from queue import Queue, PriorityQueue

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from complete_model_inventory import CompleteModelInventory, ModelStatus, TrainingPriority

@dataclass
class TrainingTask:
    """وظیفه آموزش"""
    model_name: str
    priority: int
    estimated_time: int
    memory_requirement: int
    dependencies: List[str]
    trainer_class: str
    config_class: str
    status: str = "pending"
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None

@dataclass
class BrainDecision:
    """تصمیم مغز متفکر"""
    action: str
    model_name: str
    reasoning: str
    confidence: float
    parameters: Dict[str, Any]
    expected_outcome: str
    timestamp: datetime

class ComprehensiveBrain:
    """🧠 مغز متفکر جامع برای آموزش"""
    
    def __init__(self):
        self.training_history = []
        self.performance_metrics = {}
        self.resource_usage = {}
        self.decision_history = []
        self.learning_rate = 0.1
        self.confidence_threshold = 0.6
        
    def analyze_training_situation(self, 
                                 pending_tasks: List[TrainingTask],
                                 running_tasks: List[TrainingTask],
                                 completed_tasks: List[TrainingTask],
                                 available_memory: int,
                                 available_threads: int) -> BrainDecision:
        """تحلیل وضعیت آموزش و تصمیم‌گیری"""
        
        # تحلیل منابع
        memory_usage = sum(task.memory_requirement for task in running_tasks)
        memory_utilization = memory_usage / max(available_memory, 1)
        
        # تحلیل عملکرد
        success_rate = self._calculate_success_rate(completed_tasks)
        avg_completion_time = self._calculate_avg_completion_time(completed_tasks)
        
        # تصمیم‌گیری بر اساس وضعیت
        if memory_utilization > 0.9:
            return self._decide_memory_optimization(pending_tasks, running_tasks)
        elif success_rate < 0.7 and len(completed_tasks) > 3:
            return self._decide_strategy_adjustment(pending_tasks)
        elif len(running_tasks) < available_threads and pending_tasks:
            return self._decide_next_training(pending_tasks, available_memory)
        else:
            return self._decide_monitoring(running_tasks)
    
    def _calculate_success_rate(self, completed_tasks: List[TrainingTask]) -> float:
        """محاسبه نرخ موفقیت"""
        if not completed_tasks:
            return 1.0
        
        successful = sum(1 for task in completed_tasks 
                        if task.result and task.result.get('success', False))
        return successful / len(completed_tasks)
    
    def _calculate_avg_completion_time(self, completed_tasks: List[TrainingTask]) -> float:
        """محاسبه میانگین زمان تکمیل"""
        if not completed_tasks:
            return 0.0
        
        total_time = sum((task.end_time - task.start_time).total_seconds() 
                        for task in completed_tasks 
                        if task.start_time and task.end_time)
        return total_time / len(completed_tasks)
    
    def _decide_memory_optimization(self, pending_tasks: List[TrainingTask], 
                                  running_tasks: List[TrainingTask]) -> BrainDecision:
        """تصمیم بهینه‌سازی حافظه"""
        return BrainDecision(
            action="optimize_memory",
            model_name="system",
            reasoning="حافظه تقریباً پر است - نیاز به بهینه‌سازی",
            confidence=0.9,
            parameters={
                "pause_low_priority": True,
                "enable_memory_cleanup": True,
                "reduce_batch_sizes": True
            },
            expected_outcome="کاهش استفاده از حافظه و بهبود پایداری",
            timestamp=datetime.now()
        )
    
    def _decide_strategy_adjustment(self, pending_tasks: List[TrainingTask]) -> BrainDecision:
        """تصمیم تنظیم استراتژی"""
        return BrainDecision(
            action="adjust_strategy",
            model_name="system",
            reasoning="نرخ موفقیت پایین - نیاز به تغییر استراتژی",
            confidence=0.8,
            parameters={
                "reduce_complexity": True,
                "increase_monitoring": True,
                "adjust_hyperparameters": True
            },
            expected_outcome="بهبود نرخ موفقیت آموزش",
            timestamp=datetime.now()
        )
    
    def _decide_next_training(self, pending_tasks: List[TrainingTask], 
                            available_memory: int) -> BrainDecision:
        """تصمیم آموزش بعدی"""
        # انتخاب بهترین task بر اساس اولویت و منابع
        suitable_tasks = [task for task in pending_tasks 
                         if task.memory_requirement <= available_memory * 0.8]
        
        if not suitable_tasks:
            return self._decide_wait()
        
        # مرتب‌سازی بر اساس اولویت و زمان
        best_task = min(suitable_tasks, 
                       key=lambda t: (t.priority, t.estimated_time))
        
        return BrainDecision(
            action="start_training",
            model_name=best_task.model_name,
            reasoning=f"انتخاب {best_task.model_name} بر اساس اولویت و منابع موجود",
            confidence=0.7,
            parameters={
                "model_name": best_task.model_name,
                "priority": best_task.priority,
                "estimated_time": best_task.estimated_time
            },
            expected_outcome=f"آموزش موفق {best_task.model_name}",
            timestamp=datetime.now()
        )
    
    def _decide_monitoring(self, running_tasks: List[TrainingTask]) -> BrainDecision:
        """تصمیم نظارت"""
        return BrainDecision(
            action="monitor",
            model_name="system",
            reasoning="همه منابع در حال استفاده - ادامه نظارت",
            confidence=0.6,
            parameters={
                "check_progress": True,
                "monitor_resources": True
            },
            expected_outcome="نظارت مداوم بر روند آموزش",
            timestamp=datetime.now()
        )
    
    def _decide_wait(self) -> BrainDecision:
        """تصمیم انتظار"""
        return BrainDecision(
            action="wait",
            model_name="system",
            reasoning="منابع کافی برای آموزش جدید موجود نیست",
            confidence=0.8,
            parameters={"wait_time": 30},
            expected_outcome="انتظار تا آزاد شدن منابع",
            timestamp=datetime.now()
        )
    
    def learn_from_result(self, task: TrainingTask, decision: BrainDecision):
        """یادگیری از نتایج"""
        self.decision_history.append({
            "decision": decision,
            "task": task,
            "outcome": task.result,
            "timestamp": datetime.now()
        })
        
        # تنظیم learning rate بر اساس نتایج
        if task.result and task.result.get('success', False):
            self.learning_rate *= 1.02  # افزایش اعتماد
        else:
            self.learning_rate *= 0.98  # کاهش اعتماد
        
        self.learning_rate = max(0.05, min(0.3, self.learning_rate))

class ComprehensiveTrainingSystem:
    """🎯 سیستم جامع آموزش"""
    
    def __init__(self, max_concurrent_trainings: int = 3, max_memory_mb: int = 16000):
        self.inventory = CompleteModelInventory()
        self.brain = ComprehensiveBrain()
        self.max_concurrent_trainings = max_concurrent_trainings
        self.max_memory_mb = max_memory_mb
        
        # صف‌های وظایف
        self.pending_tasks = PriorityQueue()
        self.running_tasks = []
        self.completed_tasks = []
        self.failed_tasks = []
        
        # Thread management
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent_trainings)
        self.futures = {}
        
        # Statistics
        self.start_time = None
        self.total_training_time = 0
        self.brain_decisions = 0
        
    def initialize_training_queue(self):
        """راه‌اندازی صف آموزش"""
        print("🎯 Initializing comprehensive training queue...")
        
        # ایجاد tasks برای تمام مدل‌های آموزش نداده
        untrained_models = self.inventory.get_untrained_models()
        partially_trained = self.inventory.get_models_by_status(ModelStatus.PARTIALLY_TRAINED)
        
        all_models_to_train = untrained_models + partially_trained
        
        for model_name in all_models_to_train:
            model_info = self.inventory.models[model_name]
            
            task = TrainingTask(
                model_name=model_name,
                priority=model_info.priority.value,
                estimated_time=model_info.training_time_estimate_min,
                memory_requirement=model_info.memory_requirement_mb,
                dependencies=model_info.dependencies,
                trainer_class=model_info.trainer_class,
                config_class=model_info.config_class
            )
            
            # اضافه کردن به صف با اولویت
            self.pending_tasks.put((task.priority, task))
        
        print(f"✅ Training queue initialized with {len(all_models_to_train)} models")
        print(f"🧠 Brain will make decisions for optimal training")
    
    def run_comprehensive_training(self):
        """اجرای آموزش جامع"""
        print("\n🚀 Starting Comprehensive Brain-Guided Training...")
        print("=" * 80)
        
        self.start_time = datetime.now()
        
        try:
            while not self.pending_tasks.empty() or self.running_tasks:
                # دریافت وضعیت فعلی
                pending_list = self._get_pending_tasks_list()
                available_memory = self._calculate_available_memory()
                available_threads = self.max_concurrent_trainings - len(self.running_tasks)
                
                # تصمیم‌گیری توسط مغز
                decision = self.brain.analyze_training_situation(
                    pending_tasks=pending_list,
                    running_tasks=self.running_tasks,
                    completed_tasks=self.completed_tasks,
                    available_memory=available_memory,
                    available_threads=available_threads
                )
                
                self.brain_decisions += 1
                self._execute_brain_decision(decision)
                
                # بررسی وضعیت tasks در حال اجرا
                self._check_running_tasks()
                
                # گزارش پیشرفت
                self._report_progress()
                
                # استراحت کوتاه
                time.sleep(5)
                
        except KeyboardInterrupt:
            print("\n⚠️ Training interrupted by user")
        except Exception as e:
            print(f"\n❌ Training failed: {e}")
        finally:
            self._cleanup_and_summary()
    
    def _get_pending_tasks_list(self) -> List[TrainingTask]:
        """دریافت لیست tasks در انتظار"""
        tasks = []
        temp_queue = PriorityQueue()
        
        while not self.pending_tasks.empty():
            priority, task = self.pending_tasks.get()
            tasks.append(task)
            temp_queue.put((priority, task))
        
        # بازگرداندن tasks به صف
        while not temp_queue.empty():
            self.pending_tasks.put(temp_queue.get())
        
        return tasks
    
    def _calculate_available_memory(self) -> int:
        """محاسبه حافظه موجود"""
        used_memory = sum(task.memory_requirement for task in self.running_tasks)
        return self.max_memory_mb - used_memory
    
    def _execute_brain_decision(self, decision: BrainDecision):
        """اجرای تصمیم مغز"""
        print(f"\n🧠 Brain Decision: {decision.action}")
        print(f"🎯 Target: {decision.model_name}")
        print(f"🔍 Reasoning: {decision.reasoning}")
        print(f"💪 Confidence: {decision.confidence:.2f}")
        
        if decision.action == "start_training":
            self._start_model_training(decision.model_name)
        elif decision.action == "optimize_memory":
            self._optimize_memory()
        elif decision.action == "adjust_strategy":
            self._adjust_strategy()
        elif decision.action == "wait":
            wait_time = decision.parameters.get("wait_time", 30)
            print(f"⏳ Waiting {wait_time} seconds...")
            time.sleep(wait_time)
    
    def _start_model_training(self, model_name: str):
        """شروع آموزش مدل"""
        # پیدا کردن task در صف
        temp_queue = PriorityQueue()
        target_task = None
        
        while not self.pending_tasks.empty():
            priority, task = self.pending_tasks.get()
            if task.model_name == model_name:
                target_task = task
                break
            else:
                temp_queue.put((priority, task))
        
        # بازگرداندن باقی tasks
        while not temp_queue.empty():
            self.pending_tasks.put(temp_queue.get())
        
        if target_task:
            target_task.status = "running"
            target_task.start_time = datetime.now()
            self.running_tasks.append(target_task)
            
            # شروع آموزش در thread جداگانه
            future = self.executor.submit(self._train_model, target_task)
            self.futures[future] = target_task
            
            print(f"🚀 Started training: {model_name}")
    
    def _train_model(self, task: TrainingTask) -> Dict[str, Any]:
        """آموزش مدل (شبیه‌سازی)"""
        try:
            print(f"🔄 Training {task.model_name}...")
            
            # شبیه‌سازی آموزش
            training_time = task.estimated_time * 60  # تبدیل به ثانیه
            time.sleep(min(training_time, 30))  # حداکثر 30 ثانیه برای تست
            
            # شبیه‌سازی نتیجه
            import random
            success = random.random() > 0.2  # 80% احتمال موفقیت
            
            result = {
                'success': success,
                'accuracy': random.uniform(0.7, 0.95) if success else random.uniform(0.3, 0.6),
                'training_time': training_time,
                'memory_used': task.memory_requirement,
                'model_name': task.model_name
            }
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'model_name': task.model_name
            }
    
    def _check_running_tasks(self):
        """بررسی وضعیت tasks در حال اجرا"""
        completed_futures = []
        
        for future in list(self.futures.keys()):
            if future.done():
                task = self.futures[future]
                try:
                    result = future.result()
                    task.result = result
                    task.end_time = datetime.now()
                    task.status = "completed" if result['success'] else "failed"
                    
                    if result['success']:
                        self.completed_tasks.append(task)
                        print(f"✅ Completed: {task.model_name} (Accuracy: {result.get('accuracy', 0):.3f})")
                    else:
                        self.failed_tasks.append(task)
                        print(f"❌ Failed: {task.model_name} - {result.get('error', 'Unknown error')}")
                    
                    # یادگیری مغز از نتیجه
                    decision = BrainDecision(
                        action="training_result",
                        model_name=task.model_name,
                        reasoning="نتیجه آموزش",
                        confidence=1.0,
                        parameters={},
                        expected_outcome="",
                        timestamp=datetime.now()
                    )
                    self.brain.learn_from_result(task, decision)
                    
                except Exception as e:
                    print(f"❌ Error getting result for {task.model_name}: {e}")
                
                completed_futures.append(future)
        
        # پاکسازی completed futures
        for future in completed_futures:
            task = self.futures.pop(future)
            self.running_tasks.remove(task)
    
    def _optimize_memory(self):
        """بهینه‌سازی حافظه"""
        print("🧹 Optimizing memory usage...")
        # شبیه‌سازی بهینه‌سازی حافظه
        time.sleep(2)
        print("✅ Memory optimization completed")
    
    def _adjust_strategy(self):
        """تنظیم استراتژی"""
        print("🔧 Adjusting training strategy...")
        # شبیه‌سازی تنظیم استراتژی
        time.sleep(1)
        print("✅ Strategy adjustment completed")
    
    def _report_progress(self):
        """گزارش پیشرفت"""
        total_models = len(self.inventory.models)
        completed = len(self.completed_tasks)
        failed = len(self.failed_tasks)
        running = len(self.running_tasks)
        pending = self.pending_tasks.qsize()
        
        progress = (completed + failed) / total_models * 100
        
        print(f"\n📊 Progress: {progress:.1f}% | ✅ {completed} | ❌ {failed} | 🔄 {running} | ⏳ {pending}")
        print(f"🧠 Brain Decisions: {self.brain_decisions}")
    
    def _cleanup_and_summary(self):
        """پاکسازی و خلاصه نهایی"""
        self.executor.shutdown(wait=True)
        
        total_time = (datetime.now() - self.start_time).total_seconds()
        
        print(f"\n🎉 COMPREHENSIVE TRAINING COMPLETED!")
        print("=" * 80)
        print(f"⏱️ Total Time: {total_time:.1f}s")
        print(f"✅ Successful: {len(self.completed_tasks)}")
        print(f"❌ Failed: {len(self.failed_tasks)}")
        print(f"🧠 Brain Decisions: {self.brain_decisions}")
        print(f"📈 Success Rate: {len(self.completed_tasks)/(len(self.completed_tasks)+len(self.failed_tasks))*100:.1f}%")
        
        # ذخیره نتایج
        self._save_training_results()
    
    def _save_training_results(self):
        """ذخیره نتایج آموزش"""
        results = {
            "timestamp": datetime.now().isoformat(),
            "total_models": len(self.inventory.models),
            "completed_tasks": [asdict(task) for task in self.completed_tasks],
            "failed_tasks": [asdict(task) for task in self.failed_tasks],
            "brain_decisions": self.brain_decisions,
            "brain_decision_history": self.brain.decision_history
        }
        
        filename = f"comprehensive_training_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, default=str, ensure_ascii=False)
        
        print(f"💾 Results saved to: {filename}")

def main():
    """🧠 اجرای سیستم آموزش جامع"""
    print("🧠 COMPREHENSIVE BRAIN-GUIDED TRAINING SYSTEM")
    print("=" * 80)
    
    try:
        # ایجاد سیستم آموزش
        training_system = ComprehensiveTrainingSystem(
            max_concurrent_trainings=3,
            max_memory_mb=16000
        )
        
        # راه‌اندازی صف آموزش
        training_system.initialize_training_queue()
        
        # اجرای آموزش جامع
        training_system.run_comprehensive_training()
        
    except Exception as e:
        print(f"\n❌ System failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
