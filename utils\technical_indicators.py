
import numpy as np
import pandas as pd
from typing import Union, List, Tuple

class TechnicalIndicators:
    """کلاس جامع اندیکاتورهای تکنیکال"""
    def __init__(self):
        self.indicators_cache = {}
        
    def sma(self, data: Union[pd.Series, np.ndarray], period: int) -> pd.Series:
        """Simple Moving Average"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return data.rolling(window=period).mean()
    
    def ema(self, data: Union[pd.Series, np.ndarray], period: int) -> pd.Series:
        """Exponential Moving Average"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return data.ewm(span=period).mean()
    
    def rsi(self, data: Union[pd.Series, np.ndarray], period: int = 14) -> pd.Series:
        """Relative Strength Index"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def macd(self, data: Union[pd.Series, np.ndarray], 
             fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD Indicator"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        
        ema_fast = self.ema(data, fast)
        ema_slow = self.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = self.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def bollinger_bands(self, data: Union[pd.Series, np.ndarray], 
                       period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Bollinger Bands"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        
        sma = self.sma(data, period)
        std = data.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        
        return upper_band, sma, lower_band
    
    def stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series,
                  k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """Stochastic Oscillator"""
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return k_percent, d_percent
    
    def atr(self, high: pd.Series, low: pd.Series, close: pd.Series, 
            period: int = 14) -> pd.Series:
        """Average True Range"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def adx(self, high: pd.Series, low: pd.Series, close: pd.Series,
            period: int = 14) -> pd.Series:
        """Average Directional Index"""
        plus_dm = high.diff()
        minus_dm = -low.diff()
        
        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm < 0] = 0
        
        tr = self.atr(high, low, close, 1)
        plus_di = 100 * (plus_dm.rolling(window=period).mean() / tr.rolling(window=period).mean())
        minus_di = 100 * (minus_dm.rolling(window=period).mean() / tr.rolling(window=period).mean())
        
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(window=period).mean()
        
        return adx
    
    def cci(self, high: pd.Series, low: pd.Series, close: pd.Series,
            period: int = 20) -> pd.Series:
        """Commodity Channel Index"""
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(window=period).mean()
        mad = typical_price.rolling(window=period).apply(lambda x: np.mean(np.abs(x - np.mean(x))))
        
        cci = (typical_price - sma_tp) / (0.015 * mad)
        return cci
    
    def williams_r(self, high: pd.Series, low: pd.Series, close: pd.Series,
                   period: int = 14) -> pd.Series:
        """Williams %R"""
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
        return williams_r
    
    def momentum(self, data: Union[pd.Series, np.ndarray], period: int = 10) -> pd.Series:
        """Momentum"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return data.diff(period)
    
    def roc(self, data: Union[pd.Series, np.ndarray], period: int = 10) -> pd.Series:
        """Rate of Change"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return ((data - data.shift(period)) / data.shift(period)) * 100
    
    def get_available_indicators(self) -> List[str]:
        """دریافت لیست اندیکاتورهای موجود"""
        return [
            'sma', 'ema', 'rsi', 'macd', 'bollinger_bands',
            'stochastic', 'atr', 'adx', 'cci', 'williams_r',
            'momentum', 'roc'
        ]
    
    def calculate_all(self, data: pd.DataFrame) -> pd.DataFrame:
        """محاسبه همه اندیکاتورها"""
        result = data.copy()
        
        # اندیکاتورهای تک ستونی
        result['sma_20'] = self.sma(data['close'], 20)
        result['ema_20'] = self.ema(data['close'], 20)
        result['rsi_14'] = self.rsi(data['close'], 14)
        result['momentum_10'] = self.momentum(data['close'], 10)
        result['roc_10'] = self.roc(data['close'], 10)
        
        # اندیکاتورهای چند ستونی
        if all(col in data.columns for col in ['high', 'low', 'close']):
            result['atr_14'] = self.atr(data['high'], data['low'], data['close'], 14)
            result['adx_14'] = self.adx(data['high'], data['low'], data['close'], 14)
            result['cci_20'] = self.cci(data['high'], data['low'], data['close'], 20)
            result['williams_r_14'] = self.williams_r(data['high'], data['low'], data['close'], 14)
            
            # MACD
            macd_line, signal_line, histogram = self.macd(data['close'])
            result['macd'] = macd_line
            result['macd_signal'] = signal_line
            result['macd_histogram'] = histogram
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self.bollinger_bands(data['close'])
            result['bb_upper'] = bb_upper
            result['bb_middle'] = bb_middle
            result['bb_lower'] = bb_lower
            
            # Stochastic
            stoch_k, stoch_d = self.stochastic(data['high'], data['low'], data['close'])
            result['stoch_k'] = stoch_k
            result['stoch_d'] = stoch_d
        
        return result
