# مستند جامع: MultiExchangeRouter

## مسئولیت
مسیریابی خودکار بین صرافی‌های مختلف، آربیتراژ، بهینه‌سازی هزینه اجرا، مدیریت نقدینگی و سیستم failover.

## پارامترها
- exchanges: لیست اطلاعات صرافی‌ها
- min_profit_threshold: حداقل سود آربیتراژ
- max_order_size_ratio: حداکثر نسبت اندازه سفارش

## متدهای کلیدی
- initialize: راه‌اندازی اتصالات
- execute_arbitrage: اجرای آربیتراژ
- execute_order: اجرای سفارش
- scan_arbitrage_opportunities: اسکن فرصت‌های آربیتراژ
- get_aggregated_order_book: order book تجمیعی

## نمونه کد
```python
from utils.multi_exchange_routing import MultiExchangeRouter, ExchangeInfo
exchanges = [ExchangeInfo('binance', 'api.binance.com', 0.1, 0.1, 0.001, 1000)]
router = MultiExchangeRouter(exchanges)
await router.initialize()
```

## مدیریت خطا
در صورت خطا در اتصال یا اجرای سفارش، از سیستم failover استفاده می‌شود.

## بهترین شیوه
- همیشه از چند صرافی برای redundancy استفاده کنید.
- فرصت‌های آربیتراژ را سریع اجرا کنید.

## نمودار
- نمودار توزیع حجم، سود آربیتراژ و وضعیت صرافی‌ها قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول در api/realtime_dashboard.py استفاده شده و در تست‌ها و مثال‌ها فعال است.

## وضعیت عملیاتی
✅ عملیاتی و در جریان اصلی پروژه فعال است. 