"""
Multi-Level Intelligent Memory System
سیستم حافظه چندسطحی هوشمند

این سیستم حافظه سلسله‌مراتبی با قابلیت تجمیع الگوها و یادگیری طولانی‌مدت ارائه می‌دهد.
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import threading
import time
import hashlib
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MemoryItem:
    """آیتم حافظه"""
    memory_id: str
    content: Dict[str, Any]
    memory_type: str
    importance: float
    access_count: int
    last_access: datetime
    created_at: datetime
    expiry_time: Optional[datetime]
    tags: List[str]
    related_memories: List[str]

@dataclass
class Pattern:
    """الگوی شناسایی شده"""
    pattern_id: str
    pattern_type: str
    features: Dict[str, float]
    frequency: int
    confidence: float
    first_seen: datetime
    last_seen: datetime
    associated_outcomes: List[Dict]

class ShortTermMemory:
    """حافظه کوتاه‌مدت"""
    
    def __init__(self, capacity: int = 100, retention_hours: int = 24):
        self.capacity = capacity
        self.retention_hours = retention_hours
        self.memories = deque(maxlen=capacity)
        self.memory_index = {}
        self.access_frequency = defaultdict(int)
        
    def store(self, memory_item: MemoryItem):
        """ذخیره در حافظه کوتاه‌مدت"""
        # تنظیم زمان انقضا
        memory_item.expiry_time = datetime.now() + timedelta(hours=self.retention_hours)
        
        # اضافه کردن به حافظه
        self.memories.append(memory_item)
        self.memory_index[memory_item.memory_id] = memory_item
        
        # پاک‌سازی حافظه‌های منقضی شده
        self._cleanup_expired()
    
    def retrieve(self, memory_id: str) -> Optional[MemoryItem]:
        """بازیابی از حافظه کوتاه‌مدت"""
        if memory_id in self.memory_index:
            memory = self.memory_index[memory_id]
            
            # بررسی انقضا
            if memory.expiry_time and datetime.now() > memory.expiry_time:
                self._remove_memory(memory_id)
                return None
            
            # به‌روزرسانی آمار دسترسی
            memory.access_count += 1
            memory.last_access = datetime.now()
            self.access_frequency[memory_id] += 1
            
            return memory
        
        return None
    
    def search(self, query: Dict[str, Any], limit: int = 10) -> List[MemoryItem]:
        """جستجو در حافظه کوتاه‌مدت"""
        results = []
        
        for memory in self.memories:
            if self._matches_query(memory, query):
                results.append(memory)
        
        # مرتب‌سازی بر اساس اهمیت و دسترسی اخیر
        results.sort(key=lambda x: (x.importance, x.access_count), reverse=True)
        return results[:limit]
    
    def get_frequent_patterns(self, min_frequency: int = 5) -> List[Dict]:
        """استخراج الگوهای پرتکرار"""
        patterns = []
        
        # تجمیع بر اساس نوع و محتوا
        pattern_groups = defaultdict(list)
        
        for memory in self.memories:
            key = f"{memory.memory_type}_{memory.content.get('action', 'unknown')}"
            pattern_groups[key].append(memory)
        
        for pattern_key, memories in pattern_groups.items():
            if len(memories) >= min_frequency:
                patterns.append({
                    'pattern_key': pattern_key,
                    'frequency': len(memories),
                    'memories': [m.memory_id for m in memories],
                    'avg_importance': np.mean([m.importance for m in memories])
                })
        
        return patterns
    
    def _matches_query(self, memory: MemoryItem, query: Dict[str, Any]) -> bool:
        """بررسی تطبیق با کوئری"""
        for key, value in query.items():
            if key == 'memory_type':
                if memory.memory_type != value:
                    return False
            elif key == 'tags':
                if not any(tag in memory.tags for tag in value):
                    return False
            elif key == 'min_importance':
                if memory.importance < value:
                    return False
            elif key in memory.content:
                if memory.content[key] != value:
                    return False
        
        return True
    
    def _cleanup_expired(self):
        """پاک‌سازی حافظه‌های منقضی شده"""
        current_time = datetime.now()
        expired_ids = []
        
        for memory in list(self.memories):
            if memory.expiry_time and current_time > memory.expiry_time:
                expired_ids.append(memory.memory_id)
        
        for memory_id in expired_ids:
            self._remove_memory(memory_id)
    
    def _remove_memory(self, memory_id: str):
        """حذف حافظه"""
        if memory_id in self.memory_index:
            memory = self.memory_index[memory_id]
            if memory in self.memories:
                self.memories.remove(memory)
            del self.memory_index[memory_id]
            if memory_id in self.access_frequency:
                del self.access_frequency[memory_id]
    
    def get_stats(self) -> Dict:
        """آمار حافظه کوتاه‌مدت"""
        return {
            'total_memories': len(self.memories),
            'capacity': self.capacity,
            'utilization': len(self.memories) / self.capacity,
            'avg_importance': np.mean([m.importance for m in self.memories]) if self.memories else 0,
            'most_accessed': max(self.access_frequency.items(), key=lambda x: x[1]) if self.access_frequency else None
        }

class MediumTermMemory:
    """حافظه میان‌مدت"""
    
    def __init__(self, db_path: str, retention_days: int = 30):
        self.db_path = db_path
        self.retention_days = retention_days
        self.pattern_detector = PatternDetector()
        self.consolidation_threshold = 10
        
        self._init_database()
    
    def _init_database(self):
        """راه‌اندازی پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS medium_term_memories (
                memory_id TEXT PRIMARY KEY,
                content TEXT,
                memory_type TEXT,
                importance REAL,
                access_count INTEGER,
                last_access TEXT,
                created_at TEXT,
                expiry_time TEXT,
                tags TEXT,
                related_memories TEXT,
                consolidation_score REAL
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS detected_patterns (
                pattern_id TEXT PRIMARY KEY,
                pattern_type TEXT,
                features TEXT,
                frequency INTEGER,
                confidence REAL,
                first_seen TEXT,
                last_seen TEXT,
                associated_outcomes TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def consolidate_from_short_term(self, short_term_memories: List[MemoryItem]):
        """تجمیع از حافظه کوتاه‌مدت"""
        # فیلتر کردن حافظه‌های مهم
        important_memories = [
            m for m in short_term_memories 
            if m.importance > 0.7 or m.access_count > 5
        ]
        
        # تشخیص الگوها
        patterns = self.pattern_detector.detect_patterns(important_memories)
        
        # ذخیره الگوها
        for pattern in patterns:
            self._store_pattern(pattern)
        
        # تجمیع حافظه‌های مرتبط
        consolidated_memories = self._consolidate_related_memories(important_memories)
        
        # ذخیره در حافظه میان‌مدت
        for memory in consolidated_memories:
            self._store_memory(memory)
        
        logger.info(f"Consolidated {len(consolidated_memories)} memories and {len(patterns)} patterns")
    
    def _consolidate_related_memories(self, memories: List[MemoryItem]) -> List[MemoryItem]:
        """تجمیع حافظه‌های مرتبط"""
        consolidated = []
        
        # گروه‌بندی بر اساس شباهت
        memory_groups = self._group_similar_memories(memories)
        
        for group in memory_groups:
            if len(group) >= self.consolidation_threshold:
                # ایجاد حافظه تجمیعی
                consolidated_memory = self._create_consolidated_memory(group)
                consolidated.append(consolidated_memory)
            else:
                # حافظه‌های منفرد
                for memory in group:
                    memory.expiry_time = datetime.now() + timedelta(days=self.retention_days)
                    consolidated.append(memory)
        
        return consolidated
    
    def _group_similar_memories(self, memories: List[MemoryItem]) -> List[List[MemoryItem]]:
        """گروه‌بندی حافظه‌های مشابه"""
        groups = []
        used_memories = set()
        
        for memory in memories:
            if memory.memory_id in used_memories:
                continue
            
            # یافتن حافظه‌های مشابه
            similar_memories = [memory]
            used_memories.add(memory.memory_id)
            
            for other_memory in memories:
                if (other_memory.memory_id not in used_memories and 
                    self._calculate_similarity(memory, other_memory) > 0.7):
                    similar_memories.append(other_memory)
                    used_memories.add(other_memory.memory_id)
            
            groups.append(similar_memories)
        
        return groups
    
    def _calculate_similarity(self, memory1: MemoryItem, memory2: MemoryItem) -> float:
        """محاسبه شباهت بین دو حافظه"""
        similarity = 0.0
        
        # شباهت نوع
        if memory1.memory_type == memory2.memory_type:
            similarity += 0.3
        
        # شباهت تگ‌ها
        common_tags = set(memory1.tags) & set(memory2.tags)
        if memory1.tags and memory2.tags:
            tag_similarity = len(common_tags) / len(set(memory1.tags) | set(memory2.tags))
            similarity += 0.3 * tag_similarity
        
        # شباهت محتوا
        content_similarity = self._calculate_content_similarity(memory1.content, memory2.content)
        similarity += 0.4 * content_similarity
        
        return similarity
    
    def _calculate_content_similarity(self, content1: Dict, content2: Dict) -> float:
        """محاسبه شباهت محتوا"""
        common_keys = set(content1.keys()) & set(content2.keys())
        if not common_keys:
            return 0.0
        
        similarity = 0.0
        for key in common_keys:
            val1, val2 = content1[key], content2[key]
            
            if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                # شباهت عددی
                max_val = max(abs(val1), abs(val2))
                if max_val > 0:
                    similarity += 1.0 - abs(val1 - val2) / max_val
                else:
                    similarity += 1.0
            elif val1 == val2:
                similarity += 1.0
        
        return similarity / len(common_keys)
    
    def _create_consolidated_memory(self, memories: List[MemoryItem]) -> MemoryItem:
        """ایجاد حافظه تجمیعی"""
        # تجمیع محتوا
        consolidated_content = {}
        all_tags = set()
        related_memories = []
        
        for memory in memories:
            # تجمیع محتوا
            for key, value in memory.content.items():
                if key not in consolidated_content:
                    consolidated_content[key] = []
                consolidated_content[key].append(value)
            
            all_tags.update(memory.tags)
            related_memories.append(memory.memory_id)
        
        # محاسبه مقادیر تجمیعی
        for key, values in consolidated_content.items():
            if all(isinstance(v, (int, float)) for v in values):
                consolidated_content[key] = np.mean(values)
            else:
                # انتخاب پرتکرارترین مقدار
                from collections import Counter
                consolidated_content[key] = Counter(values).most_common(1)[0][0]
        
        # ایجاد حافظه جدید
        memory_id = f"consolidated_{int(time.time() * 1000000) % 1000000}"
        
        return MemoryItem(
            memory_id=memory_id,
            content=consolidated_content,
            memory_type="consolidated",
            importance=np.mean([m.importance for m in memories]),
            access_count=sum(m.access_count for m in memories),
            last_access=max(m.last_access for m in memories),
            created_at=datetime.now(),
            expiry_time=datetime.now() + timedelta(days=self.retention_days),
            tags=list(all_tags),
            related_memories=related_memories
        )
    
    def _store_memory(self, memory: MemoryItem):
        """ذخیره حافظه در پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO medium_term_memories 
            (memory_id, content, memory_type, importance, access_count, last_access, 
             created_at, expiry_time, tags, related_memories, consolidation_score)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            memory.memory_id,
            json.dumps(memory.content),
            memory.memory_type,
            memory.importance,
            memory.access_count,
            memory.last_access.isoformat(),
            memory.created_at.isoformat(),
            memory.expiry_time.isoformat() if memory.expiry_time else None,
            json.dumps(memory.tags),
            json.dumps(memory.related_memories),
            0.0  # consolidation_score
        ))
        
        conn.commit()
        conn.close()
    
    def _store_pattern(self, pattern: Pattern):
        """ذخیره الگو"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO detected_patterns 
            (pattern_id, pattern_type, features, frequency, confidence, first_seen, last_seen, associated_outcomes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            pattern.pattern_id,
            pattern.pattern_type,
            json.dumps(pattern.features),
            pattern.frequency,
            pattern.confidence,
            pattern.first_seen.isoformat(),
            pattern.last_seen.isoformat(),
            json.dumps(pattern.associated_outcomes)
        ))
        
        conn.commit()
        conn.close()
    
    def retrieve_patterns(self, pattern_type: str = None, min_confidence: float = 0.5) -> List[Pattern]:
        """بازیابی الگوها"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = "SELECT * FROM detected_patterns WHERE confidence >= ?"
        params = [min_confidence]
        
        if pattern_type:
            query += " AND pattern_type = ?"
            params.append(pattern_type)
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        patterns = []
        for row in rows:
            pattern = Pattern(
                pattern_id=row[0],
                pattern_type=row[1],
                features=json.loads(row[2]),
                frequency=row[3],
                confidence=row[4],
                first_seen=datetime.fromisoformat(row[5]),
                last_seen=datetime.fromisoformat(row[6]),
                associated_outcomes=json.loads(row[7])
            )
            patterns.append(pattern)
        
        conn.close()
        return patterns

class LongTermMemory:
    """حافظه طولانی‌مدت"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.knowledge_base = {}
        self.semantic_index = {}
        
        self._init_database()
    
    def _init_database(self):
        """راه‌اندازی پایگاه داده"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS long_term_knowledge (
                knowledge_id TEXT PRIMARY KEY,
                knowledge_type TEXT,
                title TEXT,
                content TEXT,
                confidence REAL,
                evidence_count INTEGER,
                created_at TEXT,
                last_updated TEXT,
                tags TEXT,
                related_knowledge TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS semantic_relationships (
                relationship_id TEXT PRIMARY KEY,
                source_id TEXT,
                target_id TEXT,
                relationship_type TEXT,
                strength REAL,
                created_at TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def consolidate_from_medium_term(self, patterns: List[Pattern], consolidated_memories: List[MemoryItem]):
        """تجمیع از حافظه میان‌مدت"""
        # تبدیل الگوهای تکراری به دانش
        for pattern in patterns:
            if pattern.frequency > 20 and pattern.confidence > 0.8:
                knowledge = self._pattern_to_knowledge(pattern)
                self._store_knowledge(knowledge)
        
        # تبدیل حافظه‌های مهم به دانش
        important_memories = [
            m for m in consolidated_memories 
            if m.importance > 0.9 and m.access_count > 10
        ]
        
        for memory in important_memories:
            knowledge = self._memory_to_knowledge(memory)
            self._store_knowledge(knowledge)
        
        logger.info(f"Consolidated {len(patterns)} patterns and {len(important_memories)} memories to long-term knowledge")
    
    def _pattern_to_knowledge(self, pattern: Pattern) -> Dict:
        """تبدیل الگو به دانش"""
        return {
            'knowledge_id': f"pattern_knowledge_{pattern.pattern_id}",
            'knowledge_type': 'pattern_knowledge',
            'title': f"Pattern: {pattern.pattern_type}",
            'content': {
                'pattern_features': pattern.features,
                'frequency': pattern.frequency,
                'confidence': pattern.confidence,
                'outcomes': pattern.associated_outcomes
            },
            'confidence': pattern.confidence,
            'evidence_count': pattern.frequency,
            'tags': [pattern.pattern_type, 'pattern', 'behavioral'],
            'related_knowledge': []
        }
    
    def _memory_to_knowledge(self, memory: MemoryItem) -> Dict:
        """تبدیل حافظه به دانش"""
        return {
            'knowledge_id': f"memory_knowledge_{memory.memory_id}",
            'knowledge_type': 'experiential_knowledge',
            'title': f"Experience: {memory.memory_type}",
            'content': memory.content,
            'confidence': memory.importance,
            'evidence_count': memory.access_count,
            'tags': memory.tags + ['experience', 'consolidated'],
            'related_knowledge': memory.related_memories
        }
    
    def _store_knowledge(self, knowledge: Dict):
        """ذخیره دانش"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO long_term_knowledge 
            (knowledge_id, knowledge_type, title, content, confidence, evidence_count, 
             created_at, last_updated, tags, related_knowledge)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            knowledge['knowledge_id'],
            knowledge['knowledge_type'],
            knowledge['title'],
            json.dumps(knowledge['content']),
            knowledge['confidence'],
            knowledge['evidence_count'],
            datetime.now().isoformat(),
            datetime.now().isoformat(),
            json.dumps(knowledge['tags']),
            json.dumps(knowledge['related_knowledge'])
        ))
        
        conn.commit()
        conn.close()
    
    def query_knowledge(self, query: str, knowledge_type: str = None, min_confidence: float = 0.5) -> List[Dict]:
        """پرس‌وجوی دانش"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        sql_query = "SELECT * FROM long_term_knowledge WHERE confidence >= ?"
        params = [min_confidence]
        
        if knowledge_type:
            sql_query += " AND knowledge_type = ?"
            params.append(knowledge_type)
        
        if query:
            sql_query += " AND (title LIKE ? OR content LIKE ?)"
            params.extend([f"%{query}%", f"%{query}%"])
        
        cursor.execute(sql_query, params)
        rows = cursor.fetchall()
        
        knowledge_items = []
        for row in rows:
            knowledge_items.append({
                'knowledge_id': row[0],
                'knowledge_type': row[1],
                'title': row[2],
                'content': json.loads(row[3]),
                'confidence': row[4],
                'evidence_count': row[5],
                'created_at': row[6],
                'last_updated': row[7],
                'tags': json.loads(row[8]),
                'related_knowledge': json.loads(row[9])
            })
        
        conn.close()
        return knowledge_items

class PatternDetector:
    """تشخیص‌دهنده الگو"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.kmeans = KMeans(n_clusters=5, random_state=42)
        
    def detect_patterns(self, memories: List[MemoryItem]) -> List[Pattern]:
        """تشخیص الگوها در حافظه‌ها"""
        if len(memories) < 10:
            return []
        
        patterns = []
        
        # الگوهای زمانی
        temporal_patterns = self._detect_temporal_patterns(memories)
        patterns.extend(temporal_patterns)
        
        # الگوهای محتوایی
        content_patterns = self._detect_content_patterns(memories)
        patterns.extend(content_patterns)
        
        # الگوهای رفتاری
        behavioral_patterns = self._detect_behavioral_patterns(memories)
        patterns.extend(behavioral_patterns)
        
        return patterns
    
    def _detect_temporal_patterns(self, memories: List[MemoryItem]) -> List[Pattern]:
        """تشخیص الگوهای زمانی"""
        patterns = []
        
        # گروه‌بندی بر اساس ساعت روز
        hour_groups = defaultdict(list)
        for memory in memories:
            hour = memory.created_at.hour
            hour_groups[hour].append(memory)
        
        # یافتن ساعات پرتکرار
        for hour, hour_memories in hour_groups.items():
            if len(hour_memories) >= 5:
                pattern = Pattern(
                    pattern_id=f"temporal_hour_{hour}",
                    pattern_type="temporal_hourly",
                    features={"hour": hour, "frequency": len(hour_memories)},
                    frequency=len(hour_memories),
                    confidence=min(1.0, len(hour_memories) / 20.0),
                    first_seen=min(m.created_at for m in hour_memories),
                    last_seen=max(m.created_at for m in hour_memories),
                    associated_outcomes=[]
                )
                patterns.append(pattern)
        
        return patterns
    
    def _detect_content_patterns(self, memories: List[MemoryItem]) -> List[Pattern]:
        """تشخیص الگوهای محتوایی"""
        patterns = []
        
        # تجمیع محتوا بر اساس کلیدها
        content_groups = defaultdict(list)
        
        for memory in memories:
            for key, value in memory.content.items():
                content_groups[key].append((value, memory))
        
        # تشخیص الگوهای مقادیر
        for key, value_memories in content_groups.items():
            if len(value_memories) >= 5:
                values = [vm[0] for vm in value_memories]
                
                if all(isinstance(v, (int, float)) for v in values):
                    # الگوی عددی
                    pattern = Pattern(
                        pattern_id=f"content_numeric_{key}",
                        pattern_type="content_numeric",
                        features={
                            "key": key,
                            "mean": np.mean(values),
                            "std": np.std(values),
                            "min": np.min(values),
                            "max": np.max(values)
                        },
                        frequency=len(values),
                        confidence=0.8,
                        first_seen=min(vm[1].created_at for vm in value_memories),
                        last_seen=max(vm[1].created_at for vm in value_memories),
                        associated_outcomes=[]
                    )
                    patterns.append(pattern)
        
        return patterns
    
    def _detect_behavioral_patterns(self, memories: List[MemoryItem]) -> List[Pattern]:
        """تشخیص الگوهای رفتاری"""
        patterns = []
        
        # الگوهای دسترسی
        access_patterns = defaultdict(list)
        
        for memory in memories:
            access_patterns[memory.memory_type].append(memory.access_count)
        
        for memory_type, access_counts in access_patterns.items():
            if len(access_counts) >= 3:
                pattern = Pattern(
                    pattern_id=f"behavioral_access_{memory_type}",
                    pattern_type="behavioral_access",
                    features={
                        "memory_type": memory_type,
                        "avg_access": np.mean(access_counts),
                        "total_instances": len(access_counts)
                    },
                    frequency=len(access_counts),
                    confidence=0.7,
                    first_seen=datetime.now() - timedelta(days=1),
                    last_seen=datetime.now(),
                    associated_outcomes=[]
                )
                patterns.append(pattern)
        
        return patterns

class IntelligentMemorySystem:
    """سیستم جامع حافظه هوشمند"""
    
    def __init__(self, db_path: str = "intelligent_memory.db"):
        self.db_path = db_path
        self.short_term = ShortTermMemory()
        self.medium_term = MediumTermMemory(db_path)
        self.long_term = LongTermMemory(db_path)
        
        # تنظیمات تجمیع
        self.consolidation_interval = 3600  # یک ساعت
        self.last_consolidation = datetime.now()
        
        # thread برای تجمیع خودکار
        self.consolidation_thread = threading.Thread(target=self._auto_consolidation, daemon=True)
        self.consolidation_thread.start()
        
        logger.info("Intelligent Memory System initialized")
    
    def store_memory(self, content: Dict[str, Any], memory_type: str, 
                    importance: float = 0.5, tags: List[str] = None) -> str:
        """ذخیره حافظه جدید"""
        memory_id = f"memory_{int(time.time() * 1000000) % 1000000}"
        
        memory_item = MemoryItem(
            memory_id=memory_id,
            content=content,
            memory_type=memory_type,
            importance=importance,
            access_count=0,
            last_access=datetime.now(),
            created_at=datetime.now(),
            expiry_time=None,
            tags=tags or [],
            related_memories=[]
        )
        
        self.short_term.store(memory_item)
        logger.info(f"Stored memory {memory_id} in short-term memory")
        
        return memory_id
    
    def retrieve_memory(self, memory_id: str) -> Optional[MemoryItem]:
        """بازیابی حافظه"""
        # جستجو در حافظه کوتاه‌مدت
        memory = self.short_term.retrieve(memory_id)
        if memory:
            return memory
        
        # جستجو در حافظه میان‌مدت
        # (پیاده‌سازی ساده‌شده)
        return None
    
    def search_memories(self, query: Dict[str, Any], search_long_term: bool = False) -> List[MemoryItem]:
        """جستجوی حافظه‌ها"""
        results = []
        
        # جستجو در حافظه کوتاه‌مدت
        short_term_results = self.short_term.search(query)
        results.extend(short_term_results)
        
        # جستجو در حافظه طولانی‌مدت
        if search_long_term:
            knowledge_items = self.long_term.query_knowledge(
                query.get('content', ''),
                query.get('knowledge_type'),
                query.get('min_confidence', 0.5)
            )
            
            # تبدیل دانش به حافظه
            for knowledge in knowledge_items:
                memory_item = MemoryItem(
                    memory_id=knowledge['knowledge_id'],
                    content=knowledge['content'],
                    memory_type=knowledge['knowledge_type'],
                    importance=knowledge['confidence'],
                    access_count=knowledge['evidence_count'],
                    last_access=datetime.now(),
                    created_at=datetime.fromisoformat(knowledge['created_at']),
                    expiry_time=None,
                    tags=knowledge['tags'],
                    related_memories=knowledge['related_knowledge']
                )
                results.append(memory_item)
        
        return results
    
    def get_patterns(self, pattern_type: str = None, min_confidence: float = 0.5) -> List[Pattern]:
        """دریافت الگوهای شناسایی شده"""
        return self.medium_term.retrieve_patterns(pattern_type, min_confidence)
    
    def get_system_stats(self) -> Dict:
        """آمار سیستم حافظه"""
        short_term_stats = self.short_term.get_stats()
        
        return {
            'short_term': short_term_stats,
            'last_consolidation': self.last_consolidation.isoformat(),
            'consolidation_interval': self.consolidation_interval,
            'total_patterns': len(self.get_patterns())
        }
    
    def _auto_consolidation(self):
        """تجمیع خودکار"""
        while True:
            try:
                time.sleep(self.consolidation_interval)
                
                # تجمیع از کوتاه‌مدت به میان‌مدت
                memories = list(self.short_term.memories)
                if len(memories) > 20:
                    self.medium_term.consolidate_from_short_term(memories)
                
                # تجمیع از میان‌مدت به طولانی‌مدت
                patterns = self.medium_term.retrieve_patterns(min_confidence=0.8)
                if patterns:
                    self.long_term.consolidate_from_medium_term(patterns, [])
                
                self.last_consolidation = datetime.now()
                logger.info("Auto-consolidation completed")
                
            except Exception as e:
                logger.error(f"Error in auto-consolidation: {e}")

def main():
    """تست سیستم حافظه هوشمند"""
    print("Multi-Level Intelligent Memory System Test")
    print("=" * 50)
    
    # ایجاد سیستم
    memory_system = IntelligentMemorySystem("test_intelligent_memory.db")
    
    # تست ذخیره حافظه‌ها
    print("Testing memory storage...")
    
    # ذخیره حافظه‌های مختلف
    memories = []
    for i in range(20):
        memory_id = memory_system.store_memory(
            content={
                'action': 'trade',
                'symbol': 'EURUSD',
                'price': 1.1000 + i * 0.0001,
                'volume': 1000 + i * 10,
                'result': 'profit' if i % 2 == 0 else 'loss',
                'amount': (i + 1) * 10
            },
            memory_type='trading_action',
            importance=0.5 + (i % 5) * 0.1,
            tags=['trading', 'forex', 'EURUSD']
        )
        memories.append(memory_id)
    
    print(f"  Stored {len(memories)} memories")
    
    # تست بازیابی
    print("\nTesting memory retrieval...")
    
    first_memory = memory_system.retrieve_memory(memories[0])
    if first_memory:
        print(f"  Retrieved memory: {first_memory.memory_id}")
        print(f"  Content: {first_memory.content}")
        print(f"  Importance: {first_memory.importance}")
    
    # تست جستجو
    print("\nTesting memory search...")
    
    search_results = memory_system.search_memories({
        'memory_type': 'trading_action',
        'tags': ['trading'],
        'min_importance': 0.6
    })
    
    print(f"  Found {len(search_results)} memories matching criteria")
    for result in search_results[:3]:
        print(f"    {result.memory_id}: importance={result.importance:.2f}")
    
    # تست الگوها
    print("\nTesting pattern detection...")
    
    # اضافه کردن حافظه‌های بیشتر برای تشخیص الگو
    for i in range(10):
        memory_system.store_memory(
            content={
                'action': 'analysis',
                'time_of_day': 9 + (i % 3),  # ساعت 9، 10، 11
                'market_condition': 'volatile' if i % 2 == 0 else 'stable',
                'decision': 'buy' if i % 3 == 0 else 'hold'
            },
            memory_type='market_analysis',
            importance=0.7,
            tags=['analysis', 'market']
        )
    
    # انتظار برای پردازش
    time.sleep(2)
    
    patterns = memory_system.get_patterns()
    print(f"  Detected {len(patterns)} patterns:")
    
    for pattern in patterns[:3]:
        print(f"    {pattern.pattern_id}: {pattern.pattern_type} (confidence: {pattern.confidence:.2f})")
    
    # آمار سیستم
    print(f"\n--- System Statistics ---")
    stats = memory_system.get_system_stats()
    
    print(f"Short-term memory:")
    print(f"  Total memories: {stats['short_term']['total_memories']}")
    print(f"  Utilization: {stats['short_term']['utilization']:.2f}")
    print(f"  Average importance: {stats['short_term']['avg_importance']:.2f}")
    
    print(f"System:")
    print(f"  Total patterns: {stats['total_patterns']}")
    print(f"  Last consolidation: {stats['last_consolidation']}")
    
    # تست جستجوی پیشرفته
    print(f"\nTesting advanced search...")
    
    advanced_results = memory_system.search_memories({
        'memory_type': 'trading_action',
        'min_importance': 0.7
    }, search_long_term=True)
    
    print(f"  Advanced search found {len(advanced_results)} results")
    
    print(f"\n✅ Multi-Level Intelligent Memory System test completed!")

if __name__ == "__main__":
    main() 