#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست ساده pipeline اصلاح شده
"""

import os
import sys
import logging
import warnings
import numpy as np
import pandas as pd
from pathlib import Path

# Suppress warnings
warnings.filterwarnings('ignore')

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from utils.anomaly_detection_system import AnomalyDetectionSystem
from utils.technical_indicators import TechnicalIndicators
from core.logger import get_logger

logger = get_logger(__name__)

def test_anomaly_detection():
    """تست سیستم تشخیص ناهنجاری"""
    print("Testing Anomaly Detection System...")
    
    try:
        # Create anomaly detection system
        anomaly_system = AnomalyDetectionSystem()
        
        # Create test data
        test_features = np.random.randn(100, 5)  # 100 samples, 5 features
        
        # Test detect_anomalies method
        anomalies = anomaly_system.detect_anomalies(test_features)
        
        print(f"✅ Anomaly detection test passed. Found {np.sum(anomalies)} anomalies out of {len(anomalies)} samples")
        return True
        
    except Exception as e:
        print(f"❌ Anomaly detection test failed: {e}")
        return False

def test_technical_indicators():
    """تست اندیکاتورهای تکنیکال"""
    print("Testing Technical Indicators...")
    
    try:
        # Create technical indicators
        ti = TechnicalIndicators()
        
        # Create test data
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        prices = np.cumsum(np.random.randn(100) * 0.02) + 100
        
        test_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': prices * 1.01,
            'low': prices * 0.99,
            'close': prices,
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        # Test MACD
        macd_line, signal_line, histogram = ti.macd(test_data['close'])
        print(f"✅ MACD test passed. MACD line shape: {macd_line.shape}")
        
        # Test other indicators
        sma = ti.sma(test_data['close'], 20)
        ema = ti.ema(test_data['close'], 12)
        rsi = ti.rsi(test_data['close'], 14)
        
        print(f"✅ Other indicators test passed. SMA shape: {sma.shape}")
        return True
        
    except Exception as e:
        print(f"❌ Technical indicators test failed: {e}")
        return False

def test_simple_training():
    """تست ساده آموزش"""
    print("Testing Simple Training...")
    
    try:
        # Test sentiment training
        from training.train_sentiment import SentimentTrainer
        trainer = SentimentTrainer()
        
        # Test data preparation
        success = trainer.prepare_data()
        print(f"✅ Sentiment data preparation: {'Success' if success else 'Failed'}")
        
        return success
        
    except Exception as e:
        print(f"❌ Simple training test failed: {e}")
        return False

def main():
    """تابع اصلی"""
    print("="*50)
    print("PIPELINE FIXES TEST")
    print("="*50)
    
    # Run tests
    tests = [
        ("Anomaly Detection", test_anomaly_detection),
        ("Technical Indicators", test_technical_indicators),
        ("Simple Training", test_simple_training)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        results[test_name] = test_func()
    
    # Print summary
    print("\n" + "="*50)
    print("TEST RESULTS SUMMARY")
    print("="*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Pipeline is ready.")
    else:
        print("⚠️  Some tests failed. Check the issues above.")

if __name__ == "__main__":
    main() 