"""
🔧 Utils Package - Refactored for v2.0
پکیج ابزارهای کمکی - بازسازی شده برای نسخه 2.0

این پکیج شامل ابزارها و utilities سازگار با معماری جدید است
"""

# Import from new core.utils
from core.utils import (
    # Performance monitoring
    PerformanceMonitor,
    performance_monitor,
    
    # Memory management
    MemoryManager,
    memory_manager,
    
    # Caching
    CacheManager,
    cache_manager,
    
    # Proxy management
    ProxyManager,
    proxy_manager,
    
    # Decorators
    retry_on_failure,
    timeout_after,
    cache_result,
    monitor_performance,
    
    # Utility functions
    get_system_info,
    cleanup_resources,
    setup_system_monitoring,
    get_resource_usage,
    
    # File utilities
    safe_file_operation,
    ensure_directory,
    get_file_hash
)

# Import from new core modules
from core.config import get_config
from core.logger import get_logger, configure_logging
from core.exceptions import (
    TradingSystemError,
    ModelLoadError,
    NetworkError,
    ResourceError,
    ValidationError
)

# Legacy imports - maintained for backward compatibility
try:
    from .data_utils import (
        DataProcessor,
        DataValidator,
        DataTransformer
    )
except ImportError:
    # Will be created if doesn't exist
    pass

try:
    from .rl_training_utils import (
        RLTrainingUtils,
        ExperienceBuffer,
        TrainingMetrics
    )
except ImportError:
    pass

try:
    from .security import (
        SecurityManager,
        encrypt_data,
        decrypt_data,
        generate_api_key
    )
except ImportError:
    pass

try:
    from .export import (
        ExportManager,
        export_to_json,
        export_to_csv,
        export_to_excel
    )
except ImportError:
    pass

try:
    from .cleanup import (
        CleanupManager,
        cleanup_old_files,
        cleanup_logs,
        cleanup_models
    )
except ImportError:
    pass

# Legacy configuration - redirect to new config
try:
    from .config import Config
    # Redirect to new config system
    Config = get_config
except ImportError:
    Config = get_config

# Legacy logger - redirect to new logger
try:
    from .logger import Logger
    # Redirect to new logger system
    Logger = get_logger
except ImportError:
    Logger = get_logger

# Compatibility layer for old imports
class LegacyAdapter:
    """آداپتور برای حفظ سازگاری با کدهای قدیمی"""
    
    @staticmethod
    def get_data_processor():
        """دریافت data processor"""
        try:
            from .data_utils import DataProcessor
            return DataProcessor()
        except ImportError:
            from .data_processor import DataProcessor
            return DataProcessor()
    
    @staticmethod
    def get_risk_manager():
        """دریافت risk manager"""
        try:
            from .risk_manager import RiskManager
            return RiskManager()
        except ImportError:
            # Create a basic risk manager
            return BasicRiskManager()
    
    @staticmethod
    def get_technical_indicators():
        """دریافت technical indicators"""
        try:
            from .technical_indicators import TechnicalIndicators
            return TechnicalIndicators()
        except ImportError:
            return BasicTechnicalIndicators()

class BasicRiskManager:
    """Risk manager پایه برای backward compatibility"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def calculate_position_size(self, balance, risk_per_trade, stop_loss_pct):
        """محاسبه اندازه پوزیشن"""
        return balance * risk_per_trade / stop_loss_pct
    
    def check_risk_limits(self, position_size, max_position_size):
        """بررسی محدودیت‌های ریسک"""
        return position_size <= max_position_size

class BasicTechnicalIndicators:
    """Technical indicators پایه"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def sma(self, data, period):
        """Simple Moving Average"""
        return data.rolling(window=period).mean()
    
    def ema(self, data, period):
        """Exponential Moving Average"""
        return data.ewm(span=period).mean()
    
    def rsi(self, data, period=14):
        """Relative Strength Index"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

# Create global adapter instance
adapter = LegacyAdapter()

# Export all available functions and classes
__all__ = [
    # Core utilities
    "PerformanceMonitor",
    "performance_monitor",
    "MemoryManager", 
    "memory_manager",
    "CacheManager",
    "cache_manager",
    "ProxyManager",
    "proxy_manager",
    
    # Decorators
    "retry_on_failure",
    "timeout_after",
    "cache_result",
    "monitor_performance",
    
    # Utility functions
    "get_system_info",
    "cleanup_resources",
    "setup_system_monitoring", 
    "get_resource_usage",
    "safe_file_operation",
    "ensure_directory",
    "get_file_hash",
    
    # Core modules
    "get_config",
    "config_manager",
    "get_logger",
    "configure_logging",
    
    # Exceptions
    "TradingSystemError",
    "ModelLoadError",
    "NetworkError",
    "ResourceError",
    "ValidationError",
    
    # Legacy compatibility
    "LegacyAdapter",
    "adapter",
    "BasicRiskManager",
    "BasicTechnicalIndicators",
    
    # Redirect legacy imports
    "Config",
    "Logger"
]

# Version info
__version__ = "2.0.0"
__author__ = "Trading System Team"

# Migration message
import warnings
warnings.warn(
    "The utils package has been refactored for v2.0. "
    "Please update your imports to use the new core modules. "
    "Legacy imports are maintained for backward compatibility but may be removed in future versions.",
    DeprecationWarning,
    stacklevel=2
)

# Initialize system components
def initialize_utils():
    """مقداردهی اولیه ابزارها"""
    logger = get_logger(__name__)
    
    try:
        # Setup system monitoring
        setup_system_monitoring()
        
        # Initialize performance monitoring
        performance_monitor.start()
        
        logger.info("(OK) Utils package initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Utils initialization failed: {e}")
        return False

# Auto-initialize when imported
if __name__ != "__main__":
    initialize_utils()
