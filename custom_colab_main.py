"""
🔥 Pearl-3x7B Custom Colab Main
اسکریپت اصلی آموزش با دیتاهای شما در Google Colab

استفاده از:
- دیتاهای شما در Google Drive
- اندیکاتورهای پیشرفته پروژه
- آموزش واقعی مدل‌ها
"""

import os
import sys
import time
import json
import zipfile
import shutil
from datetime import datetime
from typing import Dict, List, Any, Optional

def main_custom_colab_training():
    """🔥 اجرای کامل آموزش با دیتاهای شما"""
    print("🔥 PEARL-3X7B CUSTOM COLAB TRAINING")
    print("=" * 80)
    print("🎯 Training with YOUR data and YOUR advanced indicators!")
    print("📊 Using data from: /content/drive/MyDrive/project2/data_new")
    print("🔧 Using indicators from your project")
    print()
    
    # Step 1: Load and prepare your data
    print("📋 STEP 1: LOADING YOUR CUSTOM DATA")
    print("=" * 50)
    
    try:
        exec(open('/content/custom_data_trainer.py').read())
        prepared_data = load_and_prepare_your_data()
        
        if not any(prepared_data[key] is not None for key in ['sentiment_data', 'timeseries_data', 'trading_data']):
            print("❌ No usable data found!")
            print("Please check your data directory and file formats.")
            return {"success": False, "error": "No usable data found"}
        
        print("✅ Your data loaded and prepared successfully!")
        
    except Exception as e:
        print(f"❌ Data loading error: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": f"Data loading error: {e}"}
    
    # Step 2: Train models with your data
    print(f"\n📋 STEP 2: TRAINING MODELS WITH YOUR DATA")
    print("=" * 50)
    
    try:
        exec(open('/content/custom_model_trainer.py').read())
        training_results = train_all_models_with_your_data(prepared_data)
        
        print("✅ Model training completed!")
        
    except Exception as e:
        print(f"❌ Model training error: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": f"Model training error: {e}"}
    
    # Step 3: Package results for download
    print(f"\n📋 STEP 3: PACKAGING YOUR TRAINED MODELS")
    print("=" * 50)
    
    try:
        package_results = package_custom_trained_models(training_results, prepared_data)
        
        if package_results['success']:
            print(f"✅ Models packaged successfully!")
            print(f"📦 Download file: {package_results['zip_file']}")
        else:
            print("⚠️ Packaging failed, but models are saved individually")
        
    except Exception as e:
        print(f"❌ Packaging error: {e}")
        package_results = {"success": False, "error": str(e)}
    
    # Final summary
    print(f"\n🎉 CUSTOM DATA TRAINING SESSION COMPLETED!")
    print("=" * 80)
    
    successful_models = sum(1 for r in training_results.values() if r.get('success', False))
    total_models = len(training_results)
    
    print(f"✅ Successfully trained: {successful_models}/{total_models} models")
    
    total_training_time = sum(
        r.get('training_time_hours', 0) 
        for r in training_results.values() 
        if r.get('success', False)
    )
    
    print(f"⏱️ Total training time: {total_training_time:.2f} hours")
    
    if successful_models > 0:
        print(f"\n🏆 MODELS TRAINED WITH YOUR DATA:")
        for name, result in training_results.items():
            if result.get('success'):
                model_name = result.get('model_name', name)
                training_time = result.get('training_time_hours', 0)
                
                # Model-specific metrics
                if 'accuracy' in result:
                    metric = f"Accuracy: {result['accuracy']:.4f}"
                elif 'test_rmse' in result:
                    metric = f"RMSE: {result['test_rmse']:.6f}"
                elif 'final_avg_reward' in result:
                    metric = f"Avg Reward: {result['final_avg_reward']:.2f}"
                else:
                    metric = "Trained successfully"
                
                print(f"   ✅ {model_name}: {training_time:.2f}h - {metric}")
                
                # Show data source info
                if 'features' in result:
                    print(f"      📊 Features: {len(result['features'])}")
                if 'target_column' in result:
                    print(f"      🎯 Target: {result['target_column']}")
    
    # Data summary
    print(f"\n📊 YOUR DATA SUMMARY:")
    data_summary = []
    if prepared_data['sentiment_data'] is not None:
        data_summary.append(f"💭 Sentiment: {len(prepared_data['sentiment_data'])} samples")
    if prepared_data['timeseries_data'] is not None:
        data_summary.append(f"📈 Time Series: {len(prepared_data['timeseries_data'])} records")
    if prepared_data['trading_data'] is not None:
        data_summary.append(f"🤖 Trading: {len(prepared_data['trading_data']['features'])} samples")
    
    for summary in data_summary:
        print(f"   {summary}")
    
    if package_results.get('success'):
        print(f"\n📥 DOWNLOAD YOUR TRAINED MODELS:")
        print(f"   from google.colab import files")
        print(f"   files.download('{package_results['zip_file']}')")
    
    # Save session report
    session_report = {
        "session_id": datetime.now().strftime("%Y%m%d_%H%M%S"),
        "data_source": "/content/drive/MyDrive/project2/data_new",
        "indicators_used": "your_advanced_indicators",
        "prepared_data_summary": {
            "sentiment_samples": len(prepared_data['sentiment_data']) if prepared_data['sentiment_data'] is not None else 0,
            "timeseries_records": len(prepared_data['timeseries_data']) if prepared_data['timeseries_data'] is not None else 0,
            "trading_samples": len(prepared_data['trading_data']['features']) if prepared_data['trading_data'] is not None else 0
        },
        "training_results": training_results,
        "package_results": package_results,
        "summary": {
            "successful_models": successful_models,
            "total_models": total_models,
            "total_training_time_hours": total_training_time,
            "session_completed": True
        }
    }
    
    report_file = f"/content/custom_training_session_{session_report['session_id']}.json"
    with open(report_file, 'w') as f:
        json.dump(session_report, f, indent=2, default=str)
    
    print(f"\n📄 Session report saved: {report_file}")
    
    return session_report

def package_custom_trained_models(training_results: dict, prepared_data: dict) -> dict:
    """📦 بسته‌بندی مدل‌های آموزش دیده با دیتاهای شما"""
    print("📦 Packaging your custom trained models...")
    
    try:
        # Create package directory
        package_dir = "/content/pearl_3x7b_custom_models"
        os.makedirs(package_dir, exist_ok=True)
        
        # Package info
        package_info = {
            "package_name": "Pearl-3x7B Custom Trained Models",
            "created_at": datetime.now().isoformat(),
            "training_type": "custom_data_with_advanced_indicators",
            "data_source": "/content/drive/MyDrive/project2/data_new",
            "indicators_used": "advanced_technical_indicators_from_project",
            "models": [],
            "data_summary": {
                "sentiment_samples": len(prepared_data['sentiment_data']) if prepared_data['sentiment_data'] is not None else 0,
                "timeseries_records": len(prepared_data['timeseries_data']) if prepared_data['timeseries_data'] is not None else 0,
                "trading_samples": len(prepared_data['trading_data']['features']) if prepared_data['trading_data'] is not None else 0
            }
        }
        
        successful_models = 0
        
        for name, result in training_results.items():
            if result.get('success') and 'model_path' in result:
                model_path = result['model_path']
                
                if os.path.exists(model_path):
                    # Copy model to package
                    dest_path = os.path.join(package_dir, f"{name}_custom_model")
                    shutil.copytree(model_path, dest_path, dirs_exist_ok=True)
                    
                    # Add to package info
                    model_info = {
                        "name": result.get('model_name', name),
                        "category": name,
                        "training_time_hours": result.get('training_time_hours', 0),
                        "model_path": dest_path,
                        "data_source": "your_custom_data",
                        "metrics": {}
                    }
                    
                    # Add model-specific metrics and info
                    if 'accuracy' in result:
                        model_info['metrics']['accuracy'] = result['accuracy']
                        model_info['metrics']['f1_score'] = result.get('f1_score', 0)
                        model_info['train_samples'] = result.get('train_samples', 0)
                        model_info['val_samples'] = result.get('val_samples', 0)
                    elif 'test_rmse' in result:
                        model_info['metrics']['test_rmse'] = result['test_rmse']
                        model_info['metrics']['train_rmse'] = result.get('train_rmse', 0)
                        model_info['features'] = result.get('features', [])
                        model_info['target_column'] = result.get('target_column', 'unknown')
                        model_info['train_samples'] = result.get('train_samples', 0)
                        model_info['test_samples'] = result.get('test_samples', 0)
                    elif 'final_avg_reward' in result:
                        model_info['metrics']['final_avg_reward'] = result['final_avg_reward']
                        model_info['metrics']['max_reward'] = result.get('max_reward', 0)
                        model_info['feature_names'] = result.get('feature_names', [])
                        model_info['total_episodes'] = result.get('total_episodes', 0)
                    
                    package_info['models'].append(model_info)
                    successful_models += 1
                    
                    print(f"   ✅ Packaged: {name}")
        
        if successful_models == 0:
            return {"success": False, "error": "No models to package"}
        
        # Save package info
        with open(os.path.join(package_dir, "package_info.json"), 'w') as f:
            json.dump(package_info, f, indent=2)
        
        # Create detailed README
        readme_content = f"""# Pearl-3x7B Custom Trained Models

## 📊 Package Information
- **Created**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Training Type**: Custom data with advanced indicators
- **Data Source**: Your data from `/content/drive/MyDrive/project2/data_new`
- **Indicators**: Advanced technical indicators from your project
- **Total Models**: {successful_models}
- **Training Platform**: Google Colab with GPU

## 📊 Your Data Summary
- **Sentiment Samples**: {package_info['data_summary']['sentiment_samples']:,}
- **Time Series Records**: {package_info['data_summary']['timeseries_records']:,}
- **Trading Samples**: {package_info['data_summary']['trading_samples']:,}

## 🏆 Trained Models

"""
        
        for model_info in package_info['models']:
            readme_content += f"""### {model_info['name']}
- **Category**: {model_info['category']}
- **Training Time**: {model_info['training_time_hours']:.2f} hours
- **Data Source**: {model_info['data_source']}
- **Metrics**: {model_info['metrics']}
- **Path**: `{os.path.basename(model_info['model_path'])}/`

"""
            
            # Add specific details
            if 'features' in model_info:
                readme_content += f"- **Features Used**: {len(model_info['features'])} features\n"
                readme_content += f"- **Target Column**: {model_info.get('target_column', 'N/A')}\n"
            if 'feature_names' in model_info:
                readme_content += f"- **Feature Names**: {len(model_info['feature_names'])} trading features\n"
            
            readme_content += "\n"
        
        readme_content += """
## 🚀 Usage Instructions

1. Extract this package to your Pearl-3x7B project
2. Place models in `models/trained_models/custom_models/`
3. Use the provided model loading functions
4. Test models with your trading system

## 🔧 Model Loading Example

```python
# Load your custom trained model
import torch

# For LSTM model
lstm_data = torch.load('custom_lstm_model/custom_lstm_model.pth')
model_state = lstm_data['model_state_dict']
scaler = lstm_data['scaler']
features = lstm_data['features']

# For FinBERT model
from transformers import AutoTokenizer, AutoModelForSequenceClassification
tokenizer = AutoTokenizer.from_pretrained('custom_finbert_model/')
model = AutoModelForSequenceClassification.from_pretrained('custom_finbert_model/')

# For DQN model
dqn_data = torch.load('custom_dqn_model/custom_dqn_model.pth')
network_state = dqn_data['main_network_state_dict']
feature_names = dqn_data['feature_names']
```

## 📞 Support
These models were trained specifically with your data and advanced indicators.
Performance metrics are based on your actual data validation.
"""
        
        with open(os.path.join(package_dir, "README.md"), 'w') as f:
            f.write(readme_content)
        
        # Create zip file
        zip_filename = f"/content/pearl_3x7b_custom_models_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
        
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, package_dir)
                    zipf.write(file_path, arcname)
        
        print(f"✅ Package created: {zip_filename}")
        print(f"📊 Contains {successful_models} custom trained models")
        
        return {
            "success": True,
            "zip_file": zip_filename,
            "package_dir": package_dir,
            "models_count": successful_models
        }
        
    except Exception as e:
        print(f"❌ Packaging failed: {e}")
        return {"success": False, "error": str(e)}

def show_custom_training_instructions():
    """📋 نمایش دستورالعمل‌های آموزش با دیتاهای شما"""
    print("""
🔥 PEARL-3X7B CUSTOM TRAINING INSTRUCTIONS
==========================================

📋 What This Does:
   • Uses YOUR data from Google Drive
   • Uses YOUR advanced indicators from project
   • Trains models with REAL training procedures
   • Packages YOUR trained models for download

🎯 Models Trained:
   • Custom FinBERT: With your text/news data
   • Custom LSTM: With your time series data
   • Custom DQN: With your trading environment data

📊 Your Data Location:
   • /content/drive/MyDrive/project2/data_new
   • Supports: CSV, PKL, Parquet, JSON files
   • Auto-detects: Price, text, time columns

🔧 Your Indicators Used:
   • utils/technical_indicators.py
   • utils/advanced_technical_indicators.py
   • All your custom indicators automatically applied

⏱️ Expected Time:
   • Data loading: 5-10 minutes
   • FinBERT training: 30-90 minutes
   • LSTM training: 20-60 minutes
   • DQN training: 30-90 minutes
   • Total: 1-4 hours

🔥 GPU Requirements:
   • T4: All models supported
   • V100/A100: Faster training

📋 To Start:
   main_custom_colab_training()

💡 Tips:
   • Make sure Google Drive is mounted
   • Keep your data files in the specified directory
   • Models will use your advanced indicators automatically
   • Download zip file when complete
""")

# Main execution
if __name__ == "__main__":
    try:
        import google.colab
        print("🚀 Running in Google Colab")
        show_custom_training_instructions()
        print("\n" + "="*50)
        print("🔥 Ready for CUSTOM training with YOUR data!")
        print("Execute: main_custom_colab_training()")
    except ImportError:
        print("⚠️ This script is designed for Google Colab")
        main_custom_colab_training()
