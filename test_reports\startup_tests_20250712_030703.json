{"type": "startup_tests", "timestamp": "2025-07-12T03:07:03.582143", "summary": {"total_tests": 4, "passed": 0, "failed": 4, "success_rate": 0.0, "total_duration": 3.5148556232452393}, "results": [{"name": "test_advanced_risk_manager.py", "passed": false, "duration": 0.9091506004333496, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 203\nE       dates = pd.date_range(\"2023-01-01\", periods=100, freq=\"H\")\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:07:00.963433"}, {"name": "test_smart_portfolio_manager.py", "passed": false, "duration": 0.9781420230865479, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 203\nE       dates = pd.date_range(\"2023-01-01\", periods=100, freq=\"H\")\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:07:01.946575"}, {"name": "test_integrated_system.py", "passed": false, "duration": 0.8206651210784912, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 203\nE       dates = pd.date_range(\"2023-01-01\", periods=100, freq=\"H\")\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:07:02.770240"}, {"name": "test_integration_*.py", "passed": false, "duration": 0.8068978786468506, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 203\nE       dates = pd.date_range(\"2023-01-01\", periods=100, freq=\"H\")\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:07:03.580144"}]}