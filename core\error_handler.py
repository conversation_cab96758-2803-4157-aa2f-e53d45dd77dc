#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ Advanced Error Handling System
سیستم پیشرفته مدیریت خطا با Circuit Breaker و Retry Logic
"""

import os
import sys
import time
import json
import traceback
import threading
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from functools import wraps
import logging
from contextlib import contextmanager

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.exceptions import (
    TradingSystemError,
    ConfigurationError,
    ModelError,
    DataError,
    ConnectionError,
    ValidationError,
    ResourceError,
    ModelLoadError,
    NetworkError
)

# Use standard logging instead of custom logger
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class ErrorSeverity(Enum):
    """سطح جدیت خطا"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """دسته‌بندی خطا"""
    SYSTEM = "system"
    NETWORK = "network"
    DATA = "data"
    MODEL = "model"
    TRADING = "trading"
    CONFIGURATION = "configuration"
    VALIDATION = "validation"
    RESOURCE = "resource"

class CircuitBreakerState(Enum):
    """وضعیت Circuit Breaker"""
    CLOSED = "closed"  # عادی
    OPEN = "open"      # خراب
    HALF_OPEN = "half_open"  # در حال بررسی

@dataclass
class ErrorInfo:
    """اطلاعات خطا"""
    error_id: str
    timestamp: datetime
    error_type: str
    message: str
    severity: ErrorSeverity
    category: ErrorCategory
    function_name: str
    module_name: str
    line_number: int
    traceback: str
    context: Dict[str, Any]
    retry_count: int = 0
    resolved: bool = False
    resolution_time: Optional[datetime] = None

@dataclass
class RetryConfig:
    """تنظیمات Retry"""
    max_attempts: int = 3
    initial_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    
class CircuitBreaker:
    """Circuit Breaker Pattern"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED
        self.lock = threading.Lock()
        
    def call(self, func: Callable, *args, **kwargs):
        """فراخوانی تابع با Circuit Breaker"""
        with self.lock:
            if self.state == CircuitBreakerState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitBreakerState.HALF_OPEN
                else:
                    raise TradingSystemError("Circuit breaker is OPEN")
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
            except Exception as e:
                self._on_failure()
                raise
    
    def _should_attempt_reset(self) -> bool:
        """آیا باید تلاش برای reset کند"""
        if self.last_failure_time is None:
            return True
        
        return (datetime.now() - self.last_failure_time).total_seconds() > self.recovery_timeout
    
    def _on_success(self):
        """پس از موفقیت"""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED
    
    def _on_failure(self):
        """پس از شکست"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN

class AdvancedErrorHandler:
    """مدیر پیشرفته خطا"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_history: List[ErrorInfo] = []
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.retry_configs: Dict[str, RetryConfig] = {}
        
        # Statistics
        self.error_stats = {
            "total_errors": 0,
            "errors_by_severity": {severity.value: 0 for severity in ErrorSeverity},
            "errors_by_category": {category.value: 0 for category in ErrorCategory},
            "resolved_errors": 0,
            "critical_errors": 0
        }
        
        # Configuration
        self.max_history_size = 1000
        self.auto_cleanup = True
        self.cleanup_interval = 3600  # 1 hour
        
        # Recovery strategies
        self.recovery_strategies = {}
        
        # Start cleanup thread
        if self.auto_cleanup:
            self._start_cleanup_thread()
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None, 
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    category: ErrorCategory = ErrorCategory.SYSTEM,
                    retry_config: RetryConfig = None) -> ErrorInfo:
        """مدیریت خطا"""
        
        # Extract error information
        error_info = self._extract_error_info(error, context, severity, category)
        
        # Log error
        self._log_error(error_info)
        
        # Store in history
        self._store_error(error_info)
        
        # Update statistics
        self._update_stats(error_info)
        
        # Check for recovery strategy
        self._attempt_recovery(error_info)
        
        # Apply retry logic if configured
        if retry_config and error_info.retry_count < retry_config.max_attempts:
            self._schedule_retry(error_info, retry_config)
        
        return error_info
    
    def _extract_error_info(self, error: Exception, context: Dict[str, Any], 
                           severity: ErrorSeverity, category: ErrorCategory) -> ErrorInfo:
        """استخراج اطلاعات خطا"""
        
        # Get traceback info
        tb = traceback.extract_tb(error.__traceback__)
        last_frame = tb[-1] if tb else None
        
        error_info = ErrorInfo(
            error_id=self._generate_error_id(),
            timestamp=datetime.now(),
            error_type=type(error).__name__,
            message=str(error),
            severity=severity,
            category=category,
            function_name=last_frame.name if last_frame else "unknown",
            module_name=last_frame.filename if last_frame else "unknown",
            line_number=last_frame.lineno if last_frame else 0,
            traceback=traceback.format_exc(),
            context=context or {},
            retry_count=0
        )
        
        return error_info
    
    def _generate_error_id(self) -> str:
        """تولید شناسه خطا"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"ERR_{timestamp}_{len(self.error_history):04d}"
    
    def _log_error(self, error_info: ErrorInfo):
        """ثبت خطا در لاگ"""
        log_level = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }.get(error_info.severity, logging.WARNING)
        
        self.logger.log(
            log_level,
            f"🚨 {error_info.error_id}: {error_info.error_type} - {error_info.message}"
        )
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"💥 CRITICAL ERROR: {error_info.traceback}")
    
    def _store_error(self, error_info: ErrorInfo):
        """ذخیره خطا در تاریخچه"""
        self.error_history.append(error_info)
        
        # Limit history size
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]
    
    def _update_stats(self, error_info: ErrorInfo):
        """بروزرسانی آمار"""
        self.error_stats["total_errors"] += 1
        self.error_stats["errors_by_severity"][error_info.severity.value] += 1
        self.error_stats["errors_by_category"][error_info.category.value] += 1
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            self.error_stats["critical_errors"] += 1
    
    def _attempt_recovery(self, error_info: ErrorInfo):
        """تلاش برای بازیابی"""
        recovery_key = f"{error_info.category.value}_{error_info.error_type}"
        
        if recovery_key in self.recovery_strategies:
            try:
                recovery_func = self.recovery_strategies[recovery_key]
                recovery_func(error_info)
                error_info.resolved = True
                error_info.resolution_time = datetime.now()
                self.error_stats["resolved_errors"] += 1
                self.logger.info(f"✅ Error {error_info.error_id} resolved automatically")
            except Exception as e:
                self.logger.error(f"❌ Recovery failed for {error_info.error_id}: {e}")
    
    def _schedule_retry(self, error_info: ErrorInfo, retry_config: RetryConfig):
        """زمان‌بندی تلاش مجدد"""
        error_info.retry_count += 1
        
        # Calculate delay
        delay = min(
            retry_config.initial_delay * (retry_config.exponential_base ** (error_info.retry_count - 1)),
            retry_config.max_delay
        )
        
        # Add jitter
        if retry_config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        self.logger.info(f"🔄 Scheduling retry for {error_info.error_id} in {delay:.2f}s (attempt {error_info.retry_count})")
    
    def register_recovery_strategy(self, error_category: ErrorCategory, 
                                 error_type: str, recovery_func: Callable):
        """ثبت استراتژی بازیابی"""
        key = f"{error_category.value}_{error_type}"
        self.recovery_strategies[key] = recovery_func
        self.logger.info(f"🔧 Recovery strategy registered for {key}")
    
    def get_circuit_breaker(self, name: str, failure_threshold: int = 5, 
                           recovery_timeout: int = 60) -> CircuitBreaker:
        """دریافت Circuit Breaker"""
        if name not in self.circuit_breakers:
            self.circuit_breakers[name] = CircuitBreaker(
                failure_threshold=failure_threshold,
                recovery_timeout=recovery_timeout
            )
        return self.circuit_breakers[name]
    
    def get_error_stats(self) -> Dict[str, Any]:
        """دریافت آمار خطا"""
        return {
            **self.error_stats,
            "recent_errors": len([e for e in self.error_history 
                                if (datetime.now() - e.timestamp).total_seconds() < 3600]),
            "circuit_breakers": {
                name: {
                    "state": cb.state.value,
                    "failure_count": cb.failure_count,
                    "last_failure": cb.last_failure_time.isoformat() if cb.last_failure_time else None
                } 
                for name, cb in self.circuit_breakers.items()
            }
        }
    
    def get_recent_errors(self, hours: int = 24) -> List[ErrorInfo]:
        """دریافت خطاهای اخیر"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [error for error in self.error_history if error.timestamp >= cutoff_time]
    
    def clear_resolved_errors(self):
        """پاک کردن خطاهای حل شده"""
        initial_count = len(self.error_history)
        self.error_history = [error for error in self.error_history if not error.resolved]
        cleared_count = initial_count - len(self.error_history)
        
        if cleared_count > 0:
            self.logger.info(f"🧹 Cleared {cleared_count} resolved errors")
    
    def export_error_report(self, filename: str = None) -> str:
        """صادرات گزارش خطا"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"error_report_{timestamp}.json"
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "statistics": self.get_error_stats(),
            "recent_errors": [
                {
                    "error_id": error.error_id,
                    "timestamp": error.timestamp.isoformat(),
                    "error_type": error.error_type,
                    "message": error.message,
                    "severity": error.severity.value,
                    "category": error.category.value,
                    "resolved": error.resolved,
                    "retry_count": error.retry_count
                }
                for error in self.get_recent_errors(24)
            ]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📊 Error report exported to: {filename}")
        return filename
    
    def _start_cleanup_thread(self):
        """شروع thread پاک‌سازی"""
        def cleanup_worker():
            while True:
                time.sleep(self.cleanup_interval)
                try:
                    self.clear_resolved_errors()
                except Exception as e:
                    self.logger.error(f"Cleanup thread error: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()

# Global instance
error_handler = AdvancedErrorHandler()

def handle_error(severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                category: ErrorCategory = ErrorCategory.SYSTEM,
                retry_config: RetryConfig = None):
    """Decorator برای مدیریت خطا"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {
                    "function": func.__name__,
                    "args": str(args)[:200],
                    "kwargs": str(kwargs)[:200]
                }
                error_handler.handle_error(e, context, severity, category, retry_config)
                raise
        return wrapper
    return decorator

def with_circuit_breaker(name: str, failure_threshold: int = 5, 
                        recovery_timeout: int = 60):
    """Decorator برای Circuit Breaker"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cb = error_handler.get_circuit_breaker(name, failure_threshold, recovery_timeout)
            return cb.call(func, *args, **kwargs)
        return wrapper
    return decorator

@contextmanager
def error_context(context: Dict[str, Any], severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 category: ErrorCategory = ErrorCategory.SYSTEM):
    """Context manager برای مدیریت خطا"""
    try:
        yield
    except Exception as e:
        error_handler.handle_error(e, context, severity, category)
        raise

# Recovery strategies
def network_error_recovery(error_info: ErrorInfo):
    """استراتژی بازیابی خطای شبکه"""
    time.sleep(2)  # Wait before retry
    # Could implement more sophisticated recovery logic here

def model_error_recovery(error_info: ErrorInfo):
    """استراتژی بازیابی خطای مدل"""
    # Could implement model reloading logic here
    pass

# Register default recovery strategies
error_handler.register_recovery_strategy(ErrorCategory.NETWORK, "ConnectionError", network_error_recovery)
error_handler.register_recovery_strategy(ErrorCategory.MODEL, "ModelError", model_error_recovery)

if __name__ == "__main__":
    """تست سیستم مدیریت خطا"""
    print("⚡ Testing Advanced Error Handler...")
    
    # Test basic error handling
    try:
        raise ValueError("Test error")
    except Exception as e:
        error_info = error_handler.handle_error(e, {"test": "context"}, ErrorSeverity.HIGH, ErrorCategory.SYSTEM)
        print(f"✅ Error handled: {error_info.error_id}")
    
    # Test circuit breaker
    @with_circuit_breaker("test_function", failure_threshold=2)
    def failing_function():
        raise RuntimeError("Always fails")
    
    # Test decorator
    @handle_error(severity=ErrorSeverity.LOW, category=ErrorCategory.TRADING)
    def test_function():
        raise ValueError("Test decorator error")
    
    print("🎉 Error handling system is ready!") 