"""
⚡ Performance Optimizer
بهینه‌سازی عملکرد

این ماژول شامل ابزارهای بهینه‌سازی عملکرد کل سیستم است
"""

import gc
import sys
import time
import psutil
import threading
import multiprocessing
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import asyncio
import cProfile
import pstats
from functools import wraps
from contextlib import contextmanager

from .logger import get_logger
from .utils import MemoryManager, CacheManager

logger = get_logger(__name__)

@dataclass
class PerformanceMetrics:
    """متریک‌های عملکرد"""
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    memory_available: float = 0.0
    disk_io: Dict[str, float] = field(default_factory=dict)
    network_io: Dict[str, float] = field(default_factory=dict)
    thread_count: int = 0
    process_count: int = 0
    gc_collections: Dict[str, int] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class OptimizationResult:
    """نتیجه بهینه‌سازی"""
    optimization_type: str
    before_metrics: PerformanceMetrics
    after_metrics: PerformanceMetrics
    improvement: float
    details: Dict[str, Any] = field(default_factory=dict)

class PerformanceProfiler:
    """پروفایلر عملکرد"""
    
    def __init__(self):
        self.profiles = {}
        self.active_profiles = {}
    
    @contextmanager
    def profile(self, name: str):
        """کنتکست منیجر برای پروفایل کردن"""
        profiler = cProfile.Profile()
        profiler.enable()
        
        try:
            yield profiler
        finally:
            profiler.disable()
            
            # Save profile
            stats = pstats.Stats(profiler)
            self.profiles[name] = {
                'stats': stats,
                'timestamp': datetime.now()
            }
    
    def get_profile_stats(self, name: str) -> Optional[Dict]:
        """دریافت آمار پروفایل"""
        if name not in self.profiles:
            return None
        
        stats = self.profiles[name]['stats']
        
        # Get top functions
        stats.sort_stats('cumulative')
        
        # Convert to dict
        profile_data = {
            'timestamp': self.profiles[name]['timestamp'],
            'total_calls': stats.total_calls,
            'total_time': stats.total_tt,
            'top_functions': []
        }
        
        # Get top 10 functions
        for func, (cc, nc, tt, ct, callers) in list(stats.stats.items())[:10]:
            profile_data['top_functions'].append({
                'function': f"{func[0]}:{func[1]}({func[2]})",
                'calls': cc,
                'total_time': tt,
                'cumulative_time': ct,
                'per_call': tt/cc if cc > 0 else 0
            })
        
        return profile_data
    
    def save_profile(self, name: str, filename: str):
        """ذخیره پروفایل در فایل"""
        if name in self.profiles:
            self.profiles[name]['stats'].dump_stats(filename)

class MemoryOptimizer:
    """بهینه‌سازی حافظه"""
    
    def __init__(self):
        self.memory_manager = MemoryManager()
        self.cache_manager = CacheManager()
        self.optimization_history = []
    
    def optimize_memory(self) -> OptimizationResult:
        """بهینه‌سازی حافظه"""
        logger.info("Starting memory optimization...")
        
        # Get initial metrics
        before_metrics = self._get_memory_metrics()
        
        # Perform optimizations
        optimizations = []
        
        # 1. Garbage collection
        gc_result = self._optimize_garbage_collection()
        optimizations.append(gc_result)
        
        # 2. Cache optimization
        cache_result = self._optimize_cache()
        optimizations.append(cache_result)
        
        # 3. Object pool optimization
        pool_result = self._optimize_object_pools()
        optimizations.append(pool_result)
        
        # Get final metrics
        after_metrics = self._get_memory_metrics()
        
        # Calculate improvement
        memory_saved = before_metrics.memory_usage - after_metrics.memory_usage
        improvement = (memory_saved / before_metrics.memory_usage) * 100 if before_metrics.memory_usage > 0 else 0
        
        result = OptimizationResult(
            optimization_type="memory",
            before_metrics=before_metrics,
            after_metrics=after_metrics,
            improvement=improvement,
            details={
                'memory_saved_mb': memory_saved,
                'optimizations': optimizations
            }
        )
        
        self.optimization_history.append(result)
        logger.info(f"Memory optimization completed. Saved {memory_saved:.1f}MB ({improvement:.1f}%)")
        
        return result
    
    def _get_memory_metrics(self) -> PerformanceMetrics:
        """دریافت متریک‌های حافظه"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return PerformanceMetrics(
            memory_usage=memory_info.rss / 1024 / 1024,  # MB
            memory_available=psutil.virtual_memory().available / 1024 / 1024,  # MB
            gc_collections={
                'gen0': gc.get_stats()[0]['collections'],
                'gen1': gc.get_stats()[1]['collections'],
                'gen2': gc.get_stats()[2]['collections']
            } if hasattr(gc, 'get_stats') else {}
        )
    
    def _optimize_garbage_collection(self) -> Dict[str, Any]:
        """بهینه‌سازی garbage collection"""
        before_objects = len(gc.get_objects())
        
        # Force garbage collection
        collected = gc.collect()
        
        after_objects = len(gc.get_objects())
        
        return {
            'type': 'garbage_collection',
            'objects_before': before_objects,
            'objects_after': after_objects,
            'objects_collected': collected,
            'objects_freed': before_objects - after_objects
        }
    
    def _optimize_cache(self) -> Dict[str, Any]:
        """بهینه‌سازی کش"""
        before_size = self.cache_manager.get_cache_size()
        
        # Clear expired entries
        expired_cleared = self.cache_manager.clear_expired()
        
        # Optimize cache size
        size_optimized = self.cache_manager.optimize_size()
        
        after_size = self.cache_manager.get_cache_size()
        
        return {
            'type': 'cache_optimization',
            'size_before': before_size,
            'size_after': after_size,
            'expired_cleared': expired_cleared,
            'size_optimized': size_optimized
        }
    
    def _optimize_object_pools(self) -> Dict[str, Any]:
        """بهینه‌سازی object pools"""
        # This would optimize object pools if implemented
        return {
            'type': 'object_pool_optimization',
            'pools_optimized': 0,
            'objects_pooled': 0
        }

class CPUOptimizer:
    """بهینه‌سازی CPU"""
    
    def __init__(self):
        self.cpu_count = multiprocessing.cpu_count()
        self.thread_pool = None
        self.process_pool = None
    
    def optimize_cpu_usage(self) -> OptimizationResult:
        """بهینه‌سازی استفاده از CPU"""
        logger.info("Starting CPU optimization...")
        
        # Get initial metrics
        before_metrics = self._get_cpu_metrics()
        
        # Perform optimizations
        optimizations = []
        
        # 1. Thread pool optimization
        thread_result = self._optimize_thread_pools()
        optimizations.append(thread_result)
        
        # 2. Process pool optimization
        process_result = self._optimize_process_pools()
        optimizations.append(process_result)
        
        # 3. Async optimization
        async_result = self._optimize_async_operations()
        optimizations.append(async_result)
        
        # Get final metrics
        after_metrics = self._get_cpu_metrics()
        
        # Calculate improvement
        cpu_improvement = before_metrics.cpu_usage - after_metrics.cpu_usage
        improvement = (cpu_improvement / before_metrics.cpu_usage) * 100 if before_metrics.cpu_usage > 0 else 0
        
        result = OptimizationResult(
            optimization_type="cpu",
            before_metrics=before_metrics,
            after_metrics=after_metrics,
            improvement=improvement,
            details={
                'cpu_improvement': cpu_improvement,
                'optimizations': optimizations
            }
        )
        
        logger.info(f"CPU optimization completed. Improvement: {improvement:.1f}%")
        
        return result
    
    def _get_cpu_metrics(self) -> PerformanceMetrics:
        """دریافت متریک‌های CPU"""
        process = psutil.Process()
        
        return PerformanceMetrics(
            cpu_usage=process.cpu_percent(interval=1),
            thread_count=process.num_threads(),
            process_count=len(psutil.pids())
        )
    
    def _optimize_thread_pools(self) -> Dict[str, Any]:
        """بهینه‌سازی thread pools"""
        optimal_threads = min(self.cpu_count * 2, 32)  # Reasonable limit
        
        if self.thread_pool:
            self.thread_pool.shutdown(wait=True)
        
        self.thread_pool = ThreadPoolExecutor(max_workers=optimal_threads)
        
        return {
            'type': 'thread_pool_optimization',
            'optimal_threads': optimal_threads,
            'cpu_count': self.cpu_count
        }
    
    def _optimize_process_pools(self) -> Dict[str, Any]:
        """بهینه‌سازی process pools"""
        optimal_processes = self.cpu_count
        
        if self.process_pool:
            self.process_pool.shutdown(wait=True)
        
        self.process_pool = ProcessPoolExecutor(max_workers=optimal_processes)
        
        return {
            'type': 'process_pool_optimization',
            'optimal_processes': optimal_processes,
            'cpu_count': self.cpu_count
        }
    
    def _optimize_async_operations(self) -> Dict[str, Any]:
        """بهینه‌سازی عملیات async"""
        # This would optimize async operations if implemented
        return {
            'type': 'async_optimization',
            'optimizations_applied': 0
        }

class IOOptimizer:
    """بهینه‌سازی I/O"""
    
    def __init__(self):
        self.io_metrics = []
    
    def optimize_io_operations(self) -> OptimizationResult:
        """بهینه‌سازی عملیات I/O"""
        logger.info("Starting I/O optimization...")
        
        # Get initial metrics
        before_metrics = self._get_io_metrics()
        
        # Perform optimizations
        optimizations = []
        
        # 1. Disk I/O optimization
        disk_result = self._optimize_disk_io()
        optimizations.append(disk_result)
        
        # 2. Network I/O optimization
        network_result = self._optimize_network_io()
        optimizations.append(network_result)
        
        # 3. Buffer optimization
        buffer_result = self._optimize_buffers()
        optimizations.append(buffer_result)
        
        # Get final metrics
        after_metrics = self._get_io_metrics()
        
        # Calculate improvement
        improvement = 0  # Would calculate based on I/O metrics
        
        result = OptimizationResult(
            optimization_type="io",
            before_metrics=before_metrics,
            after_metrics=after_metrics,
            improvement=improvement,
            details={
                'optimizations': optimizations
            }
        )
        
        logger.info("I/O optimization completed")
        
        return result
    
    def _get_io_metrics(self) -> PerformanceMetrics:
        """دریافت متریک‌های I/O"""
        process = psutil.Process()
        io_counters = process.io_counters()
        
        return PerformanceMetrics(
            disk_io={
                'read_bytes': io_counters.read_bytes,
                'write_bytes': io_counters.write_bytes,
                'read_count': io_counters.read_count,
                'write_count': io_counters.write_count
            },
            network_io={}  # Would add network metrics if available
        )
    
    def _optimize_disk_io(self) -> Dict[str, Any]:
        """بهینه‌سازی disk I/O"""
        return {
            'type': 'disk_io_optimization',
            'optimizations_applied': 0
        }
    
    def _optimize_network_io(self) -> Dict[str, Any]:
        """بهینه‌سازی network I/O"""
        return {
            'type': 'network_io_optimization',
            'optimizations_applied': 0
        }
    
    def _optimize_buffers(self) -> Dict[str, Any]:
        """بهینه‌سازی buffers"""
        return {
            'type': 'buffer_optimization',
            'optimizations_applied': 0
        }

class PerformanceOptimizer:
    """بهینه‌سازی عملکرد اصلی"""
    
    def __init__(self):
        self.memory_optimizer = MemoryOptimizer()
        self.cpu_optimizer = CPUOptimizer()
        self.io_optimizer = IOOptimizer()
        self.profiler = PerformanceProfiler()
        
        self.optimization_history = []
        self.auto_optimization = False
        self.optimization_interval = 300  # 5 minutes
        self.optimization_thread = None
    
    def optimize_system(self) -> Dict[str, OptimizationResult]:
        """بهینه‌سازی کل سیستم"""
        logger.info("Starting system optimization...")
        
        results = {}
        
        # Memory optimization
        try:
            results['memory'] = self.memory_optimizer.optimize_memory()
        except Exception as e:
            logger.error(f"Memory optimization failed: {e}")
        
        # CPU optimization
        try:
            results['cpu'] = self.cpu_optimizer.optimize_cpu_usage()
        except Exception as e:
            logger.error(f"CPU optimization failed: {e}")
        
        # I/O optimization
        try:
            results['io'] = self.io_optimizer.optimize_io_operations()
        except Exception as e:
            logger.error(f"I/O optimization failed: {e}")
        
        # Record optimization
        self.optimization_history.append({
            'timestamp': datetime.now(),
            'results': results
        })
        
        logger.info("System optimization completed")
        
        return results
    
    def start_auto_optimization(self):
        """شروع بهینه‌سازی خودکار"""
        if self.auto_optimization:
            logger.warning("Auto optimization already running")
            return
        
        self.auto_optimization = True
        self.optimization_thread = threading.Thread(target=self._auto_optimization_loop)
        self.optimization_thread.daemon = True
        self.optimization_thread.start()
        
        logger.info("Auto optimization started")
    
    def stop_auto_optimization(self):
        """توقف بهینه‌سازی خودکار"""
        self.auto_optimization = False
        if self.optimization_thread:
            self.optimization_thread.join(timeout=5)
        
        logger.info("Auto optimization stopped")
    
    def _auto_optimization_loop(self):
        """حلقه بهینه‌سازی خودکار"""
        while self.auto_optimization:
            try:
                # Check if optimization is needed
                if self._should_optimize():
                    self.optimize_system()
                
                # Wait for next interval
                time.sleep(self.optimization_interval)
                
            except Exception as e:
                logger.error(f"Auto optimization error: {e}")
                time.sleep(60)  # Wait 1 minute before retry
    
    def _should_optimize(self) -> bool:
        """بررسی نیاز به بهینه‌سازی"""
        process = psutil.Process()
        
        # Check memory usage
        memory_percent = process.memory_percent()
        if memory_percent > 80:  # More than 80% memory usage
            return True
        
        # Check CPU usage
        cpu_percent = process.cpu_percent(interval=1)
        if cpu_percent > 80:  # More than 80% CPU usage
            return True
        
        # Check last optimization time
        if self.optimization_history:
            last_optimization = self.optimization_history[-1]['timestamp']
            if (datetime.now() - last_optimization).total_seconds() > self.optimization_interval:
                return True
        
        return False
    
    def get_performance_report(self) -> Dict[str, Any]:
        """دریافت گزارش عملکرد"""
        process = psutil.Process()
        
        report = {
            'timestamp': datetime.now(),
            'current_metrics': {
                'cpu_usage': process.cpu_percent(interval=1),
                'memory_usage': process.memory_info().rss / 1024 / 1024,
                'memory_percent': process.memory_percent(),
                'thread_count': process.num_threads(),
                'open_files': len(process.open_files()),
                'connections': len(process.connections())
            },
            'optimization_history': self.optimization_history[-10:],  # Last 10 optimizations
            'auto_optimization': self.auto_optimization,
            'optimization_interval': self.optimization_interval
        }
        
        return report

# Global instance
performance_optimizer = PerformanceOptimizer()

# Decorator for performance monitoring
def monitor_performance(func):
    """دکوراتور برای مانیتورینگ عملکرد"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        with performance_optimizer.profiler.profile(func.__name__):
            return func(*args, **kwargs)
    return wrapper

# Context manager for performance monitoring
@contextmanager
def performance_context(name: str):
    """کنتکست منیجر برای مانیتورینگ عملکرد"""
    with performance_optimizer.profiler.profile(name):
        yield

# Performance optimization decorator
def optimize_performance(optimization_type: str = "auto"):
    """دکوراتور برای بهینه‌سازی عملکرد"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Run optimization before function
            if optimization_type == "auto":
                if performance_optimizer._should_optimize():
                    performance_optimizer.optimize_system()
            elif optimization_type == "memory":
                performance_optimizer.memory_optimizer.optimize_memory()
            elif optimization_type == "cpu":
                performance_optimizer.cpu_optimizer.optimize_cpu_usage()
            elif optimization_type == "io":
                performance_optimizer.io_optimizer.optimize_io_operations()
            
            # Run function
            return func(*args, **kwargs)
        return wrapper
    return decorator 