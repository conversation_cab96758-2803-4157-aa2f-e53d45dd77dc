{"type": "startup_tests", "timestamp": "2025-07-10T03:40:37.123626", "summary": {"total_tests": 4, "passed": 0, "failed": 4, "success_rate": 0.0, "total_duration": 160.60360598564148}, "results": [{"name": "test_advanced_risk_manager.py", "passed": false, "duration": 40.571616411209106, "output": "", "error": "list index out of range", "timestamp": "2025-07-10T03:38:37.079920"}, {"name": "test_smart_portfolio_manager.py", "passed": false, "duration": 46.77513790130615, "output": "", "error": "list index out of range", "timestamp": "2025-07-10T03:39:23.857775"}, {"name": "test_integrated_system.py", "passed": false, "duration": 41.10673379898071, "output": "", "error": "list index out of range", "timestamp": "2025-07-10T03:40:04.966508"}, {"name": "test_integration_*.py", "passed": false, "duration": 32.15011787414551, "output": "", "error": "list index out of range", "timestamp": "2025-07-10T03:40:37.121626"}]}