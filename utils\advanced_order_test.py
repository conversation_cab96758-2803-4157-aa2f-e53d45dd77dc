#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 Advanced Order Management Test
"""

import os
import sys
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_advanced_order_management():
    """تست سیستم مدیریت سفارشات پیشرفته"""
    print("🧪 Testing Advanced Order Management System...")
    
    try:
        # Test 1: Import
        print("1️⃣ Testing import...")
        from core.advanced_order_management import (
            AdvancedOrderManager,
            OrderSide,
            OrderType,
            get_order_manager,
            create_market_order,
            create_limit_order
        )
        print("   ✓ Import successful")
        
        # Test 2: Get manager
        print("2️⃣ Testing order manager...")
        order_manager = get_order_manager()
        print("   ✓ Order manager obtained")
        
        # Test 3: Create market order
        print("3️⃣ Testing market order creation...")
        market_order = create_market_order("EURUSD", OrderSide.BUY, 1000)
        print(f"   ✓ Market order created: {market_order.order_id}")
        print(f"   ✓ Order status: {market_order.status.value}")
        print(f"   ✓ Risk check: {market_order.risk_check_result.value}")
        
        # Test 4: Create limit order
        print("4️⃣ Testing limit order creation...")
        limit_order = create_limit_order("GBPUSD", OrderSide.SELL, 500, 1.2750)
        print(f"   ✓ Limit order created: {limit_order.order_id}")
        print(f"   ✓ Order price: {limit_order.price}")
        
        # Test 5: Submit orders
        print("5️⃣ Testing order submission...")
        market_success = order_manager.submit_order(market_order.order_id)
        limit_success = order_manager.submit_order(limit_order.order_id)
        print(f"   ✓ Market order submitted: {market_success}")
        print(f"   ✓ Limit order submitted: {limit_success}")
        
        # Test 6: Get active orders
        print("6️⃣ Testing active orders...")
        active_orders = order_manager.get_active_orders()
        print(f"   ✓ Active orders: {len(active_orders)}")
        for order in active_orders:
            print(f"   ✓ {order.symbol} {order.side.value} {order.quantity}")
        
        # Test 7: Execute market order
        print("7️⃣ Testing order execution...")
        execution_success = order_manager.execute_order(
            market_order.order_id, 
            1.0950,  # execution price
            1000     # full quantity
        )
        print(f"   ✓ Market order executed: {execution_success}")
        
        # Test 8: Check position
        print("8️⃣ Testing position tracking...")
        position = order_manager.get_position("EURUSD")
        if position:
            print(f"   ✓ Position found: {position.symbol}")
            print(f"   ✓ Net quantity: {position.get_net_quantity()}")
            print(f"   ✓ Unrealized PnL: {position.unrealized_pnl}")
        else:
            print("   ❌ No position found")
        
        # Test 9: Statistics
        print("9️⃣ Testing statistics...")
        stats = order_manager.get_statistics()
        print(f"   ✓ Total orders: {stats['total_orders']}")
        print(f"   ✓ Active orders: {stats['active_orders']}")
        print(f"   ✓ Filled orders: {stats['filled_orders']}")
        print(f"   ✓ Total PnL: {stats['total_pnl']}")
        
        # Test 10: Cancel order
        print("🔟 Testing order cancellation...")
        cancel_success = order_manager.cancel_order(limit_order.order_id)
        print(f"   ✓ Order cancelled: {cancel_success}")
        
        print("\n🎉 All Advanced Order Management tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_advanced_order_management()
    print(f"\n✅ Advanced Order Management System is {'ready' if success else 'not ready'}!") 