#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Expert System Analysis
تحلیل تخصصی کامل سیستم
"""

import sys
import os
import json
import importlib.util
from pathlib import Path
import logging

# تنظیم logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

sys.path.insert(0, '.')

class ExpertSystemAnalyzer:
    """تحلیلگر تخصصی سیستم"""
    
    def __init__(self):
        self.results = {
            'data_analysis': {},
            'indicators_analysis': {},
            'models_analysis': {},
            'strategies_analysis': {},
            'backtesting_analysis': {},
            'optimization_analysis': {}
        }
        
        # بارگذاری تنظیمات پروکسی
        self.proxy_config = self._load_proxy_config()
    
    def _load_proxy_config(self):
        """بارگذاری تنظیمات پروکسی"""
        try:
            with open('PROXY.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"Could not load proxy config: {e}")
            return {}
    
    def analyze_data_infrastructure(self):
        """تحلیل زیرساخت داده‌ها"""
        print("🔍 Analyzing Data Infrastructure...")
        
        data_stats = {
            'total_files': 0,
            'pairs': {},
            'timeframes': {},
            'file_sizes': {},
            'data_quality': {}
        }
        
        # بررسی فایل‌های داده
        data_dirs = ['data', 'data_new']
        for data_dir in data_dirs:
            if os.path.exists(data_dir):
                for root, dirs, files in os.walk(data_dir):
                    for file in files:
                        if file.endswith('.csv'):
                            file_path = os.path.join(root, file)
                            data_stats['total_files'] += 1
                            
                            # استخراج جفت ارز
                            pair = os.path.basename(root)
                            if pair not in data_stats['pairs']:
                                data_stats['pairs'][pair] = 0
                            data_stats['pairs'][pair] += 1
                            
                            # استخراج تایم‌فریم
                            timeframe = file.replace('.csv', '')
                            if timeframe not in data_stats['timeframes']:
                                data_stats['timeframes'][timeframe] = 0
                            data_stats['timeframes'][timeframe] += 1
                            
                            # اندازه فایل
                            size = os.path.getsize(file_path)
                            data_stats['file_sizes'][file_path] = size
        
        # تست کیفیت داده
        self._test_data_quality(data_stats)
        
        self.results['data_analysis'] = data_stats
        print(f"✅ Found {data_stats['total_files']} data files")
        print(f"📊 Currency pairs: {len(data_stats['pairs'])}")
        print(f"⏰ Timeframes: {list(data_stats['timeframes'].keys())}")
        
        return data_stats
    
    def _test_data_quality(self, data_stats):
        """تست کیفیت داده‌ها"""
        try:
            import pandas as pd
            
            # تست یک فایل نمونه
            if data_stats['total_files'] > 0:
                sample_file = next(iter(data_stats['file_sizes'].keys()))
                df = pd.read_csv(sample_file)
                
                data_stats['data_quality'] = {
                    'sample_file': sample_file,
                    'rows': len(df),
                    'columns': list(df.columns),
                    'null_values': df.isnull().sum().to_dict(),
                    'data_types': df.dtypes.to_dict()
                }
                
        except Exception as e:
            data_stats['data_quality'] = {'error': str(e)}
    
    def analyze_indicators(self):
        """تحلیل اندیکاتورها"""
        print("🔍 Analyzing Technical Indicators...")
        
        indicators_info = {
            'files_found': [],
            'classes_found': [],
            'methods_found': [],
            'working_indicators': [],
            'broken_indicators': []
        }
        
        # بررسی فایل‌های اندیکاتور
        utils_files = []
        if os.path.exists('utils'):
            for file in os.listdir('utils'):
                if any(keyword in file.lower() for keyword in ['indicator', 'technical', 'ta']):
                    utils_files.append(f'utils/{file}')
                    indicators_info['files_found'].append(file)
        
        # تحلیل تخصصی هر فایل
        for file_path in utils_files:
            if file_path.endswith('.py'):
                self._analyze_indicator_file(file_path, indicators_info)
        
        # تست عملکرد اندیکاتورها
        self._test_indicators_functionality(indicators_info)
        
        self.results['indicators_analysis'] = indicators_info
        print(f"📈 Found {len(indicators_info['files_found'])} indicator files")
        print(f"🔧 Working indicators: {len(indicators_info['working_indicators'])}")
        print(f"❌ Broken indicators: {len(indicators_info['broken_indicators'])}")
        
        return indicators_info
    
    def _analyze_indicator_file(self, file_path, indicators_info):
        """تحلیل فایل اندیکاتور"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # جستجوی کلاس‌ها
            import re
            classes = re.findall(r'class\s+(\w+)', content)
            indicators_info['classes_found'].extend(classes)
            
            # جستجوی متدها
            methods = re.findall(r'def\s+(\w+)', content)
            indicators_info['methods_found'].extend(methods)
            
        except Exception as e:
            logger.error(f"Error analyzing {file_path}: {e}")
    
    def _test_indicators_functionality(self, indicators_info):
        """تست عملکرد اندیکاتورها"""
        # تست TechnicalIndicators
        try:
            from utils.technical_indicators import TechnicalIndicators
            ti = TechnicalIndicators()
            
            # تست متدهای مختلف
            test_methods = ['sma', 'ema', 'rsi', 'macd', 'bollinger_bands']
            import numpy as np
            test_data = np.random.random(100)
            
            for method in test_methods:
                if hasattr(ti, method):
                    try:
                        getattr(ti, method)(test_data)
                        indicators_info['working_indicators'].append(method)
                    except Exception as e:
                        indicators_info['broken_indicators'].append(f"{method}: {str(e)}")
                        
        except ImportError as e:
            indicators_info['broken_indicators'].append(f"TechnicalIndicators import failed: {e}")
    
    def analyze_models(self):
        """تحلیل مدل‌های AI"""
        print("🔍 Analyzing AI Models...")
        
        models_info = {
            'local_models': [],
            'downloadable_models': [],
            'proxy_models': [],
            'rl_models': [],
            'working_models': [],
            'broken_models': [],
            'model_files': []
        }
        
        # بررسی فایل‌های مدل
        model_dirs = ['models', 'utils', 'ai_models']
        for model_dir in model_dirs:
            if os.path.exists(model_dir):
                for file in os.listdir(model_dir):
                    if file.endswith('.py') and file != '__init__.py':
                        models_info['model_files'].append(f"{model_dir}/{file}")
        
        # تحلیل تخصصی مدل‌ها
        self._analyze_local_models(models_info)
        self._analyze_downloadable_models(models_info)
        self._analyze_rl_models(models_info)
        
        self.results['models_analysis'] = models_info
        print(f"🤖 Found {len(models_info['model_files'])} model files")
        print(f"💻 Local models: {len(models_info['local_models'])}")
        print(f"📥 Downloadable models: {len(models_info['downloadable_models'])}")
        print(f"🎮 RL models: {len(models_info['rl_models'])}")
        
        return models_info
    
    def _analyze_local_models(self, models_info):
        """تحلیل مدل‌های محلی"""
        # بررسی مدل‌های RL محلی
        rl_models = ['ppo', 'a2c', 'dqn', 'ddpg', 'sac', 'td3']
        
        for model_file in models_info['model_files']:
            try:
                with open(model_file, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                
                # شناسایی مدل‌های RL
                for rl_model in rl_models:
                    if rl_model in content:
                        models_info['rl_models'].append(f"{model_file}:{rl_model}")
                
                # شناسایی مدل‌های محلی
                if any(keyword in content for keyword in ['sklearn', 'xgboost', 'lightgbm']):
                    models_info['local_models'].append(model_file)
                    
            except Exception as e:
                logger.error(f"Error analyzing {model_file}: {e}")
    
    def _analyze_downloadable_models(self, models_info):
        """تحلیل مدل‌های قابل دانلود"""
        # تست مدل‌های Hugging Face
        try:
            from utils.sentiment_analyzer import AdvancedSentimentAnalyzer
            
            # این مدل‌ها از Hugging Face دانلود می‌شوند
            hf_models = [
                'mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis',
                'HooshvareLab/bert-fa-base-uncased'
            ]
            
            for model_name in hf_models:
                models_info['downloadable_models'].append({
                    'name': model_name,
                    'type': 'huggingface',
                    'proxy_required': True
                })
                
        except Exception as e:
            models_info['broken_models'].append(f"Sentiment models: {e}")
    
    def _analyze_rl_models(self, models_info):
        """تحلیل مدل‌های یادگیری تقویتی"""
        # تست مدل‌های stable-baselines3
        try:
            # بررسی وجود PPO
            if os.path.exists('models/ppo_model.py') or 'ppo' in str(models_info['model_files']):
                models_info['rl_models'].append('PPO')
                
            # بررسی وجود A2C
            if os.path.exists('models/a2c_model.py') or 'a2c' in str(models_info['model_files']):
                models_info['rl_models'].append('A2C')
                
            # تست عملکرد
            try:
                from stable_baselines3 import PPO, A2C
                models_info['working_models'].extend(['PPO', 'A2C'])
            except ImportError:
                models_info['broken_models'].append('stable-baselines3 not installed')
                
        except Exception as e:
            models_info['broken_models'].append(f"RL models analysis: {e}")
    
    def analyze_strategies(self):
        """تحلیل استراتژی‌ها"""
        print("🔍 Analyzing Trading Strategies...")
        
        strategies_info = {
            'strategy_files': [],
            'genetic_strategies': [],
            'rule_based_strategies': [],
            'ml_strategies': [],
            'working_strategies': [],
            'broken_strategies': []
        }
        
        # بررسی فایل‌های استراتژی
        strategy_dirs = ['utils', 'strategies', 'examples']
        for strategy_dir in strategy_dirs:
            if os.path.exists(strategy_dir):
                for file in os.listdir(strategy_dir):
                    if 'strategy' in file.lower() or 'trading' in file.lower():
                        strategies_info['strategy_files'].append(f"{strategy_dir}/{file}")
        
        # تست استراتژی‌های مختلف
        self._test_genetic_strategies(strategies_info)
        self._test_rule_based_strategies(strategies_info)
        
        self.results['strategies_analysis'] = strategies_info
        print(f"🎯 Found {len(strategies_info['strategy_files'])} strategy files")
        print(f"🧬 Genetic strategies: {len(strategies_info['genetic_strategies'])}")
        print(f"📋 Rule-based strategies: {len(strategies_info['rule_based_strategies'])}")
        
        return strategies_info
    
    def _test_genetic_strategies(self, strategies_info):
        """تست استراتژی‌های ژنتیک"""
        try:
            from utils.genetic_strategy_evolution import GeneticStrategyEvolution
            gse = GeneticStrategyEvolution()
            strategies_info['genetic_strategies'].append('GeneticStrategyEvolution')
            strategies_info['working_strategies'].append('GeneticStrategyEvolution')
        except Exception as e:
            strategies_info['broken_strategies'].append(f"GeneticStrategyEvolution: {e}")
    
    def _test_rule_based_strategies(self, strategies_info):
        """تست استراتژی‌های قانون‌محور"""
        # جستجو در فایل‌ها
        for strategy_file in strategies_info['strategy_files']:
            try:
                with open(strategy_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'class' in content and 'strategy' in content.lower():
                    strategies_info['rule_based_strategies'].append(strategy_file)
                    
            except Exception as e:
                logger.error(f"Error reading {strategy_file}: {e}")
    
    def analyze_backtesting(self):
        """تحلیل بک‌تست"""
        print("🔍 Analyzing Backtesting Framework...")
        
        backtesting_info = {
            'frameworks_found': [],
            'working_frameworks': [],
            'broken_frameworks': [],
            'backtest_files': []
        }
        
        # بررسی فریم‌ورک‌های بک‌تست
        self._test_backtesting_frameworks(backtesting_info)
        
        self.results['backtesting_analysis'] = backtesting_info
        print(f"🔄 Found {len(backtesting_info['frameworks_found'])} backtesting frameworks")
        print(f"✅ Working: {len(backtesting_info['working_frameworks'])}")
        
        return backtesting_info
    
    def _test_backtesting_frameworks(self, backtesting_info):
        """تست فریم‌ورک‌های بک‌تست"""
        # تست BacktestingFramework
        try:
            from core.backtesting_framework import BacktestingFramework
            bf = BacktestingFramework()
            backtesting_info['frameworks_found'].append('BacktestingFramework')
            backtesting_info['working_frameworks'].append('BacktestingFramework')
        except Exception as e:
            backtesting_info['broken_frameworks'].append(f"BacktestingFramework: {e}")
        
        # تست Backtrader
        try:
            import backtrader as bt
            backtesting_info['frameworks_found'].append('Backtrader')
            backtesting_info['working_frameworks'].append('Backtrader')
        except ImportError:
            backtesting_info['broken_frameworks'].append('Backtrader: not installed')
    
    def analyze_optimization(self):
        """تحلیل بهینه‌سازی"""
        print("🔍 Analyzing Optimization Systems...")
        
        optimization_info = {
            'optimizers_found': [],
            'working_optimizers': [],
            'broken_optimizers': [],
            'optimization_files': []
        }
        
        # بررسی فایل‌های بهینه‌سازی
        if os.path.exists('optimization'):
            for file in os.listdir('optimization'):
                if file.endswith('.py'):
                    optimization_info['optimization_files'].append(f"optimization/{file}")
        
        # تست بهینه‌سازها
        self._test_optimizers(optimization_info)
        
        self.results['optimization_analysis'] = optimization_info
        print(f"⚙️ Found {len(optimization_info['optimization_files'])} optimization files")
        print(f"✅ Working optimizers: {len(optimization_info['working_optimizers'])}")
        
        return optimization_info
    
    def _test_optimizers(self, optimization_info):
        """تست بهینه‌سازها"""
        # تست Bayesian Optimization
        try:
            from optimization.bayesian import BayesianOptimizer
            optimization_info['optimizers_found'].append('BayesianOptimizer')
            optimization_info['working_optimizers'].append('BayesianOptimizer')
        except Exception as e:
            optimization_info['broken_optimizers'].append(f"BayesianOptimizer: {e}")
        
        # تست Genetic Algorithm
        try:
            from optimization.genetic import GeneticOptimizer
            optimization_info['optimizers_found'].append('GeneticOptimizer')
            optimization_info['working_optimizers'].append('GeneticOptimizer')
        except Exception as e:
            optimization_info['broken_optimizers'].append(f"GeneticOptimizer: {e}")
        
        # تست Optuna
        try:
            import optuna
            optimization_info['optimizers_found'].append('Optuna')
            optimization_info['working_optimizers'].append('Optuna')
        except ImportError:
            optimization_info['broken_optimizers'].append('Optuna: not installed')
    
    def generate_expert_report(self):
        """تولید گزارش تخصصی"""
        print("\n" + "="*60)
        print("📊 EXPERT SYSTEM ANALYSIS REPORT")
        print("="*60)
        
        # محاسبه نمرات
        scores = self._calculate_scores()
        
        # گزارش تفصیلی
        self._print_detailed_report(scores)
        
        # توصیه‌های تخصصی
        self._print_expert_recommendations()
        
        # ذخیره گزارش
        self._save_report(scores)
        
        return scores
    
    def _calculate_scores(self):
        """محاسبه نمرات"""
        scores = {}
        
        # Data Infrastructure
        data_analysis = self.results['data_analysis']
        scores['data'] = min(100, (data_analysis['total_files'] / 100) * 100)
        
        # Indicators
        indicators_analysis = self.results['indicators_analysis']
        total_indicators = len(indicators_analysis['working_indicators']) + len(indicators_analysis['broken_indicators'])
        scores['indicators'] = (len(indicators_analysis['working_indicators']) / max(1, total_indicators)) * 100
        
        # Models
        models_analysis = self.results['models_analysis']
        total_models = len(models_analysis['working_models']) + len(models_analysis['broken_models'])
        scores['models'] = (len(models_analysis['working_models']) / max(1, total_models)) * 100
        
        # Strategies
        strategies_analysis = self.results['strategies_analysis']
        total_strategies = len(strategies_analysis['working_strategies']) + len(strategies_analysis['broken_strategies'])
        scores['strategies'] = (len(strategies_analysis['working_strategies']) / max(1, total_strategies)) * 100
        
        # Backtesting
        backtesting_analysis = self.results['backtesting_analysis']
        total_backtesting = len(backtesting_analysis['working_frameworks']) + len(backtesting_analysis['broken_frameworks'])
        scores['backtesting'] = (len(backtesting_analysis['working_frameworks']) / max(1, total_backtesting)) * 100
        
        # Optimization
        optimization_analysis = self.results['optimization_analysis']
        total_optimization = len(optimization_analysis['working_optimizers']) + len(optimization_analysis['broken_optimizers'])
        scores['optimization'] = (len(optimization_analysis['working_optimizers']) / max(1, total_optimization)) * 100
        
        # Overall Score
        scores['overall'] = sum(scores.values()) / len(scores)
        
        return scores
    
    def _print_detailed_report(self, scores):
        """چاپ گزارش تفصیلی"""
        print(f"\n📊 SYSTEM HEALTH SCORES:")
        print(f"Data Infrastructure: {scores['data']:.1f}%")
        print(f"Technical Indicators: {scores['indicators']:.1f}%")
        print(f"AI Models: {scores['models']:.1f}%")
        print(f"Trading Strategies: {scores['strategies']:.1f}%")
        print(f"Backtesting: {scores['backtesting']:.1f}%")
        print(f"Optimization: {scores['optimization']:.1f}%")
        print(f"Overall System: {scores['overall']:.1f}%")
        
        # رنگ‌بندی نتایج
        if scores['overall'] >= 80:
            print("🟢 EXCELLENT: System is production-ready")
        elif scores['overall'] >= 60:
            print("🟡 GOOD: System needs minor improvements")
        elif scores['overall'] >= 40:
            print("🟠 FAIR: System needs significant work")
        else:
            print("🔴 POOR: System needs major reconstruction")
    
    def _print_expert_recommendations(self):
        """چاپ توصیه‌های تخصصی"""
        print(f"\n🎯 EXPERT RECOMMENDATIONS:")
        
        # بر اساس نتایج تحلیل
        models_analysis = self.results['models_analysis']
        if len(models_analysis['broken_models']) > 0:
            print("🔧 MODELS: Fix broken model imports and dependencies")
            
        indicators_analysis = self.results['indicators_analysis']
        if len(indicators_analysis['broken_indicators']) > 0:
            print("📈 INDICATORS: Repair technical indicator implementations")
            
        optimization_analysis = self.results['optimization_analysis']
        if len(optimization_analysis['broken_optimizers']) > 0:
            print("⚙️ OPTIMIZATION: Install missing optimization libraries")
            
        backtesting_analysis = self.results['backtesting_analysis']
        if len(backtesting_analysis['broken_frameworks']) > 0:
            print("🔄 BACKTESTING: Fix backtesting framework issues")
    
    def _save_report(self, scores):
        """ذخیره گزارش"""
        report = {
            'timestamp': str(pd.Timestamp.now()),
            'scores': scores,
            'detailed_results': self.results,
            'proxy_config': self.proxy_config
        }
        
        with open('expert_system_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Expert report saved: expert_system_report.json")

def main():
    """اجرای تحلیل تخصصی"""
    print("🔍 Expert System Analysis")
    print("="*50)
    
    analyzer = ExpertSystemAnalyzer()
    
    # اجرای تحلیل‌های مختلف
    analyzer.analyze_data_infrastructure()
    analyzer.analyze_indicators()
    analyzer.analyze_models()
    analyzer.analyze_strategies()
    analyzer.analyze_backtesting()
    analyzer.analyze_optimization()
    
    # تولید گزارش نهایی
    scores = analyzer.generate_expert_report()
    
    return scores['overall']

if __name__ == "__main__":
    try:
        import pandas as pd
        overall_score = main()
        exit(0 if overall_score >= 60 else 1)
    except ImportError:
        print("❌ pandas required for analysis")
        exit(1) 