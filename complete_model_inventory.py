"""
📋 Complete Model Inventory for Pearl-3x7B
فهرست کامل مدل‌های Pearl-3x7B

این فایل شامل:
1. شناسایی کامل تمام مدل‌های موجود
2. وضعیت آموزش هر مدل
3. اولویت‌بندی آموزش
4. نیازمندی‌های هر مدل
"""

import os
import sys
from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class ModelStatus(Enum):
    """وضعیت مدل"""
    NOT_TRAINED = "not_trained"
    PARTIALLY_TRAINED = "partially_trained"
    FULLY_TRAINED = "fully_trained"
    NEEDS_RETRAINING = "needs_retraining"

class TrainingPriority(Enum):
    """اولویت آموزش"""
    CRITICAL = 1      # ضروری - باید فوراً آموزش ببیند
    HIGH = 2          # مهم - آموزش در اولویت بالا
    MEDIUM = 3        # متوسط - آموزش در صف
    LOW = 4           # کم - آموزش در آینده
    RESEARCH = 5      # تحقیقاتی - آموزش اختیاری

@dataclass
class ModelInfo:
    """اطلاعات کامل مدل"""
    name: str
    category: str
    type: str
    status: ModelStatus
    priority: TrainingPriority
    trainer_class: str
    config_class: str
    dependencies: List[str]
    memory_requirement_mb: int
    training_time_estimate_min: int
    evaluation_metrics: List[str]
    description: str
    needs_brain_training: bool = True

class CompleteModelInventory:
    """فهرست کامل مدل‌های Pearl-3x7B"""
    
    def __init__(self):
        self.models = self._create_complete_inventory()
        
    def _create_complete_inventory(self) -> Dict[str, ModelInfo]:
        """ایجاد فهرست کامل مدل‌ها"""
        
        models = {}
        
        # =================== 1. SENTIMENT ANALYSIS MODELS ===================
        models.update({
            "FinBERT": ModelInfo(
                name="FinBERT",
                category="sentiment",
                type="transformer",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.CRITICAL,
                trainer_class="PearlSentimentTrainer",
                config_class="SentimentTrainingConfig",
                dependencies=["transformers", "torch", "datasets"],
                memory_requirement_mb=2048,
                training_time_estimate_min=45,
                evaluation_metrics=["accuracy", "precision", "recall", "f1_score"],
                description="Financial BERT for sentiment analysis",
                needs_brain_training=True
            ),
            
            "CryptoBERT": ModelInfo(
                name="CryptoBERT",
                category="sentiment",
                type="transformer",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.HIGH,
                trainer_class="PearlSentimentTrainer",
                config_class="SentimentTrainingConfig",
                dependencies=["transformers", "torch", "datasets"],
                memory_requirement_mb=1800,
                training_time_estimate_min=40,
                evaluation_metrics=["accuracy", "precision", "recall", "f1_score"],
                description="Cryptocurrency-focused BERT model",
                needs_brain_training=True
            ),
            
            "FinancialSentimentModel": ModelInfo(
                name="FinancialSentimentModel",
                category="sentiment",
                type="ensemble",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.HIGH,
                trainer_class="PearlSentimentTrainer",
                config_class="SentimentTrainingConfig",
                dependencies=["transformers", "torch", "sklearn"],
                memory_requirement_mb=3000,
                training_time_estimate_min=60,
                evaluation_metrics=["accuracy", "precision", "recall", "f1_score", "ensemble_agreement"],
                description="Ensemble financial sentiment model",
                needs_brain_training=True
            ),
            
            "SentimentEnsemble": ModelInfo(
                name="SentimentEnsemble",
                category="sentiment",
                type="ensemble",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.MEDIUM,
                trainer_class="PearlSentimentTrainer",
                config_class="SentimentTrainingConfig",
                dependencies=["transformers", "torch", "sklearn"],
                memory_requirement_mb=4000,
                training_time_estimate_min=90,
                evaluation_metrics=["accuracy", "precision", "recall", "f1_score", "ensemble_diversity"],
                description="Advanced sentiment ensemble system",
                needs_brain_training=True
            ),
            
            "HuggingFaceSentimentManager": ModelInfo(
                name="HuggingFaceSentimentManager",
                category="sentiment",
                type="manager",
                status=ModelStatus.PARTIALLY_TRAINED,  # فعلاً فقط pre-trained models
                priority=TrainingPriority.CRITICAL,
                trainer_class="HuggingFaceSentimentTrainer",
                config_class="HuggingFaceSentimentConfig",
                dependencies=["transformers", "torch"],
                memory_requirement_mb=1000,
                training_time_estimate_min=30,
                evaluation_metrics=["accuracy", "response_time", "model_availability"],
                description="Manager for HuggingFace sentiment models (currently using pre-trained)",
                needs_brain_training=True
            )
        })
        
        # =================== 2. TIME SERIES MODELS ===================
        models.update({
            "LSTM_TimeSeries": ModelInfo(
                name="LSTM_TimeSeries",
                category="timeseries",
                type="lstm",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.CRITICAL,
                trainer_class="PearlTimeSeriesTrainer",
                config_class="TimeSeriesTrainingConfig",
                dependencies=["torch", "numpy", "pandas", "sklearn"],
                memory_requirement_mb=1024,
                training_time_estimate_min=30,
                evaluation_metrics=["rmse", "mae", "mape", "directional_accuracy"],
                description="LSTM-based time series prediction",
                needs_brain_training=True
            ),
            
            "GRU_TimeSeries": ModelInfo(
                name="GRU_TimeSeries",
                category="timeseries",
                type="gru",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.HIGH,
                trainer_class="PearlTimeSeriesTrainer",
                config_class="TimeSeriesTrainingConfig",
                dependencies=["torch", "numpy", "pandas", "sklearn"],
                memory_requirement_mb=900,
                training_time_estimate_min=25,
                evaluation_metrics=["rmse", "mae", "mape", "directional_accuracy"],
                description="GRU-based time series prediction",
                needs_brain_training=True
            ),
            
            "Transformer_TimeSeries": ModelInfo(
                name="Transformer_TimeSeries",
                category="timeseries",
                type="transformer",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.HIGH,
                trainer_class="PearlTimeSeriesTrainer",
                config_class="TimeSeriesTrainingConfig",
                dependencies=["torch", "numpy", "pandas"],
                memory_requirement_mb=2048,
                training_time_estimate_min=50,
                evaluation_metrics=["rmse", "mae", "mape", "directional_accuracy", "attention_weights"],
                description="Transformer-based time series prediction",
                needs_brain_training=True
            ),
            
            "ChronosModel": ModelInfo(
                name="ChronosModel",
                category="timeseries",
                type="foundation",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.MEDIUM,
                trainer_class="PearlTimeSeriesTrainer",
                config_class="TimeSeriesTrainingConfig",
                dependencies=["chronos", "torch", "transformers"],
                memory_requirement_mb=1500,
                training_time_estimate_min=20,
                evaluation_metrics=["rmse", "mae", "mape", "directional_accuracy"],
                description="Amazon Chronos foundation model for time series",
                needs_brain_training=True
            ),
            
            "TimeSeriesEnsemble": ModelInfo(
                name="TimeSeriesEnsemble",
                category="timeseries",
                type="ensemble",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.MEDIUM,
                trainer_class="PearlTimeSeriesTrainer",
                config_class="TimeSeriesTrainingConfig",
                dependencies=["torch", "numpy", "pandas", "sklearn"],
                memory_requirement_mb=3500,
                training_time_estimate_min=80,
                evaluation_metrics=["rmse", "mae", "mape", "directional_accuracy", "ensemble_variance"],
                description="Ensemble of multiple time series models",
                needs_brain_training=True
            )
        })
        
        # =================== 3. REINFORCEMENT LEARNING MODELS ===================
        models.update({
            "DQN_Agent": ModelInfo(
                name="DQN_Agent",
                category="reinforcement_learning",
                type="dqn",
                status=ModelStatus.PARTIALLY_TRAINED,  # فقط تست شده
                priority=TrainingPriority.CRITICAL,
                trainer_class="PearlRLTrainer",
                config_class="RLTrainingConfig",
                dependencies=["torch", "gym", "numpy", "stable_baselines3"],
                memory_requirement_mb=1200,
                training_time_estimate_min=60,
                evaluation_metrics=["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"],
                description="Deep Q-Network trading agent",
                needs_brain_training=True
            ),
            
            "PPO_Agent": ModelInfo(
                name="PPO_Agent",
                category="reinforcement_learning",
                type="ppo",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.CRITICAL,
                trainer_class="PearlRLTrainer",
                config_class="RLTrainingConfig",
                dependencies=["torch", "gym", "numpy", "stable_baselines3"],
                memory_requirement_mb=1100,
                training_time_estimate_min=55,
                evaluation_metrics=["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"],
                description="Proximal Policy Optimization trading agent",
                needs_brain_training=True
            ),
            
            "A2C_Agent": ModelInfo(
                name="A2C_Agent",
                category="reinforcement_learning",
                type="a2c",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.HIGH,
                trainer_class="PearlRLTrainer",
                config_class="RLTrainingConfig",
                dependencies=["torch", "gym", "numpy", "stable_baselines3"],
                memory_requirement_mb=1000,
                training_time_estimate_min=45,
                evaluation_metrics=["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"],
                description="Advantage Actor-Critic trading agent",
                needs_brain_training=True
            ),
            
            "TD3_Agent": ModelInfo(
                name="TD3_Agent",
                category="reinforcement_learning",
                type="td3",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.HIGH,
                trainer_class="PearlRLTrainer",
                config_class="RLTrainingConfig",
                dependencies=["torch", "gym", "numpy", "stable_baselines3"],
                memory_requirement_mb=1300,
                training_time_estimate_min=70,
                evaluation_metrics=["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown"],
                description="Twin Delayed Deep Deterministic Policy Gradient agent",
                needs_brain_training=True
            ),
            
            "EnhancedDQNAgent": ModelInfo(
                name="EnhancedDQNAgent",
                category="reinforcement_learning",
                type="enhanced_dqn",
                status=ModelStatus.PARTIALLY_TRAINED,  # فقط تست شده
                priority=TrainingPriority.CRITICAL,
                trainer_class="EnhancedPearlRLTrainer",
                config_class="RLTrainingConfig",
                dependencies=["torch", "gym", "numpy"],
                memory_requirement_mb=1500,
                training_time_estimate_min=80,
                evaluation_metrics=["avg_reward", "success_rate", "sharpe_ratio", "max_drawdown", "replay_efficiency"],
                description="Enhanced DQN with prioritized replay and multi-step learning",
                needs_brain_training=True
            )
        })
        
        # =================== 4. ADVANCED MODELS ===================
        models.update({
            "HierarchicalRL": ModelInfo(
                name="HierarchicalRL",
                category="advanced_rl",
                type="hierarchical",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.RESEARCH,
                trainer_class="HierarchicalRLTrainer",
                config_class="HierarchicalRLConfig",
                dependencies=["torch", "gym", "numpy"],
                memory_requirement_mb=2000,
                training_time_estimate_min=90,
                evaluation_metrics=["avg_reward", "success_rate", "hierarchy_efficiency"],
                description="Hierarchical reinforcement learning system",
                needs_brain_training=True
            ),

            "MetaLearner": ModelInfo(
                name="MetaLearner",
                category="meta_learning",
                type="meta",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.RESEARCH,
                trainer_class="MetaLearnerTrainer",
                config_class="MetaLearnerConfig",
                dependencies=["torch", "higher", "numpy"],
                memory_requirement_mb=2500,
                training_time_estimate_min=120,
                evaluation_metrics=["adaptation_speed", "few_shot_accuracy", "generalization"],
                description="Meta-learning system for rapid adaptation",
                needs_brain_training=True
            ),

            "ZeroShotLearning": ModelInfo(
                name="ZeroShotLearning",
                category="zero_shot",
                type="zero_shot",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.RESEARCH,
                trainer_class="ZeroShotTrainer",
                config_class="ZeroShotConfig",
                dependencies=["torch", "transformers", "clip"],
                memory_requirement_mb=1800,
                training_time_estimate_min=80,
                evaluation_metrics=["zero_shot_accuracy", "transfer_efficiency"],
                description="Zero-shot learning for new market conditions",
                needs_brain_training=True
            ),

            "ContinualLearning": ModelInfo(
                name="ContinualLearning",
                category="continual",
                type="continual",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.LOW,
                trainer_class="ContinualLearningTrainer",
                config_class="ContinualLearningConfig",
                dependencies=["torch", "avalanche", "numpy"],
                memory_requirement_mb=2200,
                training_time_estimate_min=100,
                evaluation_metrics=["retention_rate", "plasticity", "catastrophic_forgetting"],
                description="Continual learning system to avoid catastrophic forgetting",
                needs_brain_training=True
            )
        })

        # =================== 5. ENSEMBLE & UNIFIED MODELS ===================
        models.update({
            "UnifiedTradingSystem": ModelInfo(
                name="UnifiedTradingSystem",
                category="unified",
                type="ensemble",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.LOW,
                trainer_class="UnifiedSystemTrainer",
                config_class="UnifiedSystemConfig",
                dependencies=["torch", "sklearn", "numpy", "pandas"],
                memory_requirement_mb=5000,
                training_time_estimate_min=150,
                evaluation_metrics=["overall_performance", "component_agreement", "system_stability"],
                description="Unified trading system combining all models",
                needs_brain_training=True
            ),

            "ModelEnsemble": ModelInfo(
                name="ModelEnsemble",
                category="ensemble",
                type="weighted_ensemble",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.MEDIUM,
                trainer_class="EnsembleTrainer",
                config_class="EnsembleConfig",
                dependencies=["sklearn", "numpy", "pandas"],
                memory_requirement_mb=1500,
                training_time_estimate_min=40,
                evaluation_metrics=["ensemble_accuracy", "diversity_score", "weight_stability"],
                description="Weighted ensemble of multiple models",
                needs_brain_training=True
            ),

            "WeightedEnsemble": ModelInfo(
                name="WeightedEnsemble",
                category="ensemble",
                type="weighted_ensemble",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.MEDIUM,
                trainer_class="EnsembleTrainer",
                config_class="EnsembleConfig",
                dependencies=["sklearn", "numpy"],
                memory_requirement_mb=800,
                training_time_estimate_min=25,
                evaluation_metrics=["ensemble_accuracy", "weight_optimization"],
                description="Weighted ensemble with dynamic weight adjustment",
                needs_brain_training=True
            ),

            "VotingEnsemble": ModelInfo(
                name="VotingEnsemble",
                category="ensemble",
                type="voting_ensemble",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.MEDIUM,
                trainer_class="EnsembleTrainer",
                config_class="EnsembleConfig",
                dependencies=["sklearn", "numpy"],
                memory_requirement_mb=600,
                training_time_estimate_min=20,
                evaluation_metrics=["voting_accuracy", "consensus_rate"],
                description="Voting-based ensemble system",
                needs_brain_training=True
            )
        })

        # =================== 6. DOCUMENT & NLP MODELS ===================
        models.update({
            "LayoutLMModel": ModelInfo(
                name="LayoutLMModel",
                category="document",
                type="layout_lm",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.LOW,
                trainer_class="DocumentTrainer",
                config_class="DocumentConfig",
                dependencies=["transformers", "torch", "pillow"],
                memory_requirement_mb=3000,
                training_time_estimate_min=60,
                evaluation_metrics=["document_accuracy", "layout_understanding"],
                description="Layout-aware document understanding model",
                needs_brain_training=False
            ),

            "BARTModel": ModelInfo(
                name="BARTModel",
                category="document",
                type="bart",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.LOW,
                trainer_class="DocumentTrainer",
                config_class="DocumentConfig",
                dependencies=["transformers", "torch"],
                memory_requirement_mb=2000,
                training_time_estimate_min=45,
                evaluation_metrics=["summarization_quality", "generation_fluency"],
                description="BART model for text summarization and generation",
                needs_brain_training=False
            ),

            "DocumentAnalyzer": ModelInfo(
                name="DocumentAnalyzer",
                category="document",
                type="analyzer",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.LOW,
                trainer_class="DocumentTrainer",
                config_class="DocumentConfig",
                dependencies=["transformers", "torch", "spacy"],
                memory_requirement_mb=1500,
                training_time_estimate_min=35,
                evaluation_metrics=["analysis_accuracy", "extraction_quality"],
                description="Document analysis and information extraction",
                needs_brain_training=False
            )
        })

        # =================== 7. BRAIN & MANAGEMENT MODELS ===================
        models.update({
            "PearlBrain": ModelInfo(
                name="PearlBrain",
                category="brain",
                type="decision_system",
                status=ModelStatus.PARTIALLY_TRAINED,  # فقط تست شده
                priority=TrainingPriority.CRITICAL,
                trainer_class="BrainTrainer",
                config_class="BrainConfig",
                dependencies=["torch", "numpy", "sklearn"],
                memory_requirement_mb=500,
                training_time_estimate_min=120,
                evaluation_metrics=["decision_accuracy", "confidence_calibration", "learning_efficiency"],
                description="Pearl-3x7B intelligent decision-making brain",
                needs_brain_training=False  # خودش مغز است!
            ),

            "ModelManager": ModelInfo(
                name="ModelManager",
                category="management",
                type="manager",
                status=ModelStatus.PARTIALLY_TRAINED,
                priority=TrainingPriority.HIGH,
                trainer_class="ManagerTrainer",
                config_class="ManagerConfig",
                dependencies=["torch", "sklearn", "pandas"],
                memory_requirement_mb=300,
                training_time_estimate_min=30,
                evaluation_metrics=["management_efficiency", "resource_optimization"],
                description="Model management and orchestration system",
                needs_brain_training=True
            ),

            "AIAgent": ModelInfo(
                name="AIAgent",
                category="agent",
                type="ai_agent",
                status=ModelStatus.NOT_TRAINED,
                priority=TrainingPriority.HIGH,
                trainer_class="AIAgentTrainer",
                config_class="AIAgentConfig",
                dependencies=["torch", "transformers", "gym"],
                memory_requirement_mb=2000,
                training_time_estimate_min=90,
                evaluation_metrics=["agent_performance", "adaptability", "decision_quality"],
                description="Comprehensive AI agent system",
                needs_brain_training=True
            )
        })

        return models
    
    def get_models_by_category(self, category: str) -> List[str]:
        """دریافت مدل‌ها بر اساس دسته"""
        return [name for name, model in self.models.items() if model.category == category]
    
    def get_models_by_status(self, status: ModelStatus) -> List[str]:
        """دریافت مدل‌ها بر اساس وضعیت"""
        return [name for name, model in self.models.items() if model.status == status]
    
    def get_models_by_priority(self, priority: TrainingPriority) -> List[str]:
        """دریافت مدل‌ها بر اساس اولویت"""
        return [name for name, model in self.models.items() if model.priority == priority]
    
    def get_training_order(self) -> List[str]:
        """ترتیب پیشنهادی آموزش"""
        # مرتب‌سازی بر اساس اولویت و زمان آموزش
        sorted_models = sorted(
            self.models.items(),
            key=lambda x: (x[1].priority.value, x[1].training_time_estimate_min)
        )
        return [name for name, _ in sorted_models]
    
    def get_untrained_models(self) -> List[str]:
        """مدل‌های آموزش نداده"""
        return self.get_models_by_status(ModelStatus.NOT_TRAINED)
    
    def get_brain_training_candidates(self) -> List[str]:
        """مدل‌هایی که نیاز به آموزش با مغز متفکر دارند"""
        return [name for name, model in self.models.items() if model.needs_brain_training]
    
    def print_summary(self):
        """چاپ خلاصه وضعیت"""
        print("📋 COMPLETE MODEL INVENTORY SUMMARY")
        print("=" * 60)
        
        # آمار کلی
        total_models = len(self.models)
        untrained = len(self.get_untrained_models())
        partially_trained = len(self.get_models_by_status(ModelStatus.PARTIALLY_TRAINED))
        fully_trained = len(self.get_models_by_status(ModelStatus.FULLY_TRAINED))
        
        print(f"📊 Total Models: {total_models}")
        print(f"❌ Not Trained: {untrained}")
        print(f"⚠️ Partially Trained: {partially_trained}")
        print(f"✅ Fully Trained: {fully_trained}")
        
        # آمار بر اساس دسته
        print(f"\n📂 By Category:")
        categories = set(model.category for model in self.models.values())
        for category in sorted(categories):
            count = len(self.get_models_by_category(category))
            print(f"  {category}: {count} models")
        
        # آمار بر اساس اولویت
        print(f"\n🎯 By Priority:")
        for priority in TrainingPriority:
            count = len(self.get_models_by_priority(priority))
            print(f"  {priority.name}: {count} models")
        
        # مدل‌های نیازمند آموزش فوری
        critical_models = self.get_models_by_priority(TrainingPriority.CRITICAL)
        print(f"\n🚨 CRITICAL MODELS (Need Immediate Training):")
        for model_name in critical_models:
            model = self.models[model_name]
            print(f"  ❗ {model_name} ({model.category}) - {model.status.value}")
        
        # مدل‌های نیازمند مغز متفکر
        brain_candidates = self.get_brain_training_candidates()
        print(f"\n🧠 BRAIN TRAINING CANDIDATES: {len(brain_candidates)} models")
        
        print(f"\n⏱️ Total Estimated Training Time: {sum(model.training_time_estimate_min for model in self.models.values())} minutes")
        print(f"💾 Total Memory Requirement: {sum(model.memory_requirement_mb for model in self.models.values())} MB")

def main():
    """اجرای فهرست‌سازی کامل"""
    inventory = CompleteModelInventory()
    inventory.print_summary()
    
    return inventory

if __name__ == "__main__":
    inventory = main()
