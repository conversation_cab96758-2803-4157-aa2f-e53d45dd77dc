#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 Simple Profit Target Demo
سیستم نمایشی ساده برای اهداف سود با $1000 سرمایه
"""

import os
import sys
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from portfolio.advanced_risk_manager import AdvancedRiskManager, RiskParameters
    from core.base import TradingSignal
except ImportError as e:
    print(f"Import warning: {e}")
    print("Creating minimal demo without full imports...")

class SimpleProfitDemo:
    """سیستم نمایشی ساده"""
    
    def __init__(self):
        print("🎯 SIMPLE PROFIT TARGET DEMO")
        print("=" * 60)
        
        # تنظیمات اساسی
        self.initial_capital = 1000.0
        self.current_capital = 1000.0
        self.daily_target = 5.0
        self.weekly_target = 30.0
        self.monthly_target = 80.0
        self.max_drawdown = 10.0  # درصد
        self.daily_loss_limit = 4.0  # درصد
        
        # آمار جلسه
        self.daily_pnl = 0.0
        self.weekly_pnl = 0.0
        self.monthly_pnl = 0.0
        self.total_pnl = 0.0
        self.trades_executed = 0
        self.successful_trades = 0
        self.positions = {}
        
        # اهداف
        self.targets_achieved = {
            "daily": False,
            "weekly": False,
            "monthly": False
        }
        
        print(f"💰 Initial Capital: ${self.initial_capital}")
        print(f"🎯 Daily Target: ${self.daily_target} per symbol")
        print(f"📊 Weekly Target: ${self.weekly_target}")
        print(f"🚀 Monthly Target: ${self.monthly_target}")
        print(f"📉 Max Drawdown: {self.max_drawdown}%")
        print(f"📊 Daily Loss Limit: {self.daily_loss_limit}%")
        print("=" * 60)
    
    def check_risk_limits(self) -> bool:
        """بررسی محدودیت‌های ریسک"""
        
        # بررسی ضرر کلی
        current_drawdown = ((self.initial_capital - self.current_capital) / self.initial_capital) * 100
        if current_drawdown >= self.max_drawdown:
            print(f"🚨 Max drawdown reached: {current_drawdown:.1f}%")
            return False
        
        # بررسی ضرر روزانه
        daily_loss_percent = abs(self.daily_pnl / self.initial_capital) * 100
        if self.daily_pnl < 0 and daily_loss_percent >= self.daily_loss_limit:
            print(f"🚨 Daily loss limit reached: {daily_loss_percent:.1f}%")
            return False
        
        return True
    
    def simulate_trade(self, symbol: str, action: str) -> Dict:
        """شبیه‌سازی معامله"""
        
        import random
        
        # قیمت‌های پایه
        base_prices = {
            "EURUSD": 1.0850,
            "GBPUSD": 1.2650,
            "USDJPY": 150.50,
            "AUDUSD": 0.6750,
            "USDCAD": 1.3550
        }
        
        # محاسبه اندازه موقعیت (2% ریسک)
        risk_amount = self.current_capital * 0.02  # 2% ریسک
        
        # قیمت ورود
        entry_price = base_prices.get(symbol, 1.0000)
        
        # stop loss و take profit
        stop_loss_percent = 0.015  # 1.5%
        take_profit_percent = 0.03  # 3%
        
        if action == "buy":
            stop_loss = entry_price * (1 - stop_loss_percent)
            take_profit = entry_price * (1 + take_profit_percent)
        else:  # sell
            stop_loss = entry_price * (1 + stop_loss_percent)
            take_profit = entry_price * (1 - take_profit_percent)
        
        # محاسبه quantity
        risk_per_unit = abs(entry_price - stop_loss)
        quantity = risk_amount / risk_per_unit
        
        # شبیه‌سازی نتیجه (70% موفقیت)
        success_rate = 0.7
        is_successful = random.random() < success_rate
        
        if is_successful:
            # رسیدن به take profit
            exit_price = take_profit
            reason = "take_profit"
        else:
            # رسیدن به stop loss
            exit_price = stop_loss
            reason = "stop_loss"
        
        # محاسبه سود/ضرر
        if action == "buy":
            pnl = quantity * (exit_price - entry_price)
        else:  # sell
            pnl = quantity * (entry_price - exit_price)
        
        # بروزرسانی حساب
        self.current_capital += pnl
        self.total_pnl += pnl
        self.daily_pnl += pnl
        self.weekly_pnl += pnl
        self.monthly_pnl += pnl
        
        # آمار
        self.trades_executed += 1
        if is_successful:
            self.successful_trades += 1
        
        return {
            "symbol": symbol,
            "action": action,
            "entry_price": entry_price,
            "exit_price": exit_price,
            "quantity": quantity,
            "pnl": pnl,
            "reason": reason,
            "successful": is_successful
        }
    
    def check_targets(self):
        """بررسی اهداف"""
        
        # بررسی هدف روزانه
        if self.daily_pnl >= self.daily_target and not self.targets_achieved["daily"]:
            self.targets_achieved["daily"] = True
            print(f"🎉 DAILY TARGET ACHIEVED: ${self.daily_pnl:.2f}")
        
        # بررسی هدف هفتگی
        if self.weekly_pnl >= self.weekly_target and not self.targets_achieved["weekly"]:
            self.targets_achieved["weekly"] = True
            print(f"🎉 WEEKLY TARGET ACHIEVED: ${self.weekly_pnl:.2f}")
        
        # بررسی هدف ماهانه
        if self.monthly_pnl >= self.monthly_target and not self.targets_achieved["monthly"]:
            self.targets_achieved["monthly"] = True
            print(f"🎉 MONTHLY TARGET ACHIEVED: ${self.monthly_pnl:.2f}")
    
    def display_status(self):
        """نمایش وضعیت"""
        
        print("\n" + "="*60)
        print("📊 CURRENT STATUS")
        print("="*60)
        
        print(f"💰 Capital:")
        print(f"   Initial: ${self.initial_capital:.2f}")
        print(f"   Current: ${self.current_capital:.2f}")
        print(f"   Total PnL: ${self.total_pnl:.2f}")
        print(f"   Daily PnL: ${self.daily_pnl:.2f}")
        
        print(f"\n🎯 Target Progress:")
        daily_progress = (self.daily_pnl / self.daily_target) * 100 if self.daily_target > 0 else 0
        weekly_progress = (self.weekly_pnl / self.weekly_target) * 100 if self.weekly_target > 0 else 0
        monthly_progress = (self.monthly_pnl / self.monthly_target) * 100 if self.monthly_target > 0 else 0
        
        print(f"   Daily: ${self.daily_pnl:.2f} / ${self.daily_target:.2f} ({daily_progress:.1f}%)")
        print(f"   Weekly: ${self.weekly_pnl:.2f} / ${self.weekly_target:.2f} ({weekly_progress:.1f}%)")
        print(f"   Monthly: ${self.monthly_pnl:.2f} / ${self.monthly_target:.2f} ({monthly_progress:.1f}%)")
        
        print(f"\n📈 Trading Stats:")
        print(f"   Total Trades: {self.trades_executed}")
        print(f"   Successful: {self.successful_trades}")
        if self.trades_executed > 0:
            success_rate = (self.successful_trades / self.trades_executed) * 100
            print(f"   Success Rate: {success_rate:.1f}%")
        
        print(f"\n📊 Risk Status:")
        current_drawdown = ((self.initial_capital - self.current_capital) / self.initial_capital) * 100
        print(f"   Current Drawdown: {current_drawdown:.1f}%")
        print(f"   Max Drawdown Limit: {self.max_drawdown:.1f}%")
        
        print(f"\n✅ Targets Achieved: {sum(self.targets_achieved.values())}/3")
        
        print("="*60)
    
    def run_demo_session(self, num_trades: int = 10):
        """اجرای جلسه نمایشی"""
        
        print(f"\n🚀 STARTING DEMO SESSION: {num_trades} trades")
        print("=" * 60)
        
        symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD"]
        actions = ["buy", "sell"]
        
        for i in range(num_trades):
            print(f"\n--- Trade {i+1}/{num_trades} ---")
            
            # بررسی محدودیت‌های ریسک
            if not self.check_risk_limits():
                print("🚨 Risk limits reached. Stopping demo.")
                break
            
            # انتخاب نماد و عمل
            import random
            symbol = random.choice(symbols)
            action = random.choice(actions)
            
            print(f"🔍 Processing {symbol} {action.upper()}...")
            
            # اجرای معامله
            result = self.simulate_trade(symbol, action)
            
            # نمایش نتیجه
            print(f"📊 Entry: ${result['entry_price']:.4f}")
            print(f"📊 Exit: ${result['exit_price']:.4f}")
            print(f"💰 PnL: ${result['pnl']:.2f}")
            print(f"📝 Reason: {result['reason']}")
            
            if result['successful']:
                print("✅ Trade successful")
            else:
                print("❌ Trade unsuccessful")
            
            # بررسی اهداف
            self.check_targets()
            
            # نمایش وضعیت هر 3 معامله
            if (i + 1) % 3 == 0:
                self.display_status()
            
            # بررسی پایان زودهنگام
            if all(self.targets_achieved.values()):
                print("\n🎉 ALL TARGETS ACHIEVED! Ending demo early.")
                break
            
            time.sleep(1)  # کمی استراحت
        
        # گزارش نهایی
        self.display_final_report()
    
    def display_final_report(self):
        """گزارش نهایی"""
        
        print("\n" + "="*60)
        print("🎯 FINAL REPORT")
        print("="*60)
        
        self.display_status()
        
        print(f"\n📋 SUMMARY:")
        return_percent = (self.total_pnl / self.initial_capital) * 100
        print(f"   Return: {return_percent:.2f}%")
        
        targets_achieved = sum(self.targets_achieved.values())
        print(f"   Targets Achieved: {targets_achieved}/3")
        
        if targets_achieved == 3:
            print(f"   🎉 EXCELLENT: All targets achieved!")
        elif targets_achieved >= 2:
            print(f"   👍 GOOD: Most targets achieved")
        elif targets_achieved >= 1:
            print(f"   📊 FAIR: Some targets achieved")
        else:
            print(f"   ⚠️ NEEDS IMPROVEMENT: No targets achieved")
        
        # ذخیره گزارش
        self.save_report()
        
        print("="*60)
    
    def save_report(self):
        """ذخیره گزارش"""
        
        report = {
            "demo_info": {
                "timestamp": datetime.now().isoformat(),
                "initial_capital": self.initial_capital,
                "final_capital": self.current_capital,
                "total_pnl": self.total_pnl,
                "return_percent": (self.total_pnl / self.initial_capital) * 100
            },
            "targets": {
                "daily": {"target": self.daily_target, "achieved": self.daily_pnl, "reached": self.targets_achieved["daily"]},
                "weekly": {"target": self.weekly_target, "achieved": self.weekly_pnl, "reached": self.targets_achieved["weekly"]},
                "monthly": {"target": self.monthly_target, "achieved": self.monthly_pnl, "reached": self.targets_achieved["monthly"]}
            },
            "performance": {
                "total_trades": self.trades_executed,
                "successful_trades": self.successful_trades,
                "success_rate": (self.successful_trades / max(self.trades_executed, 1)) * 100,
                "targets_achieved": sum(self.targets_achieved.values())
            }
        }
        
        filename = f"simple_profit_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"✅ Report saved to {filename}")

# اجرای نمایشی
if __name__ == "__main__":
    print("🎯 SIMPLE PROFIT TARGET DEMO")
    print("Target: $5 daily per symbol with $1000 capital")
    print("=" * 60)
    
    # ایجاد سیستم
    demo = SimpleProfitDemo()
    
    # نمایش وضعیت اولیه
    demo.display_status()
    
    print("\n🚀 Ready to start demo!")
    print("Commands:")
    print("  demo.run_demo_session(10) - Run 10 trades")
    print("  demo.display_status() - Show current status")
    
    # اجرای نمایشی سریع
    print("\n📊 Running quick demo with 5 trades...")
    demo.run_demo_session(5) 