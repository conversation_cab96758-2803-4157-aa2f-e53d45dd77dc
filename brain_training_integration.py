"""
🔗 Advanced Brain Integration Module
ماژول ادغام مغز پیشرفته با آموزش واقعی

این ماژول مغز پیشرفته را به سیستم آموزش واقعی متصل می‌کند.
"""

import os
import sys
from typing import Dict, Any, List

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import advanced brain
try:
    from advanced_brain_training_system import AdaptiveNeuralBrain, AdvancedTrainingConfig
    ADVANCED_BRAIN_AVAILABLE = True
except ImportError:
    print("⚠️ Advanced brain not available")
    ADVANCED_BRAIN_AVAILABLE = False

# Import real training
try:
    from real_model_trainer import RealModelTrainer, RealModelInfo
    REAL_TRAINER_AVAILABLE = True
except ImportError:
    print("⚠️ Real model trainer not available")
    REAL_TRAINER_AVAILABLE = False

class BrainTrainingIntegration:
    """🔗 ادغام مغز با آموزش"""
    
    def __init__(self):
        self.brain_config = AdvancedTrainingConfig() if ADVANCED_BRAIN_AVAILABLE else None
        self.brain = AdaptiveNeuralBrain(self.brain_config) if ADVANCED_BRAIN_AVAILABLE else None
        self.trainer = RealModelTrainer() if REAL_TRAINER_AVAILABLE else None
        
        self.integration_ready = ADVANCED_BRAIN_AVAILABLE and REAL_TRAINER_AVAILABLE
        
        if self.integration_ready:
            print("✅ Brain-Training integration ready")
        else:
            print("❌ Brain-Training integration not ready")
            if not ADVANCED_BRAIN_AVAILABLE:
                print("   - Advanced brain not available")
            if not REAL_TRAINER_AVAILABLE:
                print("   - Real trainer not available")
    
    def run_integrated_training(self, models_to_train: List[Dict[str, Any]]):
        """اجرای آموزش ادغام‌شده"""
        if not self.integration_ready:
            print("❌ Integration not ready")
            return False
        
        print("🚀 Starting integrated brain-guided training...")
        
        for model_info in models_to_train:
            # Convert to RealModelInfo
            real_model = RealModelInfo(
                name=model_info["name"],
                category=model_info["category"],
                priority=model_info["priority"],
                trainer_module=model_info.get("trainer_module", "training.train_" + model_info["category"]),
                trainer_class=model_info.get("trainer_class", model_info["category"].capitalize() + "Trainer"),
                config_class=model_info.get("config_class", model_info["category"].capitalize() + "TrainingConfig"),
                data_requirements=model_info.get("data_requirements", ["price_data"]),
                estimated_time_hours=model_info.get("estimated_time_hours", 1.0),
                memory_gb=model_info.get("memory_gb", 2.0)
            )
            
            # Brain decision
            system_resources = {
                "available_memory_gb": 16.0,
                "cpu_usage": 30.0,
                "gpu_usage": 20.0
            }
            
            decision = self.brain.analyze_training_situation(
                [model_info],
                system_resources,
                []
            )
            
            print(f"🧠 Brain decision for {model_info['name']}:")
            print(f"   Action: {decision['action']}")
            print(f"   Reasoning: {decision['reasoning']}")
            print(f"   Confidence: {decision['confidence']:.3f}")
            
            if decision['action'] == 'train':
                # Train model
                print(f"🚀 Training {model_info['name']} with brain guidance...")
                result = self.trainer.train_model_real(real_model)
                
                # Brain learns from outcome
                self.brain.learn_from_outcome(decision, result)
                
                if result['success']:
                    print(f"✅ {model_info['name']} training successful")
                else:
                    print(f"❌ {model_info['name']} training failed")
            else:
                print(f"⏸️ Brain decided not to train {model_info['name']}")
        
        return True

def main():
    """اجرای ماژول ادغام"""
    integration = BrainTrainingIntegration()
    
    if integration.integration_ready:
        # Test models
        test_models = [
            {"name": "FinBERT", "category": "sentiment", "priority": 1},
            {"name": "LSTM_TimeSeries", "category": "timeseries", "priority": 1}
        ]
        
        integration.run_integrated_training(test_models)
    else:
        print("❌ Integration not ready. Please ensure both advanced_brain_training_system.py and real_model_trainer.py are available.")

if __name__ == "__main__":
    main()
