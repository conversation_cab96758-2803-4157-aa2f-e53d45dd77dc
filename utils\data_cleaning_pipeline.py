import pandas as pd
from typing import Optional, List
from .data_cleaning import (
    drop_missing,
    fill_missing,
    remove_outliers,
    filter_invalid,
    drop_duplicates,
    impute_missing_statistical,
    spike_correction,
    contextual_outlier_cleaning,
    anomaly_detection_cleaning,
    adaptive_outlier_cleaning,
    remove_holiday_illiquid,
    corporate_action_cleaning,
    microstructure_noise_cleaning,
    ml_based_cleaning,
    adaptive_cleaning_by_reward,
    explainable_cleaning,
    testable_cleaning,
    scenario_based_cleaning,
    feature_driven_cleaning,
    imitation_cleaning,
    cross_validation_cleaning
)

  
def advanced_data_cleaning(
    df: pd.DataFrame,
    cleaning_sets: Optional[List[str]] = None,
    # --- Set 1: Basic Cleaning ---
    dropna_cols: Optional[List[str]] = None,
    fillna_value: Optional[float] = None,
    duplicate_subset: Optional[List[str]] = None,
    filter_cols: Optional[List[str]] = None,
    min_val: Optional[float] = None,
    max_val: Optional[float] = None,
    outlier_cols: Optional[List[str]] = None,
    outlier_z: float = 3.0,
    impute_col: Optional[str] = None,
    impute_method: str = 'mean',
    # --- Set 2: Advanced Financial Cleaning ---
    spike_cols: Optional[List[str]] = None,
    spike_threshold: float = 5.0,
    contextual_outlier_cols: Optional[List[str]] = None,
    contextual_window: int = 10,
    # --- Set 3: Smart & Adaptive Cleaning ---
    anomaly_cols: Optional[List[str]] = None,
    anomaly_method: str = 'isolation_forest',
    adaptive_vol_cols: Optional[List[str]] = None,
    adaptive_z: float = 3.0,
    # --- Set 4: Explainable & Testable ---
    log_changes: bool = False,
    report: bool = False
) -> pd.DataFrame:
    """
    اجرای cleaning ترکیبی و ماژولار روی داده‌های مالی بر اساس cleaning sets.
    cleaning_sets: لیست نام ست‌های cleaning جهت اجرا (مثلاً ['basic', 'advanced'])
    سایر پارامترها بر اساس ست‌ها قابل تنظیم است.
    """
    # ست ۱: Basic & Essential
    if (not cleaning_sets) or ('basic' in cleaning_sets):
        df = drop_missing(df, columns=dropna_cols)
        if fillna_value is not None:
            df = fill_missing(df, value=fillna_value)
        df = drop_duplicates(df, subset=duplicate_subset)
        df = filter_invalid(df, columns=filter_cols, min_val=min_val, max_val=max_val)
        df = remove_outliers(df, columns=outlier_cols, z_thresh=outlier_z)
        if impute_col:
            df = impute_missing_statistical(df, impute_col, method=impute_method)

    # ست ۲: Advanced Financial Cleaning
    if cleaning_sets and 'advanced' in cleaning_sets:
        if spike_cols:
            df = spike_correction(df, columns=spike_cols, threshold=spike_threshold)
        if contextual_outlier_cols:
            df = contextual_outlier_cleaning(
                df,
                columns=contextual_outlier_cols,
                window=contextual_window
            )
        # cleaning مالی تخصصی عملیاتی
        # مثال: تعدیل قیمت با adjustment dict (در صورت وجود)
        adjustment_dict = None
        if hasattr(df, 'corporate_action_adjustments'):
            adjustment_dict = getattr(df, 'corporate_action_adjustments')
        df = remove_holiday_illiquid(df)
        df = corporate_action_cleaning(df, adjustment_dict=adjustment_dict)
        df = microstructure_noise_cleaning(df)

    # ست ۳: Smart & Adaptive & Explainable Cleaning
    if cleaning_sets and 'smart' in cleaning_sets:
        if anomaly_cols:
            df = anomaly_detection_cleaning(
                df,
                columns=anomaly_cols,
                method=anomaly_method
            )
        if adaptive_vol_cols:
            df = adaptive_outlier_cleaning(
                df,
                columns=adaptive_vol_cols,
                z_thresh=adaptive_z
            )
        # cleaning هوشمند و تطبیقی عملیاتی
        df = ml_based_cleaning(df, columns=outlier_cols)
        df = adaptive_cleaning_by_reward(df)
        # cleaning explainable و تست‌پذیر با لاگ
        cleaning_log = []
        df = explainable_cleaning(df, log=cleaning_log)
        df = testable_cleaning(df, test_func=lambda d: d.isna().sum().sum() < len(d))
        if log_changes:
            print('Cleaning log:', cleaning_log)

    # ست ۵ تا ۸: cleaning سناریومحور، feature-driven، imitation، cross-validation
    if cleaning_sets and any(s in cleaning_sets for s in ['scenario', 'feature', 'imitation', 'cv']):
        # cleaning سناریومحور عملیاتی (در صورت تعریف تابع سناریو)
        scenario_func = getattr(df, 'scenario_func', None)
        df = scenario_based_cleaning(df, scenario_func=scenario_func)
        # cleaning feature-driven عملیاتی (در صورت تعریف تابع feature)
        feature_func = getattr(df, 'feature_func', None)
        df = feature_driven_cleaning(df, feature_func=feature_func)
        # cleaning imitation عملیاتی (در صورت تعریف تابع imitation)
        imitation_func = getattr(df, 'imitation_func', None)
        df = imitation_cleaning(df, imitation_func=imitation_func)
        # cleaning cross-validation عملیاتی (در صورت تعریف تابع cv)
        cv_func = getattr(df, 'cv_func', None)
        df = cross_validation_cleaning(df, cv_func=cv_func)

    # ست ۴: Explainable & Testable (لاگ و گزارش)
    if log_changes:
        # TODO: پیاده‌سازی logging تغییرات cleaning
        pass
    if report:
        # TODO: تولید گزارش cleaning (summary, visual)
        pass

    # مرحله نهایی: حذف یا مقداردهی NaNهای باقی‌مانده (در ستون‌های کلیدی)
    # اگر fillna_value داده شده، مقداردهی؛ در غیر این صورت حذف ردیف‌های NaN
    if fillna_value is not None:
        df = fill_missing(df, value=fillna_value)
    else:
        df = drop_missing(df)
    return df

# مثال استفاده:
# df_clean = advanced_data_cleaning(
#     df,
#     cleaning_sets=['basic', 'advanced'],
#     dropna_cols=['close'],
#     spike_cols=['close']
# )
