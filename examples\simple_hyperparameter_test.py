"""
Simple Auto-Hyperparameter Tuning Test
تست ساده سیستم بهینه‌سازی خودکار پارامترها
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import random
import json
from typing import Dict

class SimpleHyperparameterTuner:
    """تنظیم‌کننده ساده پارامترها"""
    
    def __init__(self):
        self.results = []
    
    def optimize_rl_parameters(self, evaluation_function) -> Dict:
        """بهینه‌سازی پارامترهای RL"""
        print("Optimizing RL parameters...")
        
        best_params = None
        best_score = -float('inf')
        
        # تست 10 ترکیب مختلف
        for i in range(10):
            params = {
                'learning_rate': random.uniform(0.001, 0.1),
                'discount_factor': random.uniform(0.9, 0.99),
                'epsilon_decay': random.uniform(0.99, 0.999),
                'epsilon_min': random.uniform(0.01, 0.1),
                'batch_size': random.choice([16, 32, 64, 128])
            }
            
            score = evaluation_function(params)
            
            if score > best_score:
                best_score = score
                best_params = params
            
            print(f"  Test {i+1}: score={score:.6f}")
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'total_evaluations': 10
        }
    
    def optimize_prediction_parameters(self, evaluation_function) -> Dict:
        """بهینه‌سازی پارامترهای پیش‌بینی"""
        print("Optimizing Prediction parameters...")
        
        best_params = None
        best_score = -float('inf')
        
        # تست 8 ترکیب مختلف
        for i in range(8):
            params = {
                'n_estimators': random.choice([50, 100, 200, 300]),
                'max_depth': random.choice([5, 8, 10, 15, 20]),
                'min_samples_split': random.choice([2, 5, 10]),
                'min_samples_leaf': random.choice([1, 2, 4]),
                'max_features': random.choice(["sqrt", "log2", "auto"])
            }
            
            score = evaluation_function(params)
            
            if score > best_score:
                best_score = score
                best_params = params
            
            print(f"  Test {i+1}: score={score:.6f}")
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'total_evaluations': 8
        }

def evaluate_rl_params(params: Dict) -> float:
    """تابع ارزیابی برای پارامترهای RL"""
    learning_rate = params['learning_rate']
    discount_factor = params['discount_factor']
    epsilon_decay = params['epsilon_decay']
    epsilon_min = params['epsilon_min']
    batch_size = params['batch_size']
    
    # شبیه‌سازی عملکرد RL
    score = (
        (1 - learning_rate) * 0.3 +  # کمتر بهتر
        discount_factor * 0.3 +       # بیشتر بهتر
        epsilon_decay * 0.2 +          # بیشتر بهتر
        (1 - epsilon_min) * 0.1 +     # کمتر بهتر
        (batch_size / 128) * 0.1       # متوسط بهتر
    )
    
    # اضافه کردن نویز
    score += random.uniform(-0.05, 0.05)
    
    return max(0, min(1, score))  # محدود کردن بین 0 و 1

def evaluate_prediction_params(params: Dict) -> float:
    """تابع ارزیابی برای پارامترهای پیش‌بینی"""
    n_estimators = params['n_estimators']
    max_depth = params['max_depth']
    min_samples_split = params['min_samples_split']
    min_samples_leaf = params['min_samples_leaf']
    max_features = params['max_features']
    
    # شبیه‌سازی دقت مدل
    base_score = 0.7
    
    # تأثیر پارامترها
    base_score += (n_estimators / 300) * 0.15
    base_score += (max_depth / 20) * 0.1
    base_score -= (min_samples_split / 10) * 0.05
    base_score -= (min_samples_leaf / 4) * 0.03
    
    if max_features == "sqrt":
        base_score += 0.02
    elif max_features == "log2":
        base_score += 0.01
    
    # اضافه کردن نویز
    base_score += random.uniform(-0.03, 0.03)
    
    return max(0, min(1, base_score))  # محدود کردن بین 0 و 1

def main():
    """تست سیستم بهینه‌سازی پارامترها"""
    print("Simple Auto-Hyperparameter Tuning Test")
    print("=" * 45)
    
    # ایجاد تنظیم‌کننده
    tuner = SimpleHyperparameterTuner()
    
    # بهینه‌سازی پارامترهای RL
    print("\n1. RL Parameter Optimization:")
    rl_result = tuner.optimize_rl_parameters(evaluate_rl_params)
    
    print(f"\nRL Optimization Results:")
    print(f"  Best score: {rl_result['best_score']:.6f}")
    print(f"  Best parameters:")
    for param, value in rl_result['best_params'].items():
        if isinstance(value, float):
            print(f"    {param}: {value:.6f}")
        else:
            print(f"    {param}: {value}")
    print(f"  Total evaluations: {rl_result['total_evaluations']}")
    
    # بهینه‌سازی پارامترهای پیش‌بینی
    print(f"\n2. Prediction Parameter Optimization:")
    pred_result = tuner.optimize_prediction_parameters(evaluate_prediction_params)
    
    print(f"\nPrediction Optimization Results:")
    print(f"  Best score: {pred_result['best_score']:.6f}")
    print(f"  Best parameters:")
    for param, value in pred_result['best_params'].items():
        print(f"    {param}: {value}")
    print(f"  Total evaluations: {pred_result['total_evaluations']}")
    
    # ذخیره نتایج
    results = {
        'rl_optimization': rl_result,
        'prediction_optimization': pred_result,
        'summary': {
            'total_evaluations': rl_result['total_evaluations'] + pred_result['total_evaluations'],
            'best_rl_score': rl_result['best_score'],
            'best_prediction_score': pred_result['best_score']
        }
    }
    
    with open('hyperparameter_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n✅ Auto-Hyperparameter Tuning completed successfully!")
    print(f"Results saved to hyperparameter_results.json")

if __name__ == "__main__":
    main() 