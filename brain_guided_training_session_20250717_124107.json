{"session_info": {"start_time": "2025-07-17T12:40:27.395958", "end_time": "2025-07-17T12:41:07.727513", "duration_seconds": 40.331555, "config": {"use_memory_optimization": true, "use_enhanced_replay": true, "use_genetic_optimization": true, "use_continual_learning": true, "use_advanced_backtesting": true, "memory_optimization_level": "aggressive", "replay_buffer_size": 10000, "prioritized_replay": true, "genetic_population_size": 20, "genetic_generations": 10, "ewc_lambda": 0.4, "use_experience_replay": true, "auto_backtest_after_training": true, "backtest_validation_split": 0.2}}, "training_results": {"completed": [{"success": true, "performance": 0.982, "metrics": {"rmse": 0.018000000000000002, "mae": 0.0126, "mape": 1.4400000000000002, "directional_accuracy": 0.85}, "epochs_completed": 50, "best_epoch": 42, "advanced_metrics": {"memory_efficiency": 0.95, "training_stability": 0.9700000000000001, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.95, "sharpe_ratio": 1.805861702924683, "max_drawdown": 0.08334198454025785, "win_rate": 0.6404818956860419}, "model_saved": true, "model_name": "LSTM_TimeSeries", "category": "timeseries", "training_duration": 11.637345, "start_time": "2025-07-17T12:40:43.169860", "end_time": "2025-07-17T12:40:54.807205", "preprocessing_results": {"memory_optimized": true, "genetic_optimization": {"learning_rate": 0.001432702296197948, "batch_size": 64, "hidden_size": 256}, "continual_learning_ready": true}, "advanced_features_applied": {"memory_optimization": true, "genetic_optimization": true, "enhanced_replay": false, "continual_learning": true}}], "failed": [{"success": false, "error": "Prerequisites not met", "details": {"trainer_available": true, "data_available": true, "dependencies_installed": true, "memory_sufficient": false, "ready_to_train": false}, "model_name": "PPO_Agent"}, {"success": false, "error": "Prerequisites not met", "details": {"trainer_available": true, "data_available": true, "dependencies_installed": true, "memory_sufficient": false, "ready_to_train": false}, "model_name": "GRU_TimeSeries"}, {"success": false, "error": "Prerequisites not met", "details": {"trainer_available": true, "data_available": true, "dependencies_installed": true, "memory_sufficient": false, "ready_to_train": false}, "model_name": "DQN_Agent"}, {"success": false, "error": "Prerequisites not met", "details": {"trainer_available": true, "data_available": true, "dependencies_installed": true, "memory_sufficient": false, "ready_to_train": false}, "model_name": "CryptoBERT"}, {"success": false, "error": "Prerequisites not met", "details": {"trainer_available": true, "data_available": true, "dependencies_installed": true, "memory_sufficient": false, "ready_to_train": false}, "model_name": "FinBERT"}], "success_rate": 16.666666666666664}, "brain_analytics": {"decisions_made": 6, "performance_memory": {"PPO_Agent": [{"success": false, "performance": 0.0, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 12:40:31.058723"}], "GRU_TimeSeries": [{"success": false, "performance": 0.0, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 12:40:35.537149"}], "DQN_Agent": [{"success": false, "performance": 0.0, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 12:40:40.015009"}], "LSTM_TimeSeries": [{"success": true, "performance": 0.982, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 12:40:56.821789"}], "CryptoBERT": [{"success": false, "performance": 0.0, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 12:41:01.273061"}], "FinBERT": [{"success": false, "performance": 0.0, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 12:41:05.711020"}]}, "decision_history": [{"decision": {"action": "train", "model": "RealModelInfo(name='PPO_Agent', category='reinforcement_learning', priority=1, trainer_module='training.train_rl', trainer_class='PearlRLTrainer', config_class='RLTrainingConfig', data_requirements=['trading_environment', 'price_data'], estimated_time_hours=2.5, memory_gb=2.2)", "reasoning": "انتخاب PPO_Agent با امتیاز 1.138", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": false, "timestamp": "2025-07-17T12:40:31.058723"}, {"decision": {"action": "train", "model": "RealModelInfo(name='GRU_TimeSeries', category='timeseries', priority=1, trainer_module='training.train_timeseries', trainer_class='PearlTimeSeriesTrainer', config_class='TimeSeriesTrainingConfig', data_requirements=['price_data', 'technical_indicators'], estimated_time_hours=0.8, memory_gb=1.8)", "reasoning": "انتخاب GRU_TimeSeries با امتیاز 1.096", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": false, "timestamp": "2025-07-17T12:40:35.537149"}, {"decision": {"action": "train", "model": "RealModelInfo(name='DQN_Agent', category='reinforcement_learning', priority=1, trainer_module='training.train_rl', trainer_class='PearlRLTrainer', config_class='RLTrainingConfig', data_requirements=['trading_environment', 'price_data'], estimated_time_hours=3.0, memory_gb=2.5)", "reasoning": "انتخاب DQN_Agent با امتیاز 1.129", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": false, "timestamp": "2025-07-17T12:40:40.015009"}, {"decision": {"action": "train", "model": "RealModelInfo(name='LSTM_TimeSeries', category='timeseries', priority=1, trainer_module='training.train_timeseries', trainer_class='PearlTimeSeriesTrainer', config_class='TimeSeriesTrainingConfig', data_requirements=['price_data', 'technical_indicators'], estimated_time_hours=1.0, memory_gb=2.0)", "reasoning": "انتخاب LSTM_TimeSeries با امتیاز 1.100", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": true, "timestamp": "2025-07-17T12:40:56.821789"}, {"decision": {"action": "train", "model": "RealModelInfo(name='CryptoBERT', category='sentiment', priority=2, trainer_module='training.train_sentiment', trainer_class='PearlSentimentTrainer', config_class='SentimentTrainingConfig', data_requirements=['crypto_news', 'sentiment_labels'], estimated_time_hours=1.5, memory_gb=2.8)", "reasoning": "انتخاب CryptoBERT با امتیاز 0.949", "confidence": 0.9492308807373047, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": false, "timestamp": "2025-07-17T12:41:01.273061"}, {"decision": {"action": "train", "model": "RealModelInfo(name='FinBERT', category='sentiment', priority=2, trainer_module='training.train_sentiment', trainer_class='PearlSentimentTrainer', config_class='SentimentTrainingConfig', data_requirements=['financial_news', 'sentiment_labels'], estimated_time_hours=2.0, memory_gb=3.0)", "reasoning": "انتخاب FinBERT با امتیاز 0.937", "confidence": 0.93748046875, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": false, "timestamp": "2025-07-17T12:41:05.711020"}]}, "advanced_features_summary": {"memory_optimization_available": true, "enhanced_replay_available": true, "genetic_evolution_available": true, "continual_learning_available": true, "backtesting_available": true}}