#!/usr/bin/env python3
"""
🧪 تست سیستم با توکن Hugging Face
تست کامل با دسترسی API
"""

from simple_financial_models import SimpleFinancialAnalyzer
import time

def test_with_full_token():
    """تست با توکن کامل"""
    print("🚀 تست با توکن Hugging Face")
    print("=" * 40)
    
    # توکن کامل
    hf_token = "*************************************"
    
    # راه‌اندازی با توکن
    analyzer = SimpleFinancialAnalyzer(hf_token=hf_token)
    
    # تست تحلیل تکی
    print("\n📰 تست تحلیل احساس:")
    test_texts = [
        "Apple stock jumps 15% on record quarterly earnings",
        "Market crashes amid economic uncertainty and inflation fears",
        "Stable trading observed with neutral market sentiment",
        "Tesla announces breakthrough in battery technology",
        "Banking sector faces regulatory pressures"
    ]
    
    results = []
    for text in test_texts:
        start_time = time.time()
        result = analyzer.analyze_sentiment(text)
        end_time = time.time()
        
        results.append(result)
        
        if "error" not in result:
            print(f"✅ {result['sentiment'].upper()} ({result['confidence']:.2f}) - {text[:50]}...")
            print(f"   زمان: {end_time - start_time:.2f}s، روش: {result['method']}")
        else:
            print(f"❌ خطا: {result['error']}")
    
    return analyzer, results

def test_market_analysis(analyzer):
    """تست تحلیل بازار"""
    print("\n📈 تست تحلیل بازار:")
    print("-" * 30)
    
    # اخبار نمونه
    financial_news = [
        "Federal Reserve signals potential rate cuts",
        "Corporate earnings exceed expectations across sectors",
        "Economic growth shows strong momentum",
        "Market volatility increases on geopolitical tensions",
        "Technology sector leads market rally",
        "Oil prices stabilize after recent volatility",
        "Consumer confidence reaches new highs",
        "Inflation data shows encouraging trends"
    ]
    
    start_time = time.time()
    market_sentiment = analyzer.get_market_sentiment(financial_news)
    end_time = time.time()
    
    print(f"🔄 تحلیل {len(financial_news)} خبر در {end_time - start_time:.2f} ثانیه")
    
    if "error" not in market_sentiment:
        print(f"🎯 حالت کلی بازار: {market_sentiment['overall_sentiment']}")
        print(f"📊 مثبت: {market_sentiment['positive_ratio']:.1%}")
        print(f"📊 منفی: {market_sentiment['negative_ratio']:.1%}")
        print(f"📊 خنثی: {market_sentiment['neutral_ratio']:.1%}")
        print(f"🎲 میانگین اطمینان: {market_sentiment['average_confidence']:.2f}")
        print(f"⚙️ روش استفاده شده: {market_sentiment['method_used']}")
    else:
        print(f"❌ خطا: {market_sentiment['error']}")
    
    return market_sentiment

def test_trading_signals(analyzer):
    """تست سیگنال‌های معاملاتی"""
    print("\n🚦 تست سیگنال‌های معاملاتی:")
    print("-" * 40)
    
    scenarios = [
        {
            "name": "سناریو صعودی",
            "news": [
                "Strong corporate earnings boost investor confidence",
                "Economic indicators point to sustained growth",
                "Central bank maintains supportive monetary policy"
            ],
            "price_trend": "up"
        },
        {
            "name": "سناریو نزولی",
            "news": [
                "Market concerns grow over inflation pressures",
                "Corporate guidance disappoints investors",
                "Economic data shows signs of slowing"
            ],
            "price_trend": "down"
        },
        {
            "name": "سناریو مختلط",
            "news": [
                "Mixed economic signals create uncertainty",
                "Some sectors show strength while others lag",
                "Market sentiment remains cautious"
            ],
            "price_trend": "neutral"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📈 {scenario['name']}:")
        signal = analyzer.generate_trading_signal(scenario['news'], scenario['price_trend'])
        print(f"🎯 سیگنال توصیه شده: {signal}")
    
    return True

def performance_benchmark(analyzer):
    """بنچمارک عملکرد"""
    print("\n⚡ بنچمارک عملکرد:")
    print("-" * 25)
    
    # تست سرعت
    test_texts = [
        "Stock market shows positive momentum",
        "Economic data reveals mixed signals",
        "Corporate earnings meet expectations",
        "Market volatility increases significantly",
        "Technology sector leads market gains"
    ] * 10  # 50 متن کل
    
    start_time = time.time()
    results = analyzer.analyze_multiple(test_texts)
    end_time = time.time()
    
    total_time = end_time - start_time
    texts_per_second = len(test_texts) / total_time
    
    print(f"📊 {len(test_texts)} متن در {total_time:.2f} ثانیه")
    print(f"🚀 سرعت: {texts_per_second:.1f} متن/ثانیه")
    
    # بررسی دقت
    successful = sum(1 for r in results if "error" not in r)
    success_rate = successful / len(results) * 100
    
    print(f"✅ نرخ موفقیت: {success_rate:.1f}%")
    
    return {
        "total_time": total_time,
        "texts_per_second": texts_per_second,
        "success_rate": success_rate
    }

def main():
    """تست اصلی"""
    print("🤖 تست جامع سیستم مدل‌های مالی با توکن کامل")
    print("=" * 60)
    
    # تست اصلی
    analyzer, results = test_with_full_token()
    
    # بررسی که سیستم کار می‌کند
    if any("error" not in r for r in results):
        print("✅ سیستم با موفقیت کار می‌کند")
        
        # تست‌های تکمیلی
        test_market_analysis(analyzer)
        test_trading_signals(analyzer)
        perf_stats = performance_benchmark(analyzer)
        
        print("\n" + "=" * 60)
        print("🎉 همه تست‌ها با موفقیت انجام شد!")
        print(f"📊 خلاصه عملکرد:")
        print(f"   - سرعت: {perf_stats['texts_per_second']:.1f} متن/ثانیه")
        print(f"   - نرخ موفقیت: {perf_stats['success_rate']:.1f}%")
        print("✅ سیستم آماده ادغام با پروژه معاملاتی است")
        
        return True
    else:
        print("❌ سیستم مشکل دارد")
        return False

if __name__ == "__main__":
    main() 