"""
مثال عملی سیستم یکپارچه معاملاتی
Practical Example of Unified Trading System
"""

import pandas as pd
import numpy as np
import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from models.unified_trading_system import UnifiedTradingSystem
from data.preprocessor import DataPreprocessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UnifiedSystemDemo:
    """مثال عملی سیستم یکپارچه معاملاتی"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.data_dir = self.project_root / "data"
        self.results_dir = self.project_root / "demo_results"
        
        # ایجاد پوشه نتایج
        self.results_dir.mkdir(exist_ok=True)
        
        # سیستم یکپارچه
        self.unified_system = None
        
        logger.info("🚀 مثال سیستم یکپارچه معاملاتی آماده شد")
    
    def create_sample_data(self, symbol: str = "EURUSD", days: int = 30) -> pd.DataFrame:
        """ایجاد داده‌های نمونه برای تست"""
        
        logger.info(f"📊 ایجاد داده‌های نمونه برای {symbol} ({days} روز)")
        
        # ایجاد بازه زمانی
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        dates = pd.date_range(start=start_date, end=end_date, freq='H')
        
        # ایجاد قیمت‌های واقع‌گرایانه
        np.random.seed(42)
        
        if symbol == "EURUSD":
            base_price = 1.1000
            volatility = 0.0008
        elif symbol == "GBPUSD":
            base_price = 1.2500
            volatility = 0.0010
        elif symbol == "USDJPY":
            base_price = 150.00
            volatility = 0.0012
        else:
            base_price = 1.0000
            volatility = 0.0008
        
        # تولید قیمت‌ها با Random Walk
        prices = [base_price]
        for i in range(1, len(dates)):
            change = np.random.normal(0, volatility)
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        # ایجاد OHLC
        df = pd.DataFrame({
            'datetime': dates,
            'open': prices,
            'close': prices
        })
        
        # محاسبه High و Low
        df['high'] = df['close'] * (1 + np.abs(np.random.normal(0, volatility/2, len(df))))
        df['low'] = df['close'] * (1 - np.abs(np.random.normal(0, volatility/2, len(df))))
        
        # تنظیم Open برای کندل بعدی
        df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
        
        # Volume تصادفی
        df['volume'] = np.random.randint(1000, 50000, len(df))
        
        return df
    
    def load_real_data(self, symbol: str, timeframe: str = "H1") -> pd.DataFrame:
        """بارگذاری داده‌های واقعی در صورت وجود"""
        
        try:
            symbol_dir = self.data_dir / symbol
            
            if timeframe == "H1":
                file_path = symbol_dir / "H1.csv"
            elif timeframe == "H4":
                file_path = symbol_dir / "H4.csv"
            elif timeframe == "D1":
                file_path = symbol_dir / "D1.csv"
            else:
                file_path = symbol_dir / "H1.csv"
            
            if file_path.exists():
                logger.info(f"📁 بارگذاری داده‌های واقعی از {file_path}")
                df = pd.read_csv(file_path)
                
                # پردازش اولیه
                preprocessor = DataPreprocessor()
                df = preprocessor.preprocess(df)
                
                return df.tail(1000)  # آخرین 1000 رکورد
            else:
                logger.warning(f"⚠️ فایل {file_path} یافت نشد، استفاده از داده‌های نمونه")
                return self.create_sample_data(symbol)
                
        except Exception as e:
            logger.error(f"❌ خطا در بارگذاری داده‌ها: {str(e)}")
            return self.create_sample_data(symbol)
    
    def initialize_system(self, symbols: list = None):
        """مقداردهی اولیه سیستم یکپارچه"""
        
        if symbols is None:
            symbols = ["EURUSD", "GBPUSD", "USDJPY"]
        
        logger.info(f"🔧 مقداردهی اولیه سیستم برای نمادها: {symbols}")
        
        try:
            # ایجاد سیستم یکپارچه
            self.unified_system = UnifiedTradingSystem(
                config_path="config.yaml",
                db_path="demo_unified_system.db"
            )
            
            # پیکربندی مدل‌های RL
            model_configs = [
                {
                    'model_type': 'ppo',
                    'params': {
                        'learning_rate': 0.0003,
                        'n_steps': 1024,
                        'batch_size': 64
                    }
                },
                {
                    'model_type': 'sac',
                    'params': {
                        'learning_rate': 0.0003,
                        'buffer_size': 50000
                    }
                }
            ]
            
            # مقداردهی اولیه مدل‌ها
            self.unified_system.initialize_models(symbols, model_configs)
            
            logger.info("✅ سیستم یکپارچه با موفقیت مقداردهی شد")
            
        except Exception as e:
            logger.error(f"❌ خطا در مقداردهی اولیه: {str(e)}")
            raise
    
    def demonstrate_single_signal(self, symbol: str = "EURUSD", timeframe: str = "H1"):
        """نمایش تولید یک سیگنال یکپارچه"""
        
        logger.info(f"\n🎯 نمایش تولید سیگنال یکپارچه برای {symbol}")
        logger.info("=" * 60)
        
        try:
            # بارگذاری داده‌ها
            market_data = self.load_real_data(symbol, timeframe)
            
            if market_data.empty:
                logger.error("❌ داده‌ای برای پردازش یافت نشد")
                return None
            
            logger.info(f"📊 داده‌های بارگذاری شده: {len(market_data)} رکورد")
            logger.info(f"📅 بازه زمانی: {market_data.index[0]} تا {market_data.index[-1]}")
            
            # تولید سیگنال یکپارچه
            logger.info("🔄 در حال تولید سیگنال یکپارچه...")
            
            unified_signal = self.unified_system.get_unified_signal(
                symbol=symbol,
                timeframe=timeframe,
                market_data=market_data
            )
            
            # نمایش نتایج
            self.display_signal_details(unified_signal)
            
            return unified_signal
            
        except Exception as e:
            logger.error(f"❌ خطا در تولید سیگنال: {str(e)}")
            return None
    
    def display_signal_details(self, signal):
        """نمایش جزئیات سیگنال"""
        
        logger.info("\n📋 جزئیات سیگنال یکپارچه:")
        logger.info("-" * 40)
        
        # اطلاعات کلی
        logger.info(f"🏷️  نماد: {signal.symbol}")
        logger.info(f"⏰ تایم‌فریم: {signal.timeframe}")
        logger.info(f"🕐 زمان: {signal.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # سیگنال نهایی
        logger.info(f"\n🎯 سیگنال نهایی:")
        logger.info(f"   📈 اقدام: {signal.final_action.upper()}")
        logger.info(f"   🎲 اطمینان: {signal.final_confidence:.2%}")
        logger.info(f"   🔧 روش ترکیب: {signal.combination_method}")
        
        # جزئیات مدل‌ها
        logger.info(f"\n🤖 جزئیات مدل RL:")
        logger.info(f"   📊 اقدام: {signal.rl_action}")
        logger.info(f"   🎯 اطمینان: {signal.rl_confidence:.2%}")
        logger.info(f"   🔧 مدل استفاده شده: {signal.rl_model_used}")
        
        logger.info(f"\n🔮 جزئیات مدل Plutus:")
        logger.info(f"   📈 روند: {signal.plutus_trend}")
        logger.info(f"   🎯 اطمینان: {signal.plutus_confidence:.2%}")
        
        # زمینه بازار
        logger.info(f"\n🌍 زمینه بازار:")
        logger.info(f"   📊 رژیم: {signal.market_regime}")
        logger.info(f"   📈 سطح نوسانات: {signal.volatility_level}")
        logger.info(f"   ⚠️ امتیاز ریسک: {signal.risk_score:.2%}")
        
        # مدیریت ریسک
        logger.info(f"\n💰 مدیریت ریسک:")
        logger.info(f"   📏 اندازه موقعیت: {signal.position_size:.2%}")
        logger.info(f"   🛑 حد ضرر: {signal.stop_loss:.2%}")
        logger.info(f"   🎯 حد سود: {signal.take_profit:.2%}")
        
        # دلیل تصمیم
        if signal.reasoning:
            logger.info(f"\n💭 دلیل تصمیم:")
            logger.info(f"   {signal.reasoning}")
    
    def demonstrate_multiple_symbols(self, symbols: list = None, timeframe: str = "H1"):
        """نمایش تولید سیگنال برای چندین نماد"""
        
        if symbols is None:
            symbols = ["EURUSD", "GBPUSD", "USDJPY"]
        
        logger.info(f"\n🔄 تولید سیگنال برای {len(symbols)} نماد")
        logger.info("=" * 60)
        
        signals = {}
        
        for symbol in symbols:
            try:
                logger.info(f"\n📊 پردازش {symbol}...")
                
                signal = self.demonstrate_single_signal(symbol, timeframe)
                if signal:
                    signals[symbol] = signal
                    
                    # خلاصه سریع
                    logger.info(f"✅ {symbol}: {signal.final_action.upper()} "
                               f"(اطمینان: {signal.final_confidence:.1%})")
                
            except Exception as e:
                logger.error(f"❌ خطا در پردازش {symbol}: {str(e)}")
        
        # خلاصه کلی
        self.display_portfolio_summary(signals)
        
        return signals
    
    def display_portfolio_summary(self, signals: dict):
        """نمایش خلاصه پرتفوی"""
        
        if not signals:
            logger.warning("⚠️ هیچ سیگنالی برای نمایش وجود ندارد")
            return
        
        logger.info(f"\n📊 خلاصه پرتفوی ({len(signals)} نماد)")
        logger.info("=" * 50)
        
        # آمار کلی
        buy_signals = sum(1 for s in signals.values() if s.final_action == 'buy')
        sell_signals = sum(1 for s in signals.values() if s.final_action == 'sell')
        hold_signals = sum(1 for s in signals.values() if s.final_action == 'hold')
        
        logger.info(f"📈 سیگنال‌های خرید: {buy_signals}")
        logger.info(f"📉 سیگنال‌های فروش: {sell_signals}")
        logger.info(f"⏸️  سیگنال‌های نگهداری: {hold_signals}")
        
        # میانگین اطمینان
        avg_confidence = np.mean([s.final_confidence for s in signals.values()])
        logger.info(f"🎯 میانگین اطمینان: {avg_confidence:.1%}")
        
        # بالاترین اطمینان
        best_signal = max(signals.items(), key=lambda x: x[1].final_confidence)
        logger.info(f"🏆 بهترین سیگنال: {best_signal[0]} "
                   f"({best_signal[1].final_action}, {best_signal[1].final_confidence:.1%})")
        
        # توزیع ریسک
        avg_risk = np.mean([s.risk_score for s in signals.values()])
        logger.info(f"⚠️ میانگین ریسک: {avg_risk:.1%}")
    
    def save_results(self, signals: dict, filename: str = None):
        """ذخیره نتایج در فایل"""
        
        if filename is None:
            filename = f"unified_signals_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        filepath = self.results_dir / filename
        
        try:
            # تبدیل سیگنال‌ها به فرمت قابل ذخیره
            results = {}
            for symbol, signal in signals.items():
                results[symbol] = {
                    'symbol': signal.symbol,
                    'timeframe': signal.timeframe,
                    'timestamp': signal.timestamp.isoformat(),
                    'final_action': signal.final_action,
                    'final_confidence': signal.final_confidence,
                    'rl_component': {
                        'action': signal.rl_action,
                        'confidence': signal.rl_confidence,
                        'model_used': signal.rl_model_used
                    },
                    'plutus_component': {
                        'trend': signal.plutus_trend,
                        'confidence': signal.plutus_confidence,
                        'prediction': signal.plutus_prediction
                    },
                    'market_context': {
                        'regime': signal.market_regime,
                        'volatility_level': signal.volatility_level,
                        'risk_score': signal.risk_score
                    },
                    'risk_management': {
                        'position_size': signal.position_size,
                        'stop_loss': signal.stop_loss,
                        'take_profit': signal.take_profit
                    },
                    'reasoning': signal.reasoning
                }
            
            # ذخیره در فایل
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"💾 نتایج در {filepath} ذخیره شد")
            
        except Exception as e:
            logger.error(f"❌ خطا در ذخیره نتایج: {str(e)}")
    
    def cleanup(self):
        """پاکسازی منابع"""
        
        try:
            if self.unified_system:
                self.unified_system.stop_system()
            
            logger.info("🧹 پاکسازی منابع انجام شد")
            
        except Exception as e:
            logger.error(f"❌ خطا در پاکسازی: {str(e)}")


def main():
    """تابع اصلی مثال"""
    
    logger.info("🚀 شروع مثال سیستم یکپارچه معاملاتی")
    logger.info("=" * 60)
    
    demo = UnifiedSystemDemo()
    
    try:
        # 1. مقداردهی اولیه سیستم
        symbols = ["EURUSD", "GBPUSD"]
        demo.initialize_system(symbols)
        
        # 2. نمایش سیگنال تکی
        logger.info("\n🎯 بخش 1: نمایش سیگنال تکی")
        single_signal = demo.demonstrate_single_signal("EURUSD", "H1")
        
        # 3. نمایش سیگنال‌های چندگانه
        logger.info("\n🔄 بخش 2: نمایش سیگنال‌های چندگانه")
        multiple_signals = demo.demonstrate_multiple_symbols(symbols, "H1")
        
        # 4. ذخیره نتایج
        if multiple_signals:
            demo.save_results(multiple_signals)
        
        # 5. نمایش آمار عملکرد سیستم
        if demo.unified_system:
            performance = demo.unified_system.get_performance_summary()
            logger.info(f"\n📊 آمار عملکرد سیستم:")
            logger.info(f"   تعداد نمادها: {performance.get('total_symbols', 0)}")
        
        logger.info("\n🎉 مثال با موفقیت تکمیل شد!")
        
    except Exception as e:
        logger.error(f"❌ خطا در اجرای مثال: {str(e)}")
        
    finally:
        # پاکسازی
        demo.cleanup()


if __name__ == "__main__":
    main() 