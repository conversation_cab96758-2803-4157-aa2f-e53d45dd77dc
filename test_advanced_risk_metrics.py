#!/usr/bin/env python3
"""
🧪 Advanced Risk Metrics Integration Test
تست کامل ادغام Advanced Risk Metrics در سیستم اصلی
"""

import sys
import os
import numpy as np
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_integration():
    """تست کامل ادغام Advanced Risk Metrics"""
    
    print("🧪 Testing Advanced Risk Metrics Integration")
    print("=" * 50)
    
    # Test 1: Core Import
    try:
        from core import (
            get_available_components,
            ADVANCED_RISK_METRICS_AVAILABLE,
            advanced_risk_calculator,
            calculate_portfolio_risk,
            RiskMetrics,
            RiskLevel
        )
        
        print("1️⃣ Core Import: ✅ Success")
        
        # Check availability
        components = get_available_components()
        risk_available = components.get('advanced_risk_metrics', False)
        print(f"   📊 Advanced Risk Metrics Available: {'✅' if risk_available else '❌'}")
        
    except Exception as e:
        print(f"1️⃣ Core Import: ❌ Failed - {e}")
        return False
    
    # Test 2: Main System Integration
    try:
        from main_new import TradingSystemManager
        
        system = TradingSystemManager()
        
        # Check if advanced_risk_calculator attribute exists
        has_risk_calculator = hasattr(system, 'advanced_risk_calculator')
        print(f"2️⃣ Main System Integration: {'✅' if has_risk_calculator else '❌'}")
        
        # Initialize system
        if system.initialize_advanced_core_components():
            risk_calc_initialized = system.advanced_risk_calculator is not None
            print(f"   🔧 Risk Calculator Initialized: {'✅' if risk_calc_initialized else '❌'}")
        else:
            print("   ⚠️ Advanced components initialization failed")
            
    except Exception as e:
        print(f"2️⃣ Main System Integration: ❌ Failed - {e}")
        return False
    
    # Test 3: Risk Calculation
    try:
        # Generate sample returns data
        np.random.seed(42)
        sample_returns = np.random.normal(0.0008, 0.015, 252)  # Daily returns for 1 year
        
        # Calculate risk metrics
        risk_metrics = calculate_portfolio_risk(sample_returns)
        
        print("3️⃣ Risk Calculation: ✅ Success")
        print(f"   📊 Sharpe Ratio: {risk_metrics.sharpe_ratio:.3f}")
        print(f"   📊 Max Drawdown: {risk_metrics.max_drawdown:.2%}")
        print(f"   📊 VaR 95%: {risk_metrics.var_95:.2%}")
        print(f"   📊 CVaR 95%: {risk_metrics.cvar_95:.2%}")
        print(f"   📊 Volatility: {risk_metrics.volatility:.2%}")
        print(f"   📊 Risk Level: {risk_metrics.risk_level.value}")
        
    except Exception as e:
        print(f"3️⃣ Risk Calculation: ❌ Failed - {e}")
        return False
    
    # Test 4: Risk Report Export
    try:
        from core.advanced_risk_metrics import export_risk_report
        
        # Export risk report
        report_file = export_risk_report(risk_metrics, "test_risk_report.json")
        
        # Check if file was created
        if os.path.exists(report_file):
            print("4️⃣ Risk Report Export: ✅ Success")
            print(f"   📄 Report file: {report_file}")
            
            # Clean up test file
            os.remove(report_file)
            print("   🧹 Test file cleaned up")
        else:
            print("4️⃣ Risk Report Export: ❌ File not created")
            
    except Exception as e:
        print(f"4️⃣ Risk Report Export: ❌ Failed - {e}")
        return False
    
    # Test 5: System Status Check
    try:
        if system.advanced_components_initialized:
            status = system.get_system_status()
            risk_calc_status = status['advanced_components'].get('advanced_risk_calculator', False)
            print(f"5️⃣ System Status Check: {'✅' if risk_calc_status else '❌'}")
        else:
            print("5️⃣ System Status Check: ⚠️ Advanced components not initialized")
            
    except Exception as e:
        print(f"5️⃣ System Status Check: ❌ Failed - {e}")
        return False
    
    # Test 6: Statistics
    try:
        from core.advanced_risk_metrics import get_risk_statistics
        
        stats = get_risk_statistics()
        print("6️⃣ Statistics: ✅ Success")
        print(f"   📊 Supported Metrics: {len(stats['supported_metrics'])}")
        print(f"   📊 Risk Levels: {len(stats['risk_levels'])}")
        print(f"   📊 Status: {stats['status']}")
        
    except Exception as e:
        print(f"6️⃣ Statistics: ❌ Failed - {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 ALL TESTS PASSED!")
    print("✅ Advanced Risk Metrics Successfully Integrated!")
    print("🚀 Ready for Production Use!")
    
    return True

if __name__ == "__main__":
    success = test_integration()
    exit(0 if success else 1) 