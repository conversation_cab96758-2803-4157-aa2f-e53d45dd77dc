import pytest
from utils.config_validation import validate_config

def test_validate_config_success():
    config = {
        "lot_size": 0.5,
        "stop_loss": 50,
        "take_profit": 100,
        "indicators": {"rsi": {"period": 14}},
    }
    validate_config(config)

def test_validate_config_missing_key():
    config = {"lot_size": 0.5, "stop_loss": 50, "take_profit": 100}
    with pytest.raises(ValueError):
        validate_config(config)

def test_validate_config_invalid_lot_size():
    config = {
        "lot_size": 200,
        "stop_loss": 50,
        "take_profit": 100,
        "indicators": {"rsi": {"period": 14}},
    }
    with pytest.raises(ValueError):
        validate_config(config)

def test_validate_config_invalid_type():
    config = {
        "lot_size": 0.5,
        "stop_loss": 50,
        "take_profit": 100,
        "indicators": 123,
    }
    with pytest.raises(ValueError):
        validate_config(config)
