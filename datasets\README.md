# 📊 Datasets Directory - مجموعه داده‌ها

این دایرکتوری شامل تمام داده‌های مورد استفاده در سیستم معاملاتی هوشمند است.

## 📁 ساختار پوشه‌ها

### 🔸 `raw/` - داده‌های خام
- داده‌های اولیه و خام از منابع مختلف
- شامل داده‌های قیمت، اخبار، و سایر منابع
- **هیچ پردازشی روی این داده‌ها انجام نشده**

### 🔸 `cleaned/` - داده‌های پاکسازی شده  
- داده‌های پردازش شده توسط `AnomalyDetectionSystem`
- حذف ناهنجاری‌ها و داده‌های نامعتبر
- آماده برای آموزش مدل‌ها

### 🔸 `synthetic/` - داده‌های مصنوعی
- داده‌های تولید شده توسط مدل‌ها
- داده‌های ترکیبی و تقویت شده
- برای افزایش حجم داده‌های آموزشی

## 🎯 نحوه استفاده

### برای Pearl-3x7B:
```python
# بارگذاری داده‌های خام
raw_data = load_raw_data("datasets/raw/EURUSD/")

# پاکسازی و آماده‌سازی
cleaned_data = preprocess_data(raw_data)

# ذخیره در cleaned
save_cleaned_data(cleaned_data, "datasets/cleaned/")
```

### برای آموزش مدل‌ها:
```python
# استفاده از داده‌های پاکسازی شده
training_data = load_from("datasets/cleaned/")
model.train(training_data)
```

## 📋 فهرست داده‌های موجود

### Raw Data:
- EURUSD (تمام timeframe ها)
- GBPUSD (تمام timeframe ها) 
- XAUUSD (تمام timeframe ها)

### Timeframes:
- M5 (5 دقیقه)
- M15 (15 دقیقه)
- H1 (1 ساعت)
- H4 (4 ساعت)
- D1 (روزانه)

## ⚠️ نکات مهم

1. **هرگز داده‌های raw را تغییر ندهید**
2. **همیشه از cleaned data برای آموزش استفاده کنید**
3. **داده‌های synthetic را برای تست نهایی استفاده نکنید**

## 🔄 فرآیند پردازش

```
Raw Data → Anomaly Detection → Cleaned Data → Model Training
    ↓
Synthetic Data Generation ← Trained Models
```

---
*این ساختار مطابق نقشه راه گنج برای Pearl-3x7B طراحی شده است.*
