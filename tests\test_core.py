"""
🧪 Core Module Tests
تست‌های ماژول هسته

این فایل شامل تست‌های unit برای ماژول‌های core است
"""

import pytest
import asyncio
import tempfile
import os
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

# Import modules under test
try:
    from core.config import get_config, ConfigManager, TradingConfig, ModelConfig
    from core.logger import get_logger, configure_logging, LogLevel
    from core.exceptions import (
        TradingSystemError, ModelLoadError, NetworkError, 
        ValidationError, ResourceError, ExceptionHandler
    )
    from core.utils import (
        PerformanceMonitor, MemoryManager, CacheManager, ProxyManager,
        retry_on_failure, timeout_after, cache_result, monitor_performance,
        get_system_info, cleanup_resources
    )
    from core.base import (
        BaseComponent, BaseModel, BaseStrategy, BaseDataProcessor,
        TradingSignal, ModelPrediction, MarketData, ComponentRegistry
    )
    
    CORE_AVAILABLE = True
    
except ImportError as e:
    print(f"⚠️ Core modules not available: {e}")
    CORE_AVAILABLE = False

pytestmark = pytest.mark.skipif(not CORE_AVAILABLE, reason="Core modules not available")

class TestConfiguration:
    """تست پیکربندی"""
    
    def test_get_config(self):
        """تست دریافت پیکربندی"""
        config = get_config()
        assert config is not None
        assert hasattr(config, 'trading')
        assert hasattr(config, 'models')
    
    def test_config_manager(self):
        """تست مدیر پیکربندی"""
        manager = ConfigManager()
        assert manager is not None
        
        # Test loading config
        config_dict = manager.get_config_dict()
        assert isinstance(config_dict, dict)
    
    def test_trading_config(self):
        """تست پیکربندی معاملاتی"""
        config = get_config()
        trading_config = config.trading
        
        assert hasattr(trading_config, 'initial_balance')
        assert hasattr(trading_config, 'max_position_size')
        assert hasattr(trading_config, 'risk_per_trade')
        
        assert trading_config.initial_balance > 0
        assert 0 < trading_config.max_position_size <= 1
        assert 0 < trading_config.risk_per_trade <= 1
    
    def test_model_config(self):
        """تست پیکربندی مدل"""
        config = get_config()
        
        # Check if models config exists
        if hasattr(config, 'models') and config.models:
            for model_name, model_config in config.models.items():
                assert hasattr(model_config, 'name')
                assert hasattr(model_config, 'model_type')

class TestLogging:
    """تست سیستم لاگ"""
    
    def test_get_logger(self):
        """تست دریافت logger"""
        logger = get_logger("test")
        assert logger is not None
        assert logger.name == "test"
    
    def test_logging_levels(self):
        """تست سطوح لاگ"""
        logger = get_logger("test_levels")
        
        # Test different log levels
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        logger.critical("Critical message")
    
    def test_configure_logging(self):
        """تست پیکربندی لاگ"""
        config = {
            "level": "DEBUG",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
        
        result = configure_logging(config)
        assert result is True
    
    def test_logger_with_metadata(self):
        """تست لاگ با metadata"""
        logger = get_logger("test_metadata")
        
        # Test logging with extra data
        logger.info("Test message", extra={
            "symbol": "EURUSD",
            "action": "buy",
            "amount": 0.1
        })

class TestExceptions:
    """تست مدیریت خطا"""
    
    def test_trading_system_error(self):
        """تست خطای سیستم معاملاتی"""
        with pytest.raises(TradingSystemError):
            raise TradingSystemError("Test error")
    
    def test_model_load_error(self):
        """تست خطای بارگذاری مدل"""
        with pytest.raises(ModelLoadError):
            raise ModelLoadError("Model loading failed")
    
    def test_network_error(self):
        """تست خطای شبکه"""
        with pytest.raises(NetworkError):
            raise NetworkError("Network connection failed")
    
    def test_validation_error(self):
        """تست خطای اعتبارسنجی"""
        with pytest.raises(ValidationError):
            raise ValidationError("Validation failed")
    
    def test_resource_error(self):
        """تست خطای منابع"""
        with pytest.raises(ResourceError):
            raise ResourceError("Resource not available")
    
    def test_exception_handler(self):
        """تست مدیریت خطا"""
        handler = ExceptionHandler()
        assert handler is not None
        
        # Test exception handling
        try:
            raise TradingSystemError("Test error")
        except Exception as e:
            result = handler.handle_exception(e)
            assert result is not None

class TestUtilities:
    """تست ابزارهای کمکی"""
    
    def test_performance_monitor(self):
        """تست مانیتور عملکرد"""
        monitor = PerformanceMonitor()
        assert monitor is not None
        
        # Test performance measurement
        with monitor.measure("test_operation"):
            # Simulate some work
            import time
            time.sleep(0.1)
        
        # Check if measurement was recorded
        stats = monitor.get_stats()
        assert "test_operation" in stats
    
    def test_memory_manager(self):
        """تست مدیر حافظه"""
        manager = MemoryManager()
        assert manager is not None
        
        # Test memory info
        memory_info = manager.get_memory_info()
        assert isinstance(memory_info, dict)
        assert "used_mb" in memory_info
        assert "available_mb" in memory_info
    
    def test_cache_manager(self):
        """تست مدیر کش"""
        manager = CacheManager()
        assert manager is not None
        
        # Test caching
        key = "test_key"
        value = "test_value"
        
        manager.set(key, value)
        cached_value = manager.get(key)
        assert cached_value == value
    
    def test_proxy_manager(self):
        """تست مدیر پروکسی"""
        manager = ProxyManager()
        assert manager is not None
        
        # Test proxy status
        status = manager.get_status()
        assert isinstance(status, dict)
    
    def test_retry_decorator(self):
        """تست دکوراتور retry"""
        call_count = 0
        
        @retry_on_failure(max_attempts=3)
        def failing_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Test error")
            return "success"
        
        result = failing_function()
        assert result == "success"
        assert call_count == 3
    
    def test_timeout_decorator(self):
        """تست دکوراتور timeout"""
        @timeout_after(1)
        def slow_function():
            import time
            time.sleep(2)
            return "completed"
        
        with pytest.raises(TimeoutError):
            slow_function()
    
    def test_cache_decorator(self):
        """تست دکوراتور cache"""
        call_count = 0
        
        @cache_result(ttl=60)
        def expensive_function(x):
            nonlocal call_count
            call_count += 1
            return x * 2
        
        # First call
        result1 = expensive_function(5)
        assert result1 == 10
        assert call_count == 1
        
        # Second call (should use cache)
        result2 = expensive_function(5)
        assert result2 == 10
        assert call_count == 1  # Should not increment
    
    def test_monitor_performance_decorator(self):
        """تست دکوراتور monitor_performance"""
        @monitor_performance
        def monitored_function():
            import time
            time.sleep(0.1)
            return "done"
        
        result = monitored_function()
        assert result == "done"
    
    def test_get_system_info(self):
        """تست دریافت اطلاعات سیستم"""
        system_info = get_system_info()
        assert isinstance(system_info, dict)
        assert "platform" in system_info
        assert "python_version" in system_info
    
    def test_cleanup_resources(self):
        """تست پاکسازی منابع"""
        # This should not raise an exception
        cleanup_resources()

class TestBaseClasses:
    """تست کلاس‌های پایه"""
    
    def test_base_component(self):
        """تست کلاس پایه component"""
        class TestComponent(BaseComponent):
            def initialize(self):
                return True
            
            def start(self):
                return True
            
            def stop(self):
                return True
            
            def health_check(self):
                return {"status": "healthy"}
        
        component = TestComponent()
        assert component is not None
        
        # Test lifecycle
        assert component.initialize() is True
        assert component.start() is True
        assert component.stop() is True
        
        # Test health check
        health = component.health_check()
        assert health["status"] == "healthy"
    
    def test_base_model(self):
        """تست کلاس پایه model"""
        class TestModel(BaseModel):
            def load_model(self, model_path):
                return True
            
            def predict(self, input_data):
                return ModelPrediction(
                    prediction="test",
                    confidence=0.9,
                    metadata={}
                )
            
            def get_model_info(self):
                return {"name": "test_model", "type": "test"}
        
        model = TestModel()
        assert model is not None
        
        # Test model operations
        assert model.load_model("test_path") is True
        
        prediction = model.predict("test_input")
        assert isinstance(prediction, ModelPrediction)
        assert prediction.prediction == "test"
        assert prediction.confidence == 0.9
        
        info = model.get_model_info()
        assert info["name"] == "test_model"
    
    def test_trading_signal(self):
        """تست سیگنال معاملاتی"""
        signal = TradingSignal(
            symbol="EURUSD",
            action="buy",
            confidence=0.8,
            timestamp=datetime.now(),
            metadata={"source": "test"}
        )
        
        assert signal.symbol == "EURUSD"
        assert signal.action == "buy"
        assert signal.confidence == 0.8
        assert signal.metadata["source"] == "test"
    
    def test_model_prediction(self):
        """تست پیش‌بینی مدل"""
        prediction = ModelPrediction(
            prediction="positive",
            confidence=0.85,
            metadata={"model": "test"}
        )
        
        assert prediction.prediction == "positive"
        assert prediction.confidence == 0.85
        assert prediction.metadata["model"] == "test"
    
    def test_market_data(self):
        """تست داده‌های بازار"""
        market_data = MarketData(
            symbol="EURUSD",
            timestamp=datetime.now(),
            open=1.1000,
            high=1.1050,
            low=1.0950,
            close=1.1025,
            volume=1000
        )
        
        assert market_data.symbol == "EURUSD"
        assert market_data.open == 1.1000
        assert market_data.close == 1.1025
        assert market_data.volume == 1000
    
    def test_component_registry(self):
        """تست رجیستری component"""
        registry = ComponentRegistry()
        assert registry is not None
        
        # Test component registration
        class TestComponent(BaseComponent):
            pass
        
        component = TestComponent()
        registry.register_component("test", component)
        
        # Test component retrieval
        retrieved = registry.get_component("test")
        assert retrieved is component
        
        # Test component list
        components = registry.get_all_components()
        assert "test" in components

class TestAsyncOperations:
    """تست عملیات async"""
    
    @pytest.mark.asyncio
    async def test_async_component(self):
        """تست component async"""
        class AsyncTestComponent(BaseComponent):
            async def async_initialize(self):
                await asyncio.sleep(0.1)
                return True
            
            async def async_operation(self):
                await asyncio.sleep(0.1)
                return "completed"
        
        component = AsyncTestComponent()
        
        # Test async operations
        result = await component.async_initialize()
        assert result is True
        
        operation_result = await component.async_operation()
        assert operation_result == "completed"
    
    @pytest.mark.asyncio
    async def test_async_performance_monitoring(self):
        """تست مانیتورینگ عملکرد async"""
        monitor = PerformanceMonitor()
        
        async def async_operation():
            await asyncio.sleep(0.1)
            return "done"
        
        # Test async performance measurement
        start_time = datetime.now()
        result = await async_operation()
        end_time = datetime.now()
        
        assert result == "done"
        assert (end_time - start_time).total_seconds() >= 0.1

class TestErrorHandling:
    """تست مدیریت خطای پیشرفته"""
    
    def test_exception_context(self):
        """تست context خطا"""
        try:
            with pytest.raises(TradingSystemError):
                raise TradingSystemError("Test error")
        except Exception as e:
            assert str(e) == "Test error"
    
    def test_nested_exceptions(self):
        """تست خطاهای تودرتو"""
        def inner_function():
            raise ModelLoadError("Inner error")
        
        def outer_function():
            try:
                inner_function()
            except ModelLoadError as e:
                raise TradingSystemError(f"Outer error: {e}")
        
        with pytest.raises(TradingSystemError) as exc_info:
            outer_function()
        
        assert "Outer error" in str(exc_info.value)
        assert "Inner error" in str(exc_info.value)
    
    def test_exception_handler_with_callback(self):
        """تست exception handler با callback"""
        handler = ExceptionHandler()
        callback_called = False
        
        def error_callback(exception):
            nonlocal callback_called
            callback_called = True
        
        # This would need to be implemented in ExceptionHandler
        # handler.set_callback(error_callback)
        
        try:
            raise TradingSystemError("Test error")
        except Exception as e:
            handler.handle_exception(e)
        
        # For now, just check that handler doesn't crash
        assert True

class TestConfiguration_Advanced:
    """تست‌های پیشرفته پیکربندی"""
    
    def test_config_validation(self):
        """تست اعتبارسنجی پیکربندی"""
        config = get_config()
        
        # Validate trading config
        trading = config.trading
        assert trading.initial_balance > 0
        assert 0 < trading.max_position_size <= 1
        assert 0 < trading.risk_per_trade <= 1
    
    def test_config_override(self):
        """تست override کردن پیکربندی"""
        # This would test environment variable overrides
        # For now, just test that config loading works
        config = get_config()
        assert config is not None
    
    def test_config_file_missing(self):
        """تست عدم وجود فایل پیکربندی"""
        # This would test fallback behavior
        # For now, just ensure config loads with defaults
        config = get_config()
        assert config is not None 