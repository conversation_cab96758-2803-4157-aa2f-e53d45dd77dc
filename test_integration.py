#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 Integration Test - Complete System Integration
تست ادغام کامل سیستم

این فایل برای بررسی اتصال صحیح همه 10 آیتم به سیستم اصلی است
"""

import os
import sys
import asyncio
import time
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_core_imports():
    """تست import های core"""
    print("1️⃣ Testing Core Imports...")
    
    try:
        # Test basic core imports
        from core import (
            get_available_components,
            initialize_advanced_components,
            cleanup_advanced_components
        )
        print("   ✅ Core management functions imported")
        
        # Test availability flags
        from core import (
            MEMORY_MANAGER_AVAILABLE,
            ORDER_MANAGER_AVAILABLE, 
            MULTI_EXCHANGE_AVAILABLE,
            MODEL_VERSIONING_AVAILABLE,
            MODEL_MONITORING_AVAILABLE
        )
        print("   ✅ Availability flags imported")
        
        # Show availability status
        available = get_available_components()
        print(f"   📦 Available components: {available}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Core import error: {e}")
        return False

def test_memory_manager_integration():
    """تست ادغام Memory Manager"""
    print("\n2️⃣ Testing Memory Manager Integration...")
    
    try:
        from core import MEMORY_MANAGER_AVAILABLE, advanced_memory_manager
        
        if not MEMORY_MANAGER_AVAILABLE:
            print("   ⚠️ Memory Manager not available")
            return True
        
        if advanced_memory_manager:
            # Test memory info
            memory_stats = advanced_memory_manager.get_memory_stats()
            print(f"   ✅ Memory stats retrieved: {memory_stats.memory_level.value}")
            
            # Test system resources
            system_resources = advanced_memory_manager.get_system_resources()
            print(f"   ✅ System resources retrieved: {list(system_resources.keys())}")
            
            # Test memory monitoring
            advanced_memory_manager.start_monitoring()
            print("   ✅ Memory monitoring started")
            
            advanced_memory_manager.stop_monitoring()
            print("   ✅ Memory monitoring stopped")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Memory Manager error: {e}")
        return False

def test_order_manager_integration():
    """تست ادغام Order Manager"""
    print("\n3️⃣ Testing Order Manager Integration...")
    
    try:
        from core import ORDER_MANAGER_AVAILABLE, AdvancedOrderManager, Order, OrderType, OrderSide
        from core.order_manager import OrderValidator
        
        if not ORDER_MANAGER_AVAILABLE:
            print("   ⚠️ Order Manager not available")
            return True
        
        # Create order manager
        order_manager = AdvancedOrderManager()
        print("   ✅ Order Manager created")
        
        # Create validator
        validator = OrderValidator()
        print("   ✅ Order validator created")
        
        # Create test order using order manager
        test_order = order_manager.create_order(
            symbol="EURUSD",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=1000
        )
        print("   ✅ Test order created")
        
        # Validate order using validator
        validation_errors = validator.validate_order(test_order)
        is_valid = len(validation_errors) == 0
        print(f"   ✅ Order validation: {is_valid}")
        
        if validation_errors:
            print(f"      Validation errors: {validation_errors}")
        
        # Submit order using order_id
        submitted = order_manager.submit_order(test_order.order_id)
        print(f"   ✅ Order submission: {submitted}")
        
        # Test order retrieval
        retrieved_order = order_manager.get_order(test_order.order_id)
        print(f"   ✅ Order retrieval: {retrieved_order is not None}")
        
        # Test statistics
        stats = order_manager.get_statistics()
        print(f"   ✅ Statistics: {stats['total_orders']} orders")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Order Manager error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_exchange_integration():
    """تست ادغام Multi Exchange"""
    print("\n4️⃣ Testing Multi-Exchange Integration...")
    
    try:
        from core import MULTI_EXCHANGE_AVAILABLE, multi_exchange_manager, ExchangeConfig, ExchangeType
        
        if not MULTI_EXCHANGE_AVAILABLE:
            print("   ⚠️ Multi-Exchange not available")
            return True
        
        if multi_exchange_manager:
            # Add test exchange
            test_config = ExchangeConfig(
                exchange_id="integration_test_exchange",
                name="Integration Test Exchange",
                exchange_type=ExchangeType.FOREX,
                api_url="https://test.api.com",
                sandbox=True
            )
            
            added = multi_exchange_manager.add_exchange(test_config)
            print(f"   ✅ Test exchange added: {added}")
            
            # Get statistics
            stats = multi_exchange_manager.get_statistics()
            print(f"   ✅ Exchange statistics: {stats['total_exchanges']} exchanges")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Multi-Exchange error: {e}")
        return False

def test_model_versioning_integration():
    """تست ادغام Model Versioning"""
    print("\n5️⃣ Testing Model Versioning Integration...")
    
    try:
        from core import MODEL_VERSIONING_AVAILABLE, model_registry, ModelMetadata, ModelType
        
        if not MODEL_VERSIONING_AVAILABLE:
            print("   ⚠️ Model Versioning not available")
            return True
        
        if model_registry:
            # Create test model
            test_model = {"type": "integration_test", "params": {"test": True}}
            
            # Create metadata
            metadata = ModelMetadata(
                name="integration_test_model",
                version="1.0", 
                model_type=ModelType.CUSTOM,
                description="Integration test model"
            )
            
            # Register model
            model_id = model_registry.register_model(
                "integration_test_model", "1.0", test_model, metadata
            )
            print(f"   ✅ Model registered: {model_id}")
            
            # Get statistics
            stats = model_registry.get_statistics()
            print(f"   ✅ Registry statistics: {stats['total_models']} models")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Model Versioning error: {e}")
        return False

def test_model_monitoring_integration():
    """تست ادغام Model Monitoring"""
    print("\n6️⃣ Testing Model Monitoring Integration...")
    
    try:
        from core import MODEL_MONITORING_AVAILABLE, monitoring_manager
        
        if not MODEL_MONITORING_AVAILABLE:
            print("   ⚠️ Model Monitoring not available")
            return True
        
        if monitoring_manager:
            # Create test monitor
            monitor = monitoring_manager.create_monitor("integration_test_model", "1.0")
            print("   ✅ Model monitor created")
            
            # Record test prediction
            monitor.record_prediction(
                features={"feature1": 1.0, "feature2": 2.0},
                prediction=1,
                actual=1,
                latency=50.0
            )
            print("   ✅ Test prediction recorded")
            
            # Get statistics
            stats = monitoring_manager.get_global_statistics()
            print(f"   ✅ Monitoring statistics: {stats['total_monitors']} monitors")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Model Monitoring error: {e}")
        return False

async def test_main_system_integration():
    """تست ادغام سیستم اصلی"""
    print("\n7️⃣ Testing Main System Integration...")
    
    try:
        # Import main system
        from main_new import TradingSystemManager
        print("   ✅ Main system imported")
        
        # Create system manager
        system = TradingSystemManager()
        print("   ✅ System manager created")
        
        # Initialize system
        initialized = system.initialize_system()
        print(f"   ✅ System initialization: {initialized}")
        
        if initialized:
            # Check system status
            status = system.get_system_status()
            print(f"   ✅ System status retrieved")
            print(f"      - Initialized: {status['is_initialized']}")
            print(f"      - Advanced components: {status['advanced_components_initialized']}")
            
            # Count available components
            basic_count = sum(1 for available in status['components'].values() if available)
            advanced_count = sum(1 for available in status['advanced_components'].values() if available)
            
            print(f"      - Basic components: {basic_count}/6")
            print(f"      - Advanced components: {advanced_count}/5")
            
            # Test system operations
            await system.start_trading_system()
            print("   ✅ Trading system started")
            
            await system.shutdown_system()
            print("   ✅ Trading system shutdown")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Main system error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_operational_workflows():
    """تست workflow های عملیاتی"""
    print("\n8️⃣ Testing Operational Workflows...")
    
    try:
        # Test configuration loading workflow
        from core.simple_config import load_config
        config = load_config("config.yaml")
        if config:
            print("   ✅ Configuration workflow")
        
        # Test error handling workflow
        from core.error_handler import AdvancedErrorHandler, handle_error
        error_handler = AdvancedErrorHandler("test_handler")
        
        @handle_error(error_handler)
        def test_function():
            return True
        
        result = test_function()
        print(f"   ✅ Error handling workflow: {result}")
        
        # Test database workflow
        from core.simple_database_manager import initialize_database_manager
        db_manager = initialize_database_manager()
        if db_manager:
            print("   ✅ Database workflow")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Operational workflow error: {e}")
        return False

async def run_integration_tests():
    """اجرای همه تست‌های ادغام"""
    print("🔗 Advanced Trading System - Integration Test")
    print("=" * 60)
    print("Testing integration of all 10 advanced modules with main system...")
    
    test_results = []
    
    # Run all tests
    test_results.append(test_core_imports())
    test_results.append(test_memory_manager_integration())
    test_results.append(test_order_manager_integration())
    test_results.append(test_multi_exchange_integration())
    test_results.append(test_model_versioning_integration())
    test_results.append(test_model_monitoring_integration())
    test_results.append(await test_main_system_integration())
    test_results.append(test_operational_workflows())
    
    # Results summary
    print("\n📊 Integration Test Results:")
    print("=" * 40)
    
    passed = sum(test_results)
    total = len(test_results)
    success_rate = (passed / total) * 100
    
    print(f"✅ Tests passed: {passed}/{total}")
    print(f"📈 Success rate: {success_rate:.1f}%")
    
    if passed == total:
        print("🎉 ALL INTEGRATION TESTS PASSED!")
        print("✅ All 10 modules are properly integrated with the main system")
    elif passed >= total * 0.8:  # 80% success rate
        print("⚠️ MOSTLY SUCCESSFUL")
        print("🔧 Most modules integrated, some minor issues")
    else:
        print("❌ INTEGRATION ISSUES DETECTED")
        print("🚨 Significant integration problems need attention")
    
    # Detailed component status
    print("\n📦 Component Integration Status:")
    
    # Try to get detailed status from main system
    try:
        from main_new import system_manager
        if system_manager.initialize_system():
            status = system_manager.get_system_status()
            
            print("   Basic Components:")
            for component, available in status['components'].items():
                print(f"     - {component}: {'✅' if available else '❌'}")
            
            print("   Advanced Components:")  
            for component, available in status['advanced_components'].items():
                print(f"     - {component}: {'✅' if available else '❌'}")
                
    except Exception as e:
        print(f"   ⚠️ Could not get detailed status: {e}")
    
    print("\n" + "=" * 60)
    return passed == total

def main():
    """نقطه ورود اصلی"""
    try:
        success = asyncio.run(run_integration_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Integration test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 