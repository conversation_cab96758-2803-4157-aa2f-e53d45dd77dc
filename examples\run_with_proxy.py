#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Scrip<PERSON> to run the sentiment integration example with proxy settings.
"""

import os
import sys
import json
import subprocess
import time
import requests

def setup_proxy():
    """Set up proxy settings from PROXY.json file."""
    try:
        # Read proxy configuration
        with open('PROXY.json', 'r', encoding='utf-8') as f:
            proxy_config = json.load(f)
        
        # Get HTTP proxy settings
        http_inbound = next((inb for inb in proxy_config['inbounds'] if inb['tag'] == 'http'), None)
        if http_inbound:
            proxy_host = http_inbound.get('listen', '127.0.0.1')
            proxy_port = http_inbound.get('port', 10809)
            
            # Set environment variables for proxy
            proxy_url = f'http://{proxy_host}:{proxy_port}'
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url
            os.environ['http_proxy'] = proxy_url
            os.environ['https_proxy'] = proxy_url
            
            # Also set for requests library
            os.environ['REQUESTS_CA_BUNDLE'] = ''
            
            print(f"Proxy set to: {proxy_url}")
            
            # Test proxy connection
            try:
                print("Testing proxy connection...")
                response = requests.get('https://huggingface.co', 
                                       proxies={'http': proxy_url, 'https': proxy_url},
                                       timeout=10,
                                       verify=False)
                if response.status_code == 200:
                    print("Proxy connection successful!")
                    return True
                else:
                    print(f"Proxy connection test failed with status code: {response.status_code}")
            except Exception as e:
                print(f"Proxy connection test failed: {e}")
                return False
        else:
            print("No HTTP proxy configuration found in PROXY.json")
    except Exception as e:
        print(f"Error setting up proxy: {e}")
    
    return False

def main():
    """Main function to run the example with proxy settings."""
    print("Setting up proxy...")
    if setup_proxy():
        print("Running sentiment integration example with proxy...")
        
        # Add the parent directory to the path
        sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
        
        # Import and run the sentiment integration example
        try:
            from utils.sentiment_integrator import SentimentIntegrator
            from utils.sentiment_analyzer import SentimentAnalyzer
            from utils.source_credibility import SourceCredibility
            from utils.news_volume_analyzer import NewsVolumeAnalyzer
            
            print("\nSentiment Integration Example (with Proxy)")
            print("====================================")
            
            # Initialize the analyzers with a retry mechanism
            print("\nInitializing sentiment analyzer...")
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    sentiment_analyzer = SentimentAnalyzer()
                    
                    # Check if models were loaded successfully
                    en_model = sentiment_analyzer.model_configs.get('en', {}).get('pipeline')
                    fa_model = sentiment_analyzer.model_configs.get('fa', {}).get('pipeline')
                    
                    if en_model or fa_model:
                        print(f"Successfully loaded models (Attempt {attempt+1}/{max_retries}):")
                        print(f"  - English model: {'Loaded' if en_model else 'Failed'}")
                        print(f"  - Persian model: {'Loaded' if fa_model else 'Failed'}")
                        break
                    else:
                        print(f"Failed to load models (Attempt {attempt+1}/{max_retries})")
                        if attempt < max_retries - 1:
                            print("Retrying in 5 seconds...")
                            time.sleep(5)
                except Exception as e:
                    print(f"Error initializing sentiment analyzer (Attempt {attempt+1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        print("Retrying in 5 seconds...")
                        time.sleep(5)
            
            # Initialize the integrator
            print("\nInitializing sentiment integrator...")
            integrator = SentimentIntegrator(sentiment_analyzer=sentiment_analyzer)
            
            # Asset we're analyzing
            asset = "AAPL"
            
            # Generate a simple signal
            print(f"\nGenerating signal for {asset}...")
            signal = integrator.get_integrated_signal(
                asset=asset,
                technical_data={'trend': 0.5, 'momentum': 0.3, 'volatility': -0.2, 'volume': 0.1},
                fundamental_data={'valuation': -0.3, 'growth': 0.6, 'quality': 0.4}
            )
            
            # Print signal information
            print(f"\nSignal = {signal['overall_signal']:.3f}, Confidence = {signal['confidence']:.3f}")
            print("Component signals:")
            for component, value in signal['component_signals'].items():
                print(f"  {component}: {value:.3f}")
            
            # Test sentiment analysis if models were loaded successfully
            if hasattr(sentiment_analyzer, 'model_configs'):
                en_model = sentiment_analyzer.model_configs.get('en', {}).get('pipeline')
                fa_model = sentiment_analyzer.model_configs.get('fa', {}).get('pipeline')
                
                if en_model:
                    print("\nTesting English sentiment analysis:")
                    texts = [
                        "Apple reported strong quarterly earnings, exceeding all expectations.",
                        "The stock plummeted following the CEO's resignation.",
                        "The market remained stable throughout the trading session."
                    ]
                    
                    for text in texts:
                        result = sentiment_analyzer.analyze(text)
                        print(f"\nText: '{text}'")
                        print(f"Sentiment: {result['label']} (score: {result['score']:.4f})")
                        print(f"Weighted score: {result['weighted_score']:.4f}")
                
                if fa_model:
                    print("\nTesting Persian sentiment analysis:")
                    texts = [
                        "اپل گزارش مالی قوی ارائه داد که از انتظارات فراتر رفت.",
                        "قیمت سهام پس از استعفای مدیرعامل سقوط کرد.",
                        "بازار در طول جلسه معاملاتی پایدار باقی ماند."
                    ]
                    
                    for text in texts:
                        result = sentiment_analyzer.analyze(text)
                        print(f"\nText: '{text}'")
                        print(f"Sentiment: {result['label']} (score: {result['score']:.4f})")
                        print(f"Weighted score: {result['weighted_score']:.4f}")
            
            print("\nDone!")
            
        except Exception as e:
            print(f"Error running example: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("Failed to set up proxy. Running example without proxy...")
        # Run the original example script as a subprocess
        subprocess.run([sys.executable, 'examples/sentiment_integration_example.py'])

if __name__ == "__main__":
    # Disable SSL warnings for proxy
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    main() 