# Trading System v2.0 Configuration
# ═══════════════════════════════════════════════════════════
# پیکربندی کامل سیستم معاملاتی نسخه 2.0

app_name: "Advanced Trading System"
version: "2.0.0"
environment: "production"
debug: false

# Basic settings
auto_save_config: true
config_backup: true
validate_on_load: true

# پیکربندی پروکسی
proxy:
  enabled: true
  http_url: "http://127.0.0.1:10809"
  socks_url: "socks5://127.0.0.1:10808"
  timeout: 30

# پیکربندی معاملات
trading:
  mode: "live"
  symbols:
    - "EURUSD"
    - "GBPUSD"
    - "USDJPY"
    - "AUDUSD"
    - "USDCAD"
  
  timeframes:
    - "H1"
    - "H4"
    - "D1"
  
  initial_capital: 10000.0
  max_positions: 5
  risk_per_trade: 0.02
  commission: 0.001

# پیکربندی دیتابیس
database:
  type: "sqlite"
  host: "localhost"
  port: 5432
  database: "trading_system.db"

# پیکربندی لاگینگ
logging:
  level: "INFO"
  file_enabled: true
  console_enabled: true
  file_path: "logs/trading_system.log"

# پیکربندی بک‌تست
backtesting:
  enabled: true
  start_date: "2023-01-01"
  end_date: "2024-01-01"
  data_path: "data"

# پیکربندی مدل‌های AI
ai_models:
  enabled: true
  sentiment_model: "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"
  max_memory_usage: 4096
  device: "auto"

# پیکربندی امنیتی
security:
  api_key_encryption: true
  session_timeout: 3600
  max_login_attempts: 5