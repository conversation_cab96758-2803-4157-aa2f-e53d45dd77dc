#!/usr/bin/env python3
"""
Basic Integration Tests
تست‌های ادغام پایه
"""

import pytest
import sys
import os
import asyncio
import pandas as pd
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_system_initialization():
    """تست مقداردهی اولیه سیستم"""
    try:
        from main_new import system_manager
        assert system_manager is not None
        print("✅ System manager imported successfully")
    except Exception as e:
        pytest.fail(f"System initialization failed: {e}")

def test_config_loading():
    """تست بارگذاری تنظیمات"""
    try:
        import yaml
        with open("config.yaml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        assert config is not None
        assert 'trading' in config
        print("✅ Configuration loaded successfully")
    except Exception as e:
        pytest.fail(f"Configuration loading failed: {e}")

def test_ai_models_loading():
    """تست بارگذاری مدل‌های AI"""
    try:
        from ai_models import ModelRegistry, SentimentEnsemble, TimeSeriesEnsemble
        registry = ModelRegistry()
        assert registry is not None
        print("✅ AI models loaded successfully")
    except Exception as e:
        pytest.fail(f"AI models loading failed: {e}")

def test_sentiment_analyzer():
    """تست تحلیلگر احساسات"""
    try:
        from utils.sentiment_analyzer import AdvancedSentimentAnalyzer
        analyzer = AdvancedSentimentAnalyzer(languages=['en'])
        result = analyzer.analyze("This is a positive test message")
        assert result is not None
        print("✅ Sentiment analyzer working")
    except Exception as e:
        pytest.fail(f"Sentiment analyzer failed: {e}")

def test_technical_indicators():
    """تست اندیکاتورهای تکنیکال"""
    try:
        from utils.technical_indicators import AdvancedTechnicalIndicators
        indicators = AdvancedTechnicalIndicators()
        
        # Create sample data
        data = pd.DataFrame({
            'open': [1.1] * 100,
            'high': [1.11] * 100,
            'low': [1.09] * 100,
            'close': [1.105] * 100,
            'volume': [1000] * 100
        })
        
        result = indicators.calculate_all(data)
        assert result is not None
        print("✅ Technical indicators working")
    except Exception as e:
        pytest.fail(f"Technical indicators failed: {e}")

def test_risk_calculator():
    """تست محاسبه‌گر ریسک"""
    try:
        from core.advanced_risk_metrics import AdvancedRiskCalculator
        calculator = AdvancedRiskCalculator()
        
        # Create sample data
        data = pd.DataFrame({
            'close': np.random.randn(100).cumsum() + 100
        })
        
        result = calculator.calculate_risk_metrics(data)
        assert result is not None
        print("✅ Risk calculator working")
    except Exception as e:
        pytest.fail(f"Risk calculator failed: {e}")

def test_correlation_analyzer():
    """تست تحلیلگر همبستگی"""
    try:
        from core.correlation_analysis import AdvancedCorrelationAnalyzer
        analyzer = AdvancedCorrelationAnalyzer()
        
        # Create sample data
        data1 = pd.DataFrame({'close': np.random.randn(100)})
        data2 = pd.DataFrame({'close': np.random.randn(100)})
        
        result = analyzer.calculate_correlation_matrix(['EURUSD', 'GBPUSD'], [data1, data2])
        assert result is not None
        print("✅ Correlation analyzer working")
    except Exception as e:
        pytest.fail(f"Correlation analyzer failed: {e}")

def test_error_handler():
    """تست مدیریت خطا"""
    try:
        from core.error_handler import AdvancedErrorHandler
        handler = AdvancedErrorHandler()
        assert handler is not None
        print("✅ Error handler working")
    except Exception as e:
        pytest.fail(f"Error handler failed: {e}")

def test_database_manager():
    """تست مدیر پایگاه داده"""
    try:
        from core.simple_database_manager import SimpleDatabaseManager
        manager = SimpleDatabaseManager()
        assert manager is not None
        print("✅ Database manager working")
    except Exception as e:
        pytest.fail(f"Database manager failed: {e}")

def test_model_registry():
    """تست رجیستری مدل‌ها"""
    try:
        from ai_models import ModelRegistry
        registry = ModelRegistry()
        
        # Test registration
        success = registry.register("test_model", "mock_model", "test_type")
        assert success is True
        
        # Test retrieval
        model = registry.get_model("test_model")
        assert model is not None
        
        print("✅ Model registry working")
    except Exception as e:
        pytest.fail(f"Model registry failed: {e}")

def test_optimization_engine():
    """تست موتور بهینه‌سازی"""
    try:
        from optimization.bayesian import BayesianOptimizer
        optimizer = BayesianOptimizer()
        assert optimizer is not None
        print("✅ Optimization engine working")
    except Exception as e:
        pytest.fail(f"Optimization engine failed: {e}")

def test_backtesting_framework():
    """تست چارچوب بک‌تستینگ"""
    try:
        from utils.backtesting_framework import SentimentBacktestingFramework
        framework = SentimentBacktestingFramework()
        assert framework is not None
        print("✅ Backtesting framework working")
    except Exception as e:
        pytest.fail(f"Backtesting framework failed: {e}")

def test_portfolio_manager():
    """تست مدیر پورتفولیو"""
    try:
        from portfolio.portfolio_manager import PortfolioManager
        manager = PortfolioManager()
        assert manager is not None
        print("✅ Portfolio manager working")
    except Exception as e:
        pytest.fail(f"Portfolio manager failed: {e}")

def test_evaluation_engine():
    """تست موتور ارزیابی"""
    try:
        from evaluation.metrics import ModelEvaluator
        evaluator = ModelEvaluator()
        assert evaluator is not None
        print("✅ Evaluation engine working")
    except Exception as e:
        pytest.fail(f"Evaluation engine failed: {e}")

def test_api_endpoints():
    """تست نقاط پایانی API"""
    try:
        from api.endpoints import app
        assert app is not None
        print("✅ API endpoints working")
    except Exception as e:
        pytest.fail(f"API endpoints failed: {e}")

def test_memory_manager():
    """تست مدیر حافظه"""
    try:
        from core.memory_manager import MemoryManager
        manager = MemoryManager()
        stats = manager.get_memory_stats()
        assert stats is not None
        print("✅ Memory manager working")
    except Exception as e:
        pytest.fail(f"Memory manager failed: {e}")

def test_order_manager():
    """تست مدیر سفارشات"""
    try:
        from core.advanced_order_management import AdvancedOrderManager
        manager = AdvancedOrderManager()
        assert manager is not None
        print("✅ Order manager working")
    except Exception as e:
        pytest.fail(f"Order manager failed: {e}")

def test_monitoring_manager():
    """تست مدیر نظارت"""
    try:
        from core.model_monitoring import ModelMonitoringManager
        manager = ModelMonitoringManager()
        assert manager is not None
        print("✅ Monitoring manager working")
    except Exception as e:
        pytest.fail(f"Monitoring manager failed: {e}")

if __name__ == "__main__":
    # Run all tests
    test_functions = [
        test_system_initialization,
        test_config_loading,
        test_ai_models_loading,
        test_sentiment_analyzer,
        test_technical_indicators,
        test_risk_calculator,
        test_correlation_analyzer,
        test_error_handler,
        test_database_manager,
        test_model_registry,
        test_optimization_engine,
        test_backtesting_framework,
        test_portfolio_manager,
        test_evaluation_engine,
        test_api_endpoints,
        test_memory_manager,
        test_order_manager,
        test_monitoring_manager
    ]
    
    passed = 0
    failed = 0
    
    for test_func in test_functions:
        try:
            test_func()
            passed += 1
        except Exception as e:
            print(f"❌ {test_func.__name__} failed: {e}")
            failed += 1
    
    print(f"\n📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("✅ All integration tests passed!")
    else:
        print(f"⚠️ {failed} tests failed") 