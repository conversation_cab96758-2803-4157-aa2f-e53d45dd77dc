#!/usr/bin/env python3
"""
🌐 تست اتصال با پروکسی HTTP
📍 استفاده از پروکسی روی پورت 10809
"""

import os
import sys
import time

# تنظیم پروکسی HTTP
PROXY_HOST = "127.0.0.1"
PROXY_PORT = "10809"
HTTP_PROXY = f"http://{PROXY_HOST}:{PROXY_PORT}"

# تنظیم متغیرهای محیطی پروکسی
os.environ['HTTP_PROXY'] = HTTP_PROXY
os.environ['HTTPS_PROXY'] = HTTP_PROXY
os.environ['http_proxy'] = HTTP_PROXY
os.environ['https_proxy'] = HTTP_PROXY

print(f"🌐 استفاده از پروکسی HTTP: {HTTP_PROXY}")

try:
    import requests
    from huggingface_hub import HfApi
    
    # تست اتصال با پروکسی
    print("🔄 تست اتصال با پروکسی...")
    
    # 1. تست اتصال ساده
    try:
        print("⏳ تست اتصال پایه...")
        response = requests.get("https://httpbin.org/ip", 
                              proxies={
                                  'http': HTTP_PROXY,
                                  'https': HTTP_PROXY
                              }, 
                              timeout=10)
        print(f"✅ IP شما: {response.json()['origin']}")
    except Exception as e:
        print(f"❌ خطا در تست پایه: {e}")
        sys.exit(1)
    
    # 2. تست HuggingFace
    print("⏳ تست HuggingFace Hub...")
    api = HfApi()
    
    try:
        # تست مدل ساده
        info = api.model_info("distilbert-base-uncased", timeout=15)
        print(f"✅ DistilBERT دانلودها: {info.downloads:,}")
        
        # تست مدل‌های مالی
        financial_models = [
            "ProsusAI/finbert",
            "amazon/chronos-t5-small", 
            "ElKulako/cryptobert"
        ]
        
        print("\n🔍 تست مدل‌های مالی:")
        successful_models = []
        
        for model in financial_models:
            try:
                print(f"  ⏳ تست {model}...")
                info = api.model_info(model, timeout=10)
                downloads = info.downloads if info.downloads else 0
                print(f"  ✅ {model}: {downloads:,} دانلود")
                successful_models.append((model, downloads))
                time.sleep(0.5)  # تاخیر کوتاه
            except Exception as e:
                print(f"  ❌ {model}: خطا - {str(e)[:50]}")
        
        # خلاصه نتایج
        print(f"\n📊 خلاصه نتایج:")
        print(f"✅ مدل‌های موفق: {len(successful_models)}/{len(financial_models)}")
        
        if successful_models:
            print("🔥 بهترین مدل‌ها:")
            sorted_models = sorted(successful_models, key=lambda x: x[1], reverse=True)
            for model, downloads in sorted_models[:3]:
                print(f"  🌟 {model}: {downloads:,}")
            
            print("\n🎉 پروکسی کار می‌کند! می‌توانیم تست کامل انجام دهیم.")
            
            # ایجاد فایل پیکربندی موفق
            config = {
                "proxy_type": "http",
                "proxy_host": PROXY_HOST,
                "proxy_port": PROXY_PORT,
                "proxy_url": HTTP_PROXY,
                "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "successful_models": len(successful_models),
                "status": "working"
            }
            
            import json
            with open("proxy_config_working.json", "w") as f:
                json.dump(config, f, indent=2)
            print("💾 پیکربندی موفق در proxy_config_working.json ذخیره شد")
        
    except Exception as e:
        print(f"❌ خطا در تست HuggingFace: {e}")
        
        # بررسی مشکلات احتمالی
        print("\n🔧 عیب‌یابی:")
        print("1. آیا پروکسی V2Ray روشن است?")
        print("2. آیا پورت 10809 باز است?")
        print("3. آیا پیکربندی درست است?")

except ImportError as e:
    print(f"❌ خطا در import: {e}")
    print("نصب کنید: pip install requests huggingface_hub")
except Exception as e:
    print(f"❌ خطای کلی: {e}")

print(f"\n🕒 تست در {time.strftime('%H:%M:%S')} تمام شد.") 