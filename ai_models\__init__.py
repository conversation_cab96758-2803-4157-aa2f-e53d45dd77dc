"""
🤖 AI Models Module - مدیریت مدل‌های هوش مصنوعی
ماژول یکپارچه برای مدیریت تمام مدل‌های AI در سیستم معاملاتی

شامل:
- Sentiment Analysis Models
- Time Series Forecasting Models
- Ensemble Models
- Model Registry
"""


# Import warning suppressor
try:
    from warning_suppressor import suppress_all_warnings
    suppress_all_warnings()
except ImportError:
    import warnings
    warnings.filterwarnings('ignore')

import os
import sys
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import numpy as np
import pandas as pd
from dataclasses import dataclass

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import sentiment analyzer
try:
    from utils.sentiment_analyzer import AdvancedSentimentAnalyzer
    SENTIMENT_AVAILABLE = True
    
    # Mock SentimentResult for compatibility
    @dataclass
    class SentimentResult:
        label: str
        score: float
        confidence: float
        market_impact: float = 0.0
        
except ImportError as e:
    logging.warning(f"Sentiment analyzer not available: {e}")
    SENTIMENT_AVAILABLE = False
    
    @dataclass
    class SentimentResult:
        label: str = 'neutral'
        score: float = 0.0
        confidence: float = 0.0
        market_impact: float = 0.0

# Import other utilities
try:
    from utils.plutus_integration import PlutusFinancialForecaster
    from utils.adaptive_plutus_system import AdaptivePlutusSystem
    PLUTUS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Plutus integration not available: {e}")
    PLUTUS_AVAILABLE = False

# Configure logging
logger = logging.getLogger(__name__)

class ModelInfo:
    """اطلاعات مدل"""
    def __init__(self, name: str, type: str, status: str, 
                 model_path: str = None, config: Dict = None):
        self.name = name
        self.type = type
        self.status = status  # 'loaded', 'failed', 'pending'
        self.model_path = model_path
        self.config = config or {}
        self.created_at = datetime.now()
        self.last_used = None
        self.performance_metrics = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        return {
            'name': self.name,
            'type': self.type,
            'status': self.status,
            'model_path': self.model_path,
            'config': self.config,
            'created_at': self.created_at.isoformat(),
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'performance_metrics': self.performance_metrics
        }

class ModelRegistry:
    """رجیستری مدل‌ها"""
    def __init__(self):
        self.models = {}
        self.model_info = {}
        self.logger = logging.getLogger(__name__ + ".ModelRegistry")
    
    def register(self, name: str, model: Any, model_type: str, 
                 config: Dict = None) -> bool:
        """ثبت مدل جدید"""
        try:
            self.models[name] = model
            self.model_info[name] = ModelInfo(
                name=name,
                type=model_type,
                status='loaded',
                config=config
            )
            self.logger.info(f"✅ Model registered: {name} ({model_type})")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to register model {name}: {e}")
            return False
    
    def get_model(self, name: str) -> Optional[Any]:
        """دریافت مدل"""
        model = self.models.get(name)
        if model and name in self.model_info:
            self.model_info[name].last_used = datetime.now()
        return model
    
    def get_info(self, name: str) -> Optional[ModelInfo]:
        """دریافت اطلاعات مدل"""
        return self.model_info.get(name)
    
    def get_all_info(self) -> Dict[str, ModelInfo]:
        """دریافت اطلاعات تمام مدل‌ها"""
        return self.model_info.copy()
    
    def list_models(self, model_type: str = None) -> List[str]:
        """لیست مدل‌ها"""
        if model_type:
            return [
                name for name, info in self.model_info.items()
                if info.type == model_type
            ]
        return list(self.models.keys())
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت مدل‌ها"""
        return {
            'total_models': len(self.models),
            'loaded_models': len([
                info for info in self.model_info.values()
                if info.status == 'loaded'
            ]),
            'failed_models': len([
                info for info in self.model_info.values()
                if info.status == 'failed'
            ]),
            'model_types': list(set(
                info.type for info in self.model_info.values()
            ))
        }

# Global model registry
model_registry = ModelRegistry()

class MockSentimentAnalyzer:
    """Mock sentiment analyzer for testing"""
    def __init__(self, *args, **kwargs):
        pass
    
    def analyze(self, text: str, language: str = None, source: str = None) -> SentimentResult:
        """Mock analyze method"""
        return SentimentResult(
            label='neutral',
            score=0.0,
            confidence=0.5,
            market_impact=0.0
        )
    
    def get_market_sentiment(self) -> Dict[str, Any]:
        """Mock market sentiment"""
        return {
            'overall_sentiment': 0.0,
            'trend': 'neutral',
            'confidence': 0.5,
            'momentum': 0.0,
            'volatility': 0.0
        }
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Mock cache stats"""
        return {'hits': 0, 'misses': 0, 'size': 0}

class SentimentEnsemble:
    """مجموعه مدل‌های تحلیل احساسات"""
    
    def __init__(self, languages: List[str] = None):
        self.languages = languages or ['en', 'fa']
        self.analyzer = None
        self.logger = logging.getLogger(__name__ + ".SentimentEnsemble")
        self.initialize()
    
    def initialize(self) -> bool:
        """مقداردهی اولیه"""
        try:
            if SENTIMENT_AVAILABLE:
                self.analyzer = AdvancedSentimentAnalyzer(
                    languages=self.languages,
                    enable_cache=True
                )
            else:
                self.analyzer = MockSentimentAnalyzer()
            
            self.logger.info("✅ Sentiment ensemble initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize sentiment ensemble: {e}")
            self.analyzer = MockSentimentAnalyzer()
            return False
    
    def analyze(self, text: str, language: str = None, 
                source: str = None) -> Optional[SentimentResult]:
        """تحلیل احساسات"""
        if not self.analyzer:
            return None
        
        try:
            return self.analyzer.analyze(text, language, source)
        except Exception as e:
            self.logger.error(f"Error analyzing sentiment: {e}")
            return SentimentResult(label='neutral', score=0.0, confidence=0.0)
    
    def analyze_batch(self, texts: List[str], sources: List[str] = None,
                      parallel: bool = True) -> List[SentimentResult]:
        """تحلیل batch"""
        if not self.analyzer:
            return []
        
        results = []
        for text in texts:
            result = self.analyze(text)
            if result:
                results.append(result)
        
        return results
    
    def get_market_sentiment(self) -> Dict[str, Any]:
        """دریافت احساسات کلی بازار"""
        if not self.analyzer:
            return {}
        
        try:
            return self.analyzer.get_market_sentiment()
        except Exception as e:
            self.logger.error(f"Error getting market sentiment: {e}")
            return {}
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'status': 'healthy' if self.analyzer else 'failed',
            'languages': self.languages,
            'cache_stats': self.analyzer.get_cache_stats() if hasattr(self.analyzer, 'get_cache_stats') else {}
        }

class MockPlutusSystem:
    """Mock Plutus system for testing"""
    def __init__(self, *args, **kwargs):
        pass
    
    def get_adaptive_signal(self, symbol: str, timeframe: str) -> Dict[str, Any]:
        """Mock adaptive signal"""
        return {
            'trend': 'neutral',
            'confidence': 0.5,
            'forecast': {'mean': [1.1000]},
            'trend_strength': 0.5
        }

class TimeSeriesEnsemble:
    """مجموعه مدل‌های پیش‌بینی سری زمانی"""
    
    def __init__(self):
        self.plutus_system = None
        self.symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
        self.timeframes = ['H1', 'H4', 'D1']
        self.logger = logging.getLogger(__name__ + ".TimeSeriesEnsemble")
        self.initialize()
    
    def initialize(self) -> bool:
        """مقداردهی اولیه"""
        try:
            if PLUTUS_AVAILABLE:
                # AdaptivePlutusSystem only takes db_path parameter
                self.plutus_system = AdaptivePlutusSystem(db_path="adaptive_plutus.db")
                # Start continuous learning if needed
                # self.plutus_system.start_continuous_learning(symbols=self.symbols)
            else:
                self.plutus_system = MockPlutusSystem()
            
            self.logger.info("✅ Time series ensemble initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize time series ensemble: {e}")
            self.plutus_system = MockPlutusSystem()
            return False
    
    def predict(self, symbol: str, timeframe: str = 'H1',
                steps: int = 1) -> Optional[Dict[str, Any]]:
        """پیش‌بینی قیمت"""
        if not self.plutus_system:
            return None
        
        try:
            return self.plutus_system.get_adaptive_signal(symbol, timeframe)
        except Exception as e:
            self.logger.error(f"Error predicting {symbol}: {e}")
            return None
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'status': 'healthy' if self.plutus_system else 'failed',
            'symbols': self.symbols,
            'timeframes': self.timeframes
        }

class ModelEnsemble:
    """Ensemble of multiple models for better predictions"""
    
    def __init__(self, models: List[Any] = None):
        self.models = models or []
        self.weights = {}
        self.performance_history = {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def add_model(self, model: Any, weight: float = 1.0, name: str = None):
        """اضافه کردن مدل به ensemble"""
        if name is None:
            name = f"model_{len(self.models)}"
        
        self.models.append(model)
        self.weights[name] = weight
        self.performance_history[name] = []
        self.logger.info(f"✅ Added model {name} with weight {weight}")
    
    def predict(self, data: Any) -> Dict[str, Any]:
        """پیش‌بینی ensemble"""
        if not self.models:
            return {'error': 'No models in ensemble'}
        
        predictions = []
        total_weight = 0
        
        for i, model in enumerate(self.models):
            model_name = f"model_{i}"
            weight = self.weights.get(model_name, 1.0)
            
            try:
                if hasattr(model, 'predict'):
                    pred = model.predict(data)
                elif hasattr(model, 'analyze'):
                    pred = model.analyze(data)
                else:
                    pred = {'prediction': 0.5, 'confidence': 0.5}
                
                predictions.append({
                    'model': model_name,
                    'prediction': pred,
                    'weight': weight
                })
                total_weight += weight
                
            except Exception as e:
                self.logger.error(f"Error in model {model_name}: {e}")
        
        # Weighted average (simplified)
        if predictions and total_weight > 0:
            avg_prediction = sum(p['weight'] for p in predictions) / total_weight
            avg_confidence = sum(p['weight'] for p in predictions) / total_weight
        else:
            avg_prediction = 0.5
            avg_confidence = 0.5
        
        return {
            'ensemble_prediction': avg_prediction,
            'ensemble_confidence': avg_confidence,
            'individual_predictions': predictions,
            'model_count': len(predictions)
        }
    
    def update_weights(self, performance_data: Dict[str, float]):
        """به‌روزرسانی وزن‌ها بر اساس عملکرد"""
        for model_name, performance in performance_data.items():
            if model_name in self.weights:
                self.performance_history[model_name].append(performance)
                # Simple weight adjustment based on recent performance
                recent_performance = sum(self.performance_history[model_name][-5:]) / min(5, len(self.performance_history[model_name]))
                self.weights[model_name] = max(0.1, min(2.0, recent_performance))
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت ensemble"""
        return {
            'model_count': len(self.models),
            'weights': self.weights,
            'status': 'healthy' if self.models else 'empty'
        }

# Aliases for compatibility
WeightedEnsemble = ModelEnsemble
VotingEnsemble = ModelEnsemble

class ModelManager:
    """مدیر مدل‌ها"""
    
    def __init__(self):
        self.models = {}
        self.registry = model_registry
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def initialize(self) -> bool:
        """مقداردهی مدل‌ها"""
        try:
            self.logger.info("🤖 Initializing AI models...")
            
            # Initialize sentiment ensemble
            sentiment_ensemble = SentimentEnsemble()
            if sentiment_ensemble.initialize():
                self.registry.register("sentiment_ensemble", sentiment_ensemble, "sentiment_analysis")
            
            # Initialize time series ensemble
            timeseries_ensemble = TimeSeriesEnsemble()
            if timeseries_ensemble.initialize():
                self.registry.register("timeseries_ensemble", timeseries_ensemble, "time_series_forecasting")
            
            self.logger.info("✅ All AI models initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize AI models: {e}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت تمام مدل‌ها"""
        return {
            'registry_status': self.registry.health_check(),
            'total_models': len(self.models),
            'status': 'healthy'
        }

# Global model manager
model_manager = ModelManager()

class HuggingFaceModel:
    """Wrapper برای مدل‌های HuggingFace"""
    
    def __init__(self, model_name: str, task: str = "sentiment-analysis"):
        self.model_name = model_name
        self.task = task
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            if SENTIMENT_AVAILABLE:
                # In a real implementation, you would load the actual HuggingFace model
                # For now, we'll use a mock implementation
                self.model = f"Mock_{self.model_name}"
                self.tokenizer = f"Mock_tokenizer_{self.model_name}"
                self.pipeline = f"Mock_pipeline_{self.model_name}"
                self.logger.info(f"✅ Mock HuggingFace model loaded: {self.model_name}")
                return True
            else:
                self.logger.warning(f"⚠️ HuggingFace not available, using mock for {self.model_name}")
                return False
        except Exception as e:
            self.logger.error(f"❌ Failed to load HuggingFace model {self.model_name}: {e}")
            return False
    
    def predict(self, text: str) -> Dict[str, Any]:
        """پیش‌بینی"""
        if not self.model:
            return {'error': 'Model not loaded'}
        
        # Mock prediction
        return {
            'model_name': self.model_name,
            'input': text,
            'prediction': 'neutral',
            'confidence': 0.5
        }
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'task': self.task,
            'loaded': self.model is not None,
            'status': 'healthy' if self.model else 'not_loaded'
        }

class ChronosModel:
    """Mock Chronos Model for time series forecasting"""
    
    def __init__(self, model_name: str = "amazon/chronos-t5-small"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            # Mock implementation
            self.model = f"Mock_{self.model_name}"
            self.tokenizer = f"Mock_tokenizer_{self.model_name}"
            self.logger.info(f"✅ Mock Chronos model loaded: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to load Chronos model: {e}")
            return False
    
    def predict(self, time_series_data: List[float], 
                prediction_length: int = 1) -> Dict[str, Any]:
        """پیش‌بینی سری زمانی"""
        if not self.model:
            return {'error': 'Model not loaded'}
        
        # Mock prediction
        return {
            'model_name': self.model_name,
            'predictions': [0.0] * prediction_length,
            'confidence_intervals': {
                'lower': [0.0] * prediction_length,
                'upper': [0.0] * prediction_length
            },
            'uncertainty': 0.1,
            'timestamp': datetime.now().isoformat()
        }
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.model is not None,
            'status': 'healthy' if self.model else 'not_loaded'
        }

class TimeSeriesModel:
    """Mock Time Series Model for forecasting"""
    
    def __init__(self, model_name: str = "time_series_model"):
        self.model_name = model_name
        self.model = None
        self.scaler = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            # Mock implementation
            self.model = f"Mock_{self.model_name}"
            self.scaler = f"Mock_scaler_{self.model_name}"
            self.logger.info(f"✅ Mock Time Series model loaded: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to load Time Series model: {e}")
            return False
    
    def predict(self, data: List[float], steps: int = 1) -> Dict[str, Any]:
        """پیش‌بینی سری زمانی"""
        if not self.model:
            return {'error': 'Model not loaded'}
        
        # Mock prediction
        return {
            'model_name': self.model_name,
            'predictions': [0.0] * steps,
            'confidence': 0.7,
            'trend': 'neutral',
            'volatility': 0.1,
            'timestamp': datetime.now().isoformat()
        }
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.model is not None,
            'status': 'healthy' if self.model else 'not_loaded'
        }

class LayoutLMModel:
    """Mock LayoutLM Model for document understanding"""
    
    def __init__(self, model_name: str = "microsoft/layoutlm-base-uncased"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            # Mock implementation
            self.model = f"Mock_{self.model_name}"
            self.tokenizer = f"Mock_tokenizer_{self.model_name}"
            self.logger.info(f"✅ Mock LayoutLM model loaded: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to load LayoutLM model: {e}")
            return False
    
    def analyze_document(self, document_path: str) -> Dict[str, Any]:
        """تحلیل سند"""
        if not self.model:
            return {'error': 'Model not loaded'}
        
        # Mock analysis
        return {
            'model_name': self.model_name,
            'document_path': document_path,
            'extracted_text': 'Mock extracted text',
            'entities': [],
            'confidence': 0.8,
            'timestamp': datetime.now().isoformat()
        }
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.model is not None,
            'status': 'healthy' if self.model else 'not_loaded'
        }

class T5Model:
    """Mock T5 Model for text generation"""
    
    def __init__(self, model_name: str = "t5-base"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            self.model = f"Mock_{self.model_name}"
            self.tokenizer = f"Mock_tokenizer_{self.model_name}"
            self.logger.info(f"✅ Mock T5 model loaded: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to load T5 model: {e}")
            return False
    
    def generate_text(self, prompt: str, max_length: int = 100) -> str:
        """تولید متن"""
        if not self.model:
            return "Model not loaded"
        
        return f"Mock generated text for: {prompt[:50]}..."
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.model is not None,
            'status': 'healthy' if self.model else 'not_loaded'
        }

class BERTModel:
    """Mock BERT Model for general language understanding"""
    
    def __init__(self, model_name: str = "bert-base-uncased"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            self.model = f"Mock_{self.model_name}"
            self.tokenizer = f"Mock_tokenizer_{self.model_name}"
            self.logger.info(f"✅ Mock BERT model loaded: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to load BERT model: {e}")
            return False
    
    def encode(self, text: str) -> List[float]:
        """رمزگذاری متن"""
        if not self.model:
            return []
        
        # Mock encoding
        return [0.0] * 768
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.model is not None,
            'status': 'healthy' if self.model else 'not_loaded'
        }

class BARTModel:
    """Mock BART Model for text generation and summarization"""
    
    def __init__(self, model_name: str = "facebook/bart-large-cnn"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            self.model = f"Mock_{self.model_name}"
            self.tokenizer = f"Mock_tokenizer_{self.model_name}"
            self.logger.info(f"✅ Mock BART model loaded: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to load BART model: {e}")
            return False
    
    def summarize(self, text: str, max_length: int = 150) -> str:
        """خلاصه‌سازی متن"""
        if not self.model:
            return "Model not loaded"
        
        return f"Mock summary of: {text[:50]}..."
    
    def generate_text(self, prompt: str, max_length: int = 100) -> str:
        """تولید متن"""
        if not self.model:
            return "Model not loaded"
        
        return f"Mock generated text for: {prompt[:50]}..."
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.model is not None,
            'status': 'healthy' if self.model else 'not_loaded'
        }

class DocumentAnalyzer:
    """Mock Document Analyzer for document processing"""
    
    def __init__(self, model_name: str = "document_analyzer"):
        self.model_name = model_name
        self.model = None
        self.processor = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def load(self) -> bool:
        """بارگذاری مدل"""
        try:
            self.model = f"Mock_{self.model_name}"
            self.processor = f"Mock_processor_{self.model_name}"
            self.logger.info(f"✅ Mock Document Analyzer loaded: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to load Document Analyzer: {e}")
            return False
    
    def analyze_document(self, document_path: str) -> Dict[str, Any]:
        """تحلیل سند"""
        if not self.model:
            return {'error': 'Model not loaded'}
        
        return {
            'model_name': self.model_name,
            'document_path': document_path,
            'text_content': 'Mock extracted text',
            'entities': [],
            'summary': 'Mock document summary',
            'sentiment': 'neutral',
            'confidence': 0.8,
            'timestamp': datetime.now().isoformat()
        }
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت"""
        return {
            'model_name': self.model_name,
            'loaded': self.model is not None,
            'status': 'healthy' if self.model else 'not_loaded'
        }

# Try to import real models if available
try:
    from .sentiment_models import FinBERTModel, CryptoBERTModel, FinancialSentimentModel
    REAL_MODELS_AVAILABLE = True
except ImportError:
    logger.warning("Real sentiment models not available, using mock implementations")
    REAL_MODELS_AVAILABLE = False
    
    # Mock implementations for testing
    class FinBERTModel:
        """Mock FinBERT Model for financial sentiment analysis"""
        
        def __init__(self, model_name: str = "ProsusAI/finbert"):
            self.model_name = model_name
            self.model = None
            self.tokenizer = None
            self.logger = logging.getLogger(self.__class__.__name__)
        
        def load(self) -> bool:
            """بارگذاری مدل"""
            try:
                self.model = f"Mock_{self.model_name}"
                self.tokenizer = f"Mock_tokenizer_{self.model_name}"
                self.logger.info(f"✅ Mock FinBERT model loaded: {self.model_name}")
                return True
            except Exception as e:
                self.logger.error(f"❌ Failed to load FinBERT model: {e}")
                return False
        
        def analyze_sentiment(self, text: str) -> Dict[str, Any]:
            """تحلیل احساسات"""
            if not self.model:
                return {'error': 'Model not loaded'}
            
            return {
                'model_name': self.model_name,
                'text': text,
                'sentiment': 'neutral',
                'confidence': 0.7,
                'label': 'neutral',
                'probabilities': {'positive': 0.3, 'negative': 0.3, 'neutral': 0.4}
            }
        
        def health_check(self) -> Dict[str, Any]:
            """بررسی سلامت"""
            return {
                'model_name': self.model_name,
                'loaded': self.model is not None,
                'status': 'healthy' if self.model else 'not_loaded'
            }
    
    class CryptoBERTModel:
        """Mock CryptoBERT Model for cryptocurrency sentiment analysis"""
        
        def __init__(self, model_name: str = "ElKulako/cryptobert"):
            self.model_name = model_name
            self.model = None
            self.tokenizer = None
            self.logger = logging.getLogger(self.__class__.__name__)
        
        def load(self) -> bool:
            """بارگذاری مدل"""
            try:
                self.model = f"Mock_{self.model_name}"
                self.tokenizer = f"Mock_tokenizer_{self.model_name}"
                self.logger.info(f"✅ Mock CryptoBERT model loaded: {self.model_name}")
                return True
            except Exception as e:
                self.logger.error(f"❌ Failed to load CryptoBERT model: {e}")
                return False
        
        def analyze_sentiment(self, text: str) -> Dict[str, Any]:
            """تحلیل احساسات"""
            if not self.model:
                return {'error': 'Model not loaded'}
            
            return {
                'model_name': self.model_name,
                'text': text,
                'sentiment': 'neutral',
                'confidence': 0.7,
                'label': 'neutral',
                'probabilities': {'positive': 0.3, 'negative': 0.3, 'neutral': 0.4}
            }
        
        def health_check(self) -> Dict[str, Any]:
            """بررسی سلامت"""
            return {
                'model_name': self.model_name,
                'loaded': self.model is not None,
                'status': 'healthy' if self.model else 'not_loaded'
            }
    
    class FinancialSentimentModel:
        """Mock Financial Sentiment Model for general financial sentiment analysis"""
        
        def __init__(self, model_name: str = "financial_sentiment_model"):
            self.model_name = model_name
            self.model = None
            self.tokenizer = None
            self.logger = logging.getLogger(self.__class__.__name__)
        
        def load(self) -> bool:
            """بارگذاری مدل"""
            try:
                self.model = f"Mock_{self.model_name}"
                self.tokenizer = f"Mock_tokenizer_{self.model_name}"
                self.logger.info(f"✅ Mock Financial Sentiment model loaded: {self.model_name}")
                return True
            except Exception as e:
                self.logger.error(f"❌ Failed to load Financial Sentiment model: {e}")
                return False
        
        def analyze_sentiment(self, text: str) -> Dict[str, Any]:
            """تحلیل احساسات"""
            if not self.model:
                return {'error': 'Model not loaded'}
            
            return {
                'model_name': self.model_name,
                'text': text,
                'sentiment': 'neutral',
                'confidence': 0.7,
                'label': 'neutral',
                'probabilities': {'positive': 0.3, 'negative': 0.3, 'neutral': 0.4}
            }
        
        def health_check(self) -> Dict[str, Any]:
            """بررسی سلامت"""
            return {
                'model_name': self.model_name,
                'loaded': self.model is not None,
                'status': 'healthy' if self.model else 'not_loaded'
            }

# Global instances for compatibility
sentiment_ensemble = None
time_series_ensemble = None
portfolio_optimizer = None
risk_manager = None
market_regime_detector = None

def initialize_models() -> ModelManager:
    """مقداردهی اولیه مدل‌ها"""
    model_manager.initialize()
    return model_manager

def create_sentiment_ensemble(languages: List[str] = None) -> Optional[SentimentEnsemble]:
    """ایجاد مجموعه تحلیل احساسات"""
    try:
        return SentimentEnsemble(languages)
    except Exception as e:
        logger.error(f"Failed to create sentiment ensemble: {e}")
        return None

def create_timeseries_ensemble() -> Optional[TimeSeriesEnsemble]:
    """ایجاد مجموعه پیش‌بینی سری زمانی"""
    try:
        return TimeSeriesEnsemble()
    except Exception as e:
        logger.error(f"Failed to create time series ensemble: {e}")
        return None

def get_model_info(model_name: str) -> Optional[Dict[str, Any]]:
    """دریافت اطلاعات مدل"""
    info = model_registry.get_info(model_name)
    return info.to_dict() if info else None

def get_model(name: str) -> Optional[Any]:
    """دریافت مدل از registry"""
    return model_registry.get_model(name)

def get_available_models() -> List[str]:
    """دریافت لیست مدل‌های موجود"""
    return model_registry.list_models()

def health_check() -> Dict[str, Any]:
    """بررسی سلامت کل سیستم"""
    return model_manager.health_check()

# Main exports
__all__ = [
    'ModelRegistry',
    'HuggingFaceModel',
    'ChronosModel',
    'TimeSeriesModel',
    'LayoutLMModel',
    'T5Model',
    'BERTModel',
    'BARTModel',
    'DocumentAnalyzer',
    'ModelEnsemble',
    'WeightedEnsemble',
    'VotingEnsemble',
    'FinBERTModel',
    'CryptoBERTModel',
    'FinancialSentimentModel',
    'SentimentEnsemble',
    'TimeSeriesEnsemble',
    'ModelManager',
    'SentimentResult',
    'ModelInfo',
    'sentiment_ensemble',
    'time_series_ensemble',
    'portfolio_optimizer',
    'risk_manager',
    'market_regime_detector',
    'model_registry',
    'model_manager',
    'get_model',
    'get_available_models',
    'get_model_info',
    'health_check',
    'initialize_models',
    'create_sentiment_ensemble',
    'create_timeseries_ensemble',
    'REAL_MODELS_AVAILABLE',
    'SENTIMENT_AVAILABLE',
    'PLUTUS_AVAILABLE'
] 