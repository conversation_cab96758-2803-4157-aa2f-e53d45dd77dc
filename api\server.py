#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌐 API Server
سرور API سیستم معاملاتی
"""

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
import asyncio
import signal
import sys
import os

# اضافه کردن مسیر پروژه
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.endpoints import create_app
from core.config import get_config

# تنظیم logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TradingAPIServer:
    """سرور API سیستم معاملاتی"""
    
    def __init__(self):
        self.app = None
        self.config = get_config()
        self.server = None
        self.is_running = False
        
    async def startup(self):
        """راه‌اندازی سرور"""
        logger.info("🚀 Starting Trading API Server...")
        
        try:
            # ایجاد FastAPI app
            self.app = create_app()
            
            # تنظیم middleware اضافی
            self.setup_middleware()
            
            # تنظیم event handlers
            self.setup_event_handlers()
            
            logger.info("✅ API Server initialized successfully")
            self.is_running = True
            
        except Exception as e:
            logger.error(f"❌ Error starting server: {e}")
            raise
    
    def setup_middleware(self):
        """تنظیم middleware های اضافی"""
        if self.app:
            # Request logging middleware
            @self.app.middleware("http")
            async def log_requests(request, call_next):
                start_time = asyncio.get_event_loop().time()
                response = await call_next(request)
                process_time = asyncio.get_event_loop().time() - start_time
                
                logger.info(
                    f"{request.method} {request.url.path} - "
                    f"Status: {response.status_code} - "
                    f"Time: {process_time:.3f}s"
                )
                return response
    
    def setup_event_handlers(self):
        """تنظیم event handlers"""
        if self.app:
            @self.app.on_event("startup")
            async def startup_event():
                logger.info("🌟 API Server startup complete")
            
            @self.app.on_event("shutdown")
            async def shutdown_event():
                logger.info("🔄 API Server shutting down...")
                self.is_running = False
    
    async def shutdown(self):
        """خاموش کردن سرور"""
        logger.info("🔴 Shutting down API Server...")
        self.is_running = False
        
        if self.server:
            self.server.should_exit = True
            await self.server.shutdown()
    
    def run(self, host: str = "0.0.0.0", port: int = 8000, debug: bool = False):
        """اجرای سرور"""
        try:
            # تنظیم signal handlers
            def signal_handler(signum, frame):
                logger.info(f"Received signal {signum}, shutting down...")
                asyncio.create_task(self.shutdown())
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            # راه‌اندازی سرور
            asyncio.run(self.startup())
            
            # اجرای uvicorn
            config = uvicorn.Config(
                app=self.app,
                host=host,
                port=port,
                log_level="info" if debug else "warning",
                reload=debug,
                access_log=debug
            )
            
            self.server = uvicorn.Server(config)
            
            logger.info(f"🌐 Server running on http://{host}:{port}")
            logger.info(f"📚 API Documentation: http://{host}:{port}/docs")
            logger.info(f"📋 ReDoc Documentation: http://{host}:{port}/redoc")
            
            asyncio.run(self.server.serve())
            
        except KeyboardInterrupt:
            logger.info("🔴 Server stopped by user")
        except Exception as e:
            logger.error(f"❌ Server error: {e}")
            raise
        finally:
            logger.info("🏁 Server shutdown complete")

class APIManager:
    """مدیر API"""
    
    def __init__(self):
        self.server = TradingAPIServer()
        self.config = get_config()
    
    def start_server(self, background: bool = False):
        """شروع سرور"""
        if background:
            # اجرای در پس‌زمینه
            import threading
            server_thread = threading.Thread(
                target=self.server.run,
                kwargs={
                    'host': self.config.get('api', {}).get('host', '0.0.0.0'),
                    'port': self.config.get('api', {}).get('port', 8000),
                    'debug': self.config.get('api', {}).get('debug', False)
                }
            )
            server_thread.daemon = True
            server_thread.start()
            return server_thread
        else:
            # اجرای مستقیم
            self.server.run(
                host=self.config.get('api', {}).get('host', '0.0.0.0'),
                port=self.config.get('api', {}).get('port', 8000),
                debug=self.config.get('api', {}).get('debug', False)
            )
    
    def stop_server(self):
        """توقف سرور"""
        asyncio.create_task(self.server.shutdown())
    
    def get_server_status(self):
        """وضعیت سرور"""
        return {
            'running': self.server.is_running,
            'app_initialized': self.server.app is not None,
            'config_loaded': self.config is not None
        }

def main():
    """اجرای اصلی"""
    try:
        # ایجاد مدیر API
        api_manager = APIManager()
        
        # شروع سرور
        api_manager.start_server()
        
    except Exception as e:
        logger.error(f"❌ Failed to start API server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
