#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simple test for Hierarchical RL to fix dimension issues
"""

import numpy as np
import torch
from models.hierarchical_rl import HierarchicalRL

def test_hierarchical_rl():
    """Test hierarchical RL with proper dimensions"""
    
    # Create model
    obs_dim = 20
    model = HierarchicalRL(obs_dim=obs_dim, device='cpu')
    
    print("Model created successfully")
    
    # Test observation extraction
    obs = np.random.randn(obs_dim)
    
    strategic_obs = model.get_strategic_observation(obs)
    print(f"Strategic obs shape: {strategic_obs.shape}")
    
    tactical_obs = model.get_tactical_observation(obs, 1)
    print(f"Tactical obs shape: {tactical_obs.shape}")
    
    execution_obs = model.get_execution_observation(obs, 1, 2)
    print(f"Execution obs shape: {execution_obs.shape}")
    
    # Test complete action
    try:
        strategy, tactical, execution = model.act(obs)
        print(f"Success! Strategy: {strategy}, Tactical: {tactical}, Execution: {execution}")
        return True
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    test_hierarchical_rl() 