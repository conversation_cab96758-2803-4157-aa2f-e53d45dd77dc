# مستند جامع: EnsembleModel

## مسئولیت
ترکیب چندین مدل RL با voting هوشمند، وزن‌دهی پویا، اندازه‌گیری اطمینان و یادگیری آنلاین برای بهبود تصمیم‌گیری.

## پارامترها
- model_configs: لیست پیکربندی مدل‌ها
- voting_method: روش رأی‌گیری (weighted, majority, rank, confidence)
- weight_update_freq: فرکانس به‌روزرسانی وزن‌ها
- confidence_threshold: آستانه اطمینان
- diversity_weight: وزن تنوع

## متدهای کلیدی
- predict: پیش‌بینی با ensemble
- update_reward: به‌روزرسانی پاداش
- save/load: ذخیره و بارگذاری
- plot_weights_history: رسم تاریخچه وزن‌ها

## نمونه کد
```python
from models.ensemble_model import EnsembleModel
configs = [{'model_type': 'ppo'}, {'model_type': 'sac'}]
ensemble = EnsembleModel(configs, env, voting_method='weighted')
action, state = ensemble.predict(observation)
```

## مدیریت خطا
در صورت خطا در بارگذاری مدل، آن مدل نادیده گرفته می‌شود.

## بهترین شیوه
- از مدل‌های متنوع برای بهبود عملکرد استفاده کنید.
- وزن‌ها را به طور منظم به‌روزرسانی کنید.

## نمودار
- نمودار تغییرات وزن‌ها و عملکرد مدل‌ها قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول در سیستم معاملاتی یکپارچه (models/unified_trading_system.py) و مثال‌ها استفاده شده است.

## وضعیت عملیاتی
✅ عملیاتی و در جریان اصلی پروژه فعال است. 