#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
👁️ Model Monitoring System Test
تست سیستم نظارت بر مدل‌ها
"""

import os
import sys
import time
import random
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_model_monitoring_system():
    """تست سیستم نظارت مدل‌ها"""
    print("👁️ Testing Model Monitoring System")
    print("=" * 50)
    
    try:
        # Import the model monitoring system
        from core.model_monitoring import (
            ModelMonitor,
            ModelMonitoringManager,
            MonitoringRule,
            AlertSeverity,
            DriftType,
            PerformanceMetric,
            StatisticalDriftDetector,
            PerformanceTracker,
            monitoring_context
        )
        
        # Test 1: Model Monitor Creation
        print("\n1️⃣ Testing Model Monitor Creation...")
        
        monitor = ModelMonitor("trading_model", "1.0")
        print(f"   ✓ Monitor created: {monitor.model_name}:v{monitor.model_version}")
        print(f"   ✓ Initial status: {monitor.status.value}")
        
        # Test 2: Reference Data Setup
        print("\n2️⃣ Testing Reference Data Setup...")
        
        # Create synthetic reference data
        np.random.seed(42)
        reference_features = {
            "price_change": np.random.normal(0, 1, 1000).tolist(),
            "volume": np.random.lognormal(0, 1, 1000).tolist(),
            "volatility": np.random.gamma(2, 2, 1000).tolist(),
            "rsi": np.random.uniform(20, 80, 1000).tolist()
        }
        
        monitor.set_reference_data(reference_features)
        print(f"   ✓ Reference data set for {len(reference_features)} features")
        print(f"   ✓ Has reference data: {monitor.get_statistics()['has_reference_data']}")
        
        # Test 3: Monitoring Rules
        print("\n3️⃣ Testing Monitoring Rules...")
        
        # Accuracy rule
        accuracy_rule = MonitoringRule(
            rule_id="accuracy_threshold",
            name="Model Accuracy Threshold",
            metric="accuracy",
            operator="<",
            threshold=0.8,
            severity=AlertSeverity.HIGH,
            time_window=60,
            description="Alert when accuracy drops below 80%"
        )
        monitor.add_monitoring_rule(accuracy_rule)
        print(f"   ✓ Accuracy rule added: {accuracy_rule.name}")
        
        # Latency rule
        latency_rule = MonitoringRule(
            rule_id="latency_threshold",
            name="Model Latency Threshold",
            metric="avg_latency",
            operator=">",
            threshold=100.0,
            severity=AlertSeverity.MEDIUM,
            time_window=30,
            description="Alert when average latency exceeds 100ms"
        )
        monitor.add_monitoring_rule(latency_rule)
        print(f"   ✓ Latency rule added: {latency_rule.name}")
        
        # MSE rule for regression
        mse_rule = MonitoringRule(
            rule_id="mse_threshold",
            name="MSE Threshold",
            metric="mse",
            operator=">",
            threshold=0.1,
            severity=AlertSeverity.MEDIUM,
            time_window=60,
            description="Alert when MSE exceeds 0.1"
        )
        monitor.add_monitoring_rule(mse_rule)
        print(f"   ✓ MSE rule added: {mse_rule.name}")
        
        # Test 4: Performance Tracking
        print("\n4️⃣ Testing Performance Tracking...")
        
        # Simulate predictions with good performance
        print("   Simulating good performance...")
        for i in range(50):
            features = {
                "price_change": random.gauss(0, 1),
                "volume": random.lognormvariate(0, 1),
                "volatility": random.gammavariate(2, 2),
                "rsi": random.uniform(20, 80)
            }
            
            # Binary classification task
            true_value = 1 if features["price_change"] > 0 else 0
            prediction = true_value if random.random() > 0.05 else 1 - true_value  # 95% accuracy
            latency = random.gauss(50, 10)  # Good latency
            
            monitor.record_prediction(features, prediction, true_value, latency)
        
        print(f"   ✓ Recorded 50 good predictions")
        
        # Test 5: Performance Snapshot
        print("\n5️⃣ Testing Performance Snapshot...")
        
        snapshot = monitor.create_performance_snapshot(time_window_minutes=60)
        print(f"   ✓ Snapshot created:")
        print(f"     - Sample count: {snapshot.sample_count}")
        print(f"     - Metrics: {list(snapshot.metrics.keys())}")
        
        for metric, value in snapshot.metrics.items():
            print(f"     - {metric}: {value:.4f}")
        
        # Test 6: Drift Detection
        print("\n6️⃣ Testing Drift Detection...")
        
        # Simulate data with drift
        print("   Simulating data drift...")
        for i in range(30):
            features = {
                "price_change": random.gauss(2, 1),  # Shifted mean (drift)
                "volume": random.lognormvariate(1, 1),  # Shifted distribution
                "volatility": random.gammavariate(3, 3),  # Different parameters
                "rsi": random.uniform(40, 90)  # Shifted range
            }
            
            prediction = 1 if features["price_change"] > 0 else 0
            actual = prediction if random.random() > 0.1 else 1 - prediction
            latency = random.gauss(60, 15)
            
            monitor.record_prediction(features, prediction, actual, latency)
        
        print(f"   ✓ Recorded 30 predictions with potential drift")
        
        # Test 7: Statistical Drift Detector
        print("\n7️⃣ Testing Statistical Drift Detector...")
        
        drift_detector = StatisticalDriftDetector(sensitivity=0.05)
        
        # Test data drift detection
        reference_data = np.random.normal(0, 1, 100)
        current_data = np.random.normal(1, 1, 100)  # Shifted mean
        
        drift_detected, drift_score, description = drift_detector.detect_data_drift(reference_data, current_data)
        print(f"   ✓ Data drift test:")
        print(f"     - Detected: {drift_detected}")
        print(f"     - Score: {drift_score:.4f}")
        print(f"     - Description: {description}")
        
        # Test concept drift detection
        predictions = [random.gauss(0, 1) for _ in range(200)]
        actuals = [p + random.gauss(0, 0.1) for p in predictions[:100]] + [p + random.gauss(0.5, 0.1) for p in predictions[100:]]
        
        concept_drift, concept_score, concept_desc = drift_detector.detect_concept_drift(predictions, actuals)
        print(f"   ✓ Concept drift test:")
        print(f"     - Detected: {concept_drift}")
        print(f"     - Score: {concept_score:.4f}")
        print(f"     - Description: {concept_desc}")
        
        # Test 8: Performance Tracker
        print("\n8️⃣ Testing Performance Tracker...")
        
        tracker = PerformanceTracker()
        
        # Record some predictions
        for i in range(100):
            prediction = random.choice([0, 1])
            actual = prediction if random.random() > 0.2 else 1 - prediction  # 80% accuracy
            latency = random.gauss(75, 20)
            
            tracker.record_prediction("test_model", "1.0", prediction, actual, latency)
        
        # Calculate metrics
        metrics = tracker.calculate_metrics("test_model", "1.0", time_window_minutes=60)
        print(f"   ✓ Performance metrics calculated:")
        for metric, value in metrics.items():
            print(f"     - {metric}: {value:.4f}")
        
        # Test performance trend
        trend = tracker.get_performance_trend("test_model", "1.0", "accuracy", time_points=5)
        print(f"   ✓ Performance trend calculated: {len(trend)} points")
        
        # Test 9: Alert System
        print("\n9️⃣ Testing Alert System...")
        
        alerts_received = []
        
        def alert_callback(alert):
            alerts_received.append(alert)
            print(f"   🚨 Alert: {alert.description}")
        
        monitor.add_alert_callback(alert_callback)
        
        # Simulate poor performance to trigger alerts
        print("   Simulating poor performance...")
        for i in range(20):
            features = {
                "price_change": random.gauss(0, 1),
                "volume": random.lognormvariate(0, 1),
                "volatility": random.gammavariate(2, 2),
                "rsi": random.uniform(20, 80)
            }
            
            # Poor accuracy
            prediction = random.choice([0, 1])
            actual = random.choice([0, 1])  # Random accuracy ~50%
            latency = random.gauss(150, 30)  # High latency
            
            monitor.record_prediction(features, prediction, actual, latency)
        
        # Check performance rules
        monitor.check_performance_rules()
        print(f"   ✓ Alerts received: {len(alerts_received)}")
        
        # Test 10: Monitoring Manager
        print("\n🔟 Testing Monitoring Manager...")
        
        manager = ModelMonitoringManager()
        
        # Create multiple monitors
        monitor1 = manager.create_monitor("model_a", "1.0")
        monitor2 = manager.create_monitor("model_b", "2.0")
        monitor3 = manager.create_monitor("model_c", "1.5")
        
        print(f"   ✓ Created 3 monitors")
        
        # Global alert callback
        global_alerts = []
        def global_alert_callback(alert):
            global_alerts.append(alert)
        
        manager.add_global_alert_callback(global_alert_callback)
        print(f"   ✓ Global alert callback added")
        
        # Start all monitoring
        manager.start_all_monitoring()
        print(f"   ✓ All monitoring started")
        
        # Get global statistics
        global_stats = manager.get_global_statistics()
        print(f"   ✓ Global statistics:")
        print(f"     - Total monitors: {global_stats['total_monitors']}")
        print(f"     - Active monitors: {global_stats['active_monitors']}")
        print(f"     - Monitoring coverage: {global_stats['monitoring_coverage']:.1f}%")
        
        # Stop all monitoring
        manager.stop_all_monitoring()
        print(f"   ✓ All monitoring stopped")
        
        # Test 11: Context Manager
        print("\n1️⃣1️⃣ Testing Monitoring Context...")
        
        with monitoring_context("context_model", "1.0") as ctx_monitor:
            print(f"   ✓ Context monitor created: {ctx_monitor.model_name}")
            
            # Record some predictions in context
            for i in range(10):
                features = {"feature1": random.gauss(0, 1)}
                prediction = random.choice([0, 1])
                actual = prediction
                
                ctx_monitor.record_prediction(features, prediction, actual)
            
            print(f"   ✓ Recorded 10 predictions in context")
        
        print(f"   ✓ Context monitoring completed")
        
        # Test 12: Monitoring Statistics
        print("\n1️⃣2️⃣ Testing Monitoring Statistics...")
        
        stats = monitor.get_statistics()
        print(f"   ✓ Monitor statistics:")
        print(f"     - Model: {stats['model_name']}:v{stats['model_version']}")
        print(f"     - Status: {stats['status']}")
        print(f"     - Total samples: {stats['total_samples']}")
        print(f"     - Total alerts: {stats['total_alerts']}")
        print(f"     - Recent alerts (24h): {stats['recent_alerts_24h']}")
        print(f"     - Monitoring rules: {stats['monitoring_rules']}")
        
        # Test 13: Report Export
        print("\n1️⃣3️⃣ Testing Report Export...")
        
        report_file = monitor.export_monitoring_report()
        if report_file and os.path.exists(report_file):
            print(f"   ✓ Report exported: {report_file}")
            
            # Check file size
            file_size = os.path.getsize(report_file)
            print(f"   ✓ Report size: {file_size} bytes")
            
            # Clean up
            os.remove(report_file)
            print(f"   ✓ Report file cleaned up")
        else:
            print(f"   ⚠️ Report export failed")
        
        # Test 14: Recent Alerts and History
        print("\n1️⃣4️⃣ Testing Recent Alerts and History...")
        
        recent_alerts = monitor.get_recent_alerts(hours=24)
        print(f"   ✓ Recent alerts (24h): {len(recent_alerts)}")
        
        performance_history = monitor.get_performance_history(hours=24)
        print(f"   ✓ Performance history (24h): {len(performance_history)}")
        
        # Show alert details
        for i, alert in enumerate(recent_alerts[:3]):  # Show first 3 alerts
            print(f"     Alert {i+1}: {alert.severity.value} - {alert.drift_type.value}")
        
        # Test 15: Performance Callbacks
        print("\n1️⃣5️⃣ Testing Performance Callbacks...")
        
        performance_snapshots = []
        
        def performance_callback(snapshot):
            performance_snapshots.append(snapshot)
        
        monitor.add_performance_callback(performance_callback)
        
        # Create snapshot to trigger callback
        snapshot = monitor.create_performance_snapshot()
        print(f"   ✓ Performance callbacks: {len(performance_snapshots)} triggered")
        
        # Final statistics
        final_stats = monitor.get_statistics()
        print(f"\n📊 Final Statistics:")
        print(f"   Total samples recorded: {final_stats['total_samples']}")
        print(f"   Total alerts generated: {final_stats['total_alerts']}")
        print(f"   Monitoring rules active: {final_stats['monitoring_rules']}")
        
        print("\n✅ All Model Monitoring System tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_drift_detection():
    """تست تشخیص انحراف"""
    print("\n🔍 Testing Drift Detection...")
    
    try:
        from core.model_monitoring import StatisticalDriftDetector
        
        detector = StatisticalDriftDetector(sensitivity=0.05)
        
        # Test 1: No drift (same distribution)
        print("\n1. Testing no drift scenario...")
        np.random.seed(42)
        reference = np.random.normal(0, 1, 500)
        current = np.random.normal(0, 1, 500)
        
        drift_detected, score, desc = detector.detect_data_drift(reference, current)
        print(f"   ✓ No drift test: detected={drift_detected}, score={score:.4f}")
        
        # Test 2: Mean shift (data drift)
        print("\n2. Testing mean shift...")
        current_shifted = np.random.normal(1.5, 1, 500)  # Mean shifted by 1.5
        
        drift_detected, score, desc = detector.detect_data_drift(reference, current_shifted)
        print(f"   ✓ Mean shift test: detected={drift_detected}, score={score:.4f}")
        
        # Test 3: Variance change
        print("\n3. Testing variance change...")
        current_variance = np.random.normal(0, 3, 500)  # Increased variance
        
        drift_detected, score, desc = detector.detect_data_drift(reference, current_variance)
        print(f"   ✓ Variance change test: detected={drift_detected}, score={score:.4f}")
        
        # Test 4: Concept drift
        print("\n4. Testing concept drift...")
        # Simulate predictions and actuals with changing relationship
        predictions = np.random.uniform(0, 1, 400)
        
        # First half: good correlation
        actuals_1 = [p + np.random.normal(0, 0.1) for p in predictions[:200]]
        # Second half: poor correlation (concept drift)
        actuals_2 = [np.random.uniform(0, 1) for _ in predictions[200:]]
        
        all_actuals = actuals_1 + actuals_2
        
        concept_drift, score, desc = detector.detect_concept_drift(predictions.tolist(), all_actuals)
        print(f"   ✓ Concept drift test: detected={concept_drift}, score={score:.4f}")
        
        print("✅ Drift detection test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Drift detection test error: {e}")
        return False

def test_performance_tracking():
    """تست ردیابی عملکرد"""
    print("\n📈 Testing Performance Tracking...")
    
    try:
        from core.model_monitoring import PerformanceTracker
        
        tracker = PerformanceTracker()
        
        # Test 1: Classification metrics
        print("\n1. Testing classification metrics...")
        
        for i in range(100):
            # Binary classification
            true_label = random.choice([0, 1])
            prediction = true_label if random.random() > 0.1 else 1 - true_label  # 90% accuracy
            latency = random.gauss(50, 10)
            
            tracker.record_prediction("classifier", "1.0", prediction, true_label, latency)
        
        class_metrics = tracker.calculate_metrics("classifier", "1.0")
        print(f"   ✓ Classification metrics: {list(class_metrics.keys())}")
        
        if "accuracy" in class_metrics:
            print(f"     - Accuracy: {class_metrics['accuracy']:.3f}")
        if "avg_latency" in class_metrics:
            print(f"     - Avg latency: {class_metrics['avg_latency']:.3f}ms")
        if "throughput" in class_metrics:
            print(f"     - Throughput: {class_metrics['throughput']:.3f} req/s")
        
        # Test 2: Regression metrics
        print("\n2. Testing regression metrics...")
        
        for i in range(100):
            # Regression
            true_value = random.gauss(10, 2)
            prediction = true_value + random.gauss(0, 0.5)  # Small error
            latency = random.gauss(30, 5)
            
            tracker.record_prediction("regressor", "1.0", prediction, true_value, latency)
        
        reg_metrics = tracker.calculate_metrics("regressor", "1.0")
        print(f"   ✓ Regression metrics: {list(reg_metrics.keys())}")
        
        if "mse" in reg_metrics:
            print(f"     - MSE: {reg_metrics['mse']:.3f}")
        if "mae" in reg_metrics:
            print(f"     - MAE: {reg_metrics['mae']:.3f}")
        if "r2_score" in reg_metrics:
            print(f"     - R² Score: {reg_metrics['r2_score']:.3f}")
        
        # Test 3: Performance trends
        print("\n3. Testing performance trends...")
        
        trend = tracker.get_performance_trend("classifier", "1.0", "accuracy", time_points=5)
        print(f"   ✓ Performance trend: {len(trend)} data points")
        
        for i, point in enumerate(trend[:3]):  # Show first 3 points
            print(f"     Point {i+1}: value={point['value']:.3f}, samples={point['sample_count']}")
        
        print("✅ Performance tracking test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Performance tracking test error: {e}")
        return False

def test_monitoring_performance():
    """تست عملکرد نظارت"""
    print("\n⚡ Testing Monitoring Performance...")
    
    try:
        from core.model_monitoring import ModelMonitor
        
        # Create monitor
        monitor = ModelMonitor("perf_test_model", "1.0")
        
        # Large-scale performance test
        num_predictions = 1000
        start_time = time.time()
        
        print(f"   Recording {num_predictions} predictions...")
        
        for i in range(num_predictions):
            features = {
                "feature1": random.gauss(0, 1),
                "feature2": random.gauss(0, 1),
                "feature3": random.gauss(0, 1)
            }
            
            prediction = random.choice([0, 1])
            actual = prediction if random.random() > 0.1 else 1 - prediction
            latency = random.gauss(50, 10)
            
            monitor.record_prediction(features, prediction, actual, latency)
        
        recording_time = time.time() - start_time
        print(f"   ✓ Recorded {num_predictions} predictions in {recording_time:.2f}s")
        print(f"   ✓ Rate: {num_predictions/recording_time:.0f} predictions/second")
        
        # Test snapshot creation performance
        start_time = time.time()
        snapshot = monitor.create_performance_snapshot()
        snapshot_time = time.time() - start_time
        
        print(f"   ✓ Created performance snapshot in {snapshot_time:.3f}s")
        print(f"   ✓ Snapshot contains {len(snapshot.metrics)} metrics")
        
        # Test statistics calculation performance
        start_time = time.time()
        stats = monitor.get_statistics()
        stats_time = time.time() - start_time
        
        print(f"   ✓ Calculated statistics in {stats_time:.3f}s")
        print(f"   ✓ Total samples tracked: {stats['total_samples']}")
        
        print("✅ Monitoring performance test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Monitoring performance test error: {e}")
        return False

def main():
    """تست اصلی"""
    print("👁️ Model Monitoring System Test Suite")
    print("=" * 60)
    
    # Run tests
    test_results = []
    
    # Test 1: Basic monitoring functionality
    test_results.append(test_model_monitoring_system())
    
    # Test 2: Drift detection
    test_results.append(test_drift_detection())
    
    # Test 3: Performance tracking
    test_results.append(test_performance_tracking())
    
    # Test 4: Performance
    test_results.append(test_monitoring_performance())
    
    # Results summary
    print("\n📊 Test Results Summary:")
    print("=" * 30)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ Tests passed: {passed}/{total}")
    print(f"📈 Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! Model Monitoring System is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 