#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Example of using Continual Learning in reinforcement learning models
to prevent catastrophic forgetting when learning new tasks.

This example demonstrates:
1. Training a model on an initial task
2. Adapting the model to a new task while preserving performance on the first task
3. Comparing performance with and without continual learning techniques
"""

import os
import sys
import numpy as np
import torch
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime
import logging

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.rl_models import RLModelFactory
from models.continual_learning import ContinualLearning
from env.trading_env import TradingEnv
from utils.data_utils import prepare_data_for_env

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_task_environments(symbols, timeframes, window_sizes):
    """
    Create different trading environments for different tasks
    
    Parameters:
    -----------
    symbols : List[str]
        List of trading symbols
    timeframes : List[str]
        List of timeframes
    window_sizes : List[int]
        List of observation window sizes
        
    Returns:
    --------
    List[TradingEnv]
        List of trading environments for different tasks
    """
    environments = []
    
    for symbol in symbols:
        for timeframe in timeframes:
            for window_size in window_sizes:
                # Prepare data for this task
                data = prepare_data_for_env(
                    symbol=symbol,
                    timeframe=timeframe,
                    train_ratio=0.7,
                    test_ratio=0.3
                )
                
                # Create environment
                env = TradingEnv(
                    data=data['train'],
                    window_size=window_size,
                    commission=0.001,
                    symbol=symbol,
                    timeframe=timeframe
                )
                
                # Create test environment
                test_env = TradingEnv(
                    data=data['test'],
                    window_size=window_size,
                    commission=0.001,
                    symbol=symbol,
                    timeframe=timeframe
                )
                
                environments.append({
                    'train_env': env,
                    'test_env': test_env,
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'window_size': window_size,
                    'task_name': f"{symbol}_{timeframe}_w{window_size}"
                })
    
    return environments

def create_data_loader(env, batch_size=32):
    """
    Create a data loader for computing Fisher information
    
    Parameters:
    -----------
    env : TradingEnv
        Trading environment
    batch_size : int
        Batch size
        
    Returns:
    --------
    DataLoader
        Data loader for the environment
    """
    # Reset environment to get initial observation
    observation = env.reset()
    
    # Collect a set of observations
    observations = []
    for _ in range(100):  # Collect 100 observations
        action = env.action_space.sample()  # Random action
        next_observation, reward, done, info = env.step(action)
        observations.append(observation)
        observation = next_observation
        if done:
            observation = env.reset()
    
    # Convert to tensor
    observations = torch.tensor(np.array(observations), dtype=torch.float32)
    
    # Create batches
    num_batches = len(observations) // batch_size
    batches = []
    for i in range(num_batches):
        start_idx = i * batch_size
        end_idx = start_idx + batch_size
        batches.append([observations[start_idx:end_idx]])
    
    return batches

def train_model(model, env, num_timesteps=10000):
    """
    Train a reinforcement learning model
    
    Parameters:
    -----------
    model : RL model
        Model to train
    env : TradingEnv
        Training environment
    num_timesteps : int
        Number of timesteps to train
        
    Returns:
    --------
    dict
        Training metrics
    """
    logger.info(f"Training model for {num_timesteps} timesteps")
    model.learn(total_timesteps=num_timesteps)
    return model

def evaluate_model(model, env, num_episodes=10):
    """
    Evaluate a reinforcement learning model
    
    Parameters:
    -----------
    model : RL model
        Model to evaluate
    env : TradingEnv
        Evaluation environment
    num_episodes : int
        Number of episodes to evaluate
        
    Returns:
    --------
    dict
        Evaluation metrics
    """
    logger.info(f"Evaluating model for {num_episodes} episodes")
    
    rewards = []
    returns = []
    
    for episode in range(num_episodes):
        observation = env.reset()
        episode_reward = 0
        done = False
        
        while not done:
            action, _ = model.predict(observation, deterministic=True)
            observation, reward, done, info = env.step(action)
            episode_reward += reward
        
        rewards.append(episode_reward)
        returns.append(info.get('portfolio_return', 0))
    
    return {
        'mean_reward': np.mean(rewards),
        'std_reward': np.std(rewards),
        'mean_return': np.mean(returns),
        'std_return': np.std(returns),
    }

def main():
    """Main function to demonstrate continual learning"""
    
    # Create task environments
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    timeframes = ['H1', 'H4']
    window_sizes = [20, 50]
    
    logger.info("Creating task environments")
    task_environments = create_task_environments(symbols, timeframes, window_sizes)
    
    # Select a subset of tasks for demonstration
    selected_tasks = task_environments[:4]  # First 4 tasks
    
    # Create model factory
    model_factory = RLModelFactory()
    
    # Create models
    logger.info("Creating models")
    baseline_model = model_factory.create('ppo', selected_tasks[0]['train_env'])
    continual_model = model_factory.create('ppo', selected_tasks[0]['train_env'])
    
    # Create continual learning wrapper
    cl = ContinualLearning(
        model=continual_model,
        ewc_lambda=0.5,
        replay_buffer_capacity=10000,
        distillation_temp=2.0,
        distillation_weight=0.5,
        use_ewc=True,
        use_replay=True,
        use_distillation=True
    )
    
    # Results storage
    results = {
        'baseline': {task['task_name']: [] for task in selected_tasks},
        'continual': {task['task_name']: [] for task in selected_tasks}
    }
    
    # Train on each task sequentially
    for i, task in enumerate(selected_tasks):
        logger.info(f"Training on task {i+1}/{len(selected_tasks)}: {task['task_name']}")
        
        # Prepare continual learning for new task
        if i > 0:
            cl.prepare_for_new_task(task_name=task['task_name'])
            
            # Create data loader for Fisher information
            data_loader = create_data_loader(task['train_env'])
            cl.set_data_loader(data_loader)
        
        # Train baseline model (from scratch for each task)
        if i > 0:
            baseline_model = model_factory.create('ppo', task['train_env'])
        
        baseline_model = train_model(baseline_model, task['train_env'])
        
        # Train continual model (continue training on new task)
        continual_model = train_model(continual_model, task['train_env'])
        
        # Evaluate both models on all previous tasks
        for j in range(i + 1):
            prev_task = selected_tasks[j]
            
            # Evaluate baseline model
            baseline_metrics = evaluate_model(baseline_model, prev_task['test_env'])
            results['baseline'][prev_task['task_name']].append(baseline_metrics)
            
            # Evaluate continual model
            continual_metrics = evaluate_model(continual_model, prev_task['test_env'])
            results['continual'][prev_task['task_name']].append(continual_metrics)
            
            # Evaluate forgetting for continual model
            if j < i:  # Only for previous tasks
                forgetting = cl.evaluate_forgetting(j, continual_metrics['mean_return'])
                logger.info(f"Task {j} forgetting: {forgetting:.4f}")
    
    # Plot results
    plot_results(results, selected_tasks)
    
    # Plot forgetting curves
    cl.plot_forgetting()
    plt.savefig('continual_learning_forgetting.png')
    
    # Plot performance curves
    cl.plot_performance()
    plt.savefig('continual_learning_performance.png')
    
    # Save models
    os.makedirs('saved_models', exist_ok=True)
    baseline_model.save('saved_models/baseline_final.zip')
    cl.save('saved_models/continual_learning')
    
    logger.info("Experiment completed successfully")

def plot_results(results, tasks):
    """
    Plot comparison of baseline and continual learning models
    
    Parameters:
    -----------
    results : dict
        Results dictionary
    tasks : List[dict]
        List of task information
    """
    task_names = [task['task_name'] for task in tasks]
    num_tasks = len(tasks)
    
    # Create figure
    fig, axes = plt.subplots(num_tasks, 1, figsize=(12, 4 * num_tasks))
    if num_tasks == 1:
        axes = [axes]
    
    # Plot performance on each task
    for i, task_name in enumerate(task_names):
        ax = axes[i]
        
        # Extract performance data
        baseline_perf = [res['mean_return'] for res in results['baseline'][task_name]]
        continual_perf = [res['mean_return'] for res in results['continual'][task_name]]
        
        # Pad with None for missing evaluations
        baseline_perf = baseline_perf + [None] * (num_tasks - len(baseline_perf))
        continual_perf = continual_perf + [None] * (num_tasks - len(continual_perf))
        
        # Plot
        x = range(1, num_tasks + 1)
        ax.plot(x, baseline_perf, 'o-', label='Baseline')
        ax.plot(x, continual_perf, 's-', label='Continual Learning')
        
        ax.set_title(f"Performance on {task_name}")
        ax.set_xlabel("Tasks Learned")
        ax.set_ylabel("Return")
        ax.set_xticks(x)
        ax.set_xticklabels([f"Task {j+1}" for j in range(num_tasks)])
        ax.legend()
        ax.grid(True)
    
    plt.tight_layout()
    plt.savefig('continual_learning_comparison.png')

if __name__ == "__main__":
    main() 