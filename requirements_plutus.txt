# Requirements for Financial Model API Integration
# متطلبات برای استفاده از API های مدل‌های مالی

# Core HTTP and API libraries
requests>=2.28.0
aiohttp>=3.8.0
httpx>=0.24.0

# Hugging Face integration
transformers>=4.20.0
huggingface-hub>=0.15.0

# Financial data APIs
yfinance>=0.2.0
alpha-vantage>=2.3.0
finnhub-python>=2.4.0

# Time series forecasting
chronos-forecasting>=0.1.0  # If available
prophet>=1.1.0

# Data processing
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# Machine learning
scikit-learn>=1.1.0
torch>=1.12.0  # For some models

# Async processing
asyncio-throttle>=1.0.0

# Configuration and logging
pydantic>=1.10.0
python-dotenv>=0.20.0

# Visualization (optional)
matplotlib>=3.5.0
plotly>=5.10.0

# Testing
pytest>=7.0.0
pytest-asyncio>=0.20.0 