# خلاصه قابلیت‌های پیاده‌سازی شده در ماژول Explainable AI

## قابلیت‌های پایه (پیاده‌سازی شده)

1. **تحلیل اهمیت ویژگی‌ها (Feature Importance Analysis)**
   - متد: `feature_importance`
   - قابلیت: محاسبه اهمیت نسبی هر ویژگی در تصمیمات مدل با استفاده از روش‌های مختلف
   - روش‌های پشتیبانی شده: permutation importance, SHAP, gradient-based

2. **توضیح تصمیمات (Decision Explanation)**
   - متد: `decision_explanation`
   - قابلیت: ارائه توضیح قابل فهم برای هر تصمیم مدل بر اساس ویژگی‌های مهم

3. **نمایش تصویری اهمیت ویژگی‌ها (Feature Importance Visualization)**
   - متد: `visualize_feature_importance`
   - قابلیت: نمایش نمودار اهمیت ویژگی‌ها به صورت تصویری

4. **نمایش تصویری فرآیند تصمیم‌گیری (Decision Process Visualization)**
   - متد: `visualize_decision_process`
   - قابلیت: نمایش تصویری فرآیند تصمیم‌گیری مدل برای یک مشاهده خاص

5. **مقایسه تصمیمات (Decision Comparison)**
   - متد: `compare_decisions`
   - قابلیت: مقایسه تصمیمات مدل برای چندین مشاهده مختلف

6. **ایجاد گزارش توضیحات (Explanation Report Generation)**
   - متد: `generate_explanation_report`
   - قابلیت: ایجاد گزارش کامل توضیحات برای مجموعه‌ای از مشاهدات

## قابلیت‌های پیشرفته (پیاده‌سازی شده)

7. **تحلیل تصمیمات در شرایط بحرانی بازار (Market Regime Analysis)**
   - متد: `analyze_market_regime`
   - قابلیت: تحلیل تصمیمات مدل در شرایط مختلف بازار (روند صعودی، نزولی، نوسانی) و نمایش اینکه چگونه اهمیت ویژگی‌ها در هر رژیم بازار تغییر می‌کند
   - ویژگی‌های کلیدی:
     - شناسایی خودکار رژیم‌های بازار با استفاده از خوشه‌بندی K-means
     - نام‌گذاری هوشمند رژیم‌ها بر اساس ویژگی‌های آنها
     - تحلیل اهمیت ویژگی‌ها در هر رژیم بازار
     - نمایش تصویری رژیم‌های بازار و تصمیمات مدل

8. **تحلیل ریسک تصمیمات (Decision Risk Analysis)**
   - متد: `analyze_decision_risk`
   - قابلیت: ارزیابی و توضیح میزان ریسک هر تصمیم معاملاتی با در نظر گرفتن عدم قطعیت در پیش‌بینی‌ها و تأثیر بالقوه آن بر پورتفولیو
   - ویژگی‌های کلیدی:
     - محاسبه معیارهای ریسک مانند Value at Risk (VaR)، Expected Shortfall (ES)، Sharpe Ratio و Maximum Drawdown
     - تحلیل ریسک بر اساس اقدامات مختلف
     - نمایش تصویری توزیع بازده‌ها و معیارهای ریسک

9. **تشخیص نقاط تصمیم‌گیری بحرانی (Critical Decision Points)**
   - متد: `identify_critical_decision_points`
   - قابلیت: شناسایی لحظات کلیدی در تاریخچه معاملات که در آنها تصمیم مدل بیشترین تأثیر را بر عملکرد کلی داشته است
   - ویژگی‌های کلیدی:
     - شناسایی بزرگترین بازده‌های مثبت و منفی
     - شناسایی نقاط تغییر روند در بازده تجمعی
     - تحلیل ویژگی‌های مهم در نقاط بحرانی
     - نمایش تصویری نقاط بحرانی و اقدامات مدل

---

## قابلیت‌های پیشرفته گروه دوم (جدید)

### 1. نقشه حرارتی تصمیمات (Decision Heatmap)
- **متد:** `create_decision_heatmap`
- **کاربرد:** نمایش الگوهای تصمیم‌گیری مدل بر اساس دو ویژگی کلیدی و زمان، برای کشف رفتارهای پنهان یا سوگیری‌های زمانی.
- **ورودی:**
  - observations: آرایه مشاهدات (n, d)
  - actions: آرایه اقدامات مدل (n,)
  - timestamps: آرایه زمان هر مشاهده
  - feature_idx: لیست اندیس دو ویژگی برای محورهای نقشه حرارتی
- **خروجی:**
  - دیکشنری شامل داده‌های نقشه حرارتی، همبستگی ویژگی‌ها و الگوهای زمانی

**مثال:**
```python
heatmap = xai.create_decision_heatmap(obs, actions, timestamps, feature_idx=[0,1], save_path='heatmap.png')
print(heatmap['feature_correlations'])
```

### 2. تحلیل کانتر-فکتوال (Counterfactual Analysis)
- **متد:** `counterfactual_analysis`
- **کاربرد:** بررسی اینکه با چه تغییرات حداقلی در ویژگی‌ها، مدل تصمیم متفاوتی می‌گیرد (مثلاً اقدام به خرید به جای فروش).
- **ورودی:**
  - observation: مشاهده اولیه
  - target_action: اقدام هدف
  - feature_ranges: محدوده تغییرات ویژگی‌ها (دیکشنری)
  - n_samples: تعداد نمونه تصادفی
- **خروجی:**
  - دیکشنری شامل لیست کانترفکتوال‌ها، اقدام اولیه، تغییرات ویژگی و کمترین تغییر

**مثال:**
```python
cf = xai.counterfactual_analysis(obs[0], target_action=1, feature_ranges={0:(-2,2),1:(-2,2)}, n_samples=20)
print(cf['minimal_counterfactual'])
```

### 3. تحلیل حساسیت چندبعدی (Multi-dimensional Sensitivity Analysis)
- **متد:** `sensitivity_analysis`
- **کاربرد:** سنجش میزان حساسیت خروجی مدل نسبت به تغییرات هر ویژگی و اثرات متقابل ویژگی‌ها.
- **ورودی:**
  - observation: مشاهده اولیه
  - feature_ranges: محدوده تغییرات ویژگی‌ها (دیکشنری)
  - n_samples: تعداد نمونه برای هر ویژگی
- **خروجی:**
  - دیکشنری شامل امتیاز حساسیت، اثرات متقابل و مرزهای ویژگی

**مثال:**
```python
sens = xai.sensitivity_analysis(obs[0], feature_ranges={0:(-2,2),1:(-2,2),2:(-2,2)}, n_samples=10)
print(sens['sensitivity_scores'])
```

### 4. تشخیص و توضیح سوگیری مدل (Bias Detection and Explanation)
- **متد:** `detect_bias`
- **کاربرد:** شناسایی و مقایسه رفتار مدل نسبت به گروه‌های مختلف داده (مثلاً بر اساس جنسیت، سن یا هر ویژگی دیگر) و کشف سوگیری احتمالی.
- **ورودی:**
  - observations: آرایه مشاهدات
  - actions: آرایه اقدامات
  - group_indices: دیکشنری نام گروه به لیست اندیس نمونه‌ها
- **خروجی:**
  - دیکشنری شامل متریک‌های سوگیری، مقایسه گروه‌ها و سوگیری ویژگی

**مثال:**
```python
groups = {'group_1': np.where(obs[:,0]>0)[0], 'group_2': np.where(obs[:,0]<=0)[0]}
bias = xai.detect_bias(obs, actions, group_indices=groups)
print(bias['group_comparisons'])
```

---

برای جزئیات بیشتر و اولویت‌بندی کامل، به فایل `docs/explainable_ai_priority.md` مراجعه کنید. 