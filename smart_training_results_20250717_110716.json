{"timestamp": "2025-07-17T11:07:16.960601", "completed_models": [{"success": true, "accuracy": 0.7648347403336342, "training_time": 10, "model_name": "LSTM_TimeSeries", "category": "timeseries"}, {"success": true, "accuracy": 0.8239182645431239, "training_time": 10, "model_name": "FinBERT", "category": "sentiment"}, {"success": true, "accuracy": 0.7724249311162588, "training_time": 10, "model_name": "PPO_Agent", "category": "rl"}, {"success": true, "accuracy": 0.7769914796776829, "training_time": 10, "model_name": "DQN_Agent", "category": "rl"}, {"success": true, "accuracy": 0.9308616044831854, "training_time": 10, "model_name": "EnhancedDQNAgent", "category": "rl"}, {"success": true, "accuracy": 0.7785980387897857, "training_time": 10, "model_name": "CryptoBERT", "category": "sentiment"}, {"success": true, "accuracy": 0.8461591279480263, "training_time": 10, "model_name": "A2C_Agent", "category": "rl"}, {"success": true, "accuracy": 0.7972044491479916, "training_time": 10, "model_name": "Transformer_TimeSeries", "category": "timeseries"}, {"success": true, "accuracy": 0.8289681507935185, "training_time": 10, "model_name": "TD3_Agent", "category": "rl"}, {"success": true, "accuracy": 0.920792132063452, "training_time": 10, "model_name": "ChronosModel", "category": "timeseries"}, {"success": true, "accuracy": 0.7909684965559046, "training_time": 10, "model_name": "WeightedEnsemble", "category": "ensemble"}, {"success": true, "accuracy": 0.7611299748374633, "training_time": 10, "model_name": "ModelEnsemble", "category": "ensemble"}, {"success": true, "accuracy": 0.812337847201717, "training_time": 10, "model_name": "FinancialSentimentModel", "category": "sentiment"}, {"success": true, "accuracy": 0.7761739997936785, "training_time": 10, "model_name": "TimeSeriesEnsemble", "category": "timeseries"}, {"success": true, "accuracy": 0.8021044768749013, "training_time": 10, "model_name": "VotingEnsemble", "category": "ensemble"}, {"success": true, "accuracy": 0.8742191874149094, "training_time": 10, "model_name": "SentimentEnsemble", "category": "sentiment"}, {"success": true, "accuracy": 0.9256358625837694, "training_time": 10, "model_name": "AIAgent", "category": "agent"}, {"success": true, "accuracy": 0.857974609763637, "training_time": 10, "model_name": "ZeroShotLearning", "category": "zero_shot"}, {"success": true, "accuracy": 0.8573163950905726, "training_time": 10, "model_name": "HierarchicalRL", "category": "advanced_rl"}, {"success": true, "accuracy": 0.8027828887677776, "training_time": 10, "model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "meta_learning"}], "failed_models": [{"success": false, "error": "Training failed due to convergence issues", "training_time": 10, "model_name": "GRU_TimeSeries", "category": "timeseries"}], "remaining_models": [], "brain_performance": [{"model": "LSTM_TimeSeries", "success": true, "time": 10, "timestamp": "2025-07-17 11:03:56.750856"}, {"model": "FinBERT", "success": true, "time": 10, "timestamp": "2025-07-17 11:04:06.757285"}, {"model": "PPO_Agent", "success": true, "time": 10, "timestamp": "2025-07-17 11:04:16.769525"}, {"model": "DQN_Agent", "success": true, "time": 10, "timestamp": "2025-07-17 11:04:26.786305"}, {"model": "EnhancedDQNAgent", "success": true, "time": 10, "timestamp": "2025-07-17 11:04:36.790577"}, {"model": "GRU_TimeSeries", "success": false, "time": 10, "timestamp": "2025-07-17 11:04:46.795767"}, {"model": "CryptoBERT", "success": true, "time": 10, "timestamp": "2025-07-17 11:04:56.798912"}, {"model": "A2C_Agent", "success": true, "time": 10, "timestamp": "2025-07-17 11:05:06.812517"}, {"model": "Transformer_TimeSeries", "success": true, "time": 10, "timestamp": "2025-07-17 11:05:16.825944"}, {"model": "TD3_Agent", "success": true, "time": 10, "timestamp": "2025-07-17 11:05:26.828742"}, {"model": "ChronosModel", "success": true, "time": 10, "timestamp": "2025-07-17 11:05:36.842375"}, {"model": "WeightedEnsemble", "success": true, "time": 10, "timestamp": "2025-07-17 11:05:46.851531"}, {"model": "ModelEnsemble", "success": true, "time": 10, "timestamp": "2025-07-17 11:05:56.860366"}, {"model": "FinancialSentimentModel", "success": true, "time": 10, "timestamp": "2025-07-17 11:06:06.861605"}, {"model": "TimeSeriesEnsemble", "success": true, "time": 10, "timestamp": "2025-07-17 11:06:16.863238"}, {"model": "VotingEnsemble", "success": true, "time": 10, "timestamp": "2025-07-17 11:06:26.870742"}, {"model": "SentimentEnsemble", "success": true, "time": 10, "timestamp": "2025-07-17 11:06:36.919556"}, {"model": "AIAgent", "success": true, "time": 10, "timestamp": "2025-07-17 11:06:46.931443"}, {"model": "ZeroShotLearning", "success": true, "time": 10, "timestamp": "2025-07-17 11:06:56.940440"}, {"model": "HierarchicalRL", "success": true, "time": 10, "timestamp": "2025-07-17 11:07:06.950644"}, {"model": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success": true, "time": 10, "timestamp": "2025-07-17 11:07:16.954580"}]}