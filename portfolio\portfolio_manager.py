import sys
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import numpy as np

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import sentiment analyzer
try:
    from utils.sentiment_analyzer import AdvancedSentimentAnalyzer
    SENTIMENT_AVAILABLE = True
except ImportError:
    SENTIMENT_AVAILABLE = False

class PortfolioManagerV2:
    """
    Enhanced Portfolio Manager with Sentiment Analysis Integration
    مدیر پورتفولیو بهبود یافته با ادغام تحلیل احساسات
    """
    
    def __init__(self, initial_balance: float = 1000, sentiment_enabled: bool = True):
        self.balance: float = initial_balance
        self.initial_balance: float = initial_balance
        self.positions: Dict[str, Dict[str, Any]] = {}
        self.trade_history: List[Dict[str, Any]] = []
        
        # Sentiment Analysis Integration - NEW
        self.sentiment_enabled = sentiment_enabled and SENTIMENT_AVAILABLE
        self.sentiment_analyzer = None
        self.sentiment_history = []
        self.sentiment_weights = {
            'position_sizing': 0.15,  # تأثیر بر اندازه پوزیشن
            'risk_adjustment': 0.20,  # تأثیر بر تنظیم ریسک
            'timing_adjustment': 0.10  # تأثیر بر زمان‌بندی
        }
        
        # Initialize sentiment analyzer
        if self.sentiment_enabled:
            try:
                self.sentiment_analyzer = AdvancedSentimentAnalyzer(
                    languages=['en'], 
                    enable_cache=True
                )
                print("✅ Portfolio Manager: Sentiment analyzer initialized")
            except Exception as e:
                print(f"⚠️ Portfolio Manager: Failed to initialize sentiment analyzer: {e}")
                self.sentiment_enabled = False
        
        # Portfolio metrics
        self.metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'max_drawdown': 0.0,
            'sentiment_influenced_trades': 0,
            'sentiment_accuracy': 0.0
        }
    
    def get_market_sentiment(self, symbol: str, news_texts: List[str] = None) -> Dict[str, Any]:
        """دریافت احساسات بازار برای یک نماد"""
        if not self.sentiment_enabled or not self.sentiment_analyzer:
            return {
                'sentiment_score': 0.0,
                'confidence': 0.0,
                'market_sentiment': 0.0,
                'impact_score': 0.0
            }
        
        try:
            # Use provided news or generate sample news
            if not news_texts:
                news_texts = [
                    f"{symbol} demonstrates strong fundamentals in current market",
                    f"Technical analysis suggests {symbol} bullish momentum",
                    f"Market sentiment for {symbol} remains optimistic"
                ]
            
            # Analyze sentiment
            sentiment_results = []
            for text in news_texts:
                result = self.sentiment_analyzer.analyze(text, source='portfolio_analysis')
                if result:
                    sentiment_results.append({
                        'score': result.score if result.label == 'positive' else -result.score,
                        'confidence': result.confidence,
                        'impact': getattr(result, 'market_impact', 0.0)
                    })
            
            if sentiment_results:
                # Calculate weighted averages
                total_confidence = sum(r['confidence'] for r in sentiment_results)
                if total_confidence > 0:
                    weighted_sentiment = sum(
                        r['score'] * r['confidence'] for r in sentiment_results
                    ) / total_confidence
                    avg_confidence = total_confidence / len(sentiment_results)
                    avg_impact = sum(r['impact'] for r in sentiment_results) / len(sentiment_results)
                else:
                    weighted_sentiment = 0.0
                    avg_confidence = 0.0
                    avg_impact = 0.0
                
                # Get overall market sentiment
                market_sentiment = self.sentiment_analyzer.get_market_sentiment()
                if hasattr(market_sentiment, 'overall_sentiment'):
                    market_overall = market_sentiment.overall_sentiment
                else:
                    market_overall = 0.0
                
                sentiment_data = {
                    'sentiment_score': weighted_sentiment,
                    'confidence': avg_confidence,
                    'market_sentiment': market_overall,
                    'impact_score': avg_impact
                }
                
                # Store in history
                self.sentiment_history.append({
                    'symbol': symbol,
                    'timestamp': datetime.now(),
                    **sentiment_data
                })
                
                return sentiment_data
            
            return {
                'sentiment_score': 0.0,
                'confidence': 0.0,
                'market_sentiment': 0.0,
                'impact_score': 0.0
            }
            
        except Exception as e:
            print(f"Error getting market sentiment for {symbol}: {e}")
            return {
                'sentiment_score': 0.0,
                'confidence': 0.0,
                'market_sentiment': 0.0,
                'impact_score': 0.0
            }
    
    def calculate_sentiment_adjusted_position_size(self, symbol: str, base_quantity: float, 
                                                 sentiment_data: Dict[str, Any] = None) -> float:
        """محاسبه اندازه پوزیشن با تعدیل احساسات"""
        if not self.sentiment_enabled:
            return base_quantity
        
        if sentiment_data is None:
            sentiment_data = self.get_market_sentiment(symbol)
        
        sentiment_score = sentiment_data.get('sentiment_score', 0.0)
        confidence = sentiment_data.get('confidence', 0.0)
        
        # Calculate sentiment adjustment
        sentiment_adjustment = sentiment_score * confidence * self.sentiment_weights['position_sizing']
        
        # Apply adjustment (positive sentiment increases position size, negative decreases)
        adjusted_quantity = base_quantity * (1 + sentiment_adjustment)
        
        # Ensure reasonable bounds
        adjusted_quantity = max(0.01, min(adjusted_quantity, base_quantity * 2.0))
        
        return adjusted_quantity
    
    def calculate_sentiment_risk_adjustment(self, symbol: str, base_risk: float,
                                          sentiment_data: Dict[str, Any] = None) -> float:
        """محاسبه تنظیم ریسک با توجه به احساسات"""
        if not self.sentiment_enabled:
            return base_risk
        
        if sentiment_data is None:
            sentiment_data = self.get_market_sentiment(symbol)
        
        sentiment_score = sentiment_data.get('sentiment_score', 0.0)
        confidence = sentiment_data.get('confidence', 0.0)
        
        # Calculate risk adjustment
        risk_adjustment = abs(sentiment_score) * confidence * self.sentiment_weights['risk_adjustment']
        
        # Negative sentiment increases risk, positive sentiment may decrease it
        if sentiment_score < 0:
            adjusted_risk = base_risk * (1 + risk_adjustment)
        else:
            adjusted_risk = base_risk * (1 - risk_adjustment * 0.5)
        
        # Ensure reasonable bounds
        adjusted_risk = max(0.01, min(adjusted_risk, base_risk * 2.0))
        
        return adjusted_risk

    def open_position(self, symbol: str, quantity: float, entry_price: float, 
                     position_type: str, news_texts: List[str] = None) -> Dict[str, Any]:
        """
        باز کردن پوزیشن جدید با تحلیل احساسات
        
        Args:
            symbol: نماد معاملاتی
            quantity: مقدار پوزیشن
            entry_price: قیمت ورود
            position_type: نوع پوزیشن ('long' یا 'short')
            news_texts: متون خبری برای تحلیل احساسات
        
        Returns:
            dict: نتیجه باز کردن پوزیشن
        """
        # اعتبارسنجی داده‌ها
        if quantity <= 0:
            raise ValueError("Quantity must be positive.")
        if entry_price <= 0:
            raise ValueError("Entry price must be positive.")
        if position_type not in ("long", "short"):
            raise ValueError("Position type must be 'long' or 'short'.")
        
        # Get sentiment analysis
        sentiment_data = self.get_market_sentiment(symbol, news_texts)
        
        # Adjust position size based on sentiment
        adjusted_quantity = self.calculate_sentiment_adjusted_position_size(
            symbol, quantity, sentiment_data
        )
        
        # Calculate sentiment-based risk adjustment
        base_risk = abs(entry_price * adjusted_quantity * 0.02)  # 2% risk
        adjusted_risk = self.calculate_sentiment_risk_adjustment(
            symbol, base_risk, sentiment_data
        )
        
        # Create position
        position_data = {
            "quantity": float(adjusted_quantity),
            "entry_price": float(entry_price),
            "position_type": 1.0 if position_type == "long" else -1.0,
            "original_quantity": float(quantity),
            "sentiment_score": sentiment_data.get('sentiment_score', 0.0),
            "sentiment_confidence": sentiment_data.get('confidence', 0.0),
            "sentiment_adjusted": self.sentiment_enabled,
            "risk_adjustment": adjusted_risk,
            "timestamp": datetime.now()
        }
        
        self.positions[symbol] = position_data
        self.metrics['total_trades'] += 1
        
        # Track sentiment influenced trades
        if self.sentiment_enabled and abs(sentiment_data.get('sentiment_score', 0.0)) > 0.3:
            self.metrics['sentiment_influenced_trades'] += 1
        
        return {
            'success': True,
            'original_quantity': quantity,
            'adjusted_quantity': adjusted_quantity,
            'sentiment_data': sentiment_data,
            'risk_adjustment': adjusted_risk
        }

    def close_position(self, symbol: str, exit_price: float, 
                      news_texts: List[str] = None) -> Dict[str, Any]:
        """
        بستن پوزیشن با تحلیل احساسات
        
        Args:
            symbol: نماد معاملاتی
            exit_price: قیمت خروج
            news_texts: متون خبری برای تحلیل احساسات
        
        Returns:
            dict: نتیجه بستن پوزیشن شامل سود/زیان
        """
        if symbol not in self.positions:
            return {'success': False, 'reason': 'Position not found', 'profit': 0.0}

        position = self.positions[symbol]
        quantity = float(position["quantity"])
        entry_price = float(position["entry_price"])
        position_type = position["position_type"]
        
        # Get current sentiment
        current_sentiment = self.get_market_sentiment(symbol, news_texts)
        
        # Calculate profit/loss
        profit = (exit_price - entry_price) * quantity * position_type
        self.balance += profit
        
        # Update metrics
        if profit > 0:
            self.metrics['winning_trades'] += 1
        else:
            self.metrics['losing_trades'] += 1
        
        # Calculate drawdown
        current_drawdown = (self.initial_balance - self.balance) / self.initial_balance
        if current_drawdown > self.metrics['max_drawdown']:
            self.metrics['max_drawdown'] = current_drawdown
        
        # Create trade record
        trade_record = {
            "symbol": symbol,
            "quantity": quantity,
            "entry_price": entry_price,
            "exit_price": exit_price,
            "position_type": "long" if position_type == 1.0 else "short",
            "profit": profit,
            "entry_sentiment": position.get('sentiment_score', 0.0),
            "exit_sentiment": current_sentiment.get('sentiment_score', 0.0),
            "sentiment_confidence": current_sentiment.get('confidence', 0.0),
            "sentiment_adjusted": position.get('sentiment_adjusted', False),
            "timestamp": datetime.now()
        }
        
        self.trade_history.append(trade_record)
        del self.positions[symbol]
        
        return {
            'success': True,
            'profit': profit,
            'entry_sentiment': position.get('sentiment_score', 0.0),
            'exit_sentiment': current_sentiment.get('sentiment_score', 0.0),
            'sentiment_impact': abs(current_sentiment.get('sentiment_score', 0.0) - position.get('sentiment_score', 0.0))
        }
    
    def get_sentiment_portfolio_analysis(self) -> Dict[str, Any]:
        """تحلیل احساسات کل پورتفولیو"""
        if not self.sentiment_enabled:
            return {'sentiment_enabled': False}
        
        # Analyze current positions
        current_positions_sentiment = {}
        for symbol, position in self.positions.items():
            current_positions_sentiment[symbol] = {
                'sentiment_score': position.get('sentiment_score', 0.0),
                'sentiment_confidence': position.get('sentiment_confidence', 0.0),
                'quantity': position['quantity'],
                'position_type': 'long' if position['position_type'] == 1.0 else 'short'
            }
        
        # Calculate portfolio sentiment score
        total_exposure = sum(abs(pos['quantity'] * pos.get('sentiment_score', 0.0)) 
                           for pos in self.positions.values())
        
        portfolio_sentiment = 0.0
        if len(self.positions) > 0:
            portfolio_sentiment = sum(
                pos['quantity'] * pos.get('sentiment_score', 0.0) * pos['position_type']
                for pos in self.positions.values()
            ) / max(len(self.positions), 1)
        
        # Analyze trade history sentiment performance
        sentiment_trades = [trade for trade in self.trade_history 
                          if trade.get('sentiment_adjusted', False)]
        
        sentiment_trade_performance = 0.0
        if sentiment_trades:
            sentiment_trade_performance = sum(trade['profit'] for trade in sentiment_trades) / len(sentiment_trades)
        
        return {
            'sentiment_enabled': True,
            'current_positions_sentiment': current_positions_sentiment,
            'portfolio_sentiment_score': portfolio_sentiment,
            'total_exposure': total_exposure,
            'sentiment_influenced_trades': self.metrics['sentiment_influenced_trades'],
            'sentiment_trade_performance': sentiment_trade_performance,
            'sentiment_history_count': len(self.sentiment_history)
        }

    def get_trade_history(self) -> List[Dict[str, Any]]:
        """دریافت تاریخچه معاملات بسته شده"""
        return self.trade_history

    def get_total_pnl(self) -> float:
        """سود/زیان کل معاملات بسته شده"""
        return sum(trade["profit"] for trade in self.trade_history)

    def get_enhanced_trade_report(self) -> str:
        """گزارش متنی پیشرفته معاملات"""
        lines = ["symbol,quantity,entry_price,exit_price,position_type,profit,entry_sentiment,exit_sentiment,sentiment_adjusted"]
        for t in self.trade_history:
            lines.append(f"{t['symbol']},{t['quantity']},{t['entry_price']},{t['exit_price']},{t['position_type']},{t['profit']},{t.get('entry_sentiment', 0.0)},{t.get('exit_sentiment', 0.0)},{t.get('sentiment_adjusted', False)}")
        return "\n".join(lines)

    def get_balance(self) -> float:
        """دریافت بالانس فعلی"""
        return self.balance

    def get_positions(self) -> Dict[str, Dict[str, Any]]:
        """دریافت پوزیشن‌های باز"""
        return self.positions
    
    def get_portfolio_metrics(self) -> Dict[str, Any]:
        """دریافت معیارهای عملکرد پورتفولیو"""
        total_trades = self.metrics['total_trades']
        win_rate = self.metrics['winning_trades'] / total_trades if total_trades > 0 else 0.0
        
        return {
            'balance': self.balance,
            'initial_balance': self.initial_balance,
            'total_return': (self.balance - self.initial_balance) / self.initial_balance,
            'total_trades': total_trades,
            'winning_trades': self.metrics['winning_trades'],
            'losing_trades': self.metrics['losing_trades'],
            'win_rate': win_rate,
            'max_drawdown': self.metrics['max_drawdown'],
            'sentiment_influenced_trades': self.metrics['sentiment_influenced_trades'],
            'sentiment_enabled': self.sentiment_enabled
        }

# Keep original class for backward compatibility
class PortfolioManager(PortfolioManagerV2):
    """Backward compatibility wrapper"""
    def __init__(self, initial_balance: float = 1000):
        # Disable sentiment by default for backward compatibility
        super().__init__(initial_balance=initial_balance, sentiment_enabled=False)

# Export both classes
__all__ = ['PortfolioManager', 'PortfolioManagerV2']
