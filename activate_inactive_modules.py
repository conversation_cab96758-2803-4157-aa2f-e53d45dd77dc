#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Activate Inactive Modules Script
اسکریپت فعال‌سازی ماژول‌های غیرفعال
"""

import os
import sys
import logging
from pathlib import Path
from datetime import datetime

# تنظیم logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def activate_adaptive_margin_control():
    """فعال‌سازی AdaptiveMarginControl"""
    logger.info("🔧 Activating AdaptiveMarginControl...")
    
    try:
        from utils.adaptive_margin_control import AdaptiveMarginControl
        
        # ایجاد instance
        margin_control = AdaptiveMarginControl()
        
        # تست اولیه
        test_data = {
            'symbol': 'EURUSD',
            'current_price': 1.1000,
            'position_size': 1000,
            'account_balance': 10000,
            'volatility': 0.02
        }
        
        margin_requirement = margin_control.calculate_adaptive_margin(test_data)
        logger.info(f"✅ AdaptiveMarginControl: Active - Margin: {margin_requirement}")
        
        return margin_control
        
    except Exception as e:
        logger.error(f"❌ AdaptiveMarginControl activation failed: {e}")
        return None

def activate_advanced_reward_system():
    """فعال‌سازی AdvancedRewardSystem"""
    logger.info("🔧 Activating AdvancedRewardSystem...")
    
    try:
        from utils.advanced_reward_system import AdvancedRewardSystem
        
        # ایجاد instance
        reward_system = AdvancedRewardSystem()
        
        # تست اولیه
        test_action = {
            'type': 'buy',
            'symbol': 'EURUSD',
            'size': 1000,
            'price': 1.1000
        }
        
        reward = reward_system.calculate_reward(test_action, {'profit': 100})
        logger.info(f"✅ AdvancedRewardSystem: Active - Reward: {reward}")
        
        return reward_system
        
    except Exception as e:
        logger.error(f"❌ AdvancedRewardSystem activation failed: {e}")
        return None

def activate_auto_market_maker():
    """فعال‌سازی AutoMarketMaker"""
    logger.info("🔧 Activating AutoMarketMaker...")
    
    try:
        from utils.auto_market_maker import AutoMarketMaker
        
        # ایجاد instance
        market_maker = AutoMarketMaker()
        
        # تست اولیه
        test_config = {
            'symbol': 'EURUSD',
            'spread': 0.0002,
            'quantity': 1000,
            'max_position': 10000
        }
        
        market_maker.configure(test_config)
        logger.info("✅ AutoMarketMaker: Active and configured")
        
        return market_maker
        
    except Exception as e:
        logger.error(f"❌ AutoMarketMaker activation failed: {e}")
        return None

def activate_hierarchical_rl():
    """فعال‌سازی HierarchicalRL"""
    logger.info("🔧 Activating HierarchicalRL...")
    
    try:
        from utils.hierarchical_rl import HierarchicalRL
        
        # ایجاد instance
        hierarchical_rl = HierarchicalRL()
        
        # تست اولیه
        test_state = {
            'price': 1.1000,
            'volume': 1000,
            'indicators': {'rsi': 50, 'macd': 0.001}
        }
        
        action = hierarchical_rl.get_action(test_state)
        logger.info(f"✅ HierarchicalRL: Active - Action: {action}")
        
        return hierarchical_rl
        
    except Exception as e:
        logger.error(f"❌ HierarchicalRL activation failed: {e}")
        return None

def activate_zero_shot_learning():
    """فعال‌سازی ZeroShotLearning"""
    logger.info("🔧 Activating ZeroShotLearning...")
    
    try:
        from utils.zero_shot_learning import ZeroShotLearning
        
        # ایجاد instance
        zero_shot = ZeroShotLearning()
        
        # تست اولیه
        test_task = {
            'task_type': 'new_symbol_prediction',
            'symbol': 'USDCAD',
            'features': [1.1, 1.2, 1.15, 1.18]
        }
        
        prediction = zero_shot.predict(test_task)
        logger.info(f"✅ ZeroShotLearning: Active - Prediction: {prediction}")
        
        return zero_shot
        
    except Exception as e:
        logger.error(f"❌ ZeroShotLearning activation failed: {e}")
        return None

def integrate_modules_into_system():
    """ادغام ماژول‌ها در سیستم اصلی"""
    logger.info("🔗 Integrating modules into main system...")
    
    try:
        # بارگذاری سیستم اصلی
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # فعال‌سازی ماژول‌ها
        modules = {}
        
        modules['adaptive_margin_control'] = activate_adaptive_margin_control()
        modules['advanced_reward_system'] = activate_advanced_reward_system()
        modules['auto_market_maker'] = activate_auto_market_maker()
        modules['hierarchical_rl'] = activate_hierarchical_rl()
        modules['zero_shot_learning'] = activate_zero_shot_learning()
        
        # شمارش ماژول‌های فعال
        active_modules = {k: v for k, v in modules.items() if v is not None}
        inactive_modules = {k: v for k, v in modules.items() if v is None}
        
        logger.info(f"📊 Integration Summary:")
        logger.info(f"  ✅ Active Modules: {len(active_modules)}")
        logger.info(f"  ❌ Inactive Modules: {len(inactive_modules)}")
        
        for module_name in active_modules:
            logger.info(f"    ✅ {module_name}")
        
        for module_name in inactive_modules:
            logger.info(f"    ❌ {module_name}")
        
        return active_modules
        
    except Exception as e:
        logger.error(f"❌ Integration failed: {e}")
        return {}

def create_integration_config():
    """ایجاد فایل تنظیمات ادغام"""
    config_content = """# Inactive Modules Integration Configuration
# تنظیمات ادغام ماژول‌های غیرفعال

[AdaptiveMarginControl]
enabled = true
margin_multiplier = 1.5
volatility_adjustment = true
dynamic_sizing = true

[AdvancedRewardSystem]
enabled = true
reward_scaling = 1.0
penalty_factor = 0.5
learning_rate = 0.01

[AutoMarketMaker]
enabled = true
default_spread = 0.0002
max_position = 10000
rebalance_frequency = 60

[HierarchicalRL]
enabled = true
high_level_actions = ["trend_follow", "mean_revert", "momentum"]
low_level_actions = ["buy", "sell", "hold"]
hierarchy_depth = 3

[ZeroShotLearning]
enabled = true
similarity_threshold = 0.8
adaptation_rate = 0.1
memory_size = 1000
"""
    
    with open('inactive_modules_config.ini', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    logger.info("✅ Integration configuration file created: inactive_modules_config.ini")

def update_main_system():
    """به‌روزرسانی سیستم اصلی برای شامل کردن ماژول‌ها"""
    logger.info("🔄 Updating main system...")
    
    try:
        # خواندن فایل main_new.py
        with open('main_new.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # اضافه کردن import های جدید
        import_section = '''
# Import inactive modules
try:
    from utils.adaptive_margin_control import AdaptiveMarginControl
    from utils.advanced_reward_system import AdvancedRewardSystem
    from utils.auto_market_maker import AutoMarketMaker
    from utils.hierarchical_rl import HierarchicalRL
    from utils.zero_shot_learning import ZeroShotLearning
    INACTIVE_MODULES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Inactive modules not available: {e}")
    INACTIVE_MODULES_AVAILABLE = False
'''
        
        # اضافه کردن کد فعال‌سازی
        activation_code = '''
            # Inactive Modules Integration
            if INACTIVE_MODULES_AVAILABLE:
                try:
                    # AdaptiveMarginControl
                    self.adaptive_margin_control = AdaptiveMarginControl()
                    logger.info("✅ AdaptiveMarginControl: Initialized")
                    success_count += 1
                    
                    # AdvancedRewardSystem
                    self.advanced_reward_system = AdvancedRewardSystem()
                    logger.info("✅ AdvancedRewardSystem: Initialized")
                    success_count += 1
                    
                    # AutoMarketMaker
                    self.auto_market_maker = AutoMarketMaker()
                    logger.info("✅ AutoMarketMaker: Initialized")
                    success_count += 1
                    
                    # HierarchicalRL
                    self.hierarchical_rl = HierarchicalRL()
                    logger.info("✅ HierarchicalRL: Initialized")
                    success_count += 1
                    
                    # ZeroShotLearning
                    self.zero_shot_learning = ZeroShotLearning()
                    logger.info("✅ ZeroShotLearning: Initialized")
                    success_count += 1
                    
                    total_count += 5
                    
                except Exception as e:
                    logger.warning(f"⚠️ Inactive modules initialization failed: {e}")
                    total_count += 5
'''
        
        # پیدا کردن محل مناسب برای اضافه کردن
        if 'INACTIVE_MODULES_AVAILABLE' not in content:
            # اضافه کردن import
            import_pos = content.find('logger = logging.getLogger(__name__)')
            if import_pos != -1:
                content = content[:import_pos] + import_section + '\n' + content[import_pos:]
            
            # اضافه کردن کد فعال‌سازی
            activation_pos = content.find('total_count += 3')
            if activation_pos != -1:
                # یافتن انتهای بلوک
                end_pos = content.find('\n\n', activation_pos)
                if end_pos != -1:
                    content = content[:end_pos] + '\n' + activation_code + content[end_pos:]
            
            # ذخیره فایل
            with open('main_new.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("✅ Main system updated with inactive modules")
            return True
        else:
            logger.info("ℹ️ Main system already includes inactive modules")
            return True
            
    except Exception as e:
        logger.error(f"❌ Failed to update main system: {e}")
        return False

def main():
    """اجرای اسکریپت فعال‌سازی"""
    print("🔧 Inactive Modules Activation Script")
    print("=" * 50)
    
    # فعال‌سازی ماژول‌ها
    active_modules = integrate_modules_into_system()
    
    # ایجاد فایل تنظیمات
    create_integration_config()
    
    # به‌روزرسانی سیستم اصلی
    update_success = update_main_system()
    
    print(f"\n📋 Results:")
    print(f"  Active Modules: {len(active_modules)}")
    print(f"  System Updated: {'✅ Yes' if update_success else '❌ No'}")
    
    if len(active_modules) > 0:
        print(f"\n🎯 Successfully Activated Modules:")
        for module_name in active_modules:
            print(f"  ✅ {module_name}")
    
    print(f"\n🎉 Inactive modules activation completed!")
    print(f"   Run 'python main_new.py' to test the integrated system")

if __name__ == "__main__":
    main() 