"""
🌍 Environment Package - Refactored for v2.0
پکیج محیط‌های معاملاتی - بازسازی شده برای نسخه 2.0

این پکیج شامل محیط‌های معاملاتی سازگار با معماری جدید است
"""

# Import from core
from core.base import BaseComponent, TradingSignal, MarketData
from core.logger import get_logger
from core.config import get_config
from core.exceptions import TradingSystemError, ValidationError

# Legacy imports - maintained for backward compatibility
try:
    from .trading_env import (
        TradingEnv,
        TradingEnvironment,
        BaseTradingEnv
    )
except ImportError:
    # Will be created if doesn't exist
    pass

try:
    from .portfolio import (
        PortfolioEnv,
        PortfolioManager,
        Portfolio
    )
except ImportError:
    pass

# New environment classes compatible with v2.0
class TradingEnvironmentV2(BaseComponent):
    """محیط معاملاتی نسخه 2.0"""
    
    def __init__(self, symbol: str = "EURUSD", timeframe: str = "H1", config: dict = None):
        super().__init__()
        
        self.symbol = symbol
        self.timeframe = timeframe
        self.config = config or get_config().trading.__dict__
        
        # Environment state
        self.current_price = 0.0
        self.balance = self.config.get("initial_balance", 10000.0)
        self.positions = []
        self.history = []
        
        # Risk management
        self.max_position_size = self.config.get("max_position_size", 0.1)
        self.stop_loss_pct = self.config.get("stop_loss_pct", 0.02)
        self.take_profit_pct = self.config.get("take_profit_pct", 0.04)
        
        # Market data
        self.market_data = None
        
        self.logger = get_logger(__name__)
    
    def initialize(self) -> bool:
        """مقداردهی اولیه محیط"""
        try:
            # Load market data
            self.market_data = self._load_market_data()
            
            # Initialize portfolio
            self._initialize_portfolio()
            
            self.logger.info(f"Trading environment initialized: {self.symbol} ({self.timeframe})")
            self._initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize trading environment: {e}")
            return False
    
    def start(self) -> bool:
        """شروع محیط معاملاتی"""
        if not self._initialized:
            if not self.initialize():
                return False
        
        self._running = True
        self.logger.info(f"Trading environment started: {self.symbol}")
        return True
    
    def stop(self) -> bool:
        """توقف محیط معاملاتی"""
        try:
            # Close all positions
            self._close_all_positions()
            
            # Save state
            self._save_state()
            
            self._running = False
            self.logger.info(f"Trading environment stopped: {self.symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping trading environment: {e}")
            return False
    
    def health_check(self) -> dict:
        """بررسی سلامت محیط"""
        return {
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "initialized": self._initialized,
            "running": self._running,
            "balance": self.balance,
            "positions_count": len(self.positions),
            "current_price": self.current_price,
            "market_data_available": self.market_data is not None
        }
    
    def _load_market_data(self):
        """بارگذاری داده‌های بازار"""
        try:
            # Load from data source
            import pandas as pd
            from pathlib import Path
            
            data_path = Path(f"data/{self.symbol}/{self.timeframe}.csv")
            if data_path.exists():
                return pd.read_csv(data_path)
            else:
                self.logger.warning(f"Market data not found: {data_path}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error loading market data: {e}")
            return None
    
    def _initialize_portfolio(self):
        """مقداردهی اولیه پورتفولیو"""
        self.positions = []
        self.history = []
        self.balance = self.config.get("initial_balance", 10000.0)
    
    def _close_all_positions(self):
        """بستن تمام پوزیشن‌ها"""
        for position in self.positions:
            self._close_position(position)
        self.positions = []
    
    def _close_position(self, position):
        """بستن یک پوزیشن"""
        # Calculate P&L
        if position["type"] == "buy":
            pnl = (self.current_price - position["entry_price"]) * position["size"]
        else:
            pnl = (position["entry_price"] - self.current_price) * position["size"]
        
        # Update balance
        self.balance += pnl
        
        # Add to history
        self.history.append({
            "timestamp": position["timestamp"],
            "action": "close",
            "type": position["type"],
            "entry_price": position["entry_price"],
            "exit_price": self.current_price,
            "size": position["size"],
            "pnl": pnl
        })
    
    def _save_state(self):
        """ذخیره وضعیت محیط"""
        # Save to file or database
        pass
    
    def reset(self):
        """بازنشانی محیط"""
        self._initialize_portfolio()
        self.current_price = 0.0
        return self.get_state()
    
    def step(self, action):
        """انجام یک قدم در محیط"""
        # Process action
        reward = self._process_action(action)
        
        # Update state
        self._update_state()
        
        # Check if done
        done = self._is_done()
        
        # Get new state
        state = self.get_state()
        
        # Additional info
        info = {
            "balance": self.balance,
            "positions": len(self.positions),
            "current_price": self.current_price
        }
        
        return state, reward, done, info
    
    def _process_action(self, action):
        """پردازش عمل"""
        # Action: 0=hold, 1=buy, 2=sell
        if action == 1:  # Buy
            return self._execute_buy()
        elif action == 2:  # Sell
            return self._execute_sell()
        else:  # Hold
            return 0.0
    
    def _execute_buy(self):
        """اجرای خرید"""
        size = self._calculate_position_size()
        
        if size > 0:
            position = {
                "timestamp": self._get_current_time(),
                "type": "buy",
                "entry_price": self.current_price,
                "size": size,
                "stop_loss": self.current_price * (1 - self.stop_loss_pct),
                "take_profit": self.current_price * (1 + self.take_profit_pct)
            }
            
            self.positions.append(position)
            return 1.0  # Positive reward for taking action
        
        return -0.1  # Negative reward for invalid action
    
    def _execute_sell(self):
        """اجرای فروش"""
        size = self._calculate_position_size()
        
        if size > 0:
            position = {
                "timestamp": self._get_current_time(),
                "type": "sell",
                "entry_price": self.current_price,
                "size": size,
                "stop_loss": self.current_price * (1 + self.stop_loss_pct),
                "take_profit": self.current_price * (1 - self.take_profit_pct)
            }
            
            self.positions.append(position)
            return 1.0  # Positive reward for taking action
        
        return -0.1  # Negative reward for invalid action
    
    def _calculate_position_size(self):
        """محاسبه اندازه پوزیشن"""
        available_balance = self.balance
        risk_amount = available_balance * self.config.get("risk_per_trade", 0.02)
        
        if self.stop_loss_pct > 0:
            position_size = risk_amount / (self.current_price * self.stop_loss_pct)
            return min(position_size, self.max_position_size)
        
        return 0.0
    
    def _update_state(self):
        """به‌روزرسانی وضعیت"""
        # Update current price (this would come from market data)
        if self.market_data is not None and len(self.market_data) > 0:
            # Use last available price
            self.current_price = self.market_data.iloc[-1]["close"]
        
        # Check stop loss and take profit
        self._check_stop_loss_take_profit()
    
    def _check_stop_loss_take_profit(self):
        """بررسی stop loss و take profit"""
        positions_to_close = []
        
        for position in self.positions:
            if position["type"] == "buy":
                if self.current_price <= position["stop_loss"]:
                    positions_to_close.append(position)
                elif self.current_price >= position["take_profit"]:
                    positions_to_close.append(position)
            else:  # sell
                if self.current_price >= position["stop_loss"]:
                    positions_to_close.append(position)
                elif self.current_price <= position["take_profit"]:
                    positions_to_close.append(position)
        
        # Close positions
        for position in positions_to_close:
            self._close_position(position)
            self.positions.remove(position)
    
    def _is_done(self):
        """بررسی پایان محیط"""
        # End if balance is too low
        if self.balance < self.config.get("min_balance", 1000.0):
            return True
        
        # End if no more data
        if self.market_data is None:
            return True
        
        return False
    
    def get_state(self):
        """دریافت وضعیت فعلی"""
        return {
            "balance": self.balance,
            "current_price": self.current_price,
            "positions": len(self.positions),
            "open_positions": [p for p in self.positions],
            "timestamp": self._get_current_time()
        }
    
    def _get_current_time(self):
        """دریافت زمان فعلی"""
        from datetime import datetime
        return datetime.now()
    
    def get_info(self):
        """دریافت اطلاعات محیط"""
        return {
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "balance": self.balance,
            "positions": len(self.positions),
            "total_trades": len(self.history),
            "current_price": self.current_price
        }

# Legacy compatibility wrappers
class LegacyTradingEnv(TradingEnvironmentV2):
    """محیط معاملاتی قدیمی برای backward compatibility"""
    
    def __init__(self, df=None, symbol="EURUSD", style="trend", timeframe="H1"):
        super().__init__(symbol=symbol, timeframe=timeframe)
        
        if df is not None:
            self.market_data = df
        
        self.style = style
    
    def reset(self):
        """بازنشانی محیط - سازگار با gym"""
        super().reset()
        return self._get_observation()
    
    def step(self, action):
        """قدم در محیط - سازگار با gym"""
        state, reward, done, info = super().step(action)
        observation = self._get_observation()
        return observation, reward, done, info
    
    def _get_observation(self):
        """دریافت observation برای RL"""
        # Return observation compatible with RL models
        return [
            self.balance / 10000.0,  # Normalized balance
            self.current_price,
            len(self.positions),
            # Add more features as needed
        ]

# Environment factory
class EnvironmentFactory:
    """کارخانه محیط‌ها"""
    
    @staticmethod
    def create_trading_env(symbol="EURUSD", timeframe="H1", version="v2"):
        """ایجاد محیط معاملاتی"""
        if version == "v2":
            return TradingEnvironmentV2(symbol=symbol, timeframe=timeframe)
        else:
            return LegacyTradingEnv(symbol=symbol, timeframe=timeframe)
    
    @staticmethod
    def create_portfolio_env(initial_balance=10000.0):
        """ایجاد محیط پورتفولیو"""
        # This would be implemented when portfolio module is refactored
        pass

# Global factory instance
env_factory = EnvironmentFactory()

# Export all available functions and classes
__all__ = [
    # New environment classes
    "TradingEnvironmentV2",
    "LegacyTradingEnv",
    "EnvironmentFactory",
    "env_factory",
    
    # Base classes
    "BaseComponent",
    "TradingSignal",
    "MarketData",
    
    # Utility functions
    "get_config",
    "get_logger"
]

# Version info
__version__ = "2.0.0"
__author__ = "Trading System Team"

# Migration message
import warnings
warnings.warn(
    "The env package has been refactored for v2.0. "
    "Please update your code to use the new TradingEnvironmentV2 class. "
    "Legacy TradingEnv is maintained for backward compatibility but may be removed in future versions.",
    DeprecationWarning,
    stacklevel=2
)

# Initialize environment package
def initialize_env_package():
    """مقداردهی اولیه پکیج محیط"""
    logger = get_logger(__name__)
    
    try:
        logger.info("✅ Environment package initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Environment initialization failed: {e}")
        return False

# Auto-initialize when imported
if __name__ != "__main__":
    initialize_env_package()
