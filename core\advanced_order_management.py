#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ Advanced Order Management System
سیستم پیشرفته مدیریت سفارشات با Database Integration و Real-time Tracking
"""

import os
import sys
import time
import uuid
import asyncio
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from decimal import Decimal, getcontext
import threading
from contextlib import contextmanager
import logging
import json

# Set decimal precision for financial calculations
getcontext().prec = 28

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from .database_transaction_manager import (
        get_database_manager, 
        transaction,
        TransactionType,
        TradingSignal,
        TradingPosition
    )
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False

# Configure logging
logger = logging.getLogger(__name__)

class OrderType(Enum):
    """نوع سفارش پیشرفته"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"
    ICEBERG = "iceberg"
    TWAP = "twap"  # Time-Weighted Average Price
    VWAP = "vwap"  # Volume-Weighted Average Price
    BRACKET = "bracket"  # Bracket Order (Entry + SL + TP)
    OCO = "oco"  # One Cancels Other

class OrderSide(Enum):
    """جهت سفارش"""
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    """وضعیت سفارش"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    ACCEPTED = "accepted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"
    REPLACED = "replaced"

class ExecutionModel(Enum):
    """مدل اجرای سفارش"""
    IMMEDIATE = "immediate"
    GRADUAL = "gradual"
    SMART = "smart"
    AGGRESSIVE = "aggressive"
    PASSIVE = "passive"

class RiskCheckResult(Enum):
    """نتیجه بررسی ریسک"""
    APPROVED = "approved"
    REJECTED = "rejected"
    WARNING = "warning"
    PENDING_APPROVAL = "pending_approval"

@dataclass
class OrderExecution:
    """اطلاعات اجرای سفارش"""
    execution_id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: Decimal
    price: Decimal
    commission: Decimal
    exchange: str
    timestamp: datetime
    execution_model: ExecutionModel
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass 
class AdvancedOrder:
    """سفارش پیشرفته"""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: Decimal
    price: Optional[Decimal] = None
    stop_price: Optional[Decimal] = None
    trail_amount: Optional[Decimal] = None
    iceberg_quantity: Optional[Decimal] = None
    time_window: Optional[int] = None  # for TWAP/VWAP in minutes
    
    # Status and tracking
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: Decimal = Decimal('0')
    avg_fill_price: Optional[Decimal] = None
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.now)
    submitted_at: Optional[datetime] = None
    filled_at: Optional[datetime] = None
    cancelled_at: Optional[datetime] = None
    
    # Advanced features
    parent_order_id: Optional[str] = None  # For bracket orders
    child_order_ids: List[str] = field(default_factory=list)
    executions: List[OrderExecution] = field(default_factory=list)
    risk_check_result: RiskCheckResult = RiskCheckResult.PENDING_APPROVAL
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        # Ensure decimal types
        self.quantity = Decimal(str(self.quantity))
        self.filled_quantity = Decimal(str(self.filled_quantity))
        
        if self.price is not None:
            self.price = Decimal(str(self.price))
        if self.stop_price is not None:
            self.stop_price = Decimal(str(self.stop_price))
        if self.trail_amount is not None:
            self.trail_amount = Decimal(str(self.trail_amount))
        if self.iceberg_quantity is not None:
            self.iceberg_quantity = Decimal(str(self.iceberg_quantity))
    
    def get_remaining_quantity(self) -> Decimal:
        """مقدار باقیمانده سفارش"""
        return self.quantity - self.filled_quantity
    
    def get_fill_percentage(self) -> float:
        """درصد پر شدن سفارش"""
        if self.quantity == 0:
            return 0.0
        return float((self.filled_quantity / self.quantity) * 100)
    
    def is_active(self) -> bool:
        """آیا سفارش فعال است"""
        return self.status in [
            OrderStatus.PENDING,
            OrderStatus.SUBMITTED, 
            OrderStatus.ACCEPTED,
            OrderStatus.PARTIALLY_FILLED
        ]
    
    def add_execution(self, execution: OrderExecution):
        """اضافه کردن execution"""
        self.executions.append(execution)
        self.filled_quantity += execution.quantity
        
        # Update average fill price
        if self.filled_quantity > 0:
            total_value = sum(ex.price * ex.quantity for ex in self.executions)
            self.avg_fill_price = total_value / self.filled_quantity
        
        # Update status
        if self.filled_quantity >= self.quantity:
            self.status = OrderStatus.FILLED
            self.filled_at = datetime.now()
        elif self.filled_quantity > 0:
            self.status = OrderStatus.PARTIALLY_FILLED

@dataclass
class AdvancedPosition:
    """پوزیشن پیشرفته"""
    symbol: str
    long_quantity: Decimal = Decimal('0')
    short_quantity: Decimal = Decimal('0')
    avg_long_price: Decimal = Decimal('0')
    avg_short_price: Decimal = Decimal('0')
    current_price: Decimal = Decimal('0')
    unrealized_pnl: Decimal = Decimal('0')
    realized_pnl: Decimal = Decimal('0')
    
    # Tracking
    opened_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    order_ids: List[str] = field(default_factory=list)
    
    def get_net_quantity(self) -> Decimal:
        """مقدار خالص پوزیشن"""
        return self.long_quantity - self.short_quantity
    
    def get_exposure(self) -> Decimal:
        """میزان مواجهه"""
        net = self.get_net_quantity()
        return abs(net) * self.current_price
    
    def update_price(self, price: Decimal):
        """بروزرسانی قیمت"""
        self.current_price = Decimal(str(price))
        self.updated_at = datetime.now()
        self._calculate_unrealized_pnl()
    
    def _calculate_unrealized_pnl(self):
        """محاسبه سود و زیان غیر محقق"""
        long_pnl = Decimal('0')
        short_pnl = Decimal('0')
        
        if self.long_quantity > 0:
            long_pnl = (self.current_price - self.avg_long_price) * self.long_quantity
        
        if self.short_quantity > 0:
            short_pnl = (self.avg_short_price - self.current_price) * self.short_quantity
        
        self.unrealized_pnl = long_pnl + short_pnl

class AdvancedRiskManager:
    """مدیر ریسک پیشرفته"""
    
    def __init__(self):
        self.max_position_size = Decimal('1000000')
        self.max_orders_per_symbol = 50
        self.max_daily_volume = Decimal('10000000')
        self.margin_requirement = Decimal('0.01')  # 1%
        self.daily_volumes = {}
        
    def check_order_risk(self, order: AdvancedOrder, current_position: Optional[AdvancedPosition] = None) -> RiskCheckResult:
        """بررسی ریسک سفارش"""
        try:
            # Basic validations
            if order.quantity <= 0:
                return RiskCheckResult.REJECTED
            
            # Position size check
            new_exposure = order.quantity * (order.price or Decimal('1'))
            if new_exposure > self.max_position_size:
                return RiskCheckResult.REJECTED
            
            # Daily volume check
            today = datetime.now().date()
            if today not in self.daily_volumes:
                self.daily_volumes[today] = {}
            
            symbol_volume = self.daily_volumes[today].get(order.symbol, Decimal('0'))
            if symbol_volume + new_exposure > self.max_daily_volume:
                return RiskCheckResult.WARNING
            
            # Update daily volume
            self.daily_volumes[today][order.symbol] = symbol_volume + new_exposure
            
            return RiskCheckResult.APPROVED
            
        except Exception as e:
            logger.error(f"Risk check error: {e}")
            return RiskCheckResult.REJECTED

class AdvancedOrderManager:
    """مدیر سفارشات پیشرفته"""
    
    def __init__(self):
        self.orders: Dict[str, AdvancedOrder] = {}
        self.positions: Dict[str, AdvancedPosition] = {}
        self.order_history: List[AdvancedOrder] = []
        self.execution_history: List[OrderExecution] = []
        
        # Components
        self.risk_manager = AdvancedRiskManager()
        self.execution_callbacks: List[Callable] = []
        
        # Threading
        self._lock = threading.Lock()
        self._running = False
        self._background_thread = None
        
        # Database integration
        self.db_manager = None
        if DATABASE_AVAILABLE:
            try:
                self.db_manager = get_database_manager()
                logger.info("Database integration enabled for order management")
            except Exception as e:
                logger.warning(f"Database integration failed: {e}")
        
        logger.info("Advanced Order Manager initialized")
    
    def create_order(self, symbol: str, side: OrderSide, order_type: OrderType,
                    quantity: Union[Decimal, float], **kwargs) -> AdvancedOrder:
        """ایجاد سفارش پیشرفته"""
        order_id = f"ORD_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        
        order = AdvancedOrder(
            order_id=order_id,
            symbol=symbol.upper(),
            side=side,
            order_type=order_type,
            quantity=Decimal(str(quantity)),
            **{k: v for k, v in kwargs.items() if v is not None}
        )
        
        # Risk check
        current_position = self.positions.get(symbol)
        risk_result = self.risk_manager.check_order_risk(order, current_position)
        order.risk_check_result = risk_result
        
        if risk_result == RiskCheckResult.REJECTED:
            order.status = OrderStatus.REJECTED
            logger.warning(f"Order rejected due to risk: {order_id}")
        
        with self._lock:
            self.orders[order_id] = order
        
        # Save to database
        self._save_order_to_db(order)
        
        logger.info(f"Order created: {order_id} - {symbol} {side.value} {quantity}")
        return order
    
    def submit_order(self, order_id: str) -> bool:
        """ارسال سفارش"""
        order = self.orders.get(order_id)
        if not order:
            return False
        
        if order.risk_check_result != RiskCheckResult.APPROVED:
            if order.risk_check_result == RiskCheckResult.WARNING:
                # اختیاری: درخواست تایید manual
                pass
            else:
                order.status = OrderStatus.REJECTED
                return False
        
        order.status = OrderStatus.SUBMITTED
        order.submitted_at = datetime.now()
        
        # Simulate order routing to exchange
        if order.order_type == OrderType.MARKET:
            # Market orders execute immediately
            self._simulate_market_execution(order)
        else:
            # Other orders wait for market conditions
            order.status = OrderStatus.ACCEPTED
        
        logger.info(f"Order submitted: {order_id}")
        return True
    
    def cancel_order(self, order_id: str) -> bool:
        """لغو سفارش"""
        order = self.orders.get(order_id)
        if not order or not order.is_active():
            return False
        
        order.status = OrderStatus.CANCELLED
        order.cancelled_at = datetime.now()
        
        # Update database
        self._update_order_in_db(order)
        
        logger.info(f"Order cancelled: {order_id}")
        return True
    
    def modify_order(self, order_id: str, **modifications) -> bool:
        """تغییر سفارش"""
        order = self.orders.get(order_id)
        if not order or not order.is_active():
            return False
        
        # Create new order with modifications
        new_order_id = f"ORD_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        
        # Copy current order attributes
        order_dict = {
            'symbol': order.symbol,
            'side': order.side,
            'order_type': order.order_type,
            'quantity': order.quantity,
            'price': order.price,
            'stop_price': order.stop_price,
            'trail_amount': order.trail_amount,
            'iceberg_quantity': order.iceberg_quantity,
            'time_window': order.time_window
        }
        
        # Apply modifications
        order_dict.update(modifications)
        
        # Create new order
        new_order = self.create_order(**order_dict)
        
        # Cancel old order
        order.status = OrderStatus.REPLACED
        
        logger.info(f"Order modified: {order_id} -> {new_order_id}")
        return True
    
    def execute_order(self, order_id: str, price: Union[Decimal, float], 
                     quantity: Union[Decimal, float], exchange: str = "") -> bool:
        """اجرای سفارش"""
        order = self.orders.get(order_id)
        if not order or not order.is_active():
            return False
        
        execution_id = f"EXE_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        execution = OrderExecution(
            execution_id=execution_id,
            order_id=order_id,
            symbol=order.symbol,
            side=order.side,
            quantity=Decimal(str(quantity)),
            price=Decimal(str(price)),
            commission=Decimal('0'),  # Calculate based on exchange rules
            exchange=exchange,
            timestamp=datetime.now(),
            execution_model=ExecutionModel.IMMEDIATE
        )
        
        # Add execution to order
        order.add_execution(execution)
        
        # Update position
        self._update_position(order, execution)
        
        # Save to database
        self._save_execution_to_db(execution)
        
        # Notify callbacks
        for callback in self.execution_callbacks:
            try:
                callback(order, execution)
            except Exception as e:
                logger.error(f"Execution callback error: {e}")
        
        logger.info(f"Order executed: {order_id} - {quantity}@{price}")
        return True
    
    def _simulate_market_execution(self, order: AdvancedOrder):
        """شبیه‌سازی اجرای Market Order"""
        # در پیاده‌سازی واقعی، قیمت از market data می‌آید
        mock_price = Decimal('1.1000')  # Mock price
        self.execute_order(order.order_id, mock_price, order.quantity)
    
    def _update_position(self, order: AdvancedOrder, execution: OrderExecution):
        """بروزرسانی پوزیشن"""
        symbol = order.symbol
        
        if symbol not in self.positions:
            self.positions[symbol] = AdvancedPosition(symbol=symbol)
        
        position = self.positions[symbol]
        
        if order.side == OrderSide.BUY:
            # Update long position
            total_value = (position.avg_long_price * position.long_quantity) + (execution.price * execution.quantity)
            position.long_quantity += execution.quantity
            if position.long_quantity > 0:
                position.avg_long_price = total_value / position.long_quantity
        else:
            # Update short position
            total_value = (position.avg_short_price * position.short_quantity) + (execution.price * execution.quantity)
            position.short_quantity += execution.quantity
            if position.short_quantity > 0:
                position.avg_short_price = total_value / position.short_quantity
        
        position.updated_at = datetime.now()
        position.order_ids.append(order.order_id)
        
        # Update current price
        position.update_price(execution.price)
    
    def _save_order_to_db(self, order: AdvancedOrder):
        """ذخیره سفارش در دیتابیس"""
        if not self.db_manager:
            return
        
        try:
            with transaction(transaction_type=TransactionType.WRITE) as (session, metrics):
                # Save as trading signal
                signal = TradingSignal(
                    symbol=order.symbol,
                    signal_type=f"{order.side.value.upper()}_{order.order_type.value.upper()}",
                    price=float(order.price or 0),
                    confidence=0.9,
                    source="order_manager",
                    signal_metadata=json.dumps({
                        'order_id': order.order_id,
                        'quantity': str(order.quantity),
                        'status': order.status.value
                    })
                )
                session.add(signal)
                
        except Exception as e:
            logger.error(f"Failed to save order to database: {e}")
    
    def _update_order_in_db(self, order: AdvancedOrder):
        """بروزرسانی سفارش در دیتابیس"""
        if not self.db_manager:
            return
        
        try:
            with transaction(transaction_type=TransactionType.WRITE) as (session, metrics):
                # Update signal metadata
                signal = session.query(TradingSignal).filter(
                    TradingSignal.signal_metadata.contains(order.order_id)
                ).first()
                
                if signal:
                    metadata = json.loads(signal.signal_metadata)
                    metadata['status'] = order.status.value
                    metadata['filled_quantity'] = str(order.filled_quantity)
                    signal.signal_metadata = json.dumps(metadata)
                    signal.processed = order.status == OrderStatus.FILLED
                
        except Exception as e:
            logger.error(f"Failed to update order in database: {e}")
    
    def _save_execution_to_db(self, execution: OrderExecution):
        """ذخیره execution در دیتابیس"""
        if not self.db_manager:
            return
        
        try:
            with transaction(transaction_type=TransactionType.WRITE) as (session, metrics):
                # Save as trading position
                position_record = TradingPosition(
                    symbol=execution.symbol,
                    position_type=execution.side.value,
                    entry_price=float(execution.price),
                    quantity=float(execution.quantity),
                    status='filled'
                )
                session.add(position_record)
                
        except Exception as e:
            logger.error(f"Failed to save execution to database: {e}")
    
    def update_market_price(self, symbol: str, price: Union[Decimal, float]):
        """بروزرسانی قیمت بازار"""
        if symbol in self.positions:
            self.positions[symbol].update_price(Decimal(str(price)))
    
    def get_order(self, order_id: str) -> Optional[AdvancedOrder]:
        """دریافت سفارش"""
        return self.orders.get(order_id)
    
    def get_active_orders(self, symbol: Optional[str] = None) -> List[AdvancedOrder]:
        """دریافت سفارشات فعال"""
        active_orders = [order for order in self.orders.values() if order.is_active()]
        
        if symbol:
            active_orders = [order for order in active_orders if order.symbol == symbol.upper()]
        
        return active_orders
    
    def get_position(self, symbol: str) -> Optional[AdvancedPosition]:
        """دریافت پوزیشن"""
        return self.positions.get(symbol.upper())
    
    def get_all_positions(self) -> Dict[str, AdvancedPosition]:
        """دریافت تمام پوزیشن‌ها"""
        return self.positions.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """آمار سیستم"""
        total_orders = len(self.orders)
        active_orders = len([o for o in self.orders.values() if o.is_active()])
        filled_orders = len([o for o in self.orders.values() if o.status == OrderStatus.FILLED])
        
        total_pnl = sum(pos.unrealized_pnl + pos.realized_pnl for pos in self.positions.values())
        total_exposure = sum(pos.get_exposure() for pos in self.positions.values())
        
        return {
            'total_orders': total_orders,
            'active_orders': active_orders,
            'filled_orders': filled_orders,
            'fill_rate': (filled_orders / total_orders * 100) if total_orders > 0 else 0,
            'total_positions': len(self.positions),
            'total_pnl': float(total_pnl),
            'total_exposure': float(total_exposure),
            'total_executions': len(self.execution_history)
        }
    
    def add_execution_callback(self, callback: Callable):
        """اضافه کردن callback برای execution"""
        self.execution_callbacks.append(callback)
    
    def close(self):
        """بستن سیستم"""
        logger.info("Advanced Order Manager closing")

# Global instance
_order_manager: Optional[AdvancedOrderManager] = None
_order_lock = threading.Lock()

def get_order_manager() -> AdvancedOrderManager:
    """دریافت instance سراسری order manager"""
    global _order_manager
    
    with _order_lock:
        if _order_manager is None:
            _order_manager = AdvancedOrderManager()
        return _order_manager

def close_order_manager():
    """بستن order manager سراسری"""
    global _order_manager
    
    with _order_lock:
        if _order_manager:
            _order_manager.close()
            _order_manager = None

# Convenience functions
def create_market_order(symbol: str, side: OrderSide, quantity: Union[Decimal, float]) -> AdvancedOrder:
    """ایجاد Market Order ساده"""
    return get_order_manager().create_order(symbol, side, OrderType.MARKET, quantity)

def create_limit_order(symbol: str, side: OrderSide, quantity: Union[Decimal, float], 
                      price: Union[Decimal, float]) -> AdvancedOrder:
    """ایجاد Limit Order ساده"""
    return get_order_manager().create_order(symbol, side, OrderType.LIMIT, quantity, price=price)

if __name__ == "__main__":
    # Test the system
    print("🧪 Testing Advanced Order Management System...")
    
    order_manager = get_order_manager()
    
    # Create test order
    order = create_market_order("EURUSD", OrderSide.BUY, 1000)
    print(f"✅ Order created: {order.order_id}")
    
    # Submit order
    success = order_manager.submit_order(order.order_id)
    print(f"✅ Order submitted: {success}")
    
    # Get statistics
    stats = order_manager.get_statistics()
    print(f"📊 Order Manager Statistics: {stats}")
    
    close_order_manager()
    print("🎉 Advanced Order Management System test completed!") 