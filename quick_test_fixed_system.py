#!/usr/bin/env python3
"""
🔧 تست سریع سیستم تصحیح شده
"""

def test_fixed_system():
    """🧪 تست سریع تغییرات اعمال شده"""
    
    print("🔧 TESTING FIXED MULTI-BRAIN SYSTEM")
    print("=" * 50)
    
    # Test 1: Early Stopping Manager
    print("\n1️⃣ Testing SmartEarlyStoppingManager...")
    try:
        from fixed_ultimate_main import SmartEarlyStoppingManager
        manager = SmartEarlyStoppingManager()
        
        # Test early stopping decision
        config = {'early_stopping_enabled': False, 'patience': 50}
        analysis = {'confidence': 0.75, 'reasoning': 'Test reasoning'}
        
        should_stop, new_patience = manager.should_stop_training(
            'test_model', 20, 0.5, 0.4, 25, config, analysis
        )
        
        print(f"   ✅ Early stopping decision: {should_stop}")
        print(f"   ✅ New patience counter: {new_patience}")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Analysis Keys
    print("\n2️⃣ Testing ensure_analysis_keys...")
    try:
        from fixed_ultimate_main import ensure_analysis_keys
        
        test_analysis = {'action': 'train_advanced'}
        fixed_analysis = ensure_analysis_keys(test_analysis)
        
        required_keys = ['performance_grade', 'unseen', 'model_readiness', 'early_stopping_config']
        missing_keys = [key for key in required_keys if key not in fixed_analysis]
        
        if not missing_keys:
            print("   ✅ All required keys present")
        else:
            print(f"   ❌ Missing keys: {missing_keys}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Google Drive Cache
    print("\n3️⃣ Testing Google Drive cache functions...")
    try:
        from fixed_ultimate_main import get_google_drive_cache_path, ensure_google_drive_cache_dirs
        
        # Test cache path generation
        model_path = get_google_drive_cache_path("saved_models", "test_model.pth")
        print(f"   ✅ Model cache path: {model_path}")
        
        # Test directory creation
        dirs_created = ensure_google_drive_cache_dirs()
        print(f"   ✅ Cache directories created: {dirs_created}")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Import Check
    print("\n4️⃣ Testing main imports...")
    try:
        from fixed_ultimate_main import (
            safe_analyze_training_situation,
            EARLY_STOPPING_MANAGER,
            SMART_CACHE
        )
        print("   ✅ All main functions imported successfully")
        
    except Exception as e:
        print(f"   ❌ Import error: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 QUICK TEST COMPLETED")
    print("✅ System ready for full training!")

if __name__ == "__main__":
    test_fixed_system()
