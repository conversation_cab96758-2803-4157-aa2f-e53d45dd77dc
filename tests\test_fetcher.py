import pytest
import pandas as pd
from data.fetcher import MT5DataFetcher



@pytest.fixture
def fetcher():
    return MT5DataFetcher("data/storage/")



def test_fetch_data(fetcher):
    # این تست فقط ساختار را بررسی می‌کند، وجود فایل داده لازم است
    try:
        df = fetcher.fetch_data("EURUSD", period="1y", interval="H1")
        assert isinstance(df, pd.DataFrame)
    except FileNotFoundError:
        pass  # اگر فایل نبود، تست را رد نمی‌کنیم
