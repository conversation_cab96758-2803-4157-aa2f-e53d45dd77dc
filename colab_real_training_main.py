"""
🔥 Pearl-3x7B REAL Colab Training Main
اسکریپت اصلی آموزش واقعی در Google Colab

این بار با دیتاست‌های واقعی و آموزش اصولی!
"""

import os
import sys
import time
import json
from datetime import datetime

def main_real_colab_training():
    """🔥 اجرای کامل آموزش واقعی در Colab"""
    print("🔥 PEARL-3X7B REAL COLAB TRAINING")
    print("=" * 80)
    print("🎯 Training with REAL datasets and PROPER training!")
    print("📊 This will take 2-6 hours depending on your GPU")
    print()
    
    # Step 1: Download real datasets
    print("📋 STEP 1: DOWNLOADING REAL DATASETS")
    print("=" * 50)
    
    try:
        exec(open('/content/real_colab_trainer.py').read())
        dataset_results = download_real_datasets()
        
        if not dataset_results['success']:
            print("❌ Dataset download failed!")
            print("Please check your internet connection and try again.")
            return {"success": False, "error": "Dataset download failed"}
        
        print("✅ All real datasets downloaded successfully!")
        datasets_dir = dataset_results['data_dir']
        
    except Exception as e:
        print(f"❌ Dataset download error: {e}")
        return {"success": False, "error": f"Dataset download error: {e}"}
    
    # Step 2: Train all models with real data
    print(f"\n📋 STEP 2: TRAINING MODELS WITH REAL DATA")
    print("=" * 50)
    
    try:
        exec(open('/content/real_model_trainer.py').read())
        training_results = train_all_real_models(datasets_dir)
        
        print("✅ Model training completed!")
        
    except Exception as e:
        print(f"❌ Model training error: {e}")
        return {"success": False, "error": f"Model training error: {e}"}
    
    # Step 3: Package results for download
    print(f"\n📋 STEP 3: PACKAGING RESULTS FOR DOWNLOAD")
    print("=" * 50)
    
    try:
        package_results = package_trained_models(training_results)
        
        if package_results['success']:
            print(f"✅ Models packaged successfully!")
            print(f"📦 Download file: {package_results['zip_file']}")
        else:
            print("⚠️ Packaging failed, but models are saved individually")
        
    except Exception as e:
        print(f"❌ Packaging error: {e}")
        package_results = {"success": False, "error": str(e)}
    
    # Final summary
    print(f"\n🎉 REAL TRAINING SESSION COMPLETED!")
    print("=" * 80)
    
    successful_models = sum(1 for r in training_results.values() if r.get('success', False))
    total_models = len(training_results)
    
    print(f"✅ Successfully trained: {successful_models}/{total_models} models")
    
    total_training_time = sum(
        r.get('training_time_hours', 0) 
        for r in training_results.values() 
        if r.get('success', False)
    )
    
    print(f"⏱️ Total training time: {total_training_time:.2f} hours")
    
    if successful_models > 0:
        print(f"\n🏆 TRAINED MODELS:")
        for name, result in training_results.items():
            if result.get('success'):
                model_name = result.get('model_name', name)
                training_time = result.get('training_time_hours', 0)
                
                # Model-specific metrics
                if 'accuracy' in result:
                    metric = f"Accuracy: {result['accuracy']:.4f}"
                elif 'test_rmse' in result:
                    metric = f"RMSE: {result['test_rmse']:.6f}"
                elif 'final_avg_reward' in result:
                    metric = f"Avg Reward: {result['final_avg_reward']:.2f}"
                else:
                    metric = "Trained successfully"
                
                print(f"   ✅ {model_name}: {training_time:.2f}h - {metric}")
    
    if package_results.get('success'):
        print(f"\n📥 DOWNLOAD YOUR MODELS:")
        print(f"   from google.colab import files")
        print(f"   files.download('{package_results['zip_file']}')")
    
    # Save session report
    session_report = {
        "session_id": datetime.now().strftime("%Y%m%d_%H%M%S"),
        "dataset_results": dataset_results,
        "training_results": training_results,
        "package_results": package_results,
        "summary": {
            "successful_models": successful_models,
            "total_models": total_models,
            "total_training_time_hours": total_training_time,
            "session_completed": True
        }
    }
    
    report_file = f"/content/real_training_session_{session_report['session_id']}.json"
    with open(report_file, 'w') as f:
        json.dump(session_report, f, indent=2, default=str)
    
    print(f"\n📄 Session report saved: {report_file}")
    
    return session_report

def package_trained_models(training_results: dict) -> dict:
    """📦 بسته‌بندی مدل‌های آموزش دیده"""
    print("📦 Packaging trained models...")
    
    try:
        import zipfile
        import shutil
        
        # Create package directory
        package_dir = "/content/pearl_3x7b_real_models"
        os.makedirs(package_dir, exist_ok=True)
        
        # Package info
        package_info = {
            "package_name": "Pearl-3x7B Real Trained Models",
            "created_at": datetime.now().isoformat(),
            "training_type": "real_datasets",
            "models": []
        }
        
        successful_models = 0
        
        for name, result in training_results.items():
            if result.get('success') and 'model_path' in result:
                model_path = result['model_path']
                
                if os.path.exists(model_path):
                    # Copy model to package
                    dest_path = os.path.join(package_dir, f"{name}_model")
                    shutil.copytree(model_path, dest_path, dirs_exist_ok=True)
                    
                    # Add to package info
                    model_info = {
                        "name": result.get('model_name', name),
                        "category": name,
                        "training_time_hours": result.get('training_time_hours', 0),
                        "model_path": dest_path,
                        "metrics": {}
                    }
                    
                    # Add model-specific metrics
                    if 'accuracy' in result:
                        model_info['metrics']['accuracy'] = result['accuracy']
                        model_info['metrics']['f1_score'] = result.get('f1_score', 0)
                    elif 'test_rmse' in result:
                        model_info['metrics']['test_rmse'] = result['test_rmse']
                        model_info['metrics']['train_rmse'] = result.get('train_rmse', 0)
                    elif 'final_avg_reward' in result:
                        model_info['metrics']['final_avg_reward'] = result['final_avg_reward']
                        model_info['metrics']['max_reward'] = result.get('max_reward', 0)
                    
                    package_info['models'].append(model_info)
                    successful_models += 1
                    
                    print(f"   ✅ Packaged: {name}")
        
        if successful_models == 0:
            return {"success": False, "error": "No models to package"}
        
        # Save package info
        with open(os.path.join(package_dir, "package_info.json"), 'w') as f:
            json.dump(package_info, f, indent=2)
        
        # Create README
        readme_content = f"""# Pearl-3x7B Real Trained Models

## 📊 Package Information
- **Created**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Training Type**: Real datasets with proper training
- **Total Models**: {successful_models}
- **Training Platform**: Google Colab with GPU

## 🏆 Included Models

"""
        
        for model_info in package_info['models']:
            readme_content += f"""### {model_info['name']}
- **Training Time**: {model_info['training_time_hours']:.2f} hours
- **Metrics**: {model_info['metrics']}
- **Path**: `{os.path.basename(model_info['model_path'])}/`

"""
        
        readme_content += """
## 🚀 Usage Instructions

1. Extract this package to your Pearl-3x7B project
2. Place models in `models/trained_models/real_models/`
3. Use the provided model loading functions
4. Test models with your trading system

## 📞 Support
These models were trained with real financial data and proper training procedures.
Performance metrics are based on actual validation data.
"""
        
        with open(os.path.join(package_dir, "README.md"), 'w') as f:
            f.write(readme_content)
        
        # Create zip file
        zip_filename = f"/content/pearl_3x7b_real_models_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
        
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, package_dir)
                    zipf.write(file_path, arcname)
        
        print(f"✅ Package created: {zip_filename}")
        print(f"📊 Contains {successful_models} real trained models")
        
        return {
            "success": True,
            "zip_file": zip_filename,
            "package_dir": package_dir,
            "models_count": successful_models
        }
        
    except Exception as e:
        print(f"❌ Packaging failed: {e}")
        return {"success": False, "error": str(e)}

def show_real_training_instructions():
    """📋 نمایش دستورالعمل‌های آموزش واقعی"""
    print("""
🔥 PEARL-3X7B REAL TRAINING INSTRUCTIONS
========================================

📋 What This Does:
   • Downloads REAL financial data (Bitcoin, stocks, etc.)
   • Downloads REAL news sentiment data
   • Trains models with PROPER training procedures
   • Packages best models for download

🎯 Models Trained:
   • FinBERT: Real news sentiment analysis
   • LSTM: Real price prediction with Bitcoin data
   • DQN: Real trading agent with market data

⏱️ Expected Time:
   • Dataset download: 10-15 minutes
   • FinBERT training: 1-2 hours
   • LSTM training: 30-60 minutes
   • DQN training: 1-3 hours
   • Total: 2-6 hours

🔥 GPU Requirements:
   • T4: All models supported
   • V100/A100: Faster training

📋 To Start:
   main_real_colab_training()

💡 Tips:
   • Keep Colab tab open during training
   • Monitor with !nvidia-smi
   • Models auto-saved every epoch
   • Download zip file when complete
""")

# Main execution
if __name__ == "__main__":
    try:
        import google.colab
        print("🚀 Running in Google Colab")
        show_real_training_instructions()
        print("\n" + "="*50)
        print("🔥 Ready for REAL training!")
        print("Execute: main_real_colab_training()")
    except ImportError:
        print("⚠️ This script is designed for Google Colab")
        main_real_colab_training()
