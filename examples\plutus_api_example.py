"""
نمونه استفاده از API های مدل‌های مالی موجود
Example usage of available financial model APIs
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import time
from typing import Dict, List, Any, Optional
import logging

# تنظیمات پروکسی
PROXY_CONFIG = {
    "http": "http://127.0.0.1:10809",
    "https": "http://127.0.0.1:10809"
}

class FinancialModelAPI:
    """
    کلاس برای استفاده از API های مختلف مدل‌های مالی
    Class for using various financial model APIs
    """
    
    def __init__(self, use_proxy: bool = True):
        self.session = requests.Session()
        if use_proxy:
            self.session.proxies.update(PROXY_CONFIG)
        
        self.logger = logging.getLogger(__name__)
    
    def use_huggingface_financial_sentiment(self, 
                                          text: str, 
                                          model_name: str = "Sigma/financial-sentiment-analysis") -> Dict[str, Any]:
        """
        استفاده از مدل تحلیل احساسات مالی در Hugging Face
        Use Hugging Face financial sentiment analysis model
        """
        try:
            url = f"https://api-inference.huggingface.co/models/{model_name}"
            
            headers = {
                "Authorization": "Bearer YOUR_HF_TOKEN_HERE",  # شما باید توکن خود را قرار دهید
                "Content-Type": "application/json"
            }
            
            payload = {
                "inputs": text,
                "parameters": {
                    "return_all_scores": True
                }
            }
            
            response = self.session.post(url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"API Error: {response.status_code} - {response.text}")
                return {"error": f"API Error: {response.status_code}"}
                
        except Exception as e:
            self.logger.error(f"Error in sentiment analysis: {str(e)}")
            return {"error": str(e)}
    
    def use_fingpt_forecaster(self, 
                            price_data: pd.DataFrame, 
                            symbol: str) -> Dict[str, Any]:
        """
        استفاده از مدل FinGPT برای پیش‌بینی قیمت
        Use FinGPT model for price forecasting
        """
        try:
            # این یک مثال مفهومی است - API واقعی ممکن است متفاوت باشد
            # This is a conceptual example - actual API might be different
            
            # آماده‌سازی داده‌ها
            latest_prices = price_data.tail(5)['close'].tolist()
            
            # ساخت prompt برای مدل
            prompt = f"""
            Based on the recent price data for {symbol}:
            Last 5 closing prices: {latest_prices}
            
            Please predict the next price movement (up/down) and provide reasoning.
            """
            
            # در صورت دسترسی به API، درخواست ارسال می‌شود
            # If API is available, request would be sent here
            
            # شبیه‌سازی پاسخ
            simulated_response = {
                "prediction": "up" if latest_prices[-1] > latest_prices[-2] else "down",
                "confidence": 0.75,
                "reasoning": f"Based on recent trend analysis for {symbol}",
                "suggested_action": "buy" if latest_prices[-1] > latest_prices[-2] else "sell"
            }
            
            return simulated_response
            
        except Exception as e:
            self.logger.error(f"Error in FinGPT forecasting: {str(e)}")
            return {"error": str(e)}
    
    def use_alpha_vantage_api(self, symbol: str, api_key: str) -> Dict[str, Any]:
        """
        استفاده از Alpha Vantage API برای داده‌های مالی
        Use Alpha Vantage API for financial data
        """
        try:
            url = "https://www.alphavantage.co/query"
            
            params = {
                "function": "TIME_SERIES_DAILY",
                "symbol": symbol,
                "apikey": api_key,
                "outputsize": "compact"
            }
            
            response = self.session.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if "Time Series (Daily)" in data:
                    # تبدیل به DataFrame
                    time_series = data["Time Series (Daily)"]
                    df = pd.DataFrame.from_dict(time_series, orient='index')
                    df.columns = ['open', 'high', 'low', 'close', 'volume']
                    df.index = pd.to_datetime(df.index)
                    df = df.astype(float)
                    
                    return {
                        "success": True,
                        "data": df.to_dict(),
                        "meta_data": data.get("Meta Data", {})
                    }
                else:
                    return {"error": "No time series data found in response"}
            else:
                return {"error": f"API Error: {response.status_code}"}
                
        except Exception as e:
            self.logger.error(f"Error in Alpha Vantage API: {str(e)}")
            return {"error": str(e)}
    
    def use_yahoo_finance_api(self, symbol: str) -> Dict[str, Any]:
        """
        استفاده از Yahoo Finance API (غیررسمی)
        Use Yahoo Finance API (unofficial)
        """
        try:
            # استفاده از yfinance library که API Yahoo Finance را استفاده می‌کند
            # Using yfinance library which uses Yahoo Finance API
            
            url = f"https://query1.finance.yahoo.com/v8/finance/chart/{symbol}"
            
            params = {
                "period1": int((datetime.now() - timedelta(days=30)).timestamp()),
                "period2": int(datetime.now().timestamp()),
                "interval": "1d",
                "includePrePost": "true"
            }
            
            response = self.session.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if "chart" in data and "result" in data["chart"]:
                    result = data["chart"]["result"][0]
                    
                    timestamps = result["timestamp"]
                    prices = result["indicators"]["quote"][0]
                    
                    # ساخت DataFrame
                    df = pd.DataFrame({
                        'timestamp': [datetime.fromtimestamp(ts) for ts in timestamps],
                        'open': prices["open"],
                        'high': prices["high"],
                        'low': prices["low"],
                        'close': prices["close"],
                        'volume': prices["volume"]
                    })
                    
                    return {
                        "success": True,
                        "data": df.to_dict(),
                        "symbol": symbol
                    }
                else:
                    return {"error": "No chart data found in response"}
            else:
                return {"error": f"API Error: {response.status_code}"}
                
        except Exception as e:
            self.logger.error(f"Error in Yahoo Finance API: {str(e)}")
            return {"error": str(e)}

class FinancialAnalysisIntegration:
    """
    کلاس برای ترکیب تحلیل‌های مختلف مالی
    Class for combining different financial analyses
    """
    
    def __init__(self):
        self.api_client = FinancialModelAPI()
        self.logger = logging.getLogger(__name__)
    
    def comprehensive_analysis(self, 
                             symbol: str, 
                             news_text: str = None,
                             alpha_vantage_key: str = None) -> Dict[str, Any]:
        """
        تحلیل جامع با استفاده از چندین منبع
        Comprehensive analysis using multiple sources
        """
        try:
            results = {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "analyses": {}
            }
            
            # 1. دریافت داده‌های قیمت
            price_data = None
            if alpha_vantage_key:
                price_result = self.api_client.use_alpha_vantage_api(symbol, alpha_vantage_key)
                if price_result.get("success"):
                    price_data = pd.DataFrame.from_dict(price_result["data"])
                    results["analyses"]["price_data"] = "Successfully retrieved from Alpha Vantage"
            
            # اگر Alpha Vantage در دسترس نبود، از Yahoo Finance استفاده کن
            if price_data is None:
                yahoo_result = self.api_client.use_yahoo_finance_api(symbol)
                if yahoo_result.get("success"):
                    price_data = pd.DataFrame.from_dict(yahoo_result["data"])
                    results["analyses"]["price_data"] = "Successfully retrieved from Yahoo Finance"
            
            # 2. تحلیل احساسات (در صورت وجود متن خبری)
            if news_text:
                sentiment_result = self.api_client.use_huggingface_financial_sentiment(news_text)
                results["analyses"]["sentiment"] = sentiment_result
            
            # 3. پیش‌بینی قیمت (در صورت وجود داده‌های قیمت)
            if price_data is not None and not price_data.empty:
                forecast_result = self.api_client.use_fingpt_forecaster(price_data, symbol)
                results["analyses"]["forecast"] = forecast_result
                
                # محاسبه شاخص‌های تکنیکال ساده
                technical_analysis = self.calculate_technical_indicators(price_data)
                results["analyses"]["technical"] = technical_analysis
            
            # 4. ترکیب نتایج و ارائه توصیه
            combined_recommendation = self.generate_combined_recommendation(results["analyses"])
            results["recommendation"] = combined_recommendation
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive analysis: {str(e)}")
            return {"error": str(e)}
    
    def calculate_technical_indicators(self, price_data: pd.DataFrame) -> Dict[str, Any]:
        """
        محاسبه شاخص‌های تکنیکال ساده
        Calculate simple technical indicators
        """
        try:
            if 'close' not in price_data.columns:
                return {"error": "Close price data not available"}
            
            close_prices = pd.to_numeric(price_data['close'], errors='coerce').dropna()
            
            if len(close_prices) < 20:
                return {"error": "Insufficient data for technical analysis"}
            
            # محاسبه میانگین متحرک
            sma_10 = close_prices.rolling(window=10).mean().iloc[-1]
            sma_20 = close_prices.rolling(window=20).mean().iloc[-1]
            
            # محاسبه RSI ساده
            delta = close_prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            current_price = close_prices.iloc[-1]
            
            indicators = {
                "current_price": float(current_price),
                "sma_10": float(sma_10),
                "sma_20": float(sma_20),
                "rsi": float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else None,
                "trend": "bullish" if current_price > sma_20 else "bearish",
                "momentum": "overbought" if rsi.iloc[-1] > 70 else "oversold" if rsi.iloc[-1] < 30 else "neutral"
            }
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {str(e)}")
            return {"error": str(e)}
    
    def generate_combined_recommendation(self, analyses: Dict[str, Any]) -> Dict[str, Any]:
        """
        تولید توصیه ترکیبی بر اساس تحلیل‌های مختلف
        Generate combined recommendation based on various analyses
        """
        try:
            recommendation = {
                "action": "hold",
                "confidence": 0.5,
                "reasoning": [],
                "risk_level": "medium"
            }
            
            signals = []
            
            # تحلیل احساسات
            if "sentiment" in analyses and not analyses["sentiment"].get("error"):
                sentiment_data = analyses["sentiment"]
                if isinstance(sentiment_data, list) and len(sentiment_data) > 0:
                    # فرض کنیم اولین نتیجه مربوط به sentiment مثبت است
                    positive_score = sentiment_data[0].get("score", 0.5)
                    if positive_score > 0.6:
                        signals.append("bullish_sentiment")
                        recommendation["reasoning"].append("Positive market sentiment detected")
                    elif positive_score < 0.4:
                        signals.append("bearish_sentiment")
                        recommendation["reasoning"].append("Negative market sentiment detected")
            
            # تحلیل تکنیکال
            if "technical" in analyses and not analyses["technical"].get("error"):
                technical = analyses["technical"]
                
                if technical.get("trend") == "bullish":
                    signals.append("bullish_technical")
                    recommendation["reasoning"].append("Technical indicators show bullish trend")
                elif technical.get("trend") == "bearish":
                    signals.append("bearish_technical")
                    recommendation["reasoning"].append("Technical indicators show bearish trend")
                
                rsi = technical.get("rsi")
                if rsi and rsi > 70:
                    signals.append("overbought")
                    recommendation["reasoning"].append("RSI indicates overbought conditions")
                elif rsi and rsi < 30:
                    signals.append("oversold")
                    recommendation["reasoning"].append("RSI indicates oversold conditions")
            
            # پیش‌بینی مدل
            if "forecast" in analyses and not analyses["forecast"].get("error"):
                forecast = analyses["forecast"]
                
                if forecast.get("prediction") == "up":
                    signals.append("bullish_forecast")
                    recommendation["reasoning"].append("AI model predicts price increase")
                elif forecast.get("prediction") == "down":
                    signals.append("bearish_forecast")
                    recommendation["reasoning"].append("AI model predicts price decrease")
            
            # تعیین اقدام نهایی
            bullish_signals = sum(1 for s in signals if "bullish" in s)
            bearish_signals = sum(1 for s in signals if "bearish" in s)
            
            if bullish_signals > bearish_signals:
                recommendation["action"] = "buy"
                recommendation["confidence"] = min(0.9, 0.5 + (bullish_signals * 0.1))
            elif bearish_signals > bullish_signals:
                recommendation["action"] = "sell"
                recommendation["confidence"] = min(0.9, 0.5 + (bearish_signals * 0.1))
            else:
                recommendation["action"] = "hold"
                recommendation["confidence"] = 0.5
            
            # تعیین سطح ریسک
            if "overbought" in signals or "oversold" in signals:
                recommendation["risk_level"] = "high"
            elif len(signals) >= 2:
                recommendation["risk_level"] = "medium"
            else:
                recommendation["risk_level"] = "low"
            
            return recommendation
            
        except Exception as e:
            self.logger.error(f"Error generating combined recommendation: {str(e)}")
            return {"error": str(e)}

def main_example():
    """
    مثال اصلی برای استفاده از سیستم
    Main example for using the system
    """
    
    # ایجاد نمونه تحلیل‌گر
    analyzer = FinancialAnalysisIntegration()
    
    # مثال تحلیل جامع
    symbol = "AAPL"
    news_text = "Apple reported strong quarterly earnings with revenue growth of 15%"
    
    # در صورت داشتن کلید Alpha Vantage
    # alpha_vantage_key = "YOUR_ALPHA_VANTAGE_KEY"
    alpha_vantage_key = None
    
    print(f"Starting comprehensive analysis for {symbol}...")
    
    try:
        results = analyzer.comprehensive_analysis(
            symbol=symbol,
            news_text=news_text,
            alpha_vantage_key=alpha_vantage_key
        )
        
        print("\n=== Analysis Results ===")
        print(json.dumps(results, indent=2, default=str))
        
        if "recommendation" in results:
            rec = results["recommendation"]
            print(f"\n=== Final Recommendation ===")
            print(f"Action: {rec['action'].upper()}")
            print(f"Confidence: {rec['confidence']:.1%}")
            print(f"Risk Level: {rec['risk_level'].upper()}")
            print(f"Reasoning: {', '.join(rec['reasoning'])}")
        
    except Exception as e:
        print(f"Error in analysis: {str(e)}")

if __name__ == "__main__":
    # راه‌اندازی logging
    logging.basicConfig(level=logging.INFO)
    
    print("Financial Model API Integration Example")
    print("=" * 50)
    
    # اجرای مثال
    main_example()
    
    print("\n" + "=" * 50)
    print("برای استفاده کامل:")
    print("1. توکن Hugging Face را دریافت کنید")
    print("2. کلید Alpha Vantage را دریافت کنید (اختیاری)")
    print("3. کتابخانه‌های مورد نیاز را نصب کنید")
    print("4. تنظیمات پروکسی را بررسی کنید") 