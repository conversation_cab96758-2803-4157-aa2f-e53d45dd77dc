#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 Smart Portfolio Manager
مدیر پرتفولیو هوشمند با هدف‌گذاری سود و مدیریت ریسک خودکار
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import asyncio
import threading
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from portfolio.advanced_risk_manager import AdvancedRiskManager, RiskParameters, RiskLevel
from core.base import TradingSignal

@dataclass
class ProfitTarget:
    """هدف سود"""
    target_type: str  # "daily", "weekly", "monthly"
    target_amount: float
    current_progress: float
    achieved: bool = False
    achievement_date: Optional[datetime] = None

@dataclass
class TradingOpportunity:
    """فرصت معاملاتی"""
    symbol: str
    signal: TradingSignal
    risk_reward_ratio: float
    position_size: float
    expected_profit: float
    confidence_score: float
    priority: int  # 1 = highest, 5 = lowest

class SmartPortfolioManager:
    """مدیر پرتفولیو هوشمند"""
    
    def __init__(self, risk_params: RiskParameters = None):
        self.risk_manager = AdvancedRiskManager(risk_params)
        self.risk_params = risk_params or RiskParameters()
        
        # هدف‌های سود
        self.profit_targets = {
            "daily": ProfitTarget("daily", self.risk_params.daily_profit_target_per_symbol, 0.0),
            "weekly": ProfitTarget("weekly", self.risk_params.weekly_profit_target, 0.0),
            "monthly": ProfitTarget("monthly", self.risk_params.monthly_profit_target, 0.0)
        }
        
        # تنظیمات هوشمند
        self.auto_trading_enabled = True
        self.aggressive_mode = False  # حالت پرخطر برای رسیدن به هدف
        self.signals_queue: List[TradingSignal] = []
        self.opportunities_queue: List[TradingOpportunity] = []
        
        # تاریخچه عملکرد
        self.performance_history = {
            "daily": [],
            "weekly": [],
            "monthly": []
        }
        
        # آمار تحلیلی
        self.analysis_stats = {
            "total_signals_received": 0,
            "signals_acted_on": 0,
            "rejected_signals": 0,
            "avg_signal_confidence": 0.0,
            "best_performing_symbol": None,
            "worst_performing_symbol": None,
            "success_rate_by_symbol": {}
        }
        
        self.last_analysis_time = datetime.now()
        self.running = False
        
    def add_trading_signal(self, signal: TradingSignal) -> bool:
        """افزودن سیگنال معاملاتی"""
        try:
            self.signals_queue.append(signal)
            self.analysis_stats["total_signals_received"] += 1
            
            # تحلیل فوری سیگنال
            opportunity = self._analyze_signal(signal)
            if opportunity:
                self.opportunities_queue.append(opportunity)
                self.opportunities_queue.sort(key=lambda x: x.priority)
                
                print(f"📊 New trading opportunity: {signal.symbol}")
                print(f"   Action: {signal.action}")
                print(f"   Confidence: {signal.confidence:.2f}")
                print(f"   Priority: {opportunity.priority}")
                print(f"   Expected Profit: ${opportunity.expected_profit:.2f}")
                
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ Error adding signal: {e}")
            return False
    
    def _analyze_signal(self, signal: TradingSignal) -> Optional[TradingOpportunity]:
        """تحلیل سیگنال و ایجاد فرصت معاملاتی"""
        
        # بررسی کیفیت سیگنال
        if signal.confidence < 0.6:
            print(f"⚠️ Signal confidence too low: {signal.confidence:.2f}")
            return None
        
        # بررسی امکان باز کردن موقعیت
        can_open, reason = self.risk_manager.can_open_position(signal.symbol, signal.price, signal.action)
        if not can_open:
            print(f"❌ Cannot open position: {reason}")
            self.analysis_stats["rejected_signals"] += 1
            return None
        
        # محاسبه stop loss و take profit
        stop_loss, take_profit = self.risk_manager.calculate_stop_loss_take_profit(
            signal.symbol, signal.price, signal.action
        )
        
        # محاسبه اندازه موقعیت
        position_size, risk_amount = self.risk_manager.calculate_position_size(
            signal.symbol, signal.price, stop_loss, signal.action
        )
        
        if position_size == 0:
            print(f"❌ Position size is zero for {signal.symbol}")
            return None
        
        # محاسبه سود مورد انتظار
        if signal.action == "buy":
            expected_profit = position_size * (take_profit - signal.price)
        else:
            expected_profit = position_size * (signal.price - take_profit)
        
        # محاسبه risk/reward ratio
        risk_reward_ratio = expected_profit / risk_amount if risk_amount > 0 else 0
        
        # تعیین اولویت
        priority = self._calculate_priority(signal, risk_reward_ratio, expected_profit)
        
        # ایجاد فرصت معاملاتی
        opportunity = TradingOpportunity(
            symbol=signal.symbol,
            signal=signal,
            risk_reward_ratio=risk_reward_ratio,
            position_size=position_size,
            expected_profit=expected_profit,
            confidence_score=signal.confidence,
            priority=priority
        )
        
        return opportunity
    
    def _calculate_priority(self, signal: TradingSignal, risk_reward_ratio: float, expected_profit: float) -> int:
        """محاسبه اولویت فرصت معاملاتی"""
        score = 0
        
        # بر اساس confidence
        if signal.confidence >= 0.9:
            score += 30
        elif signal.confidence >= 0.8:
            score += 20
        elif signal.confidence >= 0.7:
            score += 10
        
        # بر اساس risk/reward ratio
        if risk_reward_ratio >= 3.0:
            score += 25
        elif risk_reward_ratio >= 2.0:
            score += 15
        elif risk_reward_ratio >= 1.5:
            score += 10
        
        # بر اساس سود مورد انتظار
        if expected_profit >= 20:
            score += 20
        elif expected_profit >= 10:
            score += 15
        elif expected_profit >= 5:
            score += 10
        
        # بر اساس عملکرد قبلی نماد
        symbol_performance = self.analysis_stats["success_rate_by_symbol"].get(signal.symbol, 0.5)
        if symbol_performance >= 0.8:
            score += 15
        elif symbol_performance >= 0.6:
            score += 10
        
        # تبدیل score به priority (1 = highest, 5 = lowest)
        if score >= 70:
            return 1
        elif score >= 50:
            return 2
        elif score >= 30:
            return 3
        elif score >= 15:
            return 4
        else:
            return 5
    
    def execute_best_opportunity(self) -> bool:
        """اجرای بهترین فرصت معاملاتی"""
        
        if not self.opportunities_queue:
            return False
        
        # انتخاب بهترین فرصت
        best_opportunity = self.opportunities_queue[0]
        
        # بررسی مجدد امکان باز کردن موقعیت
        can_open, reason = self.risk_manager.can_open_position(
            best_opportunity.symbol, 
            best_opportunity.signal.price, 
            best_opportunity.signal.action
        )
        
        if not can_open:
            print(f"❌ Cannot execute opportunity: {reason}")
            self.opportunities_queue.remove(best_opportunity)
            return False
        
        # باز کردن موقعیت
        success = self.risk_manager.open_position(
            best_opportunity.symbol,
            best_opportunity.signal.price,
            best_opportunity.signal.action
        )
        
        if success:
            self.analysis_stats["signals_acted_on"] += 1
            self.opportunities_queue.remove(best_opportunity)
            
            # بروزرسانی آمار
            self._update_performance_stats(best_opportunity)
            
            print(f"✅ Executed opportunity for {best_opportunity.symbol}")
            return True
        
        return False
    
    def _update_performance_stats(self, opportunity: TradingOpportunity):
        """بروزرسانی آمار عملکرد"""
        symbol = opportunity.symbol
        
        # بروزرسانی میانگین confidence
        total_signals = self.analysis_stats["total_signals_received"]
        current_avg = self.analysis_stats["avg_signal_confidence"]
        new_avg = ((current_avg * (total_signals - 1)) + opportunity.confidence_score) / total_signals
        self.analysis_stats["avg_signal_confidence"] = new_avg
        
        # آماده‌سازی برای tracking موفقیت نماد
        if symbol not in self.analysis_stats["success_rate_by_symbol"]:
            self.analysis_stats["success_rate_by_symbol"][symbol] = 0.5  # شروع از 50%
    
    def check_profit_targets(self) -> Dict[str, bool]:
        """بررسی دستیابی به اهداف سود"""
        results = {}
        
        portfolio_status = self.risk_manager.get_portfolio_status()
        
        # بررسی هدف روزانه
        daily_pnl = portfolio_status["capital"]["daily_pnl"]
        if daily_pnl >= self.profit_targets["daily"].target_amount:
            if not self.profit_targets["daily"].achieved:
                self.profit_targets["daily"].achieved = True
                self.profit_targets["daily"].achievement_date = datetime.now()
                print(f"🎯 Daily profit target achieved: ${daily_pnl:.2f}")
        
        self.profit_targets["daily"].current_progress = daily_pnl
        results["daily"] = self.profit_targets["daily"].achieved
        
        # بررسی هدف هفتگی
        weekly_pnl = portfolio_status["capital"]["weekly_pnl"]
        if weekly_pnl >= self.profit_targets["weekly"].target_amount:
            if not self.profit_targets["weekly"].achieved:
                self.profit_targets["weekly"].achieved = True
                self.profit_targets["weekly"].achievement_date = datetime.now()
                print(f"🎯 Weekly profit target achieved: ${weekly_pnl:.2f}")
        
        self.profit_targets["weekly"].current_progress = weekly_pnl
        results["weekly"] = self.profit_targets["weekly"].achieved
        
        # بررسی هدف ماهانه
        monthly_pnl = portfolio_status["capital"]["monthly_pnl"]
        if monthly_pnl >= self.profit_targets["monthly"].target_amount:
            if not self.profit_targets["monthly"].achieved:
                self.profit_targets["monthly"].achieved = True
                self.profit_targets["monthly"].achievement_date = datetime.now()
                print(f"🎯 Monthly profit target achieved: ${monthly_pnl:.2f}")
        
        self.profit_targets["monthly"].current_progress = monthly_pnl
        results["monthly"] = self.profit_targets["monthly"].achieved
        
        return results
    
    def adjust_trading_strategy(self):
        """تنظیم استراتژی معاملاتی بر اساس عملکرد"""
        
        portfolio_status = self.risk_manager.get_portfolio_status()
        risk_level = self.risk_manager.get_risk_level()
        
        # اگر در سطح ریسک بالا هستیم
        if risk_level in [RiskLevel.HIGH, RiskLevel.EXTREME]:
            self.aggressive_mode = False
            print("⚠️ Switching to conservative mode due to high risk level")
        
        # اگر به هدف روزانه نزدیک هستیم
        daily_progress = self.profit_targets["daily"].current_progress
        daily_target = self.profit_targets["daily"].target_amount
        
        if daily_progress >= daily_target * 0.8:  # 80% از هدف
            self.aggressive_mode = False
            print("📊 Reducing aggressiveness - close to daily target")
        
        # اگر خیلی عقب هستیم
        if daily_progress < daily_target * 0.3:  # کمتر از 30% هدف
            current_hour = datetime.now().hour
            if current_hour > 18:  # بعد از 6 بعدازظهر
                self.aggressive_mode = True
                print("🚀 Switching to aggressive mode - behind on daily target")
    
    def monitor_positions(self):
        """نظارت بر موقعیت‌های باز"""
        
        for symbol, position in self.risk_manager.positions.items():
            # در اینجا باید قیمت فعلی را بررسی کنیم
            # برای سادگی، فرض می‌کنیم قیمت تغییر نکرده
            current_price = position.entry_price
            
            # بررسی stop loss
            if position.side == "buy" and current_price <= position.stop_loss:
                print(f"🛑 Stop loss triggered for {symbol}")
                self.risk_manager.close_position(symbol, current_price, "stop_loss")
            elif position.side == "sell" and current_price >= position.stop_loss:
                print(f"🛑 Stop loss triggered for {symbol}")
                self.risk_manager.close_position(symbol, current_price, "stop_loss")
            
            # بررسی take profit
            if position.side == "buy" and current_price >= position.take_profit:
                print(f"🎯 Take profit triggered for {symbol}")
                self.risk_manager.close_position(symbol, current_price, "take_profit")
            elif position.side == "sell" and current_price <= position.take_profit:
                print(f"🎯 Take profit triggered for {symbol}")
                self.risk_manager.close_position(symbol, current_price, "take_profit")
    
    def auto_trading_loop(self):
        """حلقه معاملات خودکار"""
        
        while self.running:
            try:
                # بررسی اهداف سود
                self.check_profit_targets()
                
                # تنظیم استراتژی
                self.adjust_trading_strategy()
                
                # نظارت بر موقعیت‌ها
                self.monitor_positions()
                
                # اجرای فرصت‌های معاملاتی
                if self.auto_trading_enabled:
                    self.execute_best_opportunity()
                
                # بازنشانی روزانه
                self.risk_manager.update_daily_reset()
                
                # استراحت
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ Error in auto trading loop: {e}")
                time.sleep(5)
    
    def start_auto_trading(self):
        """شروع معاملات خودکار"""
        if not self.running:
            self.running = True
            self.auto_trading_thread = threading.Thread(target=self.auto_trading_loop)
            self.auto_trading_thread.daemon = True
            self.auto_trading_thread.start()
            print("🚀 Auto trading started")
    
    def stop_auto_trading(self):
        """توقف معاملات خودکار"""
        self.running = False
        print("⏹️ Auto trading stopped")
    
    def get_comprehensive_report(self) -> Dict:
        """گزارش جامع پرتفولیو"""
        
        portfolio_status = self.risk_manager.get_portfolio_status()
        
        return {
            "portfolio_status": portfolio_status,
            "profit_targets": {
                "daily": {
                    "target": self.profit_targets["daily"].target_amount,
                    "current": self.profit_targets["daily"].current_progress,
                    "achieved": self.profit_targets["daily"].achieved,
                    "progress_percent": (self.profit_targets["daily"].current_progress / 
                                       self.profit_targets["daily"].target_amount * 100)
                },
                "weekly": {
                    "target": self.profit_targets["weekly"].target_amount,
                    "current": self.profit_targets["weekly"].current_progress,
                    "achieved": self.profit_targets["weekly"].achieved,
                    "progress_percent": (self.profit_targets["weekly"].current_progress / 
                                       self.profit_targets["weekly"].target_amount * 100)
                },
                "monthly": {
                    "target": self.profit_targets["monthly"].target_amount,
                    "current": self.profit_targets["monthly"].current_progress,
                    "achieved": self.profit_targets["monthly"].achieved,
                    "progress_percent": (self.profit_targets["monthly"].current_progress / 
                                       self.profit_targets["monthly"].target_amount * 100)
                }
            },
            "trading_stats": {
                "signals_received": self.analysis_stats["total_signals_received"],
                "signals_acted_on": self.analysis_stats["signals_acted_on"],
                "signals_rejected": self.analysis_stats["rejected_signals"],
                "action_rate": (self.analysis_stats["signals_acted_on"] / 
                              max(self.analysis_stats["total_signals_received"], 1) * 100),
                "avg_signal_confidence": self.analysis_stats["avg_signal_confidence"],
                "opportunities_in_queue": len(self.opportunities_queue)
            },
            "system_status": {
                "auto_trading_enabled": self.auto_trading_enabled,
                "aggressive_mode": self.aggressive_mode,
                "running": self.running,
                "risk_level": self.risk_manager.get_risk_level().value
            },
            "timestamp": datetime.now()
        }
    
    def save_comprehensive_state(self, filename: str = "smart_portfolio_state.json"):
        """ذخیره وضعیت کامل"""
        
        # ذخیره risk manager
        self.risk_manager.save_state("risk_manager_state.json")
        
        # ذخیره portfolio manager
        portfolio_state = {
            "profit_targets": {
                "daily": {
                    "target_amount": self.profit_targets["daily"].target_amount,
                    "current_progress": self.profit_targets["daily"].current_progress,
                    "achieved": self.profit_targets["daily"].achieved,
                    "achievement_date": self.profit_targets["daily"].achievement_date.isoformat() if self.profit_targets["daily"].achievement_date else None
                },
                "weekly": {
                    "target_amount": self.profit_targets["weekly"].target_amount,
                    "current_progress": self.profit_targets["weekly"].current_progress,
                    "achieved": self.profit_targets["weekly"].achieved,
                    "achievement_date": self.profit_targets["weekly"].achievement_date.isoformat() if self.profit_targets["weekly"].achievement_date else None
                },
                "monthly": {
                    "target_amount": self.profit_targets["monthly"].target_amount,
                    "current_progress": self.profit_targets["monthly"].current_progress,
                    "achieved": self.profit_targets["monthly"].achieved,
                    "achievement_date": self.profit_targets["monthly"].achievement_date.isoformat() if self.profit_targets["monthly"].achievement_date else None
                }
            },
            "analysis_stats": self.analysis_stats,
            "system_settings": {
                "auto_trading_enabled": self.auto_trading_enabled,
                "aggressive_mode": self.aggressive_mode
            },
            "timestamp": datetime.now().isoformat()
        }
        
        with open(filename, 'w') as f:
            json.dump(portfolio_state, f, indent=2, default=str)
        
        print(f"✅ Portfolio state saved to {filename}")

# مثال استفاده
if __name__ == "__main__":
    # تنظیمات ریسک بر اساس درخواست کاربر
    risk_params = RiskParameters(
        initial_capital=1000.0,
        max_drawdown_percent=10.0,
        daily_loss_limit_percent=4.0,
        daily_profit_target_per_symbol=5.0,
        weekly_profit_target=30.0,
        monthly_profit_target=80.0
    )
    
    # ایجاد مدیر پرتفولیو هوشمند
    portfolio_manager = SmartPortfolioManager(risk_params)
    
    print("🧠 SMART PORTFOLIO MANAGER INITIALIZED")
    print("=" * 50)
    
    # نمایش اهداف
    print(f"🎯 Profit Targets:")
    print(f"   Daily: ${risk_params.daily_profit_target_per_symbol} per symbol")
    print(f"   Weekly: ${risk_params.weekly_profit_target}")
    print(f"   Monthly: ${risk_params.monthly_profit_target}")
    
    # تست سیگنال
    test_signal = TradingSignal(
        symbol="EURUSD",
        action="buy",
        confidence=0.85,
        price=1.0850,
        timestamp=datetime.now(),
        reasoning="Strong bullish signal"
    )
    
    print(f"\n📊 Testing signal processing...")
    success = portfolio_manager.add_trading_signal(test_signal)
    print(f"   Signal processed: {success}")
    
    # اجرای فرصت
    print(f"\n🚀 Executing best opportunity...")
    executed = portfolio_manager.execute_best_opportunity()
    print(f"   Opportunity executed: {executed}")
    
    # گزارش جامع
    print(f"\n📋 Comprehensive Report:")
    report = portfolio_manager.get_comprehensive_report()
    print(f"   Current Capital: ${report['portfolio_status']['capital']['current']:.2f}")
    print(f"   Daily Progress: ${report['profit_targets']['daily']['current']:.2f} / ${report['profit_targets']['daily']['target']:.2f}")
    print(f"   Signals Received: {report['trading_stats']['signals_received']}")
    print(f"   Action Rate: {report['trading_stats']['action_rate']:.1f}%")
    print(f"   Risk Level: {report['system_status']['risk_level'].upper()}")
    
    # ذخیره وضعیت
    portfolio_manager.save_comprehensive_state("portfolio_demo.json")
    
    print(f"\n🎯 Portfolio Manager ready for automated trading!")
    print(f"   Use start_auto_trading() to begin automated execution")
    print(f"   Use get_comprehensive_report() for live monitoring") 