"""
Genetic Strategy Evolution System for Adaptive Plutus
Provides evolutionary algorithm for strategy optimization
"""

import logging
from typing import Dict, Any, List
from datetime import datetime
import random
import numpy as np


class GeneticStrategyEvolution:
    """Genetic algorithm for strategy evolution and optimization"""
    
    def __init__(self, population_size: int = 50, mutation_rate: float = 0.1):
        self.logger = logging.getLogger(__name__)
        self.population_size = population_size
        self.mutation_rate = mutation_rate
        self.population = []
        self.generation = 0
        self.best_strategies = []
        
    async def initialize(self):
        """Initialize the genetic evolution system"""
        try:
            self.logger.info("🧬 Genetic Strategy Evolution initializing...")
            self.population = self._create_initial_population()
            self.generation = 0
            self.best_strategies = []
            self.logger.info("✅ Genetic Strategy Evolution initialized")
            return True
        except Exception as e:
            self.logger.error(f"❌ Genetic evolution initialization failed: {e}")
            return False
    
    def _create_initial_population(self) -> List[Dict[str, Any]]:
        """Create initial population of strategies"""
        population = []
        for i in range(self.population_size):
            strategy = {
                'id': f'strategy_{i}',
                'parameters': self._generate_random_parameters(),
                'fitness': 0.0,
                'generation': 0
            }
            population.append(strategy)
        return population
    
    def _generate_random_parameters(self) -> Dict[str, Any]:
        """Generate random strategy parameters"""
        return {
            'ma_short': random.randint(5, 20),
            'ma_long': random.randint(21, 50),
            'rsi_period': random.randint(10, 30),
            'rsi_overbought': random.uniform(60, 80),
            'rsi_oversold': random.uniform(20, 40),
            'stop_loss': random.uniform(0.01, 0.05),
            'take_profit': random.uniform(0.02, 0.10)
        }
    
    async def evolve_strategies(self, fitness_scores: Dict[str, float]):
        """Evolve strategies based on fitness scores"""
        try:
            self.logger.info(f"🔄 Evolving strategies for generation {self.generation}")
            
            # Update fitness scores
            for strategy in self.population:
                if strategy['id'] in fitness_scores:
                    strategy['fitness'] = fitness_scores[strategy['id']]
            
            # Sort by fitness
            self.population.sort(key=lambda x: x['fitness'], reverse=True)
            
            # Keep best strategies
            self.best_strategies = self.population[:10].copy()
            
            # Create new population
            new_population = []
            for i in range(self.population_size):
                if i < len(self.best_strategies):
                    # Elite strategies
                    new_strategy = self.best_strategies[i].copy()
                else:
                    # Crossover and mutation
                    parent1 = self._select_parent()
                    parent2 = self._select_parent()
                    new_strategy = self._crossover(parent1, parent2)
                    new_strategy = self._mutate(new_strategy)
                
                new_strategy['generation'] = self.generation + 1
                new_population.append(new_strategy)
            
            self.population = new_population
            self.generation += 1
            
            self.logger.info(f"✅ Evolution complete. Generation {self.generation}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Strategy evolution failed: {e}")
            return False
    
    def _select_parent(self) -> Dict[str, Any]:
        """Select parent using tournament selection"""
        tournament_size = 3
        tournament = random.sample(self.population, tournament_size)
        return max(tournament, key=lambda x: x['fitness'])
    
    def _crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any]) -> Dict[str, Any]:
        """Perform crossover between two parents"""
        child = {
            'id': f'strategy_gen{self.generation}_{random.randint(1000, 9999)}',
            'parameters': {},
            'fitness': 0.0,
            'generation': self.generation
        }
        
        # Crossover parameters
        for param in parent1['parameters']:
            if random.random() < 0.5:
                child['parameters'][param] = parent1['parameters'][param]
            else:
                child['parameters'][param] = parent2['parameters'][param]
        
        return child
    
    def _mutate(self, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Mutate strategy parameters"""
        mutated_strategy = strategy.copy()
        
        for param, value in strategy['parameters'].items():
            if random.random() < self.mutation_rate:
                if isinstance(value, int):
                    mutated_strategy['parameters'][param] = max(1, value + random.randint(-2, 2))
                elif isinstance(value, float):
                    mutated_strategy['parameters'][param] = max(0.001, value * random.uniform(0.8, 1.2))
        
        return mutated_strategy
    
    async def get_best_strategies(self, count: int = 5) -> List[Dict[str, Any]]:
        """Get the best strategies from current population"""
        sorted_population = sorted(self.population, key=lambda x: x['fitness'], reverse=True)
        return sorted_population[:count]
    
    async def get_evolution_stats(self) -> Dict[str, Any]:
        """Get evolution statistics"""
        if not self.population:
            return {}
        
        fitness_scores = [s['fitness'] for s in self.population]
        return {
            'generation': self.generation,
            'population_size': len(self.population),
            'best_fitness': max(fitness_scores),
            'average_fitness': np.mean(fitness_scores),
            'worst_fitness': min(fitness_scores),
            'best_strategies_count': len(self.best_strategies)
        } 