#!/usr/bin/env python3
"""
تست ساده مدل‌های دانلود شده
"""

def test_distilbert():
    """تست DistilBERT"""
    print("🔍 Testing DistilBERT...")
    try:
        from transformers import AutoTokenizer, AutoModel
        
        tokenizer = AutoTokenizer.from_pretrained('distilbert-base-uncased')
        model = AutoModel.from_pretrained('distilbert-base-uncased')
        
        test_text = "Bitcoin price is rising today"
        inputs = tokenizer(test_text, return_tensors='pt')
        outputs = model(**inputs)
        
        print(f"   ✅ DistilBERT works! Output shape: {outputs.last_hidden_state.shape}")
        return True
    except Exception as e:
        print(f"   ❌ DistilBERT failed: {e}")
        return False

def test_finbert():
    """تست FinBERT"""
    print("🔍 Testing FinBERT...")
    try:
        from transformers import pipeline
        
        classifier = pipeline("sentiment-analysis", model="ProsusAI/finbert")
        
        test_text = "The company's earnings exceeded expectations"
        result = classifier(test_text)[0]
        
        print(f"   ✅ FinBERT works! Sentiment: {result['label']} ({result['score']:.2f})")
        return True
    except Exception as e:
        print(f"   ❌ FinBERT failed: {e}")
        return False

def test_bert_tiny():
    """تست BERT Tiny"""
    print("🔍 Testing BERT Tiny...")
    try:
        from transformers import AutoTokenizer, AutoModel
        
        tokenizer = AutoTokenizer.from_pretrained('prajjwal1/bert-tiny')
        model = AutoModel.from_pretrained('prajjwal1/bert-tiny')
        
        test_text = "Fast lightweight model test"
        inputs = tokenizer(test_text, return_tensors='pt')
        outputs = model(**inputs)
        
        print(f"   ✅ BERT Tiny works! Output shape: {outputs.last_hidden_state.shape}")
        return True
    except Exception as e:
        print(f"   ❌ BERT Tiny failed: {e}")
        return False

def main():
    print("🧪 Simple Model Testing")
    print("=" * 30)
    
    results = []
    
    # Test each model
    results.append(test_distilbert())
    results.append(test_finbert())
    results.append(test_bert_tiny())
    
    # Summary
    successful = sum(results)
    total = len(results)
    
    print(f"\n📊 Results: {successful}/{total} models working")
    
    if successful == total:
        print("🎉 All models are working perfectly!")
    else:
        print(f"⚠️ {total - successful} models need attention")

if __name__ == "__main__":
    main() 