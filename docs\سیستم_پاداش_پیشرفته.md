# مستند جامع: AdvancedRewardSystem

## مسئولیت
سیستم پاداش تطبیقی با حافظه drawdown، پاداش‌دهی بر اساس شرایط بازار و بازیابی.

## پارامترها
- memory_size: اندازه حافظه تجربیات
- learning_rate: نرخ یادگیری
- volatility_threshold: آستانه نوسان
- recovery_bonus_factor: ضریب پاداش بازیابی

## متدهای کلیدی
- detect_market_regime: تشخیص رژیم بازار
- calculate_dynamic_multiplier: محاسبه ضریب پاداش پویا
- get_multilevel_reward: پاداش چندسطحی
- update_state: بروزرسانی وضعیت سیستم

## نمونه کد
```python
from utils.advanced_reward_system import AdaptiveRewardSystem
ars = AdaptiveRewardSystem()
regime = ars.detect_market_regime(np.array(returns))
reward_adj = ars.calculate_dynamic_multiplier(0.05, regime, 0.02, 100)
```

## مدیریت خطا
در صورت نبود داده کافی، حالت پیش‌فرض یا SIDEWAYS برمی‌گرداند.

## بهترین شیوه
در هر اپیزود، وضعیت drawdown و رژیم بازار را بروزرسانی کنید.

## نمودار
- نمودار تغییرات ضریب پاداش و drawdown قابل ترسیم است.

## اتصال به اسکریپت اصلی
- این ماژول به صورت مستقیم در جریان اصلی معاملات یا unified_trading_system استفاده نشده و فقط در سطح تست یا توسعه وجود دارد.
- سیستم پاداش عملیاتی در پروژه با نام AdvancedRewardRedistributor در utils/reward_redistribution.py پیاده‌سازی و در api/realtime_dashboard.py استفاده شده است. 