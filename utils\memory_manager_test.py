#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 Memory Management System Test
تست سیستم مدیریت حافظه
"""

import os
import sys
import time
import gc
import threading
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_memory_management():
    """تست سیستم مدیریت حافظه"""
    print("🧠 Testing Advanced Memory Management System")
    print("=" * 50)
    
    try:
        # Import the memory manager
        from core.memory_manager import (
            AdvancedMemoryManager, 
            MemoryPoolType, 
            MemoryLevel,
            memory_context,
            memory_monitor
        )
        
        # Test 1: Basic Memory Manager
        print("\n1️⃣ Testing Basic Memory Manager...")
        memory_manager = AdvancedMemoryManager()
        
        # Get initial stats
        initial_stats = memory_manager.get_memory_stats()
        print(f"   ✓ Initial memory: {initial_stats.process_memory:.2f} MB")
        print(f"   ✓ Memory level: {initial_stats.memory_level.value}")
        print(f"   ✓ System memory: {initial_stats.memory_percent:.1f}%")
        
        # Test 2: Memory Pools
        print("\n2️⃣ Testing Memory Pools...")
        
        # Test buffer pool
        buffer_pool = memory_manager.get_pool(MemoryPoolType.BUFFERS)
        if buffer_pool:
            print(f"   ✓ Buffer pool created: {buffer_pool.pool_type.value}")
            
            # Test pool operations
            test_obj = [1, 2, 3, 4, 5]
            returned = buffer_pool.return_object(test_obj)
            print(f"   ✓ Object returned to pool: {returned}")
            
            retrieved = buffer_pool.get_object()
            print(f"   ✓ Object retrieved from pool: {retrieved is not None}")
            
            pool_stats = buffer_pool.get_stats()
            print(f"   ✓ Pool stats: {pool_stats}")
        
        # Test 3: Resource Monitoring
        print("\n3️⃣ Testing Resource Monitoring...")
        
        system_resources = memory_manager.get_system_resources()
        print(f"   ✓ System memory: {system_resources['memory']['system']['percent']:.1f}%")
        print(f"   ✓ Process memory: {system_resources['memory']['process']['rss']:.2f} MB")
        
        if 'cpu' in system_resources:
            print(f"   ✓ CPU usage: {system_resources['cpu']['percent']:.1f}%")
        
        # Test 4: Garbage Collection
        print("\n4️⃣ Testing Garbage Collection...")
        
        # Create some objects to collect
        large_data = []
        for i in range(1000):
            large_data.append([j for j in range(100)])
        
        # Force GC
        gc_result = memory_manager.force_gc()
        print(f"   ✓ GC completed: {gc_result['collected_objects']} objects collected")
        print(f"   ✓ Memory freed: {gc_result['memory_freed']:.2f} MB")
        
        # Clear large data
        large_data.clear()
        del large_data
        
        # Test 5: Memory Context
        print("\n5️⃣ Testing Memory Context...")
        
        @memory_monitor
        def memory_intensive_function():
            # Create temporary data
            temp_data = []
            for i in range(10000):
                temp_data.append(f"data_{i}")
            return len(temp_data)
        
        result = memory_intensive_function()
        print(f"   ✓ Context function result: {result}")
        
        # Test 6: Memory Pressure Detection
        print("\n6️⃣ Testing Memory Pressure Detection...")
        
        pressure_detected = memory_manager.check_memory_pressure()
        print(f"   ✓ Memory pressure detected: {pressure_detected}")
        
        # Test 7: Pool Statistics
        print("\n7️⃣ Testing Pool Statistics...")
        
        pool_stats = memory_manager.get_pool_stats()
        for pool_type, stats in pool_stats.items():
            print(f"   ✓ {pool_type}: {stats}")
        
        # Test 8: Export Report
        print("\n8️⃣ Testing Report Export...")
        
        try:
            report_file = memory_manager.export_memory_report()
            if report_file and os.path.exists(report_file):
                print(f"   ✓ Report exported: {report_file}")
                # Clean up
                os.remove(report_file)
                print(f"   ✓ Report file cleaned up")
            else:
                print(f"   ⚠️ Report export failed or file not found")
        except Exception as e:
            print(f"   ⚠️ Report export error: {e}")
        
        # Test 9: Recent Alerts
        print("\n9️⃣ Testing Recent Alerts...")
        
        recent_alerts = memory_manager.get_recent_alerts(hours=1)
        print(f"   ✓ Recent alerts: {len(recent_alerts)}")
        
        # Test 10: Memory Context Manager
        print("\n🔟 Testing Memory Context Manager...")
        
        with memory_context(MemoryPoolType.CACHE) as mm:
            test_data = [i**2 for i in range(5000)]
            current_stats = mm.get_memory_stats()
            print(f"   ✓ Memory in context: {current_stats.process_memory:.2f} MB")
        
        print(f"   ✓ Context completed successfully")
        
        # Final stats
        final_stats = memory_manager.get_memory_stats()
        print(f"\n📊 Final Stats:")
        print(f"   Memory usage: {final_stats.process_memory:.2f} MB")
        print(f"   Memory level: {final_stats.memory_level.value}")
        print(f"   Active objects: {final_stats.active_objects}")
        
        # Stop monitoring
        memory_manager.stop_monitoring()
        print(f"   ✓ Monitoring stopped")
        
        print("\n✅ All Memory Management tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_performance():
    """تست عملکرد حافظه"""
    print("\n🚀 Testing Memory Performance...")
    
    try:
        from core.memory_manager import memory_manager, MemoryPoolType
        
        # Performance test
        start_time = time.time()
        
        # Create and use memory pools
        cache_pool = memory_manager.get_pool(MemoryPoolType.CACHE)
        
        # Test multiple operations
        operations = 1000
        for i in range(operations):
            # Return object to pool
            test_obj = f"test_data_{i}"
            cache_pool.return_object(test_obj)
            
            # Get object from pool
            retrieved = cache_pool.get_object()
            
            if i % 100 == 0:
                # Check memory periodically
                memory_manager.check_memory_pressure()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"   ✓ Performance test completed in {duration:.2f} seconds")
        print(f"   ✓ Operations per second: {operations/duration:.0f}")
        
        # Get pool stats
        pool_stats = cache_pool.get_stats()
        print(f"   ✓ Pool efficiency: {pool_stats['hit_rate']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test error: {e}")
        return False

def main():
    """تست اصلی"""
    print("🧠 Memory Management System Test Suite")
    print("=" * 60)
    
    # Run tests
    test_results = []
    
    # Test 1: Basic functionality
    test_results.append(test_memory_management())
    
    # Test 2: Performance
    test_results.append(test_memory_performance())
    
    # Results summary
    print("\n📊 Test Results Summary:")
    print("=" * 30)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ Tests passed: {passed}/{total}")
    print(f"📈 Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! Memory Management System is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 