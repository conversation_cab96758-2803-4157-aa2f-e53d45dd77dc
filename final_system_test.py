#!/usr/bin/env python3
"""
🎯 Final System Test
تست نهایی سیستم بعد از رفع همه مشکلات
"""

import sys
import os
sys.path.insert(0, '.')

def main():
    """تست نهایی سیستم"""
    print("🎯 Final System Test")
    print("=" * 50)
    
    try:
        # Test main system
        from main_new import TradingSystemManager
        
        system_manager = TradingSystemManager()
        
        # Initialize system
        if system_manager.initialize_system():
            print("✅ System initialization: SUCCESS")
            
            # Display status
            system_manager.display_system_status()
            
            # Run tests
            if system_manager.run_system_tests():
                print("✅ System tests: SUCCESS")
            else:
                print("⚠️ System tests: PARTIAL SUCCESS")
            
            print("\n🎉 Final system test completed!")
            return 0
        else:
            print("❌ System initialization failed")
            return 1
            
    except Exception as e:
        print(f"❌ Final test error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
