#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌐 Multi-Exchange System Test
تست سیستم چند صرافی
"""

import os
import sys
import asyncio
import time
from datetime import datetime
from decimal import Decimal

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_multi_exchange_system():
    """تست سیستم چند صرافی"""
    print("🌐 Testing Multi-Exchange System")
    print("=" * 50)
    
    try:
        # Import the multi exchange system
        from core.multi_exchange import (
            MultiExchangeManager, 
            ExchangeConfig,
            ExchangeType,
            DataType,
            exchange_session
        )
        
        # Test 1: Create Exchange Configurations
        print("\n1️⃣ Testing Exchange Configuration...")
        
        # Create forex exchange config
        forex_config = ExchangeConfig(
            exchange_id="forex_test",
            name="Test Forex Exchange",
            exchange_type=ExchangeType.FOREX,
            api_url="https://api.forex.test",
            sandbox=True,
            enabled=True
        )
        print(f"   ✓ Forex config created: {forex_config.name}")
        
        # Create crypto exchange config
        crypto_config = ExchangeConfig(
            exchange_id="crypto_test",
            name="Test Crypto Exchange",
            exchange_type=ExchangeType.CRYPTO,
            api_url="https://api.crypto.test",
            sandbox=True,
            enabled=True
        )
        print(f"   ✓ Crypto config created: {crypto_config.name}")
        
        # Test 2: Multi-Exchange Manager
        print("\n2️⃣ Testing Multi-Exchange Manager...")
        
        manager = MultiExchangeManager()
        
        # Add exchanges
        forex_added = manager.add_exchange(forex_config)
        crypto_added = manager.add_exchange(crypto_config)
        
        print(f"   ✓ Forex exchange added: {forex_added}")
        print(f"   ✓ Crypto exchange added: {crypto_added}")
        
        # Test 3: Exchange Connection
        print("\n3️⃣ Testing Exchange Connection...")
        
        # Connect to all exchanges
        connection_results = await manager.connect_all()
        
        for exchange_id, connected in connection_results.items():
            print(f"   ✓ {exchange_id} connected: {connected}")
        
        # Test 4: Market Data Retrieval
        print("\n4️⃣ Testing Market Data Retrieval...")
        
        # Get forex data
        forex_data = await manager.get_market_data("EURUSD", DataType.QUOTE)
        for exchange_id, data in forex_data.items():
            if data:
                print(f"   ✓ {exchange_id} EURUSD: bid={data.bid}, ask={data.ask}")
        
        # Get crypto data
        crypto_data = await manager.get_market_data("BTCUSD", DataType.QUOTE)
        for exchange_id, data in crypto_data.items():
            if data:
                print(f"   ✓ {exchange_id} BTCUSD: bid={data.bid}, ask={data.ask}")
        
        # Test 5: Exchange Information
        print("\n5️⃣ Testing Exchange Information...")
        
        exchange_info = manager.get_exchange_info()
        for exchange_id, info in exchange_info.items():
            print(f"   ✓ {exchange_id}: {info.status.value}")
            print(f"     - Latency: {info.latency:.2f}ms")
            print(f"     - Symbols: {info.symbols_count}")
            print(f"     - Uptime: {info.uptime:.2f}s")
        
        # Test 6: Symbol Discovery
        print("\n6️⃣ Testing Symbol Discovery...")
        
        all_symbols = manager.get_all_symbols()
        for exchange_id, symbols in all_symbols.items():
            print(f"   ✓ {exchange_id}: {len(symbols)} symbols")
            if symbols:
                print(f"     - Sample: {symbols[:3]}")
        
        # Test 7: Order Routing
        print("\n7️⃣ Testing Order Routing...")
        
        # Create test order
        from core.order_manager import Order, OrderType, OrderSide
        test_order = Order(
            order_id="test_order_1",
            symbol="EURUSD",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=Decimal('100000')
        )
        
        # Submit order (will be routed automatically)
        order_results = await manager.submit_order(test_order)
        for exchange_id, submitted in order_results.items():
            print(f"   ✓ Order submitted to {exchange_id}: {submitted}")
        
        # Test 8: Data Aggregation
        print("\n8️⃣ Testing Data Aggregation...")
        
        # Get aggregated data
        aggregator = manager.data_aggregator
        
        # Add some test data
        await asyncio.sleep(0.5)  # Wait for data to accumulate
        
        # Get best quote
        best_quote = aggregator.get_best_quote("EURUSD")
        if best_quote:
            if best_quote["best_bid"]:
                print(f"   ✓ Best bid: {best_quote['best_bid'].bid} from {best_quote['best_bid'].exchange_id}")
            if best_quote["best_ask"]:
                print(f"   ✓ Best ask: {best_quote['best_ask'].ask} from {best_quote['best_ask'].exchange_id}")
        
        # Test 9: Connection Monitoring
        print("\n9️⃣ Testing Connection Monitoring...")
        
        # Get connection monitor stats
        connection_monitor = manager.connection_monitor
        
        for exchange_id in manager.exchanges.keys():
            uptime = connection_monitor.get_uptime(exchange_id)
            print(f"   ✓ {exchange_id} uptime: {uptime:.2f}%")
        
        # Test 10: System Statistics
        print("\n🔟 Testing System Statistics...")
        
        stats = manager.get_statistics()
        print(f"   ✓ Total exchanges: {stats['total_exchanges']}")
        print(f"   ✓ Connected exchanges: {stats['connected_exchanges']}")
        print(f"   ✓ Total symbols: {stats['total_symbols']}")
        print(f"   ✓ Total orders: {stats['total_orders']}")
        print(f"   ✓ Data packets processed: {stats['data_packets_processed']}")
        
        # Test 11: Exchange Session Context
        print("\n1️⃣1️⃣ Testing Exchange Session Context...")
        
        # Create a new manager for session test
        session_manager = MultiExchangeManager()
        session_manager.add_exchange(forex_config)
        
        async with exchange_session() as session:
            # This should connect and disconnect automatically
            print(f"   ✓ Exchange session context working")
        
        # Test 12: Disconnect All
        print("\n1️⃣2️⃣ Testing Disconnect All...")
        
        disconnect_results = await manager.disconnect_all()
        for exchange_id, disconnected in disconnect_results.items():
            print(f"   ✓ {exchange_id} disconnected: {disconnected}")
        
        print("\n✅ All Multi-Exchange System tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_data_aggregation():
    """تست تجمیع داده"""
    print("\n📊 Testing Data Aggregation...")
    
    try:
        from core.multi_exchange import DataAggregator, MarketData, DataType
        
        # Create aggregator
        aggregator = DataAggregator()
        
        # Add test data
        test_data_1 = MarketData(
            symbol="EURUSD",
            exchange_id="exchange_1",
            data_type=DataType.QUOTE,
            timestamp=datetime.now(),
            bid=Decimal('1.0950'),
            ask=Decimal('1.0952')
        )
        
        test_data_2 = MarketData(
            symbol="EURUSD",
            exchange_id="exchange_2",
            data_type=DataType.QUOTE,
            timestamp=datetime.now(),
            bid=Decimal('1.0948'),
            ask=Decimal('1.0950')
        )
        
        aggregator.add_data(test_data_1)
        aggregator.add_data(test_data_2)
        
        # Test best quote
        best_quote = aggregator.get_best_quote("EURUSD")
        if best_quote:
            print(f"   ✓ Best bid: {best_quote['best_bid'].bid} from {best_quote['best_bid'].exchange_id}")
            print(f"   ✓ Best ask: {best_quote['best_ask'].ask} from {best_quote['best_ask'].exchange_id}")
        
        # Test latest data
        latest_data = aggregator.get_latest_data("EURUSD", "exchange_1")
        if latest_data:
            print(f"   ✓ Latest data: {latest_data.bid}/{latest_data.ask}")
        
        print("✅ Data aggregation test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Data aggregation test error: {e}")
        return False

async def test_order_routing():
    """تست مسیریابی سفارش"""
    print("\n🚀 Testing Order Routing...")
    
    try:
        from core.multi_exchange import OrderRouter, ForexExchange, CryptoExchange, ExchangeConfig, ExchangeType
        from core.order_manager import Order, OrderType, OrderSide
        from decimal import Decimal
        
        # Create router
        router = OrderRouter()
        
        # Create test exchanges
        forex_config = ExchangeConfig(
            exchange_id="forex_test",
            name="Test Forex",
            exchange_type=ExchangeType.FOREX,
            supported_symbols=["EURUSD", "GBPUSD"]
        )
        
        crypto_config = ExchangeConfig(
            exchange_id="crypto_test",
            name="Test Crypto",
            exchange_type=ExchangeType.CRYPTO,
            supported_symbols=["BTCUSD", "ETHUSD"]
        )
        
        forex_exchange = ForexExchange(forex_config)
        crypto_exchange = CryptoExchange(crypto_config)
        
        # Mock connected status
        forex_exchange.status = forex_exchange.status.__class__.CONNECTED
        crypto_exchange.status = crypto_exchange.status.__class__.CONNECTED
        
        exchanges = {
            "forex_test": forex_exchange,
            "crypto_test": crypto_exchange
        }
        
        # Test forex order routing
        forex_order = Order(
            order_id="forex_order_1",
            symbol="EURUSD",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=Decimal('100000')
        )
        
        forex_route = await router.route_order(forex_order, exchanges)
        print(f"   ✓ Forex order routed to: {forex_route}")
        
        # Test crypto order routing
        crypto_order = Order(
            order_id="crypto_order_1",
            symbol="BTCUSD",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=Decimal('1')
        )
        
        crypto_route = await router.route_order(crypto_order, exchanges)
        print(f"   ✓ Crypto order routed to: {crypto_route}")
        
        print("✅ Order routing test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Order routing test error: {e}")
        return False

async def test_performance():
    """تست عملکرد"""
    print("\n⚡ Testing Performance...")
    
    try:
        from core.multi_exchange import MultiExchangeManager, ExchangeConfig, ExchangeType, DataType
        
        # Create manager
        manager = MultiExchangeManager()
        
        # Add multiple exchanges
        for i in range(5):
            config = ExchangeConfig(
                exchange_id=f"test_exchange_{i}",
                name=f"Test Exchange {i}",
                exchange_type=ExchangeType.FOREX,
                api_url=f"https://api.test{i}.com"
            )
            manager.add_exchange(config)
        
        # Connect all
        start_time = time.time()
        await manager.connect_all()
        connect_time = time.time() - start_time
        
        print(f"   ✓ Connected to 5 exchanges in {connect_time:.2f}s")
        
        # Get market data from all exchanges
        start_time = time.time()
        data_results = await manager.get_market_data("EURUSD", DataType.QUOTE)
        data_time = time.time() - start_time
        
        print(f"   ✓ Retrieved market data in {data_time:.3f}s")
        print(f"   ✓ Data sources: {len(data_results)}")
        
        # Disconnect all
        start_time = time.time()
        await manager.disconnect_all()
        disconnect_time = time.time() - start_time
        
        print(f"   ✓ Disconnected from all exchanges in {disconnect_time:.2f}s")
        
        print("✅ Performance test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Performance test error: {e}")
        return False

async def main():
    """تست اصلی"""
    print("🌐 Multi-Exchange System Test Suite")
    print("=" * 60)
    
    # Run tests
    test_results = []
    
    # Test 1: Basic multi-exchange functionality
    test_results.append(await test_multi_exchange_system())
    
    # Test 2: Data aggregation
    test_results.append(await test_data_aggregation())
    
    # Test 3: Order routing
    test_results.append(await test_order_routing())
    
    # Test 4: Performance
    test_results.append(await test_performance())
    
    # Results summary
    print("\n📊 Test Results Summary:")
    print("=" * 30)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ Tests passed: {passed}/{total}")
    print(f"📈 Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! Multi-Exchange System is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 