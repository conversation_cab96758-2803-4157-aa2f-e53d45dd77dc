{"type": "startup_tests", "timestamp": "2025-07-12T03:33:30.272989", "summary": {"total_tests": 4, "passed": 0, "failed": 4, "success_rate": 0.0, "total_duration": 184.44658184051514}, "results": [{"name": "test_advanced_risk_manager.py", "passed": false, "duration": 63.93709135055542, "output": "✅ All warnings suppressed\n✅ All warnings suppressed\n2025-07-12 03:30:43 - utils - \u001b[32mIN<PERSON><PERSON>\u001b[0m - ✅ Utils package initialized successfully\n✅ All warnings suppressed\n2025-07-12 03:31:14 - utils.sentiment_analyzer - \u001b[32mIN<PERSON>O\u001b[0m - پروکسی با موفقیت تنظیم شد.\n2025-07-12 03:31:15 - utils.sentiment_analyzer - \u001b[32mIN<PERSON>O\u001b[0m - Device set to use cpu\n2025-07-12 03:31:15 - utils.sentiment_analyzer - \u001b[33mWARNING\u001b[0m - spaCy model not found. Entity recognition will be limited.\n2025-07-12 03:31:18 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:31:18 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:31:21 - utils.sentiment_analyzer - \u001b[32m<PERSON><PERSON>O\u001b[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:31:21 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:31:21 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-12 03:31:21 - utils.sentiment_analyzer - \u001b[33mWARNING\u001b[0m - spaCy model not found. Entity recognition will be limited.\n2025-07-12 03:31:22 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:31:22 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:31:24 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:31:24 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:31:24 - utils.adaptive_plutus_system - \u001b[32mINFO\u001b[0m - Adaptive Plutus System initialized\n2025-07-12 03:31:24 - utils.adaptive_plutus_system - \u001b[32mINFO\u001b[0m - Adaptive Plutus System initialized\n2025-07-12 03:31:24 - models - \u001b[32mINFO\u001b[0m - ✅ Models package initialized successfully\n2025-07-12 03:31:25 - env - \u001b[32mINFO\u001b[0m - ✅ Environment package initialized successfully\n2025-07-12 03:31:25 - portfolio - \u001b[32mINFO\u001b[0m - ✅ Portfolio package initialized successfully\n2025-07-12 03:31:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: model_performance\n2025-07-12 03:31:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: strategy_performance\n2025-07-12 03:31:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: risk_assessment\n2025-07-12 03:31:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: prediction_accuracy\n2025-07-12 03:31:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluation engine initialized\n2025-07-12 03:31:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: model_performance\n2025-07-12 03:31:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: strategy_performance\n2025-07-12 03:31:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: risk_assessment\n2025-07-12 03:31:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: prediction_accuracy\n2025-07-12 03:31:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluation engine initialized\n2025-07-12 03:31:25 - evaluation - \u001b[32mINFO\u001b[0m - ✅ Evaluation package initialized successfully\n2025-07-12 03:31:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: bayesian\n2025-07-12 03:31:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: genetic\n2025-07-12 03:31:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: grid_search\n2025-07-12 03:31:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: random_search\n2025-07-12 03:31:25 - optimization - \u001b[32mINFO\u001b[0m - Optimization engine initialized\n2025-07-12 03:31:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: bayesian\n2025-07-12 03:31:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: genetic\n2025-07-12 03:31:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: grid_search\n2025-07-12 03:31:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: random_search\n2025-07-12 03:31:25 - optimization - \u001b[32mINFO\u001b[0m - Optimization engine initialized\n2025-07-12 03:31:25 - optimization - \u001b[32mINFO\u001b[0m - ✅ Optimization package initialized successfully\n2025-07-12 03:31:25 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-12 03:31:25 - utils.sentiment_analyzer - \u001b[33mWARNING\u001b[0m - spaCy model not found. Entity recognition will be limited.\n2025-07-12 03:31:26 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:31:26 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:31:26 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /health\n2025-07-12 03:31:26 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /status\n2025-07-12 03:31:26 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /models\n2025-07-12 03:31:26 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: POST /predict\n2025-07-12 03:31:26 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /portfolio\n2025-07-12 03:31:26 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /signals\n2025-07-12 03:31:26 - api - \u001b[32mINFO\u001b[0m - Middleware added: _rate_limit_middleware\n2025-07-12 03:31:26 - api - \u001b[32mINFO\u001b[0m - Middleware added: _logging_middleware\n2025-07-12 03:31:26 - api - \u001b[32mINFO\u001b[0m - API manager initialized on localhost:8000\n2025-07-12 03:31:26 - api - \u001b[32mINFO\u001b[0m - ✅ API package initialized successfully\n", "error": "INFO:core.memory_manager:Memory monitoring started\nINFO:core.order_manager:Order cleanup thread started\nINFO:core.model_versioning:Loaded 3 models from registry\nINFO:core.correlation_analysis:Advanced Correlation Analyzer initialized\nINFO:core.backtesting_framework:Advanced Backtesting Engine initialized\nINFO:core.enhanced_error_handling.global:Circuit breaker 'default' registered\nINFO:core.enhanced_error_handling.global:Circuit breaker 'api_calls' registered\nINFO:core.enhanced_error_handling.global:Retry handler 'default' registered\nINFO:core.enhanced_error_handling.global:Retry handler 'network' registered\nINFO:core.utils:✅ Performance monitoring started\nINFO:core.utils:✅ System monitoring setup completed\n\u001b[32mINFO\u001b[0m:utils:✅ Utils package initialized successfully\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:پروکسی با موفقیت تنظیم شد.\nWARNING:ai_models:Real sentiment models not available, using mock implementations\nINFO:ModelManager:🤖 Initializing AI models...\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Devi<PERSON> set to use cpu\n\u001b[33mWARNING\u001b[0m:utils.sentiment_analyzer:spaCy model not found. Entity recognition will be limited.\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\nINFO:ai_models.SentimentEnsemble:✅ Sentiment ensemble initialized\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[33mWARNING\u001b[0m:utils.sentiment_analyzer:spaCy model not found. Entity recognition will be limited.\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\nINFO:ai_models.SentimentEnsemble:✅ Sentiment ensemble initialized\nINFO:ai_models.ModelRegistry:✅ Model registered: sentiment_ensemble (sentiment_analysis)\nINFO:examples.plutus_integration_final:Plutus Integrated Trading System initialized\n\u001b[32mINFO\u001b[0m:utils.adaptive_plutus_system:Adaptive Plutus System initialized\nINFO:ai_models.TimeSeriesEnsemble:✅ Time series ensemble initialized\nINFO:examples.plutus_integration_final:Plutus Integrated Trading System initialized\n\u001b[32mINFO\u001b[0m:utils.adaptive_plutus_system:Adaptive Plutus System initialized\nINFO:ai_models.TimeSeriesEnsemble:✅ Time series ensemble initialized\nINFO:ai_models.ModelRegistry:✅ Model registered: timeseries_ensemble (time_series_forecasting)\nINFO:ModelManager:✅ All AI models initialized successfully\n\u001b[32mINFO\u001b[0m:models:✅ Models package initialized successfully\n(CVXPY) Jul 12 03:31:25 AM: Encountered unexpected exception importing solver CLARABEL:\nImportError('DLL load failed while importing clarabel: The specified procedure could not be found.')\n\u001b[32mINFO\u001b[0m:env:✅ Environment package initialized successfully\n\u001b[32mINFO\u001b[0m:portfolio:✅ Portfolio package initialized successfully\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: model_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: strategy_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: risk_assessment\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: prediction_accuracy\n\u001b[32mINFO\u001b[0m:evaluation:Evaluation engine initialized\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: model_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: strategy_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: risk_assessment\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: prediction_accuracy\n\u001b[32mINFO\u001b[0m:evaluation:Evaluation engine initialized\n\u001b[32mINFO\u001b[0m:evaluation:✅ Evaluation package initialized successfully\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: bayesian\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: genetic\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: grid_search\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: random_search\n\u001b[32mINFO\u001b[0m:optimization:Optimization engine initialized\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: bayesian\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: genetic\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: grid_search\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: random_search\n\u001b[32mINFO\u001b[0m:optimization:Optimization engine initialized\n\u001b[32mINFO\u001b[0m:optimization:✅ Optimization package initialized successfully\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[33mWARNING\u001b[0m:utils.sentiment_analyzer:spaCy model not found. Entity recognition will be limited.\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /health\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /status\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /models\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: POST /predict\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /portfolio\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /signals\n\u001b[32mINFO\u001b[0m:api:Middleware added: _rate_limit_middleware\n\u001b[32mINFO\u001b[0m:api:Middleware added: _logging_middleware\n\u001b[32mINFO\u001b[0m:api:API manager initialized on localhost:8000\n\u001b[32mINFO\u001b[0m:api:✅ API package initialized successfully\nINFO:core.error_handler:🔧 Recovery strategy registered for network_ConnectionError\nINFO:core.error_handler:🔧 Recovery strategy registered for model_ModelError\nERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]\n__main__.py: error: unrecognized arguments: --timeout=300\n  inifile: D:\\project\\pytest.ini\n  rootdir: D:\\project\n\n", "timestamp": "2025-07-12T03:31:29.688420"}, {"name": "test_smart_portfolio_manager.py", "passed": false, "duration": 40.044528007507324, "output": "✅ All warnings suppressed\n✅ All warnings suppressed\n2025-07-12 03:31:37 - utils - \u001b[32mIN<PERSON><PERSON>\u001b[0m - ✅ Utils package initialized successfully\n✅ All warnings suppressed\n2025-07-12 03:31:54 - utils.sentiment_analyzer - \u001b[32mIN<PERSON>O\u001b[0m - پروکسی با موفقیت تنظیم شد.\n2025-07-12 03:31:55 - utils.sentiment_analyzer - \u001b[32mIN<PERSON>O\u001b[0m - Device set to use cpu\n2025-07-12 03:31:55 - utils.sentiment_analyzer - \u001b[33mWARNING\u001b[0m - spaCy model not found. Entity recognition will be limited.\n2025-07-12 03:31:57 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:31:57 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:32:01 - utils.sentiment_analyzer - \u001b[32m<PERSON><PERSON>O\u001b[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:32:01 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:32:01 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-12 03:32:01 - utils.sentiment_analyzer - \u001b[33mWARNING\u001b[0m - spaCy model not found. Entity recognition will be limited.\n2025-07-12 03:32:02 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:32:02 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:32:04 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:32:04 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:32:04 - utils.adaptive_plutus_system - \u001b[32mINFO\u001b[0m - Adaptive Plutus System initialized\n2025-07-12 03:32:04 - utils.adaptive_plutus_system - \u001b[32mINFO\u001b[0m - Adaptive Plutus System initialized\n2025-07-12 03:32:04 - models - \u001b[32mINFO\u001b[0m - ✅ Models package initialized successfully\n2025-07-12 03:32:05 - env - \u001b[32mINFO\u001b[0m - ✅ Environment package initialized successfully\n2025-07-12 03:32:05 - portfolio - \u001b[32mINFO\u001b[0m - ✅ Portfolio package initialized successfully\n2025-07-12 03:32:05 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: model_performance\n2025-07-12 03:32:05 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: strategy_performance\n2025-07-12 03:32:05 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: risk_assessment\n2025-07-12 03:32:05 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: prediction_accuracy\n2025-07-12 03:32:05 - evaluation - \u001b[32mINFO\u001b[0m - Evaluation engine initialized\n2025-07-12 03:32:05 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: model_performance\n2025-07-12 03:32:05 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: strategy_performance\n2025-07-12 03:32:05 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: risk_assessment\n2025-07-12 03:32:05 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: prediction_accuracy\n2025-07-12 03:32:05 - evaluation - \u001b[32mINFO\u001b[0m - Evaluation engine initialized\n2025-07-12 03:32:05 - evaluation - \u001b[32mINFO\u001b[0m - ✅ Evaluation package initialized successfully\n2025-07-12 03:32:05 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: bayesian\n2025-07-12 03:32:05 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: genetic\n2025-07-12 03:32:05 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: grid_search\n2025-07-12 03:32:05 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: random_search\n2025-07-12 03:32:05 - optimization - \u001b[32mINFO\u001b[0m - Optimization engine initialized\n2025-07-12 03:32:05 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: bayesian\n2025-07-12 03:32:05 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: genetic\n2025-07-12 03:32:05 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: grid_search\n2025-07-12 03:32:05 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: random_search\n2025-07-12 03:32:05 - optimization - \u001b[32mINFO\u001b[0m - Optimization engine initialized\n2025-07-12 03:32:05 - optimization - \u001b[32mINFO\u001b[0m - ✅ Optimization package initialized successfully\n2025-07-12 03:32:06 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-12 03:32:06 - utils.sentiment_analyzer - \u001b[33mWARNING\u001b[0m - spaCy model not found. Entity recognition will be limited.\n2025-07-12 03:32:06 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:32:06 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:32:06 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /health\n2025-07-12 03:32:06 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /status\n2025-07-12 03:32:06 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /models\n2025-07-12 03:32:06 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: POST /predict\n2025-07-12 03:32:06 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /portfolio\n2025-07-12 03:32:06 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /signals\n2025-07-12 03:32:06 - api - \u001b[32mINFO\u001b[0m - Middleware added: _rate_limit_middleware\n2025-07-12 03:32:06 - api - \u001b[32mINFO\u001b[0m - Middleware added: _logging_middleware\n2025-07-12 03:32:06 - api - \u001b[32mINFO\u001b[0m - API manager initialized on localhost:8000\n2025-07-12 03:32:06 - api - \u001b[32mINFO\u001b[0m - ✅ API package initialized successfully\n", "error": "INFO:core.memory_manager:Memory monitoring started\nINFO:core.order_manager:Order cleanup thread started\nINFO:core.model_versioning:Loaded 3 models from registry\nINFO:core.correlation_analysis:Advanced Correlation Analyzer initialized\nINFO:core.backtesting_framework:Advanced Backtesting Engine initialized\nINFO:core.enhanced_error_handling.global:Circuit breaker 'default' registered\nINFO:core.enhanced_error_handling.global:Circuit breaker 'api_calls' registered\nINFO:core.enhanced_error_handling.global:Retry handler 'default' registered\nINFO:core.enhanced_error_handling.global:Retry handler 'network' registered\nINFO:core.utils:✅ Performance monitoring started\nINFO:core.utils:✅ System monitoring setup completed\n\u001b[32mINFO\u001b[0m:utils:✅ Utils package initialized successfully\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:پروکسی با موفقیت تنظیم شد.\nWARNING:ai_models:Real sentiment models not available, using mock implementations\nINFO:ModelManager:🤖 Initializing AI models...\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Devi<PERSON> set to use cpu\n\u001b[33mWARNING\u001b[0m:utils.sentiment_analyzer:spaCy model not found. Entity recognition will be limited.\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\nINFO:ai_models.SentimentEnsemble:✅ Sentiment ensemble initialized\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[33mWARNING\u001b[0m:utils.sentiment_analyzer:spaCy model not found. Entity recognition will be limited.\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\nINFO:ai_models.SentimentEnsemble:✅ Sentiment ensemble initialized\nINFO:ai_models.ModelRegistry:✅ Model registered: sentiment_ensemble (sentiment_analysis)\nINFO:examples.plutus_integration_final:Plutus Integrated Trading System initialized\n\u001b[32mINFO\u001b[0m:utils.adaptive_plutus_system:Adaptive Plutus System initialized\nINFO:ai_models.TimeSeriesEnsemble:✅ Time series ensemble initialized\nINFO:examples.plutus_integration_final:Plutus Integrated Trading System initialized\n\u001b[32mINFO\u001b[0m:utils.adaptive_plutus_system:Adaptive Plutus System initialized\nINFO:ai_models.TimeSeriesEnsemble:✅ Time series ensemble initialized\nINFO:ai_models.ModelRegistry:✅ Model registered: timeseries_ensemble (time_series_forecasting)\nINFO:ModelManager:✅ All AI models initialized successfully\n\u001b[32mINFO\u001b[0m:models:✅ Models package initialized successfully\n(CVXPY) Jul 12 03:32:05 AM: Encountered unexpected exception importing solver CLARABEL:\nImportError('DLL load failed while importing clarabel: The specified procedure could not be found.')\n\u001b[32mINFO\u001b[0m:env:✅ Environment package initialized successfully\n\u001b[32mINFO\u001b[0m:portfolio:✅ Portfolio package initialized successfully\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: model_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: strategy_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: risk_assessment\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: prediction_accuracy\n\u001b[32mINFO\u001b[0m:evaluation:Evaluation engine initialized\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: model_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: strategy_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: risk_assessment\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: prediction_accuracy\n\u001b[32mINFO\u001b[0m:evaluation:Evaluation engine initialized\n\u001b[32mINFO\u001b[0m:evaluation:✅ Evaluation package initialized successfully\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: bayesian\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: genetic\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: grid_search\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: random_search\n\u001b[32mINFO\u001b[0m:optimization:Optimization engine initialized\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: bayesian\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: genetic\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: grid_search\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: random_search\n\u001b[32mINFO\u001b[0m:optimization:Optimization engine initialized\n\u001b[32mINFO\u001b[0m:optimization:✅ Optimization package initialized successfully\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[33mWARNING\u001b[0m:utils.sentiment_analyzer:spaCy model not found. Entity recognition will be limited.\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /health\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /status\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /models\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: POST /predict\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /portfolio\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /signals\n\u001b[32mINFO\u001b[0m:api:Middleware added: _rate_limit_middleware\n\u001b[32mINFO\u001b[0m:api:Middleware added: _logging_middleware\n\u001b[32mINFO\u001b[0m:api:API manager initialized on localhost:8000\n\u001b[32mINFO\u001b[0m:api:✅ API package initialized successfully\nINFO:core.error_handler:🔧 Recovery strategy registered for network_ConnectionError\nINFO:core.error_handler:🔧 Recovery strategy registered for model_ModelError\nERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]\n__main__.py: error: unrecognized arguments: --timeout=300\n  inifile: D:\\project\\pytest.ini\n  rootdir: D:\\project\n\n", "timestamp": "2025-07-12T03:32:09.750409"}, {"name": "test_integrated_system.py", "passed": false, "duration": 39.44559121131897, "output": "✅ All warnings suppressed\n✅ All warnings suppressed\n2025-07-12 03:32:17 - utils - \u001b[32mIN<PERSON><PERSON>\u001b[0m - ✅ Utils package initialized successfully\n✅ All warnings suppressed\n2025-07-12 03:32:34 - utils.sentiment_analyzer - \u001b[32mIN<PERSON>O\u001b[0m - پروکسی با موفقیت تنظیم شد.\n2025-07-12 03:32:35 - utils.sentiment_analyzer - \u001b[32mIN<PERSON>O\u001b[0m - Device set to use cpu\n2025-07-12 03:32:35 - utils.sentiment_analyzer - \u001b[33mWARNING\u001b[0m - spaCy model not found. Entity recognition will be limited.\n2025-07-12 03:32:37 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:32:37 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:32:40 - utils.sentiment_analyzer - \u001b[32m<PERSON><PERSON>O\u001b[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:32:40 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:32:40 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-12 03:32:40 - utils.sentiment_analyzer - \u001b[33mWARNING\u001b[0m - spaCy model not found. Entity recognition will be limited.\n2025-07-12 03:32:41 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:32:41 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:32:44 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:32:44 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:32:44 - utils.adaptive_plutus_system - \u001b[32mINFO\u001b[0m - Adaptive Plutus System initialized\n2025-07-12 03:32:44 - utils.adaptive_plutus_system - \u001b[32mINFO\u001b[0m - Adaptive Plutus System initialized\n2025-07-12 03:32:44 - models - \u001b[32mINFO\u001b[0m - ✅ Models package initialized successfully\n2025-07-12 03:32:44 - env - \u001b[32mINFO\u001b[0m - ✅ Environment package initialized successfully\n2025-07-12 03:32:44 - portfolio - \u001b[32mINFO\u001b[0m - ✅ Portfolio package initialized successfully\n2025-07-12 03:32:44 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: model_performance\n2025-07-12 03:32:44 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: strategy_performance\n2025-07-12 03:32:44 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: risk_assessment\n2025-07-12 03:32:44 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: prediction_accuracy\n2025-07-12 03:32:44 - evaluation - \u001b[32mINFO\u001b[0m - Evaluation engine initialized\n2025-07-12 03:32:44 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: model_performance\n2025-07-12 03:32:44 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: strategy_performance\n2025-07-12 03:32:44 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: risk_assessment\n2025-07-12 03:32:44 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: prediction_accuracy\n2025-07-12 03:32:44 - evaluation - \u001b[32mINFO\u001b[0m - Evaluation engine initialized\n2025-07-12 03:32:44 - evaluation - \u001b[32mINFO\u001b[0m - ✅ Evaluation package initialized successfully\n2025-07-12 03:32:44 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: bayesian\n2025-07-12 03:32:44 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: genetic\n2025-07-12 03:32:44 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: grid_search\n2025-07-12 03:32:44 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: random_search\n2025-07-12 03:32:44 - optimization - \u001b[32mINFO\u001b[0m - Optimization engine initialized\n2025-07-12 03:32:44 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: bayesian\n2025-07-12 03:32:44 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: genetic\n2025-07-12 03:32:44 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: grid_search\n2025-07-12 03:32:44 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: random_search\n2025-07-12 03:32:44 - optimization - \u001b[32mINFO\u001b[0m - Optimization engine initialized\n2025-07-12 03:32:44 - optimization - \u001b[32mINFO\u001b[0m - ✅ Optimization package initialized successfully\n2025-07-12 03:32:45 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-12 03:32:45 - utils.sentiment_analyzer - \u001b[33mWARNING\u001b[0m - spaCy model not found. Entity recognition will be limited.\n2025-07-12 03:32:46 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:32:46 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:32:46 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /health\n2025-07-12 03:32:46 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /status\n2025-07-12 03:32:46 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /models\n2025-07-12 03:32:46 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: POST /predict\n2025-07-12 03:32:46 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /portfolio\n2025-07-12 03:32:46 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /signals\n2025-07-12 03:32:46 - api - \u001b[32mINFO\u001b[0m - Middleware added: _rate_limit_middleware\n2025-07-12 03:32:46 - api - \u001b[32mINFO\u001b[0m - Middleware added: _logging_middleware\n2025-07-12 03:32:46 - api - \u001b[32mINFO\u001b[0m - API manager initialized on localhost:8000\n2025-07-12 03:32:46 - api - \u001b[32mINFO\u001b[0m - ✅ API package initialized successfully\n", "error": "INFO:core.memory_manager:Memory monitoring started\nINFO:core.order_manager:Order cleanup thread started\nINFO:core.model_versioning:Loaded 3 models from registry\nINFO:core.correlation_analysis:Advanced Correlation Analyzer initialized\nINFO:core.backtesting_framework:Advanced Backtesting Engine initialized\nINFO:core.enhanced_error_handling.global:Circuit breaker 'default' registered\nINFO:core.enhanced_error_handling.global:Circuit breaker 'api_calls' registered\nINFO:core.enhanced_error_handling.global:Retry handler 'default' registered\nINFO:core.enhanced_error_handling.global:Retry handler 'network' registered\nINFO:core.utils:✅ Performance monitoring started\nINFO:core.utils:✅ System monitoring setup completed\n\u001b[32mINFO\u001b[0m:utils:✅ Utils package initialized successfully\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:پروکسی با موفقیت تنظیم شد.\nWARNING:ai_models:Real sentiment models not available, using mock implementations\nINFO:ModelManager:🤖 Initializing AI models...\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Devi<PERSON> set to use cpu\n\u001b[33mWARNING\u001b[0m:utils.sentiment_analyzer:spaCy model not found. Entity recognition will be limited.\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\nINFO:ai_models.SentimentEnsemble:✅ Sentiment ensemble initialized\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[33mWARNING\u001b[0m:utils.sentiment_analyzer:spaCy model not found. Entity recognition will be limited.\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\nINFO:ai_models.SentimentEnsemble:✅ Sentiment ensemble initialized\nINFO:ai_models.ModelRegistry:✅ Model registered: sentiment_ensemble (sentiment_analysis)\nINFO:examples.plutus_integration_final:Plutus Integrated Trading System initialized\n\u001b[32mINFO\u001b[0m:utils.adaptive_plutus_system:Adaptive Plutus System initialized\nINFO:ai_models.TimeSeriesEnsemble:✅ Time series ensemble initialized\nINFO:examples.plutus_integration_final:Plutus Integrated Trading System initialized\n\u001b[32mINFO\u001b[0m:utils.adaptive_plutus_system:Adaptive Plutus System initialized\nINFO:ai_models.TimeSeriesEnsemble:✅ Time series ensemble initialized\nINFO:ai_models.ModelRegistry:✅ Model registered: timeseries_ensemble (time_series_forecasting)\nINFO:ModelManager:✅ All AI models initialized successfully\n\u001b[32mINFO\u001b[0m:models:✅ Models package initialized successfully\n(CVXPY) Jul 12 03:32:44 AM: Encountered unexpected exception importing solver CLARABEL:\nImportError('DLL load failed while importing clarabel: The specified procedure could not be found.')\n\u001b[32mINFO\u001b[0m:env:✅ Environment package initialized successfully\n\u001b[32mINFO\u001b[0m:portfolio:✅ Portfolio package initialized successfully\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: model_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: strategy_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: risk_assessment\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: prediction_accuracy\n\u001b[32mINFO\u001b[0m:evaluation:Evaluation engine initialized\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: model_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: strategy_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: risk_assessment\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: prediction_accuracy\n\u001b[32mINFO\u001b[0m:evaluation:Evaluation engine initialized\n\u001b[32mINFO\u001b[0m:evaluation:✅ Evaluation package initialized successfully\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: bayesian\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: genetic\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: grid_search\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: random_search\n\u001b[32mINFO\u001b[0m:optimization:Optimization engine initialized\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: bayesian\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: genetic\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: grid_search\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: random_search\n\u001b[32mINFO\u001b[0m:optimization:Optimization engine initialized\n\u001b[32mINFO\u001b[0m:optimization:✅ Optimization package initialized successfully\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[33mWARNING\u001b[0m:utils.sentiment_analyzer:spaCy model not found. Entity recognition will be limited.\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /health\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /status\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /models\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: POST /predict\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /portfolio\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /signals\n\u001b[32mINFO\u001b[0m:api:Middleware added: _rate_limit_middleware\n\u001b[32mINFO\u001b[0m:api:Middleware added: _logging_middleware\n\u001b[32mINFO\u001b[0m:api:API manager initialized on localhost:8000\n\u001b[32mINFO\u001b[0m:api:✅ API package initialized successfully\nINFO:core.error_handler:🔧 Recovery strategy registered for network_ConnectionError\nINFO:core.error_handler:🔧 Recovery strategy registered for model_ModelError\nERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]\n__main__.py: error: unrecognized arguments: --timeout=300\n  inifile: D:\\project\\pytest.ini\n  rootdir: D:\\project\n\n", "timestamp": "2025-07-12T03:32:49.218807"}, {"name": "test_integration_*.py", "passed": false, "duration": 41.01937127113342, "output": "✅ All warnings suppressed\n✅ All warnings suppressed\n2025-07-12 03:32:56 - utils - \u001b[32m<PERSON><PERSON><PERSON>\u001b[0m - ✅ Utils package initialized successfully\n✅ All warnings suppressed\n2025-07-12 03:33:15 - utils.sentiment_analyzer - \u001b[32mIN<PERSON>O\u001b[0m - پروکسی با موفقیت تنظیم شد.\n2025-07-12 03:33:16 - utils.sentiment_analyzer - \u001b[32mIN<PERSON><PERSON>\u001b[0m - Device set to use cpu\n2025-07-12 03:33:16 - utils.sentiment_analyzer - \u001b[33mWARNING\u001b[0m - spaCy model not found. Entity recognition will be limited.\n2025-07-12 03:33:18 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:33:18 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:33:21 - utils.sentiment_analyzer - \u001b[32m<PERSON><PERSON>O\u001b[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:33:21 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:33:21 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-12 03:33:21 - utils.sentiment_analyzer - \u001b[33mWARNING\u001b[0m - spaCy model not found. Entity recognition will be limited.\n2025-07-12 03:33:22 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:33:22 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:33:25 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:33:25 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\n2025-07-12 03:33:25 - utils.adaptive_plutus_system - \u001b[32mINFO\u001b[0m - Adaptive Plutus System initialized\n2025-07-12 03:33:25 - utils.adaptive_plutus_system - \u001b[32mINFO\u001b[0m - Adaptive Plutus System initialized\n2025-07-12 03:33:25 - models - \u001b[32mINFO\u001b[0m - ✅ Models package initialized successfully\n2025-07-12 03:33:25 - env - \u001b[32mINFO\u001b[0m - ✅ Environment package initialized successfully\n2025-07-12 03:33:25 - portfolio - \u001b[32mINFO\u001b[0m - ✅ Portfolio package initialized successfully\n2025-07-12 03:33:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: model_performance\n2025-07-12 03:33:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: strategy_performance\n2025-07-12 03:33:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: risk_assessment\n2025-07-12 03:33:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: prediction_accuracy\n2025-07-12 03:33:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluation engine initialized\n2025-07-12 03:33:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: model_performance\n2025-07-12 03:33:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: strategy_performance\n2025-07-12 03:33:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: risk_assessment\n2025-07-12 03:33:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluator registered: prediction_accuracy\n2025-07-12 03:33:25 - evaluation - \u001b[32mINFO\u001b[0m - Evaluation engine initialized\n2025-07-12 03:33:25 - evaluation - \u001b[32mINFO\u001b[0m - ✅ Evaluation package initialized successfully\n2025-07-12 03:33:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: bayesian\n2025-07-12 03:33:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: genetic\n2025-07-12 03:33:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: grid_search\n2025-07-12 03:33:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: random_search\n2025-07-12 03:33:25 - optimization - \u001b[32mINFO\u001b[0m - Optimization engine initialized\n2025-07-12 03:33:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: bayesian\n2025-07-12 03:33:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: genetic\n2025-07-12 03:33:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: grid_search\n2025-07-12 03:33:25 - optimization - \u001b[32mINFO\u001b[0m - Optimizer registered: random_search\n2025-07-12 03:33:25 - optimization - \u001b[32mINFO\u001b[0m - Optimization engine initialized\n2025-07-12 03:33:25 - optimization - \u001b[32mINFO\u001b[0m - ✅ Optimization package initialized successfully\n2025-07-12 03:33:26 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Device set to use cpu\n2025-07-12 03:33:26 - utils.sentiment_analyzer - \u001b[33mWARNING\u001b[0m - spaCy model not found. Entity recognition will be limited.\n2025-07-12 03:33:27 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:33:27 - utils.sentiment_analyzer - \u001b[32mINFO\u001b[0m - Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n2025-07-12 03:33:27 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /health\n2025-07-12 03:33:27 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /status\n2025-07-12 03:33:27 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /models\n2025-07-12 03:33:27 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: POST /predict\n2025-07-12 03:33:27 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /portfolio\n2025-07-12 03:33:27 - api - \u001b[32mINFO\u001b[0m - Endpoint registered: GET /signals\n2025-07-12 03:33:27 - api - \u001b[32mINFO\u001b[0m - Middleware added: _rate_limit_middleware\n2025-07-12 03:33:27 - api - \u001b[32mINFO\u001b[0m - Middleware added: _logging_middleware\n2025-07-12 03:33:27 - api - \u001b[32mINFO\u001b[0m - API manager initialized on localhost:8000\n2025-07-12 03:33:27 - api - \u001b[32mINFO\u001b[0m - ✅ API package initialized successfully\n", "error": "INFO:core.memory_manager:Memory monitoring started\nINFO:core.order_manager:Order cleanup thread started\nINFO:core.model_versioning:Loaded 3 models from registry\nINFO:core.correlation_analysis:Advanced Correlation Analyzer initialized\nINFO:core.backtesting_framework:Advanced Backtesting Engine initialized\nINFO:core.enhanced_error_handling.global:Circuit breaker 'default' registered\nINFO:core.enhanced_error_handling.global:Circuit breaker 'api_calls' registered\nINFO:core.enhanced_error_handling.global:Retry handler 'default' registered\nINFO:core.enhanced_error_handling.global:Retry handler 'network' registered\nINFO:core.utils:✅ Performance monitoring started\nINFO:core.utils:✅ System monitoring setup completed\n\u001b[32mINFO\u001b[0m:utils:✅ Utils package initialized successfully\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:پروکسی با موفقیت تنظیم شد.\nWARNING:ai_models:Real sentiment models not available, using mock implementations\nINFO:ModelManager:🤖 Initializing AI models...\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Devi<PERSON> set to use cpu\n\u001b[33mWARNING\u001b[0m:utils.sentiment_analyzer:spaCy model not found. Entity recognition will be limited.\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\nINFO:ai_models.SentimentEnsemble:✅ Sentiment ensemble initialized\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[33mWARNING\u001b[0m:utils.sentiment_analyzer:spaCy model not found. Entity recognition will be limited.\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: HooshvareLab/bert-fa-base-uncased\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded fa model: HooshvareLab/bert-fa-base-uncased\nINFO:ai_models.SentimentEnsemble:✅ Sentiment ensemble initialized\nINFO:ai_models.ModelRegistry:✅ Model registered: sentiment_ensemble (sentiment_analysis)\nINFO:examples.plutus_integration_final:Plutus Integrated Trading System initialized\n\u001b[32mINFO\u001b[0m:utils.adaptive_plutus_system:Adaptive Plutus System initialized\nINFO:ai_models.TimeSeriesEnsemble:✅ Time series ensemble initialized\nINFO:examples.plutus_integration_final:Plutus Integrated Trading System initialized\n\u001b[32mINFO\u001b[0m:utils.adaptive_plutus_system:Adaptive Plutus System initialized\nINFO:ai_models.TimeSeriesEnsemble:✅ Time series ensemble initialized\nINFO:ai_models.ModelRegistry:✅ Model registered: timeseries_ensemble (time_series_forecasting)\nINFO:ModelManager:✅ All AI models initialized successfully\n\u001b[32mINFO\u001b[0m:models:✅ Models package initialized successfully\n(CVXPY) Jul 12 03:33:25 AM: Encountered unexpected exception importing solver CLARABEL:\nImportError('DLL load failed while importing clarabel: The specified procedure could not be found.')\n\u001b[32mINFO\u001b[0m:env:✅ Environment package initialized successfully\n\u001b[32mINFO\u001b[0m:portfolio:✅ Portfolio package initialized successfully\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: model_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: strategy_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: risk_assessment\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: prediction_accuracy\n\u001b[32mINFO\u001b[0m:evaluation:Evaluation engine initialized\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: model_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: strategy_performance\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: risk_assessment\n\u001b[32mINFO\u001b[0m:evaluation:Evaluator registered: prediction_accuracy\n\u001b[32mINFO\u001b[0m:evaluation:Evaluation engine initialized\n\u001b[32mINFO\u001b[0m:evaluation:✅ Evaluation package initialized successfully\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: bayesian\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: genetic\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: grid_search\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: random_search\n\u001b[32mINFO\u001b[0m:optimization:Optimization engine initialized\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: bayesian\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: genetic\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: grid_search\n\u001b[32mINFO\u001b[0m:optimization:Optimizer registered: random_search\n\u001b[32mINFO\u001b[0m:optimization:Optimization engine initialized\n\u001b[32mINFO\u001b[0m:optimization:✅ Optimization package initialized successfully\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Device set to use cpu\n\u001b[33mWARNING\u001b[0m:utils.sentiment_analyzer:spaCy model not found. Entity recognition will be limited.\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:utils.sentiment_analyzer:Successfully loaded en model: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /health\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /status\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /models\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: POST /predict\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /portfolio\n\u001b[32mINFO\u001b[0m:api:Endpoint registered: GET /signals\n\u001b[32mINFO\u001b[0m:api:Middleware added: _rate_limit_middleware\n\u001b[32mINFO\u001b[0m:api:Middleware added: _logging_middleware\n\u001b[32mINFO\u001b[0m:api:API manager initialized on localhost:8000\n\u001b[32mINFO\u001b[0m:api:✅ API package initialized successfully\nINFO:core.error_handler:🔧 Recovery strategy registered for network_ConnectionError\nINFO:core.error_handler:🔧 Recovery strategy registered for model_ModelError\nERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]\n__main__.py: error: unrecognized arguments: --timeout=900\n  inifile: D:\\project\\pytest.ini\n  rootdir: D:\\project\n\n", "timestamp": "2025-07-12T03:33:30.255803"}]}