#!/usr/bin/env python3
"""
🧪 Simple Database Test
تست ساده سیستم دیتابیس
"""

import os
import sys
import tempfile

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_simple_database():
    """تست ساده Database Manager"""
    print("🗄️ Testing Simple Database Transaction Management...")
    
    tests_passed = 0
    tests_failed = 0
    
    # Test 1: Import
    try:
        from core.simple_database_manager import SimpleDatabaseManager
        print("✅ Test 1: Import SimpleDatabaseManager - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 1: Import SimpleDatabaseManager - FAILED: {e}")
        tests_failed += 1
        return False
    
    # Test 2: Create database manager
    try:
        db_manager = SimpleDatabaseManager("test_simple.db")
        print("✅ Test 2: Create Database Manager - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 2: Create Database Manager - FAILED: {e}")
        tests_failed += 1
        return False
    
    # Test 3: Initialize database
    try:
        success = db_manager.initialize()
        assert success == True
        print("✅ Test 3: Initialize Database - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 3: Initialize Database - FAILED: {e}")
        tests_failed += 1
        return False
    
    # Test 4: Insert trading signal
    try:
        signal_id = db_manager.insert_trading_signal(
            symbol="EURUSD",
            signal_type="buy",
            price=1.1234,
            confidence=0.85,
            source="test_system"
        )
        assert signal_id is not None
        assert signal_id > 0
        print("✅ Test 4: Insert Trading Signal - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 4: Insert Trading Signal - FAILED: {e}")
        tests_failed += 1
        return False
    
    # Test 5: Get trading signals
    try:
        signals = db_manager.get_trading_signals(symbol="EURUSD")
        assert isinstance(signals, list)
        assert len(signals) > 0
        print("✅ Test 5: Get Trading Signals - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 5: Get Trading Signals - FAILED: {e}")
        tests_failed += 1
        return False
    
    # Test 6: Bulk insert signals
    try:
        bulk_signals = [
            {"symbol": "GBPUSD", "signal_type": "sell", "price": 1.2345, "confidence": 0.75},
            {"symbol": "USDJPY", "signal_type": "buy", "price": 110.50, "confidence": 0.90}
        ]
        
        inserted_count = db_manager.bulk_insert_signals(bulk_signals)
        assert inserted_count == 2
        print("✅ Test 6: Bulk Insert Signals - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 6: Bulk Insert Signals - FAILED: {e}")
        tests_failed += 1
        return False
    
    # Test 7: Database statistics
    try:
        stats = db_manager.get_database_statistics()
        assert isinstance(stats, dict)
        assert 'trading_signals_count' in stats
        assert stats['trading_signals_count'] >= 3  # We inserted 3 signals
        print("✅ Test 7: Database Statistics - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 7: Database Statistics - FAILED: {e}")
        tests_failed += 1
        return False
    
    # Test 8: Execute query
    try:
        query_result = db_manager.execute_query("SELECT COUNT(*) as count FROM trading_signals")
        assert isinstance(query_result, list)
        assert len(query_result) > 0
        assert query_result[0]['count'] >= 3
        print("✅ Test 8: Execute Query - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 8: Execute Query - FAILED: {e}")
        tests_failed += 1
        return False
    
    # Test 9: Transaction statistics
    try:
        stats = db_manager.get_database_statistics()
        transaction_stats = stats.get('transaction_stats', {})
        assert isinstance(transaction_stats, dict)
        print("✅ Test 9: Transaction Statistics - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 9: Transaction Statistics - FAILED: {e}")
        tests_failed += 1
        return False
    
    # Test 10: Close database
    try:
        db_manager.close()
        print("✅ Test 10: Close Database - PASSED")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Test 10: Close Database - FAILED: {e}")
        tests_failed += 1
        return False
    
    # Cleanup
    try:
        if os.path.exists("test_simple.db"):
            os.remove("test_simple.db")
    except:
        pass
    
    # Summary
    total_tests = tests_passed + tests_failed
    success_rate = (tests_passed / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n📊 DATABASE TEST RESULTS:")
    print(f"✅ Passed: {tests_passed}")
    print(f"❌ Failed: {tests_failed}")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    if tests_failed == 0:
        print("\n🎉 ALL DATABASE TESTS PASSED!")
        print("✅ Database Transaction Management is ready!")
        return True
    else:
        print("\n❌ Some tests failed!")
        return False

if __name__ == "__main__":
    success = test_simple_database()
    exit(0 if success else 1) 