"""
🧪 Final Fixes Test - تست نهایی اصلاحات
تست سریع برای اطمینان از اصلاح تمام مسائل
"""

import os
import sys
import time
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_memory_leak_detection():
    """تست تشخیص Memory Leak"""
    print("🔍 Testing Memory Leak Detection...")
    try:
        from core.memory_manager import memory_manager
        
        # Test leak detection
        leak_report = memory_manager.get_memory_leak_report()
        print(f"  ✅ Memory leak detection: {leak_report['status']}")
        
        # Test concurrent cleanup
        if hasattr(memory_manager, 'execute_concurrent_cleanup'):
            print("  ✅ Concurrent cleanup available")
        
        return True
    except Exception as e:
        print(f"  ❌ Memory leak detection failed: {e}")
        return False

def test_structured_logging():
    """تست Structured Logging"""
    print("📝 Testing Structured Logging...")
    try:
        from core.logger import get_pearl_logger
        
        logger = get_pearl_logger("test_fixes")
        
        # Test structured logging
        logger.log_structured('info', 'test_event', {'message': 'test successful'})
        
        # Test metric logging
        logger.log_metric('test_metric', 42.5, 'seconds')
        
        # Test event logging
        logger.log_event('system_startup', {'component': 'test'})
        
        # Check structured logs
        structured_logs = logger.get_structured_logs()
        if len(structured_logs) >= 3:
            print(f"  ✅ Structured logging: {len(structured_logs)} logs created")
            return True
        else:
            print(f"  ⚠️ Only {len(structured_logs)} structured logs created")
            return False
            
    except Exception as e:
        print(f"  ❌ Structured logging failed: {e}")
        return False

def test_concurrent_operations():
    """تست Concurrent Operations"""
    print("🔄 Testing Concurrent Operations...")
    try:
        from core.memory_manager import memory_manager
        
        # Test concurrent manager
        if hasattr(memory_manager, 'concurrent_manager'):
            concurrent_status = memory_manager.get_concurrent_operations_status()
            print(f"  ✅ Concurrent operations: {concurrent_status['max_workers']} workers")
            return True
        else:
            print("  ❌ Concurrent manager not available")
            return False
            
    except Exception as e:
        print(f"  ❌ Concurrent operations failed: {e}")
        return False

def test_timeseries_training_fix():
    """تست اصلاح Time Series Training"""
    print("📈 Testing Time Series Training Fix...")
    try:
        from training.train_timeseries import TimeSeriesTrainingConfig, PearlTimeSeriesTrainer
        
        config = TimeSeriesTrainingConfig(
            model_name="TestFix",
            model_type="lstm",
            num_epochs=1,
            batch_size=4
        )
        
        trainer = PearlTimeSeriesTrainer(config)
        print("  ✅ Time series trainer initialization successful")
        return True
        
    except Exception as e:
        print(f"  ❌ Time series training fix failed: {e}")
        return False

def test_rl_training_fix():
    """تست اصلاح RL Training"""
    print("🤖 Testing RL Training Fix...")
    try:
        from training.train_rl import RLTrainingConfig, PearlRLTrainer
        
        config = RLTrainingConfig(
            model_name="TestRLFix",
            algorithm="dqn",
            state_dim=20,  # Correct dimension
            action_dim=3,
            num_episodes=2
        )
        
        trainer = PearlRLTrainer(config)
        print("  ✅ RL trainer initialization successful")
        return True
        
    except Exception as e:
        print(f"  ❌ RL training fix failed: {e}")
        return False

def test_training_integration_fix():
    """تست اصلاح Training Integration"""
    print("🔗 Testing Training Integration Fix...")
    try:
        from training.train_sentiment import SentimentTrainingConfig
        from training.train_timeseries import TimeSeriesTrainingConfig
        from training.train_rl import RLTrainingConfig
        
        # Test all configs with proper parameters
        sentiment_config = SentimentTrainingConfig("TestSentiment", num_epochs=1)
        timeseries_config = TimeSeriesTrainingConfig("TestTimeSeries", num_epochs=1)
        rl_config = RLTrainingConfig(
            model_name="TestRL",
            algorithm="dqn",
            state_dim=20,
            action_dim=3,
            num_episodes=2
        )
        
        print("  ✅ All training configs created successfully")
        return True
        
    except Exception as e:
        print(f"  ❌ Training integration fix failed: {e}")
        return False

def main():
    """اجرای تست‌های نهایی"""
    print("🧪 FINAL FIXES TEST - Pearl-3x7B")
    print("=" * 50)
    
    tests = [
        ("Memory Leak Detection", test_memory_leak_detection),
        ("Structured Logging", test_structured_logging),
        ("Concurrent Operations", test_concurrent_operations),
        ("Time Series Training Fix", test_timeseries_training_fix),
        ("RL Training Fix", test_rl_training_fix),
        ("Training Integration Fix", test_training_integration_fix)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
        
        print()  # Empty line between tests
    
    # Summary
    print("📊 FINAL TEST RESULTS")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    success_rate = (passed / total) * 100
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Success Rate: {success_rate:.1f}% ({passed}/{total})")
    
    if success_rate >= 80:
        print("🎉 All major fixes are working correctly!")
    elif success_rate >= 60:
        print("⚠️ Most fixes are working, minor issues remain")
    else:
        print("❌ Significant issues still exist")
    
    return success_rate

if __name__ == "__main__":
    success_rate = main()
    exit(0 if success_rate >= 80 else 1)
