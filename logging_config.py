# -*- coding: utf-8 -*-
"""
Logging Configuration
تنظیمات logging برای سیستم
"""

import logging
import warnings
from datetime import datetime

# Suppress warnings
warnings.filterwarnings('ignore')

# Configure logging
def setup_logging():
    """تنظیم logging سیستم"""
    
    # Create custom formatter
    class CustomFormatter(logging.Formatter):
        """فرمت‌کننده سفارشی برای log"""
        
        def format(self, record):
            # Custom format based on log level
            if record.levelno == logging.INFO:
                if "✅" in record.getMessage():
                    return f"✅ {record.getMessage()}"
                elif "⚠️" in record.getMessage():
                    return f"⚠️ {record.getMessage()}"
                elif "❌" in record.getMessage():
                    return f"❌ {record.getMessage()}"
                else:
                    return f"ℹ️ {record.getMessage()}"
            elif record.levelno == logging.WARNING:
                return f"⚠️ WARNING: {record.getMessage()}"
            elif record.levelno == logging.ERROR:
                return f"❌ ERROR: {record.getMessage()}"
            else:
                return super().format(record)
    
    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        format='%(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    
    # Apply custom formatter to all handlers
    for handler in logging.root.handlers:
        handler.setFormatter(CustomFormatter())
    
    # Suppress specific loggers
    logging.getLogger('transformers').setLevel(logging.ERROR)
    logging.getLogger('urllib3').setLevel(logging.ERROR)
    logging.getLogger('requests').setLevel(logging.ERROR)
    logging.getLogger('cvxpy').setLevel(logging.ERROR)
    logging.getLogger('spacy').setLevel(logging.ERROR)
    
    print("✅ Logging configuration loaded")

# Initialize logging
setup_logging()
