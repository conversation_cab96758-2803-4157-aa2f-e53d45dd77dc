# مستندات سیستم هوش مصنوعی قابل تفسیر (ExplainableAI)

## 📋 معرفی کلی

سیستم **ExplainableAI** یکی از ماژول‌های پیشرفته و فعال پروژه است که امکان تفسیر و توضیح تصمیمات مدل‌های یادگیری تقویتی را فراهم می‌کند. این ماژول از تکنیک‌های مختلف مانند SHAP، Feature Importance، و Attention Visualization برای شفاف‌سازی فرآیند تصمیم‌گیری استفاده می‌کند.

## 🎯 مسئولیت‌های اصلی

### 1. تحلیل اهمیت ویژگی‌ها (Feature Importance)
- محاسبه اهمیت ویژگی‌ها با روش‌های مختلف
- تحلیل تأثیر هر ویژگی بر تصمیمات مدل
- مقایسه روش‌های Permutation، SHAP، و Gradient

### 2. توضیح تصمیمات (Decision Explanation)
- تفسیر دلیل تصمیمات مدل
- شناسایی ویژگی‌های تأثیرگذار
- تولید توضیحات قابل فهم

### 3. تحلیل رژیم بازار (Market Regime Analysis)
- تشخیص رژیم‌های مختلف بازار
- تحلیل تصمیمات در شرایط مختلف
- بررسی تطبیق‌پذیری مدل

### 4. تحلیل ریسک تصمیمات (Decision Risk Analysis)
- ارزیابی ریسک تصمیمات
- شناسایی نقاط پرریسک
- تحلیل رابطه ریسک-بازده

## 🔧 کلاس‌های اصلی

### ExplainableAI
کلاس اصلی سیستم که تمام قابلیت‌های تفسیر را ارائه می‌دهد.

```python
class ExplainableAI:
    def __init__(self, model=None, feature_names=None):
        self.model = model
        self.feature_names = feature_names
        self.explainer = None
        self.explanation_data = {}
```

## 📊 متدهای اصلی

### 1. feature_importance()
```python
def feature_importance(self, observations, actions=None, method='permutation', n_samples=100):
    """
    محاسبه اهمیت ویژگی‌ها در تصمیمات مدل
    
    پارامترها:
    - observations: مشاهدات ورودی
    - actions: اقدامات انجام شده
    - method: روش محاسبه ('permutation', 'shap', 'gradient')
    - n_samples: تعداد نمونه‌ها
    
    خروجی:
    - Dict[str, float]: اهمیت هر ویژگی
    """
```

### 2. decision_explanation()
```python
def decision_explanation(self, observation, action=None):
    """
    توضیح دلیل تصمیم مدل برای یک مشاهده خاص
    
    پارامترها:
    - observation: مشاهده ورودی
    - action: اقدام انجام شده
    
    خروجی:
    - Dict[str, Any]: توضیح تصمیم
    """
```

### 3. analyze_market_regime()
```python
def analyze_market_regime(self, observations, actions=None, market_data=None, n_regimes=3):
    """
    تحلیل رژیم بازار و تصمیمات مدل
    
    پارامترها:
    - observations: مشاهدات
    - actions: اقدامات
    - market_data: داده‌های بازار
    - n_regimes: تعداد رژیم‌ها
    
    خروجی:
    - Dict: نتایج تحلیل رژیم
    """
```

### 4. analyze_decision_risk()
```python
def analyze_decision_risk(self, observations, actions=None, returns=None):
    """
    تحلیل ریسک تصمیمات
    
    پارامترها:
    - observations: مشاهدات
    - actions: اقدامات
    - returns: بازده‌ها
    
    خروجی:
    - Dict: نتایج تحلیل ریسک
    """
```

## 🎨 متدهای تصویرسازی

### 1. visualize_feature_importance()
نمایش نمودار اهمیت ویژگی‌ها

### 2. visualize_decision_process()
نمایش فرآیند تصمیم‌گیری

### 3. create_decision_heatmap()
ایجاد نقشه حرارتی تصمیمات

### 4. visualize_decision_flow()
نمایش جریان تصمیم‌گیری

## 🔍 تحلیل‌های پیشرفته

### 1. counterfactual_analysis()
تحلیل شرایط فرضی (اگر چه اتفاقی می‌افتاد)

### 2. sensitivity_analysis()
تحلیل حساسیت تصمیمات

### 3. detect_bias()
تشخیص تعصب در تصمیمات

### 4. behavioral_anomaly_detection()
تشخیص ناهنجاری‌های رفتاری

## 📈 نمونه کد استفاده

```python
from utils.explainable_ai import ExplainableAI
import numpy as np

# ایجاد نمونه
explainer = ExplainableAI(model=your_model, feature_names=['price', 'volume', 'rsi'])

# محاسبه اهمیت ویژگی‌ها
observations = np.random.rand(100, 3)
importances = explainer.feature_importance(observations, method='permutation')
print("اهمیت ویژگی‌ها:", importances)

# توضیح تصمیم
explanation = explainer.decision_explanation(observations[0])
print("توضیح تصمیم:", explanation['explanation'])

# تحلیل رژیم بازار
market_analysis = explainer.analyze_market_regime(observations)
print("تحلیل رژیم بازار:", market_analysis)

# تصویرسازی
explainer.visualize_feature_importance(save_path='feature_importance.png')
explainer.visualize_decision_process(observations[0], save_path='decision_process.png')
```

## 🔧 تنظیمات و پیکربندی

### پارامترهای مهم:
- `model`: مدل یادگیری تقویتی
- `feature_names`: نام ویژگی‌ها
- `n_samples`: تعداد نمونه‌ها برای محاسبه
- `method`: روش محاسبه اهمیت

### روش‌های محاسبه اهمیت:
1. **Permutation**: جایگشت ویژگی‌ها
2. **SHAP**: مقادیر شپلی
3. **Gradient**: گرادیان‌های مدل

## 🚨 مدیریت خطا

```python
try:
    importances = explainer.feature_importance(observations)
except ValueError as e:
    print(f"خطا در محاسبه اهمیت: {e}")
except Exception as e:
    print(f"خطای غیرمنتظره: {e}")
```

## 📊 متریک‌های عملکرد

### 1. کیفیت توضیحات
- دقت توضیحات
- قابلیت فهم
- سازگاری

### 2. کارایی محاسباتی
- زمان محاسبه
- استفاده از حافظه
- مقیاس‌پذیری

## 🔗 اتصال به سیستم اصلی

### فایل‌های مرتبط:
- `utils/explainable_ai.py`: پیاده‌سازی اصلی
- `tests/test_explainable_ai.py`: تست‌های جامع
- `utils/rl_training_utils.py`: استفاده در آموزش
- `api/realtime_dashboard.py`: نمایش در داشبورد

### وضعیت عملیاتی:
✅ **فعال و عملیاتی** - این ماژول در سیستم‌های مختلف پروژه استفاده می‌شود

## 🎯 بهترین شیوه‌های استفاده

### 1. انتخاب روش مناسب
```python
# برای مدل‌های سریع
importances = explainer.feature_importance(data, method='permutation')

# برای دقت بالا
importances = explainer.feature_importance(data, method='shap')

# برای مدل‌های عمیق
importances = explainer.feature_importance(data, method='gradient')
```

### 2. تفسیر نتایج
```python
# مرتب‌سازی بر اساس اهمیت
sorted_features = sorted(importances.items(), key=lambda x: x[1], reverse=True)
print("مهم‌ترین ویژگی‌ها:", sorted_features[:5])
```

### 3. تصویرسازی
```python
# ذخیره نمودارها
explainer.visualize_feature_importance(save_path='importance.png', top_n=10)
explainer.compare_decisions(observations[:5], save_path='comparison.png')
```

## 🔮 نمودار معماری

```mermaid
graph TD
    A[ExplainableAI] --> B[Feature Importance]
    A --> C[Decision Explanation]
    A --> D[Market Regime Analysis]
    A --> E[Risk Analysis]
    
    B --> F[Permutation Method]
    B --> G[SHAP Method]
    B --> H[Gradient Method]
    
    C --> I[Top Features]
    C --> J[Explanation Text]
    
    D --> K[Regime Detection]
    D --> L[Regime Comparison]
    
    E --> M[Risk Metrics]
    E --> N[Critical Points]
    
    A --> O[Visualization]
    O --> P[Feature Charts]
    O --> Q[Decision Heatmaps]
    O --> R[Flow Diagrams]
```

## 📚 مراجع و منابع

- SHAP (SHapley Additive exPlanations)
- LIME (Local Interpretable Model-agnostic Explanations)  
- Permutation Feature Importance
- Attention Mechanisms in Deep Learning

---

**نکته**: این ماژول یکی از ماژول‌های کلیدی پروژه است که امکان تفسیر و درک بهتر تصمیمات مدل‌های یادگیری تقویتی را فراهم می‌کند. 