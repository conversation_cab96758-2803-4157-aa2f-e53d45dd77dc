#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نمایش آموزش مدل‌های یادگیری تقویتی
"""

import os
import sys
import warnings
import pandas as pd
import numpy as np
from datetime import datetime

# Suppress warnings
warnings.filterwarnings('ignore')

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.rl_trainer import RLTrainer
from utils.logger import setup_logger

def main():
    """تابع اصلی"""
    logger = setup_logger('rl_demo')
    
    logger.info("🚀 Starting RL Training Demo")
    logger.info("=" * 50)
    
    try:
        # Initialize RL Trainer
        trainer = RLTrainer()
        logger.info("✅ RL Trainer initialized")
        
        # Available symbols
        symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
        model_types = ['ppo', 'a2c']  # Start with simpler models
        
        logger.info(f"📊 Symbols: {symbols}")
        logger.info(f"🤖 Model types: {model_types}")
        
        # Test with EURUSD first
        symbol = 'EURUSD'
        model_type = 'ppo'
        
        logger.info(f"🎯 Training {model_type.upper()} for {symbol}")
        logger.info("-" * 30)
        
        # Train model
        training_result = trainer.train_model(
            model_type=model_type,
            symbol=symbol,
            total_timesteps=50000  # Start with smaller number for testing
        )
        
        logger.info(f"✅ Training completed: {training_result}")
        
        # Evaluate model
        logger.info(f"📈 Evaluating {model_type.upper()} for {symbol}")
        eval_result = trainer.evaluate_model(model_type, symbol, n_episodes=5)
        
        logger.info(f"📊 Evaluation results:")
        logger.info(f"   Mean reward: {eval_result['mean_reward']:.2f}")
        logger.info(f"   Std reward: {eval_result['std_reward']:.2f}")
        logger.info(f"   Mean length: {eval_result['mean_length']:.2f}")
        
        # Compare models for EURUSD
        logger.info(f"🔄 Comparing models for {symbol}")
        comparison = trainer.compare_models(symbol, model_types)
        
        logger.info(f"🏆 Model comparison:")
        for model_type, metrics in comparison.items():
            if 'error' not in metrics:
                logger.info(f"   {model_type.upper()}: {metrics['mean_reward']:.2f} ± {metrics['std_reward']:.2f}")
            else:
                logger.info(f"   {model_type.upper()}: Error - {metrics['error']}")
        
        if 'best_model' in comparison:
            best = comparison['best_model']
            logger.info(f"🥇 Best model: {best['model_type'].upper()} with reward {best['mean_reward']:.2f}")
        
        logger.info("=" * 50)
        logger.info("✅ RL Training Demo completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loading():
    """تست بارگذاری داده‌ها"""
    logger = setup_logger('data_test')
    
    try:
        trainer = RLTrainer()
        
        # Test data loading for different symbols
        symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
        
        for symbol in symbols:
            try:
                df = trainer.load_data(symbol)
                logger.info(f"✅ {symbol}: {len(df)} records loaded")
                logger.info(f"   Columns: {list(df.columns)}")
                logger.info(f"   Date range: {df['datetime'].min()} to {df['datetime'].max()}")
            except Exception as e:
                logger.error(f"❌ Failed to load {symbol}: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Data loading test failed: {e}")
        return False

def test_environment_creation():
    """تست ایجاد محیط"""
    logger = setup_logger('env_test')
    
    try:
        trainer = RLTrainer()
        
        # Load data
        df = trainer.load_data('EURUSD')
        
        # Create environment
        env = trainer.create_environment(df, 'EURUSD')
        
        logger.info(f"✅ Environment created successfully")
        logger.info(f"   Observation space: {env.observation_space}")
        logger.info(f"   Action space: {env.action_space}")
        
        # Test environment
        obs = env.reset()
        logger.info(f"   Initial observation shape: {obs.shape}")
        
        # Take a few steps
        for i in range(5):
            action = env.action_space.sample()
            obs, reward, done, info = env.step(action)
            logger.info(f"   Step {i+1}: reward={reward[0]:.4f}, done={done[0]}")
            if done[0]:
                break
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Environment test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧠 RL Training Demo")
    print("=" * 50)
    
    # Test data loading
    print("\n📊 Testing data loading...")
    if test_data_loading():
        print("✅ Data loading test passed")
    else:
        print("❌ Data loading test failed")
        exit(1)
    
    # Test environment creation
    print("\n🌍 Testing environment creation...")
    if test_environment_creation():
        print("✅ Environment test passed")
    else:
        print("❌ Environment test failed")
        exit(1)
    
    # Run main demo
    print("\n🚀 Running main demo...")
    if main():
        print("✅ Demo completed successfully!")
    else:
        print("❌ Demo failed!")
        exit(1) 