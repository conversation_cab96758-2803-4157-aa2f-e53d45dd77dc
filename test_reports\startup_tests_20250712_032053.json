{"type": "startup_tests", "timestamp": "2025-07-12T03:20:53.707030", "summary": {"total_tests": 4, "passed": 0, "failed": 4, "success_rate": 0.0, "total_duration": 4.79423189163208}, "results": [{"name": "test_advanced_risk_manager.py", "passed": false, "duration": 1.2816259860992432, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 391\nE       await asyncio.to_thread(env.stop)\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:20:50.179423"}, {"name": "test_smart_portfolio_manager.py", "passed": false, "duration": 0.9949071407318115, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 391\nE       await asyncio.to_thread(env.stop)\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:20:51.177326"}, {"name": "test_integrated_system.py", "passed": false, "duration": 1.069124698638916, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 391\nE       await asyncio.to_thread(env.stop)\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:20:52.250455"}, {"name": "test_integration_*.py", "passed": false, "duration": 1.4485740661621094, "output": "", "error": "ImportError while loading conftest 'D:\\project\\tests\\conftest.py'.\nC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py:50: in parse\n    return compile(source, filename, mode, flags,\nE     File \"D:\\project\\tests\\conftest.py\", line 391\nE       await asyncio.to_thread(env.stop)\nE   IndentationError: unexpected indent\n", "timestamp": "2025-07-12T03:20:53.703029"}]}