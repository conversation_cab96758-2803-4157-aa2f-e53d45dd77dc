#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 Order Management System Test
تست سیستم مدیریت سفارشات
"""

import os
import sys
import time
from decimal import Decimal
from datetime import datetime, timedelta

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_order_management():
    """تست سیستم مدیریت سفارشات"""
    print("📊 Testing Advanced Order Management System")
    print("=" * 50)
    
    try:
        # Import the order manager
        from core.order_manager import (
            AdvancedOrderManager, 
            OrderType, 
            OrderSide, 
            OrderStatus,
            TimeInForce,
            trading_session
        )
        
        # Test 1: Basic Order Creation
        print("\n1️⃣ Testing Basic Order Creation...")
        order_manager = AdvancedOrderManager()
        
        # Create market order
        market_order = order_manager.create_order(
            symbol="EURUSD",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100000
        )
        print(f"   ✓ Market order created: {market_order.order_id}")
        print(f"   ✓ Order status: {market_order.status.value}")
        
        # Create limit order
        limit_order = order_manager.create_order(
            symbol="EURUSD",
            side=OrderSide.SELL,
            order_type=OrderType.LIMIT,
            quantity=50000,
            price=1.1000
        )
        print(f"   ✓ Limit order created: {limit_order.order_id}")
        
        # Test 2: Order Submission
        print("\n2️⃣ Testing Order Submission...")
        
        submitted1 = order_manager.submit_order(market_order.order_id)
        submitted2 = order_manager.submit_order(limit_order.order_id)
        
        print(f"   ✓ Market order submitted: {submitted1}")
        print(f"   ✓ Limit order submitted: {submitted2}")
        
        # Test 3: Order Execution
        print("\n3️⃣ Testing Order Execution...")
        
        # Fill market order
        filled = order_manager.fill_order(
            market_order.order_id, 
            fill_price=1.0950, 
            fill_quantity=100000,
            commission=5.0
        )
        print(f"   ✓ Market order filled: {filled}")
        
        # Check order status
        updated_order = order_manager.get_order(market_order.order_id)
        print(f"   ✓ Order fill percentage: {updated_order.get_fill_percentage():.1f}%")
        print(f"   ✓ Average fill price: {updated_order.avg_fill_price}")
        
        # Test 4: Position Management
        print("\n4️⃣ Testing Position Management...")
        
        position = order_manager.get_position("EURUSD")
        if position:
            print(f"   ✓ Position created: {position.side.value}")
            print(f"   ✓ Position quantity: {position.quantity}")
            print(f"   ✓ Entry price: {position.entry_price}")
            print(f"   ✓ Current PnL: {position.get_total_pnl()}")
            print(f"   ✓ ROI: {position.get_roi():.2f}%")
        
        # Test 5: Market Price Updates
        print("\n5️⃣ Testing Market Price Updates...")
        
        # Update market price
        order_manager.update_market_price("EURUSD", 1.1000)
        
        # Check updated position
        updated_position = order_manager.get_position("EURUSD")
        if updated_position:
            print(f"   ✓ Updated unrealized PnL: {updated_position.unrealized_pnl}")
            print(f"   ✓ Updated ROI: {updated_position.get_roi():.2f}%")
        
        # Test 6: Order Cancellation
        print("\n6️⃣ Testing Order Cancellation...")
        
        # Cancel limit order
        cancelled = order_manager.cancel_order(limit_order.order_id)
        cancelled_order = order_manager.get_order(limit_order.order_id)
        
        print(f"   ✓ Order cancelled: {cancelled}")
        print(f"   ✓ Order status: {cancelled_order.status.value}")
        
        # Test 7: Multiple Orders and Partial Fills
        print("\n7️⃣ Testing Multiple Orders and Partial Fills...")
        
        # Create large order
        large_order = order_manager.create_order(
            symbol="GBPUSD",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=200000
        )
        
        order_manager.submit_order(large_order.order_id)
        
        # Partial fill 1
        order_manager.fill_order(large_order.order_id, 1.2500, 100000, 5.0)
        # Partial fill 2
        order_manager.fill_order(large_order.order_id, 1.2520, 100000, 5.0)
        
        # Check partial fills
        partial_order = order_manager.get_order(large_order.order_id)
        print(f"   ✓ Partial fills: {len(partial_order.fills)}")
        print(f"   ✓ Fill percentage: {partial_order.get_fill_percentage():.1f}%")
        print(f"   ✓ Average fill price: {partial_order.avg_fill_price}")
        
        # Test 8: Order History and Statistics
        print("\n8️⃣ Testing Order History and Statistics...")
        
        # Get active orders
        active_orders = order_manager.get_active_orders()
        print(f"   ✓ Active orders: {len(active_orders)}")
        
        # Get order history
        order_history = order_manager.get_order_history()
        print(f"   ✓ Order history: {len(order_history)}")
        
        # Get trade history
        trade_history = order_manager.get_trade_history()
        print(f"   ✓ Trade history: {len(trade_history)}")
        
        # Get statistics
        stats = order_manager.get_statistics()
        print(f"   ✓ Total orders: {stats['total_orders']}")
        print(f"   ✓ Filled orders: {stats['filled_orders']}")
        print(f"   ✓ Total volume: {stats['total_volume']}")
        print(f"   ✓ Total commission: {stats['total_commission']}")
        print(f"   ✓ Active positions: {stats['active_positions']}")
        print(f"   ✓ Total PnL: {stats['total_pnl']}")
        
        # Test 9: Trading Session Context
        print("\n9️⃣ Testing Trading Session Context...")
        
        with trading_session("USDJPY") as session_manager:
            # Create order in session
            session_order = session_manager.create_order(
                symbol="USDJPY",
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                quantity=100000
            )
            
            session_manager.submit_order(session_order.order_id)
            session_manager.fill_order(session_order.order_id, 110.50, 100000)
            
            print(f"   ✓ Session order created and filled: {session_order.order_id}")
        
        # Test 10: Order Validation
        print("\n🔟 Testing Order Validation...")
        
        # Test invalid order
        try:
            invalid_order = order_manager.create_order(
                symbol="INVALID",
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                quantity=0  # Invalid quantity
            )
            print(f"   ❌ Should have failed: {invalid_order.order_id}")
        except Exception as e:
            print(f"   ✓ Invalid order rejected: {str(e)[:50]}...")
        
        # Test 11: Export Trading Report
        print("\n1️⃣1️⃣ Testing Report Export...")
        
        try:
            report_file = order_manager.export_trading_report()
            if report_file and os.path.exists(report_file):
                print(f"   ✓ Report exported: {report_file}")
                # Clean up
                os.remove(report_file)
                print(f"   ✓ Report file cleaned up")
            else:
                print(f"   ⚠️ Report export failed or file not found")
        except Exception as e:
            print(f"   ⚠️ Report export error: {e}")
        
        # Final statistics
        final_stats = order_manager.get_statistics()
        all_positions = order_manager.get_all_positions()
        
        print(f"\n📊 Final Statistics:")
        print(f"   Total orders: {final_stats['total_orders']}")
        print(f"   Filled orders: {final_stats['filled_orders']}")
        print(f"   Cancelled orders: {final_stats['cancelled_orders']}")
        print(f"   Total volume: {final_stats['total_volume']}")
        print(f"   Active positions: {len([p for p in all_positions.values() if p.side.value != 'flat'])}")
        print(f"   Total PnL: {final_stats['total_pnl']}")
        
        # Stop cleanup
        order_manager.stop_cleanup()
        print(f"   ✓ Cleanup stopped")
        
        print("\n✅ All Order Management tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_advanced_order_types():
    """تست انواع پیشرفته سفارشات"""
    print("\n🚀 Testing Advanced Order Types...")
    
    try:
        from core.order_manager import (
            AdvancedOrderManager, 
            OrderType, 
            OrderSide,
            TimeInForce
        )
        
        order_manager = AdvancedOrderManager()
        
        # Test Stop Order
        print("\n1. Testing Stop Order...")
        stop_order = order_manager.create_order(
            symbol="EURUSD",
            side=OrderSide.SELL,
            order_type=OrderType.STOP,
            quantity=100000,
            stop_price=1.0900
        )
        print(f"   ✓ Stop order created: {stop_order.order_id}")
        
        # Test Stop Limit Order
        print("\n2. Testing Stop Limit Order...")
        stop_limit_order = order_manager.create_order(
            symbol="EURUSD",
            side=OrderSide.BUY,
            order_type=OrderType.STOP_LIMIT,
            quantity=100000,
            price=1.1000,
            stop_price=1.0950
        )
        print(f"   ✓ Stop limit order created: {stop_limit_order.order_id}")
        
        # Test Time In Force
        print("\n3. Testing Time In Force...")
        ioc_order = order_manager.create_order(
            symbol="GBPUSD",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=50000,
            price=1.2500,
            time_in_force=TimeInForce.IOC
        )
        print(f"   ✓ IOC order created: {ioc_order.order_id}")
        
        # Test GTD Order
        print("\n4. Testing GTD Order...")
        expires_at = datetime.now() + timedelta(hours=1)
        gtd_order = order_manager.create_order(
            symbol="USDJPY",
            side=OrderSide.SELL,
            order_type=OrderType.LIMIT,
            quantity=100000,
            price=111.00,
            time_in_force=TimeInForce.GTD,
            expires_at=expires_at
        )
        print(f"   ✓ GTD order created: {gtd_order.order_id}")
        print(f"   ✓ Expires at: {gtd_order.expires_at}")
        
        print("\n✅ Advanced order types test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Advanced order types test error: {e}")
        return False

def test_position_management():
    """تست مدیریت پوزیشن"""
    print("\n💼 Testing Position Management...")
    
    try:
        from core.order_manager import (
            AdvancedOrderManager, 
            OrderType, 
            OrderSide,
            PositionSide
        )
        
        order_manager = AdvancedOrderManager()
        
        # Test Long Position
        print("\n1. Testing Long Position...")
        long_order = order_manager.create_order(
            symbol="XAUUSD",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=10
        )
        
        order_manager.submit_order(long_order.order_id)
        order_manager.fill_order(long_order.order_id, 1950.00, 10)
        
        position = order_manager.get_position("XAUUSD")
        print(f"   ✓ Long position: {position.side.value} {position.quantity}")
        print(f"   ✓ Entry price: {position.entry_price}")
        
        # Update price and check PnL
        order_manager.update_market_price("XAUUSD", 1960.00)
        print(f"   ✓ Unrealized PnL: {position.unrealized_pnl}")
        print(f"   ✓ ROI: {position.get_roi():.2f}%")
        
        # Test Position Reduction
        print("\n2. Testing Position Reduction...")
        reduce_order = order_manager.create_order(
            symbol="XAUUSD",
            side=OrderSide.SELL,
            order_type=OrderType.MARKET,
            quantity=5
        )
        
        order_manager.submit_order(reduce_order.order_id)
        order_manager.fill_order(reduce_order.order_id, 1965.00, 5)
        
        updated_position = order_manager.get_position("XAUUSD")
        print(f"   ✓ Reduced position: {updated_position.quantity}")
        print(f"   ✓ Realized PnL: {updated_position.realized_pnl}")
        print(f"   ✓ Total PnL: {updated_position.get_total_pnl()}")
        
        # Test Position Reversal
        print("\n3. Testing Position Reversal...")
        reverse_order = order_manager.create_order(
            symbol="XAUUSD",
            side=OrderSide.SELL,
            order_type=OrderType.MARKET,
            quantity=15  # More than remaining position
        )
        
        order_manager.submit_order(reverse_order.order_id)
        order_manager.fill_order(reverse_order.order_id, 1970.00, 15)
        
        reversed_position = order_manager.get_position("XAUUSD")
        print(f"   ✓ Reversed position: {reversed_position.side.value} {reversed_position.quantity}")
        print(f"   ✓ New entry price: {reversed_position.entry_price}")
        
        print("\n✅ Position management test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Position management test error: {e}")
        return False

def main():
    """تست اصلی"""
    print("📊 Order Management System Test Suite")
    print("=" * 60)
    
    # Run tests
    test_results = []
    
    # Test 1: Basic functionality
    test_results.append(test_order_management())
    
    # Test 2: Advanced order types
    test_results.append(test_advanced_order_types())
    
    # Test 3: Position management
    test_results.append(test_position_management())
    
    # Results summary
    print("\n📊 Test Results Summary:")
    print("=" * 30)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ Tests passed: {passed}/{total}")
    print(f"📈 Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! Order Management System is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 