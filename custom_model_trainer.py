"""
🔥 Pearl-3x7B Custom Model Trainer
مربی مدل‌ها با دیتاهای شما

استفاده از دیتاهای واقعی شما و اندیکاتورهای پیشرفته
"""

import os
import sys
import time
import json
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, f1_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

class CustomFinBERTTrainer:
    """🤗 مربی FinBERT با دیتاهای شما"""
    
    def __init__(self, sentiment_data: pd.DataFrame):
        self.sentiment_data = sentiment_data
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🤗 Custom FinBERT Trainer initialized on {self.device}")
    
    def train_with_your_data(self) -> Dict[str, Any]:
        """آموزش FinBERT با دیتاهای احساسات شما"""
        print("🤗 TRAINING FINBERT WITH YOUR DATA")
        print("=" * 45)
        
        try:
            # Install transformers if needed
            os.system("pip install transformers torch-audio")
            
            from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments
            
            # Prepare your data
            texts = self.sentiment_data['text'].tolist()
            
            # Create sentiment labels using simple heuristics or existing labels
            if 'sentiment' in self.sentiment_data.columns:
                # Use existing sentiment labels
                labels = self.sentiment_data['sentiment'].tolist()
                print(f"   📊 Using existing sentiment labels")
            else:
                # Create labels using keyword analysis
                labels = self._create_sentiment_labels(texts)
                print(f"   📊 Created sentiment labels using keyword analysis")
            
            print(f"   📊 Total samples: {len(texts)}")
            
            # Label distribution
            label_counts = pd.Series(labels).value_counts()
            print(f"   📊 Label distribution: {dict(label_counts)}")
            
            # Split data
            train_texts, val_texts, train_labels, val_labels = train_test_split(
                texts, labels, test_size=0.2, random_state=42, stratify=labels
            )
            
            print(f"   📊 Train samples: {len(train_texts)}")
            print(f"   📊 Validation samples: {len(val_texts)}")
            
            # Load pre-trained model
            model_name = "ProsusAI/finbert"
            print(f"   🤗 Loading {model_name}...")
            
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForSequenceClassification.from_pretrained(
                model_name, 
                num_labels=len(set(labels)),
                ignore_mismatched_sizes=True
            ).to(self.device)
            
            # Tokenize data
            print("   🔤 Tokenizing your texts...")
            train_encodings = tokenizer(train_texts, truncation=True, padding=True, max_length=512, return_tensors='pt')
            val_encodings = tokenizer(val_texts, truncation=True, padding=True, max_length=512, return_tensors='pt')
            
            # Create datasets
            class CustomSentimentDataset(torch.utils.data.Dataset):
                def __init__(self, encodings, labels):
                    self.encodings = encodings
                    self.labels = labels
                
                def __getitem__(self, idx):
                    item = {key: val[idx] for key, val in self.encodings.items()}
                    item['labels'] = torch.tensor(self.labels[idx], dtype=torch.long)
                    return item
                
                def __len__(self):
                    return len(self.labels)
            
            train_dataset = CustomSentimentDataset(train_encodings, train_labels)
            val_dataset = CustomSentimentDataset(val_encodings, val_labels)
            
            # Training arguments
            training_args = TrainingArguments(
                output_dir='/content/custom_finbert_results',
                num_train_epochs=3,
                per_device_train_batch_size=8,
                per_device_eval_batch_size=8,
                warmup_steps=100,
                weight_decay=0.01,
                logging_dir='/content/custom_finbert_logs',
                logging_steps=50,
                evaluation_strategy="steps",
                eval_steps=200,
                save_steps=500,
                load_best_model_at_end=True,
                metric_for_best_model="eval_loss",
                greater_is_better=False,
            )
            
            # Initialize trainer
            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset,
                tokenizer=tokenizer,
            )
            
            # Start training
            print("🔥 Starting FinBERT training with your data...")
            start_time = time.time()
            
            trainer.train()
            
            training_time = time.time() - start_time
            
            # Evaluate
            print("📊 Evaluating model...")
            eval_results = trainer.evaluate()
            
            # Test predictions
            model.eval()
            predictions = []
            true_labels = []
            
            with torch.no_grad():
                for i in range(0, len(val_texts), 8):
                    batch_texts = val_texts[i:i+8]
                    batch_labels = val_labels[i:i+8]
                    
                    inputs = tokenizer(batch_texts, truncation=True, padding=True, max_length=512, return_tensors='pt').to(self.device)
                    outputs = model(**inputs)
                    
                    batch_predictions = torch.argmax(outputs.logits, dim=-1).cpu().numpy()
                    predictions.extend(batch_predictions)
                    true_labels.extend(batch_labels)
            
            # Calculate metrics
            accuracy = accuracy_score(true_labels, predictions)
            f1 = f1_score(true_labels, predictions, average='weighted')
            
            # Save model
            model_save_path = f"/content/models/custom_finbert_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(model_save_path, exist_ok=True)
            
            model.save_pretrained(model_save_path)
            tokenizer.save_pretrained(model_save_path)
            
            print(f"✅ Custom FinBERT training completed!")
            print(f"   Training time: {training_time/3600:.2f} hours")
            print(f"   Accuracy: {accuracy:.4f}")
            print(f"   F1 Score: {f1:.4f}")
            print(f"   Model saved: {model_save_path}")
            
            return {
                "success": True,
                "model_name": "Custom_FinBERT",
                "training_time_hours": training_time / 3600,
                "accuracy": accuracy,
                "f1_score": f1,
                "eval_loss": eval_results.get('eval_loss', 0),
                "model_path": model_save_path,
                "train_samples": len(train_texts),
                "val_samples": len(val_texts),
                "data_source": "your_custom_data"
            }
            
        except Exception as e:
            print(f"❌ Custom FinBERT training failed: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    def _create_sentiment_labels(self, texts: List[str]) -> List[int]:
        """ایجاد برچسب‌های احساسات با تحلیل کلمات کلیدی"""
        
        positive_words = ['good', 'great', 'excellent', 'positive', 'up', 'rise', 'gain', 'profit', 'bull', 'buy']
        negative_words = ['bad', 'terrible', 'negative', 'down', 'fall', 'loss', 'bear', 'sell', 'crash', 'drop']
        
        labels = []
        
        for text in texts:
            text_lower = text.lower()
            
            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)
            
            if positive_count > negative_count:
                labels.append(2)  # Positive
            elif negative_count > positive_count:
                labels.append(0)  # Negative
            else:
                labels.append(1)  # Neutral
        
        return labels

class CustomLSTMTrainer:
    """📈 مربی LSTM با دیتاهای سری زمانی شما"""
    
    def __init__(self, timeseries_data: pd.DataFrame):
        self.timeseries_data = timeseries_data
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"📈 Custom LSTM Trainer initialized on {self.device}")
    
    def train_with_your_data(self) -> Dict[str, Any]:
        """آموزش LSTM با دیتاهای سری زمانی شما"""
        print("📈 TRAINING LSTM WITH YOUR TIME SERIES DATA")
        print("=" * 50)
        
        try:
            # Find price column
            price_col = None
            for col in self.timeseries_data.columns:
                if any(keyword in col.lower() for keyword in ['close', 'price', 'value']):
                    price_col = col
                    break
            
            if price_col is None:
                return {"success": False, "error": "No price column found"}
            
            # Select features (numeric columns only)
            feature_cols = []
            for col in self.timeseries_data.columns:
                if (self.timeseries_data[col].dtype in ['float64', 'int64'] and 
                    not self.timeseries_data[col].isna().all()):
                    feature_cols.append(col)
            
            if len(feature_cols) < 3:
                return {"success": False, "error": "Not enough numeric features"}
            
            print(f"   📊 Using {len(feature_cols)} features")
            print(f"   📊 Target column: {price_col}")
            
            # Clean data
            data = self.timeseries_data[feature_cols].dropna()
            print(f"   📊 Clean data: {len(data)} records")
            
            if len(data) < 100:
                return {"success": False, "error": "Not enough clean data"}
            
            # Normalize data
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(data)
            
            # Create sequences
            sequence_length = min(60, len(data) // 10)  # Adaptive sequence length
            X, y = [], []
            
            target_idx = feature_cols.index(price_col)
            
            for i in range(sequence_length, len(scaled_data)):
                X.append(scaled_data[i-sequence_length:i])
                y.append(scaled_data[i, target_idx])
            
            X, y = np.array(X), np.array(y)
            
            print(f"   📊 Sequences: {X.shape}")
            print(f"   📊 Targets: {y.shape}")
            
            # Split data
            split_idx = int(0.8 * len(X))
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # Convert to tensors
            X_train = torch.FloatTensor(X_train).to(self.device)
            y_train = torch.FloatTensor(y_train).to(self.device)
            X_test = torch.FloatTensor(X_test).to(self.device)
            y_test = torch.FloatTensor(y_test).to(self.device)
            
            # Define LSTM model
            class CustomLSTM(nn.Module):
                def __init__(self, input_size, hidden_size=128, num_layers=3, dropout=0.2):
                    super(CustomLSTM, self).__init__()
                    self.hidden_size = hidden_size
                    self.num_layers = num_layers
                    
                    self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                                       batch_first=True, dropout=dropout)
                    self.attention = nn.MultiheadAttention(hidden_size, num_heads=8, batch_first=True)
                    self.fc1 = nn.Linear(hidden_size, 64)
                    self.fc2 = nn.Linear(64, 1)
                    self.dropout = nn.Dropout(dropout)
                    self.relu = nn.ReLU()
                
                def forward(self, x):
                    lstm_out, _ = self.lstm(x)
                    
                    # Apply attention
                    attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
                    
                    # Use last time step
                    out = attn_out[:, -1, :]
                    out = self.dropout(out)
                    out = self.relu(self.fc1(out))
                    out = self.dropout(out)
                    out = self.fc2(out)
                    
                    return out
            
            # Initialize model
            model = CustomLSTM(input_size=len(feature_cols)).to(self.device)
            criterion = nn.MSELoss()
            optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
            
            print(f"   🧠 Model parameters: {sum(p.numel() for p in model.parameters()):,}")
            
            # Training loop
            print("🔥 Starting LSTM training with your data...")
            start_time = time.time()
            
            batch_size = 32
            num_epochs = 100
            best_loss = float('inf')
            patience_counter = 0
            
            for epoch in range(num_epochs):
                model.train()
                total_loss = 0
                
                # Mini-batch training
                for i in range(0, len(X_train), batch_size):
                    batch_X = X_train[i:i+batch_size]
                    batch_y = y_train[i:i+batch_size]
                    
                    optimizer.zero_grad()
                    outputs = model(batch_X)
                    loss = criterion(outputs.squeeze(), batch_y)
                    loss.backward()
                    
                    # Gradient clipping
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    
                    optimizer.step()
                    total_loss += loss.item()
                
                avg_loss = total_loss / (len(X_train) // batch_size)
                
                # Validation
                model.eval()
                with torch.no_grad():
                    val_outputs = model(X_test)
                    val_loss = criterion(val_outputs.squeeze(), y_test).item()
                
                scheduler.step(val_loss)
                
                # Early stopping
                if val_loss < best_loss:
                    best_loss = val_loss
                    patience_counter = 0
                    # Save best model
                    torch.save(model.state_dict(), '/content/best_custom_lstm_model.pth')
                else:
                    patience_counter += 1
                
                if epoch % 10 == 0:
                    print(f"   Epoch {epoch:3d}: Train Loss: {avg_loss:.6f}, Val Loss: {val_loss:.6f}")
                
                if patience_counter >= 20:
                    print(f"   Early stopping at epoch {epoch}")
                    break
            
            training_time = time.time() - start_time
            
            # Load best model and evaluate
            model.load_state_dict(torch.load('/content/best_custom_lstm_model.pth'))
            model.eval()
            
            with torch.no_grad():
                train_pred = model(X_train)
                test_pred = model(X_test)
                
                train_rmse = torch.sqrt(criterion(train_pred.squeeze(), y_train)).item()
                test_rmse = torch.sqrt(criterion(test_pred.squeeze(), y_test)).item()
            
            # Save model
            model_save_path = f"/content/models/custom_lstm_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(model_save_path, exist_ok=True)
            
            torch.save({
                'model_state_dict': model.state_dict(),
                'scaler': scaler,
                'features': feature_cols,
                'sequence_length': sequence_length,
                'target_column': price_col
            }, f"{model_save_path}/custom_lstm_model.pth")
            
            print(f"✅ Custom LSTM training completed!")
            print(f"   Training time: {training_time/3600:.2f} hours")
            print(f"   Train RMSE: {train_rmse:.6f}")
            print(f"   Test RMSE: {test_rmse:.6f}")
            print(f"   Model saved: {model_save_path}")
            
            return {
                "success": True,
                "model_name": "Custom_LSTM",
                "training_time_hours": training_time / 3600,
                "train_rmse": train_rmse,
                "test_rmse": test_rmse,
                "model_path": model_save_path,
                "train_samples": len(X_train),
                "test_samples": len(X_test),
                "features": feature_cols,
                "target_column": price_col,
                "data_source": "your_custom_data"
            }
            
        except Exception as e:
            print(f"❌ Custom LSTM training failed: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}

class CustomDQNTrainer:
    """🤖 مربی DQN با دیتاهای معاملاتی شما"""

    def __init__(self, trading_data: Dict[str, Any]):
        self.trading_data = trading_data
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🤖 Custom DQN Trainer initialized on {self.device}")

    def train_with_your_data(self) -> Dict[str, Any]:
        """آموزش DQN با دیتاهای معاملاتی شما"""
        print("🤖 TRAINING DQN WITH YOUR TRADING DATA")
        print("=" * 45)

        try:
            features = self.trading_data['features']
            rewards = self.trading_data['rewards']
            actions = self.trading_data['actions']

            print(f"   📊 Trading samples: {len(features)}")
            print(f"   📊 Feature dimension: {features.shape[1]}")
            print(f"   📊 Unique actions: {len(np.unique(actions))}")

            # Define DQN network
            class CustomDQN(nn.Module):
                def __init__(self, state_size, action_size, hidden_size=256):
                    super(CustomDQN, self).__init__()
                    self.network = nn.Sequential(
                        nn.Linear(state_size, hidden_size),
                        nn.ReLU(),
                        nn.Dropout(0.2),
                        nn.Linear(hidden_size, hidden_size),
                        nn.ReLU(),
                        nn.Dropout(0.2),
                        nn.Linear(hidden_size, hidden_size // 2),
                        nn.ReLU(),
                        nn.Linear(hidden_size // 2, action_size)
                    )

                def forward(self, x):
                    return self.network(x)

            # Initialize networks
            state_size = features.shape[1]
            action_size = 3  # Hold, Buy, Sell

            main_network = CustomDQN(state_size, action_size).to(self.device)
            target_network = CustomDQN(state_size, action_size).to(self.device)
            target_network.load_state_dict(main_network.state_dict())

            optimizer = optim.Adam(main_network.parameters(), lr=0.001)
            criterion = nn.MSELoss()

            print(f"   🧠 Network parameters: {sum(p.numel() for p in main_network.parameters()):,}")

            # Experience replay buffer
            class ReplayBuffer:
                def __init__(self, capacity=10000):
                    self.capacity = capacity
                    self.buffer = []
                    self.position = 0

                def push(self, state, action, reward, next_state, done):
                    if len(self.buffer) < self.capacity:
                        self.buffer.append(None)
                    self.buffer[self.position] = (state, action, reward, next_state, done)
                    self.position = (self.position + 1) % self.capacity

                def sample(self, batch_size):
                    return np.random.choice(self.buffer, batch_size, replace=False)

                def __len__(self):
                    return len(self.buffer)

            replay_buffer = ReplayBuffer(capacity=50000)

            # Training parameters
            epsilon = 1.0
            epsilon_decay = 0.995
            epsilon_min = 0.01
            gamma = 0.95
            batch_size = 64
            target_update_freq = 1000

            # Fill replay buffer with your data
            print("   📚 Filling replay buffer with your trading data...")
            for i in range(len(features) - 1):
                state = features[i]
                action = actions[i]
                reward = rewards[i]
                next_state = features[i + 1]
                done = (i == len(features) - 2)

                replay_buffer.push(state, action, reward, next_state, done)

            print(f"   📚 Replay buffer size: {len(replay_buffer)}")

            # Training loop
            print("🔥 Starting DQN training with your data...")
            start_time = time.time()

            num_episodes = 500  # Reduced for faster training
            episode_rewards = []
            losses = []

            for episode in range(num_episodes):
                # Sample batch from replay buffer
                if len(replay_buffer) < batch_size:
                    continue

                batch = replay_buffer.sample(batch_size)
                states = torch.FloatTensor([e[0] for e in batch]).to(self.device)
                actions_batch = torch.LongTensor([e[1] for e in batch]).to(self.device)
                rewards_batch = torch.FloatTensor([e[2] for e in batch]).to(self.device)
                next_states = torch.FloatTensor([e[3] for e in batch]).to(self.device)
                dones = torch.BoolTensor([e[4] for e in batch]).to(self.device)

                # Current Q values
                current_q_values = main_network(states).gather(1, actions_batch.unsqueeze(1))

                # Next Q values from target network
                next_q_values = target_network(next_states).max(1)[0].detach()
                target_q_values = rewards_batch + (gamma * next_q_values * ~dones)

                # Compute loss
                loss = criterion(current_q_values.squeeze(), target_q_values)

                # Optimize
                optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(main_network.parameters(), max_norm=1.0)
                optimizer.step()

                losses.append(loss.item())

                # Update target network
                if episode % target_update_freq == 0:
                    target_network.load_state_dict(main_network.state_dict())

                # Decay epsilon
                epsilon = max(epsilon_min, epsilon * epsilon_decay)

                # Calculate episode reward (simulate trading with your data)
                episode_reward = 0
                portfolio_value = 10000  # Start with $10,000

                for i in range(min(50, len(features) - 1)):  # Simulate 50 steps
                    state = torch.FloatTensor(features[i]).unsqueeze(0).to(self.device)

                    if np.random.random() > epsilon:
                        with torch.no_grad():
                            q_values = main_network(state)
                            action = q_values.max(1)[1].item()
                    else:
                        action = np.random.randint(0, action_size)

                    # Execute action and calculate reward
                    price_change = rewards[i]

                    if action == 1:  # Buy
                        episode_reward += price_change * portfolio_value
                    elif action == 2:  # Sell
                        episode_reward -= price_change * portfolio_value
                    # action == 0 is hold

                episode_rewards.append(episode_reward)

                if episode % 50 == 0:
                    avg_reward = np.mean(episode_rewards[-50:])
                    avg_loss = np.mean(losses[-50:]) if losses else 0
                    print(f"   Episode {episode:3d}: Avg Reward: {avg_reward:8.2f}, Avg Loss: {avg_loss:.6f}")

            training_time = time.time() - start_time

            # Evaluate final performance
            final_avg_reward = np.mean(episode_rewards[-50:])
            max_reward = max(episode_rewards) if episode_rewards else 0

            # Save model
            model_save_path = f"/content/models/custom_dqn_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(model_save_path, exist_ok=True)

            torch.save({
                'main_network_state_dict': main_network.state_dict(),
                'target_network_state_dict': target_network.state_dict(),
                'state_size': state_size,
                'action_size': action_size,
                'episode_rewards': episode_rewards,
                'feature_names': self.trading_data.get('feature_names', []),
                'price_column': self.trading_data.get('price_column', 'price')
            }, f"{model_save_path}/custom_dqn_model.pth")

            print(f"✅ Custom DQN training completed!")
            print(f"   Training time: {training_time/3600:.2f} hours")
            print(f"   Final avg reward: {final_avg_reward:.2f}")
            print(f"   Max reward: {max_reward:.2f}")
            print(f"   Model saved: {model_save_path}")

            return {
                "success": True,
                "model_name": "Custom_DQN",
                "training_time_hours": training_time / 3600,
                "final_avg_reward": final_avg_reward,
                "max_reward": max_reward,
                "total_episodes": num_episodes,
                "model_path": model_save_path,
                "feature_names": self.trading_data.get('feature_names', []),
                "data_source": "your_custom_data"
            }

        except Exception as e:
            print(f"❌ Custom DQN training failed: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}

def train_all_models_with_your_data(prepared_data: Dict[str, Any]) -> Dict[str, Any]:
    """آموزش همه مدل‌ها با دیتاهای شما"""
    print("🔥 TRAINING ALL MODELS WITH YOUR CUSTOM DATA")
    print("=" * 60)

    results = {}

    # 1. Train FinBERT with your sentiment data
    if prepared_data['sentiment_data'] is not None:
        print("\n🤗 Training Custom FinBERT...")
        finbert_trainer = CustomFinBERTTrainer(prepared_data['sentiment_data'])
        results['finbert'] = finbert_trainer.train_with_your_data()
    else:
        print("❌ No sentiment data available")
        results['finbert'] = {"success": False, "error": "No sentiment data"}

    # 2. Train LSTM with your time series data
    if prepared_data['timeseries_data'] is not None:
        print("\n📈 Training Custom LSTM...")
        lstm_trainer = CustomLSTMTrainer(prepared_data['timeseries_data'])
        results['lstm'] = lstm_trainer.train_with_your_data()
    else:
        print("❌ No time series data available")
        results['lstm'] = {"success": False, "error": "No time series data"}

    # 3. Train DQN with your trading data
    if prepared_data['trading_data'] is not None:
        print("\n🤖 Training Custom DQN...")
        dqn_trainer = CustomDQNTrainer(prepared_data['trading_data'])
        results['dqn'] = dqn_trainer.train_with_your_data()
    else:
        print("❌ No trading data available")
        results['dqn'] = {"success": False, "error": "No trading data"}

    # Summary
    successful = sum(1 for r in results.values() if r.get('success', False))
    total = len(results)

    print(f"\n🎉 CUSTOM DATA TRAINING SUMMARY:")
    print(f"   ✅ Successful: {successful}/{total}")

    for name, result in results.items():
        if result.get('success'):
            training_time = result.get('training_time_hours', 0)
            print(f"   ✅ {name.upper()}: {training_time:.2f}h - Trained with your data")
        else:
            print(f"   ❌ {name.upper()}: {result.get('error', 'Failed')}")

    return results

# Main execution
if __name__ == "__main__":
    print("🔥 CUSTOM MODEL TRAINER READY")
    print("First run: load_and_prepare_your_data()")
    print("Then run: train_all_models_with_your_data(prepared_data)")
