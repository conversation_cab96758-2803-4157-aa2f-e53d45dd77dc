# -*- coding: utf-8 -*-
"""
CVXPY Warnings Fix
حل هشدارهای CVXPY
"""

import warnings
import os

# Suppress CVXPY warnings
warnings.filterwarnings('ignore', category=UserWarning, message='.*CLARABEL.*')
warnings.filterwarnings('ignore', category=ImportWarning, message='.*CLARABEL.*')

# Set environment variable to disable CLARABEL
os.environ['CVXPY_CLARABEL_DISABLED'] = '1'

def setup_cvxpy():
    """تنظیم CVXPY برای عملکرد بهتر"""
    try:
        import cvxpy as cp
        
        # Set default solver to OSQP
        cp.settings.SOLVER = cp.OSQP
        
        # Configure solver settings
        cp.settings.VERBOSE = False
        cp.settings.WARN_DUALS = False
        
        print("✅ CVXPY configured successfully")
        return True
        
    except Exception as e:
        print(f"❌ CVXPY configuration failed: {e}")
        return False

# Initialize CVXPY
setup_cvxpy()
