#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تست یکپارچه سیستم Backtesting با Sentiment Analysis
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# تنظیم پروکسی
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:10809'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:10809'

# اضافه کردن مسیر پروژه
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.backtesting_framework import SentimentBacktestingFramework
from utils.offline_sentiment_analyzer import SentimentAnalyzer

# تنظیم لاگینگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_sample_data():
    """ایجاد داده‌های نمونه برای تست"""
    dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='H')
    
    # ایجاد داده‌های قیمت تصادفی
    np.random.seed(42)
    base_price = 1.1000
    returns = np.random.normal(0, 0.001, len(dates))
    prices = [base_price]
    
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(new_price)
    
    # ایجاد OHLCV
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        # ایجاد High, Low, Open, Close
        daily_volatility = np.random.uniform(0.0005, 0.002)
        high = price * (1 + daily_volatility)
        low = price * (1 - daily_volatility)
        open_price = np.random.uniform(low, high)
        close_price = np.random.uniform(low, high)
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'datetime': date,
            'Open': open_price,
            'High': high,
            'Low': low,
            'Close': close_price,
            'Volume': volume
        })
    
    df = pd.DataFrame(data)
    return df


def test_sentiment_analyzer():
    """تست تحلیلگر احساسات"""
    print("\n🧪 Testing Sentiment Analyzer...")
    print("=" * 50)
    
    analyzer = SentimentAnalyzer()
    
    test_texts = [
        "شرکت اپل سود بالایی گزارش داد و سهام آن رشد کرد",
        "بازار سهام امروز افت کرد و سرمایه‌گذاران نگران هستند",
        "اقتصاد جهانی در حال بهبود است و چشم‌انداز مثبتی دارد",
        "Apple reported strong profits and stock price increased",
        "Stock market fell today and investors are worried",
        "Global economy is improving with positive outlook"
    ]
    
    for text in test_texts:
        result = analyzer.analyze(text)
        print(f"📝 Text: {text[:50]}...")
        print(f"   Label: {result.label}, Score: {result.score:.3f}, Confidence: {result.confidence:.3f}")
        print()


def test_data_loading():
    """تست بارگذاری داده‌ها"""
    print("\n📊 Testing Data Loading...")
    print("=" * 50)
    
    # ایجاد داده‌های نمونه
    sample_df = create_sample_data()
    
    # ذخیره در فایل موقت
    temp_file = "temp_test_data.csv"
    sample_df.to_csv(temp_file, index=False)
    
    try:
        # تست فریم‌ورک
        framework = SentimentBacktestingFramework(initial_cash=100000)
        
        # بارگذاری داده‌ها
        success = framework.load_data(temp_file, "TEST")
        
        if success:
            print("✅ Data loading successful!")
            
            # تست اجرای backtest
            print("\n🚀 Running backtest...")
            results = framework.run_backtest()
            
            if results:
                print("✅ Backtest completed successfully!")
                print(f"💰 Initial: ${results['initial_value']:,.2f}")
                print(f"💰 Final: ${results['final_value']:,.2f}")
                print(f"📈 Return: {results['total_return']:.2f}%")
                
                # تولید گزارش
                report = framework.generate_report()
                print(f"\n📊 Report:\n{report}")
            else:
                print("❌ Backtest failed!")
        else:
            print("❌ Data loading failed!")
    
    finally:
        # پاک کردن فایل موقت
        if os.path.exists(temp_file):
            os.remove(temp_file)


def test_real_data():
    """تست با داده‌های واقعی"""
    print("\n📈 Testing with Real Data...")
    print("=" * 50)
    
    # بررسی فایل‌های موجود
    data_paths = [
        "data/EURUSD/H1.csv",
        "data/EURUSD/D1.csv",
        "data/GBPUSD/H1.csv",
        "data/USDJPY/H1.csv"
    ]
    
    for data_path in data_paths:
        if os.path.exists(data_path):
            print(f"🔍 Testing with: {data_path}")
            
            try:
                framework = SentimentBacktestingFramework(initial_cash=100000)
                success = framework.load_data(data_path, "REAL")
                
                if success:
                    print("✅ Data loaded successfully!")
                    
                    # اجرای backtest کوتاه
                    results = framework.run_backtest()
                    if results:
                        print(f"📈 Return: {results['total_return']:.2f}%")
                    else:
                        print("❌ Backtest failed!")
                else:
                    print("❌ Data loading failed!")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
            
            print()


def main():
    """تابع اصلی"""
    print("🚀 Comprehensive Backtesting Integration Test")
    print("=" * 60)
    
    # تست تحلیلگر احساسات
    test_sentiment_analyzer()
    
    # تست بارگذاری داده‌ها
    test_data_loading()
    
    # تست با داده‌های واقعی
    test_real_data()
    
    print("\n✅ All tests completed!")


if __name__ == "__main__":
    main() 