import os
import tempfile
from utils.cleanup import cleanup_old_models

def test_cleanup_old_models():
    with tempfile.TemporaryDirectory() as tmpdir:
        # ایجاد فایل‌های تست مدل
        paths = []
        for i in range(5):
            f = os.path.join(tmpdir, f"model_{i}.zip")
            with open(f, "w") as fp:
                fp.write("test")
            paths.append(f)
        cleanup_old_models([tmpdir], keep_last=2)
        remaining = [f for f in os.listdir(tmpdir) if f.endswith('.zip')]
        assert len(remaining) == 2
