import pandas as pd
import os


class MT5DataFetcher:
    def __init__(self, manual_data_path, user=None, password=None, server=None):
        self.manual_data_path = manual_data_path
        print("Using existing data files instead of connecting to MT5.")

    def fetch_data(self, symbol, period="1y", interval="H1"):
        file_path = os.path.join(self.manual_data_path, f"{symbol}_{interval}.csv")
        if not os.path.exists(file_path):
            raise FileNotFoundError(
                f"No data file found for {symbol} on {interval} at {file_path}"
            )

        df = pd.read_csv(file_path)
        required_columns = ["datetime", "open", "high", "low", "close", "volume"]
        if not all(col in df.columns for col in required_columns):
            raise ValueError(
                f"Invalid data format in {file_path}. Required columns: {required_columns}"
            )

        df["datetime"] = pd.to_datetime(df["datetime"])
        print(f"Loaded manual data for {symbol} on {interval}")
        return df

    def __del__(self):
        pass  # نیازی به shutdown نیست چون به MT5 وصل نیستیم


def read_large_csv(file_path, nrows=5):
    """Read and display the first few rows of a large CSV file."""
    try:
        df = pd.read_csv(file_path, nrows=nrows)
        print(df.head())
    except Exception as e:
        print(f"Error reading the file: {e}")
