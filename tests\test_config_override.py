import os
import tempfile
import yaml
from utils.config_override import load_config_with_override, override_config_with_env, override_config_with_cli

def test_override_config_with_env(monkeypatch):
    config = {'lot_size': 0.1, 'stop_loss': 10}
    monkeypatch.setenv('LOT_SIZE', '0.5')
    new_config = override_config_with_env(config.copy())
    assert new_config['lot_size'] == 0.5

def test_override_config_with_cli(monkeypatch):
    import sys
    config = {'lot_size': 0.1, 'stop_loss': 10}
    sys.argv += ['--lot_size', '0.7']
    new_config = override_config_with_cli(config.copy())
    assert new_config['lot_size'] == 0.7
    sys.argv = sys.argv[:-2]

def test_load_config_with_override(tmp_path, monkeypatch):
    config_dict = {'lot_size': 0.1, 'stop_loss': 10}
    config_file = tmp_path / 'config.yaml'
    config_file.write_text(yaml.dump(config_dict), encoding='utf-8')
    monkeypatch.setenv('STOP_LOSS', '20')
    import sys
    sys.argv += ['--lot_size', '0.9']
    config = load_config_with_override(str(config_file))
    assert config['stop_loss'] == 20
    assert config['lot_size'] == 0.9
    sys.argv = sys.argv[:-2]
