#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحلیل‌گر کامل پروژه
Project Complete Analyzer
"""

import os
import ast
import json
from datetime import datetime
from typing import Dict, List, Any
import sqlite3

class ProjectAnalyzer:
    def __init__(self, project_path: str = "."):
        self.project_path = project_path
        self.analysis_results = {}
        self.total_lines = 0
        self.total_classes = 0
        self.total_functions = 0
        self.files_analyzed = 0
        
    def analyze_file(self, filepath: str) -> Dict[str, Any]:
        """تحلیل یک فایل Python"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            classes = [node.name for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
            functions = [node.name for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
            imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    imports.extend([alias.name for alias in node.names])
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module)
            
            lines = len(content.split('\n'))
            
            return {
                'file': filepath,
                'lines': lines,
                'classes': len(classes),
                'functions': len(functions),
                'imports': len(set(imports)),
                'class_names': classes,
                'function_names': functions,
                'import_names': list(set(imports))
            }
        except Exception as e:
            return {'file': filepath, 'error': str(e)}
    
    def find_python_files(self) -> List[str]:
        """پیدا کردن تمام فایل‌های Python"""
        py_files = []
        for root, dirs, files in os.walk(self.project_path):
            # Skip cache and virtual environment directories
            dirs[:] = [d for d in dirs if not d.startswith(('.', '__pycache__', 'venv', 'env'))]
            
            for file in files:
                if file.endswith('.py'):
                    py_files.append(os.path.join(root, file))
        
        return py_files
    
    def analyze_database_files(self) -> Dict[str, Any]:
        """تحلیل فایل‌های پایگاه داده"""
        db_files = []
        for root, dirs, files in os.walk(self.project_path):
            for file in files:
                if file.endswith('.db'):
                    db_files.append(os.path.join(root, file))
        
        db_analysis = {}
        for db_file in db_files:
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # Get table names
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                
                table_info = {}
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    table_info[table_name] = count
                
                db_analysis[db_file] = {
                    'tables': len(tables),
                    'table_info': table_info
                }
                
                conn.close()
            except Exception as e:
                db_analysis[db_file] = {'error': str(e)}
        
        return db_analysis
    
    def analyze_project_structure(self) -> Dict[str, Any]:
        """تحلیل ساختار پروژه"""
        structure = {}
        
        for root, dirs, files in os.walk(self.project_path):
            # Skip hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            relative_path = os.path.relpath(root, self.project_path)
            if relative_path == '.':
                relative_path = 'root'
            
            structure[relative_path] = {
                'directories': len(dirs),
                'files': len(files),
                'python_files': len([f for f in files if f.endswith('.py')]),
                'config_files': len([f for f in files if f.endswith(('.yaml', '.yml', '.json', '.cfg', '.ini'))]),
                'data_files': len([f for f in files if f.endswith(('.csv', '.db', '.pkl'))]),
                'image_files': len([f for f in files if f.endswith(('.png', '.jpg', '.jpeg', '.gif'))]),
                'text_files': len([f for f in files if f.endswith(('.txt', '.md', '.log'))])
            }
        
        return structure
    
    def run_complete_analysis(self) -> Dict[str, Any]:
        """اجرای تحلیل کامل"""
        print("🔍 شروع تحلیل کامل پروژه...")
        
        # Find all Python files
        py_files = self.find_python_files()
        print(f"📁 تعداد فایل‌های Python: {len(py_files)}")
        
        # Analyze each Python file
        file_analysis = {}
        for filepath in py_files:
            result = self.analyze_file(filepath)
            file_analysis[filepath] = result
            
            if 'error' not in result:
                self.total_lines += result['lines']
                self.total_classes += result['classes']
                self.total_functions += result['functions']
                self.files_analyzed += 1
        
        # Analyze database files
        db_analysis = self.analyze_database_files()
        
        # Analyze project structure
        structure_analysis = self.analyze_project_structure()
        
        # Create comprehensive report
        report = {
            'analysis_date': datetime.now().isoformat(),
            'project_path': self.project_path,
            'summary': {
                'total_python_files': len(py_files),
                'files_analyzed': self.files_analyzed,
                'total_lines_of_code': self.total_lines,
                'total_classes': self.total_classes,
                'total_functions': self.total_functions,
                'total_database_files': len(db_analysis)
            },
            'file_analysis': file_analysis,
            'database_analysis': db_analysis,
            'structure_analysis': structure_analysis
        }
        
        return report
    
    def generate_detailed_report(self) -> str:
        """تولید گزارش تفصیلی"""
        report = self.run_complete_analysis()
        
        detailed_report = f"""
# 🔍 گزارش تحلیل کامل پروژه
تاریخ تحلیل: {report['analysis_date']}

## 📊 خلاصه کلی
- تعداد فایل‌های Python: {report['summary']['total_python_files']}
- تعداد خطوط کد: {report['summary']['total_lines_of_code']:,}
- تعداد کلاس‌ها: {report['summary']['total_classes']}
- تعداد توابع: {report['summary']['total_functions']}
- تعداد فایل‌های پایگاه داده: {report['summary']['total_database_files']}

## 🏗️ ساختار پروژه
"""
        
        # Add structure analysis
        for path, info in report['structure_analysis'].items():
            detailed_report += f"\n### 📁 {path}\n"
            detailed_report += f"- پوشه‌ها: {info['directories']}\n"
            detailed_report += f"- فایل‌های Python: {info['python_files']}\n"
            detailed_report += f"- فایل‌های تنظیمات: {info['config_files']}\n"
            detailed_report += f"- فایل‌های داده: {info['data_files']}\n"
            detailed_report += f"- فایل‌های تصویری: {info['image_files']}\n"
            detailed_report += f"- فایل‌های متنی: {info['text_files']}\n"
        
        # Add top files by size
        detailed_report += "\n## 📋 فایل‌های کلیدی (بر اساس تعداد خطوط)\n"
        
        files_by_size = []
        for filepath, info in report['file_analysis'].items():
            if 'error' not in info:
                files_by_size.append((filepath, info['lines'], info['classes'], info['functions']))
        
        files_by_size.sort(key=lambda x: x[1], reverse=True)
        
        for filepath, lines, classes, functions in files_by_size[:20]:  # Top 20 files
            detailed_report += f"\n### 📄 {filepath}\n"
            detailed_report += f"- خطوط: {lines:,}\n"
            detailed_report += f"- کلاس‌ها: {classes}\n"
            detailed_report += f"- توابع: {functions}\n"
        
        # Add database analysis
        if report['database_analysis']:
            detailed_report += "\n## 🗄️ تحلیل پایگاه‌های داده\n"
            for db_file, info in report['database_analysis'].items():
                detailed_report += f"\n### 💾 {db_file}\n"
                if 'error' not in info:
                    detailed_report += f"- تعداد جداول: {info['tables']}\n"
                    for table_name, count in info['table_info'].items():
                        detailed_report += f"  - {table_name}: {count:,} رکورد\n"
                else:
                    detailed_report += f"- خطا: {info['error']}\n"
        
        # Add key modules analysis
        detailed_report += "\n## 🔧 تحلیل ماژول‌های کلیدی\n"
        
        key_modules = {
            'main.py': 'فایل اصلی پروژه',
            'ph3_optimizer.py': 'بهینه‌ساز اصلی',
            'utils/genetic_strategy_evolution.py': 'الگوریتم ژنتیک',
            'utils/adaptive_plutus_system.py': 'سیستم تطبیقی پلوتوس',
            'utils/advanced_rl_agent.py': 'یادگیری تقویتی پیشرفته',
            'utils/intelligent_memory_system.py': 'سیستم حافظه هوشمند',
            'utils/market_regime_detector.py': 'تشخیص رژیم بازار',
            'utils/anomaly_detection_system.py': 'تشخیص ناهنجاری',
            'utils/federated_learning_system.py': 'یادگیری فدرال',
            'utils/multi_step_prediction_fixed.py': 'پیش‌بینی چندمرحله‌ای'
        }
        
        for module_path, description in key_modules.items():
            if module_path in report['file_analysis']:
                info = report['file_analysis'][module_path]
                if 'error' not in info:
                    detailed_report += f"\n### 🔗 {module_path}\n"
                    detailed_report += f"**توضیح:** {description}\n"
                    detailed_report += f"- خطوط: {info['lines']:,}\n"
                    detailed_report += f"- کلاس‌ها: {info['classes']} - {', '.join(info['class_names'][:5])}\n"
                    detailed_report += f"- توابع: {info['functions']} - {', '.join(info['function_names'][:5])}\n"
                    detailed_report += f"- وابستگی‌ها: {info['imports']} - {', '.join(info['import_names'][:10])}\n"
        
        return detailed_report

def main():
    analyzer = ProjectAnalyzer()
    
    # Generate complete analysis
    report = analyzer.run_complete_analysis()
    
    # Save JSON report
    with open('نقشه_راه_دقیق.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # Generate detailed report
    detailed_report = analyzer.generate_detailed_report()
    
    # Save detailed report
    with open('نقشه_راه_دقیق.md', 'w', encoding='utf-8') as f:
        f.write(detailed_report)
    
    print("✅ تحلیل کامل انجام شد!")
    print(f"📊 خلاصه: {report['summary']['total_python_files']} فایل، {report['summary']['total_lines_of_code']:,} خط کد")
    print("📄 گزارش‌ها ذخیره شدند:")
    print("  - نقشه_راه_دقیق.json")
    print("  - نقشه_راه_دقیق.md")

if __name__ == "__main__":
    main() 