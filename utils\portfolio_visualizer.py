import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Any, Optional

class PortfolioVisualizer:
    """
    ابزار گزارش‌گیری تصویری برای PortfolioManager
    """
    def __init__(self, trade_history: List[Dict[str, Any]]):
        self.trade_history = trade_history

    def plot_pnl_curve(self, save_path: Optional[str] = None):
        """نمودار تجمعی سود/زیان معاملات"""
        if not self.trade_history:
            print("No trades to plot.")
            return
        pnl = [t['profit'] for t in self.trade_history]
        cum_pnl = [sum(pnl[:i+1]) for i in range(len(pnl))]
        plt.figure(figsize=(10, 5))
        plt.plot(cum_pnl, marker='o')
        plt.title('Cumulative PnL')
        plt.xlabel('Trade #')
        plt.ylabel('Cumulative Profit/Loss')
        plt.grid(True)
        if save_path:
            plt.savefig(save_path)
        plt.show()

    def plot_trade_distribution(self, save_path: Optional[str] = None):
        """توزیع سود/زیان معاملات"""
        if not self.trade_history:
            print("No trades to plot.")
            return
        pnl = [t['profit'] for t in self.trade_history]
        plt.figure(figsize=(8, 4))
        sns.histplot(pnl, bins=20, kde=True)
        plt.title('Trade Profit/Loss Distribution')
        plt.xlabel('Profit/Loss')
        plt.ylabel('Frequency')
        if save_path:
            plt.savefig(save_path)
        plt.show()

    def plot_drawdown(self, save_path: Optional[str] = None):
        """نمودار drawdown معاملات"""
        if not self.trade_history:
            print("No trades to plot.")
            return
        pnl = [t['profit'] for t in self.trade_history]
        cum_pnl = [sum(pnl[:i+1]) for i in range(len(pnl))]
        peak = [max(cum_pnl[:i+1]) for i in range(len(cum_pnl))]
        drawdown = [cum_pnl[i] - peak[i] for i in range(len(cum_pnl))]
        plt.figure(figsize=(10, 4))
        plt.fill_between(range(len(drawdown)), drawdown, color='red', alpha=0.3)
        plt.title('Drawdown Curve')
        plt.xlabel('Trade #')
        plt.ylabel('Drawdown')
        plt.grid(True)
        if save_path:
            plt.savefig(save_path)
        plt.show()
