#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔌 Circuit Breaker System Test
"""

import os
import sys
import time
import asyncio

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_circuit_breaker_system():
    """تست سیستم Circuit Breaker"""
    print("🔌 Testing Circuit Breaker System...")
    
    try:
        # Test 1: Import
        print("1️⃣ Testing import...")
        from core.circuit_breaker_system import (
            get_circuit_breaker_manager,
            circuit_breaker,
            circuit_context,
            circuit_async
        )
        print("   ✓ Import successful")
        
        # Test 2: Get manager
        print("2️⃣ Testing circuit breaker manager...")
        manager = get_circuit_breaker_manager()
        print("   ✓ Manager obtained")
        
        # Test 3: Register breaker
        print("3️⃣ Testing breaker registration...")
        breaker = manager.register_breaker("test_service")
        print(f"   ✓ Breaker registered: {breaker.name}")
        print(f"   ✓ Initial state: {breaker.state}")
        
        # Test 4: Decorator test
        print("4️⃣ Testing decorator...")
        call_count = 0
        
        @circuit_breaker("test_decorator")
        def test_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Test error")
            return "Success"
        
        # Test failures
        for i in range(2):
            try:
                test_function()
            except:
                pass
        
        print("   ✓ Decorator applied successfully")
        
        # Test 5: Context manager
        print("5️⃣ Testing context manager...")
        try:
            with circuit_context("test_context"):
                print("   ✓ Context manager working")
        except Exception as e:
            print(f"   ❌ Context manager failed: {e}")
        
        # Test 6: Get all breakers
        print("6️⃣ Testing breaker metrics...")
        all_metrics = manager.describe_all()
        print(f"   ✓ Total breakers: {len(all_metrics)}")
        for name, metrics in all_metrics.items():
            print(f"   ✓ {name}: state={metrics.state}, failures={metrics.failure_count}")
        
        print("\n🎉 All Circuit Breaker tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_circuit():
    """تست Circuit Breaker async"""
    print("\n⚡ Testing Async Circuit Breaker...")
    
    try:
        from core.circuit_breaker_system import circuit_async, get_circuit_breaker_manager
        
        # Test async context manager
        async with circuit_async("test_async"):
            print("   ✓ Async context manager working")
        
        # Start monitoring
        manager = get_circuit_breaker_manager()
        await manager.start_monitoring(interval=1)
        
        # Wait a bit
        await asyncio.sleep(2)
        
        # Stop monitoring
        await manager.stop_monitoring()
        
        print("✅ Async Circuit Breaker test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Async test failed: {e}")
        return False

if __name__ == "__main__":
    # Run sync test
    success1 = test_circuit_breaker_system()
    
    # Run async test
    success2 = asyncio.run(test_async_circuit())
    
    if success1 and success2:
        print("\n✅ Circuit Breaker System is ready!")
    else:
        print("\n❌ Some tests failed!") 