 Ray Tune best config: {'learning_rate': 0.0028859457082868445, 'batch_size': 32, 'hidden_size': 128}
🎯 Ray Tune best score: 0.808
🔍 DEBUG: Ray result: <class 'dict'>
🔍 DEBUG: Updated distributed_config with Ray results
🎯 PyCaret Brain: Analyzing data patterns...
✅ Using cached result for pycaret_data_analysis
✅ Using cached PyCaret analysis result
🛡️ Added missing key: early_stopping_config
🛡️ Added missing key: performance_grade
🛡️ Added missing key: unseen
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
🚨 CRITICAL: Multi-Brain analysis completely failed: 'hyperparameter_suggestions'
🔄 Using emergency fallback analysis...
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
✅ initial_analysis analysis completed successfully
🎯 Early stopping: Disabled
   📊 Multi-Brain Analysis Complete!
   🎯 Action: train_advanced
   💪 Confidence: 75.0%
   🧠 Reasoning: Safe fallback analysis for initial_analysis
   🎯 Market Domination Potential: HIGH

📋 STEP 2: TRAINING MARKET-DOMINATING MODELS
==================================================

🧠 MULTI-BRAIN: Multi-Symbol Multi-Style Training
   🎯 Primary Symbol: AUDUSD
   📊 Available Symbols: Multiple symbols analyzed
   🎨 Trading Styles: 10 professional styles
   ⏰ Session-Aware: 05:05
   🧠 Multi-Brain loaded symbol data for analysis

🎯 SMART TRAINING STRATEGY
========================================
📈 LSTM/GRU: Transfer Learning + Fine-tuning
🤖 DQN/PPO: Pre-trained Models + Fine-tuning


📈 Training Market-Dominating LSTM (Transfer Learning) - MONITORED...
🧠 Internal Brain monitoring: safe_analyze_training_situation_LSTM
🧠 Multi-Brain analyzing LSTM training for AUDUSD...
🧠 Multi-Brain analyzing LSTM training for AUDUSD...
🔍 DEBUG: Starting _internal_analyze_training_situation for LSTM
🔍 DEBUG: Creating cache key...
🔍 DEBUG: Cache key created successfully
🔍 DEBUG: Checking cache...
🔍 DEBUG: Starting brain analysis...
🎯 Optuna Brain: Analyzing hyperparameters...
🔍 DEBUG: Calling optuna_brain.suggest_hyperparameters...
✅ Using cached result for optuna_hyperparameters
🔍 DEBUG: Optuna result: <class 'dict'>
🔍 DEBUG: Updated hyperparameter_suggestions with Optuna results
🤖 AutoGluon Brain: Analyzing model selection...
🔍 DEBUG: Calling autogluon_brain.recommend_models...
🔧 Applying final sklearn.metrics fix before AutoGluon...
🔧 Fixed sklearn.metrics.fbeta_score compatibility issue
🤖 AutoGluon: Running real model selection...
🎯 AutoGluon best model: WeightedEnsemble_L2 (score: 0.775)
🔍 DEBUG: AutoGluon result: <class 'dict'>
🔍 DEBUG: Updated model_recommendations with AutoGluon results
🚀 Ray Brain: Analyzing distributed training...
🔍 DEBUG: Calling ray_brain.suggest_distributed_config...
🚀 Ray Tune: Running real distributed optimization...
+-----------------------------------------------------------------------+
| Configuration for experiment     train_function_2025-07-21_05-05-32   |
+-----------------------------------------------------------------------+
| Search algorithm                 BasicVariantGenerator                |
| Scheduler                        FIFOScheduler                        |
| Number of trials                 5                                    |
+-----------------------------------------------------------------------+

View detailed results here: /root/ray_results/train_function_2025-07-21_05-05-32
To visualize your results with TensorBoard, run: `tensorboard --logdir /tmp/ray_temp/session_2025-07-21_05-04-38_746307_189660/artifacts/2025-07-21_05-05-32/train_function_2025-07-21_05-05-32/driver_artifacts`

🎯 Ray Tune best config: {'learning_rate': 0.00018724636396291616, 'batch_size': 16, 'hidden_size': 256}
🎯 Ray Tune best score: 0.715
🔍 DEBUG: Ray result: <class 'dict'>
🔍 DEBUG: Updated distributed_config with Ray results
🎯 PyCaret Brain: Analyzing data patterns...
✅ Using cached result for pycaret_data_analysis
✅ Using cached PyCaret analysis result
🛡️ Added missing key: early_stopping_config
🛡️ Added missing key: performance_grade
🛡️ Added missing key: unseen
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
🚨 CRITICAL: Multi-Brain analysis completely failed: 'hyperparameter_suggestions'
🔄 Using emergency fallback analysis...
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
✅ LSTM analysis completed successfully
🎯 Early stopping: Disabled
✅ safe_analyze_training_situation_LSTM completed successfully
🎯 Starting monitored training for LSTM
✅ Found checkpoint: LSTM at /content/drive/MyDrive/project2/models/checkpoints/LSTM/progress.json
🔄 Resuming LSTM from step 0
🔄 LSTM will resume from checkpoint
🧠 Direct training with Internal Brain watching: LSTM
🎯 Transfer Learning LSTM Strategy:
   📚 Using proven architecture patterns
   🔧 Multi-Brain optimized hyperparameters
   ⚡ Faster convergence with smart initialization
🔄 Resuming from checkpoint: epoch 8
📂 Model path: /content/drive/MyDrive/project2/models/checkpoints/LSTM/LSTM_latest
🎯 Using optimized config: {'sequence_length': 60, 'hidden_size': 256, 'num_layers': 3, 'learning_rate': 0.001, 'batch_size': 32, 'dropout': 0.2, 'transfer_learning': True}
📈 Training Market-Dominating LSTM with Multi-Brain System...
🏦 Integrating Advanced Account Management...
🧪 Enabling Advanced Backtesting...
🏦 Using risk profile: moderate
🧠 Multi-Brain approved training with 75.0% confidence
🎯 Using Transfer Learning configuration
   📚 Architecture: 256 hidden units, 3 layers
   ⚡ Learning rate: 0.001
   🎯 Batch size: 32
🎯 Merging Transfer Learning config with Multi-Brain suggestions
🎯 Final merged config: {'learning_rate': 0.001, 'batch_size': 32, 'hidden_size': 256, 'num_layers': 3, 'dropout': 0.2, 'sequence_length': 60, 'patience': 50, 'min_delta': 0.001, 'early_stopping_enabled': False, 'transfer_learning': True}
🧠 Multi-Brain optimizations: 2 suggestions
💡 Multi-Brain optimizations:
   • Safe fallback for LSTM
   • Early stopping disabled
   🔧 Creating returns target from close
   📊 Returns target stats: mean=0.000361, std=0.974741
   📊 Returns target range: [-3.000000, 3.000000]
   🔍 Original data shape: (6209, 168)
   🧹 Cleaned data shape: (6209, 169)
   📊 Features: 168, Samples: 6209
   ⚠️ GPU not available, using CPU
   🔍 Validating tensors: X_train=(4943, 30, 168), y_train=(4943,)
   💾 Creating memory-efficient tensors for MAXIMUM parameters...
   📊 Tensor memory usage: X_train=95.0MB
   🎯 Total model + data memory: ~121.7MB
   ✅ Tensors successfully moved to cpu
   🎯 Training device: cpu
   🧠 SMART config: hidden_size=256, num_layers=3
   💪 LSTM parameters: ~1.3M parameters
   💾 Estimated memory: ~5.1MB
   🎯 Memory-optimized for stable training!
   📈 FIXED TRAINING: LR=0.0005, weight_decay=1e-3
   🧪 Testing gradient flow...
   ✅ Gradient flow test: PASSED
   ⚠️ Continual learning not available, using standard scheduler
   🧠 Model parameters: 1,874,754
   ⏰ Training started at: 05:05:51
✅ Found checkpoint: LSTM at /content/drive/MyDrive/project2/models/checkpoints/LSTM/progress.json
🔄 Resuming LSTM from step 0
🔄 Resuming LSTM training from epoch 8
✅ LSTM model loaded from checkpoint
   ⚠️ Weights-only loading failed, trying legacy mode...
   🔄 Checkpoint loaded: advanced_lstm
      📅 Saved: 2025-07-21T04:52:34.016971
      📊 Epoch: 8, Loss: 1.518166, Performance: 0.0158
   🎯 ENHANCED Training: 500 epochs, patience 100
   📊 ENHANCED improvement threshold: 0.001 (optimized for better learning)
   🔧 ENHANCED LR: 0.001, Dropout: 0.5, Grad Clip: 0.5
   🔄 Resuming training from epoch 9
   📊 Previous best: Loss 1.518166, Performance 0.0158
   ⏱️ Estimated time: 245.5 minutes
   💪 ULTIMATE LSTM Power: 1,874,754 parameters
   📊 Training on 4943 samples with 168 features
   🎯 Target: Minimize RMSE and maximize correlation
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0148
   📊 Final performance: 0.0148 (correlation: 0.0148)
      💾 New best model saved! Loss: 1.406527 (Improved by 0.111639)
      💾 Model saved to Google Drive: /content/drive/MyDrive/project2/models/best_lstm_model.pth
📁 Created checkpoint directory: /content/drive/MyDrive/project2/models/checkpoints/LSTM
✅ Google Drive cache directories created at: /content/drive/MyDrive/project2/cache
💾 Saved to Google Drive cache: /content/drive/MyDrive/project2/checkpoints/LSTM_progress.json
✅ Checkpoint saved: LSTM
✅ LSTM checkpoint saved to: /content/drive/MyDrive/project2/models/checkpoints/LSTM/LSTM_latest.pth
   💾 Checkpoint saved: advanced_lstm at epoch 9
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0145
   📊 Final performance: 0.0145 (correlation: 0.0145)
   Epoch 10/500 (2.0%): Train Loss: 0.101957, Val Loss: 1.406457, Performance: 0.0145
      ⏰ Elapsed: 0.2m, ETA: 8.2m
      💪 ULTIMATE LSTM: 1,874,754 parameters
      🎯 Best Loss: 1.406527, Patience: 1/100
      📈 Learning Rate: 0.001000
      📈 Improvement: 0.00% from best
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0133
   📊 Final performance: 0.0133 (correlation: 0.0133)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0132
   📊 Final performance: 0.0132 (correlation: 0.0132)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0132
   📊 Final performance: 0.0132 (correlation: 0.0132)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0126
   📊 Final performance: 0.0126 (correlation: 0.0126)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0122
   📊 Final performance: 0.0122 (correlation: 0.0122)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0126
   📊 Final performance: 0.0126 (correlation: 0.0126)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0129
   📊 Final performance: 0.0129 (correlation: 0.0129)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0126
   📊 Final performance: 0.0126 (correlation: 0.0126)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0126
   📊 Final performance: 0.0126 (correlation: 0.0126)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0126
   📊 Final performance: 0.0126 (correlation: 0.0126)
   Epoch 20/500 (4.0%): Train Loss: 0.100330, Val Loss: 1.407399, Performance: 0.0126
      ⏰ Elapsed: 1.0m, ETA: 23.7m
      💪 ULTIMATE LSTM: 1,874,754 parameters
      🎯 Best Loss: 1.406527, Patience: 11/100
      📈 Learning Rate: 0.000950
      📊 No improvement for 11 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0076
   📊 Final performance: 0.0076 (correlation: 0.0076)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0014
   📊 Final performance: 0.0014 (correlation: -0.0014)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0051
   📊 Final performance: 0.0051 (correlation: -0.0051)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0070
   📊 Final performance: 0.0070 (correlation: -0.0070)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0076
   📊 Final performance: 0.0076 (correlation: -0.0076)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0081
   📊 Final performance: 0.0081 (correlation: -0.0081)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0092
   📊 Final performance: 0.0092 (correlation: -0.0092)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0095
   📊 Final performance: 0.0095 (correlation: -0.0095)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0093
   📊 Final performance: 0.0093 (correlation: -0.0093)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0094
   📊 Final performance: 0.0094 (correlation: -0.0094)
   Epoch 30/500 (6.0%): Train Loss: 0.100879, Val Loss: 1.408295, Performance: 0.0094
      ⏰ Elapsed: 2.0m, ETA: 30.2m
      💪 ULTIMATE LSTM: 1,874,754 parameters
      🎯 Best Loss: 1.406527, Patience: 21/100
      📈 Learning Rate: 0.000902
      📊 No improvement for 21 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0095
   📊 Final performance: 0.0095 (correlation: -0.0095)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0090
   📊 Final performance: 0.0090 (correlation: -0.0090)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0086
   📊 Final performance: 0.0086 (correlation: -0.0086)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0091
   📊 Final performance: 0.0091 (correlation: -0.0091)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0075
   📊 Final performance: 0.0075 (correlation: -0.0075)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0056
   📊 Final performance: 0.0056 (correlation: -0.0056)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.0029
   📊 Final performance: 0.0029 (correlation: -0.0029)
Stop signal received (e.g. via SIGINT/Ctrl+C), ending Ray Tune run. This will try to checkpoint the experiment state one last time. Press CTRL+C (or send SIGINT/SIGKILL/SIGTERM) to skip. 
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: 0.0001
   📊 Final performance: 0.0001 (correlation: 0.0001)
An exception has occurred, use %tb to see the full traceback.