nitializing advanced neural pattern recognition...
🧠 ULTIMATE Genius Indicator Creator initialized
🚀 Advanced caching and memory management enabled
🧠 Creating ULTIMATE genius indicator combinations...
🚀 Generating 50+ advanced neural patterns...
💾 Checking cache and optimizing memory...
📂 Loaded from Google Drive cache: /content/drive/MyDrive/project2/cache/genius_indicators/genius_6209x128_0.66254_1.pkl
🔍 Cache validation: Found 40 genius columns
🔍 Valid indicators: 40/40
⚡ Found cached genius indicators - using 40 valid indicators!
✅ Populated created_indicators list with 40 indicators
🔍 Evaluating genius indicator performance...
🔍 Evaluating 40 genius indicators...
💾 Computing advanced metrics with caching...
🏆 TOP 10 ULTIMATE GENIUS INDICATORS:
    1. genius_kalman            : 0.7015 (Corr: +1.000, Pred: -0.021, Stab: 0.977)
    2. genius_adaptive_ma       : 0.6635 (Corr: +0.997, Pred: -0.020, Stab: 0.793)
    3. genius_liquidity_flow    : 0.3439 (Corr: -0.307, Pred: +0.019, Stab: 0.644)
    4. genius_liquidity_stress  : 0.3432 (Corr: -0.369, Pred: +0.044, Stab: 0.586)
    5. genius_chaos             : 0.3198 (Corr: -0.353, Pred: +0.031, Stab: 0.522)
    6. genius_support_resistance: 0.2884 (Corr: +0.117, Pred: -0.008, Stab: 0.697)
    7. genius_momentum_fusion   : 0.2766 (Corr: +0.073, Pred: -0.009, Stab: 0.724)
    8. genius_neural_mimic      : 0.2693 (Corr: +0.017, Pred: +0.002, Stab: 0.810)
    9. genius_entropy_measure   : 0.2684 (Corr: -0.338, Pred: +0.033, Stab: 0.483)
   10. genius_fractal_dimension : 0.2488 (Corr: +0.214, Pred: -0.019, Stab: 0.585)

🧠 GENIUS INDICATOR SUMMARY:
   🚀 Total Created: 40
   ✅ Successfully Evaluated: 40
   🏆 High Performance (>0.1): 40
   🎯 Average Performance: 0.2219
💾 Caching performance evaluation for future use...
✅ Data enhanced with 168 total features
🧠 Including 40 ULTIMATE genius indicators
🎯 Advanced neural patterns and quantum oscillators integrated!
⚛️ Quantum consciousness and market awareness activated!
🌟 Multi-dimensional analysis and pattern recognition enabled!
💾 Advanced caching and memory management optimized!
🔧 Fixed gradient computation issues for stable training!
✅ All missing indicators successfully implemented!
🎯 Ready for training with AUDUSD data!
✅ Data loaded and enhanced!
📊 Enhanced data: 6209 records, 168 features

🧠 MULTI-BRAIN INITIAL ANALYSIS:
🧠 Multi-Brain analyzing initial_analysis training for AUDUSD...
🧠 Multi-Brain analyzing initial_analysis training for AUDUSD...
🔍 DEBUG: Starting _internal_analyze_training_situation for initial_analysis
🔍 DEBUG: Creating cache key...
🔍 DEBUG: Cache key created successfully
🔍 DEBUG: Checking cache...
🔍 DEBUG: Starting brain analysis...
🎯 Optuna Brain: Analyzing hyperparameters...
🔍 DEBUG: Calling optuna_brain.suggest_hyperparameters...
✅ Using cached result for optuna_hyperparameters
🔍 DEBUG: Optuna result: <class 'dict'>
🔍 DEBUG: Updated hyperparameter_suggestions with Optuna results
🤖 AutoGluon Brain: Analyzing model selection...
🔍 DEBUG: Calling autogluon_brain.recommend_models...
🔧 Applying final sklearn.metrics fix before AutoGluon...
🔧 Fixed sklearn.metrics.fbeta_score compatibility issue
🤖 AutoGluon: Running real model selection...
🎯 AutoGluon best model: CatBoost (score: 0.780)
🔍 DEBUG: AutoGluon result: <class 'dict'>
🔍 DEBUG: Updated model_recommendations with AutoGluon results
🚀 Ray Brain: Analyzing distributed training...
🔍 DEBUG: Calling ray_brain.suggest_distributed_config...
🚀 Ray Tune: Running real distributed optimization...
+-----------------------------------------------------------------------+
| Configuration for experiment     train_function_2025-07-21_04-40-32   |
+-----------------------------------------------------------------------+
| Search algorithm                 BasicVariantGenerator                |
| Scheduler                        FIFOScheduler                        |
| Number of trials                 5                                    |
+-----------------------------------------------------------------------+

View detailed results here: /root/ray_results/train_function_2025-07-21_04-40-32
To visualize your results with TensorBoard, run: `tensorboard --logdir /tmp/ray_temp/session_2025-07-21_04-40-25_935433_171560/artifacts/2025-07-21_04-40-32/train_function_2025-07-21_04-40-32/driver_artifacts`

🎯 Ray Tune best config: {'learning_rate': 1.8363902657556923e-05, 'batch_size': 32, 'hidden_size': 128}
🎯 Ray Tune best score: 0.720
🔍 DEBUG: Ray result: <class 'dict'>
🔍 DEBUG: Updated distributed_config with Ray results
🎯 PyCaret Brain: Analyzing data patterns...
✅ Using cached result for pycaret_data_analysis
✅ Using cached PyCaret analysis result
🛡️ Added missing key: early_stopping_config
🛡️ Added missing key: performance_grade
🛡️ Added missing key: unseen
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
🚨 CRITICAL: Multi-Brain analysis completely failed: 'hyperparameter_suggestions'
🔄 Using emergency fallback analysis...
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
✅ initial_analysis analysis completed successfully
🎯 Early stopping: Disabled
   📊 Multi-Brain Analysis Complete!
   🎯 Action: train_advanced
   💪 Confidence: 75.0%
   🧠 Reasoning: Safe fallback analysis for initial_analysis
   🎯 Market Domination Potential: HIGH

📋 STEP 2: TRAINING MARKET-DOMINATING MODELS
==================================================

🧠 MULTI-BRAIN: Multi-Symbol Multi-Style Training
   🎯 Primary Symbol: AUDUSD
   📊 Available Symbols: Multiple symbols analyzed
   🎨 Trading Styles: 10 professional styles
   ⏰ Session-Aware: 04:40
   🧠 Multi-Brain loaded symbol data for analysis

🎯 SMART TRAINING STRATEGY
========================================
📈 LSTM/GRU: Transfer Learning + Fine-tuning
🤖 DQN/PPO: Pre-trained Models + Fine-tuning


📈 Training Market-Dominating LSTM (Transfer Learning) - MONITORED...
🧠 Internal Brain monitoring: safe_analyze_training_situation_LSTM
🧠 Multi-Brain analyzing LSTM training for AUDUSD...
🧠 Multi-Brain analyzing LSTM training for AUDUSD...
🔍 DEBUG: Starting _internal_analyze_training_situation for LSTM
🔍 DEBUG: Creating cache key...
🔍 DEBUG: Cache key created successfully
🔍 DEBUG: Checking cache...
🔍 DEBUG: Starting brain analysis...
🎯 Optuna Brain: Analyzing hyperparameters...
🔍 DEBUG: Calling optuna_brain.suggest_hyperparameters...
✅ Using cached result for optuna_hyperparameters
🔍 DEBUG: Optuna result: <class 'dict'>
🔍 DEBUG: Updated hyperparameter_suggestions with Optuna results
🤖 AutoGluon Brain: Analyzing model selection...
🔍 DEBUG: Calling autogluon_brain.recommend_models...
🔧 Applying final sklearn.metrics fix before AutoGluon...
🔧 Fixed sklearn.metrics.fbeta_score compatibility issue
🤖 AutoGluon: Running real model selection...
🎯 AutoGluon best model: WeightedEnsemble_L2 (score: 0.740)
🔍 DEBUG: AutoGluon result: <class 'dict'>
🔍 DEBUG: Updated model_recommendations with AutoGluon results
🚀 Ray Brain: Analyzing distributed training...
🔍 DEBUG: Calling ray_brain.suggest_distributed_config...
🚀 Ray Tune: Running real distributed optimization...
+-----------------------------------------------------------------------+
| Configuration for experiment     train_function_2025-07-21_04-41-20   |
+-----------------------------------------------------------------------+
| Search algorithm                 BasicVariantGenerator                |
| Scheduler                        FIFOScheduler                        |
| Number of trials                 5                                    |
+-----------------------------------------------------------------------+

View detailed results here: /root/ray_results/train_function_2025-07-21_04-41-20
To visualize your results with TensorBoard, run: `tensorboard --logdir /tmp/ray_temp/session_2025-07-21_04-40-25_935433_171560/artifacts/2025-07-21_04-41-20/train_function_2025-07-21_04-41-20/driver_artifacts`

🎯 Ray Tune best config: {'learning_rate': 0.00029531326675689507, 'batch_size': 16, 'hidden_size': 128}
🎯 Ray Tune best score: 0.781
🔍 DEBUG: Ray result: <class 'dict'>
🔍 DEBUG: Updated distributed_config with Ray results
🎯 PyCaret Brain: Analyzing data patterns...
✅ Using cached result for pycaret_data_analysis
✅ Using cached PyCaret analysis result
🛡️ Added missing key: early_stopping_config
🛡️ Added missing key: performance_grade
🛡️ Added missing key: unseen
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
🚨 CRITICAL: Multi-Brain analysis completely failed: 'hyperparameter_suggestions'
🔄 Using emergency fallback analysis...
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
✅ LSTM analysis completed successfully
🎯 Early stopping: Disabled
✅ safe_analyze_training_situation_LSTM completed successfully
🎯 Starting monitored training for LSTM
✅ Found checkpoint: LSTM at /content/drive/MyDrive/project2/models/checkpoints/LSTM/progress.json
🔄 Resuming LSTM from step 0
🔄 LSTM will resume from checkpoint
🧠 Direct training with Internal Brain watching: LSTM
🎯 Transfer Learning LSTM Strategy:
   📚 Using proven architecture patterns
   🔧 Multi-Brain optimized hyperparameters
   ⚡ Faster convergence with smart initialization
🔄 Resuming from checkpoint: epoch 3
📂 Model path: /content/drive/MyDrive/project2/models/checkpoints/LSTM/LSTM_latest
🎯 Using optimized config: {'sequence_length': 60, 'hidden_size': 256, 'num_layers': 3, 'learning_rate': 0.001, 'batch_size': 32, 'dropout': 0.2, 'transfer_learning': True}
📈 Training Market-Dominating LSTM with Multi-Brain System...
🏦 Integrating Advanced Account Management...
🧪 Enabling Advanced Backtesting...
🏦 Using risk profile: moderate
🧠 Multi-Brain approved training with 75.0% confidence
🎯 Using Transfer Learning configuration
   📚 Architecture: 256 hidden units, 3 layers
   ⚡ Learning rate: 0.001
   🎯 Batch size: 32
🎯 Merging Transfer Learning config with Multi-Brain suggestions
🎯 Final merged config: {'learning_rate': 0.001, 'batch_size': 32, 'hidden_size': 256, 'num_layers': 3, 'dropout': 0.2, 'sequence_length': 60, 'patience': 50, 'min_delta': 0.001, 'early_stopping_enabled': False, 'transfer_learning': True}
🧠 Multi-Brain optimizations: 2 suggestions
💡 Multi-Brain optimizations:
   • Safe fallback for LSTM
   • Early stopping disabled
   🔧 Creating returns target from close
   📊 Returns target stats: mean=-0.000000, std=1.000000
   🔍 Original data shape: (6209, 168)
❌ LSTM training failed: "['returns_target'] not in index"
✅ LSTM training completed successfully

🧠 Training Advanced GRU (Transfer Learning) - MONITORED...
🧠 Internal Brain monitoring: safe_analyze_training_situation_GRU
🧠 Multi-Brain analyzing GRU training for AUDUSD...
🧠 Multi-Brain analyzing GRU training for AUDUSD...
🔍 DEBUG: Starting _internal_analyze_training_situation for GRU
🔍 DEBUG: Creating cache key...
🔍 DEBUG: Cache key created successfully
🔍 DEBUG: Checking cache...
🔍 DEBUG: Starting brain analysis...
🎯 Optuna Brain: Analyzing hyperparameters...
🔍 DEBUG: Calling optuna_brain.suggest_hyperparameters...
✅ Using cached result for optuna_hyperparameters
🔍 DEBUG: Optuna result: <class 'dict'>
🔍 DEBUG: Updated hyperparameter_suggestions with Optuna results
🤖 AutoGluon Brain: Analyzing model selection...
🔍 DEBUG: Calling autogluon_brain.recommend_models...
🔧 Applying final sklearn.metrics fix before AutoGluon...
🔧 Fixed sklearn.metrics.fbeta_score compatibility issue
🤖 AutoGluon: Running real model selection...
Traceback (most recent call last):
  File "<string>", line 11650, in train_advanced_lstm
  File "/usr/local/lib/python3.11/dist-packages/pandas/core/frame.py", line 3464, in __getitem__
    indexer = self.loc._get_listlike_indexer(key, axis=1)[1]
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/pandas/core/indexing.py", line 1314, in _get_listlike_indexer
    self._validate_read_indexer(keyarr, indexer, axis)
  File "/usr/local/lib/python3.11/dist-packages/pandas/core/indexing.py", line 1377, in _validate_read_indexer
    raise KeyError(f"{not_found} not in index")
KeyError: "['returns_target'] not in index"
🎯 AutoGluon best model: LightGBMXT (score: 0.745)
🔍 DEBUG: AutoGluon result: <class 'dict'>
🔍 DEBUG: Updated model_recommendations with AutoGluon results
🚀 Ray Brain: Analyzing distributed training...
🔍 DEBUG: Calling ray_brain.suggest_distributed_config...
🚀 Ray Tune: Running real distributed optimization...
+-----------------------------------------------------------------------+
| Configuration for experiment     train_function_2025-07-21_04-42-06   |
+-----------------------------------------------------------------------+
| Search algorithm                 BasicVariantGenerator                |
| Scheduler                        FIFOScheduler                        |
| Number of trials                 5                                    |
+-----------------------------------------------------------------------+

View detailed results here: /root/ray_results/train_function_2025-07-21_04-42-06
To visualize your results with TensorBoard, run: `tensorboard --logdir /tmp/ray_temp/session_2025-07-21_04-40-25_935433_171560/artifacts/2025-07-21_04-42-06/train_function_2025-07-21_04-42-06/driver_artifacts`

🎯 Ray Tune best config: {'learning_rate': 2.3965875487809837e-05, 'batch_size': 32, 'hidden_size': 128}
🎯 Ray Tune best score: 0.741
🔍 DEBUG: Ray result: <class 'dict'>
🔍 DEBUG: Updated distributed_config with Ray results
🎯 PyCaret Brain: Analyzing data patterns...
✅ Using cached result for pycaret_data_analysis
✅ Using cached PyCaret analysis result
🛡️ Added missing key: early_stopping_config
🛡️ Added missing key: performance_grade
🛡️ Added missing key: unseen
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
🚨 CRITICAL: Multi-Brain analysis completely failed: 'hyperparameter_suggestions'
🔄 Using emergency fallback analysis...
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
✅ GRU analysis completed successfully
🎯 Early stopping: Disabled
✅ safe_analyze_training_situation_GRU completed successfully
🎯 Starting monitored training for GRU
🆕 Starting fresh training for GRU
🧠 Direct training with Internal Brain watching: GRU
🎯 Transfer Learning GRU Strategy:
   📚 Using proven GRU architecture patterns
   🔧 Multi-Brain optimized hyperparameters
   ⚡ Enhanced with attention mechanisms
🎯 Using optimized GRU config: {'sequence_length': 60, 'hidden_size': 256, 'num_layers': 3, 'learning_rate': 0.001, 'batch_size': 32, 'dropout': 0.2, 'attention': True, 'transfer_learning': True}
🧠 Training Advanced GRU with Multi-Brain Analysis...
🏦 Integrating Advanced Account Management...
🧪 Enabling Advanced Backtesting...
🏦 Using risk profile: moderate
🎯 Using Transfer Learning GRU configuration
   📚 Architecture: 256 hidden units, 3 layers
   ⚡ Learning rate: 0.001
   🎯 Attention: True
🚀 Optimizing memory for training...
💪 FORCING MAXIMUM parameters as requested by user
🚀 Ignoring memory constraints - using MAXIMUM power
🧹 GC round 1: 356 objects collected
[I 2025-07-21 04:42:28,079] A new study created in memory with name: no-name-cfe5eb34-caba-4f8c-aef5-5a1ade040ab9
[I 2025-07-21 04:42:28,085] Trial 0 finished with value: 0.**************** and parameters: {'hidden_size': 256, 'num_layers': 3, 'learning_rate': 1.055710857211482e-05, 'batch_size': 64, 'sequence_length': 103, 'dropout': 0.***************, 'weight_decay': 1.3045008170941308e-05}. Best is trial 0 with value: 0.****************.
[I 2025-07-21 04:42:28,089] Trial 1 finished with value: 0.**************** and parameters: {'hidden_size': 256, 'num_layers': 3, 'learning_rate': 1.3692805991698528e-05, 'batch_size': 16, 'sequence_length': 115, 'dropout': 0.1252822375916917, 'weight_decay': 1.2201414329580306e-06}. Best is trial 0 with value: 0.****************.
[I 2025-07-21 04:42:28,091] Trial 2 finished with value: 0.7754549800235094 and parameters: {'hidden_size': 64, 'num_layers': 3, 'learning_rate': 0.0002672180442336729, 'batch_size': 16, 'sequence_length': 87, 'dropout': 0.3722547465738547, 'weight_decay': 3.7933715392138777e-06}. Best is trial 2 with value: 0.7754549800235094.
[I 2025-07-21 04:42:28,097] Trial 3 finished with value: 0.5629539088847351 and parameters: {'hidden_size': 64, 'num_layers': 3, 'learning_rate': 6.0399533560069996e-05, 'batch_size': 16, 'sequence_length': 69, 'dropout': 0.4270569898078962, 'weight_decay': 0.0007889474055857583}. Best is trial 2 with value: 0.7754549800235094.
[I 2025-07-21 04:42:28,100] Trial 4 finished with value: 0.854693919717165 and parameters: {'hidden_size': 256, 'num_layers': 4, 'learning_rate': 0.0003358297048645257, 'batch_size': 64, 'sequence_length': 42, 'dropout': 0.41842317469979895, 'weight_decay': 6.223089977888392e-05}. Best is trial 4 with value: 0.854693919717165.
[I 2025-07-21 04:42:28,104] Trial 5 finished with value: 0.7569646903363502 and parameters: {'hidden_size': 256, 'num_layers': 2, 'learning_rate': 0.00024063100480397617, 'batch_size': 64, 'sequence_length': 77, 'dropout': 0.48317958581278286, 'weight_decay': 2.286064774998087e-06}. Best is trial 4 with value: 0.854693919717165.
[I 2025-07-21 04:42:28,107] Trial 6 finished with value: 0.7152989692666487 and parameters: {'hidden_size': 256, 'num_layers': 4, 'learning_rate': 0.0002971684517430777, 'batch_size': 16, 'sequence_length': 56, 'dropout': 0.3869397522398509, 'weight_decay': 1.417926862551231e-06}. Best is trial 4 with value: 0.854693919717165.
[I 2025-07-21 04:42:28,110] Trial 7 finished with value: 0.5457015604532404 and parameters: {'hidden_size': 128, 'num_layers': 3, 'learning_rate': 0.009247626866981405, 'batch_size': 32, 'sequence_length': 45, 'dropout': 0.3067946960450405, 'weight_decay': 1.7674800709297268e-05}. Best is trial 4 with value: 0.854693919717165.
[I 2025-07-21 04:42:28,113] Trial 8 finished with value: 0.582764118629539 and parameters: {'hidden_size': 512, 'num_layers': 4, 'learning_rate': 3.695581256608931e-05, 'batch_size': 16, 'sequence_length': 85, 'dropout': 0.3117097693618409, 'weight_decay': 2.705894658750633e-05}. Best is trial 4 with value: 0.854693919717165.
[I 2025-07-21 04:42:28,115] Trial 9 finished with value: 0.8697213886021053 and parameters: {'hidden_size': 256, 'num_layers': 4, 'learning_rate': 0.00040887116106765037, 'batch_size': 32, 'sequence_length': 51, 'dropout': 0.3439118229093934, 'weight_decay': 1.4359412248753366e-05}. Best is trial 9 with value: 0.8697213886021053.
🔧 Advanced memory trimming applied
💾 Memory usage: 1600.2 MB
🧠 Multi-Brain analyzing gru training for AUDUSD...
🧠 Multi-Brain analyzing gru training for AUDUSD...
🔍 DEBUG: Starting _internal_analyze_training_situation for gru
🔍 DEBUG: Creating cache key...
🔍 DEBUG: Cache key created successfully
🔍 DEBUG: Checking cache...
🔍 DEBUG: Starting brain analysis...
🎯 Optuna Brain: Analyzing hyperparameters...
🔍 DEBUG: Calling optuna_brain.suggest_hyperparameters...
🎯 Optuna found best params: {'hidden_size': 256, 'num_layers': 4, 'learning_rate': 0.00040887116106765037, 'batch_size': 32, 'sequence_length': 51, 'dropout': 0.3439118229093934, 'weight_decay': 1.4359412248753366e-05}
✅ Google Drive cache directories created at: /content/drive/MyDrive/project2/cache
💾 Saved to Google Drive cache: /content/drive/MyDrive/project2/cache/optuna_studies/study_cache.pkl
💾 Optuna studies saved to Google Drive
💾 Cached successful result for optuna_hyperparameters
🔍 DEBUG: Optuna result: <class 'dict'>
🔍 DEBUG: Updated hyperparameter_suggestions with Optuna results
🤖 AutoGluon Brain: Analyzing model selection...
🔍 DEBUG: Calling autogluon_brain.recommend_models...
🔧 Applying final sklearn.metrics fix before AutoGluon...
🔧 Fixed sklearn.metrics.fbeta_score compatibility i