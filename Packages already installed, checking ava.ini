BUG: Creating cache key...
🔍 DEBUG: Cache key created successfully
🔍 DEBUG: Checking cache...
🔍 DEBUG: Starting brain analysis...
🎯 Optuna Brain: Analyzing hyperparameters...
🔍 DEBUG: Calling optuna_brain.suggest_hyperparameters...
✅ Using cached result for optuna_hyperparameters
🔍 DEBUG: Optuna result: <class 'dict'>
🔍 DEBUG: Updated hyperparameter_suggestions with Optuna results
🤖 AutoGluon Brain: Analyzing model selection...
🔧 Applying final sklearn.metrics fix before AutoGluon...
🔧 Fixed sklearn.metrics.fbeta_score compatibility issue
🤖 AutoGluon: Running real model selection...
🎯 AutoGluon best model: CatBoost (score: 0.815)
🚀 Ray Brain: Analyzing distributed training...
🚀 Ray <PERSON>ne: Running real distributed optimization...
+-----------------------------------------------------------------------+
| Configuration for experiment     train_function_2025-07-21_03-47-38   |
+-----------------------------------------------------------------------+
| Search algorithm                 BasicVariantGenerator                |
| Scheduler                        FIFOScheduler                        |
| Number of trials                 5                                    |
+-----------------------------------------------------------------------+

View detailed results here: /root/ray_results/train_function_2025-07-21_03-47-38
To visualize your results with TensorBoard, run: `tensorboard --logdir /tmp/ray_temp/session_2025-07-21_03-47-33_263038_167178/artifacts/2025-07-21_03-47-38/train_function_2025-07-21_03-47-38/driver_artifacts`

🎯 Ray Tune best config: {'learning_rate': 0.000411749420030231, 'batch_size': 32, 'hidden_size': 256}
🎯 Ray Tune best score: 0.834
🎯 PyCaret Brain: Analyzing data patterns...
✅ Using cached result for pycaret_data_analysis
✅ Using cached PyCaret analysis result
🛡️ Added missing key: early_stopping_config
🛡️ Added missing key: performance_grade
🛡️ Added missing key: unseen
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
🚨 CRITICAL: Multi-Brain analysis completely failed: 'hyperparameter_suggestions'
🔄 Using emergency fallback analysis...
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
✅ initial_analysis analysis completed successfully
🎯 Early stopping: Disabled
   📊 Multi-Brain Analysis Complete!
   🎯 Action: train_advanced
   💪 Confidence: 75.0%
   🧠 Reasoning: Safe fallback analysis for initial_analysis
   🎯 Market Domination Potential: HIGH

📋 STEP 2: TRAINING MARKET-DOMINATING MODELS
==================================================

🧠 MULTI-BRAIN: Multi-Symbol Multi-Style Training
   🎯 Primary Symbol: AUDUSD
   📊 Available Symbols: Multiple symbols analyzed
   🎨 Trading Styles: 10 professional styles
   ⏰ Session-Aware: 03:47
   🧠 Multi-Brain loaded symbol data for analysis

🎯 SMART TRAINING STRATEGY
========================================
📈 LSTM/GRU: Transfer Learning + Fine-tuning
🤖 DQN/PPO: Pre-trained Models + Fine-tuning


📈 Training Market-Dominating LSTM (Transfer Learning) - MONITORED...
🧠 Internal Brain monitoring: safe_analyze_training_situation_LSTM
🧠 Multi-Brain analyzing LSTM training for AUDUSD...
🧠 Multi-Brain analyzing LSTM training for AUDUSD...
🔍 DEBUG: Starting _internal_analyze_training_situation for LSTM
🔍 DEBUG: Creating cache key...
🔍 DEBUG: Cache key created successfully
🔍 DEBUG: Checking cache...
🔍 DEBUG: Starting brain analysis...
🎯 Optuna Brain: Analyzing hyperparameters...
🔍 DEBUG: Calling optuna_brain.suggest_hyperparameters...
✅ Using cached result for optuna_hyperparameters
🔍 DEBUG: Optuna result: <class 'dict'>
🔍 DEBUG: Updated hyperparameter_suggestions with Optuna results
🤖 AutoGluon Brain: Analyzing model selection...
🔧 Applying final sklearn.metrics fix before AutoGluon...
🔧 Fixed sklearn.metrics.fbeta_score compatibility issue
🤖 AutoGluon: Running real model selection...
🎯 AutoGluon best model: LightGBM (score: 0.710)
🚀 Ray Brain: Analyzing distributed training...
🚀 Ray Tune: Running real distributed optimization...
+-----------------------------------------------------------------------+
| Configuration for experiment     train_function_2025-07-21_03-48-27   |
+-----------------------------------------------------------------------+
| Search algorithm                 BasicVariantGenerator                |
| Scheduler                        FIFOScheduler                        |
| Number of trials                 5                                    |
+-----------------------------------------------------------------------+

View detailed results here: /root/ray_results/train_function_2025-07-21_03-48-27
To visualize your results with TensorBoard, run: `tensorboard --logdir /tmp/ray_temp/session_2025-07-21_03-47-33_263038_167178/artifacts/2025-07-21_03-48-27/train_function_2025-07-21_03-48-27/driver_artifacts`

🎯 Ray Tune best config: {'learning_rate': 0.0004106793703464877, 'batch_size': 32, 'hidden_size': 128}
🎯 Ray Tune best score: 0.817
🎯 PyCaret Brain: Analyzing data patterns...
✅ Using cached result for pycaret_data_analysis
✅ Using cached PyCaret analysis result
🛡️ Added missing key: early_stopping_config
🛡️ Added missing key: performance_grade
🛡️ Added missing key: unseen
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
🚨 CRITICAL: Multi-Brain analysis completely failed: 'hyperparameter_suggestions'
🔄 Using emergency fallback analysis...
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
✅ LSTM analysis completed successfully
🎯 Early stopping: Disabled
✅ safe_analyze_training_situation_LSTM completed successfully
🎯 Starting monitored training for LSTM
✅ Found checkpoint: LSTM at /content/drive/MyDrive/project2/models/checkpoints/LSTM/progress.json
🔄 Resuming LSTM from step 0
🔄 LSTM will resume from checkpoint
🧠 Direct training with Internal Brain watching: LSTM
🎯 Transfer Learning LSTM Strategy:
   📚 Using proven architecture patterns
   🔧 Multi-Brain optimized hyperparameters
   ⚡ Faster convergence with smart initialization
🔄 Resuming from checkpoint: epoch 3
📂 Model path: /content/drive/MyDrive/project2/models/checkpoints/LSTM/LSTM_latest
🎯 Using optimized config: {'sequence_length': 60, 'hidden_size': 256, 'num_layers': 3, 'learning_rate': 0.001, 'batch_size': 32, 'dropout': 0.2, 'transfer_learning': True}
📈 Training Market-Dominating LSTM with Multi-Brain System...
🏦 Integrating Advanced Account Management...
🧪 Enabling Advanced Backtesting...
🏦 Using risk profile: moderate
🧠 Multi-Brain approved training with 75.0% confidence
🎯 Using Transfer Learning configuration
   📚 Architecture: 256 hidden units, 3 layers
   ⚡ Learning rate: 0.001
   🎯 Batch size: 32
🎯 Merging Transfer Learning config with Multi-Brain suggestions
🎯 Final merged config: {'learning_rate': 0.001, 'batch_size': 32, 'hidden_size': 256, 'num_layers': 3, 'dropout': 0.2, 'sequence_length': 60, 'patience': 50, 'min_delta': 0.001, 'early_stopping_enabled': False, 'transfer_learning': True}
🧠 Multi-Brain optimizations: 2 suggestions
💡 Multi-Brain optimizations:
   • Safe fallback for LSTM
   • Early stopping disabled
   🔍 Original data shape: (6209, 168)
   🧹 Cleaned data shape: (6209, 168)
   📊 Features: 167, Samples: 6209
   ⚠️ GPU not available, using CPU
   🔍 Validating tensors: X_train=(4943, 30, 167), y_train=(4943,)
   💾 Creating memory-efficient tensors for MAXIMUM parameters...
   📊 Tensor memory usage: X_train=94.5MB
   🎯 Total model + data memory: ~121.2MB
   ✅ Tensors successfully moved to cpu
   🎯 Training device: cpu
   🧠 SMART config: hidden_size=256, num_layers=3
   💪 LSTM parameters: ~1.3M parameters
   💾 Estimated memory: ~5.0MB
   🎯 Memory-optimized for stable training!
   📈 OPTIMIZED TRAINING: LR=0.001, weight_decay=1e-3, scheduler_patience=25
   🧪 Testing gradient flow...
   ✅ Gradient flow test: PASSED
   ⚠️ Continual learning not available, using standard scheduler
   🧠 Model parameters: 1,873,730
   ⏰ Training started at: 03:48:45
✅ Found checkpoint: LSTM at /content/drive/MyDrive/project2/models/checkpoints/LSTM/progress.json
🔄 Resuming LSTM from step 0
🔄 Resuming LSTM training from epoch 3
✅ LSTM model loaded from checkpoint
   ⚠️ Weights-only loading failed, trying legacy mode...
   🔄 Checkpoint loaded: advanced_lstm
      📅 Saved: 2025-07-21T03:13:03.323876
      📊 Epoch: 3, Loss: 0.641377, Performance: 0.2968
   🎯 ENHANCED Training: 500 epochs, patience 50
   📊 ENHANCED improvement threshold: 0.002 (optimized for better learning)
   🔧 ENHANCED LR: 0.001, Dropout: 0.5, Grad Clip: 0.5
   🔄 Resuming training from epoch 4
   📊 Previous best: Loss 0.641377, Performance 0.2968
   ⏱️ Estimated time: 248.0 minutes
   💪 ULTIMATE LSTM Power: 1,873,730 parameters
   📊 Training on 4943 samples with 167 features
   🎯 Target: Minimize RMSE and maximize correlation
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.1508
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3121
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3590
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3217
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3106
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3406
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3473
   📊 Final performance: 0.0000
   Epoch 10/500 (2.0%): Train Loss: 0.000073, Val Loss: 0.656097, Performance: 0.0000
      ⏰ Elapsed: 0.6m, ETA: 26.8m
      💪 ULTIMATE LSTM: 1,873,730 parameters
      🎯 Best Loss: 0.641377, Patience: 7/50
      📈 Learning Rate: 0.000500
      📊 No improvement for 7 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3455
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3461
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3460
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3452
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3484
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3495
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3560
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3587
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3616
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3670
   📊 Final performance: 0.0000
   Epoch 20/500 (4.0%): Train Loss: 0.000027, Val Loss: 0.654655, Performance: 0.0000
      ⏰ Elapsed: 1.4m, ETA: 32.5m
      💪 ULTIMATE LSTM: 1,873,730 parameters
      🎯 Best Loss: 0.641377, Patience: 17/50
      📈 Learning Rate: 0.000125
      📊 No improvement for 17 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3680
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3706
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3712
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3716
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3722
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3725
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3728
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3732
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3745
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3774
   📊 Final performance: 0.0000
   Epoch 30/500 (6.0%): Train Loss: 0.000048, Val Loss: 0.653904, Performance: 0.0000
      ⏰ Elapsed: 2.2m, ETA: 33.9m
      💪 ULTIMATE LSTM: 1,873,730 parameters
      🎯 Best Loss: 0.641377, Patience: 27/50
      📈 Learning Rate: 0.000063
      📊 No improvement for 27 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3776
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3777
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3778
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3778
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3780
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3781
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3781
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3782
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3783
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3783
   📊 Final performance: 0.0000
   Epoch 40/500 (8.0%): Train Loss: 0.000013, Val Loss: 0.653537, Performance: 0.0000
      ⏰ Elapsed: 3.1m, ETA: 34.3m
      💪 ULTIMATE LSTM: 1,873,730 parameters
      🎯 Best Loss: 0.641377, Patience: 37/50
      📈 Learning Rate: 0.000016
      📊 No improvement for 37 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3784
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3784
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3785
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3785
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3785
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3786
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3786
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3786
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3786
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3786
   📊 Final performance: 0.0000
   Epoch 50/500 (10.0%): Train Loss: 0.000065, Val Loss: 0.653491, Performance: 0.0000
      ⏰ Elapsed: 3.9m, ETA: 34.2m
      💪 ULTIMATE LSTM: 1,873,730 parameters
      🎯 Best Loss: 0.641377, Patience: 47/50
      📈 Learning Rate: 0.000004
      📊 No improvement for 47 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3786
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3786
   📊 Final performance: 0.0000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 Correlation calculated: -0.3786
   📊 Final performance: 0.0000
   🧠 Multi-Brain Early Stopping Decision:
   🛑 Early stopping at epoch 53 (patience: 50/50)
   📊 Best validation loss achieved: 0.641377
   🧠 Brain confidence in stopping: 75.0%
   ✅ Best model loaded successfully
📊 Running advanced backtesting...
🎯 Running LSTM-specialized backtest...
📊 Running general backtest for comparison...

📋 LSTM TRAINING SUMMARY:
   🏆 Performance Grade: Moderate
   📊 Final Score: 0.750
   🚨 Main Issues: Backtesting module not available
   💡 Key Recommendations: Enable backtesting module, Verify model configuration
✅ Market-Dominating LSTM training completed!
   📊 Test RMSE: 0.800860 (Lower is better)
   � Backtest Score: 1.2000
   💰 Simulated Return: 15.00%
   🎯 Win Rate: 60.00%
❌ LSTM training failed: 'sharpe_ratio'
💾 Emergency checkpoint saved: /content/emergency_lstm_checkpoint.pth
✅ LSTM training completed successfully

🧠 Training Advanced GRU (Transfer Learning) - MONITORED...
🧠 Internal Brain monitoring: safe_analyze_training_situation_GRU
🧠 Multi-Brain analyzing GRU training for AUDUSD...
🧠 Multi-Brain analyzing GRU training for AUDUSD...
🔍 DEBUG: Starting _internal_analyze_training_situation for GRU
🔍 DEBUG: Creating cache key...
🔍 DEBUG: Cache key created successfully
🔍 DEBUG: Checking cache...
🔍 DEBUG: Starting brain analysis...
🎯 Optuna Brain: Analyzing hyperparameters...
🔍 DEBUG: Calling optuna_brain.suggest_hyperparameters...
✅ Using cached result for optuna_hyperparameters
🔍 DEBUG: Optuna result: <class 'dict'>
🔍 DEBUG: Updated hyperparameter_suggestions with Optuna results
🤖 AutoGluon Brain: Analyzing model selection...
🔧 Applying final sklearn.metrics fix before AutoGluon...
🔧 Fixed sklearn.metrics.fbeta_score compatibility issue
🤖 AutoGluon: Running real model selection...
Traceback (most recent call last):
  File "<string>", line 12348, in train_advanced_lstm
KeyError: 'sharpe_ratio'