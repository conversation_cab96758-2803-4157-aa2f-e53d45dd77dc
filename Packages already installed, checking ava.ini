Checkpoint saved: LSTM
✅ LSTM checkpoint saved to: /content/drive/MyDrive/project2/models/checkpoints/LSTM/LSTM_latest.pth
   💾 Checkpoint saved: advanced_lstm at epoch 0
   Epoch 0/500 (0.0%): Train Loss: 0.086736, Val Loss: 0.692892, Performance: 0.6760
      ⏰ Elapsed: 0.0m, ETA: 0.0m
      💪 ULTIMATE LSTM: 122,418 parameters
      🎯 Best Loss: 0.692892, Patience: 0/100
      📈 Learning Rate: 0.001000
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5105, Recall=1.0000, F1=0.6760
   📊 Final performance: 0.6760 (correlation: 0.6760)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5105, Recall=1.0000, F1=0.6760
   📊 Final performance: 0.6760 (correlation: 0.6760)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5105, Recall=1.0000, F1=0.6760
   📊 Final performance: 0.6760 (correlation: 0.6760)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5105, Recall=1.0000, F1=0.6760
   📊 Final performance: 0.6760 (correlation: 0.6760)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5105, Recall=1.0000, F1=0.6760
   📊 Final performance: 0.6760 (correlation: 0.6760)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5105, Recall=1.0000, F1=0.6760
   📊 Final performance: 0.6760 (correlation: 0.6760)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5105, Recall=1.0000, F1=0.6760
   📊 Final performance: 0.6760 (correlation: 0.6760)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5105, Recall=1.0000, F1=0.6760
   📊 Final performance: 0.6760 (correlation: 0.6760)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5105, Recall=1.0000, F1=0.6760
   📊 Final performance: 0.6760 (correlation: 0.6760)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5105, Recall=1.0000, F1=0.6760
   📊 Final performance: 0.6760 (correlation: 0.6760)
   Epoch 10/500 (2.0%): Train Loss: 0.086436, Val Loss: 0.693025, Performance: 0.6760
      ⏰ Elapsed: 0.1m, ETA: 6.6m
      💪 ULTIMATE LSTM: 122,418 parameters
      🎯 Best Loss: 0.692892, Patience: 10/100
      📈 Learning Rate: 0.000950
      📊 No improvement for 10 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5105, Recall=1.0000, F1=0.6760
   📊 Final performance: 0.6760 (correlation: 0.6760)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5105, Recall=1.0000, F1=0.6760
   📊 Final performance: 0.6760 (correlation: 0.6760)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5129, Precision=0.5118, Recall=0.9984, F1=0.6767
   📊 Final performance: 0.6767 (correlation: 0.6767)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5106, Recall=0.9937, F1=0.6746
   📊 Final performance: 0.6746 (correlation: 0.6746)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5081, Precision=0.5094, Recall=0.9842, F1=0.6714
   📊 Final performance: 0.6714 (correlation: 0.6714)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5097, Precision=0.5104, Recall=0.9762, F1=0.6703
   📊 Final performance: 0.6703 (correlation: 0.6703)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5113, Precision=0.5112, Recall=0.9731, F1=0.6703
   📊 Final performance: 0.6703 (correlation: 0.6703)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5129, Precision=0.5122, Recall=0.9683, F1=0.6700
   📊 Final performance: 0.6700 (correlation: 0.6700)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5162, Precision=0.5139, Recall=0.9683, F1=0.6714
   📊 Final performance: 0.6714 (correlation: 0.6714)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5154, Precision=0.5135, Recall=0.9651, F1=0.6703
   📊 Final performance: 0.6703 (correlation: 0.6703)
   Epoch 20/500 (4.0%): Train Loss: 0.086661, Val Loss: 0.693161, Performance: 0.6703
      ⏰ Elapsed: 0.3m, ETA: 7.6m
      💪 ULTIMATE LSTM: 122,418 parameters
      🎯 Best Loss: 0.692892, Patience: 20/100
      📈 Learning Rate: 0.000902
      📊 No improvement for 20 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5097, Precision=0.5106, Recall=0.9540, F1=0.6652
   📊 Final performance: 0.6652 (correlation: 0.6652)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5110, Recall=0.9540, F1=0.6656
   📊 Final performance: 0.6656 (correlation: 0.6656)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5097, Precision=0.5107, Recall=0.9477, F1=0.6637
   📊 Final performance: 0.6637 (correlation: 0.6637)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5057, Precision=0.5087, Recall=0.9255, F1=0.6565
   📊 Final performance: 0.6565 (correlation: 0.6565)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5057, Precision=0.5088, Recall=0.9176, F1=0.6546
   📊 Final performance: 0.6546 (correlation: 0.6546)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5032, Precision=0.5075, Recall=0.9065, F1=0.6507
   📊 Final performance: 0.6507 (correlation: 0.6507)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5049, Precision=0.5085, Recall=0.9049, F1=0.6511
   📊 Final performance: 0.6511 (correlation: 0.6511)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5089, Precision=0.5108, Recall=0.9017, F1=0.6521
   📊 Final performance: 0.6521 (correlation: 0.6521)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5113, Precision=0.5124, Recall=0.8875, F1=0.6497
   📊 Final performance: 0.6497 (correlation: 0.6497)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5113, Precision=0.5127, Recall=0.8669, F1=0.6443
   📊 Final performance: 0.6443 (correlation: 0.6443)
   Epoch 30/500 (6.0%): Train Loss: 0.086008, Val Loss: 0.693421, Performance: 0.6443
      ⏰ Elapsed: 0.5m, ETA: 6.9m
      💪 ULTIMATE LSTM: 122,418 parameters
      🎯 Best Loss: 0.692892, Patience: 30/100
      📈 Learning Rate: 0.000857
      📊 No improvement for 30 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5097, Precision=0.5121, Recall=0.8415, F1=0.6367
   📊 Final performance: 0.6367 (correlation: 0.6367)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5128, Recall=0.8241, F1=0.6322
   📊 Final performance: 0.6322 (correlation: 0.6322)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5105, Precision=0.5133, Recall=0.7924, F1=0.6231
   📊 Final performance: 0.6231 (correlation: 0.6231)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.5032, Precision=0.5090, Recall=0.7623, F1=0.6104
   📊 Final performance: 0.6104 (correlation: 0.6104)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4984, Precision=0.5059, Recall=0.7448, F1=0.6026
   📊 Final performance: 0.6026 (correlation: 0.6026)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4943, Precision=0.5033, Recall=0.7227, F1=0.5934
   📊 Final performance: 0.5934 (correlation: 0.5934)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4960, Precision=0.5045, Recall=0.7163, F1=0.5920
   📊 Final performance: 0.5920 (correlation: 0.5920)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4951, Precision=0.5040, Recall=0.6989, F1=0.5857
   📊 Final performance: 0.5857 (correlation: 0.5857)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4919, Precision=0.5018, Recall=0.6783, F1=0.5768
   📊 Final performance: 0.5768 (correlation: 0.5768)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4814, Precision=0.4940, Recall=0.6498, F1=0.5613
   📊 Final performance: 0.5613 (correlation: 0.5613)
   Epoch 40/500 (8.0%): Train Loss: 0.086524, Val Loss: 0.696417, Performance: 0.5613
      ⏰ Elapsed: 0.6m, ETA: 6.4m
      💪 ULTIMATE LSTM: 122,418 parameters
      🎯 Best Loss: 0.692892, Patience: 40/100
      📈 Learning Rate: 0.000774
      📊 No improvement for 40 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4822, Precision=0.4945, Recall=0.6418, F1=0.5586
   📊 Final performance: 0.5586 (correlation: 0.5586)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4830, Precision=0.4951, Recall=0.6387, F1=0.5578
   📊 Final performance: 0.5578 (correlation: 0.5578)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4798, Precision=0.4926, Recall=0.6307, F1=0.5532
   📊 Final performance: 0.5532 (correlation: 0.5532)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4790, Precision=0.4919, Recall=0.6276, F1=0.5515
   📊 Final performance: 0.5515 (correlation: 0.5515)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4838, Precision=0.4956, Recall=0.6244, F1=0.5526
   📊 Final performance: 0.5526 (correlation: 0.5526)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4822, Precision=0.4943, Recall=0.6197, F1=0.5499
   📊 Final performance: 0.5499 (correlation: 0.5499)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4830, Precision=0.4950, Recall=0.6228, F1=0.5516
   📊 Final performance: 0.5516 (correlation: 0.5516)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4822, Precision=0.4944, Recall=0.6276, F1=0.5531
   📊 Final performance: 0.5531 (correlation: 0.5531)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4846, Precision=0.4963, Recall=0.6339, F1=0.5567
   📊 Final performance: 0.5567 (correlation: 0.5567)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4862, Precision=0.4975, Recall=0.6387, F1=0.5593
   📊 Final performance: 0.5593 (correlation: 0.5593)
   Epoch 50/500 (10.0%): Train Loss: 0.085983, Val Loss: 0.701141, Performance: 0.5593
      ⏰ Elapsed: 0.7m, ETA: 6.1m
      💪 ULTIMATE LSTM: 122,418 parameters
      🎯 Best Loss: 0.692892, Patience: 50/100
      📈 Learning Rate: 0.000735
      📊 No improvement for 50 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4879, Precision=0.4988, Recall=0.6403, F1=0.5607
   📊 Final performance: 0.5607 (correlation: 0.5607)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4895, Precision=0.5000, Recall=0.6387, F1=0.5609
   📊 Final performance: 0.5609 (correlation: 0.5609)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4903, Precision=0.5006, Recall=0.6371, F1=0.5607
   📊 Final performance: 0.5607 (correlation: 0.5607)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4887, Precision=0.4994, Recall=0.6355, F1=0.5593
   📊 Final performance: 0.5593 (correlation: 0.5593)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4903, Precision=0.5006, Recall=0.6371, F1=0.5607
   📊 Final performance: 0.5607 (correlation: 0.5607)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4887, Precision=0.4994, Recall=0.6323, F1=0.5580
   📊 Final performance: 0.5580 (correlation: 0.5580)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4846, Precision=0.4962, Recall=0.6244, F1=0.5530
   📊 Final performance: 0.5530 (correlation: 0.5530)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4822, Precision=0.4943, Recall=0.6133, F1=0.5474
   📊 Final performance: 0.5474 (correlation: 0.5474)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4854, Precision=0.4968, Recall=0.6086, F1=0.5470
   📊 Final performance: 0.5470 (correlation: 0.5470)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4862, Precision=0.4974, Recall=0.6038, F1=0.5455
   📊 Final performance: 0.5455 (correlation: 0.5455)
   Epoch 60/500 (12.0%): Train Loss: 0.084007, Val Loss: 0.708447, Performance: 0.5455
      ⏰ Elapsed: 0.8m, ETA: 5.9m
      💪 ULTIMATE LSTM: 122,418 parameters
      🎯 Best Loss: 0.692892, Patience: 60/100
      📈 Learning Rate: 0.000698
      📊 No improvement for 60 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4879, Precision=0.4987, Recall=0.6038, F1=0.5462
   📊 Final performance: 0.5462 (correlation: 0.5462)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4879, Precision=0.4987, Recall=0.6022, F1=0.5456
   📊 Final performance: 0.5456 (correlation: 0.5456)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4911, Precision=0.5013, Recall=0.6022, F1=0.5472
   📊 Final performance: 0.5472 (correlation: 0.5472)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4895, Precision=0.5000, Recall=0.5975, F1=0.5444
   📊 Final performance: 0.5444 (correlation: 0.5444)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4903, Precision=0.5007, Recall=0.5927, F1=0.5428
   📊 Final performance: 0.5428 (correlation: 0.5428)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4895, Precision=0.5000, Recall=0.5911, F1=0.5418
   📊 Final performance: 0.5418 (correlation: 0.5418)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4903, Precision=0.5007, Recall=0.5895, F1=0.5415
   📊 Final performance: 0.5415 (correlation: 0.5415)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4903, Precision=0.5007, Recall=0.5864, F1=0.5401
   📊 Final performance: 0.5401 (correlation: 0.5401)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4919, Precision=0.5020, Recall=0.5864, F1=0.5409
   📊 Final performance: 0.5409 (correlation: 0.5409)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4895, Precision=0.5000, Recall=0.5816, F1=0.5377
   📊 Final performance: 0.5377 (correlation: 0.5377)
   Epoch 70/500 (14.0%): Train Loss: 0.085412, Val Loss: 0.727154, Performance: 0.5377
      ⏰ Elapsed: 0.9m, ETA: 5.6m
      💪 ULTIMATE LSTM: 122,418 parameters
      🎯 Best Loss: 0.692892, Patience: 70/100
      📈 Learning Rate: 0.000663
      📊 No improvement for 70 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4879, Precision=0.4986, Recall=0.5769, F1=0.5349
   📊 Final performance: 0.5349 (correlation: 0.5349)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4879, Precision=0.4986, Recall=0.5737, F1=0.5335
   📊 Final performance: 0.5335 (correlation: 0.5335)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4871, Precision=0.4979, Recall=0.5721, F1=0.5324
   📊 Final performance: 0.5324 (correlation: 0.5324)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4887, Precision=0.4993, Recall=0.5705, F1=0.5325
   📊 Final performance: 0.5325 (correlation: 0.5325)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4879, Precision=0.4986, Recall=0.5674, F1=0.5308
   📊 Final performance: 0.5308 (correlation: 0.5308)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4871, Precision=0.4979, Recall=0.5658, F1=0.5297
   📊 Final performance: 0.5297 (correlation: 0.5297)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4871, Precision=0.4979, Recall=0.5658, F1=0.5297
   📊 Final performance: 0.5297 (correlation: 0.5297)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4862, Precision=0.4972, Recall=0.5658, F1=0.5293
   📊 Final performance: 0.5293 (correlation: 0.5293)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4846, Precision=0.4958, Recall=0.5674, F1=0.5292
   📊 Final performance: 0.5292 (correlation: 0.5292)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4838, Precision=0.4952, Recall=0.5689, F1=0.5295
   📊 Final performance: 0.5295 (correlation: 0.5295)
   Epoch 80/500 (16.0%): Train Loss: 0.085459, Val Loss: 0.749030, Performance: 0.5295
      ⏰ Elapsed: 1.1m, ETA: 5.5m
      💪 ULTIMATE LSTM: 122,418 parameters
      🎯 Best Loss: 0.692892, Patience: 80/100
      📈 Learning Rate: 0.000599
      📊 No improvement for 80 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4838, Precision=0.4952, Recall=0.5753, F1=0.5323
   📊 Final performance: 0.5323 (correlation: 0.5323)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4879, Precision=0.4987, Recall=0.5864, F1=0.5390
   📊 Final performance: 0.5390 (correlation: 0.5390)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4854, Precision=0.4967, Recall=0.5911, F1=0.5398
   📊 Final performance: 0.5398 (correlation: 0.5398)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4838, Precision=0.4954, Recall=0.5959, F1=0.5410
   📊 Final performance: 0.5410 (correlation: 0.5410)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4854, Precision=0.4967, Recall=0.6022, F1=0.5444
   📊 Final performance: 0.5444 (correlation: 0.5444)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4854, Precision=0.4968, Recall=0.6117, F1=0.5483
   📊 Final performance: 0.5483 (correlation: 0.5483)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4879, Precision=0.4987, Recall=0.6197, F1=0.5527
   📊 Final performance: 0.5527 (correlation: 0.5527)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4879, Precision=0.4988, Recall=0.6339, F1=0.5583
   📊 Final performance: 0.5583 (correlation: 0.5583)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4871, Precision=0.4981, Recall=0.6387, F1=0.5597
   📊 Final performance: 0.5597 (correlation: 0.5597)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4911, Precision=0.5012, Recall=0.6513, F1=0.5665
   📊 Final performance: 0.5665 (correlation: 0.5665)
   Epoch 90/500 (18.0%): Train Loss: 0.079316, Val Loss: 0.757075, Performance: 0.5665
      ⏰ Elapsed: 1.2m, ETA: 5.2m
      💪 ULTIMATE LSTM: 122,418 parameters
      🎯 Best Loss: 0.692892, Patience: 90/100
      📈 Learning Rate: 0.000569
      📊 No improvement for 90 epochs
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4943, Precision=0.5036, Recall=0.6656, F1=0.5734
   📊 Final performance: 0.5734 (correlation: 0.5734)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4951, Precision=0.5041, Recall=0.6799, F1=0.5789
   📊 Final performance: 0.5789 (correlation: 0.5789)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4968, Precision=0.5053, Recall=0.6846, F1=0.5814
   📊 Final performance: 0.5814 (correlation: 0.5814)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4992, Precision=0.5070, Recall=0.6926, F1=0.5854
   📊 Final performance: 0.5854 (correlation: 0.5854)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4951, Precision=0.5040, Recall=0.6973, F1=0.5851
   📊 Final performance: 0.5851 (correlation: 0.5851)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4968, Precision=0.5051, Recall=0.7021, F1=0.5875
   📊 Final performance: 0.5875 (correlation: 0.5875)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4927, Precision=0.5023, Recall=0.7005, F1=0.5850
   📊 Final performance: 0.5850 (correlation: 0.5850)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4935, Precision=0.5028, Recall=0.7021, F1=0.5860
   📊 Final performance: 0.5860 (correlation: 0.5860)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4943, Precision=0.5034, Recall=0.7005, F1=0.5858
   📊 Final performance: 0.5858 (correlation: 0.5858)
   🔍 Debug: val_outputs shape: (1236,), y_test shape: (1236,)
   🎯 LSTM Metrics: Accuracy=0.4919, Precision=0.5017, Recall=0.6941, F1=0.5824
   📊 Final performance: 0.5824 (correlation: 0.5824)
   Epoch 100/500 (20.0%): Train Loss: 0.051728, Val Loss: 0.900176, Performance: 0.5824
      ⏰ Elapsed: 1.3m, ETA: 5.1m
      💪 ULTIMATE LSTM: 122,418 parameters
      🎯 Best Loss: 0.692892, Patience: 100/100
      📈 Learning Rate: 0.000540
      📊 No improvement for 100 epochs
   🧠 Multi-Brain Early Stopping Decision:
   🛑 Early stopping at epoch 100
   📊 Best validation loss: 0.692892
   🧠 Brain confidence: 80.0%
   ✅ Best model loaded successfully
📊 Running advanced backtesting...
🎯 Running LSTM-specialized backtest...
📊 Running general backtest for comparison...

📋 LSTM TRAINING SUMMARY:
   🏆 Performance Grade: Moderate
   📊 Final Score: 0.750
   🚨 Main Issues: Backtesting module not available
   💡 Key Recommendations: Enable backtesting module, Verify model configuration
✅ Market-Dominating LSTM training completed!
   📊 Test RMSE: 0.832402 (Lower is better)
   � Backtest Score: 1.2000
   💰 Simulated Return: 15.00%
   🎯 Win Rate: 60.00%
   📈 Sharpe Ratio: 1.200
   🔥 Market Domination Score: 71.03%
   🧠 Genius Indicators: 0 created
   🚀 ULTIMATE Training: 500 epochs
   💪 ULTIMATE Power: 122,418 parameters
   💾 Model saved: /content/models/advanced_lstm_20250721_091428
   ✅ Brain confirms current style is optimal
🧪 Running Advanced Backtesting...
🧪 Running comprehensive backtest for Market_Dominating_LSTM...
⚠️ Backtesting failed: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (1236,) + inhomogeneous part.
❌ LSTM training failed: name 'risk_profile' is not defined
💾 Emergency checkpoint saved: /content/emergency_lstm_checkpoint.pth
❌ LSTM training failed: Invalid result

🧠 Training Advanced GRU (Transfer Learning) - MONITORED...
🔧 Running: safe_analyze_training_situation_GRU
🧠 Multi-Brain analyzing GRU training for AUDUSD...
🔧 Configuring GRU training for AUDUSD...
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
✅ GRU analysis completed successfully
🎯 Early stopping: Disabled
✅ safe_analyze_training_situation_GRU completed successfully
🎯 Starting monitored training for GRU
🆕 Starting fresh training for GRU
🧠 Direct training with Internal Brain watching: GRU
🎯 Transfer Learning GRU Strategy:
   📚 Using proven GRU architecture patterns
   🔧 Multi-Brain optimized hyperparameters
   ⚡ Enhanced with attention mechanisms
🎯 Using optimized GRU config: {'sequence_length': 60, 'hidden_size': 64, 'num_layers': 2, 'learning_rate': 0.001, 'batch_size': 32, 'dropout': 0.2, 'weight_decay': 0.0001, 'gradient_clipping': 0.5, 'optimizer': 'AdamW', 'attention': True, 'transfer_learning': True}
🧠 Training Advanced GRU with Multi-Brain Analysis...
🏦 Integrating Advanced Account Management...
🧪 Enabling Advanced Backtesting...
🏦 Using risk profile: moderate
🎯 Using Transfer Learning GRU configuration
   📚 Architecture: 64 hidden units, 2 layers
   ⚡ Learning rate: 0.001
   🎯 Attention: True
🚀 Optimizing memory for training...
💪 FORCING MAXIMUM parameters as requested by user
🚀 Ignoring memory constraints - using MAXIMUM power
Traceback (most recent call last):
  File "<string>", line 12583, in train_advanced_lstm
NameError: name 'risk_profile' is not defined
🧹 GC round 1: 636 objects collected
🔧 Advanced memory trimming applied
💾 Memory usage: 1479.9 MB
🧠 Multi-Brain analyzing gru training for AUDUSD...
🔧 Configuring gru training for AUDUSD...
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
✅ gru analysis completed successfully
🎯 Early stopping: Disabled
🧠 Brain Decision for AUDUSD: train_advanced
   🎯 Trading Style: day_trading
   📊 Style Confidence: 80.0%
   🎨 Using 0 style-specific indicators
🧠 Brain approved GRU training with 75.0% confidence
   🔧 Creating returns target for GRU...
❌ Advanced GRU training failed: cannot access local variable 'test_data' where it is not associated with a value
❌ GRU training failed: Invalid result

🤖 Training Market-Dominating DQN (Pre-trained + Fine-tune) - MONITORED...
🔧 Running: safe_analyze_training_situation_DQN
🧠 Multi-Brain analyzing DQN training for AUDUSD...
🔧 Configuring DQN training for AUDUSD...
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
✅ DQN analysis completed successfully
🎯 Early stopping: Disabled
✅ safe_analyze_training_situation_DQN completed successfully
🎯 Starting monitored training for DQN
✅ Found checkpoint: DQN at /content/drive/MyDrive/project2/models/checkpoints/DQN/progress.json
🔄 Resuming DQN from step 0
🔄 DQN will resume from checkpoint
🧠 Direct training with Internal Brain watching: DQN
❌ DQN training failed: train_pretrained_dqn() got an unexpected keyword argument 'resume_info'

🚀 Training Advanced PPO (Pre-trained + Fine-tune) - MONITORED...
🔧 Running: safe_analyze_training_situation_PPO
🧠 Multi-Brain analyzing PPO training for AUDUSD...
🔧 Configuring PPO training for AUDUSD...
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
✅ PPO analysis completed successfully
🎯 Early stopping: Disabled
✅ safe_analyze_training_situation_PPO completed successfully
🎯 Starting monitored training for PPO
✅ Found checkpoint: PPO at /content/drive/MyDrive/project2/models/checkpoints/PPO/progress.json
🔄 Resuming PPO from step 0
🔄 PPO will resume from checkpoint
🧠 Direct training with Internal Brain watching: PPO
❌ PPO training failed: train_pretrained_ppo() got an unexpected keyword argument 'resume_info'

================================================================================
🚀 PHASE 2: Training Research-Recommended Advanced Models
================================================================================

🏦 Training Advanced FinBERT (Financial Sentiment) - MONITORED...
🔧 Running: safe_analyze_training_situation_FinBERT
🧠 Multi-Brain analyzing FinBERT training for AUDUSD...
🔧 Configuring FinBERT training for AUDUSD...
🛡️ Added missing key: model_readiness
🛡️ Added missing key: training_status
✅ FinBERT analysis completed successfully
🎯 Early stopping: Disabled
✅ safe_analyze_training_situation_FinBERT completed successfully
🎯 Starting monitored training for FinBERT
🆕 Starting fresh training for FinBERT
🧠 Direct training with Internal Brain watching: FinBERT
🏦 Training Advanced FinBERT with Multi-Brain System...
🚀 Using Pre-trained FinBERT model (ProsusAI/finbert)
   📦 Fine-tuning for 50000 steps
   ⚡ Learning rate: 2e-05
   🎯 Batch size: 16
🧠 Brain approved FinBERT training with 80.0% confidence
📥 Loading FinBERT with advanced cache + optimization system...
⚠️ FinBERT import failed: AdvancedModelCacheSystem.download_model_with_cache() got an unexpected keyword argument 'model_kwargs'
🔄 Falling back to standard BERT model...
Traceback (most recent call last):
  File "<string>", line 12723, in train_advanced_gru
UnboundLocalError: cannot access local variable 'test_data' where it is not associated with a value
tokenizer_config.json: 100%
 48.0/48.0 [00:00<00:00, 2.73kB/s]
config.json: 100%
 570/570 [00:00<00:00, 41.0kB/s]
vocab.txt: 100%
 232k/232k [00:00<00:00, 1.12MB/s]
tokenizer.json: 100%
 466k/466k [00:00<00:00, 996kB/s]
model.safetensors: 100%
 440M/440M [00:17<00:00, 31.8MB/s]
Some weights of the model checkpoint at bert-base-uncased were not used when initializing BertForSequenceClassification: ['cls.predictions.transform.LayerNorm.bias', 'cls.predictions.transform.dense.weight', 'cls.seq_relationship.bias', 'cls.predictions.bias', 'cls.seq_relationship.weight', 'cls.predictions.transform.dense.bias', 'cls.predictions.transform.LayerNorm.weight']
- This IS expected if you are initializing BertForSequenceClassification from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing BertForSequenceClassification from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Some weights of BertForSequenceClassification were not initialized from the model checkpoint at bert-base-uncased and are newly initialized: ['classifier.weight', 'classifier.bias']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
🔧 Optimizing model memory (keeping ALL parameters)...
   ✅ Gradient checkpointing enabled
   🎯 ALL PARAMETERS PRESERVED!
✅ FinBERT loaded with ALL parameters + memory optimization!
🔥 Model optimized for cpu
⚠️ Mixed precision not available, using FP32
🎯 Advanced config:
   Learning Rate: 2e-05
   Actual Batch Size: 16
   Effective Batch Size: 256 (with 16x accumulation)
   Max Epochs: 3
   Mixed Precision: Disabled
📊 Creating high-quality financial sentiment dataset...
🏦 Creating financial sentiment dataset...
📊 Created financial sentiment dataset: 15 samples
🆕 Starting fresh training for FinBERT

🎯 ADVANCED OPTIMIZATION SUMMARY
==================================================
✅ Mixed Precision: Disabled (FP32)
✅ Gradient Accumulation: 16 steps
✅ Device: cpu
💾 CPU Memory: 1.9GB / 12.7GB

🚀 ALL PARAMETERS PRESERVED!
💪 Maximum performance with full model capacity!

🔄 Epoch 1/3
📦 Creating sentiment batches (batch_size=16)...
🔍 Validating sentiment model...
✅ Validation accuracy: 0.6667
Epoch 1: Loss=1.0480, Val_Score=0.6667
📁 Created checkpoint directory: /content/drive/MyDrive/project2/models/checkpoints/FinBERT
✅ Google Drive cache directories created at: /content/drive/MyDrive/project2/cache
💾 Saved to Google Drive cache: /content/drive/MyDrive/project2/checkpoints/FinBERT_progress.json
✅ Checkpoint saved: FinBERT