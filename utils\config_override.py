import os
import argparse
import yaml
from typing import Any, Dict

def override_config_with_env(config: Dict[str, Any]) -> Dict[str, Any]:
    """Override config values with environment variables if present."""
    for key in config:
        env_key = key.upper()
        if env_key in os.environ:
            val = os.environ[env_key]
            try:
                config[key] = type(config[key])(val)
            except Exception:
                config[key] = val
    return config

def override_config_with_cli(config: Dict[str, Any]) -> Dict[str, Any]:
    """Override config values with CLI arguments if present."""
    parser = argparse.ArgumentParser()
    for key, value in config.items():
        parser.add_argument(f'--{key}', type=type(value), default=None)
    args, _ = parser.parse_known_args()
    for key in config:
        cli_val = getattr(args, key, None)
        if cli_val is not None:
            config[key] = cli_val
    return config

def load_config_with_override(config_path: str = "config.yaml") -> Dict[str, Any]:
    with open(config_path, "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)
    config = override_config_with_env(config)
    config = override_config_with_cli(config)
    return config
