"""Advanced Reward Redistribution System

این ماژول سیستم پیشرفته‌ای برای تعدیل پاداش در دوره‌های مختلف معاملاتی فراهم می‌کند.
ویژگی‌های کلیدی:
- تعدیل پاداش بر اساس drawdown با الگوریتم‌های مختلف
- سیستم وزن‌دهی پویا بر اساس عملکرد
- تحلیل ریسک و بازگشت
- سیستم حافظه برای تطبیق با شرایط بازار
- پیش‌بینی پاداش آینده
- تحلیل sentiment و momentum
- نظارت بر کیفیت پاداش‌ها

Dependencies: numpy, pandas, scikit-learn, scipy
"""
from __future__ import annotations

import warnings
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import List, Dict, Tuple, Optional, Callable, Any, Union
import numpy as np
import pandas as pd
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor

__all__ = [
    "RewardRedistributor",
    "AdvancedRewardRedistributor", 
    "RewardStrategy",
    "RewardMemorySystem",
    "RewardPredictor",
    "RewardQualityMonitor",
    "redistribute",
    "RedistributionStrategy",
    "RiskAdjustedRewardSystem",
    "DynamicRewardWeighting",
    "RewardOptimizer"
]


class RedistributionStrategy(Enum):
    """استراتژی‌های مختلف تعدیل پاداش"""
    LINEAR = "linear"
    EXPONENTIAL = "exponential"
    LOGARITHMIC = "logarithmic"
    ADAPTIVE = "adaptive"
    RISK_PARITY = "risk_parity"
    MOMENTUM_BASED = "momentum_based"
    VOLATILITY_ADJUSTED = "volatility_adjusted"


class RewardStrategy(ABC):
    """کلاس پایه برای استراتژی‌های تعدیل پاداش"""
    
    @abstractmethod
    def calculate_multiplier(self, drawdown: float, context: Dict[str, Any]) -> float:
        """محاسبه ضریب تعدیل پاداش"""
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """نام استراتژی"""
        pass


class LinearRewardStrategy(RewardStrategy):
    """استراتژی خطی برای تعدیل پاداش"""
    
    def __init__(self, base_multiplier: float = 1.0, slope: float = 2.0):
        self.base_multiplier = base_multiplier
        self.slope = slope
    
    def calculate_multiplier(self, drawdown: float, context: Dict[str, Any]) -> float:
        return self.base_multiplier + (drawdown * self.slope)
    
    def get_strategy_name(self) -> str:
        return "Linear"


class ExponentialRewardStrategy(RewardStrategy):
    """استراتژی نمایی برای تعدیل پاداش"""
    
    def __init__(self, base_multiplier: float = 1.0, exponent: float = 2.0):
        self.base_multiplier = base_multiplier
        self.exponent = exponent
    
    def calculate_multiplier(self, drawdown: float, context: Dict[str, Any]) -> float:
        return self.base_multiplier * (1 + drawdown) ** self.exponent
    
    def get_strategy_name(self) -> str:
        return "Exponential"


class AdaptiveRewardStrategy(RewardStrategy):
    """استراتژی تطبیقی که بر اساس شرایط بازار تغییر می‌کند"""
    
    def __init__(self, volatility_threshold: float = 0.02, trend_threshold: float = 0.1):
        self.volatility_threshold = volatility_threshold
        self.trend_threshold = trend_threshold
    
    def calculate_multiplier(self, drawdown: float, context: Dict[str, Any]) -> float:
        volatility = context.get('volatility', 0.01)
        trend = context.get('trend', 0.0)
        
        # تعدیل بر اساس نوسان
        vol_adjustment = 1.0 + (volatility / self.volatility_threshold) * 0.5
        
        # تعدیل بر اساس ترند
        trend_adjustment = 1.0 + abs(trend) / self.trend_threshold * 0.3
        
        # ضریب نهایی
        base_multiplier = 1.0 + drawdown * 3.0
        return base_multiplier * vol_adjustment * trend_adjustment
    
    def get_strategy_name(self) -> str:
        return "Adaptive"


@dataclass
class RewardMemorySystem:
    """سیستم حافظه برای ذخیره و تحلیل تاریخچه پاداش‌ها"""
    
    max_memory_size: int = 10000
    memory_decay: float = 0.95
    
    def __post_init__(self):
        self.reward_history: List[float] = []
        self.drawdown_history: List[float] = []
        self.performance_history: List[float] = []
        self.timestamp_history: List[int] = []
        self.context_history: List[Dict[str, Any]] = []
    
    def add_record(self, reward: float, drawdown: float, performance: float, 
                   timestamp: int, context: Dict[str, Any]):
        """افزودن رکورد جدید به حافظه"""
        self.reward_history.append(reward)
        self.drawdown_history.append(drawdown)
        self.performance_history.append(performance)
        self.timestamp_history.append(timestamp)
        self.context_history.append(context.copy())
        
        # مدیریت اندازه حافظه
        if len(self.reward_history) > self.max_memory_size:
            self.reward_history.pop(0)
            self.drawdown_history.pop(0)
            self.performance_history.pop(0)
            self.timestamp_history.pop(0)
            self.context_history.pop(0)
    
    def get_weighted_average_performance(self) -> float:
        """محاسبه میانگین وزن‌دار عملکرد"""
        if not self.performance_history:
            return 0.0
        
        weights = np.array([self.memory_decay ** i for i in range(len(self.performance_history))])
        weights = weights[::-1]  # وزن‌های جدیدتر بیشتر
        
        return np.average(self.performance_history, weights=weights)
    
    def get_drawdown_statistics(self) -> Dict[str, float]:
        """آمار drawdown تاریخی"""
        if not self.drawdown_history:
            return {'mean': 0.0, 'std': 0.0, 'max': 0.0, 'percentile_95': 0.0}
        
        dd_array = np.array(self.drawdown_history)
        return {
            'mean': np.mean(dd_array),
            'std': np.std(dd_array),
            'max': np.max(dd_array),
            'percentile_95': np.percentile(dd_array, 95)
        }


class RewardPredictor:
    """پیش‌بینی‌کننده پاداش آینده"""
    
    def __init__(self, model_type: str = 'rf', lookback_window: int = 50):
        self.model_type = model_type
        self.lookback_window = lookback_window
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
    
    def _prepare_features(self, rewards: List[float], drawdowns: List[float], 
                         contexts: List[Dict[str, Any]]) -> np.ndarray:
        """تهیه ویژگی‌ها برای مدل"""
        features = []
        
        for i in range(len(rewards)):
            feature_row = []
            
            # ویژگی‌های پاداش
            if i >= 5:
                feature_row.extend([
                    np.mean(rewards[i-5:i]),  # میانگین 5 پاداش قبلی
                    np.std(rewards[i-5:i]),   # انحراف استاندارد
                    rewards[i-1] if i > 0 else 0,  # پاداش قبلی
                ])
            else:
                feature_row.extend([0, 0, 0])
            
            # ویژگی‌های drawdown
            feature_row.append(drawdowns[i])
            
            # ویژگی‌های context
            ctx = contexts[i] if i < len(contexts) else {}
            feature_row.extend([
                ctx.get('volatility', 0.01),
                ctx.get('trend', 0.0),
                ctx.get('volume', 1.0),
                ctx.get('spread', 0.001)
            ])
            
            features.append(feature_row)
        
        return np.array(features)
    
    def train(self, memory_system: RewardMemorySystem):
        """آموزش مدل پیش‌بینی"""
        if len(memory_system.reward_history) < self.lookback_window:
            return
        
        # تهیه داده‌ها
        features = self._prepare_features(
            memory_system.reward_history,
            memory_system.drawdown_history,
            memory_system.context_history
        )
        
        # هدف: پیش‌بینی پاداش بعدی
        targets = memory_system.reward_history[1:] + [memory_system.reward_history[-1]]
        
        if len(features) != len(targets):
            targets = targets[:len(features)]
        
        # استانداردسازی
        features_scaled = self.scaler.fit_transform(features)
        
        # آموزش مدل
        if self.model_type == 'rf':
            self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        else:
            self.model = LinearRegression()
        
        self.model.fit(features_scaled, targets)
        self.is_trained = True
    
    def predict_next_reward(self, current_context: Dict[str, Any], 
                           memory_system: RewardMemorySystem) -> float:
        """پیش‌بینی پاداش بعدی"""
        if not self.is_trained or not memory_system.reward_history:
            return 0.0
        
        # تهیه ویژگی‌های فعلی
        recent_rewards = memory_system.reward_history[-5:]
        recent_drawdowns = memory_system.drawdown_history[-1:]
        
        feature_row = []
        
        # ویژگی‌های پاداش
        if len(recent_rewards) >= 5:
            feature_row.extend([
                np.mean(recent_rewards),
                np.std(recent_rewards),
                recent_rewards[-1]
            ])
        else:
            feature_row.extend([0, 0, 0])
        
        # ویژگی‌های drawdown
        feature_row.append(recent_drawdowns[-1] if recent_drawdowns else 0)
        
        # ویژگی‌های context
        feature_row.extend([
            current_context.get('volatility', 0.01),
            current_context.get('trend', 0.0),
            current_context.get('volume', 1.0),
            current_context.get('spread', 0.001)
        ])
        
        # پیش‌بینی
        features_scaled = self.scaler.transform([feature_row])
        prediction = self.model.predict(features_scaled)[0]
        
        return prediction


class RewardQualityMonitor:
    """نظارت بر کیفیت پاداش‌ها"""
    
    def __init__(self, quality_threshold: float = 0.5):
        self.quality_threshold = quality_threshold
        self.quality_history: List[float] = []
    
    def calculate_reward_quality(self, rewards: List[float], 
                               performance: List[float]) -> float:
        """محاسبه کیفیت پاداش بر اساس همبستگی با عملکرد"""
        if len(rewards) < 10 or len(performance) < 10:
            return 0.5
        
        # همبستگی پیرسون
        correlation, p_value = stats.pearsonr(rewards, performance)
        
        # تعدیل بر اساس p-value
        if p_value > 0.05:
            correlation *= 0.5
        
        # نرمال‌سازی به بازه [0, 1]
        quality = (correlation + 1) / 2
        
        self.quality_history.append(quality)
        return quality
    
    def is_quality_acceptable(self) -> bool:
        """بررسی قابل قبول بودن کیفیت"""
        if not self.quality_history:
            return True
        
        recent_quality = np.mean(self.quality_history[-10:])
        return recent_quality >= self.quality_threshold
    
    def get_quality_trend(self) -> str:
        """روند کیفیت"""
        if len(self.quality_history) < 5:
            return "insufficient_data"
        
        recent = np.mean(self.quality_history[-5:])
        older = np.mean(self.quality_history[-10:-5]) if len(self.quality_history) >= 10 else recent
        
        if recent > older * 1.1:
            return "improving"
        elif recent < older * 0.9:
            return "declining"
        else:
            return "stable"


class DynamicRewardWeighting:
    """سیستم وزن‌دهی پویا برای پاداش‌ها"""
    
    def __init__(self, adaptation_rate: float = 0.1):
        self.adaptation_rate = adaptation_rate
        self.action_weights: Dict[str, float] = {}
        self.action_performance: Dict[str, List[float]] = {}
    
    def update_action_performance(self, action: str, performance: float):
        """به‌روزرسانی عملکرد اکشن"""
        if action not in self.action_performance:
            self.action_performance[action] = []
        
        self.action_performance[action].append(performance)
        
        # محدود کردن تاریخچه
        if len(self.action_performance[action]) > 100:
            self.action_performance[action].pop(0)
    
    def calculate_action_weight(self, action: str) -> float:
        """محاسبه وزن اکشن"""
        if action not in self.action_performance:
            return 1.0
        
        performances = self.action_performance[action]
        if len(performances) < 5:
            return 1.0
        
        # محاسبه وزن بر اساس عملکرد
        avg_performance = np.mean(performances[-10:])
        std_performance = np.std(performances[-10:])
        
        # وزن بر اساس شارپ ریشو
        if std_performance > 0:
            sharpe_ratio = avg_performance / std_performance
            weight = 1.0 + sharpe_ratio * 0.5
        else:
            weight = 1.0 + avg_performance
        
        return max(0.1, min(3.0, weight))  # محدود کردن وزن
    
    def get_weighted_reward(self, action: str, base_reward: float) -> float:
        """محاسبه پاداش وزن‌دار"""
        weight = self.calculate_action_weight(action)
        return base_reward * weight


class RiskAdjustedRewardSystem:
    """سیستم تعدیل پاداش بر اساس ریسک"""
    
    def __init__(self, risk_free_rate: float = 0.02, target_sharpe: float = 1.5):
        self.risk_free_rate = risk_free_rate
        self.target_sharpe = target_sharpe
        self.returns_history: List[float] = []
    
    def calculate_risk_adjusted_reward(self, base_reward: float, 
                                     current_return: float) -> float:
        """محاسبه پاداش تعدیل‌شده بر اساس ریسک"""
        self.returns_history.append(current_return)
        
        if len(self.returns_history) < 10:
            return base_reward
        
        # محاسبه شارپ ریشو فعلی
        recent_returns = self.returns_history[-20:]
        avg_return = np.mean(recent_returns)
        std_return = np.std(recent_returns)
        
        if std_return == 0:
            return base_reward
        
        current_sharpe = (avg_return - self.risk_free_rate) / std_return
        
        # تعدیل پاداش بر اساس شارپ ریشو
        sharpe_adjustment = current_sharpe / self.target_sharpe
        adjusted_reward = base_reward * (1 + sharpe_adjustment * 0.5)
        
        return adjusted_reward


@dataclass
class AdvancedRewardRedistributor:
    """سیستم پیشرفته تعدیل پاداش"""
    
    strategy: RewardStrategy = field(default_factory=lambda: LinearRewardStrategy())
    memory_system: RewardMemorySystem = field(default_factory=RewardMemorySystem)
    predictor: RewardPredictor = field(default_factory=RewardPredictor)
    quality_monitor: RewardQualityMonitor = field(default_factory=RewardQualityMonitor)
    dynamic_weighting: DynamicRewardWeighting = field(default_factory=DynamicRewardWeighting)
    risk_adjustment: RiskAdjustedRewardSystem = field(default_factory=RiskAdjustedRewardSystem)
    
    max_multiplier: float = 5.0
    min_multiplier: float = 0.1
    enable_prediction: bool = True
    enable_quality_monitoring: bool = True
    enable_risk_adjustment: bool = True
    
    def __post_init__(self):
        self.step_count = 0
        self.total_redistributed_reward = 0.0
        self.redistribution_stats = {
            'count': 0,
            'total_original': 0.0,
            'total_redistributed': 0.0,
            'avg_multiplier': 1.0
        }
    
    def _calculate_drawdown(self, equity_curve: List[float]) -> float:
        """محاسبه drawdown فعلی"""
        if len(equity_curve) < 2:
            return 0.0
        
        peak = np.maximum.accumulate(equity_curve)
        current_dd = (peak[-1] - equity_curve[-1]) / peak[-1] if peak[-1] > 0 else 0.0
        return max(0.0, current_dd)
    
    def _prepare_context(self, equity_curve: List[float], 
                        additional_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """تهیه context برای استراتژی"""
        context = additional_context.copy() if additional_context else {}
        
        if len(equity_curve) >= 10:
            recent_returns = np.diff(equity_curve[-10:])
            context.update({
                'volatility': np.std(recent_returns),
                'trend': np.mean(recent_returns),
                'volume': context.get('volume', 1.0),
                'spread': context.get('spread', 0.001)
            })
        
        return context
    
    def redistribute_single(self, reward: float, equity_curve: List[float],
                          action: Optional[str] = None,
                          additional_context: Optional[Dict[str, Any]] = None) -> float:
        """تعدیل یک پاداش"""
        
        # محاسبه drawdown
        drawdown = self._calculate_drawdown(equity_curve)
        
        # تهیه context
        context = self._prepare_context(equity_curve, additional_context)
        
        # محاسبه ضریب اصلی
        base_multiplier = self.strategy.calculate_multiplier(drawdown, context)
        
        # اعمال وزن‌دهی پویا
        if action and self.dynamic_weighting:
            weight = self.dynamic_weighting.calculate_action_weight(action)
            base_multiplier *= weight
        
        # محدود کردن ضریب
        multiplier = np.clip(base_multiplier, self.min_multiplier, self.max_multiplier)
        
        # پاداش اولیه
        redistributed_reward = reward * multiplier
        
        # تعدیل ریسک
        if self.enable_risk_adjustment and len(equity_curve) >= 2:
            current_return = equity_curve[-1] - equity_curve[-2] if len(equity_curve) >= 2 else 0
            redistributed_reward = self.risk_adjustment.calculate_risk_adjusted_reward(
                redistributed_reward, current_return
            )
        
        # ذخیره در حافظه
        performance = equity_curve[-1] if equity_curve else 0
        self.memory_system.add_record(
            reward=redistributed_reward,
            drawdown=drawdown,
            performance=performance,
            timestamp=self.step_count,
            context=context
        )
        
        # به‌روزرسانی آمار
        self.redistribution_stats['count'] += 1
        self.redistribution_stats['total_original'] += reward
        self.redistribution_stats['total_redistributed'] += redistributed_reward
        self.redistribution_stats['avg_multiplier'] = (
            self.redistribution_stats['total_redistributed'] / 
            self.redistribution_stats['total_original']
            if self.redistribution_stats['total_original'] != 0 else 1.0
        )
        
        # آموزش مدل پیش‌بینی
        if self.enable_prediction and self.step_count % 50 == 0:
            self.predictor.train(self.memory_system)
        
        # نظارت کیفیت
        if self.enable_quality_monitoring and self.step_count % 20 == 0:
            recent_rewards = self.memory_system.reward_history[-20:]
            recent_performance = self.memory_system.performance_history[-20:]
            if len(recent_rewards) >= 10:
                quality = self.quality_monitor.calculate_reward_quality(
                    recent_rewards, recent_performance
                )
                if not self.quality_monitor.is_quality_acceptable():
                    warnings.warn(f"Reward quality declining: {quality:.3f}")
        
        # به‌روزرسانی عملکرد اکشن
        if action and self.dynamic_weighting:
            self.dynamic_weighting.update_action_performance(action, performance)
        
        self.step_count += 1
        return redistributed_reward
    
    def redistribute_batch(self, rewards: List[float], equity_curve: List[float],
                          actions: Optional[List[str]] = None,
                          contexts: Optional[List[Dict[str, Any]]] = None) -> List[float]:
        """تعدیل دسته‌ای پاداش‌ها"""
        
        redistributed_rewards = []
        
        for i, reward in enumerate(rewards):
            action = actions[i] if actions and i < len(actions) else None
            context = contexts[i] if contexts and i < len(contexts) else None
            
            # equity curve تا نقطه فعلی
            current_equity = equity_curve[:i+1] if i < len(equity_curve) else equity_curve
            
            redistributed_reward = self.redistribute_single(
                reward, current_equity, action, context
            )
            redistributed_rewards.append(redistributed_reward)
        
        return redistributed_rewards
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """خلاصه عملکرد سیستم"""
        return {
            'redistribution_stats': self.redistribution_stats.copy(),
            'memory_stats': {
                'total_records': len(self.memory_system.reward_history),
                'avg_performance': self.memory_system.get_weighted_average_performance(),
                'drawdown_stats': self.memory_system.get_drawdown_statistics()
            },
            'quality_stats': {
                'current_quality': self.quality_monitor.quality_history[-1] if self.quality_monitor.quality_history else 0,
                'quality_trend': self.quality_monitor.get_quality_trend(),
                'is_acceptable': self.quality_monitor.is_quality_acceptable()
            },
            'strategy_name': self.strategy.get_strategy_name(),
            'step_count': self.step_count
        }
    
    def predict_next_reward(self, current_context: Dict[str, Any]) -> float:
        """پیش‌بینی پاداش بعدی"""
        if not self.enable_prediction:
            return 0.0
        
        return self.predictor.predict_next_reward(current_context, self.memory_system)
    
    def optimize_strategy(self, target_metric: str = 'sharpe_ratio') -> Dict[str, Any]:
        """بهینه‌سازی استراتژی بر اساس عملکرد"""
        if len(self.memory_system.reward_history) < 50:
            return {'status': 'insufficient_data'}
        
        # تحلیل عملکرد استراتژی‌های مختلف
        strategies_to_test = [
            LinearRewardStrategy(),
            ExponentialRewardStrategy(),
            AdaptiveRewardStrategy()
        ]
        
        best_strategy = None
        best_score = float('-inf')
        results = {}
        
        for strategy in strategies_to_test:
            # شبیه‌سازی عملکرد
            temp_redistributor = AdvancedRewardRedistributor(strategy=strategy)
            temp_redistributor.memory_system = self.memory_system  # استفاده از داده‌های موجود
            
            # محاسبه متریک
            if target_metric == 'sharpe_ratio':
                returns = np.diff(self.memory_system.performance_history[-50:])
                if len(returns) > 0 and np.std(returns) > 0:
                    score = np.mean(returns) / np.std(returns)
                else:
                    score = 0
            else:
                score = np.mean(self.memory_system.performance_history[-50:])
            
            results[strategy.get_strategy_name()] = score
            
            if score > best_score:
                best_score = score
                best_strategy = strategy
        
        # اعمال بهترین استراتژی
        if best_strategy and best_strategy.get_strategy_name() != self.strategy.get_strategy_name():
            self.strategy = best_strategy
            return {
                'status': 'optimized',
                'new_strategy': best_strategy.get_strategy_name(),
                'improvement': best_score,
                'all_results': results
            }
        
        return {
            'status': 'no_improvement',
            'current_strategy': self.strategy.get_strategy_name(),
            'current_score': best_score,
            'all_results': results
        }


class RewardOptimizer:
    """بهینه‌ساز پاداش با الگوریتم‌های مختلف"""
    
    def __init__(self, optimization_method: str = 'grid_search'):
        self.optimization_method = optimization_method
        self.optimization_history: List[Dict[str, Any]] = []
    
    def optimize_hyperparameters(self, redistributor: AdvancedRewardRedistributor,
                                validation_data: Dict[str, List[float]]) -> Dict[str, Any]:
        """بهینه‌سازی هایپرپارامترها"""
        
        if self.optimization_method == 'grid_search':
            return self._grid_search_optimization(redistributor, validation_data)
        elif self.optimization_method == 'random_search':
            return self._random_search_optimization(redistributor, validation_data)
        else:
            return {'status': 'unsupported_method'}
    
    def _grid_search_optimization(self, redistributor: AdvancedRewardRedistributor,
                                 validation_data: Dict[str, List[float]]) -> Dict[str, Any]:
        """بهینه‌سازی grid search"""
        
        # پارامترهای مختلف برای تست
        max_multiplier_options = [3.0, 5.0, 7.0]
        min_multiplier_options = [0.1, 0.2, 0.3]
        
        best_params = None
        best_score = float('-inf')
        results = []
        
        for max_mult in max_multiplier_options:
            for min_mult in min_multiplier_options:
                # تست پارامترها
                test_redistributor = AdvancedRewardRedistributor(
                    strategy=redistributor.strategy,
                    max_multiplier=max_mult,
                    min_multiplier=min_mult
                )
                
                # شبیه‌سازی
                rewards = validation_data['rewards']
                equity_curve = validation_data['equity_curve']
                
                redistributed = test_redistributor.redistribute_batch(rewards, equity_curve)
                
                # محاسبه امتیاز (شارپ ریشو)
                if len(redistributed) > 1:
                    returns = np.diff(redistributed)
                    if np.std(returns) > 0:
                        score = np.mean(returns) / np.std(returns)
                    else:
                        score = 0
                else:
                    score = 0
                
                results.append({
                    'max_multiplier': max_mult,
                    'min_multiplier': min_mult,
                    'score': score
                })
                
                if score > best_score:
                    best_score = score
                    best_params = {'max_multiplier': max_mult, 'min_multiplier': min_mult}
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': results,
            'method': 'grid_search'
        }
    
    def _random_search_optimization(self, redistributor: AdvancedRewardRedistributor,
                                   validation_data: Dict[str, List[float]]) -> Dict[str, Any]:
        """بهینه‌سازی random search"""
        
        best_params = None
        best_score = float('-inf')
        results = []
        
        # تست تصادفی
        for _ in range(20):
            max_mult = np.random.uniform(2.0, 8.0)
            min_mult = np.random.uniform(0.05, 0.5)
            
            # تست پارامترها
            test_redistributor = AdvancedRewardRedistributor(
                strategy=redistributor.strategy,
                max_multiplier=max_mult,
                min_multiplier=min_mult
            )
            
            # شبیه‌سازی
            rewards = validation_data['rewards']
            equity_curve = validation_data['equity_curve']
            
            redistributed = test_redistributor.redistribute_batch(rewards, equity_curve)
            
            # محاسبه امتیاز
            if len(redistributed) > 1:
                returns = np.diff(redistributed)
                if np.std(returns) > 0:
                    score = np.mean(returns) / np.std(returns)
                else:
                    score = 0
            else:
                score = 0
            
            results.append({
                'max_multiplier': max_mult,
                'min_multiplier': min_mult,
                'score': score
            })
            
            if score > best_score:
                best_score = score
                best_params = {'max_multiplier': max_mult, 'min_multiplier': min_mult}
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': results,
            'method': 'random_search'
        }


# کلاس قدیمی برای سازگاری
@dataclass
class RewardRedistributor:
    """کلاس ساده تعدیل پاداش (برای سازگاری)"""

    thresholds: Optional[List[float]] = None
    multipliers: Optional[List[float]] = None
    cap: float = 3.0

    def __post_init__(self):
        if self.thresholds is None:
            self.thresholds = [0.05, 0.10, 0.20]
        if self.multipliers is None:
            self.multipliers = [1.0, 1.2, 1.5, 2.0]
        if len(self.multipliers) != len(self.thresholds) + 1:
            raise ValueError("multipliers length must be thresholds length + 1")
        if any(self.thresholds[i] >= self.thresholds[i + 1] for i in range(len(self.thresholds) - 1)):
            raise ValueError("thresholds must be in ascending order")

    def _calculate_drawdown(self, equity_curve: List[float]) -> float:
        """محاسبه drawdown فعلی"""
        if len(equity_curve) == 0:
            return 0.0
        peak = np.maximum.accumulate(equity_curve)
        dd = (peak - equity_curve) / peak
        return dd[-1] if len(dd) > 0 else 0.0

    def _get_multiplier(self, drawdown: float) -> float:
        """محاسبه ضریب بر اساس drawdown"""
        for i, thr in enumerate(self.thresholds):
            if drawdown < thr:
                return min(self.multipliers[i], self.cap)
        return min(self.multipliers[-1], self.cap)

    def redistribute(self, rewards: List[float], equity_curve: List[float]) -> List[float]:
        """تعدیل پاداش‌ها"""
        if len(equity_curve) == 0:
            return list(rewards)

        drawdown = self._calculate_drawdown(equity_curve)
        m = self._get_multiplier(drawdown)
        return [r * m for r in rewards]


# تابع میانبر
_default_redistributor = RewardRedistributor()


def redistribute(rewards: List[float], equity_curve: List[float]) -> List[float]:
    """تابع میانبر برای تعدیل سریع پاداش"""
    return _default_redistributor.redistribute(rewards, equity_curve) 


# Factory function برای ایجاد redistributor پیشرفته
def create_advanced_redistributor(strategy_type: str = 'adaptive',
                                 **kwargs) -> AdvancedRewardRedistributor:
    """ایجاد redistributor پیشرفته"""
    
    strategy_map = {
        'linear': LinearRewardStrategy,
        'exponential': ExponentialRewardStrategy,
        'adaptive': AdaptiveRewardStrategy
    }
    
    if strategy_type not in strategy_map:
        raise ValueError(f"Unknown strategy type: {strategy_type}")
    
    strategy = strategy_map[strategy_type]()
    
    return AdvancedRewardRedistributor(strategy=strategy, **kwargs) 