#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Fix Import Issues Script
اسکریپت رفع مشکلات import
"""

import os
import logging
from datetime import datetime

# تنظیم logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_configuration_import():
    """رفع مشکل import AdvancedConfigurationManager"""
    logger.info("🔧 Fixing AdvancedConfigurationManager import...")
    
    try:
        # خواندن فایل main_new.py
        with open('main_new.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # حذف import مشکل‌دار
        problematic_import = '''# Import configuration management
try:
    from core import (
        # Configuration Management
        CONFIGURATION_MANAGEMENT_AVAILABLE,
        AdvancedConfigurationManager,
        config_manager
    )
except ImportError:
    CONFIGURATION_MANAGEMENT_AVAILABLE = False
    config_manager = None'''
        
        if problematic_import in content:
            content = content.replace(problematic_import, '')
            logger.info("✅ Removed problematic import")
        
        # اضافه کردن import ساده‌تر
        simple_import = '''# Configuration Management variables
CONFIGURATION_MANAGEMENT_AVAILABLE = False
config_manager = None'''
        
        # پیدا کردن مکان مناسب
        insert_point = content.find('# Configure logging')
        if insert_point != -1:
            content = content[:insert_point] + simple_import + '\n\n' + content[insert_point:]
        
        # ذخیره فایل
        with open('main_new.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("✅ Configuration import fixed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing configuration import: {e}")
        return False

def remove_configuration_section():
    """حذف بخش Configuration Management از کد"""
    logger.info("🔧 Removing Configuration Management section...")
    
    try:
        # خواندن فایل main_new.py
        with open('main_new.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # حذف بخش Configuration Management
        config_section_start = '''try:
                from core import CONFIGURATION_MANAGEMENT_AVAILABLE, config_manager
            except ImportError:
                CONFIGURATION_MANAGEMENT_AVAILABLE = False
                config_manager = None
                
            if CONFIGURATION_MANAGEMENT_AVAILABLE and config_manager:'''
        
        config_section_end = '''                total_count += 1'''
        
        # پیدا کردن و حذف بخش
        start_idx = content.find(config_section_start)
        if start_idx != -1:
            # پیدا کردن انتهای بخش
            end_idx = content.find(config_section_end, start_idx)
            if end_idx != -1:
                end_idx += len(config_section_end)
                # حذف بخش
                content = content[:start_idx] + content[end_idx:]
                
                # ذخیره فایل
                with open('main_new.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info("✅ Configuration Management section removed")
                return True
        
        logger.info("ℹ️ Configuration Management section not found")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error removing configuration section: {e}")
        return False

def test_imports():
    """تست imports"""
    logger.info("🧪 Testing imports...")
    
    try:
        import sys
        sys.path.insert(0, '.')
        
        # تست import اصلی
        from main_new import TradingSystemManager
        logger.info("✅ TradingSystemManager import successful")
        
        # تست ایجاد instance
        manager = TradingSystemManager()
        logger.info("✅ TradingSystemManager instance created")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Import test failed: {e}")
        return False

def main():
    """اجرای رفع مشکلات import"""
    print('🔧 Fix Import Issues Script')
    print('=' * 40)
    print(f'⏰ شروع: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    try:
        # 1. رفع مشکل configuration import
        if not fix_configuration_import():
            return 1
        
        # 2. حذف بخش Configuration Management
        if not remove_configuration_section():
            return 1
        
        # 3. تست imports
        if not test_imports():
            return 1
        
        print(f'\n🎉 مشکلات import رفع شدند!')
        print(f'⏰ پایان: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        return 0
        
    except Exception as e:
        logger.error(f"❌ خطای کلی: {e}")
        return 1

if __name__ == "__main__":
    exit(main()) 