{"command": "python utils/federated_learning_system.py", "timestamp": "2025-07-08T05:52:18.956822", "execution_time": 12.21430516242981, "return_code": 1, "stdout": "Federated Learning System Test\n========================================\nCreated server and 4 clients\n\n--- Federated Learning Round 1 ---\n  Client 1 sent RL_Agent update (accuracy: 0.785)\n  Client 1 sent Prediction_Model update (accuracy: 0.748)\n  Client 2 sent RL_Agent update (accuracy: 0.860)\n  Client 2 sent Prediction_Model update (accuracy: 0.752)\n  Client 3 sent RL_Agent update (accuracy: 0.757)\n  Client 3 sent Prediction_Model update (accuracy: 0.749)\n  Client 4 sent RL_Agent update (accuracy: 0.856)\n  Client 4 sent Prediction_Model update (accuracy: 0.873)\n\n  Aggregating updates...\n    RL_Agent v1: 4 clients\n    Prediction_Model v1: 4 clients\n\n--- Federated Learning Round 2 ---\n  Client 1 sent RL_Agent update (accuracy: 0.894)\n  Client 1 sent Prediction_Model update (accuracy: 0.712)\n  Client 2 sent RL_Agent update (accuracy: 0.767)\n  Client 2 sent Prediction_Model update (accuracy: 0.773)\n  Client 3 sent RL_Agent update (accuracy: 0.798)\n  Client 3 sent Prediction_Model update (accuracy: 0.758)\n  Client 4 sent RL_Agent update (accuracy: 0.738)\n  Client 4 sent Prediction_Model update (accuracy: 0.860)\n\n  Aggregating updates...\n    RL_Agent v2: 4 clients\n    Prediction_Model v2: 4 clients\n\n--- Federated Learning Round 3 ---\n  Client 1 sent RL_Agent update (accuracy: 0.820)\n  Client 1 sent Prediction_Model update (accuracy: 0.846)\n  Client 2 sent RL_Agent update (accuracy: 0.721)\n  Client 2 sent Prediction_Model update (accuracy: 0.732)\n  Client 3 sent RL_Agent update (accuracy: 0.716)\n  Client 3 sent Prediction_Model update (accuracy: 0.800)\n  Client 4 sent RL_Agent update (accuracy: 0.832)\n  Client 4 sent Prediction_Model update (accuracy: 0.771)\n\n  Aggregating updates...\n    RL_Agent v3: 4 clients\n    Prediction_Model v3: 4 clients\n\n--- Final Status ---\nTotal rounds: 3\nActive models: ['RL_Agent', 'Prediction_Model']\nTotal federated models: 2\n\nRL_Agent Final Model:\n  Version: 3\n  Participating clients: 4\n  Sample weights: [('q_learning_rate', 0.009062341242226418), ('exploration_rate', 0.16447281615971238), ('discount_factor', 0.9517626106926134)]\n\nPrediction_Model Final Model:\n  Version: 3\n  Participating clients: 4\n  Sample weights: [('feature_weight_1', 0.6937616400366549), ('feature_weight_2', 0.589794845453546), ('bias_term', -0.022212998640454888)]\n", "stderr": "INFO:__main__:Federated Learning Server initialized\nINFO:__main__:Federated Learning Client client_1 initialized\nINFO:__main__:Federated Learning Client client_2 initialized\nINFO:__main__:Federated Learning Client client_3 initialized\nINFO:__main__:Federated Learning Client client_4 initialized\nINFO:__main__:Training local model RL_Agent for client client_1\nINFO:__main__:Received valid update from client client_1 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_1\nINFO:__main__:Received valid update from client client_1 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_2\nINFO:__main__:Received valid update from client client_2 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_2\nINFO:__main__:Received valid update from client client_2 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_3\nINFO:__main__:Received valid update from client client_3 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_3\nINFO:__main__:Received valid update from client client_3 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_4\nINFO:__main__:Received valid update from client client_4 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_4\nINFO:__main__:Received valid update from client client_4 for Prediction_Model\nINFO:__main__:Aggregated 4 updates for RL_Agent v1\nINFO:__main__:Applying global update for RL_Agent v1\nINFO:__main__:Applying global update for RL_Agent v1\nINFO:__main__:Applying global update for RL_Agent v1\nINFO:__main__:Applying global update for RL_Agent v1\nINFO:__main__:Aggregated 4 updates for Prediction_Model v1\nINFO:__main__:Applying global update for Prediction_Model v1\nINFO:__main__:Applying global update for Prediction_Model v1\nINFO:__main__:Applying global update for Prediction_Model v1\nINFO:__main__:Applying global update for Prediction_Model v1\nINFO:__main__:Training local model RL_Agent for client client_1\nINFO:__main__:Received valid update from client client_1 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_1\nINFO:__main__:Received valid update from client client_1 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_2\nINFO:__main__:Received valid update from client client_2 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_2\nINFO:__main__:Received valid update from client client_2 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_3\nINFO:__main__:Received valid update from client client_3 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_3\nINFO:__main__:Received valid update from client client_3 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_4\nINFO:__main__:Received valid update from client client_4 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_4\nINFO:__main__:Received valid update from client client_4 for Prediction_Model\nINFO:__main__:Aggregated 4 updates for RL_Agent v2\nINFO:__main__:Applying global update for RL_Agent v2\nINFO:__main__:Applying global update for RL_Agent v2\nINFO:__main__:Applying global update for RL_Agent v2\nINFO:__main__:Applying global update for RL_Agent v2\nINFO:__main__:Aggregated 4 updates for Prediction_Model v2\nINFO:__main__:Applying global update for Prediction_Model v2\nINFO:__main__:Applying global update for Prediction_Model v2\nINFO:__main__:Applying global update for Prediction_Model v2\nINFO:__main__:Applying global update for Prediction_Model v2\nINFO:__main__:Training local model RL_Agent for client client_1\nINFO:__main__:Received valid update from client client_1 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_1\nINFO:__main__:Received valid update from client client_1 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_2\nINFO:__main__:Received valid update from client client_2 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_2\nINFO:__main__:Received valid update from client client_2 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_3\nINFO:__main__:Received valid update from client client_3 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_3\nINFO:__main__:Received valid update from client client_3 for Prediction_Model\nINFO:__main__:Training local model RL_Agent for client client_4\nINFO:__main__:Received valid update from client client_4 for RL_Agent\nINFO:__main__:Training local model Prediction_Model for client client_4\nINFO:__main__:Received valid update from client client_4 for Prediction_Model\nINFO:__main__:Aggregated 4 updates for RL_Agent v3\nINFO:__main__:Applying global update for RL_Agent v3\nINFO:__main__:Applying global update for RL_Agent v3\nINFO:__main__:Applying global update for RL_Agent v3\nINFO:__main__:Applying global update for RL_Agent v3\nINFO:__main__:Aggregated 4 updates for Prediction_Model v3\nINFO:__main__:Applying global update for Prediction_Model v3\nINFO:__main__:Applying global update for Prediction_Model v3\nINFO:__main__:Applying global update for Prediction_Model v3\nINFO:__main__:Applying global update for Prediction_Model v3\nTraceback (most recent call last):\n  File \"D:\\project\\utils\\federated_learning_system.py\", line 523, in <module>\n    main() \n  File \"D:\\project\\utils\\federated_learning_system.py\", line 520, in main\n    print(f\"\\n\\u2705 Federated Learning System test completed!\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\u2705' in position 2: character maps to <undefined>\n", "success": false}