def explainable_ai_feature_importance(model, data, feature_names=None):
    # محاسبه اهمیت ویژگی‌ها با permutation importance (ساده)
    import numpy as np
    base_score = model.score(data)
    importances = []
    for i in range(data.shape[1]):
        d_shuffled = data.copy()
        np.random.shuffle(d_shuffled[:, i])
        score = model.score(d_shuffled)
        importances.append(base_score - score)
    if feature_names is not None:
        return dict(zip(feature_names, importances))
    return importances

def advanced_auto_feature_engineering(data, methods=["poly2", "log", "diff"]):
    # تولید ویژگی‌های جدید: چندجمله‌ای، لگاریتمی، مشتق
    import numpy as np
    feats = [data]
    if "poly2" in methods:
        feats.append(data ** 2)
    if "poly3" in methods:
        feats.append(data ** 3)
    if "log" in methods:
        feats.append(np.log1p(np.abs(data)))
    if "diff" in methods:
        feats.append(np.diff(data, prepend=data[0]))
    return np.stack(feats, axis=1)

def realistic_scenario_based_backtesting(data, scenario_func, n_scenarios=3, window_size=100):
    # اجرای backtest روی سناریوهای واقعی (تابع سناریوساز)
    import numpy as np
    scenarios = scenario_func(data, n_scenarios=n_scenarios, window_size=window_size)
    results = []
    for i, scenario in enumerate(scenarios):
        # فرض: مدل ساده RL با reward تصادفی
        rewards = np.cumsum(np.random.randn(window_size) + 2)
        results.append({"scenario": i+1, "final_reward": rewards[-1], "rewards": rewards})
    return results
# --- ست دهم: Federated Learning CV, Auto-News Event Detection, Auto-Scenario Generation ---
## --- ست یازدهم: Explainable AI, Auto-Feature Engineering پیشرفته، Scenario-based Backtesting واقعی ---
## --- ست دوازدهم: anomaly detection پیشرفته، adaptive reward shaping، imitation learning ---
def advanced_anomaly_detection(data, method="zscore", threshold=3.0):
    # شناسایی نقاط غیرعادی با روش‌های مختلف (zscore, iqr)
    import numpy as np
    data = np.array(data)
    if method == "zscore":
        z = (data - np.mean(data)) / (np.std(data) + 1e-8)
        return np.where(np.abs(z) > threshold)[0]
    elif method == "iqr":
        q1, q3 = np.percentile(data, [25, 75])
        iqr = q3 - q1
        lower = q1 - threshold * iqr
        upper = q3 + threshold * iqr
        return np.where((data < lower) | (data > upper))[0]
    else:
        return np.array([])

def adaptive_reward_shaping(rewards, volatility, alpha=0.5):
    # شکل‌دهی پاداش بر اساس نوسان (volatility)
    import numpy as np
    rewards = np.array(rewards, dtype=float)
    shaped = rewards + alpha * volatility
    return shaped

def imitation_learning_expert_policy(data, expert_actions):
    # ساده‌ترین imitation: یادگیری از expert_actions
    import numpy as np
    # فرض: مدل فقط expert_actions را تقلید می‌کند
    return np.array(expert_actions)
## --- ست یازدهم: Explainable AI, Auto-Feature Engineering پیشرفته، Scenario-based Backtesting واقعی ---
## --- ست دوازدهم: anomaly detection پیشرفته، adaptive reward shaping، imitation learning ---
def federated_learning_cv(data_list, n_splits=3, window_size=100):
    # data_list: list of arrays (simulate federated clients)
    import numpy as np
    for client_id, data in enumerate(data_list):
        n = len(data)
        for i in range(n_splits):
            train_end = window_size * (i + 1)
            test_start = train_end
            test_end = train_end + window_size
            if test_end > n:
                break
            train_idx = np.arange(0, train_end)
            test_idx = np.arange(test_start, test_end)
            yield client_id, train_idx, test_idx

def auto_news_event_detection(data, threshold=2.5):
    # detect sudden jumps as news events
    import numpy as np
    data = np.array(data)
    diffs = np.diff(data)
    events = np.where(np.abs(diffs) > threshold)[0] + 1
    return events

def auto_scenario_generation(data, n_scenarios=3, window_size=100):
    # generate synthetic scenarios by shuffling and scaling
    import numpy as np
    scenarios = []
    for i in range(n_scenarios):
        d = np.array(data)
        d = d * np.random.uniform(0.8, 1.2)
        d = np.random.permutation(d)
        scenarios.append(d[:window_size])
    return scenarios
# --- ست نهم: Anomaly Detection CV, Self-Play CV, Whale Tracking Reward Shaping ---
def anomaly_detection_cv(data, n_splits=3, window_size=100, anomaly_frac=0.1):
    import numpy as np
    n = len(data)
    for i in range(n_splits):
        train_end = window_size * (i + 1)
        test_start = train_end
        test_end = train_end + window_size
        if test_end > n:
            break
        train_idx = np.arange(0, train_end)
        test_idx = np.arange(test_start, test_end)
        # introduce anomalies in test set
        test_data = np.array(data[test_idx], dtype=float)
        n_anom = int(anomaly_frac * len(test_data))
        if n_anom > 0:
            test_data[:n_anom] += np.random.normal(10, 2, size=n_anom)
        yield train_idx, test_idx, test_data

def self_play_cv(data, n_splits=3, window_size=100):
    # simulate self-play by shuffling and splitting
    import numpy as np
    n = len(data)
    for i in range(n_splits):
        idx = np.random.permutation(np.arange(n))
        split = len(idx) // 2
        train_idx = idx[:split]
        test_idx = idx[split:split+window_size]
        yield train_idx, test_idx

def whale_tracking_reward_shaping(rewards, whale_indices, whale_bonus=5):
    import numpy as np
    rewards = np.array(rewards, dtype=float)
    rewards[whale_indices] += whale_bonus
    return rewards
# --- ست هشتم: Scenario-based Backtesting, Auto-Feature Engineering, Multi-Market CV ---
def scenario_backtesting_cv(data, scenario_labels, n_splits=3):
    import numpy as np
    unique_scenarios = np.unique(scenario_labels)
    for scenario in unique_scenarios[:n_splits]:
        idx = np.where(scenario_labels == scenario)[0]
        split = len(idx) // 2
        train_idx = idx[:split]
        test_idx = idx[split:]
        yield train_idx, test_idx, scenario

def auto_feature_engineering(data, degree=2):
    import numpy as np
    data = np.array(data)
    feats = [data]
    if degree >= 2:
        feats.append(data**2)
    if degree >= 3:
        feats.append(data**3)
    return np.stack(feats, axis=1)

def multi_market_cv(data_dict, n_splits=3, window_size=100):
    # data_dict: {market_name: data_array}
    for market, data in data_dict.items():
        n = len(data)
        for i in range(n_splits):
            train_end = window_size * (i + 1)
            test_start = train_end
            test_end = train_end + window_size
            if test_end > n:
                break
            train_idx = np.arange(0, train_end)
            test_idx = np.arange(test_start, test_end)
            yield market, train_idx, test_idx
# --- ست هفتم: Adversarial CV, Lookahead CV, Comparative EarlyStopping ---
def adversarial_cv(data, n_splits=3, window_size=100, attack_strength=5):
    n = len(data)
    for i in range(n_splits):
        train_end = window_size * (i + 1)
        test_start = train_end
        test_end = train_end + window_size
        if test_end > n:
            break
        train_idx = np.arange(0, train_end)
        test_idx = np.arange(test_start, test_end)
        # adversarial: add outlier/attack to test set
        test_adv = np.array(data[test_idx], dtype=float)
        if len(test_adv) > 0:
            test_adv[:min(5, len(test_adv))] += attack_strength
        yield train_idx, test_idx, test_adv

def lookahead_cv(data, n_splits=3, window_size=100, lookahead=10):
    n = len(data)
    for i in range(n_splits):
        train_end = window_size * (i + 1)
        test_start = train_end
        test_end = train_end + window_size + lookahead
        if test_end > n:
            break
        train_idx = np.arange(0, train_end)
        test_idx = np.arange(test_start, test_end)
        yield train_idx, test_idx

def comparative_earlystopping_curve(rewards, patience=5, min_delta=0.0):
    from .rl_training_utils import EarlyStopping
    stopper = EarlyStopping(patience=patience, min_delta=min_delta)
    curve = []
    for r in rewards:
        stop = stopper.step(r)
        curve.append(r)
        if stop:
            break
    return curve
# --- ست ششم: Overlapping CV, Lookahead EarlyStopping, Feature Importance CV ---
def overlapping_cv(data, n_splits=5, window_size=100, overlap=0.5):
    n = len(data)
    step = int(window_size * (1 - overlap))
    for i in range(n_splits):
        train_end = step * i + window_size
        test_start = train_end
        test_end = train_end + window_size
        if test_end > n:
            break
        train_idx = np.arange(0, train_end)
        test_idx = np.arange(test_start, test_end)
        yield train_idx, test_idx

class EarlyStoppingLookahead:
    # Early stopping if no improvement in the next N steps (lookahead window).
    def __init__(self, patience=5, min_delta=0.0, lookahead=3):
        self.patience = patience
        self.min_delta = min_delta
        self.lookahead = lookahead
        self.history = []
        self.counter = 0
        self.early_stop = False

    def step(self, score):
        self.history.append(score)
        if len(self.history) < self.lookahead:
            return False
        lookahead_max = max(self.history[-self.lookahead:])
        if not hasattr(self, 'best_score') or lookahead_max > self.best_score + self.min_delta:
            self.best_score = lookahead_max
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        return self.early_stop

def feature_importance_cv(data, feature_matrix, n_splits=3, window_size=100):
    n = len(data)
    for i in range(n_splits):
        train_end = window_size * (i + 1)
        test_start = train_end
        test_end = train_end + window_size
        if test_end > n:
            break
        train_idx = np.arange(0, train_end)
        test_idx = np.arange(test_start, test_end)
        # importance: mean abs value per feature (dummy)
        importances = feature_matrix[train_idx].mean(axis=0)
        yield train_idx, test_idx, importances
# --- Early Stopping: Combo, Lookahead, Regularization ---
class EarlyStoppingCombo:
    # Early stopping based on both reward and sharpe ratio.
    def __init__(self, patience=5, min_delta=0.0):
        self.patience = patience
        self.min_delta = min_delta
        self.best_score = None
        self.best_sharpe = None
        self.counter = 0
        self.early_stop = False

    def step(self, score, sharpe):
        if (self.best_score is None or score > self.best_score + self.min_delta) and \
           (self.best_sharpe is None or sharpe > self.best_sharpe + self.min_delta):
            self.best_score = score
            self.best_sharpe = sharpe
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        return self.early_stop

class EarlyStoppingLookahead:
    # Early stopping if no improvement in the next N steps (lookahead window).
    def __init__(self, patience=5, min_delta=0.0, lookahead=3):
        self.patience = patience
        self.min_delta = min_delta
        self.lookahead = lookahead
        self.history = []
        self.counter = 0
        self.early_stop = False

    def step(self, score):
        self.history.append(score)
        if len(self.history) < self.lookahead:
            return False
        lookahead_max = max(self.history[-self.lookahead:])
        if not hasattr(self, 'best_score') or lookahead_max > self.best_score + self.min_delta:
            self.best_score = lookahead_max
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        return self.early_stop

class EarlyStoppingRegularization:
    # Early stopping if overfitting detected (large train/test gap).
    def __init__(self, patience=5, min_delta=0.0, max_gap=10.0):
        self.patience = patience
        self.min_delta = min_delta
        self.max_gap = max_gap
        self.counter = 0
        self.early_stop = False

    def step(self, train_score, test_score):
        gap = abs(train_score - test_score)
        if gap > self.max_gap:
            self.counter += 1
        else:
            self.counter = 0
        if self.counter >= self.patience:
            self.early_stop = True
        return self.early_stop
# --- Early Stopping Variants ---
class EarlyStoppingMovingAverage:
    def __init__(self, patience=5, min_delta=0.0, window=3):
        self.patience = patience
        self.min_delta = min_delta
        self.window = window
        self.scores = []
        self.counter = 0
        self.early_stop = False

    def step(self, score):
        self.scores.append(score)
        if len(self.scores) > self.window:
            self.scores.pop(0)
        avg_score = np.mean(self.scores)
        if len(self.scores) < self.window:
            return False
        if not hasattr(self, 'best_avg') or avg_score > self.best_avg + self.min_delta:
            self.best_avg = avg_score
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        return self.early_stop

class EarlyStoppingDynamicPatience:
    def __init__(self, base_patience=5, min_delta=0.0):
        self.base_patience = base_patience
        self.min_delta = min_delta
        self.best_score = None
        self.counter = 0
        self.early_stop = False

    def step(self, score, reward_std=0):
        patience = max(1, int(self.base_patience * (1 + reward_std)))
        if self.best_score is None or score > self.best_score + self.min_delta:
            self.best_score = score
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= patience:
                self.early_stop = True
        return self.early_stop

class EarlyStoppingDrawdown:
    def __init__(self, max_drawdown=10):
        self.max_drawdown = max_drawdown
        self.peak = -np.inf
        self.early_stop = False

    def step(self, score):
        if score > self.peak:
            self.peak = score
        drawdown = self.peak - score
        if drawdown > self.max_drawdown:
            self.early_stop = True
        return self.early_stop

# --- Cross-Validation Variants ---
def adaptive_window_cv(data, n_splits=5, min_window=100, max_window=500, window_size=None):
    """
    Cross-validation with adaptive/random window size. Supports both window_size and min_window/max_window.
    """
    import numpy as np
    n = len(data)
    if window_size is not None:
        min_window = max_window = window_size
    for i in range(n_splits):
        window = np.random.randint(min_window, max_window + 1) if min_window != max_window else min_window
        train_end = window * (i + 1)
        test_start = train_end
        test_end = train_end + window
        if test_end > n:
            break
        train_idx = np.arange(0, train_end)
        test_idx = np.arange(test_start, test_end)
        yield train_idx, test_idx

def overlapping_window_cv(data, n_splits=5, window_size=200, overlap=0.5):
    n = len(data)
    step = int(window_size * (1 - overlap))
    for i in range(n_splits):
        train_end = step * i + window_size
        test_start = train_end
        test_end = train_end + window_size
        if test_end > n:
            break
        train_idx = np.arange(0, train_end)
        test_idx = np.arange(test_start, test_end)
        yield train_idx, test_idx

def scenario_based_cv(data, scenario_labels, n_splits=3):
    # scenario_labels: array of market regime labels (e.g. bull, bear, side)
    unique_scenarios = np.unique(scenario_labels)
    for scenario in unique_scenarios[:n_splits]:
        idx = np.where(scenario_labels == scenario)[0]
        split = len(idx) // 2
        train_idx = idx[:split]
        test_idx = idx[split:]
        yield train_idx, test_idx
import numpy as np

# --- Uncertainty/Confidence Interval CV ---
def uncertainty_cv(data, n_splits=5, window_size=200, ci_level=0.95):
    n = len(data)
    for i in range(n_splits):
        train_end = window_size * (i + 1)
        test_start = train_end
        test_end = train_end + window_size
        if test_end > n:
            break
        train_idx = np.arange(0, train_end)
        test_idx = np.arange(test_start, test_end)
        yield train_idx, test_idx, ci_level

# --- Data Augmentation for CV ---
def augment_data(data, method="jitter", **kwargs):
    data = np.array(data)
    if method == "jitter":
        noise = kwargs.get("noise", 0.01)
        return data + np.random.normal(0, noise, size=data.shape)
    elif method == "scaling":
        scale = kwargs.get("scale", 1.1)
        return data * scale
    elif method == "permutation":
        return np.random.permutation(data)
    else:
        return data

def data_augmentation_cv(data, n_splits=5, window_size=200, method="jitter", **kwargs):
    n = len(data)
    for i in range(n_splits):
        train_end = window_size * (i + 1)
        test_start = train_end
        test_end = train_end + window_size
        if test_end > n:
            break
        train_idx = np.arange(0, train_end)
        test_idx = np.arange(test_start, test_end)
        aug_train = augment_data(data[train_idx], method, **kwargs)
        aug_test = augment_data(data[test_idx], method, **kwargs)
        yield aug_train, aug_test

# --- Stability Test for CV ---
def stability_cv(data, cv_func, n_runs=5, **cv_kwargs):
    # Run CV multiple times and return list of results for stability analysis.
    results = []
    for _ in range(n_runs):
        run_results = []
        for split in cv_func(data, **cv_kwargs):
            run_results.append(split)
        results.append(run_results)
    return results

class EarlyStopping:
    def __init__(self, patience=5, min_delta=0.0):
        self.patience = patience
        self.min_delta = min_delta
        self.best_score = None
        self.counter = 0
        self.early_stop = False

    def step(self, score):
        if self.best_score is None or score > self.best_score + self.min_delta:
            self.best_score = score
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        return self.early_stop

def rolling_window_cv(data, n_splits=5, window_size=None):
    # data: numpy array or pandas DataFrame
    n = len(data)
    if window_size is None:
        window_size = n // (n_splits + 1)
    for i in range(n_splits):
        train_end = window_size * (i + 1)
        test_start = train_end
        test_end = train_end + window_size
        if test_end > n:
            break
        train_idx = np.arange(0, train_end)
        test_idx = np.arange(test_start, test_end)
        yield train_idx, test_idx

# --- ست سیزدهم: Adversarial Training, Whale Tracking, Federated Learning ---
def adversarial_training(data, model, attack_func, n_epochs=10):
    # آموزش مدل با داده‌های دستکاری‌شده (adversarial)
    import numpy as np
    history = []
    for epoch in range(n_epochs):
        adv_data = attack_func(data)
        model.fit(adv_data)
        score = model.score(adv_data)
        history.append(score)
    return history

def whale_tracking(data, volume, whale_threshold=0.9):
    # شناسایی و علامت‌گذاری رفتار نهنگ‌ها (معامله‌گران بزرگ)
    import numpy as np
    vol = np.array(volume)
    threshold = np.quantile(vol, whale_threshold)
    whale_indices = np.where(vol >= threshold)[0]
    whale_mask = np.zeros_like(vol, dtype=bool)
    whale_mask[whale_indices] = True
    return whale_indices, whale_mask

def federated_learning_train(data_list, model_class, n_rounds=5, **model_kwargs):
    # آموزش مدل به صورت federated (بدون انتقال داده خام)
    import numpy as np
    models = [model_class(**model_kwargs) for _ in data_list]
    for rnd in range(n_rounds):
        for i, (model, data) in enumerate(zip(models, data_list)):
            model.fit(data)
        # میانگین‌گیری وزن‌ها (ساده)
        weights = [getattr(m, 'coef_', None) for m in models]
        if all(w is not None for w in weights):
            avg_weight = np.mean(weights, axis=0)
            for m in models:
                m.coef_ = avg_weight.copy()
    return models

# --- ست چهاردهم: Multi-Account, Auto-Trade Execution, Real-Time Anomaly Alerting ---
def multi_account_portfolio_manager(accounts, actions):
    # مدیریت چند حساب و سبد متنوع
    # accounts: dict {account_id: {'balance': float, 'positions': list}}
    # actions: dict {account_id: list of {'type': 'buy'/'sell', 'amount': float, 'symbol': str}}
    results = {}
    for acc_id, acc in accounts.items():
        balance = acc.get('balance', 0)
        positions = acc.get('positions', [])
        for act in actions.get(acc_id, []):
            if act['type'] == 'buy':
                balance -= act['amount']
                positions.append({'symbol': act['symbol'], 'amount': act['amount']})
            elif act['type'] == 'sell':
                balance += act['amount']
                # حذف اولین پوزیشن با این symbol (ساده)
                for i, p in enumerate(positions):
                    if p['symbol'] == act['symbol']:
                        positions.pop(i)
                        break
        results[acc_id] = {'balance': balance, 'positions': positions}
    return results

def auto_trade_execution(order_book, strategy_func):
    # اجرای خودکار معاملات و مسیریابی سفارش تطبیقی
    # order_book: dict {symbol: {'bid': float, 'ask': float}}
    # strategy_func: (order_book) -> list of orders
    orders = strategy_func(order_book)
    executed = []
    for order in orders:
        symbol = order['symbol']
        side = order['side']
        price = order_book[symbol]['ask'] if side == 'buy' else order_book[symbol]['bid']
        executed.append({'symbol': symbol, 'side': side, 'amount': order['amount'], 'price': price})
    return executed

def real_time_anomaly_alerting(data, window=20, threshold=3.0):
    # آلارم آنومالی لحظه‌ای با threshold تطبیقی
    import numpy as np
    alerts = []
    for i in range(window, len(data)):
        win = data[i-window:i]
        mu, sigma = np.mean(win), np.std(win) + 1e-8
        if abs(data[i] - mu) > threshold * sigma:
            alerts.append(i)
    return alerts
