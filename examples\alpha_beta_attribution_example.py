#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
مثال کاربردی Alpha/Beta Attribution System
نمایش کامل قابلیت‌های سیستم تشخیص خودکار آلفا و بتا
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.alpha_beta_attribution import (
    AlphaBetaAttributionEngine,
    RiskFactorType,
    AttributionPeriod
)


def generate_sample_data():
    """تولید داده‌های نمونه واقعی"""
    print("🔄 تولید داده‌های نمونه...")
    
    # Set random seed for reproducibility
    np.random.seed(42)
    
    # Generate 2 years of daily data
    dates = pd.date_range('2022-01-01', periods=504, freq='D')
    
    # Market factors (simulating realistic market conditions)
    market_return = np.random.normal(0.0008, 0.018, 504)  # ~20% annual volatility
    size_factor = np.random.normal(0.0002, 0.012, 504)    # Small cap premium
    value_factor = np.random.normal(0.0001, 0.010, 504)   # Value premium
    momentum_factor = np.random.normal(0.0003, 0.015, 504) # Momentum effect
    quality_factor = np.random.normal(0.0001, 0.008, 504)  # Quality premium
    
    # Portfolio with known characteristics
    true_alpha = 0.0008  # 20% annual alpha
    true_beta = 1.15     # Slightly aggressive
    size_exposure = 0.25  # Small cap tilt
    value_exposure = -0.1 # Growth tilt
    momentum_exposure = 0.3 # Momentum strategy
    quality_exposure = 0.15 # Quality focus
    
    # Generate portfolio returns with realistic noise
    portfolio_returns = pd.Series(
        true_alpha + 
        true_beta * market_return +
        size_exposure * size_factor +
        value_exposure * value_factor +
        momentum_exposure * momentum_factor +
        quality_exposure * quality_factor +
        np.random.normal(0, 0.012, 504),  # Idiosyncratic risk
        index=dates
    )
    
    # Benchmark returns (market)
    benchmark_returns = pd.Series(market_return, index=dates)
    
    # Factor returns
    factor_returns = pd.DataFrame({
        'market': market_return,
        'size': size_factor,
        'value': value_factor,
        'momentum': momentum_factor
    }, index=dates)
    
    # Market data for ML features
    market_data = pd.DataFrame({
        'market_return': market_return,
        'vix': np.random.normal(20, 8, 504),  # Volatility index
        'term_spread': np.random.normal(0.02, 0.01, 504),  # Term structure
        'credit_spread': np.random.normal(0.015, 0.005, 504)  # Credit risk
    }, index=dates)
    
    return {
        'portfolio_returns': portfolio_returns,
        'benchmark_returns': benchmark_returns,
        'factor_returns': factor_returns,
        'market_data': market_data,
        'true_parameters': {
            'alpha': true_alpha,
            'beta': true_beta,
            'size_exposure': size_exposure,
            'value_exposure': value_exposure,
            'momentum_exposure': momentum_exposure,
            'quality_exposure': quality_exposure
        }
    }


def demonstrate_basic_alpha_beta(engine, data):
    """نمایش محاسبه Alpha/Beta پایه"""
    print("\n" + "="*60)
    print("📊 محاسبه Alpha/Beta پایه")
    print("="*60)
    
    portfolio_returns = data['portfolio_returns']
    benchmark_returns = data['benchmark_returns']
    true_params = data['true_parameters']
    
    # Calculate alpha/beta for different periods
    periods = [63, 126, 252, 504]  # 3M, 6M, 1Y, 2Y
    
    for period in periods:
        if len(portfolio_returns) >= period:
            period_data = portfolio_returns.tail(period)
            period_benchmark = benchmark_returns.tail(period)
            
            result = engine.calculate_alpha_beta(period_data, period_benchmark)
            
            print(f"\n📈 دوره {period} روز ({period//21:.1f} ماه):")
            print(f"   Alpha: {result.alpha:.4f} (واقعی: {true_params['alpha']:.4f})")
            print(f"   Beta:  {result.beta:.4f} (واقعی: {true_params['beta']:.4f})")
            print(f"   R²:    {result.r_squared:.4f}")
            print(f"   Information Ratio: {result.information_ratio:.4f}")
            print(f"   Sharpe Ratio: {result.sharpe_ratio:.4f}")
            print(f"   Tracking Error: {result.tracking_error:.4f}")
            print(f"   Max Drawdown: {result.max_drawdown:.4f}")
            print(f"   Calmar Ratio: {result.calmar_ratio:.4f}")


def demonstrate_realtime_updates(engine, data):
    """نمایش به‌روزرسانی‌های زمان واقعی"""
    print("\n" + "="*60)
    print("⚡ به‌روزرسانی‌های زمان واقعی")
    print("="*60)
    
    portfolio_returns = data['portfolio_returns']
    benchmark_returns = data['benchmark_returns']
    
    # Reset Kalman filter
    engine.kalman_filter.reset()
    
    # Simulate real-time updates for last 30 days
    recent_portfolio = portfolio_returns.tail(30)
    recent_benchmark = benchmark_returns.tail(30)
    
    print("\n📊 شبیه‌سازی 30 روز آخر:")
    print("روز    Alpha    Beta     Trend")
    print("-" * 35)
    
    for i, (date, port_ret) in enumerate(recent_portfolio.items()):
        bench_ret = recent_benchmark.loc[date]
        
        alpha, beta = engine.realtime_alpha_beta(port_ret, bench_ret)
        
        # Show every 5th day
        if i % 5 == 0:
            metrics = engine.get_realtime_metrics()
            trend = "↗" if metrics.get('alpha_trend', 0) > 0 else "↘"
            print(f"{i+1:3d}   {alpha:7.4f}  {beta:7.4f}   {trend}")
    
    # Final metrics
    final_metrics = engine.get_realtime_metrics()
    print(f"\n🎯 نتایج نهایی:")
    print(f"   Alpha فعلی: {final_metrics['current_alpha']:.4f}")
    print(f"   Beta فعلی: {final_metrics['current_beta']:.4f}")
    print(f"   روند Alpha: {final_metrics['alpha_trend']:.6f}")
    print(f"   روند Beta: {final_metrics['beta_trend']:.6f}")
    print(f"   تعداد مشاهدات: {final_metrics['observations_count']}")


def demonstrate_multi_factor_analysis(engine, data):
    """نمایش تحلیل چندعاملی"""
    print("\n" + "="*60)
    print("🔍 تحلیل چندعاملی")
    print("="*60)
    
    portfolio_returns = data['portfolio_returns']
    factor_returns = data['factor_returns']
    true_params = data['true_parameters']
    
    # Multi-factor attribution
    multi_factor_result = engine.multi_factor_attribution(portfolio_returns, factor_returns)
    
    print(f"\n📊 نتایج مدل چندعاملی:")
    print(f"   Alpha: {multi_factor_result['alpha']:.4f}")
    print(f"   R²: {multi_factor_result['r_squared']:.4f}")
    print(f"   Residual Volatility: {multi_factor_result['residual_vol']:.4f}")
    
    print(f"\n🎯 در معرض فاکتورها:")
    exposures = multi_factor_result['exposures']
    factor_names = ['Market', 'Size', 'Value', 'Momentum']
    true_exposures = [true_params['beta'], true_params['size_exposure'], 
                     true_params['value_exposure'], true_params['momentum_exposure']]
    
    for i, (factor_type, exposure) in enumerate(exposures.items()):
        true_exp = true_exposures[i] if i < len(true_exposures) else 0
        print(f"   {factor_names[i]:10s}: {exposure.exposure:7.4f} (واقعی: {true_exp:7.4f})")
        print(f"                Contribution: {exposure.contribution_to_return:7.4f}")


def demonstrate_alpha_decomposition(engine, data):
    """نمایش تجزیه منابع Alpha"""
    print("\n" + "="*60)
    print("🔬 تجزیه منابع Alpha")
    print("="*60)
    
    portfolio_returns = data['portfolio_returns']
    
    # Decompose alpha sources
    alpha_sources = engine.decompose_alpha_sources(portfolio_returns)
    
    print(f"\n📊 منابع Alpha:")
    total_alpha = sum(alpha_sources.values())
    
    for source, contribution in alpha_sources.items():
        percentage = (contribution / total_alpha * 100) if total_alpha != 0 else 0
        print(f"   {source:20s}: {contribution:8.4f} ({percentage:5.1f}%)")
    
    print(f"   {'Total':20s}: {total_alpha:8.4f} (100.0%)")


def demonstrate_alpha_prediction(engine, data):
    """نمایش پیش‌بینی Alpha"""
    print("\n" + "="*60)
    print("🔮 پیش‌بینی Alpha با ML")
    print("="*60)
    
    portfolio_returns = data['portfolio_returns']
    market_data = data['market_data']
    
    try:
        # Predict future alpha
        prediction_result = engine.predict_future_alpha(
            portfolio_returns, 
            market_data, 
            horizon_days=21
        )
        
        print(f"\n🎯 پیش‌بینی Alpha (21 روز آینده):")
        print(f"   Alpha پیش‌بینی شده: {prediction_result['predicted_alpha']:.4f}")
        print(f"   اعتماد مدل (R²): {prediction_result['confidence']:.4f}")
        print(f"   افق زمانی: {prediction_result['horizon_days']} روز")
        
        print(f"\n📊 اهمیت ویژگی‌ها:")
        feature_importance = prediction_result['feature_importance']
        sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
        
        for feature, importance in sorted_features[:5]:  # Top 5 features
            print(f"   {feature:20s}: {importance:.4f}")
            
    except Exception as e:
        print(f"⚠️  خطا در پیش‌بینی Alpha: {str(e)}")


def demonstrate_comprehensive_report(engine, data):
    """نمایش گزارش جامع"""
    print("\n" + "="*60)
    print("📋 گزارش جامع Attribution")
    print("="*60)
    
    portfolio_returns = data['portfolio_returns']
    benchmark_returns = data['benchmark_returns']
    factor_returns = data['factor_returns']
    
    # Generate comprehensive report
    report = engine.generate_attribution_report(
        portfolio_returns, 
        benchmark_returns, 
        factor_returns
    )
    
    print(f"\n📊 خلاصه عملکرد:")
    perf_summary = report['performance_summary']
    print(f"   بازده کل: {perf_summary['total_return']:.2%}")
    print(f"   بازده سالانه: {perf_summary['annualized_return']:.2%}")
    print(f"   نوسانات: {perf_summary['volatility']:.2%}")
    print(f"   نسبت شارپ: {perf_summary['sharpe_ratio']:.4f}")
    print(f"   حداکثر افت: {perf_summary['max_drawdown']:.2%}")
    print(f"   نسبت کالمار: {perf_summary['calmar_ratio']:.4f}")
    
    print(f"\n🎯 Alpha/Beta:")
    alpha_beta = report['alpha_beta']
    print(f"   Alpha: {alpha_beta.alpha:.4f} (سالانه: {alpha_beta.jensen_alpha:.2%})")
    print(f"   Beta: {alpha_beta.beta:.4f}")
    print(f"   R²: {alpha_beta.r_squared:.4f}")
    print(f"   Information Ratio: {alpha_beta.information_ratio:.4f}")
    print(f"   Tracking Error: {alpha_beta.tracking_error:.2%}")
    
    print(f"\n🔍 منابع Alpha:")
    alpha_sources = report['alpha_sources']
    for source, contribution in alpha_sources.items():
        print(f"   {source:20s}: {contribution:8.4f}")
    
    print(f"\n📅 دوره تحلیل:")
    print(f"   از: {report['period_start'].strftime('%Y-%m-%d')}")
    print(f"   تا: {report['period_end'].strftime('%Y-%m-%d')}")
    print(f"   تولید گزارش: {report['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")


def create_visualization(data):
    """ایجاد نمودارهای تصویری"""
    print("\n" + "="*60)
    print("📈 ایجاد نمودارهای تصویری")
    print("="*60)
    
    portfolio_returns = data['portfolio_returns']
    benchmark_returns = data['benchmark_returns']
    
    # Calculate cumulative returns
    portfolio_cumulative = (1 + portfolio_returns).cumprod()
    benchmark_cumulative = (1 + benchmark_returns).cumprod()
    
    # Create figure with subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Alpha/Beta Attribution Analysis', fontsize=16, fontweight='bold')
    
    # Plot 1: Cumulative Returns
    axes[0, 0].plot(portfolio_cumulative.index, portfolio_cumulative.values, 
                    label='Portfolio', linewidth=2, color='blue')
    axes[0, 0].plot(benchmark_cumulative.index, benchmark_cumulative.values, 
                    label='Benchmark', linewidth=2, color='red', linestyle='--')
    axes[0, 0].set_title('Cumulative Returns')
    axes[0, 0].set_ylabel('Cumulative Return')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Plot 2: Rolling Alpha (30-day)
    rolling_alpha = []
    rolling_dates = []
    
    for i in range(30, len(portfolio_returns)):
        period_portfolio = portfolio_returns.iloc[i-30:i]
        period_benchmark = benchmark_returns.iloc[i-30:i]
        
        if len(period_portfolio) > 5:
            # Simple alpha calculation
            excess_return = period_portfolio.mean() - period_benchmark.mean()
            rolling_alpha.append(excess_return * 252)  # Annualized
            rolling_dates.append(portfolio_returns.index[i])
    
    axes[0, 1].plot(rolling_dates, rolling_alpha, color='green', linewidth=2)
    axes[0, 1].set_title('Rolling Alpha (30-day)')
    axes[0, 1].set_ylabel('Alpha (Annualized)')
    axes[0, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    axes[0, 1].grid(True, alpha=0.3)
    
    # Plot 3: Return Distribution
    axes[1, 0].hist(portfolio_returns.values, bins=50, alpha=0.7, 
                    label='Portfolio', color='blue', density=True)
    axes[1, 0].hist(benchmark_returns.values, bins=50, alpha=0.7, 
                    label='Benchmark', color='red', density=True)
    axes[1, 0].set_title('Return Distribution')
    axes[1, 0].set_xlabel('Daily Return')
    axes[1, 0].set_ylabel('Density')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # Plot 4: Scatter Plot (Beta estimation)
    axes[1, 1].scatter(benchmark_returns.values, portfolio_returns.values, 
                       alpha=0.5, s=10, color='purple')
    
    # Add regression line
    from sklearn.linear_model import LinearRegression
    X = benchmark_returns.values.reshape(-1, 1)
    y = portfolio_returns.values
    reg = LinearRegression().fit(X, y)
    
    x_line = np.linspace(benchmark_returns.min(), benchmark_returns.max(), 100)
    y_line = reg.predict(x_line.reshape(-1, 1))
    axes[1, 1].plot(x_line, y_line, color='red', linewidth=2, 
                    label=f'Beta = {reg.coef_[0]:.3f}')
    
    axes[1, 1].set_title('Beta Estimation')
    axes[1, 1].set_xlabel('Benchmark Return')
    axes[1, 1].set_ylabel('Portfolio Return')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save the plot
    plt.savefig('alpha_beta_analysis.png', dpi=300, bbox_inches='tight')
    print("📊 نمودارها در فایل 'alpha_beta_analysis.png' ذخیره شد")
    
    # Show basic statistics
    print(f"\n📊 آمار کلیدی:")
    print(f"   بازده کل Portfolio: {(portfolio_cumulative.iloc[-1] - 1):.2%}")
    print(f"   بازده کل Benchmark: {(benchmark_cumulative.iloc[-1] - 1):.2%}")
    print(f"   Excess Return: {(portfolio_cumulative.iloc[-1] - benchmark_cumulative.iloc[-1]):.2%}")
    print(f"   Portfolio Volatility: {portfolio_returns.std() * np.sqrt(252):.2%}")
    print(f"   Benchmark Volatility: {benchmark_returns.std() * np.sqrt(252):.2%}")


def main():
    """تابع اصلی"""
    print("🚀 مثال کاربردی Alpha/Beta Attribution System")
    print("="*60)
    
    # Generate sample data
    data = generate_sample_data()
    print("✅ داده‌های نمونه تولید شد")
    
    # Initialize Attribution Engine
    engine = AlphaBetaAttributionEngine(data['benchmark_returns'])
    print("✅ موتور Attribution راه‌اندازی شد")
    
    # Demonstrate different capabilities
    demonstrate_basic_alpha_beta(engine, data)
    demonstrate_realtime_updates(engine, data)
    demonstrate_multi_factor_analysis(engine, data)
    demonstrate_alpha_decomposition(engine, data)
    demonstrate_alpha_prediction(engine, data)
    demonstrate_comprehensive_report(engine, data)
    
    # Create visualizations
    try:
        create_visualization(data)
    except Exception as e:
        print(f"⚠️  خطا در ایجاد نمودار: {str(e)}")
    
    print("\n" + "="*60)
    print("🎉 نمایش کامل Alpha/Beta Attribution System")
    print("="*60)
    
    # Performance comparison
    true_params = data['true_parameters']
    final_result = engine.calculate_alpha_beta(
        data['portfolio_returns'], 
        data['benchmark_returns']
    )
    
    print(f"\n🎯 مقایسه نتایج:")
    print(f"   Alpha - واقعی: {true_params['alpha']:.4f}, تخمین: {final_result.alpha:.4f}")
    print(f"   Beta  - واقعی: {true_params['beta']:.4f}, تخمین: {final_result.beta:.4f}")
    print(f"   دقت Alpha: {abs(final_result.alpha - true_params['alpha']):.4f}")
    print(f"   دقت Beta: {abs(final_result.beta - true_params['beta']):.4f}")
    
    print(f"\n💡 نکات کلیدی:")
    print(f"   • سیستم Alpha/Beta با دقت بالا کار می‌کند")
    print(f"   • فیلتر کالمن برای به‌روزرسانی زمان واقعی مناسب است")
    print(f"   • تحلیل چندعاملی بینش عمیق‌تری ارائه می‌دهد")
    print(f"   • ML برای پیش‌بینی Alpha کاربرد دارد")
    print(f"   • سیستم برای استفاده تولیدی آماده است")


if __name__ == "__main__":
    main() 