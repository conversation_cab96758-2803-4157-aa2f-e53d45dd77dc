# 📊 گزارش کشف مدل‌های HuggingFace
==================================================
📅 تاریخ: 2025/07/08 18:26:00

## 📈 خلاصه نتایج
✅ مدل‌های کارآمد: 7
🟡 مدل‌های در حال لود: 0
❌ مدل‌های ناموفق: 20

## 🚀 مدل‌های کارآمد
### financial_sentiment
**ID:** `mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis`
**قابلیت‌ها:** sentiment_analysis, financial_analysis
**تست‌های موفق:** sentiment_positive, sentiment_negative, sentiment_neutral, financial_news, stock_mention

### finbert
**ID:** `ProsusAI/finbert`
**قابلیت‌ها:** sentiment_analysis, financial_analysis
**تست‌های موفق:** sentiment_positive, sentiment_negative, sentiment_neutral, financial_news, stock_mention

### financial_bert
**ID:** `nlptown/bert-base-multilingual-uncased-sentiment`
**قابلیت‌ها:** sentiment_analysis, financial_analysis
**تست‌های موفق:** sentiment_positive, sentiment_negative, sentiment_neutral, financial_news, stock_mention

### finbert_esg
**ID:** `yiyanghkust/finbert-esg`
**قابلیت‌ها:** sentiment_analysis, financial_analysis
**تست‌های موفق:** sentiment_positive, sentiment_negative, sentiment_neutral, financial_news, stock_mention

### trading_bert
**ID:** `nlptown/bert-base-multilingual-uncased-sentiment`
**قابلیت‌ها:** sentiment_analysis, financial_analysis
**تست‌های موفق:** sentiment_positive, sentiment_negative, sentiment_neutral, financial_news, stock_mention

### bart_large
**ID:** `facebook/bart-large`
**قابلیت‌ها:** sentiment_analysis, financial_analysis
**تست‌های موفق:** sentiment_positive, sentiment_negative, sentiment_neutral, financial_news, stock_mention

### emotion_english_distilroberta_base
**ID:** `j-hartmann/emotion-english-distilroberta-base`
**قابلیت‌ها:** sentiment_analysis, financial_analysis
**تست‌های موفق:** sentiment_positive, sentiment_negative, sentiment_neutral, financial_news, stock_mention

## 💻 کد نمونه استفاده
```python
# استفاده از مدل‌های کشف شده
working_models = {
    "financial_sentiment": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
    "finbert": "ProsusAI/finbert",
    "financial_bert": "nlptown/bert-base-multilingual-uncased-sentiment",
    "finbert_esg": "yiyanghkust/finbert-esg",
    "trading_bert": "nlptown/bert-base-multilingual-uncased-sentiment",
    "bart_large": "facebook/bart-large",
    "emotion_english_distilroberta_base": "j-hartmann/emotion-english-distilroberta-base",
}

# تست مدل
model_id = working_models["financial_sentiment"]
# فراخوانی API...
```