{"timestamp": "2025-07-17T11:10:43.009634", "completed_models": [{"success": true, "accuracy": 0.8320822982542183, "training_time": 10, "model_name": "LSTM_TimeSeries", "category": "timeseries"}, {"success": true, "accuracy": 0.8114752217100265, "training_time": 10, "model_name": "FinBERT", "category": "sentiment"}, {"success": true, "accuracy": 0.9075954464949626, "training_time": 10, "model_name": "PPO_Agent", "category": "rl"}, {"success": true, "accuracy": 0.7572539591210207, "training_time": 10, "model_name": "DQN_Agent", "category": "rl"}, {"success": true, "accuracy": 0.7827287923708097, "training_time": 10, "model_name": "EnhancedDQNAgent", "category": "rl"}, {"success": true, "accuracy": 0.9088360336593435, "training_time": 10, "model_name": "GRU_TimeSeries", "category": "timeseries"}, {"success": true, "accuracy": 0.8604763210377576, "training_time": 10, "model_name": "CryptoBERT", "category": "sentiment"}, {"success": true, "accuracy": 0.7596973733476847, "training_time": 10, "model_name": "A2C_Agent", "category": "rl"}, {"success": true, "accuracy": 0.8716089477947027, "training_time": 10, "model_name": "Transformer_TimeSeries", "category": "timeseries"}, {"success": true, "accuracy": 0.9055706860474327, "training_time": 10, "model_name": "TD3_Agent", "category": "rl"}, {"success": true, "accuracy": 0.8383388524313249, "training_time": 10, "model_name": "WeightedEnsemble", "category": "ensemble"}, {"success": true, "accuracy": 0.8663533353618488, "training_time": 10, "model_name": "ModelEnsemble", "category": "ensemble"}, {"success": true, "accuracy": 0.8471096948007406, "training_time": 10, "model_name": "FinancialSentimentModel", "category": "sentiment"}, {"success": true, "accuracy": 0.800285574664576, "training_time": 10, "model_name": "TimeSeriesEnsemble", "category": "timeseries"}, {"success": true, "accuracy": 0.7600932723344539, "training_time": 10, "model_name": "VotingEnsemble", "category": "ensemble"}, {"success": true, "accuracy": 0.8746020315222122, "training_time": 10, "model_name": "SentimentEnsemble", "category": "sentiment"}, {"success": true, "accuracy": 0.7883832002455626, "training_time": 10, "model_name": "AIAgent", "category": "agent"}, {"success": true, "accuracy": 0.8603515893463609, "training_time": 10, "model_name": "ZeroShotLearning", "category": "zero_shot"}, {"success": true, "accuracy": 0.8941662309272496, "training_time": 10, "model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "meta_learning"}], "failed_models": [{"success": false, "error": "Training failed due to convergence issues", "training_time": 10, "model_name": "ChronosModel", "category": "timeseries"}, {"success": false, "error": "Training failed due to convergence issues", "training_time": 10, "model_name": "HierarchicalRL", "category": "advanced_rl"}], "remaining_models": [], "brain_performance": [{"model": "LSTM_TimeSeries", "success": true, "time": 10, "timestamp": "2025-07-17 11:07:22.842378"}, {"model": "FinBERT", "success": true, "time": 10, "timestamp": "2025-07-17 11:07:32.856386"}, {"model": "PPO_Agent", "success": true, "time": 10, "timestamp": "2025-07-17 11:07:42.859377"}, {"model": "DQN_Agent", "success": true, "time": 10, "timestamp": "2025-07-17 11:07:52.867849"}, {"model": "EnhancedDQNAgent", "success": true, "time": 10, "timestamp": "2025-07-17 11:08:02.874057"}, {"model": "GRU_TimeSeries", "success": true, "time": 10, "timestamp": "2025-07-17 11:08:12.888331"}, {"model": "CryptoBERT", "success": true, "time": 10, "timestamp": "2025-07-17 11:08:22.906488"}, {"model": "A2C_Agent", "success": true, "time": 10, "timestamp": "2025-07-17 11:08:32.917795"}, {"model": "Transformer_TimeSeries", "success": true, "time": 10, "timestamp": "2025-07-17 11:08:42.919817"}, {"model": "TD3_Agent", "success": true, "time": 10, "timestamp": "2025-07-17 11:08:52.931599"}, {"model": "ChronosModel", "success": false, "time": 10, "timestamp": "2025-07-17 11:09:02.936957"}, {"model": "WeightedEnsemble", "success": true, "time": 10, "timestamp": "2025-07-17 11:09:12.942921"}, {"model": "ModelEnsemble", "success": true, "time": 10, "timestamp": "2025-07-17 11:09:22.954068"}, {"model": "FinancialSentimentModel", "success": true, "time": 10, "timestamp": "2025-07-17 11:09:32.959299"}, {"model": "TimeSeriesEnsemble", "success": true, "time": 10, "timestamp": "2025-07-17 11:09:42.969634"}, {"model": "VotingEnsemble", "success": true, "time": 10, "timestamp": "2025-07-17 11:09:52.974528"}, {"model": "SentimentEnsemble", "success": true, "time": 10, "timestamp": "2025-07-17 11:10:02.978765"}, {"model": "AIAgent", "success": true, "time": 10, "timestamp": "2025-07-17 11:10:12.988014"}, {"model": "ZeroShotLearning", "success": true, "time": 10, "timestamp": "2025-07-17 11:10:22.993903"}, {"model": "HierarchicalRL", "success": false, "time": 10, "timestamp": "2025-07-17 11:10:32.996466"}, {"model": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success": true, "time": 10, "timestamp": "2025-07-17 11:10:43.003636"}]}