import os
import logging
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline
from langdetect import detect, LangDetectException
from utils.source_credibility import SourceCredibility

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set proxy for transformers if needed
if os.path.exists('PROXY.json'):
    logger.info("پروکسی با موفقیت تنظیم شد.")

class SentimentAnalyzer:
    """
    Financial sentiment analyzer with multi-language support and source credibility weighting.
    
    This class uses pre-trained language models to analyze sentiment in financial texts,
    with support for multiple languages and weighting based on source credibility.
    """
    
    def __init__(self, credibility_manager=None):
        """
        Initialize the SentimentAnalyzer with models for different languages.
        
        Args:
            credibility_manager (SourceCredibility, optional): Manager for source credibility scores.
                If None, a default instance will be created.
        """
        # Set device
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Device set to use {self.device}")
        
        # Initialize credibility manager
        if credibility_manager is None:
            self.credibility_manager = SourceCredibility()
        else:
            self.credibility_manager = credibility_manager
        
        # Configure models for different languages
        self.model_configs = {
            'en': {
                'name': 'ProsusAI/finbert',
                'pipeline': None
            },
            'fa': {
                'name': 'HooshvareLab/bert-fa-base-uncased',
                'pipeline': None
            }
        }
        
        # Load models for each language
        self._load_models()
    
    def _load_models(self):
        """
        Load models for each configured language.
        """
        for lang, config in self.model_configs.items():
            try:
                logger.info(f"Loading {lang} model: {config['name']}")
                
                # Load tokenizer and model
                tokenizer = AutoTokenizer.from_pretrained(config['name'])
                model = AutoModelForSequenceClassification.from_pretrained(config['name']).to(self.device)
                
                # Create sentiment analysis pipeline
                self.model_configs[lang]['pipeline'] = pipeline(
                    "sentiment-analysis",
                    model=model,
                    tokenizer=tokenizer,
                    device=0 if self.device == "cuda" else -1
                )
                logger.info(f"{lang.upper()} model loaded successfully")
            except Exception as e:
                logger.error(f"Error loading {lang} model: {e}")
                # Continue with other models if one fails
    
    def detect_language(self, text):
        """
        Detect the language of the given text.
        
        Args:
            text (str): The text to analyze
            
        Returns:
            str: Language code ('en', 'fa', etc.)
        """
        try:
            lang = detect(text)
            # همه زبان‌ها به انگلیسی نگاشت می‌شوند
            return 'en'
        except LangDetectException:
            logger.warning("Could not detect language. Falling back to English.")
            return 'en'
        except Exception as e:
            logger.error(f"Error in language detection: {e}")
            return 'en'  # Default to English on any error
    
    def analyze(self, text, language=None, source=None):
        """
        Analyze the sentiment of the given financial text.
        
        Args:
            text (str or list): A single text string or a list of text strings to analyze
            language (str, optional): Force a specific language ('en', 'fa'). If None, language is auto-detected.
            source (str, optional): The source of the text (e.g., 'Reuters', 'Twitter').
                                  Used for credibility weighting.
            
        Returns:
            dict or list: A dictionary with 'label', 'score', and 'weighted_score' for a single text,
                         or a list of such dictionaries for multiple texts
        """
        # Handle empty or None inputs
        if text is None:
            logger.warning("None value provided for sentiment analysis")
            return {'label': 'neutral', 'score': 0.0, 'weighted_score': 0.0}
            
        if isinstance(text, list) and len(text) == 0:
            logger.warning("Empty list provided for sentiment analysis")
            return []
            
        if isinstance(text, str) and not text.strip():
            logger.warning("Empty text provided for sentiment analysis")
            return {'label': 'neutral', 'score': 0.0, 'weighted_score': 0.0}
        
        # Handle both single string and list of strings
        is_single = isinstance(text, str)
        texts = [text] if is_single else text
        results = []
        
        for single_text in texts:
            try:
                # Detect language if not specified
                if language is None:
                    try:
                        lang = self.detect_language(single_text)
                    except Exception as e:
                        logger.error(f"Error in language detection: {e}")
                        # Return neutral sentiment on language detection error
                        results.append({'label': 'neutral', 'score': 0.0, 'weighted_score': 0.0})
                        continue
                else:
                    lang = language
                
                # Ensure we have a pipeline for this language
                if lang not in self.model_configs or self.model_configs[lang]['pipeline'] is None:
                    logger.warning(f"No model available for language '{lang}'. Using English model as fallback.")
                    lang = 'en'
                    # If English model is also not available, return neutral
                    if self.model_configs[lang]['pipeline'] is None:
                        results.append({'label': 'neutral', 'score': 0.0, 'weighted_score': 0.0})
                        continue
                
                # Run sentiment analysis
                pipeline = self.model_configs[lang]['pipeline']
                result = pipeline(single_text)[0]
                
                # Normalize label to lowercase
                result['label'] = result['label'].lower()
                
                # Calculate normalized sentiment score (-1 to 1 range)
                normalized_score = result['score'] if result['label'] == 'positive' else (
                    -result['score'] if result['label'] == 'negative' else 0.0
                )
                
                # Apply credibility weighting
                weighted_score = self.credibility_manager.apply_credibility_weight(normalized_score, source)
                
                # Determine final label based on weighted score
                if weighted_score > 0.1:
                    final_label = 'positive'
                elif weighted_score < -0.1:
                    final_label = 'negative'
                else:
                    final_label = 'neutral'
                
                # Add weighted score and final label to result
                result_with_weighting = {
                    'label': final_label,
                    'score': result['score'],
                    'weighted_score': weighted_score,
                    'credibility': self.credibility_manager.get_score(source)
                }
                
                results.append(result_with_weighting)
                
            except Exception as e:
                logger.error(f"Error analyzing text '{single_text[:30]}...': {e}")
                results.append({'label': 'neutral', 'score': 0.0, 'weighted_score': 0.0})
        
        # Return single result or list based on input type
        return results[0] if is_single else results

    def get_sentiment_score(self, text, language=None, source=None):
        """
        Get a normalized sentiment score in the range [-1, 1], weighted by source credibility.
        
        Args:
            text (str): The text to analyze
            language (str, optional): Force a specific language. If None, language is auto-detected.
            source (str, optional): The source of the text (e.g., 'Reuters', 'Twitter').
            
        Returns:
            float: A weighted score between -1 (very negative) and 1 (very positive)
        """
        try:
            result = self.analyze(text, language, source)
            return result['weighted_score']
        except Exception as e:
            logger.error(f"Error in get_sentiment_score: {e}")
            return 0.0

if __name__ == "__main__":
    # Example usage
    analyzer = SentimentAnalyzer()
    
    # Test with English texts from different sources
    en_texts = [
        "The company reported strong earnings, exceeding analyst expectations.",
        "The stock plummeted following the CEO's resignation.",
        "The market remained stable throughout the trading session."
    ]
    
    sources = ["Reuters", "Twitter", "Unknown Source"]
    
    print("\n=== English Text Analysis with Source Credibility ===")
    for i, text in enumerate(en_texts):
        source = sources[i % len(sources)]
        sentiment = analyzer.analyze(text, source=source)
        print(f"Text: '{text}'")
        print(f"Source: {source} (credibility: {sentiment['credibility']:.2f})")
        print(f"Sentiment: {sentiment['label']} (raw score: {sentiment['score']:.4f})")
        print(f"Weighted score: {sentiment['weighted_score']:.4f}")
        print("-" * 80)
    
    # Test with Persian texts
    fa_texts = [
        "سود شرکت بسیار فراتر از انتظارات بازار بود.",
        "قیمت سهام پس از استعفای مدیر عامل سقوط کرد.",
        "بازار در طول جلسه معاملاتی پایدار باقی ماند."
    ]
    
    print("\n=== Persian Text Analysis ===")
    for text in fa_texts:
        sentiment = analyzer.analyze(text, source="Financial Times")
        print(f"Text: '{text}'")
        print(f"Source: Financial Times (credibility: {sentiment['credibility']:.2f})")
        print(f"Sentiment: {sentiment['label']} (raw score: {sentiment['score']:.4f})")
        print(f"Weighted score: {sentiment['weighted_score']:.4f}")
        print("-" * 80) 