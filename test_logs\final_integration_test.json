{"command": "python test_final_integration.py", "timestamp": "2025-07-08T05:51:56.670097", "execution_time": 2.0295026302337646, "return_code": 1, "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"D:\\project\\test_final_integration.py\", line 655, in <module>\n    success = main()\n  File \"D:\\project\\test_final_integration.py\", line 631, in main\n    print(\"\\U0001f680 FINAL ADVANCED TRADING SYSTEM INTEGRATION TEST\")\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f680' in position 0: character maps to <undefined>\n", "success": false}