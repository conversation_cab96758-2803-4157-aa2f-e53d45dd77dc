"""
🎯 Model Manager
مدیریت مرکزی مدل‌های AI

این ماژول شامل مدیریت، بارگذاری، و کش مدل‌ها است
"""

import os
import json
import pickle
import threading
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import gc
import psutil
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from core.base import BaseModel, ModelPrediction
from core.advanced_config import ProxyConfig
from core.logger import get_logger
from core.exceptions import ModelLoadError, ResourceError, NetworkError
# from core.utils import memory_monitor  # Commented out - not available

logger = get_logger(__name__)

@dataclass
class ModelInfo:
    """اطلاعات مدل"""
    name: str
    model_path: str
    model_type: str
    status: str  # loading, loaded, error, unloaded
    priority: int
    memory_usage: float  # MB
    load_time: float  # seconds
    last_used: datetime
    usage_count: int
    error_count: int
    cache_size: int
    config: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        data = asdict(self)
        data['last_used'] = self.last_used.isoformat()
        return data

@dataclass
class ModelMetrics:
    """معیارهای عملکرد مدل"""
    model_name: str
    total_predictions: int
    avg_prediction_time: float
    avg_confidence: float
    success_rate: float
    memory_usage: float
    last_prediction: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        data = asdict(self)
        data['last_prediction'] = self.last_prediction.isoformat()
        return data

class ModelCache:
    """کش مدل‌ها"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        self.max_size = max_size
        self.ttl = ttl  # seconds
        self.cache = {}
        self.access_times = {}
        self.lock = threading.Lock()
    
    def get(self, key: str) -> Optional[Any]:
        """دریافت از کش"""
        with self.lock:
            if key in self.cache:
                # Check TTL
                if time.time() - self.access_times[key] < self.ttl:
                    self.access_times[key] = time.time()
                    return self.cache[key]
                else:
                    # Expired
                    del self.cache[key]
                    del self.access_times[key]
            
            return None
    
    def set(self, key: str, value: Any):
        """ذخیره در کش"""
        with self.lock:
            # Remove old entries if cache is full
            if len(self.cache) >= self.max_size:
                self._evict_oldest()
            
            self.cache[key] = value
            self.access_times[key] = time.time()
    
    def _evict_oldest(self):
        """حذف قدیمی‌ترین ورودی"""
        if not self.access_times:
            return
        
        oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[oldest_key]
        del self.access_times[oldest_key]
    
    def clear(self):
        """پاک کردن کش"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def size(self) -> int:
        """اندازه کش"""
        return len(self.cache)
    
    def get_stats(self) -> Dict[str, Any]:
        """آمار کش"""
        with self.lock:
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "ttl": self.ttl,
                "hit_rate": getattr(self, '_hit_rate', 0.0)
            }

class ModelRegistry:
    """رجیستری مدل‌ها"""
    
    def __init__(self):
        self.models = {}
        self.model_info = {}
        self.lock = threading.Lock()
        self.logger = get_logger(__name__)
    
    def register_model(self, name: str, model_path: str, model_type: str, 
                      priority: int = 1, config: Dict[str, Any] = None):
        """ثبت مدل"""
        with self.lock:
            model_info = ModelInfo(
                name=name,
                model_path=model_path,
                model_type=model_type,
                status="registered",
                priority=priority,
                memory_usage=0.0,
                load_time=0.0,
                last_used=datetime.now(),
                usage_count=0,
                error_count=0,
                cache_size=0,
                config=config or {}
            )
            
            self.model_info[name] = model_info
            self.logger.info(f"Model registered: {name} ({model_type})")
    
    def get_model(self, name: str) -> Optional[BaseModel]:
        """دریافت مدل"""
        with self.lock:
            return self.models.get(name)
    
    def set_model(self, name: str, model: BaseModel):
        """تنظیم مدل"""
        with self.lock:
            self.models[name] = model
            if name in self.model_info:
                self.model_info[name].status = "loaded"
                self.model_info[name].last_used = datetime.now()
    
    def unload_model(self, name: str):
        """خارج کردن مدل از حافظه"""
        with self.lock:
            if name in self.models:
                del self.models[name]
                gc.collect()  # Force garbage collection
                
                if name in self.model_info:
                    self.model_info[name].status = "unloaded"
                    self.model_info[name].memory_usage = 0.0
                
                self.logger.info(f"Model unloaded: {name}")
    
    def get_all_models(self) -> Dict[str, BaseModel]:
        """دریافت تمام مدل‌ها"""
        with self.lock:
            return self.models.copy()
    
    def get_model_info(self, name: str) -> Optional[ModelInfo]:
        """دریافت اطلاعات مدل"""
        with self.lock:
            return self.model_info.get(name)
    
    def get_all_info(self) -> Dict[str, ModelInfo]:
        """دریافت تمام اطلاعات"""
        with self.lock:
            return self.model_info.copy()
    
    def update_usage(self, name: str, prediction_time: float = 0.0, success: bool = True):
        """به‌روزرسانی آمار استفاده"""
        with self.lock:
            if name in self.model_info:
                info = self.model_info[name]
                info.usage_count += 1
                info.last_used = datetime.now()
                
                if not success:
                    info.error_count += 1
                
                # Update load time if provided
                if prediction_time > 0:
                    info.load_time = prediction_time
    
    def get_models_by_type(self, model_type: str) -> List[str]:
        """دریافت مدل‌ها بر اساس نوع"""
        with self.lock:
            return [name for name, info in self.model_info.items() 
                   if info.model_type == model_type]
    
    def get_models_by_priority(self, limit: int = None) -> List[str]:
        """دریافت مدل‌ها بر اساس اولویت"""
        with self.lock:
            sorted_models = sorted(self.model_info.items(), 
                                 key=lambda x: x[1].priority)
            
            if limit:
                sorted_models = sorted_models[:limit]
            
            return [name for name, info in sorted_models]

class ModelManager:
    """مدیریت مدل‌ها"""
    
    def __init__(self, proxy_config: ProxyConfig = None, cache_dir: str = "models_cache"):
        self.proxy_config = proxy_config
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # Initialize components
        self.registry = ModelRegistry()
        self.cache = ModelCache(max_size=1000, ttl=3600)
        self.metrics = {}
        
        # Threading
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.loading_locks = {}
        
        # Memory management
        self.max_memory_usage = 4096  # MB
        self.memory_check_interval = 60  # seconds
        self.last_memory_check = datetime.now()
        
        self.logger = get_logger(__name__)
        
        # Setup proxy if provided
        if proxy_config and proxy_config.enabled:
            self._setup_proxy()
    
    async def initialize(self) -> bool:
        """Initialize the model manager asynchronously"""
        try:
            self.logger.info("🔄 Initializing Model Manager...")
            
            # Ensure cache directory exists
            self.cache_dir.mkdir(exist_ok=True)
            
            # Initialize registry if not already done
            if not hasattr(self, 'registry') or self.registry is None:
                self.registry = ModelRegistry()
            
            # Initialize cache if not already done
            if not hasattr(self, 'cache') or self.cache is None:
                self.cache = ModelCache(max_size=1000, ttl=3600)
            
            # Register default models if not already done
            self._register_default_models()
            
            self.logger.info("✅ Model Manager initialized successfully")
            return True
        
        except Exception as e:
            self.logger.error(f"❌ Model Manager initialization failed: {e}")
            return False
    
    def _register_default_models(self):
        """Register default models"""
        try:
            # Register sentiment models
            self.registry.register_model(
                name="financial_sentiment",
                model_path="models/sentiment/financial_sentiment",
                model_type="sentiment",
                priority=1,
                config={"max_length": 512}
            )
            
            # Register time series models
            self.registry.register_model(
                name="time_series_lstm",
                model_path="models/timeseries/lstm_model",
                model_type="time_series",
                priority=2,
                config={"sequence_length": 60}
            )
            
            # Register document models
            self.registry.register_model(
                name="document_analyzer",
                model_path="models/document/document_analyzer",
                model_type="document",
                priority=3,
                config={"max_length": 1024}
            )
            
            self.logger.info("✅ Default models registered")
            
        except Exception as e:
            self.logger.warning(f"⚠️ Could not register default models: {e}")
    
    def _setup_proxy(self):
        """تنظیم پروکسی"""
        import os
        
        proxy_url = self.proxy_config.url
        os.environ['HTTP_PROXY'] = proxy_url
        os.environ['HTTPS_PROXY'] = proxy_url
        os.environ['http_proxy'] = proxy_url
        os.environ['https_proxy'] = proxy_url
        
        self.logger.info(f"Proxy configured: {proxy_url}")
    
    def load_model(self, name: str, force_reload: bool = False) -> BaseModel:
        """بارگذاری مدل"""
        # Check if model is already loaded
        if not force_reload:
            existing_model = self.registry.get_model(name)
            if existing_model:
                self.registry.update_usage(name)
                return existing_model
        
        # Get model info
        model_info = self.registry.get_model_info(name)
        if not model_info:
            raise ModelLoadError(f"Model not registered: {name}")
        
        # Check memory before loading
        self._check_memory_usage()
        
        # Prevent concurrent loading of the same model
        if name not in self.loading_locks:
            self.loading_locks[name] = threading.Lock()
        
        with self.loading_locks[name]:
            # Double-check if model was loaded while waiting
            if not force_reload:
                existing_model = self.registry.get_model(name)
                if existing_model:
                    return existing_model
            
            try:
                start_time = time.time()
                self.logger.info(f"Loading model: {name}")
                
                # Update status
                model_info.status = "loading"
                
                # Load the appropriate model based on type
                model = self._create_model_instance(model_info)
                
                # Load the actual model
                success = model.load_model(
                    model_info.model_path,
                    timeout=self.proxy_config.timeout if self.proxy_config else 30,
                    **model_info.config
                )
                
                if not success:
                    raise ModelLoadError(f"Failed to load model: {name}")
                
                # Calculate metrics
                load_time = time.time() - start_time
                memory_usage = self._estimate_model_memory(model)
                
                # Update model info
                model_info.status = "loaded"
                model_info.load_time = load_time
                model_info.memory_usage = memory_usage
                model_info.last_used = datetime.now()
                
                # Register in registry
                self.registry.set_model(name, model)
                
                # Initialize metrics
                self.metrics[name] = ModelMetrics(
                    model_name=name,
                    total_predictions=0,
                    avg_prediction_time=0.0,
                    avg_confidence=0.0,
                    success_rate=1.0,
                    memory_usage=memory_usage,
                    last_prediction=datetime.now()
                )
                
                self.logger.info(f"✅ Model loaded successfully: {name} ({load_time:.2f}s, {memory_usage:.2f}MB)")
                return model
                
            except Exception as e:
                # Update error status
                model_info.status = "error"
                model_info.error_count += 1
                
                error_msg = f"Failed to load model {name}: {str(e)}"
                self.logger.error(error_msg)
                raise ModelLoadError(error_msg, model_name=name, details={"error": str(e)})
    
    def _create_model_instance(self, model_info: ModelInfo) -> BaseModel:
        """ایجاد instance مدل"""
        model_type = model_info.model_type
        
        if model_type == "sentiment":
            from .sentiment_models import FinancialSentimentModel
            return FinancialSentimentModel(
                name=model_info.name,
                config=model_info.config
            )
        
        elif model_type == "time_series":
            from .time_series_models import TimeSeriesModel
            return TimeSeriesModel(
                name=model_info.name,
                config=model_info.config
            )
        
        elif model_type == "document" or model_type == "summarization":
            from .document_models import DocumentAnalyzer
            return DocumentAnalyzer(
                name=model_info.name,
                config=model_info.config
            )
        
        else:
            # Default to HuggingFace model
            from .huggingface_models import HuggingFaceModel
            return HuggingFaceModel(
                name=model_info.name,
                model_type=model_type,
                config=model_info.config
            )
    
    def _estimate_model_memory(self, model: BaseModel) -> float:
        """تخمین حافظه مدل"""
        try:
            # Basic memory estimation
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            return current_memory * 0.1  # Rough estimate
        except Exception:
            return 512.0  # Default estimate
    
    def _check_memory_usage(self):
        """بررسی استفاده حافظه"""
        current_time = datetime.now()
        
        if (current_time - self.last_memory_check).seconds < self.memory_check_interval:
            return
        
        try:
            memory_info = psutil.virtual_memory()
            memory_usage_mb = memory_info.used / 1024 / 1024
            
            if memory_usage_mb > self.max_memory_usage:
                self.logger.warning(f"High memory usage: {memory_usage_mb:.2f}MB")
                self._free_memory()
            
            self.last_memory_check = current_time
            
        except Exception as e:
            self.logger.error(f"Error checking memory: {e}")
    
    def _free_memory(self):
        """آزادسازی حافظه"""
        # Find least recently used models
        all_info = self.registry.get_all_info()
        
        # Sort by last used time
        sorted_models = sorted(all_info.items(), key=lambda x: x[1].last_used)
        
        # Unload oldest models
        freed_count = 0
        for name, info in sorted_models:
            if info.status == "loaded" and freed_count < 2:
                self.registry.unload_model(name)
                freed_count += 1
                self.logger.info(f"Freed memory by unloading: {name}")
        
        # Force garbage collection
        gc.collect()
    
    def predict(self, model_name: str, input_data: Any, **kwargs) -> ModelPrediction:
        """پیش‌بینی با مدل"""
        # Load model if not loaded
        model = self.load_model(model_name)
        
        start_time = time.time()
        
        try:
            # Check cache first
            cache_key = f"{model_name}_{hash(str(input_data))}"
            cached_result = self.cache.get(cache_key)
            
            if cached_result:
                self.logger.debug(f"Cache hit for {model_name}")
                return cached_result
            
            # Make prediction
            prediction = model.predict(input_data, **kwargs)
            
            # Cache result
            self.cache.set(cache_key, prediction)
            
            # Update metrics
            prediction_time = time.time() - start_time
            self._update_metrics(model_name, prediction, prediction_time, True)
            
            return prediction
            
        except Exception as e:
            prediction_time = time.time() - start_time
            self._update_metrics(model_name, None, prediction_time, False)
            
            self.logger.error(f"Prediction error for {model_name}: {e}")
            raise
    
    def _update_metrics(self, model_name: str, prediction: Optional[ModelPrediction], 
                       prediction_time: float, success: bool):
        """به‌روزرسانی معیارها"""
        if model_name not in self.metrics:
            self.metrics[model_name] = ModelMetrics(
                model_name=model_name,
                total_predictions=0,
                avg_prediction_time=0.0,
                avg_confidence=0.0,
                success_rate=1.0,
                memory_usage=0.0,
                last_prediction=datetime.now()
            )
        
        metrics = self.metrics[model_name]
        
        # Update prediction count
        metrics.total_predictions += 1
        
        # Update timing
        metrics.avg_prediction_time = (
            (metrics.avg_prediction_time * (metrics.total_predictions - 1) + prediction_time) /
            metrics.total_predictions
        )
        
        # Update confidence if prediction was successful
        if success and prediction:
            metrics.avg_confidence = (
                (metrics.avg_confidence * (metrics.total_predictions - 1) + prediction.confidence) /
                metrics.total_predictions
            )
        
        # Update success rate
        if success:
            metrics.success_rate = (
                (metrics.success_rate * (metrics.total_predictions - 1) + 1.0) /
                metrics.total_predictions
            )
        else:
            metrics.success_rate = (
                (metrics.success_rate * (metrics.total_predictions - 1) + 0.0) /
                metrics.total_predictions
            )
        
        metrics.last_prediction = datetime.now()
        
        # Update registry
        self.registry.update_usage(model_name, prediction_time, success)
    
    def get_model_status(self, model_name: str) -> Dict[str, Any]:
        """وضعیت مدل"""
        model_info = self.registry.get_model_info(model_name)
        if not model_info:
            return {"status": "not_found"}
        
        metrics = self.metrics.get(model_name)
        
        return {
            "info": model_info.to_dict(),
            "metrics": metrics.to_dict() if metrics else None,
            "loaded": model_info.status == "loaded",
            "memory_usage": model_info.memory_usage
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """وضعیت کل سیستم"""
        all_info = self.registry.get_all_info()
        
        status = {
            "total_models": len(all_info),
            "loaded_models": len([info for info in all_info.values() if info.status == "loaded"]),
            "total_memory": sum(info.memory_usage for info in all_info.values()),
            "cache_stats": self.cache.get_stats(),
            "models": {}
        }
        
        # Add individual model status
        for name in all_info.keys():
            status["models"][name] = self.get_model_status(name)
        
        return status
    
    def cleanup(self):
        """تمیز کردن منابع"""
        # Clear cache
        self.cache.clear()
        
        # Unload all models
        for name in list(self.registry.get_all_models().keys()):
            self.registry.unload_model(name)
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        # Force garbage collection
        gc.collect()
        
        self.logger.info("Model manager cleanup completed")
    
    def __del__(self):
        """تمیز کردن هنگام حذف"""
        try:
            self.cleanup()
        except Exception:
            pass 