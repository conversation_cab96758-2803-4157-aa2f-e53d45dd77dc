import pytest
import datetime
from utils.news_volume_analyzer import NewsVolumeAnalyzer

@pytest.fixture
def analyzer():
    """Create a NewsVolumeAnalyzer with test settings."""
    return NewsVolumeAnalyzer(
        short_window=6,  # 6 hours
        long_window=24,  # 24 hours
        threshold_ratio=3.0,
        min_news_count=3
    )

def test_initialization():
    """Test that the analyzer initializes with correct parameters."""
    analyzer = NewsVolumeAnalyzer(
        short_window=12,
        long_window=48,
        threshold_ratio=2.5,
        min_news_count=5
    )
    
    assert analyzer.short_window == 12
    assert analyzer.long_window == 48
    assert analyzer.threshold_ratio == 2.5
    assert analyzer.min_news_count == 5
    assert isinstance(analyzer.news_timestamps, dict)
    assert isinstance(analyzer.volume_cache, dict)

def test_initialization_invalid_windows():
    """Test that initialization fails when short_window >= long_window."""
    with pytest.raises(ValueError):
        NewsVolumeAnalyzer(short_window=24, long_window=24)
    
    with pytest.raises(ValueError):
        NewsVolumeAnalyzer(short_window=48, long_window=24)

def test_add_news(analyzer):
    """Test adding news items for different assets."""
    now = datetime.datetime.now()
    
    # Add news for BTC
    analyzer.add_news("BTC", now)
    analyzer.add_news("BTC", now - datetime.timedelta(hours=1))
    
    # Add news for ETH
    analyzer.add_news("ETH", now)
    
    # Check that news items were added correctly
    assert len(analyzer.news_timestamps["btc"]) == 2  # Case insensitive
    assert len(analyzer.news_timestamps["eth"]) == 1

def test_clean_old_news(analyzer):
    """Test that old news items are removed."""
    now = datetime.datetime.now()
    
    # Add news items at different times
    analyzer.add_news("BTC", now - datetime.timedelta(hours=30))  # Too old, should be cleaned
    analyzer.add_news("BTC", now - datetime.timedelta(hours=20))  # Within long window
    analyzer.add_news("BTC", now - datetime.timedelta(hours=5))   # Recent
    
    # Clean old news
    analyzer._clean_old_news("BTC", now)
    
    # Only the items within the long window should remain
    assert len(analyzer.news_timestamps["btc"]) == 2

def test_get_volume(analyzer):
    """Test getting volume within a time window."""
    now = datetime.datetime.now()
    
    # Add news items at different times
    analyzer.add_news("BTC", now - datetime.timedelta(hours=10))
    analyzer.add_news("BTC", now - datetime.timedelta(hours=8))
    analyzer.add_news("BTC", now - datetime.timedelta(hours=5))
    analyzer.add_news("BTC", now - datetime.timedelta(hours=2))
    
    # Check volumes for different windows
    assert analyzer.get_volume("BTC", 6, now) == 2  # Last 6 hours
    assert analyzer.get_volume("BTC", 12, now) == 4  # Last 12 hours

def test_no_spike_with_insufficient_news(analyzer):
    """Test that no spike is detected when there's insufficient news volume."""
    now = datetime.datetime.now()
    
    # Add just 2 news items (below min_news_count of 3)
    analyzer.add_news("BTC", now - datetime.timedelta(hours=1))
    analyzer.add_news("BTC", now - datetime.timedelta(hours=2))
    
    is_spike, metrics = analyzer.detect_volume_spike("BTC", now)
    
    assert is_spike is False
    assert metrics["short_volume"] == 2
    assert metrics["ratio"] == 0.0

def test_no_spike_with_normal_distribution(analyzer):
    """Test that no spike is detected when news is evenly distributed."""
    now = datetime.datetime.now()
    
    # Add news evenly distributed over time
    for i in range(24):
        analyzer.add_news("BTC", now - datetime.timedelta(hours=i))
    
    is_spike, metrics = analyzer.detect_volume_spike("BTC", now)
    
    assert is_spike is False
    # With 24 items evenly distributed over 24 hours, we expect ~6-7 items in 6-hour window
    assert 6 <= metrics["short_volume"] <= 7
    assert metrics["ratio"] < analyzer.threshold_ratio

def test_spike_detection(analyzer):
    """Test that a spike is detected when there's a sudden increase in news volume."""
    now = datetime.datetime.now()
    
    # Add background news (1 per 3 hours)
    for i in range(8):
        hours_ago = i * 3
        if hours_ago < 24:  # Within long window
            analyzer.add_news("BTC", now - datetime.timedelta(hours=hours_ago))
    
    # Add spike (10 news items in the last 2 hours)
    for i in range(10):
        hours_ago = i * 0.2  # Spread over 2 hours
        analyzer.add_news("BTC", now - datetime.timedelta(hours=hours_ago))
    
    is_spike, metrics = analyzer.detect_volume_spike("BTC", now)
    
    assert is_spike is True
    assert metrics["short_volume"] >= 10
    assert metrics["ratio"] >= analyzer.threshold_ratio

def test_get_all_assets_with_spikes(analyzer):
    """Test getting all assets with spikes."""
    now = datetime.datetime.now()
    
    # Set up BTC with a spike
    for i in range(10):
        hours_ago = i * 0.2  # Spread over 2 hours
        analyzer.add_news("BTC", now - datetime.timedelta(hours=hours_ago))
    
    # Set up ETH with normal distribution
    for i in range(24):
        analyzer.add_news("ETH", now - datetime.timedelta(hours=i))
    
    # Set up XRP with a spike
    for i in range(8):
        hours_ago = i * 0.3  # Spread over 2.4 hours
        analyzer.add_news("XRP", now - datetime.timedelta(hours=hours_ago))
    
    spikes = analyzer.get_all_assets_with_spikes(now)
    
    # Extract just the asset names for easier assertion
    spike_assets = [asset for asset, _ in spikes]
    
    assert "btc" in spike_assets
    assert "xrp" in spike_assets
    assert "eth" not in spike_assets
    assert len(spike_assets) == 2

def test_volume_cache(analyzer):
    """Test that volume calculations are cached."""
    now = datetime.datetime.now()
    
    # Add some news
    for i in range(5):
        analyzer.add_news("BTC", now - datetime.timedelta(hours=i))
    
    # First call should calculate and cache
    is_spike1, metrics1 = analyzer.detect_volume_spike("BTC", now)
    
    # Check that cache was created
    assert "btc" in analyzer.volume_cache
    assert analyzer.volume_cache["btc"]["timestamp"] == now
    
    # Adding a new item should invalidate cache
    analyzer.add_news("BTC", now)
    assert "btc" not in analyzer.volume_cache
    
    # Calling again should recalculate
    is_spike2, metrics2 = analyzer.detect_volume_spike("BTC", now)
    
    # Results should be different
    assert metrics2["short_volume"] == metrics1["short_volume"] + 1 