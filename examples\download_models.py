import os
import sys
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline

# اضافه کردن مسیر اصلی پروژه به sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.proxy_setup import setup_proxy

def download_models():
    """
    دانلود مدل‌های زبانی مورد نیاز برای sentiment analysis
    """
    # تنظیم پروکسی
    setup_proxy()
    
    # مشخص کردن دستگاه
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")
    
    # لیست مدل‌ها
    models = {
        'en': "ProsusAI/finbert",
        'fa': "m3hrdadfi/albert-fa-base-v2-sentiment-multi"
    }
    
    # دانلود و تست هر مدل
    for lang, model_name in models.items():
        print(f"\nDownloading {lang} model: {model_name}")
        try:
            # دانلود tokenizer و مدل
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForSequenceClassification.from_pretrained(model_name).to(device)
            
            # ایجاد sentiment analysis pipeline
            nlp = pipeline(
                "sentiment-analysis",
                model=model,
                tokenizer=tokenizer,
                device=0 if device == "cuda" else -1
            )
            
            # تست مدل
            if lang == 'en':
                test_text = "The company reported strong earnings, exceeding analyst expectations."
            else:  # fa
                test_text = "سود شرکت بسیار فراتر از انتظارات بازار بود."
            
            result = nlp(test_text)[0]
            print(f"Model test result: {result}")
            print(f"{lang.upper()} model downloaded and tested successfully")
            
        except Exception as e:
            print(f"Error downloading {lang} model: {e}")
    
    print("\nModel download process completed.")

if __name__ == "__main__":
    download_models() 