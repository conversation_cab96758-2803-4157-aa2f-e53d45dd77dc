#!/usr/bin/env python3
"""
�� Main Entry Point - Redirects to main_new.py
نقطه ورود اصلی - هدایت به main_new.py

This file now redirects to main_new.py which contains the complete AI Brain controlled system
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Redirect to main_new.py"""
    try:
        logger.info("🚀 Redirecting to main_new.py (AI Brain controlled system)...")
        
        # Import and run main_new.py
        from main_new import main as new_main
        new_main()
        
    except ImportError as e:
        logger.error(f"❌ Cannot import main_new.py: {e}")
        logger.error("Please ensure main_new.py is available")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Error running main_new.py: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
