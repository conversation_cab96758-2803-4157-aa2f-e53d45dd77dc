"""
🔥 Pearl-3x7B STABLE Ultimate Main
نسخه پایدار برای "پدر بازار در آوردن" 😄

ویژگی‌های پایدار:
- حل مشکلات CUDA/cuDNN
- استفاده از GRU به جای LSTM
- Memory management بهبود یافته
- Error handling پیشرفته
"""

import os
import sys
import time
import warnings
warnings.filterwarnings('ignore')

# Safe environment setup
def setup_stable_environment():
    """راه‌اندازی محیط پایدار"""
    print("🛡️ Setting up stable environment...")
    
    # Disable problematic features
    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
    os.environ['PYDEVD_DISABLE_FILE_VALIDATION'] = '1'
    
    # Force CPU mode to avoid CUDA issues
    os.environ['CUDA_VISIBLE_DEVICES'] = ''
    print("🔄 Forced CPU mode for maximum stability")
    
    return False  # Always use CPU

# Setup stable environment
cuda_available = setup_stable_environment()

# Install packages safely
def install_packages_safely():
    """نصب امن پکیج‌ها"""
    packages = ['torch', 'numpy', 'pandas', 'scikit-learn']
    
    for package in packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} available")
        except ImportError:
            print(f"📦 Installing {package}...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])

install_packages_safely()

# Now import everything
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional

import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error

def stable_market_domination_training():
    """🔥 آموزش پایدار برای تسلط بر بازار"""
    print("🔥 PEARL-3X7B STABLE MARKET DOMINATION TRAINING")
    print("=" * 80)
    print("👑 MISSION: پدر بازار در آوردن (نسخه پایدار)!")
    print("🛡️ MODE: CPU-only for maximum stability")
    print()
    
    # Step 1: Load and enhance data
    print("📋 STEP 1: LOADING YOUR REAL DATA")
    print("=" * 50)
    
    try:
        enhanced_data = load_and_analyze_trading_data()
        print("✅ Your trading data loaded and enhanced!")
        print(f"📊 Enhanced data: {len(enhanced_data)} records, {len(enhanced_data.columns)} features")
        
    except Exception as e:
        print(f"❌ Data loading error: {e}")
        return {"success": False, "error": str(e)}
    
    # Step 2: Train stable models
    print(f"\n📋 STEP 2: TRAINING STABLE MODELS")
    print("=" * 50)
    
    try:
        training_results = {}
        
        # Train Stable GRU (instead of LSTM)
        print("\n📈 Training Stable GRU Model...")
        training_results['gru'] = train_stable_gru(enhanced_data)
        
        # Train Stable Neural Network
        print("\n🧠 Training Stable Neural Network...")
        training_results['nn'] = train_stable_neural_network(enhanced_data)
        
        print("✅ All stable models trained!")
        
    except Exception as e:
        print(f"❌ Training error: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}
    
    # Step 3: Results summary
    print(f"\n📋 STEP 3: RESULTS SUMMARY")
    print("=" * 50)
    
    successful = sum(1 for r in training_results.values() if r.get('success', False))
    print(f"✅ Successfully trained: {successful}/{len(training_results)} models")
    
    for name, result in training_results.items():
        if result.get('success'):
            print(f"   👑 {name.upper()}: {result.get('performance', 'Trained')}")
            if 'backtest_score' in result:
                print(f"      📊 Backtest Score: {result['backtest_score']:.3f}")
    
    print(f"\n🎉 STABLE TRAINING COMPLETED!")
    print(f"💪 Ready to dominate the market with stable models! 😄")
    
    return {"success": True, "results": training_results}

def load_and_analyze_trading_data():
    """بارگذاری و تحلیل دیتاهای معاملاتی"""
    print("📊 Loading your trading data...")
    
    # Try multiple paths
    data_paths = [
        "/content/drive/MyDrive/project2/data_new",
        "/content/drive/MyDrive/data_new",
        "/content/drive/MyDrive/project/data_new"
    ]
    
    symbols_data = {}
    
    for data_path in data_paths:
        if os.path.exists(data_path):
            print(f"✅ Found data at: {data_path}")
            
            # Scan symbol directories
            for symbol_dir in os.listdir(data_path):
                symbol_path = os.path.join(data_path, symbol_dir)
                
                if os.path.isdir(symbol_path):
                    h1_file = os.path.join(symbol_path, "H1.csv")
                    
                    if os.path.exists(h1_file):
                        try:
                            df = pd.read_csv(h1_file)
                            
                            if len(df) > 1000 and all(col in df.columns for col in ['datetime', 'open', 'high', 'low', 'close']):
                                df['datetime'] = pd.to_datetime(df['datetime'])
                                df = df.sort_values('datetime').set_index('datetime')
                                
                                symbols_data[symbol_dir] = df
                                print(f"   ✅ {symbol_dir}: {len(df):,} records")
                                
                        except Exception as e:
                            print(f"   ❌ {symbol_dir}: {e}")
            break
    
    if not symbols_data:
        raise ValueError("No trading data found")
    
    # Select best symbol
    best_symbol = max(symbols_data.items(), key=lambda x: len(x[1]))[0]
    best_data = symbols_data[best_symbol]
    
    print(f"🏆 Selected symbol: {best_symbol} ({len(best_data):,} records)")
    
    # Add enhanced indicators
    enhanced_data = add_stable_indicators(best_data)
    
    return enhanced_data

def add_stable_indicators(df):
    """اضافه کردن اندیکاتورهای پایدار"""
    print("🔧 Adding stable indicators...")
    
    enhanced_df = df.copy()
    
    try:
        # Basic OHLC features
        enhanced_df['hl_pct'] = (enhanced_df['high'] - enhanced_df['low']) / enhanced_df['close']
        enhanced_df['oc_pct'] = (enhanced_df['close'] - enhanced_df['open']) / enhanced_df['open']
        
        # Moving averages
        for period in [5, 10, 20, 50]:
            enhanced_df[f'sma_{period}'] = enhanced_df['close'].rolling(period).mean()
            enhanced_df[f'ema_{period}'] = enhanced_df['close'].ewm(span=period).mean()
        
        # Returns and volatility
        enhanced_df['returns'] = enhanced_df['close'].pct_change()
        enhanced_df['volatility'] = enhanced_df['returns'].rolling(20).std()
        
        # RSI
        delta = enhanced_df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-8)
        enhanced_df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        ema12 = enhanced_df['close'].ewm(span=12).mean()
        ema26 = enhanced_df['close'].ewm(span=26).mean()
        enhanced_df['macd'] = ema12 - ema26
        enhanced_df['macd_signal'] = enhanced_df['macd'].ewm(span=9).mean()
        
        # Bollinger Bands
        sma20 = enhanced_df['close'].rolling(20).mean()
        std20 = enhanced_df['close'].rolling(20).std()
        enhanced_df['bb_upper'] = sma20 + (std20 * 2)
        enhanced_df['bb_lower'] = sma20 - (std20 * 2)
        enhanced_df['bb_position'] = (enhanced_df['close'] - enhanced_df['bb_lower']) / (enhanced_df['bb_upper'] - enhanced_df['bb_lower'])
        
        # ATR
        high_low = enhanced_df['high'] - enhanced_df['low']
        high_close = np.abs(enhanced_df['high'] - enhanced_df['close'].shift())
        low_close = np.abs(enhanced_df['low'] - enhanced_df['close'].shift())
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        enhanced_df['atr'] = true_range.rolling(14).mean()
        
        # Volume indicators (if available)
        if 'volume' in enhanced_df.columns:
            enhanced_df['volume_sma'] = enhanced_df['volume'].rolling(20).mean()
            enhanced_df['volume_ratio'] = enhanced_df['volume'] / (enhanced_df['volume_sma'] + 1e-8)
        
        # Time features
        enhanced_df['hour'] = enhanced_df.index.hour
        enhanced_df['day_of_week'] = enhanced_df.index.dayofweek
        enhanced_df['is_weekend'] = (enhanced_df.index.dayofweek >= 5).astype(int)
        
        # Trend indicators
        enhanced_df['trend_5'] = (enhanced_df['close'] > enhanced_df['sma_5']).astype(int)
        enhanced_df['trend_20'] = (enhanced_df['close'] > enhanced_df['sma_20']).astype(int)
        
        print(f"✅ Added {len(enhanced_df.columns) - len(df.columns)} stable indicators")
        
    except Exception as e:
        print(f"⚠️ Error adding some indicators: {e}")
    
    return enhanced_df

def train_stable_gru(data):
    """آموزش GRU پایدار"""
    print("📈 Training Stable GRU...")
    
    try:
        # Prepare data
        numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
        target_col = 'close'
        feature_cols = [col for col in numeric_cols if col != target_col]
        
        if len(feature_cols) < 5:
            return {"success": False, "error": "Not enough features"}
        
        # Clean data
        clean_data = data[feature_cols + [target_col]].dropna()
        
        if len(clean_data) < 200:
            return {"success": False, "error": "Not enough clean data"}
        
        print(f"   📊 Features: {len(feature_cols)}, Samples: {len(clean_data)}")
        
        # Scale data
        feature_scaler = StandardScaler()
        target_scaler = StandardScaler()
        
        features_scaled = feature_scaler.fit_transform(clean_data[feature_cols])
        target_scaled = target_scaler.fit_transform(clean_data[[target_col]])
        
        # Create sequences
        sequence_length = min(30, len(clean_data) // 10)
        X, y = [], []
        
        for i in range(sequence_length, len(features_scaled)):
            X.append(features_scaled[i-sequence_length:i])
            y.append(target_scaled[i, 0])
        
        X, y = np.array(X), np.array(y)
        
        # Split data
        split_idx = int(0.8 * len(X))
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Convert to tensors (CPU only)
        device = torch.device('cpu')
        X_train = torch.FloatTensor(X_train)
        y_train = torch.FloatTensor(y_train)
        X_test = torch.FloatTensor(X_test)
        y_test = torch.FloatTensor(y_test)
        
        print(f"   🔥 Training on {device} (stable mode)")
        
        # Define stable GRU model
        class StableGRU(nn.Module):
            def __init__(self, input_size, hidden_size=64, num_layers=2):
                super(StableGRU, self).__init__()
                self.gru = nn.GRU(input_size, hidden_size, num_layers, batch_first=True)
                self.fc1 = nn.Linear(hidden_size, 32)
                self.fc2 = nn.Linear(32, 1)
                self.dropout = nn.Dropout(0.2)
                self.relu = nn.ReLU()
            
            def forward(self, x):
                gru_out, _ = self.gru(x)
                out = gru_out[:, -1, :]  # Last time step
                out = self.dropout(self.relu(self.fc1(out)))
                out = self.fc2(out)
                return out
        
        # Initialize model
        model = StableGRU(len(feature_cols))
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        
        print(f"   🧠 Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # Training loop
        num_epochs = 100
        best_loss = float('inf')
        patience = 0
        
        for epoch in range(num_epochs):
            model.train()
            
            # Training
            optimizer.zero_grad()
            outputs = model(X_train)
            loss = criterion(outputs.squeeze(), y_train)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            # Validation
            model.eval()
            with torch.no_grad():
                val_outputs = model(X_test)
                val_loss = criterion(val_outputs.squeeze(), y_test).item()
            
            if val_loss < best_loss:
                best_loss = val_loss
                patience = 0
                torch.save(model.state_dict(), '/content/best_gru.pth')
            else:
                patience += 1
            
            if epoch % 20 == 0:
                print(f"   Epoch {epoch}: Train Loss: {loss.item():.6f}, Val Loss: {val_loss:.6f}")
            
            if patience >= 20:
                break
        
        # Load best model and evaluate
        model.load_state_dict(torch.load('/content/best_gru.pth'))
        model.eval()
        
        with torch.no_grad():
            test_pred = model(X_test)
            test_rmse = torch.sqrt(criterion(test_pred.squeeze(), y_test)).item()
        
        # Simple backtest
        predictions = test_pred.squeeze().numpy()
        targets = y_test.numpy()
        correlation = np.corrcoef(predictions, targets)[0, 1] if len(predictions) > 1 else 0
        backtest_score = max(0, correlation)
        
        print(f"✅ Stable GRU training completed!")
        print(f"   📊 Test RMSE: {test_rmse:.6f}")
        print(f"   📊 Correlation: {correlation:.4f}")
        print(f"   📊 Backtest Score: {backtest_score:.4f}")
        
        return {
            "success": True,
            "model_name": "Stable_GRU",
            "test_rmse": test_rmse,
            "correlation": correlation,
            "backtest_score": backtest_score,
            "performance": f"RMSE: {test_rmse:.6f}, Corr: {correlation:.4f}",
            "features": len(feature_cols),
            "samples": len(clean_data)
        }
        
    except Exception as e:
        print(f"❌ Stable GRU training failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}
