"""
🌐 API Package - Refactored for v2.0
پکیج API - بازسازی شده برای نسخه 2.0

این پکیج شامل API endpoints و realtime dashboard سازگار با معماری جدید است
"""

# Import from core
from core.base import BaseComponent
from core.logger import get_logger
from core.config import get_config
from core.exceptions import TradingSystemError, ValidationError

# Legacy imports - maintained for backward compatibility
try:
    from .endpoints import (
        app,
        create_app,
        APIEndpoints,
        TradingAPI
    )
except ImportError:
    pass

try:
    from .realtime_dashboard import (
        DashboardServer,
        RealtimeDashboard,
        WebSocketManager
    )
except ImportError:
    pass

try:
    from .websocket_handler import (
        WebSocketHandler,
        MessageBroker,
        EventDispatcher
    )
except ImportError:
    pass

# New API classes compatible with v2.0
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import json
import asyncio
from dataclasses import dataclass, asdict

@dataclass
class APIResponse:
    """پاسخ API"""
    success: bool
    data: Any
    message: str
    timestamp: datetime
    request_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل به dictionary"""
        return {
            "success": self.success,
            "data": self.data,
            "message": self.message,
            "timestamp": self.timestamp.isoformat(),
            "request_id": self.request_id
        }
    
    def to_json(self) -> str:
        """تبدیل به JSON"""
        return json.dumps(self.to_dict())

class APIManager(BaseComponent):
    """مدیر API نسخه 2.0"""
    
    def __init__(self, name: str = "api_manager", config: Dict[str, Any] = None):
        super().__init__(name, config)
        
        self.config = config or get_config().api.__dict__
        self.endpoints = {}
        self.middleware = []
        self.websocket_connections = {}
        
        # API settings
        self.host = self.config.get("host", "localhost")
        self.port = self.config.get("port", 8000)
        self.debug = self.config.get("debug", False)
        self.cors_enabled = self.config.get("cors_enabled", True)
        
        # Rate limiting
        self.rate_limit = self.config.get("rate_limit", 100)  # requests per minute
        self.rate_limit_window = self.config.get("rate_limit_window", 60)  # seconds
        
        # Authentication
        self.auth_enabled = self.config.get("auth_enabled", False)
        self.api_keys = self.config.get("api_keys", [])
        
        self.logger = get_logger(__name__)
        self.app = None
        self.server = None
        
        # Initialize BaseComponent status variables
        self._initialized = False
        self._running = False
    
    def initialize(self) -> bool:
        """مقداردهی اولیه"""
        try:
            # Create FastAPI app
            self._create_app()
            
            # Register default endpoints
            self._register_default_endpoints()
            
            # Setup middleware
            self._setup_middleware()
            
            # Setup WebSocket handlers
            self._setup_websockets()
            
            self.logger.info(f"API manager initialized on {self.host}:{self.port}")
            self._initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize API manager: {e}")
            return False
    
    def start(self) -> bool:
        """شروع سرور API"""
        if not self._initialized:
            if not self.initialize():
                return False
        
        try:
            # Start server
            self._start_server()
            
            self._running = True
            self.logger.info(f"API server started on {self.host}:{self.port}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start API server: {e}")
            return False
    
    def stop(self) -> bool:
        """توقف سرور API"""
        try:
            # Stop server
            if self.server:
                self._stop_server()
            
            # Close WebSocket connections
            self._close_websocket_connections()
            
            self._running = False
            self.logger.info("API server stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping API server: {e}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """بررسی سلامت API"""
        return {
            "initialized": self._initialized,
            "running": self._running,
            "host": self.host,
            "port": self.port,
            "endpoints_count": len(self.endpoints),
            "websocket_connections": len(self.websocket_connections),
            "auth_enabled": self.auth_enabled
        }
    
    def register_endpoint(self, path: str, method: str, handler: Callable, **kwargs):
        """ثبت endpoint جدید"""
        endpoint_key = f"{method.upper()}:{path}"
        self.endpoints[endpoint_key] = {
            "path": path,
            "method": method.upper(),
            "handler": handler,
            "kwargs": kwargs
        }
        
        # Register with FastAPI if app exists
        if self.app:
            self._register_fastapi_endpoint(path, method, handler, **kwargs)
        
        self.logger.info(f"Endpoint registered: {method.upper()} {path}")
    
    def add_middleware(self, middleware: Callable):
        """افزودن middleware"""
        self.middleware.append(middleware)
        self.logger.info(f"Middleware added: {middleware.__name__}")
    
    def broadcast_message(self, message: Dict[str, Any], channel: str = "general"):
        """ارسال پیام به تمام کلاینت‌های WebSocket"""
        if not self.websocket_connections:
            return
        
        message_json = json.dumps(message)
        
        for connection_id, connection_info in self.websocket_connections.items():
            if connection_info.get("channel") == channel:
                try:
                    connection_info["websocket"].send_text(message_json)
                except Exception as e:
                    self.logger.error(f"Error sending message to {connection_id}: {e}")
    
    def _create_app(self):
        """ایجاد FastAPI app"""
        try:
            from fastapi import FastAPI
            from fastapi.middleware.cors import CORSMiddleware
            
            self.app = FastAPI(
                title="Trading System API v2.0",
                description="Advanced Trading System API with AI Models",
                version="2.0.0",
                debug=self.debug
            )
            
            # Add CORS middleware
            if self.cors_enabled:
                self.app.add_middleware(
                    CORSMiddleware,
                    allow_origins=["*"],
                    allow_credentials=True,
                    allow_methods=["*"],
                    allow_headers=["*"],
                )
            
        except ImportError:
            self.logger.error("FastAPI not installed. Install with: pip install fastapi uvicorn")
            raise
    
    def _register_default_endpoints(self):
        """ثبت endpoints پیش‌فرض"""
        # Health check endpoint
        self.register_endpoint("/health", "GET", self._health_endpoint)
        
        # System status endpoint
        self.register_endpoint("/status", "GET", self._status_endpoint)
        
        # Models endpoint
        self.register_endpoint("/models", "GET", self._models_endpoint)
        
        # Prediction endpoint
        self.register_endpoint("/predict", "POST", self._predict_endpoint)
        
        # Portfolio endpoint
        self.register_endpoint("/portfolio", "GET", self._portfolio_endpoint)
        
        # Signals endpoint
        self.register_endpoint("/signals", "GET", self._signals_endpoint)
    
    def _setup_middleware(self):
        """تنظیم middleware"""
        # Authentication middleware
        if self.auth_enabled:
            self.add_middleware(self._auth_middleware)
        
        # Rate limiting middleware
        self.add_middleware(self._rate_limit_middleware)
        
        # Logging middleware
        self.add_middleware(self._logging_middleware)
    
    def _setup_websockets(self):
        """تنظیم WebSocket handlers"""
        if self.app:
            @self.app.websocket("/ws/{channel}")
            async def websocket_endpoint(websocket, channel: str):
                await self._handle_websocket(websocket, channel)
    
    def _start_server(self):
        """شروع سرور"""
        try:
            import uvicorn
            
            # Run server in a separate thread
            import threading
            
            def run_server():
                uvicorn.run(
                    self.app,
                    host=self.host,
                    port=self.port,
                    log_level="info" if self.debug else "warning"
                )
            
            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()
            
            self.server = server_thread
            
        except ImportError:
            self.logger.error("uvicorn not installed. Install with: pip install uvicorn")
            raise
    
    def _stop_server(self):
        """توقف سرور"""
        # This is a simplified implementation
        # In production, you'd want more graceful shutdown
        pass
    
    def _close_websocket_connections(self):
        """بستن اتصالات WebSocket"""
        for connection_id, connection_info in self.websocket_connections.items():
            try:
                connection_info["websocket"].close()
            except:
                pass
        
        self.websocket_connections.clear()
    
    def _register_fastapi_endpoint(self, path: str, method: str, handler: Callable, **kwargs):
        """ثبت endpoint با FastAPI"""
        if method.upper() == "GET":
            self.app.get(path, **kwargs)(handler)
        elif method.upper() == "POST":
            self.app.post(path, **kwargs)(handler)
        elif method.upper() == "PUT":
            self.app.put(path, **kwargs)(handler)
        elif method.upper() == "DELETE":
            self.app.delete(path, **kwargs)(handler)
    
    async def _handle_websocket(self, websocket, channel: str):
        """مدیریت اتصال WebSocket"""
        await websocket.accept()
        
        connection_id = f"{channel}_{datetime.now().timestamp()}"
        self.websocket_connections[connection_id] = {
            "websocket": websocket,
            "channel": channel,
            "connected_at": datetime.now()
        }
        
        try:
            while True:
                # Wait for messages
                data = await websocket.receive_text()
                
                # Handle message
                await self._handle_websocket_message(connection_id, data)
                
        except Exception as e:
            self.logger.error(f"WebSocket error: {e}")
        finally:
            # Remove connection
            if connection_id in self.websocket_connections:
                del self.websocket_connections[connection_id]
    
    async def _handle_websocket_message(self, connection_id: str, message: str):
        """مدیریت پیام WebSocket"""
        try:
            data = json.loads(message)
            
            # Handle different message types
            message_type = data.get("type", "unknown")
            
            if message_type == "ping":
                # Send pong response
                response = {"type": "pong", "timestamp": datetime.now().isoformat()}
                await self.websocket_connections[connection_id]["websocket"].send_text(json.dumps(response))
            
            elif message_type == "subscribe":
                # Subscribe to data feed
                channel = data.get("channel", "general")
                self.websocket_connections[connection_id]["channel"] = channel
            
            # Add more message types as needed
            
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON message from {connection_id}")
        except Exception as e:
            self.logger.error(f"Error handling WebSocket message: {e}")
    
    # Default endpoint handlers
    async def _health_endpoint(self):
        """Health check endpoint"""
        return APIResponse(
            success=True,
            data={"status": "healthy"},
            message="API is running",
            timestamp=datetime.now()
        ).to_dict()
    
    async def _status_endpoint(self):
        """System status endpoint"""
        # Get system status from main manager
        try:
            from main_new import system_manager
            status = system_manager.get_system_status()
            
            return APIResponse(
                success=True,
                data=status,
                message="System status retrieved",
                timestamp=datetime.now()
            ).to_dict()
            
        except Exception as e:
            return APIResponse(
                success=False,
                data={},
                message=f"Error getting system status: {e}",
                timestamp=datetime.now()
            ).to_dict()
    
    async def _models_endpoint(self):
        """Models endpoint"""
        try:
            from ai_models import get_available_models
            models = get_available_models()
            
            return APIResponse(
                success=True,
                data={"models": models},
                message="Available models retrieved",
                timestamp=datetime.now()
            ).to_dict()
            
        except Exception as e:
            return APIResponse(
                success=False,
                data={},
                message=f"Error getting models: {e}",
                timestamp=datetime.now()
            ).to_dict()
    
    async def _predict_endpoint(self, request_data: Dict[str, Any]):
        """Prediction endpoint"""
        try:
            model_name = request_data.get("model")
            input_data = request_data.get("data")
            
            if not model_name or not input_data:
                return APIResponse(
                    success=False,
                    data={},
                    message="Missing model or data parameter",
                    timestamp=datetime.now()
                ).to_dict()
            
            # Get model and make prediction
            from ai_models import get_model
            model = get_model(model_name)
            
            if not model:
                return APIResponse(
                    success=False,
                    data={},
                    message=f"Model not found: {model_name}",
                    timestamp=datetime.now()
                ).to_dict()
            
            prediction = model.predict(input_data)
            
            return APIResponse(
                success=True,
                data={"prediction": prediction},
                message="Prediction completed",
                timestamp=datetime.now()
            ).to_dict()
            
        except Exception as e:
            return APIResponse(
                success=False,
                data={},
                message=f"Prediction error: {e}",
                timestamp=datetime.now()
            ).to_dict()
    
    async def _portfolio_endpoint(self):
        """Portfolio endpoint"""
        try:
            # Get portfolio status
            # This would connect to portfolio manager
            portfolio_data = {
                "balance": 10000.0,
                "equity": 10150.0,
                "free_margin": 9500.0,
                "open_positions": 3,
                "total_pnl": 150.0
            }
            
            return APIResponse(
                success=True,
                data=portfolio_data,
                message="Portfolio data retrieved",
                timestamp=datetime.now()
            ).to_dict()
            
        except Exception as e:
            return APIResponse(
                success=False,
                data={},
                message=f"Error getting portfolio: {e}",
                timestamp=datetime.now()
            ).to_dict()
    
    async def _signals_endpoint(self):
        """Signals endpoint"""
        try:
            # Get latest trading signals
            signals = [
                {
                    "symbol": "EURUSD",
                    "signal": "buy",
                    "confidence": 0.85,
                    "timestamp": datetime.now().isoformat()
                },
                {
                    "symbol": "GBPUSD",
                    "signal": "sell",
                    "confidence": 0.72,
                    "timestamp": datetime.now().isoformat()
                }
            ]
            
            return APIResponse(
                success=True,
                data={"signals": signals},
                message="Trading signals retrieved",
                timestamp=datetime.now()
            ).to_dict()
            
        except Exception as e:
            return APIResponse(
                success=False,
                data={},
                message=f"Error getting signals: {e}",
                timestamp=datetime.now()
            ).to_dict()
    
    # Middleware implementations
    async def _auth_middleware(self, request, call_next):
        """Authentication middleware"""
        if self.auth_enabled:
            auth_header = request.headers.get("Authorization")
            
            if not auth_header or not auth_header.startswith("Bearer "):
                return APIResponse(
                    success=False,
                    data={},
                    message="Authentication required",
                    timestamp=datetime.now()
                ).to_dict()
            
            token = auth_header.split(" ")[1]
            
            if token not in self.api_keys:
                return APIResponse(
                    success=False,
                    data={},
                    message="Invalid API key",
                    timestamp=datetime.now()
                ).to_dict()
        
        return await call_next(request)
    
    async def _rate_limit_middleware(self, request, call_next):
        """Rate limiting middleware"""
        # Simple rate limiting implementation
        # In production, you'd use Redis or similar
        client_ip = request.client.host
        
        # Check rate limit
        # This is a simplified implementation
        
        return await call_next(request)
    
    async def _logging_middleware(self, request, call_next):
        """Logging middleware"""
        start_time = datetime.now()
        
        # Log request
        self.logger.info(f"API Request: {request.method} {request.url}")
        
        # Process request
        response = await call_next(request)
        
        # Log response
        process_time = (datetime.now() - start_time).total_seconds()
        self.logger.info(f"API Response: {response.status_code} - {process_time:.3f}s")
        
        return response

# Create global instances
api_manager = APIManager("global_api_manager")

# Export all available functions and classes
__all__ = [
    # New classes
    "APIResponse",
    "APIManager",
    "api_manager",
    
    # Base classes
    "BaseComponent"
]

# Version info
__version__ = "2.0.0"
__author__ = "Trading System Team"

# Initialize API package
def initialize_api_package():
    """مقداردهی اولیه پکیج API"""
    logger = get_logger(__name__)
    
    try:
        # Initialize API manager
        api_manager.initialize()
        
        logger.info("✅ API package initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ API initialization failed: {e}")
        return False

# Auto-initialize when imported
if __name__ != "__main__":
    initialize_api_package()
