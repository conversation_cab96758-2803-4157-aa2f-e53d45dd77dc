{"command": "python utils/multi_step_prediction_fixed.py", "timestamp": "2025-07-08T05:52:02.495352", "execution_time": 4.554729461669922, "return_code": 1, "stdout": "Multi-Step Prediction System Test\n========================================\nSample data created:\n  Records: 500\n  Price range: 1.08517 - 1.11711\n\nTraining models...\n  1h: MSE=0.000010\n  4h: MSE=0.000016\n  12h: MSE=0.000021\n  24h: MSE=0.000042\n\nGenerating predictions...\n", "stderr": "INFO:__main__:Multi-Step Prediction System initialized\nD:\\project\\utils\\multi_step_prediction_fixed.py:420: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n  dates = pd.date_range(start='2023-01-01', periods=500, freq='H')\nINFO:__main__:Multi-Step Predictor initialized for EURUSD\nD:\\project\\utils\\multi_step_prediction_fixed.py:73: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n  features = features.fillna(method='ffill').fillna(0)\nINFO:__main__:Model trained for 1h: MSE=0.000010, MAE=0.002336\nD:\\project\\utils\\multi_step_prediction_fixed.py:73: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n  features = features.fillna(method='ffill').fillna(0)\nINFO:__main__:Model trained for 4h: MSE=0.000016, MAE=0.003363\nD:\\project\\utils\\multi_step_prediction_fixed.py:73: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n  features = features.fillna(method='ffill').fillna(0)\nINFO:__main__:Model trained for 12h: MSE=0.000021, MAE=0.003317\nD:\\project\\utils\\multi_step_prediction_fixed.py:73: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n  features = features.fillna(method='ffill').fillna(0)\nINFO:__main__:Model trained for 24h: MSE=0.000042, MAE=0.005168\nTraceback (most recent call last):\n  File \"D:\\project\\utils\\multi_step_prediction_fixed.py\", line 474, in <module>\n    main() \n  File \"D:\\project\\utils\\multi_step_prediction_fixed.py\", line 457, in main\n    signals = prediction_system.generate_trading_signals(\"EURUSD\", sample_data, current_price)\n  File \"D:\\project\\utils\\multi_step_prediction_fixed.py\", line 364, in generate_trading_signals\n    prediction = self.get_prediction(symbol, data, current_price)\n  File \"D:\\project\\utils\\multi_step_prediction_fixed.py\", line 337, in get_prediction\n    prediction = self.predictors[symbol].predict(data, current_price)\nTypeError: predict() takes 2 positional arguments but 3 were given\n", "success": false}