#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 Model Comparison System
سیستم مقایسه مدل‌ها
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging
import json
from pathlib import Path

# تنظیم logging
logger = logging.getLogger(__name__)

@dataclass
class ModelPerformance:
    """عملکرد مدل"""
    model_name: str
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    auc_score: float
    training_time: float
    prediction_time: float
    memory_usage: float
    additional_metrics: Dict[str, float] = None
    
    def __post_init__(self):
        if self.additional_metrics is None:
            self.additional_metrics = {}

@dataclass
class ComparisonResult:
    """نتیجه مقایسه"""
    best_model: str
    worst_model: str
    performance_ranking: List[Tuple[str, float]]
    detailed_comparison: Dict[str, Dict[str, float]]
    statistical_significance: Dict[str, bool]
    recommendations: List[str]
    comparison_date: datetime

class ModelComparator:
    """مقایسه‌کننده مدل‌ها"""
    
    def __init__(self):
        self.models_performance = {}
        self.comparison_history = []
        self.metrics_weights = {
            'accuracy': 0.3,
            'precision': 0.2,
            'recall': 0.2,
            'f1_score': 0.15,
            'auc_score': 0.15
        }
    
    def add_model_performance(self, performance: ModelPerformance):
        """اضافه کردن عملکرد مدل"""
        self.models_performance[performance.model_name] = performance
        logger.info(f"Added performance data for model: {performance.model_name}")
    
    def compare_models(self, 
                      model_names: Optional[List[str]] = None,
                      metrics: Optional[List[str]] = None) -> ComparisonResult:
        """مقایسه مدل‌ها"""
        
        if model_names is None:
            model_names = list(self.models_performance.keys())
        
        if not model_names:
            raise ValueError("No models available for comparison")
        
        if metrics is None:
            metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'auc_score']
        
        # محاسبه امتیاز کل برای هر مدل
        model_scores = {}
        detailed_comparison = {}
        
        for model_name in model_names:
            if model_name not in self.models_performance:
                logger.warning(f"Model {model_name} not found in performance data")
                continue
            
            performance = self.models_performance[model_name]
            
            # محاسبه امتیاز وزنی
            weighted_score = 0
            model_metrics = {}
            
            for metric in metrics:
                if hasattr(performance, metric):
                    value = getattr(performance, metric)
                    weight = self.metrics_weights.get(metric, 0.1)
                    weighted_score += value * weight
                    model_metrics[metric] = value
                else:
                    logger.warning(f"Metric {metric} not found for model {model_name}")
            
            model_scores[model_name] = weighted_score
            detailed_comparison[model_name] = model_metrics
        
        # رتبه‌بندی مدل‌ها
        performance_ranking = sorted(
            model_scores.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        
        best_model = performance_ranking[0][0] if performance_ranking else None
        worst_model = performance_ranking[-1][0] if performance_ranking else None
        
        # تحلیل معناداری آماری
        statistical_significance = self._calculate_statistical_significance(
            model_names, metrics
        )
        
        # تولید توصیه‌ها
        recommendations = self._generate_recommendations(
            performance_ranking, detailed_comparison
        )
        
        # ایجاد نتیجه مقایسه
        result = ComparisonResult(
            best_model=best_model,
            worst_model=worst_model,
            performance_ranking=performance_ranking,
            detailed_comparison=detailed_comparison,
            statistical_significance=statistical_significance,
            recommendations=recommendations,
            comparison_date=datetime.now()
        )
        
        # ذخیره در تاریخچه
        self.comparison_history.append(result)
        
        return result
    
    def _calculate_statistical_significance(self, 
                                          model_names: List[str], 
                                          metrics: List[str]) -> Dict[str, bool]:
        """محاسبه معناداری آماری"""
        significance = {}
        
        # شبیه‌سازی تست آماری
        for i, model1 in enumerate(model_names):
            for j, model2 in enumerate(model_names[i+1:], i+1):
                comparison_key = f"{model1}_vs_{model2}"
                
                # شبیه‌سازی t-test
                # در پیاده‌سازی واقعی، از scipy.stats استفاده کنید
                p_value = np.random.uniform(0.001, 0.1)  # شبیه‌سازی
                significance[comparison_key] = p_value < 0.05
        
        return significance
    
    def _generate_recommendations(self, 
                                ranking: List[Tuple[str, float]], 
                                detailed: Dict[str, Dict[str, float]]) -> List[str]:
        """تولید توصیه‌ها"""
        recommendations = []
        
        if not ranking:
            return recommendations
        
        best_model, best_score = ranking[0]
        
        # توصیه مدل برتر
        recommendations.append(f"TOP PERFORMING MODEL: {best_model} with score {best_score:.3f}")
        
        # تحلیل نقاط قوت و ضعف
        if best_model in detailed:
            best_metrics = detailed[best_model]
            
            # یافتن بهترین متریک
            best_metric = max(best_metrics.items(), key=lambda x: x[1])
            recommendations.append(f"STRENGTH: {best_model}: {best_metric[0]} = {best_metric[1]:.3f}")
            
            # یافتن ضعیف‌ترین متریک
            worst_metric = min(best_metrics.items(), key=lambda x: x[1])
            if worst_metric[1] < 0.7:  # threshold
                recommendations.append(f"WEAKNESS: {best_model}: {worst_metric[0]} = {worst_metric[1]:.3f}")
        
        # مقایسه با سایر مدل‌ها
        if len(ranking) > 1:
            second_best = ranking[1]
            score_diff = ranking[0][1] - second_best[1]
            
            if score_diff < 0.05:  # تفاوت کم
                recommendations.append(f"SMALL DIFFERENCE: {best_model} vs {second_best[0]} - More investigation needed")
            else:
                recommendations.append(f"SIGNIFICANT DIFFERENCE: {best_model} outperforms {second_best[0]}")
        
        # توصیه‌های عمومی
        accuracy_scores = [detailed[model].get('accuracy', 0) for model in detailed]
        avg_accuracy = np.mean(accuracy_scores) if accuracy_scores else 0
        
        if avg_accuracy < 0.8:
            recommendations.append("LOW AVERAGE ACCURACY - Data or feature engineering needs improvement")
        
        return recommendations
    
    def generate_comparison_report(self, result: ComparisonResult) -> str:
        """تولید گزارش مقایسه"""
        report = f"""
=== MODEL COMPARISON REPORT ===
Date: {result.comparison_date}

TOP PERFORMING MODEL: {result.best_model}
WEAKEST MODEL: {result.worst_model}

PERFORMANCE RANKING:
"""
        
        for i, (model, score) in enumerate(result.performance_ranking, 1):
            report += f"   {i}. {model}: {score:.3f}\n"
        
        report += f"""
DETAILED PERFORMANCE:
"""
        for model, metrics in result.detailed_comparison.items():
            report += f"""
MODEL: {model}:
"""
            for metric, value in metrics.items():
                report += f"   - {metric}: {value:.3f}\n"
        
        report += f"""
RECOMMENDATIONS:
"""
        for i, recommendation in enumerate(result.recommendations, 1):
            report += f"   {i}. {recommendation}\n"
        
        return report
    
    def save_comparison_results(self, result: ComparisonResult, filename: str = None):
        """ذخیره نتایج مقایسه"""
        if filename is None:
            filename = f"model_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # تبدیل به دیکشنری قابل serialize
        result_dict = {
            'best_model': result.best_model,
            'worst_model': result.worst_model,
            'performance_ranking': result.performance_ranking,
            'detailed_comparison': result.detailed_comparison,
            'statistical_significance': result.statistical_significance,
            'recommendations': result.recommendations,
            'comparison_date': result.comparison_date.isoformat()
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result_dict, f, indent=2, ensure_ascii=False)
            logger.info(f"Comparison results saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving comparison results: {e}")
    
    def load_comparison_results(self, filename: str) -> ComparisonResult:
        """بارگذاری نتایج مقایسه"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return ComparisonResult(
                best_model=data['best_model'],
                worst_model=data['worst_model'],
                performance_ranking=data['performance_ranking'],
                detailed_comparison=data['detailed_comparison'],
                statistical_significance=data['statistical_significance'],
                recommendations=data['recommendations'],
                comparison_date=datetime.fromisoformat(data['comparison_date'])
            )
        except Exception as e:
            logger.error(f"Error loading comparison results: {e}")
            raise

class AutoModelSelector:
    """انتخاب‌کننده خودکار مدل"""
    
    def __init__(self, comparator: ModelComparator):
        self.comparator = comparator
        self.selection_criteria = {
            'min_accuracy': 0.7,
            'min_f1_score': 0.65,
            'max_prediction_time': 1.0,  # seconds
            'max_memory_usage': 1000  # MB
        }
    
    def select_best_model(self, 
                         task_requirements: Dict[str, Any] = None) -> Optional[str]:
        """انتخاب بهترین مدل"""
        
        if not self.comparator.models_performance:
            logger.warning("No models available for selection")
            return None
        
        # فیلتر کردن مدل‌ها بر اساس معیارها
        eligible_models = []
        
        for model_name, performance in self.comparator.models_performance.items():
            if self._meets_criteria(performance, task_requirements):
                eligible_models.append(model_name)
        
        if not eligible_models:
            logger.warning("No models meet the selection criteria")
            return None
        
        # مقایسه مدل‌های واجد شرایط
        comparison_result = self.comparator.compare_models(eligible_models)
        
        return comparison_result.best_model
    
    def _meets_criteria(self, 
                       performance: ModelPerformance, 
                       task_requirements: Dict[str, Any] = None) -> bool:
        """بررسی معیارهای انتخاب"""
        
        # معیارهای پایه
        if performance.accuracy < self.selection_criteria['min_accuracy']:
            return False
        
        if performance.f1_score < self.selection_criteria['min_f1_score']:
            return False
        
        if performance.prediction_time > self.selection_criteria['max_prediction_time']:
            return False
        
        if performance.memory_usage > self.selection_criteria['max_memory_usage']:
            return False
        
        # معیارهای خاص تسک
        if task_requirements:
            for key, value in task_requirements.items():
                if hasattr(performance, key):
                    if getattr(performance, key) < value:
                        return False
        
        return True

def create_sample_performances() -> List[ModelPerformance]:
    """ایجاد نمونه عملکرد مدل‌ها"""
    return [
        ModelPerformance(
            model_name="LSTM_Model",
            accuracy=0.85,
            precision=0.82,
            recall=0.88,
            f1_score=0.85,
            auc_score=0.91,
            training_time=120.5,
            prediction_time=0.05,
            memory_usage=256.0
        ),
        ModelPerformance(
            model_name="XGBoost_Model",
            accuracy=0.88,
            precision=0.86,
            recall=0.84,
            f1_score=0.85,
            auc_score=0.92,
            training_time=45.2,
            prediction_time=0.02,
            memory_usage=128.0
        ),
        ModelPerformance(
            model_name="Random_Forest",
            accuracy=0.82,
            precision=0.80,
            recall=0.85,
            f1_score=0.82,
            auc_score=0.88,
            training_time=30.1,
            prediction_time=0.03,
            memory_usage=192.0
        )
    ]

def main():
    """تست سیستم مقایسه"""
    # ایجاد مقایسه‌کننده
    comparator = ModelComparator()
    
    # اضافه کردن عملکرد مدل‌ها
    sample_performances = create_sample_performances()
    for performance in sample_performances:
        comparator.add_model_performance(performance)
    
    # مقایسه مدل‌ها
    result = comparator.compare_models()
    
    # تولید گزارش
    report = comparator.generate_comparison_report(result)
    print(report)
    
    # ذخیره نتایج
    comparator.save_comparison_results(result)
    
    # انتخاب خودکار مدل
    selector = AutoModelSelector(comparator)
    best_model = selector.select_best_model()
    print(f"\nMODEL SELECTED: {best_model}")

if __name__ == "__main__":
    main()
