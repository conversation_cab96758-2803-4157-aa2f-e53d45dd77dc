import os
import sys
import time
import datetime
import subprocess
import logging

# تنظیم لاگر
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"sentiment_tests_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def run_test(test_name=None):
    """
    اجرای تست‌های sentiment_analyzer با لاگ دقیق
    
    Args:
        test_name: نام تست خاص برای اجرا (اختیاری)
    """
    start_time = time.time()
    
    if test_name:
        cmd = f"python -m pytest tests/test_sentiment_analyzer.py::{test_name} -v"
        logger.info(f"اجرای تست {test_name}...")
    else:
        cmd = "python -m pytest tests/test_sentiment_analyzer.py -v"
        logger.info("اجرای تمام تست‌های sentiment_analyzer...")
    
    logger.info(f"دستور: {cmd}")
    
    # اجرای دستور و ثبت خروجی
    process = subprocess.Popen(
        cmd, 
        shell=True, 
        stdout=subprocess.PIPE, 
        stderr=subprocess.PIPE,
        universal_newlines=True
    )
    
    # خواندن خروجی به صورت خط به خط
    stdout_lines = []
    while True:
        line = process.stdout.readline()
        if not line and process.poll() is not None:
            break
        if line:
            line = line.strip()
            stdout_lines.append(line)
            logger.info(f"خروجی: {line}")
    
    # خواندن خطاها
    stderr_lines = []
    for line in process.stderr.readlines():
        line = line.strip()
        stderr_lines.append(line)
        logger.error(f"خطا: {line}")
    
    # انتظار برای اتمام پردازش
    return_code = process.wait()
    
    # ثبت نتیجه
    elapsed_time = time.time() - start_time
    logger.info(f"زمان اجرا: {elapsed_time:.2f} ثانیه")
    logger.info(f"کد بازگشتی: {return_code}")
    
    if return_code == 0:
        logger.info("تست با موفقیت انجام شد.")
    else:
        logger.error("تست با خطا مواجه شد.")
    
    return {
        'return_code': return_code,
        'stdout': stdout_lines,
        'stderr': stderr_lines,
        'elapsed_time': elapsed_time
    }

if __name__ == "__main__":
    logger.info("شروع اجرای تست‌های sentiment_analyzer...")
    
    # اجرای تست language_detection به صورت جداگانه
    logger.info("=== تست language_detection ===")
    result_lang = run_test("test_language_detection")
    
    # اجرای تست error_handling به صورت جداگانه
    logger.info("=== تست error_handling ===")
    result_error = run_test("test_error_handling")
    
    # اجرای تست source_credibility_weighting به صورت جداگانه
    logger.info("=== تست source_credibility_weighting ===")
    result_cred = run_test("test_source_credibility_weighting")
    
    logger.info(f"تمام تست‌ها اجرا شدند. فایل لاگ در {log_file} ذخیره شد.") 