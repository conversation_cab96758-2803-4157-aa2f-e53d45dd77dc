# 📋 لیست مدل‌های در انتظار دانلود و پیاده‌سازی

> **تاریخ بروزرسانی:** {{ current_date }}
> **وضعیت:** مدل‌هایی که هنوز به پروژه اضافه نشده‌اند

## 🚀 مدل‌های اولویت بالا (فوری)

### 1️⃣ مدل‌های Time Series پیشرفته
```
✅ دانلود شده: ProsusAI/finbert, ElKulako/cryptobert, nlpaueb/sec-bert-base
❌ در انتظار:
- amazon/chronos-bolt-base          # 205M - سریع‌ترین Chronos
- amazon/chronos-bolt-small         # 48M - متعادل
- amazon/chronos-t5-large           # 710M - دقیق‌ترین
- google/timesfm-1.0-200m           # Google TimesFM
- Salesforce/moirai-1.0-R-base      # Foundation Model
- ibm-granite/granite-timeseries-ttm-v1  # IBM Granite
```

### 2️⃣ مدل‌های زبان بزرگ مالی
```
❌ در انتظار:
- FinGPT/fingpt-forecaster_dow30_llama2-7b_lora  # پیش‌بینی Dow 30
- bavest/fin-llama-33b-merged                     # Fin-Llama بزرگ
- arcee-ai/Llama-3-SEC-Base                       # Llama-3 SEC
- ChanceFocus/finma-7b-nlp                        # FinMA NLP
- ChanceFocus/finma-7b-full                       # FinMA کامل
```

### 3️⃣ مدل‌های تحلیل احساسات تخصصی
```
✅ دانلود شده: mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis
❌ در انتظار:
- bilalzafar/FinAI-BERT                          # تشخیص AI در گزارش‌ها
- zhayunduo/roberta-base-stocktwits-finetuned    # StockTwits
- cardiffnlp/twitter-roberta-base-sentiment-latest # Twitter Sentiment
- j-hartmann/emotion-english-distilroberta-base   # تحلیل احساسات عمیق
```

### 4️⃣ مدل‌های چندحالته (متن + تصویر + جدول)
```
❌ در انتظار:
- microsoft/layoutlmv3-base                      # بهترین مدل چندحالته
- unstructured-io/layoutlm-invoices             # تحلیل فاکتور
- microsoft/table-transformer-structure-recognition # تشخیص جدول
- google/pix2struct-base                         # تصویر به متن
```

## 🎯 مدل‌های اولویت متوسط (میان‌مدت)

### 5️⃣ مدل‌های خلاصه‌سازی و تحلیل اسناد
```
❌ در انتظار:
- facebook/bart-large-cnn                       # خلاصه‌سازی اخبار
- google/pegasus-xsum                           # خلاصه‌سازی حرفه‌ای
- allenai/longformer-base-4096                  # اسناد طولانی
- microsoft/layoutlmv2-base-uncased             # ساختار اسناد
```

### 6️⃣ مدل‌های Reinforcement Learning
```
❌ در انتظار:
- Andyrasika/a2c-PandaReachDense-v3            # A2C Algorithm
- delmaksym/ppo-Huggy                          # PPO Algorithm
- huggingface-projects/Deep-Reinforcement-Learning-Leaderboard
```

### 7️⃣ مدل‌های کدنویسی و تحلیل الگوریتم
```
❌ در انتظار:
- microsoft/codebert-base                       # تحلیل کد
- huggingface/CodeBERTa-small-v1               # کد BERT
- microsoft/graphcodebert-base                  # Graph Code
```

## 🔬 مدل‌های تحقیقاتی (طولانی‌مدت)

### 8️⃣ مدل‌های تخصصی پیشرفته
```
❌ در انتظار:
- microsoft/prophetnet-large-uncased           # پیش‌بینی روند
- facebook/opt-1.3b                           # تصمیم‌گیری
- EleutherAI/gpt-neo-1.3B                     # استراتژی معاملات
- microsoft/DialoGPT-medium                   # مکالمه مالی
- microsoft/DialoGPT-large                    # مکالمه پیشرفته
```

### 9️⃣ مدل‌های چندزبانه و فارسی
```
❌ در انتظار:
- HooshvareLab/bert-fa-base-uncased           # BERT فارسی
- m3hrdadfi/bert-fa-base-uncased-sentiment-digikala # احساسات فارسی
- sentence-transformers/all-MiniLM-L6-v2      # Embedding چندزبانه
```

### 🔟 مدل‌های آزمایشی و نوآورانه
```
❌ در انتظار:
- google/flan-t5-base                         # Instruction Following
- google/flan-t5-small                        # سبک‌تر
- bert-base-uncased                           # BERT کلاسیک
- roberta-base                                # RoBERTa پایه
- distilbert-base-uncased                     # DistilBERT
- albert-base-v2                              # ALBERT
```

---

## 📊 آمار کلی مدل‌های در انتظار

| دسته‌بندی | تعداد مدل | حجم تقریبی | اولویت |
|-----------|-----------|-------------|---------|
| Time Series | 6 | ~2.5GB | 🔴 فوری |
| LLM مالی | 5 | ~15GB | 🔴 فوری |
| Sentiment | 4 | ~800MB | 🔴 فوری |
| Multimodal | 4 | ~1.2GB | 🟡 متوسط |
| Document Analysis | 4 | ~1.5GB | 🟡 متوسط |
| Reinforcement Learning | 3 | ~500MB | 🟡 متوسط |
| Code Analysis | 3 | ~600MB | 🟡 متوسط |
| Advanced Trading | 5 | ~8GB | 🟢 طولانی‌مدت |
| Multilingual | 3 | ~1GB | 🟢 طولانی‌مدت |
| Experimental | 10 | ~3GB | 🟢 طولانی‌مدت |

**مجموع:** 47 مدل، ~34GB حجم تقریبی

---

## 🚀 توصیه‌های دانلود بر اساس اولویت

### فاز 1 (هفته آینده):
1. `amazon/chronos-bolt-base` - جایگزین Plutus
2. `bilalzafar/FinAI-BERT` - تشخیص AI
3. `microsoft/layoutlmv3-base` - تحلیل اسناد

### فاز 2 (ماه آینده):
1. `FinGPT/fingpt-forecaster_dow30_llama2-7b_lora`
2. `facebook/bart-large-cnn`
3. `google/timesfm-1.0-200m`

### فاز 3 (سه ماه آینده):
1. `bavest/fin-llama-33b-merged`
2. `microsoft/prophetnet-large-uncased`
3. `HooshvareLab/bert-fa-base-uncased`

---

## ⚠️ نکات مهم:

### 🔧 نیازمندی‌های سیستم:
- **GPU:** حداقل 8GB VRAM برای مدل‌های بزرگ
- **RAM:** حداقل 16GB برای مدل‌های متوسط
- **Storage:** 50GB فضای خالی برای کش مدل‌ها

### 🌐 تنظیمات پروکسی:
```python
# از PROXY.json موجود استفاده شود
proxies = {
    "http": "http://127.0.0.1:10809",
    "https": "http://127.0.0.1:10809"
}
```

### 📝 نحوه استفاده:
```bash
# دانلود مرحله‌ای
python download_priority_models.py --phase 1
python download_priority_models.py --phase 2
python download_priority_models.py --phase 3
```

---

## 🎯 هدف نهایی:
ایجاد یک سیستم معاملاتی کامل با بیش از 60 مدل AI تخصصی که قادر به:
- پیش‌بینی دقیق قیمت
- تحلیل احساسات پیشرفته
- تصمیم‌گیری هوشمند
- مدیریت ریسک خودکار
- تحلیل اسناد و گزارش‌ها
- پردازش چندزبانه

**تاریخ هدف تکمیل:** 6 ماه آینده 