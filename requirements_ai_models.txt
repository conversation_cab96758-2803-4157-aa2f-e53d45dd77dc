# 🤖 AI Model Requirements
# نیازمندی‌های مدل‌های هوش مصنوعی

# Core ML Libraries
torch>=2.0.0
transformers>=4.30.0
tokenizers>=0.13.0
accelerate>=0.20.0

# Sentence Transformers
sentence-transformers>=2.2.0

# Time Series Models
chronos-forecasting>=1.0.0

# Additional ML Libraries
numpy>=1.21.0
scipy>=1.7.0
scikit-learn>=1.0.0
pandas>=1.3.0

# Deep Learning
tensorflow>=2.12.0  # Alternative to PyTorch
keras>=2.12.0

# NLP Libraries
nltk>=3.8
spacy>=3.5.0
textblob>=0.17.1

# Financial Data
yfinance>=0.2.0
alpha-vantage>=2.3.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Async and Web
aiohttp>=3.8.0
websockets>=10.0
requests>=2.28.0

# Data Processing
polars>=0.18.0  # Faster than pandas
dask>=2023.1.0  # Parallel computing

# Model Optimization
onnx>=1.14.0
onnxruntime>=1.15.0
tensorrt>=8.6.0  # NVIDIA GPU optimization

# Monitoring
wandb>=0.15.0  # Weights & Biases
mlflow>=2.3.0  # ML experiment tracking

# API & Web
fastapi>=0.95.0
uvicorn>=0.22.0
streamlit>=1.22.0  # Dashboard

# Testing
pytest>=7.3.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0

# Proxy & Network
httpx>=0.24.0
aiofiles>=23.0.0

# Configuration
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Logging
loguru>=0.7.0
structlog>=23.0.0

# Memory Management
psutil>=5.9.0
py-spy>=0.3.0  # Performance profiling

# Security
cryptography>=40.0.0
python-jose>=3.3.0

# Database
aiosqlite>=0.19.0
sqlalchemy>=2.0.0

# Caching
redis>=4.5.0
diskcache>=5.6.0

# Task Queue
celery>=5.2.0
rq>=1.13.0

# Model Serving
bentoml>=1.0.0
seldon-core>=1.17.0

# GPU Support (Optional)
# cuda-python>=12.0.0
# cupy-cuda12x>=12.0.0

# Quantization
bitsandbytes>=0.39.0
auto-gptq>=0.3.0

# Model Compression
optimum>=1.8.0
intel-extension-for-pytorch>=2.0.0  # Intel optimization 