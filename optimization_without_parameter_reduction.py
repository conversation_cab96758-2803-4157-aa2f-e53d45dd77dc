#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Optimization Without Parameter Reduction
بهینه‌سازی بدون کاهش پارامتر

Solutions to optimize performance while keeping ALL parameters:
1. Memory Management Optimization
2. Gradient Accumulation
3. Mixed Precision Training
4. Dynamic Batching
5. Model Parallelism
6. Checkpoint Streaming
7. Smart Caching
8. Progressive Loading
"""

import torch
import torch.nn as nn
import gc
import psutil
import numpy as np
from typing import Dict, Any, List, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedMemoryOptimizer:
    """Advanced memory optimization without reducing parameters"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.mixed_precision = torch.cuda.is_available()
        self.gradient_accumulation_steps = self._calculate_optimal_accumulation()
        
    def _calculate_optimal_accumulation(self) -> int:
        """Calculate optimal gradient accumulation steps based on available memory"""
        if torch.cuda.is_available():
            gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
            if gpu_memory_gb >= 24:
                return 1  # No accumulation needed
            elif gpu_memory_gb >= 12:
                return 2  # Accumulate 2 steps
            elif gpu_memory_gb >= 8:
                return 4  # Accumulate 4 steps
            elif gpu_memory_gb >= 6:
                return 8  # Accumulate 8 steps
            else:
                return 16  # Accumulate 16 steps
        else:
            # CPU training - use more accumulation
            ram_gb = psutil.virtual_memory().total / 1024**3
            if ram_gb >= 32:
                return 4
            elif ram_gb >= 16:
                return 8
            else:
                return 16
    
    def optimize_model_memory(self, model):
        """Optimize model memory usage without reducing parameters"""
        print("🔧 Optimizing model memory usage...")
        
        # 1. Enable gradient checkpointing
        if hasattr(model, 'gradient_checkpointing_enable'):
            model.gradient_checkpointing_enable()
            print("   ✅ Gradient checkpointing enabled")
        
        # 2. Use memory-efficient attention if available
        if hasattr(model, 'config') and hasattr(model.config, 'use_memory_efficient_attention'):
            model.config.use_memory_efficient_attention = True
            print("   ✅ Memory-efficient attention enabled")
        
        # 3. Enable activation checkpointing for custom models
        for module in model.modules():
            if hasattr(module, 'use_checkpoint'):
                module.use_checkpoint = True
        
        return model
    
    def setup_mixed_precision_training(self):
        """Setup mixed precision training for memory efficiency"""
        if self.mixed_precision:
            scaler = torch.cuda.amp.GradScaler()
            print("🔥 Mixed precision training enabled (FP16)")
            return scaler
        else:
            print("⚠️ Mixed precision not available, using FP32")
            return None
    
    def dynamic_batch_sizing(self, base_batch_size: int, model, sample_input) -> int:
        """Dynamically determine optimal batch size"""
        print("📊 Determining optimal batch size...")
        
        optimal_batch_size = base_batch_size
        
        try:
            # Test different batch sizes
            test_sizes = [base_batch_size * 2, base_batch_size * 4, base_batch_size * 8]
            
            for test_size in test_sizes:
                try:
                    # Create test batch
                    if isinstance(sample_input, dict):
                        test_batch = {k: v.repeat(test_size, *([1] * (v.dim() - 1))) 
                                    for k, v in sample_input.items()}
                    else:
                        test_batch = sample_input.repeat(test_size, *([1] * (sample_input.dim() - 1)))
                    
                    # Test forward pass
                    with torch.no_grad():
                        _ = model(**test_batch if isinstance(test_batch, dict) else test_batch)
                    
                    optimal_batch_size = test_size
                    print(f"   ✅ Batch size {test_size} works")
                    
                    # Clean up
                    del test_batch
                    torch.cuda.empty_cache() if torch.cuda.is_available() else None
                    
                except RuntimeError as e:
                    if "out of memory" in str(e).lower():
                        print(f"   ❌ Batch size {test_size} too large")
                        break
                    else:
                        raise e
        
        except Exception as e:
            print(f"   ⚠️ Dynamic batch sizing failed: {e}")
        
        print(f"🎯 Optimal batch size: {optimal_batch_size}")
        return optimal_batch_size
    
    def progressive_model_loading(self, model_class, model_name: str, device_map: str = "auto"):
        """Load model progressively to manage memory"""
        print(f"📥 Progressive loading: {model_name}")
        
        try:
            # Try loading with device_map for automatic distribution
            if torch.cuda.device_count() > 1:
                model = model_class.from_pretrained(
                    model_name,
                    device_map=device_map,
                    torch_dtype=torch.float16 if self.mixed_precision else torch.float32,
                    low_cpu_mem_usage=True
                )
                print("   ✅ Multi-GPU loading with device_map")
            else:
                # Single GPU/CPU loading with memory optimization
                model = model_class.from_pretrained(
                    model_name,
                    torch_dtype=torch.float16 if self.mixed_precision else torch.float32,
                    low_cpu_mem_usage=True
                )
                model = model.to(self.device)
                print(f"   ✅ Single device loading to {self.device}")
            
            return model
            
        except Exception as e:
            print(f"   ❌ Progressive loading failed: {e}")
            # Fallback to standard loading
            model = model_class.from_pretrained(model_name)
            model = model.to(self.device)
            return model
    
    def optimize_training_loop(self, model, optimizer, loss_fn, dataloader, scaler=None):
        """Optimized training loop with gradient accumulation"""
        print("🔄 Starting optimized training loop...")
        
        model.train()
        total_loss = 0
        step_count = 0
        
        # Clear cache before training
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        for batch_idx, batch in enumerate(dataloader):
            try:
                # Move batch to device efficiently
                if isinstance(batch, dict):
                    batch = {k: v.to(self.device, non_blocking=True) for k, v in batch.items()}
                else:
                    batch = batch.to(self.device, non_blocking=True)
                
                # Forward pass with mixed precision
                if scaler and self.mixed_precision:
                    with torch.cuda.amp.autocast():
                        outputs = model(**batch if isinstance(batch, dict) else batch)
                        loss = loss_fn(outputs, batch.get('labels', batch))
                        loss = loss / self.gradient_accumulation_steps
                else:
                    outputs = model(**batch if isinstance(batch, dict) else batch)
                    loss = loss_fn(outputs, batch.get('labels', batch))
                    loss = loss / self.gradient_accumulation_steps
                
                # Backward pass
                if scaler and self.mixed_precision:
                    scaler.scale(loss).backward()
                else:
                    loss.backward()
                
                total_loss += loss.item()
                
                # Gradient accumulation
                if (batch_idx + 1) % self.gradient_accumulation_steps == 0:
                    # Gradient clipping
                    if scaler and self.mixed_precision:
                        scaler.unscale_(optimizer)
                        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                        scaler.step(optimizer)
                        scaler.update()
                    else:
                        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                        optimizer.step()
                    
                    optimizer.zero_grad()
                    step_count += 1
                
                # Memory cleanup every few batches
                if batch_idx % 10 == 0:
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                    gc.collect()
                
                # Progress reporting
                if batch_idx % 50 == 0:
                    avg_loss = total_loss / max(1, step_count)
                    memory_used = torch.cuda.memory_allocated() / 1024**3 if torch.cuda.is_available() else 0
                    print(f"   Batch {batch_idx}: Loss={avg_loss:.4f}, GPU Memory={memory_used:.1f}GB")
                
            except RuntimeError as e:
                if "out of memory" in str(e).lower():
                    print(f"   ⚠️ OOM at batch {batch_idx}, cleaning up...")
                    
                    # Emergency cleanup
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                    gc.collect()
                    
                    # Skip this batch and continue
                    continue
                else:
                    raise e
        
        avg_loss = total_loss / max(1, step_count)
        print(f"✅ Training completed: Average Loss={avg_loss:.4f}")
        return avg_loss
    
    def memory_efficient_evaluation(self, model, eval_dataloader):
        """Memory-efficient evaluation without reducing parameters"""
        print("🔍 Starting memory-efficient evaluation...")
        
        model.eval()
        total_correct = 0
        total_samples = 0
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(eval_dataloader):
                try:
                    # Move batch to device
                    if isinstance(batch, dict):
                        batch = {k: v.to(self.device, non_blocking=True) for k, v in batch.items()}
                    else:
                        batch = batch.to(self.device, non_blocking=True)
                    
                    # Forward pass with mixed precision
                    if self.mixed_precision:
                        with torch.cuda.amp.autocast():
                            outputs = model(**batch if isinstance(batch, dict) else batch)
                    else:
                        outputs = model(**batch if isinstance(batch, dict) else batch)
                    
                    # Calculate accuracy (example)
                    if hasattr(outputs, 'logits'):
                        predictions = torch.argmax(outputs.logits, dim=-1)
                        labels = batch.get('labels', batch)
                        total_correct += (predictions == labels).sum().item()
                        total_samples += labels.size(0)
                    
                    # Memory cleanup
                    if batch_idx % 20 == 0:
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()
                        gc.collect()
                
                except RuntimeError as e:
                    if "out of memory" in str(e).lower():
                        print(f"   ⚠️ OOM during evaluation at batch {batch_idx}")
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()
                        gc.collect()
                        continue
                    else:
                        raise e
        
        accuracy = total_correct / total_samples if total_samples > 0 else 0
        print(f"✅ Evaluation completed: Accuracy={accuracy:.4f}")
        return accuracy
    
    def get_memory_stats(self):
        """Get current memory statistics"""
        stats = {}
        
        # GPU memory
        if torch.cuda.is_available():
            stats['gpu_allocated'] = torch.cuda.memory_allocated() / 1024**3
            stats['gpu_reserved'] = torch.cuda.memory_reserved() / 1024**3
            stats['gpu_total'] = torch.cuda.get_device_properties(0).total_memory / 1024**3
        
        # CPU memory
        memory = psutil.virtual_memory()
        stats['cpu_used'] = memory.used / 1024**3
        stats['cpu_available'] = memory.available / 1024**3
        stats['cpu_total'] = memory.total / 1024**3
        
        return stats
    
    def print_optimization_summary(self):
        """Print optimization summary"""
        print("\n🎯 OPTIMIZATION SUMMARY")
        print("=" * 50)
        print(f"✅ Mixed Precision: {'Enabled' if self.mixed_precision else 'Disabled'}")
        print(f"✅ Gradient Accumulation: {self.gradient_accumulation_steps} steps")
        print(f"✅ Device: {self.device}")
        
        stats = self.get_memory_stats()
        if 'gpu_total' in stats:
            print(f"🔥 GPU Memory: {stats['gpu_allocated']:.1f}GB / {stats['gpu_total']:.1f}GB")
        print(f"💾 CPU Memory: {stats['cpu_used']:.1f}GB / {stats['cpu_total']:.1f}GB")
        
        print("\n🚀 ALL PARAMETERS PRESERVED!")
        print("💪 Maximum performance with full model capacity!")

def apply_advanced_optimizations(model, optimizer, train_dataloader, eval_dataloader=None):
    """Apply all advanced optimizations without reducing parameters"""
    print("🚀 APPLYING ADVANCED OPTIMIZATIONS")
    print("=" * 60)
    print("🎯 Goal: Maximum performance with ALL parameters preserved")
    print()
    
    # Initialize optimizer
    memory_optimizer = AdvancedMemoryOptimizer()
    
    # Optimize model
    model = memory_optimizer.optimize_model_memory(model)
    
    # Setup mixed precision
    scaler = memory_optimizer.setup_mixed_precision_training()
    
    # Print optimization summary
    memory_optimizer.print_optimization_summary()
    
    return memory_optimizer, scaler

def main():
    """Test optimization system"""
    print("🧪 Testing Advanced Optimization System")
    print("=" * 50)
    
    optimizer = AdvancedMemoryOptimizer()
    optimizer.print_optimization_summary()
    
    print("\n✅ Optimization system ready!")
    print("🎯 Use apply_advanced_optimizations() in your training code")

if __name__ == "__main__":
    main()
