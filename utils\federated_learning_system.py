"""
Federated Learning System
سیستم یادگیری فدرال

این سیستم امکان اشتراک یادگیری بین چندین instance را فراهم می‌کند
بدون اشتراک داده‌های خام.
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import logging
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
import pickle
import threading
import time
import random

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelUpdate:
    """به‌روزرسانی مدل"""
    client_id: str
    model_type: str
    weights: Dict[str, float]
    performance_metrics: Dict[str, float]
    timestamp: datetime
    data_size: int
    privacy_hash: str

@dataclass
class FederatedModel:
    """مدل فدرال"""
    model_type: str
    global_weights: Dict[str, float]
    version: int
    participating_clients: List[str]
    last_update: datetime
    performance_history: List[Dict]

class PrivacyPreservingAggregator:
    """تجمیع‌کننده حفظ حریم خصوصی"""
    
    def __init__(self, min_clients: int = 3):
        self.min_clients = min_clients
        self.noise_level = 0.01
        
    def aggregate_weights(self, updates: List[ModelUpdate]) -> Dict[str, float]:
        """تجمیع وزن‌ها با حفظ حریم خصوصی"""
        if len(updates) < self.min_clients:
            logger.warning(f"Not enough clients for aggregation: {len(updates)} < {self.min_clients}")
            return {}
        
        # محاسبه وزن‌های تجمیعی
        aggregated_weights = {}
        total_data_size = sum(update.data_size for update in updates)
        
        for update in updates:
            # وزن بر اساس اندازه داده
            client_weight = update.data_size / total_data_size
            
            for param_name, param_value in update.weights.items():
                if param_name not in aggregated_weights:
                    aggregated_weights[param_name] = 0.0
                
                # اضافه کردن نویز برای حفظ حریم خصوصی
                noisy_value = param_value + np.random.normal(0, self.noise_level)
                aggregated_weights[param_name] += client_weight * noisy_value
        
        return aggregated_weights
    
    def validate_update(self, update: ModelUpdate) -> bool:
        """اعتبارسنجی به‌روزرسانی"""
        # بررسی hash برای تایید صحت
        expected_hash = self._calculate_privacy_hash(update)
        
        if update.privacy_hash != expected_hash:
            logger.warning(f"Invalid privacy hash for client {update.client_id}")
            return False
        
        # بررسی محدوده وزن‌ها
        for param_name, param_value in update.weights.items():
            if abs(param_value) > 10.0:  # محدوده معقول
                logger.warning(f"Suspicious weight value: {param_name}={param_value}")
                return False
        
        return True
    
    def _calculate_privacy_hash(self, update: ModelUpdate) -> str:
        """محاسبه hash حریم خصوصی"""
        # ایجاد hash از وزن‌ها و metadata
        content = f"{update.client_id}_{update.model_type}_{update.timestamp.isoformat()}"
        for param_name, param_value in sorted(update.weights.items()):
            content += f"_{param_name}_{param_value:.6f}"
        
        return hashlib.sha256(content.encode()).hexdigest()[:16]

class FederatedLearningClient:
    """کلاینت یادگیری فدرال"""
    
    def __init__(self, client_id: str, db_path: str = "federated_client.db"):
        self.client_id = client_id
        self.db_path = db_path
        self.local_models = {}
        self.performance_history = []
        
        self._init_database()
        logger.info(f"Federated Learning Client {client_id} initialized")
    
    def _init_database(self):
        """راه‌اندازی پایگاه داده کلاینت"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS local_models (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_type TEXT,
                weights TEXT,
                performance TEXT,
                timestamp TEXT,
                data_size INTEGER
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS federated_updates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_type TEXT,
                global_weights TEXT,
                version INTEGER,
                timestamp TEXT,
                applied BOOLEAN
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def train_local_model(self, model_type: str, training_data: Dict) -> ModelUpdate:
        """آموزش مدل محلی"""
        logger.info(f"Training local model {model_type} for client {self.client_id}")
        
        # شبیه‌سازی آموزش مدل
        if model_type == "RL_Agent":
            weights = self._train_rl_model(training_data)
        elif model_type == "Prediction_Model":
            weights = self._train_prediction_model(training_data)
        else:
            weights = self._train_generic_model(training_data)
        
        # محاسبه متریک‌های عملکرد
        performance_metrics = self._evaluate_local_model(model_type, weights, training_data)
        
        # ایجاد به‌روزرسانی
        update = ModelUpdate(
            client_id=self.client_id,
            model_type=model_type,
            weights=weights,
            performance_metrics=performance_metrics,
            timestamp=datetime.now(),
            data_size=training_data.get('data_size', 1000),
            privacy_hash=""
        )
        
        # محاسبه hash حریم خصوصی
        aggregator = PrivacyPreservingAggregator()
        update.privacy_hash = aggregator._calculate_privacy_hash(update)
        
        # ذخیره مدل محلی
        self._save_local_model(update)
        
        return update
    
    def _train_rl_model(self, training_data: Dict) -> Dict[str, float]:
        """آموزش مدل RL"""
        # شبیه‌سازی وزن‌های RL
        base_performance = training_data.get('base_performance', 0.5)
        noise = np.random.normal(0, 0.1)
        
        return {
            'q_learning_rate': 0.01 + noise * 0.001,
            'exploration_rate': max(0.1, 0.9 - base_performance + noise * 0.1),
            'discount_factor': 0.95 + noise * 0.01,
            'policy_weight': base_performance + noise * 0.1
        }
    
    def _train_prediction_model(self, training_data: Dict) -> Dict[str, float]:
        """آموزش مدل پیش‌بینی"""
        # شبیه‌سازی وزن‌های مدل پیش‌بینی
        market_volatility = training_data.get('market_volatility', 0.5)
        
        return {
            'feature_weight_1': random.uniform(0.1, 0.9),
            'feature_weight_2': random.uniform(0.1, 0.9),
            'bias_term': random.uniform(-0.1, 0.1),
            'volatility_adjustment': market_volatility * 0.5,
            'trend_sensitivity': random.uniform(0.3, 0.7)
        }
    
    def _train_generic_model(self, training_data: Dict) -> Dict[str, float]:
        """آموزش مدل عمومی"""
        return {
            'weight_1': random.uniform(0.1, 0.9),
            'weight_2': random.uniform(0.1, 0.9),
            'bias': random.uniform(-0.1, 0.1)
        }
    
    def _evaluate_local_model(self, model_type: str, weights: Dict, training_data: Dict) -> Dict[str, float]:
        """ارزیابی مدل محلی"""
        # شبیه‌سازی متریک‌های عملکرد
        base_accuracy = random.uniform(0.7, 0.9)
        
        return {
            'accuracy': base_accuracy,
            'loss': 1.0 - base_accuracy,
            'training_time': random.uniform(10, 60),
            'data_quality': training_data.get('data_quality', 0.8)
        }
    
    def _save_local_model(self, update: ModelUpdate):
        """ذخیره مدل محلی"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO local_models 
            (model_type, weights, performance, timestamp, data_size)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            update.model_type,
            json.dumps(update.weights),
            json.dumps(update.performance_metrics),
            update.timestamp.isoformat(),
            update.data_size
        ))
        
        conn.commit()
        conn.close()
    
    def apply_global_update(self, global_weights: Dict[str, float], model_type: str, version: int):
        """اعمال به‌روزرسانی سراسری"""
        logger.info(f"Applying global update for {model_type} v{version}")
        
        # ذخیره به‌روزرسانی سراسری
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO federated_updates 
            (model_type, global_weights, version, timestamp, applied)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            model_type,
            json.dumps(global_weights),
            version,
            datetime.now().isoformat(),
            True
        ))
        
        conn.commit()
        conn.close()
        
        # اعمال وزن‌ها به مدل محلی
        self.local_models[model_type] = global_weights

class FederatedLearningServer:
    """سرور یادگیری فدرال"""
    
    def __init__(self, db_path: str = "federated_server.db"):
        self.db_path = db_path
        self.aggregator = PrivacyPreservingAggregator()
        self.federated_models = {}
        self.pending_updates = {}
        self.round_number = 0
        
        self._init_database()
        logger.info("Federated Learning Server initialized")
    
    def _init_database(self):
        """راه‌اندازی پایگاه داده سرور"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS federated_models (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_type TEXT,
                global_weights TEXT,
                version INTEGER,
                participating_clients TEXT,
                timestamp TEXT,
                performance_metrics TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_updates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                client_id TEXT,
                model_type TEXT,
                weights TEXT,
                performance_metrics TEXT,
                timestamp TEXT,
                round_number INTEGER,
                validated BOOLEAN
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def receive_update(self, update: ModelUpdate) -> bool:
        """دریافت به‌روزرسانی از کلاینت"""
        # اعتبارسنجی به‌روزرسانی
        if not self.aggregator.validate_update(update):
            logger.warning(f"Invalid update from client {update.client_id}")
            return False
        
        # ذخیره به‌روزرسانی
        model_type = update.model_type
        if model_type not in self.pending_updates:
            self.pending_updates[model_type] = []
        
        self.pending_updates[model_type].append(update)
        
        # ذخیره در پایگاه داده
        self._save_client_update(update)
        
        logger.info(f"Received valid update from client {update.client_id} for {model_type}")
        return True
    
    def _save_client_update(self, update: ModelUpdate):
        """ذخیره به‌روزرسانی کلاینت"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO client_updates 
            (client_id, model_type, weights, performance_metrics, timestamp, round_number, validated)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            update.client_id,
            update.model_type,
            json.dumps(update.weights),
            json.dumps(update.performance_metrics),
            update.timestamp.isoformat(),
            self.round_number,
            True
        ))
        
        conn.commit()
        conn.close()
    
    def aggregate_updates(self, model_type: str) -> Optional[FederatedModel]:
        """تجمیع به‌روزرسانی‌ها"""
        if model_type not in self.pending_updates:
            return None
        
        updates = self.pending_updates[model_type]
        if len(updates) < self.aggregator.min_clients:
            logger.info(f"Not enough updates for {model_type}: {len(updates)}")
            return None
        
        # تجمیع وزن‌ها
        global_weights = self.aggregator.aggregate_weights(updates)
        
        if not global_weights:
            return None
        
        # ایجاد مدل فدرال جدید
        participating_clients = [update.client_id for update in updates]
        
        # محاسبه متریک‌های عملکرد تجمیعی
        avg_performance = {}
        for metric in updates[0].performance_metrics.keys():
            avg_performance[metric] = np.mean([
                update.performance_metrics[metric] for update in updates
            ])
        
        if model_type in self.federated_models:
            version = self.federated_models[model_type].version + 1
        else:
            version = 1
        
        federated_model = FederatedModel(
            model_type=model_type,
            global_weights=global_weights,
            version=version,
            participating_clients=participating_clients,
            last_update=datetime.now(),
            performance_history=[avg_performance]
        )
        
        self.federated_models[model_type] = federated_model
        
        # ذخیره مدل فدرال
        self._save_federated_model(federated_model)
        
        # پاک کردن به‌روزرسانی‌های پردازش شده
        self.pending_updates[model_type] = []
        
        logger.info(f"Aggregated {len(updates)} updates for {model_type} v{version}")
        return federated_model
    
    def _save_federated_model(self, model: FederatedModel):
        """ذخیره مدل فدرال"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO federated_models 
            (model_type, global_weights, version, participating_clients, timestamp, performance_metrics)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            model.model_type,
            json.dumps(model.global_weights),
            model.version,
            json.dumps(model.participating_clients),
            model.last_update.isoformat(),
            json.dumps(model.performance_history[-1])
        ))
        
        conn.commit()
        conn.close()
    
    def get_latest_model(self, model_type: str) -> Optional[FederatedModel]:
        """دریافت آخرین مدل فدرال"""
        return self.federated_models.get(model_type)
    
    def get_server_status(self) -> Dict:
        """وضعیت سرور"""
        return {
            'round_number': self.round_number,
            'active_models': list(self.federated_models.keys()),
            'pending_updates': {
                model_type: len(updates) 
                for model_type, updates in self.pending_updates.items()
            },
            'total_models': len(self.federated_models)
        }

def main():
    """تست سیستم یادگیری فدرال"""
    print("Federated Learning System Test")
    print("=" * 40)
    
    # ایجاد سرور
    server = FederatedLearningServer("test_federated_server.db")
    
    # ایجاد کلاینت‌ها
    clients = []
    for i in range(4):
        client = FederatedLearningClient(f"client_{i+1}", f"test_federated_client_{i+1}.db")
        clients.append(client)
    
    print(f"Created server and {len(clients)} clients")
    
    # شبیه‌سازی دور یادگیری فدرال
    for round_num in range(3):
        print(f"\n--- Federated Learning Round {round_num + 1} ---")
        
        # هر کلاینت مدل محلی آموزش می‌دهد
        for i, client in enumerate(clients):
            training_data = {
                'data_size': random.randint(500, 2000),
                'base_performance': random.uniform(0.6, 0.9),
                'market_volatility': random.uniform(0.3, 0.8),
                'data_quality': random.uniform(0.7, 0.95)
            }
            
            # آموزش مدل‌های مختلف
            for model_type in ["RL_Agent", "Prediction_Model"]:
                update = client.train_local_model(model_type, training_data)
                success = server.receive_update(update)
                
                if success:
                    print(f"  Client {i+1} sent {model_type} update (accuracy: {update.performance_metrics['accuracy']:.3f})")
        
        # تجمیع به‌روزرسانی‌ها
        print(f"\n  Aggregating updates...")
        for model_type in ["RL_Agent", "Prediction_Model"]:
            federated_model = server.aggregate_updates(model_type)
            
            if federated_model:
                print(f"    {model_type} v{federated_model.version}: {len(federated_model.participating_clients)} clients")
                
                # ارسال مدل به کلاینت‌ها
                for client in clients:
                    client.apply_global_update(
                        federated_model.global_weights,
                        model_type,
                        federated_model.version
                    )
        
        server.round_number += 1
    
    # وضعیت نهایی
    print(f"\n--- Final Status ---")
    status = server.get_server_status()
    print(f"Total rounds: {status['round_number']}")
    print(f"Active models: {status['active_models']}")
    print(f"Total federated models: {status['total_models']}")
    
    # نمایش مدل‌های نهایی
    for model_type in status['active_models']:
        model = server.get_latest_model(model_type)
        if model:
            print(f"\n{model_type} Final Model:")
            print(f"  Version: {model.version}")
            print(f"  Participating clients: {len(model.participating_clients)}")
            print(f"  Sample weights: {list(model.global_weights.items())[:3]}")
    
    print(f"\n✅ Federated Learning System test completed!")

if __name__ == "__main__":
    main() 