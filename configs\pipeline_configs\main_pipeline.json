{"pipeline_steps": ["sentiment_training", "timeseries_training", "rl_training", "model_comparison"], "parallel_training": false, "save_intermediate_results": true, "final_report_path": "training/pipeline_results.json", "models_to_train": {"sentiment": true, "timeseries": true, "rl": true}, "data_paths": {"sentiment": "data/", "timeseries": "data/", "rl": "data/"}, "training_config": {"max_retries": 3, "retry_delay": 60, "timeout": 3600}, "evaluation_config": {"run_evaluation": true, "save_evaluation_results": true, "evaluation_metrics": ["accuracy", "f1_score", "execution_time", "memory_usage", "interpretability"]}, "output_config": {"save_models": true, "save_metadata": true, "save_logs": true, "create_summary": true}, "performance_config": {"use_gpu": false, "num_workers": 1, "batch_size_override": null, "memory_limit": "4GB"}}