"""
🔧 Simple Sentiment Test
تست ساده sentiment analysis
"""

import os
import sys

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_sentiment():
    """تست ساده sentiment"""
    print("🔧 Testing Simple Sentiment...")
    
    try:
        # Test import
        print("🔄 Importing modules...")
        from ai_models.sentiment_models import LocalHuggingFaceModel
        print("✅ Import successful")
        
        # Test model creation
        print("🔄 Creating model...")
        model = LocalHuggingFaceModel("financial_roberta")
        print("✅ Model created")
        
        # Test model loading
        print("🔄 Loading model...")
        load_success = model.load()
        
        if load_success:
            print("✅ Model loaded successfully")
            
            # Test simple analysis
            print("🔄 Testing analysis...")
            test_text = "The market is good"
            
            # Check pipeline
            print(f"Pipeline type: {type(model.pipeline)}")
            
            # Test pipeline directly
            raw_result = model.pipeline(test_text)
            print(f"Raw pipeline result: {raw_result}")
            print(f"Raw result type: {type(raw_result)}")

            # Test with return_all_scores
            raw_result_all = model.pipeline(test_text, return_all_scores=True)
            print(f"Raw pipeline result (all scores): {raw_result_all}")
            print(f"Raw result all type: {type(raw_result_all)}")

            if isinstance(raw_result, list) and len(raw_result) > 0:
                print(f"First element: {raw_result[0]}")
                print(f"First element type: {type(raw_result[0])}")

            # Test our method (both modes)
            result1 = model.analyze_sentiment(test_text, return_all_scores=False)
            print(f"✅ Analysis (single): {result1.sentiment} ({result1.confidence:.3f})")

            result2 = model.analyze_sentiment(test_text, return_all_scores=True)
            print(f"✅ Analysis (all): {result2.sentiment} ({result2.confidence:.3f})")
            print(f"    Probabilities: {result2.probabilities}")
            
            return True
        else:
            print("❌ Model loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_sentiment()
    print(f"\nResult: {'✅ SUCCESS' if success else '❌ FAILED'}")
