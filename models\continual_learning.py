import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import os
import json
import logging
import copy
from typing import Dict, List, Tuple, Any, Optional, Union
from datetime import datetime
import matplotlib.pyplot as plt
from collections import defaultdict, deque

from .rl_models import RLModelFactory

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EWCLayer:
    """
    Elastic Weight Consolidation (EWC) layer implementation to prevent catastrophic forgetting
    
    This class identifies important model parameters using Fisher information matrix
    and prevents them from changing too much.
    """
    
    def __init__(self, model, ewc_lambda=0.4):
        """
        Initialize EWCLayer
        
        Parameters:
        -----------
        model : RL model
            The main model to prevent forgetting
        ewc_lambda : float
            Importance coefficient for preserving old parameters
        """
        self.model = model
        self.ewc_lambda = ewc_lambda
        self.fisher_information = {}  # Fisher information matrix
        self.optimal_params = {}  # Previous optimal parameters
        self.tasks_seen = 0  # Number of tasks seen
        
    def compute_fisher_information(self, data_loader):
        """
        Compute Fisher information matrix for model parameters
        
        Parameters:
        -----------
        data_loader : training data
            Data used to compute Fisher matrix
        """
        # Store current parameters as optimal parameters
        self.optimal_params = {}
        for name, param in self.model.policy.named_parameters():
            if param.requires_grad:
                self.optimal_params[name] = param.data.clone()
        
        # Initialize Fisher matrix
        fisher_information = {}
        for name, param in self.model.policy.named_parameters():
            if param.requires_grad:
                fisher_information[name] = torch.zeros_like(param.data)
        
        # Compute Fisher matrix using training data
        self.model.policy.eval()  # Set model to evaluation mode
        
        for batch in data_loader:
            # Extract observations from batch
            observations = batch[0]
            
            # Zero gradients
            self.model.policy.optimizer.zero_grad()
            
            # Compute model output
            output = self.model.policy(observations)
            
            # Compute log probabilities of actions
            log_probs = torch.log_softmax(output, dim=1)
            
            # Sample actions based on probabilities
            actions = torch.multinomial(torch.softmax(output, dim=1), 1).detach()
            
            # Compute log probabilities of selected actions
            selected_log_probs = log_probs.gather(1, actions)
            
            # Compute gradients
            selected_log_probs.mean().backward()
            
            # Update Fisher matrix
            for name, param in self.model.policy.named_parameters():
                if param.requires_grad and param.grad is not None:
                    fisher_information[name] += param.grad.data ** 2 / len(data_loader)
        
        # Store Fisher matrix
        if not self.fisher_information:
            # First task
            self.fisher_information = fisher_information
        else:
            # Combine new Fisher matrix with previous one
            for name in self.fisher_information:
                self.fisher_information[name] = (self.fisher_information[name] * self.tasks_seen + 
                                                fisher_information[name]) / (self.tasks_seen + 1)
        
        self.tasks_seen += 1
        self.model.policy.train()  # Return to training mode
        
    def ewc_loss(self):
        """
        Compute EWC loss to prevent forgetting
        
        Returns:
        -------
        torch.Tensor
            EWC loss value
        """
        if not self.optimal_params:
            return torch.tensor(0.0)
        
        loss = torch.tensor(0.0)
        for name, param in self.model.policy.named_parameters():
            if name in self.optimal_params and name in self.fisher_information:
                # Compute difference between current and optimal parameters
                diff = (param - self.optimal_params[name]) ** 2
                # Compute loss considering parameter importance
                loss += (self.fisher_information[name] * diff).sum()
        
        return self.ewc_lambda * loss
    
    def update_model_loss(self, original_loss_fn):
        """
        Update model loss function by adding EWC loss
        
        Parameters:
        -----------
        original_loss_fn : callable
            Original model loss function
        
        Returns:
        -------
        callable
            New loss function with EWC consideration
        """
        def new_loss_fn(*args, **kwargs):
            # Compute original loss
            original_loss = original_loss_fn(*args, **kwargs)
            # Add EWC loss
            ewc_loss = self.ewc_loss()
            # Combine losses
            total_loss = original_loss + ewc_loss
            return total_loss
        
        return new_loss_fn

class ReplayBuffer:
    """
    Experience replay buffer implementation for continual learning
    
    This class stores important experiences from previous tasks and uses them
    in future training to prevent forgetting.
    """
    
    def __init__(self, capacity=10000, importance_sampling=True):
        """
        Initialize ReplayBuffer
        
        Parameters:
        -----------
        capacity : int
            Buffer capacity (maximum number of stored experiences)
        importance_sampling : bool
            Whether to use importance sampling
        """
        self.buffer = deque(maxlen=capacity)
        self.importance_sampling = importance_sampling
        self.priorities = deque(maxlen=capacity)
        self.alpha = 0.6  # Priority coefficient
        self.beta = 0.4  # Importance sampling coefficient
        self.beta_increment = 0.001  # Beta increment rate
        self.epsilon = 1e-6  # Small value to prevent zero priorities
        
    def add(self, experience, priority=None):
        """
        Add an experience to buffer
        
        Parameters:
        -----------
        experience : tuple
            Experience including (observation, action, reward, next_observation, done)
        priority : float, optional
            Experience priority (if None, use maximum current priority)
        """
        if priority is None:
            priority = max(self.priorities) if self.priorities else 1.0
        
        self.buffer.append(experience)
        self.priorities.append(priority)
        
    def sample(self, batch_size):
        """
        Sample from buffer
        
        Parameters:
        -----------
        batch_size : int
            Batch size
        
        Returns:
        -------
        tuple
            Batch of experiences, sampled indices, and sampling weights
        """
        if self.importance_sampling:
            return self._importance_sampling(batch_size)
        else:
            return self._uniform_sampling(batch_size)
        
    def _uniform_sampling(self, batch_size):
        """
        Uniform sampling from buffer
        """
        indices = np.random.choice(len(self.buffer), batch_size, replace=False)
        batch = [self.buffer[i] for i in indices]
        weights = np.ones(batch_size)
        
        return batch, indices, weights
        
    def _importance_sampling(self, batch_size):
        """
        Importance sampling from buffer
        """
        # Compute sampling probabilities based on priorities
        priorities = np.array(self.priorities) ** self.alpha
        probabilities = priorities / np.sum(priorities)
        
        # Sample based on probabilities
        indices = np.random.choice(len(self.buffer), batch_size, replace=False, p=probabilities)
        
        # Compute sampling weights
        weights = (len(self.buffer) * probabilities[indices]) ** (-self.beta)
        weights /= np.max(weights)  # Normalize weights
        
        # Increase beta to converge to uniform sampling
        self.beta = min(1.0, self.beta + self.beta_increment)
        
        # Extract samples
        batch = [self.buffer[i] for i in indices]
        
        return batch, indices, weights
        
    def update_priorities(self, indices, errors):
        """
        Update experience priorities
        
        Parameters:
        -----------
        indices : List[int]
            Experience indices
        errors : List[float]
            TD errors for each experience
        """
        for i, error in zip(indices, errors):
            self.priorities[i] = error + self.epsilon
            
    def __len__(self):
        return len(self.buffer)

class ContinualLearning:
    """
    Main class for implementing continual learning in RL models
    
    This class uses a combination of different methods to prevent forgetting:
    1. Elastic Weight Consolidation (EWC)
    2. Experience Replay
    3. Knowledge Distillation
    """
    
    def __init__(
        self,
        model,
        ewc_lambda=0.4,
        replay_buffer_capacity=10000,
        distillation_temp=2.0,
        distillation_weight=0.5,
        use_ewc=True,
        use_replay=True,
        use_distillation=True
    ):
        """
        Initialize ContinualLearning
        
        Parameters:
        -----------
        model : RL model
            The main model to prevent forgetting
        ewc_lambda : float
            Importance coefficient for preserving old parameters in EWC
        replay_buffer_capacity : int
            Capacity of past experience buffer
        distillation_temp : float
            Knowledge distillation temperature (higher = softer)
        distillation_weight : float
            Weight of distillation loss in total loss
        use_ewc : bool
            Whether to use EWC method
        use_replay : bool
            Whether to use Experience Replay method
        use_distillation : bool
            Whether to use Knowledge Distillation method
        """
        self.model = model
        self.use_ewc = use_ewc
        self.use_replay = use_replay
        self.use_distillation = use_distillation
        self.distillation_temp = distillation_temp
        self.distillation_weight = distillation_weight
        
        # EWC implementation
        if use_ewc:
            self.ewc = EWCLayer(model, ewc_lambda)
        
        # Replay Buffer implementation
        if use_replay:
            self.replay_buffer = ReplayBuffer(capacity=replay_buffer_capacity)
        
        # Knowledge Distillation implementation
        if use_distillation:
            self.teacher_model = None  # Teacher model (previous version)
        
        # Store training history
        self.history = {
            'tasks': [],
            'performance': defaultdict(list),
            'forgetting': defaultdict(list)
        }
        
        # Task counter
        self.task_counter = 0
        
    def prepare_for_new_task(self, task_name=None):
        """
        Prepare for learning a new task
        
        Parameters:
        -----------
        task_name : str, optional
            New task name
        """
        task_id = self.task_counter
        task_name = task_name or f"Task_{task_id}"
        
        logger.info(f"Preparing for new task: {task_name}")
        
        # Store task in history
        self.history['tasks'].append(task_name)
        
        # Store current model version as teacher for knowledge distillation
        if self.use_distillation:
            self.teacher_model = copy.deepcopy(self.model)
            self.teacher_model.policy.eval()  # Set teacher model to evaluation mode
        
        # Compute Fisher matrix for EWC
        if self.use_ewc and hasattr(self, 'data_loader'):
            self.ewc.compute_fisher_information(self.data_loader)
        
        self.task_counter += 1
        
    def set_data_loader(self, data_loader):
        """
        Set training data for computing Fisher matrix
        
        Parameters:
        -----------
        data_loader : training data
        """
        self.data_loader = data_loader
        
    def add_experience(self, experience, priority=None):
        """
        Add an experience to buffer
        
        Parameters:
        -----------
        experience : tuple
            Experience including (observation, action, reward, next_observation, done)
        priority : float, optional
            Experience priority
        """
        if self.use_replay:
            self.replay_buffer.add(experience, priority)
            
    def distillation_loss(self, student_output, teacher_output):
        """
        Compute knowledge distillation loss
        
        Parameters:
        -----------
        student_output : torch.Tensor
            Student model output (current model)
        teacher_output : torch.Tensor
            Teacher model output (previous model)
            
        Returns:
        -------
        torch.Tensor
            Distillation loss value
        """
        if not self.use_distillation or self.teacher_model is None:
            return torch.tensor(0.0)
        
        # Convert outputs to soft log probabilities
        student_log_probs = torch.log_softmax(student_output / self.distillation_temp, dim=1)
        teacher_probs = torch.softmax(teacher_output / self.distillation_temp, dim=1)
        
        # Compute KL-Divergence loss
        loss = nn.KLDivLoss(reduction='batchmean')(student_log_probs, teacher_probs) * (self.distillation_temp ** 2)
        
        return self.distillation_weight * loss
        
    def update_model_loss(self, original_loss_fn):
        """
        Update model loss function by adding continual learning losses
        
        Parameters:
        -----------
        original_loss_fn : callable
            Original model loss function
            
        Returns:
        -------
        callable
            New loss function with continual learning consideration
        """
        def new_loss_fn(model_output, target, observations=None):
            # Compute original loss
            original_loss = original_loss_fn(model_output, target)
            
            # Add EWC loss
            ewc_loss = self.ewc.ewc_loss() if self.use_ewc else torch.tensor(0.0)
            
            # Add knowledge distillation loss
            distillation_loss = torch.tensor(0.0)
            if self.use_distillation and self.teacher_model is not None and observations is not None:
                with torch.no_grad():
                    teacher_output = self.teacher_model.policy(observations)
                distillation_loss = self.distillation_loss(model_output, teacher_output)
            
            # Combine losses
            total_loss = original_loss + ewc_loss + distillation_loss
            
            return total_loss
        
        return new_loss_fn
        
    def train_with_replay(self, batch_size=32, replay_ratio=0.5):
        """
        Train model using past experience buffer
        
        Parameters:
        -----------
        batch_size : int
            Batch size
        replay_ratio : float
            Ratio of past experiences to new experiences
        """
        if not self.use_replay or len(self.replay_buffer) < batch_size:
            return
        
        # Sample from buffer
        batch, indices, weights = self.replay_buffer.sample(batch_size)
        
        # Extract experiences
        observations = torch.stack([torch.tensor(exp[0]) for exp in batch])
        actions = torch.tensor([exp[1] for exp in batch])
        rewards = torch.tensor([exp[2] for exp in batch])
        next_observations = torch.stack([torch.tensor(exp[3]) for exp in batch])
        dones = torch.tensor([exp[4] for exp in batch])
        
        # Train model with past experiences
        # (This part depends on specific model implementation)
        
        # Update priorities based on TD error
        # (TD error calculation depends on specific model implementation)
        # self.replay_buffer.update_priorities(indices, td_errors)
        
    def evaluate_forgetting(self, task_id, performance):
        """
        Evaluate forgetting amount for a specific task
        
        Parameters:
        -----------
        task_id : int
            Task ID
        performance : float
            Current model performance on task
        """
        if task_id >= len(self.history['performance']):
            return 0.0
        
        # Calculate best previous performance on this task
        best_performance = max(self.history['performance'][task_id]) if self.history['performance'][task_id] else 0.0
        
        # Calculate forgetting amount (performance decrease)
        forgetting = max(0.0, best_performance - performance)
        
        # Store performance and forgetting
        self.history['performance'][task_id].append(performance)
        self.history['forgetting'][task_id].append(forgetting)
        
        return forgetting
        
    def plot_forgetting(self):
        """
        Plot forgetting curve for different tasks
        
        Returns:
        -------
        matplotlib.figure.Figure
            Forgetting plot
        """
        plt.figure(figsize=(12, 6))
        
        for task_id, forgetting in self.history['forgetting'].items():
            task_name = self.history['tasks'][task_id] if task_id < len(self.history['tasks']) else f"Task_{task_id}"
            plt.plot(forgetting, label=task_name)
        
        plt.title('Forgetting Curve')
        plt.xlabel('Training Steps')
        plt.ylabel('Forgetting (Performance Decrease)')
        plt.legend()
        plt.grid(True)
        
        return plt.gcf()
        
    def plot_performance(self):
        """
        Plot performance curve for different tasks
        
        Returns:
        -------
        matplotlib.figure.Figure
            Performance plot
        """
        plt.figure(figsize=(12, 6))
        
        for task_id, performance in self.history['performance'].items():
            task_name = self.history['tasks'][task_id] if task_id < len(self.history['tasks']) else f"Task_{task_id}"
            plt.plot(performance, label=task_name)
        
        plt.title('Performance Curve')
        plt.xlabel('Training Steps')
        plt.ylabel('Performance')
        plt.legend()
        plt.grid(True)
        
        return plt.gcf()
        
    def save(self, path):
        """
        Save model and continual learning settings
        
        Parameters:
        -----------
        path : str
            Save path
        """
        # Create directory if needed
        os.makedirs(path, exist_ok=True)
        
        # Save main model
        model_path = os.path.join(path, "model.zip")
        self.model.save(model_path)
        
        # Save settings and history
        config = {
            'use_ewc': self.use_ewc,
            'use_replay': self.use_replay,
            'use_distillation': self.use_distillation,
            'distillation_temp': self.distillation_temp,
            'distillation_weight': self.distillation_weight,
            'task_counter': self.task_counter,
            'history': {
                'tasks': self.history['tasks'],
                'performance': {k: v for k, v in self.history['performance'].items()},
                'forgetting': {k: v for k, v in self.history['forgetting'].items()}
            },
            'timestamp': datetime.now().isoformat()
        }
        
        with open(os.path.join(path, 'continual_learning_config.json'), 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Continual learning model saved to {path}")
        
    @classmethod
    def load(cls, path, model_factory, env):
        """
        Load model and continual learning settings
        
        Parameters:
        -----------
        path : str
            Load path
        model_factory : RLModelFactory
            Factory for creating RL models
        env : RL environment
            Environment used for the model
            
        Returns:
        -------
        ContinualLearning
            Loaded ContinualLearning instance
        """
        config_path = os.path.join(path, 'continual_learning_config.json')
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Continual learning config not found at {config_path}")
        
        # Load settings
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Load model
        model_path = os.path.join(path, "model.zip")
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model not found at {model_path}")
        
        # Extract model type from filename or metadata
        model_type = 'ppo'  # Default
        model = model_factory.load_checkpoint(model_type, env, model_path)
        
        # Create new instance
        continual_learning = cls(
            model=model,
            use_ewc=config.get('use_ewc', True),
            use_replay=config.get('use_replay', True),
            use_distillation=config.get('use_distillation', True),
            distillation_temp=config.get('distillation_temp', 2.0),
            distillation_weight=config.get('distillation_weight', 0.5)
        )
        
        # Restore history
        continual_learning.task_counter = config.get('task_counter', 0)
        continual_learning.history['tasks'] = config.get('history', {}).get('tasks', [])
        
        for k, v in config.get('history', {}).get('performance', {}).items():
            continual_learning.history['performance'][int(k)] = v
            
        for k, v in config.get('history', {}).get('forgetting', {}).items():
            continual_learning.history['forgetting'][int(k)] = v
        
        logger.info(f"Continual learning model loaded from {path}")
        return continual_learning



class ContinualLearningSystem:
    """سیستم یادگیری مداوم"""
    
    def __init__(self):
        self.models = {}
        self.performance_history = {}
        self.adaptation_threshold = 0.1
        
    def add_model(self, name: str, model: Any):
        """اضافه کردن مدل جدید"""
        self.models[name] = model
        self.performance_history[name] = []
        
    def evaluate_model(self, name: str, data: Any) -> float:
        """ارزیابی عملکرد مدل"""
        if name not in self.models:
            return 0.0
        
        # شبیه‌سازی ارزیابی
        performance = 0.8  # نمونه
        self.performance_history[name].append(performance)
        return performance
        
    def adapt_model(self, name: str, new_data: Any):
        """تطبیق مدل با داده‌های جدید"""
        if name in self.models:
            print(f"Adapting model {name} with new data")
            # شبیه‌سازی تطبیق
            return True
        return False
        
    def get_best_model(self) -> str:
        """دریافت بهترین مدل"""
        if not self.performance_history:
            return None
            
        best_model = max(self.performance_history.items(), 
                        key=lambda x: sum(x[1])/len(x[1]) if x[1] else 0)
        return best_model[0]
