#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗄️ Advanced Database Transaction Management
سیستم پیشرفته مدیریت تراکنش‌های دیتابیس
"""

import os
import sys
import time
import threading
import logging
from typing import Dict, List, Optional, Any, Callable, Union, Type
from datetime import datetime, timedelta
from contextlib import contextmanager
from dataclasses import dataclass, field
from enum import Enum
from functools import wraps

# SQLAlchemy imports
from sqlalchemy import create_engine, text, inspect, MetaData, Table, Column, Integer, String, Float, DateTime, Boolean, Text, JSON
from sqlalchemy.orm import sessionmaker, Session, declarative_base, relationship
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError, IntegrityError, OperationalError
from sqlalchemy.engine import Engine
from sqlalchemy.sql import func

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logger = logging.getLogger(__name__)

class TransactionStatus(str, Enum):
    """وضعیت تراکنش"""
    PENDING = "pending"
    COMMITTED = "committed"
    ROLLED_BACK = "rolled_back"
    FAILED = "failed"

class TransactionType(str, Enum):
    """نوع تراکنش"""
    READ = "read"
    WRITE = "write"
    BULK_INSERT = "bulk_insert"
    BULK_UPDATE = "bulk_update"
    BULK_DELETE = "bulk_delete"

@dataclass
class TransactionMetrics:
    """متریک‌های تراکنش"""
    transaction_id: str
    transaction_type: TransactionType
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    status: TransactionStatus = TransactionStatus.PENDING
    affected_rows: int = 0
    error_message: Optional[str] = None
    retry_count: int = 0
    connection_info: Dict[str, Any] = field(default_factory=dict)

class DatabaseConnectionPool:
    """مجموعه اتصالات دیتابیس"""
    
    def __init__(self, connection_string: str, pool_size: int = 10, max_overflow: int = 20):
        self.connection_string = connection_string
        self.pool_size = pool_size
        self.max_overflow = max_overflow
        self.engine = None
        self.SessionLocal = None
        self._lock = threading.Lock()
        self._is_initialized = False
        
    def initialize(self) -> bool:
        """راه‌اندازی pool اتصالات"""
        try:
            with self._lock:
                if self._is_initialized:
                    return True
                
                # Create engine with connection pool
                self.engine = create_engine(
                    self.connection_string,
                    poolclass=QueuePool,
                    pool_size=self.pool_size,
                    max_overflow=self.max_overflow,
                    pool_pre_ping=True,
                    pool_recycle=3600,
                    echo=False
                )
                
                # Create session factory
                self.SessionLocal = sessionmaker(
                    bind=self.engine,
                    autocommit=False,
                    autoflush=False,
                    expire_on_commit=False
                )
                
                # Test connection
                with self.engine.connect() as conn:
                    conn.execute(text("SELECT 1"))
                
                self._is_initialized = True
                logger.info("✅ Database connection pool initialized successfully")
                return True
                
        except Exception as e:
            logger.error(f"❌ Error initializing database connection pool: {e}")
            return False
    
    def get_session(self) -> Session:
        """دریافت session جدید"""
        if not self._is_initialized:
            if not self.initialize():
                raise RuntimeError("Database connection pool not initialized")
        
        return self.SessionLocal()
    
    def get_engine(self) -> Engine:
        """دریافت engine"""
        if not self._is_initialized:
            if not self.initialize():
                raise RuntimeError("Database connection pool not initialized")
        
        return self.engine
    
    def close(self):
        """بستن pool اتصالات"""
        try:
            if self.engine:
                self.engine.dispose()
                logger.info("✅ Database connection pool closed")
        except Exception as e:
            logger.error(f"❌ Error closing database connection pool: {e}")

class TransactionManager:
    """مدیر تراکنش‌ها"""
    
    def __init__(self, connection_pool: DatabaseConnectionPool):
        self.connection_pool = connection_pool
        self.active_transactions: Dict[str, TransactionMetrics] = {}
        self.transaction_history: List[TransactionMetrics] = []
        self._lock = threading.Lock()
        self._transaction_counter = 0
        
    def generate_transaction_id(self) -> str:
        """تولید شناسه تراکنش"""
        with self._lock:
            self._transaction_counter += 1
            return f"txn_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self._transaction_counter:06d}"
    
    @contextmanager
    def transaction(self, transaction_type: TransactionType = TransactionType.WRITE, 
                   timeout: int = 30, retry_count: int = 3):
        """Context manager برای مدیریت تراکنش"""
        transaction_id = self.generate_transaction_id()
        session = None
        
        # Create transaction metrics
        metrics = TransactionMetrics(
            transaction_id=transaction_id,
            transaction_type=transaction_type,
            start_time=datetime.now()
        )
        
        with self._lock:
            self.active_transactions[transaction_id] = metrics
        
        try:
            session = self.connection_pool.get_session()
            
            # Begin transaction
            session.begin()
            
            logger.info(f"🔄 Transaction started: {transaction_id} ({transaction_type.value})")
            
            yield session, metrics
            
            # Commit transaction
            session.commit()
            metrics.status = TransactionStatus.COMMITTED
            metrics.end_time = datetime.now()
            metrics.duration = (metrics.end_time - metrics.start_time).total_seconds()
            
            logger.info(f"✅ Transaction committed: {transaction_id} ({metrics.duration:.3f}s)")
            
        except Exception as e:
            if session:
                session.rollback()
            
            metrics.status = TransactionStatus.ROLLED_BACK
            metrics.end_time = datetime.now()
            metrics.duration = (metrics.end_time - metrics.start_time).total_seconds()
            metrics.error_message = str(e)
            
            logger.error(f"❌ Transaction rolled back: {transaction_id} - {e}")
            
            # Retry logic
            if retry_count > 0 and isinstance(e, OperationalError):
                logger.info(f"🔄 Retrying transaction: {transaction_id}")
                time.sleep(1)  # Wait before retry
                with self.transaction(transaction_type, timeout, retry_count - 1) as (retry_session, retry_metrics):
                    yield retry_session, retry_metrics
            else:
                raise
        
        finally:
            if session:
                session.close()
            
            # Move to history
            with self._lock:
                if transaction_id in self.active_transactions:
                    del self.active_transactions[transaction_id]
                self.transaction_history.append(metrics)
                
                # Keep only last 1000 transactions
                if len(self.transaction_history) > 1000:
                    self.transaction_history = self.transaction_history[-1000:]
    
    def get_transaction_metrics(self, transaction_id: str) -> Optional[TransactionMetrics]:
        """دریافت متریک‌های تراکنش"""
        with self._lock:
            # Check active transactions
            if transaction_id in self.active_transactions:
                return self.active_transactions[transaction_id]
            
            # Check history
            for metrics in self.transaction_history:
                if metrics.transaction_id == transaction_id:
                    return metrics
        
        return None
    
    def get_active_transactions(self) -> List[TransactionMetrics]:
        """دریافت تراکنش‌های فعال"""
        with self._lock:
            return list(self.active_transactions.values())
    
    def get_transaction_statistics(self) -> Dict[str, Any]:
        """دریافت آمار تراکنش‌ها"""
        with self._lock:
            total_transactions = len(self.transaction_history)
            if total_transactions == 0:
                return {}
            
            committed = sum(1 for t in self.transaction_history if t.status == TransactionStatus.COMMITTED)
            rolled_back = sum(1 for t in self.transaction_history if t.status == TransactionStatus.ROLLED_BACK)
            
            durations = [t.duration for t in self.transaction_history if t.duration is not None]
            avg_duration = sum(durations) / len(durations) if durations else 0
            
            return {
                "total_transactions": total_transactions,
                "committed": committed,
                "rolled_back": rolled_back,
                "success_rate": (committed / total_transactions) * 100 if total_transactions > 0 else 0,
                "average_duration": avg_duration,
                "active_transactions": len(self.active_transactions)
            }

# Database Models
Base = declarative_base()

class TradingSignal(Base):
    """جدول سیگنال‌های معاملاتی"""
    __tablename__ = 'trading_signals'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    signal_type = Column(String(10), nullable=False)  # buy, sell, hold
    price = Column(Float, nullable=False)
    timestamp = Column(DateTime, default=func.now(), nullable=False, index=True)
    confidence = Column(Float, default=0.0)
    source = Column(String(50), nullable=False)
    signal_metadata = Column(JSON, nullable=True)
    processed = Column(Boolean, default=False, index=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

class TradingPosition(Base):
    """جدول موقعیت‌های معاملاتی"""
    __tablename__ = 'trading_positions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    position_type = Column(String(10), nullable=False)  # long, short
    entry_price = Column(Float, nullable=False)
    exit_price = Column(Float, nullable=True)
    quantity = Column(Float, nullable=False)
    status = Column(String(20), default='open', nullable=False, index=True)
    profit_loss = Column(Float, default=0.0)
    opened_at = Column(DateTime, default=func.now(), nullable=False)
    closed_at = Column(DateTime, nullable=True)
    position_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

class TradingTransaction(Base):
    """جدول تراکنش‌های معاملاتی"""
    __tablename__ = 'trading_transactions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    transaction_id = Column(String(100), unique=True, nullable=False, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    transaction_type = Column(String(20), nullable=False)  # buy, sell
    price = Column(Float, nullable=False)
    quantity = Column(Float, nullable=False)
    commission = Column(Float, default=0.0)
    total_amount = Column(Float, nullable=False)
    timestamp = Column(DateTime, default=func.now(), nullable=False, index=True)
    status = Column(String(20), default='pending', nullable=False, index=True)
    transaction_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

class DatabaseManager:
    """مدیر کامل دیتابیس"""
    
    def __init__(self, connection_string: str, pool_size: int = 10, max_overflow: int = 20):
        self.connection_pool = DatabaseConnectionPool(connection_string, pool_size, max_overflow)
        self.transaction_manager = TransactionManager(self.connection_pool)
        self.metadata = Base.metadata
        self._is_initialized = False
    
    def initialize(self) -> bool:
        """راه‌اندازی مدیر دیتابیس"""
        try:
            # Initialize connection pool
            if not self.connection_pool.initialize():
                return False
            
            # Create tables
            engine = self.connection_pool.get_engine()
            self.metadata.create_all(engine)
            
            self._is_initialized = True
            logger.info("✅ Database manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error initializing database manager: {e}")
            return False
    
    def get_transaction_manager(self) -> TransactionManager:
        """دریافت مدیر تراکنش‌ها"""
        return self.transaction_manager
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """اجرای کوئری"""
        with self.transaction_manager.transaction(TransactionType.READ) as (session, metrics):
            result = session.execute(text(query), params or {})
            rows = result.fetchall()
            
            # Convert to list of dictionaries
            if rows:
                columns = result.keys()
                return [dict(zip(columns, row)) for row in rows]
            return []
    
    def insert_trading_signal(self, symbol: str, signal_type: str, price: float, 
                            confidence: float = 0.0, source: str = "system", 
                            metadata: Optional[Dict[str, Any]] = None) -> Optional[int]:
        """درج سیگنال معاملاتی"""
        try:
            with self.transaction_manager.transaction(TransactionType.WRITE) as (session, metrics):
                signal = TradingSignal(
                    symbol=symbol,
                    signal_type=signal_type,
                    price=price,
                    confidence=confidence,
                    source=source,
                    signal_metadata=metadata
                )
                
                session.add(signal)
                session.flush()
                
                metrics.affected_rows = 1
                return signal.id
                
        except Exception as e:
            logger.error(f"❌ Error inserting trading signal: {e}")
            return None
    
    def get_trading_signals(self, symbol: Optional[str] = None, 
                           processed: Optional[bool] = None,
                           limit: int = 100) -> List[Dict[str, Any]]:
        """دریافت سیگنال‌های معاملاتی"""
        try:
            with self.transaction_manager.transaction(TransactionType.READ) as (session, metrics):
                query = session.query(TradingSignal)
                
                if symbol:
                    query = query.filter(TradingSignal.symbol == symbol)
                
                if processed is not None:
                    query = query.filter(TradingSignal.processed == processed)
                
                signals = query.order_by(TradingSignal.created_at.desc()).limit(limit).all()
                
                return [
                    {
                        "id": signal.id,
                        "symbol": signal.symbol,
                        "signal_type": signal.signal_type,
                        "price": signal.price,
                        "confidence": signal.confidence,
                        "source": signal.source,
                        "metadata": signal.signal_metadata,
                        "processed": signal.processed,
                        "timestamp": signal.timestamp.isoformat(),
                        "created_at": signal.created_at.isoformat()
                    }
                    for signal in signals
                ]
                
        except Exception as e:
            logger.error(f"❌ Error getting trading signals: {e}")
            return []
    
    def bulk_insert_signals(self, signals: List[Dict[str, Any]]) -> int:
        """درج انبوه سیگنال‌ها"""
        try:
            with self.transaction_manager.transaction(TransactionType.BULK_INSERT) as (session, metrics):
                signal_objects = [
                    TradingSignal(
                        symbol=signal.get('symbol'),
                        signal_type=signal.get('signal_type'),
                        price=signal.get('price'),
                        confidence=signal.get('confidence', 0.0),
                        source=signal.get('source', 'system'),
                        signal_metadata=signal.get('metadata')
                    )
                    for signal in signals
                ]
                
                session.bulk_save_objects(signal_objects)
                session.flush()
                
                metrics.affected_rows = len(signals)
                return len(signals)
                
        except Exception as e:
            logger.error(f"❌ Error bulk inserting signals: {e}")
            return 0
    
    def get_database_statistics(self) -> Dict[str, Any]:
        """دریافت آمار دیتابیس"""
        try:
            with self.transaction_manager.transaction(TransactionType.READ) as (session, metrics):
                stats = {}
                
                # Count tables
                stats['trading_signals_count'] = session.query(TradingSignal).count()
                stats['trading_positions_count'] = session.query(TradingPosition).count()
                stats['trading_transactions_count'] = session.query(TradingTransaction).count()
                
                # Transaction statistics
                stats['transaction_stats'] = self.transaction_manager.get_transaction_statistics()
                
                return stats
                
        except Exception as e:
            logger.error(f"❌ Error getting database statistics: {e}")
            return {}
    
    def cleanup_old_records(self, days: int = 30) -> int:
        """پاک‌سازی رکوردهای قدیمی"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with self.transaction_manager.transaction(TransactionType.BULK_DELETE) as (session, metrics):
                # Delete old processed signals
                deleted_signals = session.query(TradingSignal).filter(
                    TradingSignal.processed == True,
                    TradingSignal.created_at < cutoff_date
                ).delete()
                
                # Delete old closed positions
                deleted_positions = session.query(TradingPosition).filter(
                    TradingPosition.status == 'closed',
                    TradingPosition.closed_at < cutoff_date
                ).delete()
                
                total_deleted = deleted_signals + deleted_positions
                metrics.affected_rows = total_deleted
                
                logger.info(f"✅ Cleaned up {total_deleted} old records")
                return total_deleted
                
        except Exception as e:
            logger.error(f"❌ Error cleaning up old records: {e}")
            return 0
    
    def close(self):
        """بستن مدیر دیتابیس"""
        try:
            self.connection_pool.close()
            logger.info("✅ Database manager closed")
        except Exception as e:
            logger.error(f"❌ Error closing database manager: {e}")

# Decorators for database operations
def with_database_transaction(transaction_type: TransactionType = TransactionType.WRITE):
    """Decorator برای عملیات دیتابیس"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            if not hasattr(self, 'database_manager'):
                raise AttributeError("Object must have database_manager attribute")
            
            with self.database_manager.transaction_manager.transaction(transaction_type) as (session, metrics):
                return func(self, session, *args, **kwargs)
        
        return wrapper
    return decorator

# Global database manager instance
_database_manager: Optional[DatabaseManager] = None

def get_database_manager() -> Optional[DatabaseManager]:
    """دریافت مدیر دیتابیس سراسری"""
    return _database_manager

def initialize_database_manager(connection_string: str, pool_size: int = 10, max_overflow: int = 20) -> bool:
    """راه‌اندازی مدیر دیتابیس سراسری"""
    global _database_manager
    
    try:
        _database_manager = DatabaseManager(connection_string, pool_size, max_overflow)
        return _database_manager.initialize()
    except Exception as e:
        logger.error(f"❌ Error initializing global database manager: {e}")
        return False

def close_database_manager():
    """بستن مدیر دیتابیس سراسری"""
    global _database_manager
    
    if _database_manager:
        _database_manager.close()
        _database_manager = None

if __name__ == "__main__":
    """تست سیستم مدیریت دیتابیس"""
    print("🗄️ Testing Database Transaction Management...")
    
    # Initialize database manager
    connection_string = "sqlite:///test_trading.db"
    db_manager = DatabaseManager(connection_string)
    
    if db_manager.initialize():
        print("✅ Database manager initialized successfully")
        
        # Test inserting signals
        signal_id = db_manager.insert_trading_signal(
            symbol="EURUSD",
            signal_type="buy",
            price=1.1234,
            confidence=0.85,
            source="test_system"
        )
        
        if signal_id:
            print(f"✅ Signal inserted with ID: {signal_id}")
        
        # Test getting signals
        signals = db_manager.get_trading_signals(symbol="EURUSD")
        print(f"✅ Retrieved {len(signals)} signals")
        
        # Test statistics
        stats = db_manager.get_database_statistics()
        print(f"✅ Database statistics: {stats}")
        
        # Close database manager
        db_manager.close()
        
        print("🎉 Database Transaction Management is ready!")
    else:
        print("❌ Failed to initialize database manager") 