"""
🔥 Pearl-3x7B Advanced Model Trainer
مربی مدل‌های پیشرفته با مغز متفکر

ویژگی‌های پیشرفته:
- استفاده از مغز متفکر پیشرفته
- 50+ اندیکاتور تخصصی
- 20+ استراتژی معاملاتی
- تشخیص الگوهای پنهان
- مقابله با حرکات فیک
- آموزش تخصصی برای "پدر بازار در آوردن" 😄
"""

# Install required packages if needed
import subprocess
import sys

def install_if_needed():
    """نصب پکیج‌های مورد نیاز در صورت عدم وجود"""
    try:
        import torch
        import numpy
        import pandas
        import sklearn
    except ImportError as e:
        missing_package = str(e).split("'")[1]
        print(f"Installing {missing_package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", missing_package])

install_if_needed()

import os
import sys
import time
import json
import warnings
warnings.filterwarnings('ignore')

# Core imports
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

# ML imports
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import accuracy_score, f1_score, mean_squared_error

class AdvancedLSTMTrainer:
    """📈 مربی LSTM پیشرفته با مغز متفکر"""
    
    def __init__(self, enhanced_data: pd.DataFrame, brain):
        self.enhanced_data = enhanced_data
        self.brain = brain
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"📈 Advanced LSTM Trainer initialized on {self.device}")
        print(f"📊 Data features: {len(enhanced_data.columns)}")
    
    def train_market_dominating_lstm(self) -> Dict[str, Any]:
        """آموزش LSTM برای تسلط بر بازار 🔥"""
        print("📈 TRAINING MARKET-DOMINATING LSTM")
        print("=" * 50)
        print("🎯 Target: پدر بازار در آوردن!")
        
        try:
            # Brain analysis
            decision = self.brain.analyze_training_situation(self.enhanced_data, 'lstm')
            
            if decision['action'] != 'train_advanced':
                return {"success": False, "error": f"Brain decision: {decision['action']}"}
            
            # Prepare advanced features
            features_data = self._prepare_advanced_features()
            
            if features_data is None:
                return {"success": False, "error": "Feature preparation failed"}
            
            # Create advanced LSTM architecture
            model = self._create_advanced_lstm_architecture(features_data['feature_count'])
            
            # Setup advanced training
            optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-5)
            scheduler = self.brain.setup_continual_learning(model, optimizer)
            criterion = nn.MSELoss()
            
            # Enhanced replay buffer
            replay_buffer = self.brain.memory_manager
            replay_buffer.create_memory_pool('lstm_training', 2000)
            
            print(f"🧠 Model parameters: {sum(p.numel() for p in model.parameters()):,}")
            print(f"🎯 Training with {features_data['feature_count']} advanced features")
            
            # Advanced training loop
            training_results = self._run_advanced_training_loop(
                model, optimizer, scheduler, criterion, features_data
            )
            
            # Advanced backtesting
            backtest_results = self._run_comprehensive_backtest(model, features_data)
            
            # Genetic evolution of hyperparameters
            if len(self.brain.performance_history) > 5:
                evolved_params = self.brain.evolve_hyperparameters(
                    {'lr': 0.001, 'batch_size': 32, 'hidden_size': 256},
                    self.brain.performance_history[-5:]
                )
                print(f"🧬 Evolved parameters: {evolved_params}")
            
            # Save advanced model
            model_save_path = self._save_advanced_model(model, features_data, training_results)
            
            final_performance = training_results['best_performance']
            
            print(f"✅ Market-dominating LSTM training completed!")
            print(f"   🎯 Final Performance: {final_performance:.4f}")
            print(f"   📊 Backtest Score: {backtest_results['score']:.4f}")
            print(f"   💰 Simulated Return: {backtest_results['total_return']:.2%}")
            print(f"   🎯 Win Rate: {backtest_results['win_rate']:.2%}")
            print(f"   📈 Sharpe Ratio: {backtest_results['sharpe_ratio']:.3f}")
            
            return {
                "success": True,
                "model_name": "Market_Dominating_LSTM",
                "training_time_hours": training_results['training_time'] / 3600,
                "final_performance": final_performance,
                "backtest_results": backtest_results,
                "model_path": model_save_path,
                "features_used": features_data['feature_names'],
                "total_features": features_data['feature_count'],
                "training_samples": features_data['train_size'],
                "test_samples": features_data['test_size'],
                "brain_confidence": decision['confidence'],
                "market_domination_score": self._calculate_domination_score(backtest_results)
            }
            
        except Exception as e:
            print(f"❌ Advanced LSTM training failed: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    def _prepare_advanced_features(self) -> Optional[Dict[str, Any]]:
        """آماده‌سازی ویژگی‌های پیشرفته"""
        print("🔧 Preparing advanced features for market domination...")
        
        # Find price column
        price_col = None
        for col in self.enhanced_data.columns:
            if any(keyword in col.lower() for keyword in ['close', 'price', 'value']):
                price_col = col
                break
        
        if price_col is None:
            print("❌ No price column found")
            return None
        
        # Select all numeric features
        numeric_cols = self.enhanced_data.select_dtypes(include=[np.number]).columns.tolist()
        
        # Remove target from features
        feature_cols = [col for col in numeric_cols if col != price_col]
        
        if len(feature_cols) < 10:
            print(f"⚠️ Only {len(feature_cols)} features available")
            return None
        
        # Clean data
        clean_data = self.enhanced_data[feature_cols + [price_col]].dropna()
        
        if len(clean_data) < 200:
            print(f"⚠️ Only {len(clean_data)} clean samples")
            return None
        
        print(f"✅ Prepared {len(feature_cols)} features from {len(clean_data)} samples")
        
        # Advanced feature scaling
        feature_scaler = RobustScaler()  # More robust to outliers
        target_scaler = StandardScaler()
        
        features_scaled = feature_scaler.fit_transform(clean_data[feature_cols])
        target_scaled = target_scaler.fit_transform(clean_data[[price_col]])
        
        # Create sequences for LSTM
        sequence_length = min(60, len(clean_data) // 10)
        X, y = [], []
        
        for i in range(sequence_length, len(features_scaled)):
            X.append(features_scaled[i-sequence_length:i])
            y.append(target_scaled[i, 0])
        
        X, y = np.array(X), np.array(y)
        
        # Advanced train/test split (time-aware)
        split_idx = int(0.8 * len(X))
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Convert to tensors
        X_train = torch.FloatTensor(X_train).to(self.device)
        y_train = torch.FloatTensor(y_train).to(self.device)
        X_test = torch.FloatTensor(X_test).to(self.device)
        y_test = torch.FloatTensor(y_test).to(self.device)
        
        return {
            'X_train': X_train, 'y_train': y_train,
            'X_test': X_test, 'y_test': y_test,
            'feature_scaler': feature_scaler,
            'target_scaler': target_scaler,
            'feature_names': feature_cols,
            'target_name': price_col,
            'sequence_length': sequence_length,
            'feature_count': len(feature_cols),
            'train_size': len(X_train),
            'test_size': len(X_test),
            'original_prices': clean_data[price_col].values
        }
    
    def _create_advanced_lstm_architecture(self, input_size: int) -> nn.Module:
        """ایجاد معماری LSTM پیشرفته"""
        
        class MarketDominatingLSTM(nn.Module):
            """🔥 LSTM برای تسلط بر بازار"""
            
            def __init__(self, input_size, hidden_size=256, num_layers=4, dropout=0.3):
                super(MarketDominatingLSTM, self).__init__()
                self.hidden_size = hidden_size
                self.num_layers = num_layers
                
                # Multi-layer LSTM with residual connections
                self.lstm1 = nn.LSTM(input_size, hidden_size, 1, batch_first=True, dropout=dropout)
                self.lstm2 = nn.LSTM(hidden_size, hidden_size, 1, batch_first=True, dropout=dropout)
                self.lstm3 = nn.LSTM(hidden_size, hidden_size, 1, batch_first=True, dropout=dropout)
                
                # Multi-head attention for pattern recognition
                self.attention = nn.MultiheadAttention(hidden_size, num_heads=8, batch_first=True)
                
                # Advanced feature extraction
                self.feature_extractor = nn.Sequential(
                    nn.Linear(hidden_size, hidden_size // 2),
                    nn.ReLU(),
                    nn.Dropout(dropout),
                    nn.Linear(hidden_size // 2, hidden_size // 4),
                    nn.ReLU(),
                    nn.Dropout(dropout)
                )
                
                # Market pattern recognition layers
                self.pattern_detector = nn.Sequential(
                    nn.Linear(hidden_size // 4, 64),
                    nn.ReLU(),
                    nn.Dropout(dropout),
                    nn.Linear(64, 32),
                    nn.ReLU()
                )
                
                # Final prediction layers
                self.predictor = nn.Sequential(
                    nn.Linear(32, 16),
                    nn.ReLU(),
                    nn.Dropout(dropout / 2),
                    nn.Linear(16, 1)
                )
                
                # Batch normalization
                self.bn1 = nn.BatchNorm1d(hidden_size)
                self.bn2 = nn.BatchNorm1d(hidden_size // 4)
                
                self.dropout = nn.Dropout(dropout)
                
            def forward(self, x):
                batch_size = x.size(0)
                
                # Multi-layer LSTM with residual connections
                lstm1_out, _ = self.lstm1(x)
                lstm2_out, _ = self.lstm2(lstm1_out)
                lstm3_out, _ = self.lstm3(lstm2_out + lstm1_out)  # Residual connection
                
                # Apply attention mechanism
                attn_out, _ = self.attention(lstm3_out, lstm3_out, lstm3_out)
                
                # Combine LSTM and attention outputs
                combined = lstm3_out + attn_out
                
                # Use last time step
                last_output = combined[:, -1, :]
                
                # Batch normalization
                if batch_size > 1:
                    last_output = self.bn1(last_output)
                
                # Feature extraction
                features = self.feature_extractor(last_output)
                
                # Batch normalization
                if batch_size > 1:
                    features = self.bn2(features)
                
                # Pattern detection
                patterns = self.pattern_detector(features)
                
                # Final prediction
                output = self.predictor(patterns)
                
                return output
        
        return MarketDominatingLSTM(input_size).to(self.device)
    
    def _run_advanced_training_loop(self, model, optimizer, scheduler, criterion, features_data) -> Dict[str, Any]:
        """حلقه آموزش پیشرفته"""
        print("🔥 Starting advanced training loop...")
        
        start_time = time.time()
        best_loss = float('inf')
        best_performance = 0.0
        patience_counter = 0
        max_patience = 30
        
        # Enhanced replay buffer
        replay_buffer = self.brain.memory_manager.memory_pools.get('lstm_training', {})
        
        num_epochs = 200
        batch_size = 32
        
        training_history = {
            'train_losses': [],
            'val_losses': [],
            'performances': []
        }
        
        for epoch in range(num_epochs):
            model.train()
            total_loss = 0
            num_batches = 0
            
            # Mini-batch training with advanced techniques
            for i in range(0, len(features_data['X_train']), batch_size):
                batch_X = features_data['X_train'][i:i+batch_size]
                batch_y = features_data['y_train'][i:i+batch_size]
                
                if len(batch_X) < 2:  # Skip small batches for batch norm
                    continue
                
                optimizer.zero_grad()
                
                # Forward pass
                outputs = model(batch_X)
                loss = criterion(outputs.squeeze(), batch_y)
                
                # Backward pass with gradient clipping
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                total_loss += loss.item()
                num_batches += 1
            
            avg_train_loss = total_loss / max(num_batches, 1)
            
            # Validation
            model.eval()
            with torch.no_grad():
                val_outputs = model(features_data['X_test'])
                val_loss = criterion(val_outputs.squeeze(), features_data['y_test']).item()
                
                # Calculate performance metric
                val_predictions = val_outputs.squeeze().cpu().numpy()
                val_targets = features_data['y_test'].cpu().numpy()
                
                # Correlation as performance metric
                correlation = np.corrcoef(val_predictions, val_targets)[0, 1]
                performance = max(0, correlation)  # Ensure non-negative
            
            # Learning rate scheduling
            if hasattr(scheduler, 'step'):
                scheduler.step(val_loss)
            
            # Early stopping with performance tracking
            if performance > best_performance:
                best_performance = performance
                best_loss = val_loss
                patience_counter = 0
                
                # Save best model state
                torch.save(model.state_dict(), '/content/best_advanced_lstm.pth')
                
                # Store in brain memory
                self.brain.performance_history.append(performance)
                
            else:
                patience_counter += 1
            
            # Record training history
            training_history['train_losses'].append(avg_train_loss)
            training_history['val_losses'].append(val_loss)
            training_history['performances'].append(performance)
            
            # Progress reporting
            if epoch % 20 == 0:
                print(f"   Epoch {epoch:3d}: Train Loss: {avg_train_loss:.6f}, "
                      f"Val Loss: {val_loss:.6f}, Performance: {performance:.4f}")
            
            # Early stopping
            if patience_counter >= max_patience:
                print(f"   Early stopping at epoch {epoch}")
                break
            
            # Continual learning adaptation
            if self.brain.continual_learning.adapt_to_new_data(performance):
                print("   🔄 Adapting to new patterns...")
        
        training_time = time.time() - start_time
        
        # Load best model
        model.load_state_dict(torch.load('/content/best_advanced_lstm.pth'))
        
        print(f"✅ Advanced training completed in {training_time/3600:.2f} hours")
        print(f"   Best Performance: {best_performance:.4f}")
        
        return {
            'training_time': training_time,
            'best_performance': best_performance,
            'best_loss': best_loss,
            'total_epochs': epoch + 1,
            'training_history': training_history
        }

    def _run_comprehensive_backtest(self, model, features_data) -> Dict[str, float]:
        """اجرای بک‌تست جامع"""
        print("📊 Running comprehensive backtest...")

        # Prepare test data for backtesting
        test_predictions = []
        test_targets = []

        model.eval()
        with torch.no_grad():
            # Get predictions in batches
            batch_size = 32
            for i in range(0, len(features_data['X_test']), batch_size):
                batch_X = features_data['X_test'][i:i+batch_size]
                batch_outputs = model(batch_X)
                test_predictions.extend(batch_outputs.squeeze().cpu().numpy())

            test_targets = features_data['y_test'].cpu().numpy()

        # Convert back to original scale
        test_predictions = np.array(test_predictions).reshape(-1, 1)
        test_targets = test_targets.reshape(-1, 1)

        pred_prices = features_data['target_scaler'].inverse_transform(test_predictions).flatten()
        true_prices = features_data['target_scaler'].inverse_transform(test_targets).flatten()

        # Advanced backtesting with brain
        backtest_results = self.brain.run_advanced_backtest(
            model, features_data['X_test'].cpu().numpy(), true_prices
        )

        # Additional performance metrics
        mse = mean_squared_error(true_prices, pred_prices)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(true_prices - pred_prices))
        mape = np.mean(np.abs((true_prices - pred_prices) / true_prices)) * 100

        # Directional accuracy
        true_direction = np.sign(np.diff(true_prices))
        pred_direction = np.sign(np.diff(pred_prices))
        directional_accuracy = np.mean(true_direction == pred_direction)

        backtest_results.update({
            'rmse': rmse,
            'mae': mae,
            'mape': mape,
            'directional_accuracy': directional_accuracy,
            'correlation': np.corrcoef(true_prices, pred_prices)[0, 1]
        })

        return backtest_results

    def _calculate_domination_score(self, backtest_results: Dict[str, float]) -> float:
        """محاسبه امتیاز تسلط بر بازار 🔥"""

        # Weighted scoring system
        score = 0.0

        # Trading performance (40%)
        if 'total_return' in backtest_results:
            return_score = min(backtest_results['total_return'], 0.5) / 0.5  # Cap at 50%
            score += return_score * 0.4

        # Win rate (25%)
        if 'win_rate' in backtest_results:
            score += backtest_results['win_rate'] * 0.25

        # Sharpe ratio (20%)
        if 'sharpe_ratio' in backtest_results:
            sharpe_score = min(abs(backtest_results['sharpe_ratio']), 3.0) / 3.0
            score += sharpe_score * 0.2

        # Directional accuracy (15%)
        if 'directional_accuracy' in backtest_results:
            score += backtest_results['directional_accuracy'] * 0.15

        return min(score, 1.0)

    def _save_advanced_model(self, model, features_data, training_results) -> str:
        """ذخیره مدل پیشرفته"""
        model_save_path = f"/content/models/market_dominating_lstm_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(model_save_path, exist_ok=True)

        # Save model and all components
        torch.save({
            'model_state_dict': model.state_dict(),
            'feature_scaler': features_data['feature_scaler'],
            'target_scaler': features_data['target_scaler'],
            'feature_names': features_data['feature_names'],
            'target_name': features_data['target_name'],
            'sequence_length': features_data['sequence_length'],
            'feature_count': features_data['feature_count'],
            'training_results': training_results,
            'model_architecture': 'MarketDominatingLSTM',
            'brain_enhanced': True
        }, f"{model_save_path}/advanced_lstm_model.pth")

        return model_save_path

class AdvancedDQNTrainer:
    """🤖 مربی DQN پیشرفته برای تسلط بر بازار"""

    def __init__(self, enhanced_data: pd.DataFrame, brain):
        self.enhanced_data = enhanced_data
        self.brain = brain
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🤖 Advanced DQN Trainer initialized on {self.device}")

    def train_market_dominating_dqn(self) -> Dict[str, Any]:
        """آموزش DQN برای تسلط بر بازار"""
        print("🤖 TRAINING MARKET-DOMINATING DQN")
        print("=" * 50)
        print("🎯 Target: شکست دادن مارکت میکرها!")

        try:
            # Brain analysis
            decision = self.brain.analyze_training_situation(self.enhanced_data, 'dqn')

            if decision['action'] != 'train_advanced':
                return {"success": False, "error": f"Brain decision: {decision['action']}"}

            # Prepare trading environment
            trading_env = self._create_advanced_trading_environment()

            if trading_env is None:
                return {"success": False, "error": "Trading environment creation failed"}

            # Create advanced DQN architecture
            main_network, target_network = self._create_advanced_dqn_architecture(trading_env['state_size'])

            # Setup advanced training components
            optimizer = optim.AdamW(main_network.parameters(), lr=0.0005, weight_decay=1e-5)

            # Enhanced replay buffer with prioritization
            from advanced_brain_trainer import EnhancedReplayBuffer
            replay_buffer = EnhancedReplayBuffer(capacity=100000, alpha=0.6)

            print(f"🧠 Network parameters: {sum(p.numel() for p in main_network.parameters()):,}")
            print(f"🎯 Training environment: {trading_env['state_size']} features")

            # Advanced training loop
            training_results = self._run_advanced_dqn_training(
                main_network, target_network, optimizer, replay_buffer, trading_env
            )

            # Advanced backtesting
            backtest_results = self._run_trading_backtest(main_network, trading_env)

            # Save advanced model
            model_save_path = self._save_advanced_dqn_model(
                main_network, target_network, trading_env, training_results
            )

            final_reward = training_results['final_avg_reward']

            print(f"✅ Market-dominating DQN training completed!")
            print(f"   🎯 Final Avg Reward: {final_reward:.2f}")
            print(f"   📊 Backtest Score: {backtest_results['score']:.4f}")
            print(f"   💰 Trading Return: {backtest_results['total_return']:.2%}")
            print(f"   🎯 Win Rate: {backtest_results['win_rate']:.2%}")

            return {
                "success": True,
                "model_name": "Market_Dominating_DQN",
                "training_time_hours": training_results['training_time'] / 3600,
                "final_avg_reward": final_reward,
                "max_reward": training_results['max_reward'],
                "backtest_results": backtest_results,
                "model_path": model_save_path,
                "total_episodes": training_results['total_episodes'],
                "brain_confidence": decision['confidence'],
                "market_domination_score": self._calculate_trading_domination_score(backtest_results)
            }

        except Exception as e:
            print(f"❌ Advanced DQN training failed: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}

    def _create_advanced_trading_environment(self) -> Optional[Dict[str, Any]]:
        """ایجاد محیط معاملاتی پیشرفته"""
        print("🔧 Creating advanced trading environment...")

        # Find price column
        price_col = None
        for col in self.enhanced_data.columns:
            if any(keyword in col.lower() for keyword in ['close', 'price', 'value']):
                price_col = col
                break

        if price_col is None:
            return None

        # Select all numeric features except price
        numeric_cols = self.enhanced_data.select_dtypes(include=[np.number]).columns.tolist()
        feature_cols = [col for col in numeric_cols if col != price_col]

        if len(feature_cols) < 20:
            print(f"⚠️ Only {len(feature_cols)} features for trading environment")
            return None

        # Clean data
        clean_data = self.enhanced_data[feature_cols + [price_col]].dropna()

        if len(clean_data) < 500:
            print(f"⚠️ Only {len(clean_data)} samples for trading")
            return None

        # Prepare trading data
        features = []
        rewards = []
        actions = []
        prices = clean_data[price_col].values

        lookback = 30  # State window

        for i in range(lookback, len(clean_data) - 1):
            # State: normalized features from lookback window
            state_data = clean_data.iloc[i-lookback:i][feature_cols]

            # Advanced normalization
            state_normalized = (state_data - state_data.mean()) / (state_data.std() + 1e-8)

            # Add market regime indicators
            price_trend = (prices[i] - prices[i-10]) / prices[i-10]
            volatility = np.std(prices[i-10:i]) / prices[i]
            volume_trend = 0  # Placeholder

            # Combine features
            state_vector = np.concatenate([
                state_normalized.values.flatten(),
                [price_trend, volatility, volume_trend]
            ])

            features.append(state_vector)

            # Advanced reward calculation
            current_price = prices[i]
            next_price = prices[i + 1]
            price_return = (next_price - current_price) / current_price

            # Risk-adjusted reward
            recent_volatility = np.std(prices[max(0, i-20):i]) / current_price
            risk_adjusted_reward = price_return / (recent_volatility + 1e-8)

            rewards.append(risk_adjusted_reward)

            # Advanced action labeling
            if risk_adjusted_reward > 0.005:  # Strong buy signal
                action = 2
            elif risk_adjusted_reward < -0.005:  # Strong sell signal
                action = 0
            else:  # Hold
                action = 1

            actions.append(action)

        print(f"✅ Created trading environment: {len(features)} samples, {len(state_vector)} state features")

        return {
            'features': np.array(features),
            'rewards': np.array(rewards),
            'actions': np.array(actions),
            'prices': prices[lookback:],
            'state_size': len(state_vector),
            'action_size': 3,
            'feature_names': feature_cols + ['price_trend', 'volatility', 'volume_trend']
        }

    def _create_advanced_dqn_architecture(self, state_size: int) -> Tuple[nn.Module, nn.Module]:
        """ایجاد معماری DQN پیشرفته"""

        class MarketDominatingDQN(nn.Module):
            """🔥 DQN برای شکست مارکت میکرها"""

            def __init__(self, state_size, action_size=3, hidden_size=512):
                super(MarketDominatingDQN, self).__init__()

                # Advanced feature extraction layers
                self.feature_extractor = nn.Sequential(
                    nn.Linear(state_size, hidden_size),
                    nn.ReLU(),
                    nn.BatchNorm1d(hidden_size),
                    nn.Dropout(0.3),

                    nn.Linear(hidden_size, hidden_size),
                    nn.ReLU(),
                    nn.BatchNorm1d(hidden_size),
                    nn.Dropout(0.3),

                    nn.Linear(hidden_size, hidden_size // 2),
                    nn.ReLU(),
                    nn.BatchNorm1d(hidden_size // 2),
                    nn.Dropout(0.2)
                )

                # Market pattern recognition
                self.pattern_detector = nn.Sequential(
                    nn.Linear(hidden_size // 2, 256),
                    nn.ReLU(),
                    nn.Dropout(0.2),

                    nn.Linear(256, 128),
                    nn.ReLU(),
                    nn.Dropout(0.1)
                )

                # Dueling DQN architecture
                self.value_stream = nn.Sequential(
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Linear(64, 1)
                )

                self.advantage_stream = nn.Sequential(
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Linear(64, action_size)
                )

            def forward(self, x):
                # Feature extraction
                features = self.feature_extractor(x)

                # Pattern detection
                patterns = self.pattern_detector(features)

                # Dueling streams
                value = self.value_stream(patterns)
                advantage = self.advantage_stream(patterns)

                # Combine value and advantage
                q_values = value + (advantage - advantage.mean(dim=1, keepdim=True))

                return q_values

        main_network = MarketDominatingDQN(state_size).to(self.device)
        target_network = MarketDominatingDQN(state_size).to(self.device)
        target_network.load_state_dict(main_network.state_dict())

        return main_network, target_network
