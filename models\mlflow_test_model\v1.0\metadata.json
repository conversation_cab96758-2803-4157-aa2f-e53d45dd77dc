{"name": "mlflow_test_model", "version": "1.0", "model_type": "custom", "description": "Model from MLflow", "author": "MLflow", "created_at": "2025-07-10T17:33:22.625682", "updated_at": "2025-07-10T17:33:22.625682", "status": "training", "stage": "staging", "tags": [], "parameters": {}, "metrics": {"accuracy": null, "precision": null, "recall": null, "f1_score": null, "auc_roc": null, "mse": null, "rmse": null, "mae": null, "r2_score": null, "custom_metrics": {}}, "dependencies": [], "training_data_hash": "", "model_hash": "", "file_path": "", "file_size": 0, "serialization_format": "pickle"}