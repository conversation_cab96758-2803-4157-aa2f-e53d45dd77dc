import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
from typing import Dict, List, Tuple, Any, Optional, Union
import shap
from sklearn.preprocessing import StandardScaler
from stable_baselines3.common.policies import BasePolicy
from matplotlib.patches import FancyBboxPatch
import plotly.express as px
from collections import Counter


class ExplainableAI:
    """
    ابزارهای explainable AI برای تحلیل تصمیمات مدل‌های یادگیری تقویتی
    
    این کلاس مجموعه‌ای از ابزارها برای تفسیر و توضیح تصمیمات مدل‌های یادگیری تقویتی
    ارائه می‌دهد. از تکنیک‌های مختلف مانند SHAP، Feature Importance، و Attention
    Visualization برای شفاف‌سازی فرآیند تصمیم‌گیری مدل استفاده می‌کند.
    """
    
    def __init__(self, model=None, feature_names=None):
        """
        مقداردهی اولیه ExplainableAI
        
        پارامترها:
        -----------
        model : object
            مدل یادگیری تقویتی (مثلاً مدل‌های stable-baselines3)
        feature_names : List[str], optional
            لیست نام ویژگی‌های ورودی مدل
        """
        self.model = model
        self.feature_names = feature_names or []
        self.explainer = None
        self.explanation_data = {}
    
    def set_model(self, model):
        """تنظیم مدل برای تحلیل"""
        self.model = model
        return self
    
    def set_feature_names(self, feature_names):
        """تنظیم نام ویژگی‌ها"""
        self.feature_names = feature_names
        return self
    
    def feature_importance(self, observations, actions=None, method='permutation', n_samples=100):
        """
        محاسبه اهمیت ویژگی‌ها در تصمیمات مدل
        
        پارامترها:
        -----------
        observations : np.ndarray
            مشاهدات ورودی به مدل
        actions : np.ndarray, optional
            اقدامات انجام شده توسط مدل (اگر None باشد، از مدل پیش‌بینی می‌شود)
        method : str, default='permutation'
            روش محاسبه اهمیت ویژگی ('permutation', 'shap', 'gradient')
        n_samples : int, default=100
            تعداد نمونه‌ها برای محاسبه اهمیت
            
        خروجی:
        -------
        Dict[str, float]
            دیکشنری اهمیت هر ویژگی
        """
        if self.model is None:
            raise ValueError("مدل تنظیم نشده است. ابتدا با استفاده از set_model یک مدل تنظیم کنید.")
        
        observations = np.array(observations)
        if len(observations.shape) == 1:
            observations = observations.reshape(1, -1)
        
        if actions is None:
            # پیش‌بینی اقدامات با مدل
            if hasattr(self.model, 'predict'):
                actions, _ = self.model.predict(observations, deterministic=True)
                if len(actions.shape) == 1:
                    actions = actions.reshape(-1, 1)
            else:
                raise ValueError("مدل باید متد predict داشته باشد یا actions باید مشخص شود.")
        elif len(actions.shape) == 1:
            actions = actions.reshape(-1, 1)
        
        importances = {}
        
        if method == 'permutation':
            importances = self._permutation_importance(observations, actions, n_samples)
        elif method == 'shap':
            importances = self._shap_importance(observations, n_samples)
        elif method == 'gradient':
            importances = self._gradient_importance(observations)
        else:
            raise ValueError(f"روش {method} پشتیبانی نمی‌شود. از 'permutation'، 'shap' یا 'gradient' استفاده کنید.")
        
        # ذخیره نتایج برای استفاده بعدی
        self.explanation_data['feature_importance'] = importances
        
        return importances
    
    def _permutation_importance(self, observations, actions, n_samples=100):
        """محاسبه اهمیت ویژگی‌ها با روش permutation"""
        n_features = observations.shape[1]
        feature_names = self.feature_names
        if len(feature_names) != n_features:
            feature_names = [f"feature_{i}" for i in range(n_features)]
        
        # محاسبه امتیاز پایه
        if hasattr(self.model, 'predict'):
            base_actions, _ = self.model.predict(observations, deterministic=True)
            if len(base_actions.shape) == 1:
                base_actions = base_actions.reshape(-1, 1)
        else:
            base_actions = self.model(observations)
            if len(base_actions.shape) == 1:
                base_actions = base_actions.reshape(-1, 1)
        
        # اطمینان از اینکه اقدامات دارای ابعاد مناسب هستند
        if actions.shape != base_actions.shape:
            if len(actions.shape) == 1:
                actions = actions.reshape(-1, 1)
            if actions.shape[0] != base_actions.shape[0]:
                # اگر تعداد نمونه‌ها متفاوت است، از اقدامات پایه استفاده می‌کنیم
                actions = base_actions
        
        base_score = np.mean(np.sum((base_actions - actions) ** 2, axis=1))
        
        importances = {}
        for i in range(n_features):
            scores = []
            for _ in range(n_samples):
                # کپی مشاهدات
                permuted_obs = observations.copy()
                
                # جایگشت ویژگی i-ام
                np.random.shuffle(permuted_obs[:, i])
                
                # پیش‌بینی با مشاهدات جایگشت‌شده
                if hasattr(self.model, 'predict'):
                    perm_actions, _ = self.model.predict(permuted_obs, deterministic=True)
                    if len(perm_actions.shape) == 1:
                        perm_actions = perm_actions.reshape(-1, 1)
                else:
                    perm_actions = self.model(permuted_obs)
                    if len(perm_actions.shape) == 1:
                        perm_actions = perm_actions.reshape(-1, 1)
                
                # محاسبه تغییر امتیاز
                perm_score = np.mean(np.sum((perm_actions - actions) ** 2, axis=1))
                scores.append(abs(perm_score - base_score))
            
            # میانگین تغییر امتیاز به عنوان اهمیت ویژگی
            importances[feature_names[i]] = np.mean(scores)
        
        # نرمال‌سازی اهمیت‌ها
        total = sum(importances.values()) or 1.0
        importances = {k: v / total for k, v in importances.items()}
        
        return importances
    
    def _shap_importance(self, observations, n_samples=100):
        """محاسبه اهمیت ویژگی‌ها با روش SHAP"""
        try:
            import shap
        except ImportError:
            raise ImportError("برای استفاده از روش SHAP، باید پکیج 'shap' را نصب کنید.")
        
        n_features = observations.shape[1]
        feature_names = self.feature_names
        if len(feature_names) != n_features:
            feature_names = [f"feature_{i}" for i in range(n_features)]
        
        # ایجاد explainer
        if hasattr(self.model, 'policy') and hasattr(self.model.policy, 'predict'):
            # برای مدل‌های stable-baselines3
            def predict_fn(x):
                if len(x) == 0:  # اگر ورودی خالی است
                    return np.array([])
                return self.model.policy.predict_values(torch.FloatTensor(x))
            
            background = observations[np.random.choice(observations.shape[0], min(100, observations.shape[0]), replace=False)]
            explainer = shap.KernelExplainer(predict_fn, background)
            
            # نمونه‌گیری برای محاسبه SHAP
            sample_idx = np.random.choice(observations.shape[0], min(n_samples, observations.shape[0]), replace=False)
            
            # اگر نمونه‌ها خالی هستند، از نمونه‌های پیش‌فرض استفاده می‌کنیم
            if len(sample_idx) == 0:
                importances = {feature_names[i]: 1.0 / n_features for i in range(n_features)}
                return importances
                
            shap_values = explainer.shap_values(observations[sample_idx])
            
            # میانگین قدر مطلق مقادیر SHAP برای هر ویژگی
            if isinstance(shap_values, list):
                # چند خروجی (مثلاً برای مدل‌های چند اقدامی)
                importances = {}
                for i in range(n_features):
                    importances[feature_names[i]] = np.mean([abs(shap_values[j][:, i]).mean() for j in range(len(shap_values))])
            else:
                importances = {feature_names[i]: abs(shap_values[:, i]).mean() for i in range(n_features)}
            
            # نرمال‌سازی اهمیت‌ها
            total = sum(importances.values()) or 1.0
            importances = {k: v / total for k, v in importances.items()}
            
            return importances
        else:
            # اگر مدل stable-baselines3 نباشد، از permutation استفاده می‌کنیم
            return self._permutation_importance(observations, None, n_samples)
    
    def _gradient_importance(self, observations):
        """محاسبه اهمیت ویژگی‌ها با روش گرادیان"""
        if not torch.is_tensor(observations):
            observations = torch.FloatTensor(observations)
            
        if not hasattr(self.model, 'policy') or not hasattr(self.model.policy, 'forward'):
            raise ValueError("مدل باید دارای policy با متد forward باشد.")
        
        n_features = observations.shape[1]
        feature_names = self.feature_names
        if len(feature_names) != n_features:
            feature_names = [f"feature_{i}" for i in range(n_features)]
        
        # فعال کردن گرادیان
        observations.requires_grad = True
        
        # محاسبه خروجی مدل
        if hasattr(self.model.policy, 'evaluate_actions'):
            # برای مدل‌های PPO، A2C و...
            _, values, _ = self.model.policy.evaluate_actions(observations, torch.zeros((observations.shape[0], 1)))
        else:
            # برای سایر مدل‌ها
            values = self.model.policy.forward(observations)
        
        # محاسبه گرادیان
        values.sum().backward()
        gradients = observations.grad.abs().mean(dim=0).detach().numpy()
        
        # تبدیل به دیکشنری
        importances = {feature_names[i]: gradients[i] for i in range(n_features)}
        
        # نرمال‌سازی اهمیت‌ها
        total = sum(importances.values()) or 1.0
        importances = {k: v / total for k, v in importances.items()}
        
        return importances
    
    def decision_explanation(self, observation, action=None):
        """
        توضیح دلیل تصمیم مدل برای یک مشاهده خاص
        
        پارامترها:
        -----------
        observation : np.ndarray
            مشاهده ورودی به مدل
        action : np.ndarray, optional
            اقدام انجام شده توسط مدل (اگر None باشد، از مدل پیش‌بینی می‌شود)
            
        خروجی:
        -------
        Dict[str, Any]
            توضیح تصمیم مدل
        """
        if self.model is None:
            raise ValueError("مدل تنظیم نشده است. ابتدا با استفاده از set_model یک مدل تنظیم کنید.")
        
        observation = np.array(observation)
        if len(observation.shape) == 1:
            observation = observation.reshape(1, -1)
        
        if action is None:
            # پیش‌بینی اقدام با مدل
            if hasattr(self.model, 'predict'):
                action, _ = self.model.predict(observation, deterministic=True)
                if len(action.shape) == 1:
                    action = action.reshape(-1, 1)
            else:
                raise ValueError("مدل باید متد predict داشته باشد یا action باید مشخص شود.")
        elif len(action.shape) == 1:
            action = action.reshape(-1, 1)
        
        # محاسبه اهمیت ویژگی‌ها برای این مشاهده خاص
        importances = self.feature_importance(observation, action, method='permutation', n_samples=10)
        
        # یافتن ویژگی‌های مهم
        sorted_importances = sorted(importances.items(), key=lambda x: x[1], reverse=True)
        top_features = sorted_importances[:3]
        
        # ایجاد توضیح
        explanation = {
            'action': action,
            'top_features': top_features,
            'explanation': f"مدل تصمیم گرفت اقدام {action} را انجام دهد، عمدتاً به دلیل ویژگی‌های: "
                          f"{', '.join([f'{name} ({importance:.2f})' for name, importance in top_features])}"
        }
        
        return explanation
    
    def visualize_feature_importance(self, save_path=None, top_n=10):
        """
        نمایش نمودار اهمیت ویژگی‌ها
        
        پارامترها:
        -----------
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار نمایش داده می‌شود)
        top_n : int, default=10
            تعداد ویژگی‌های برتر برای نمایش
            
        خروجی:
        -------
        matplotlib.figure.Figure
            شیء نمودار
        """
        if 'feature_importance' not in self.explanation_data:
            raise ValueError("ابتدا باید با استفاده از متد feature_importance اهمیت ویژگی‌ها را محاسبه کنید.")
        
        importances = self.explanation_data['feature_importance']
        
        # مرتب‌سازی ویژگی‌ها بر اساس اهمیت
        sorted_importances = sorted(importances.items(), key=lambda x: x[1], reverse=True)[:top_n]
        features = [item[0] for item in sorted_importances]
        values = [item[1] for item in sorted_importances]
        
        # ایجاد نمودار
        fig, ax = plt.subplots(figsize=(10, 6))
        y_pos = np.arange(len(features))
        ax.barh(y_pos, values, align='center')
        ax.set_yticks(y_pos)
        ax.set_yticklabels(features)
        ax.invert_yaxis()  # برای نمایش مهم‌ترین ویژگی در بالا
        ax.set_xlabel('اهمیت نسبی')
        ax.set_title('اهمیت ویژگی‌ها در تصمیم‌گیری مدل')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            plt.close()
            return None
        else:
            return fig
    
    def visualize_decision_process(self, observation, save_path=None):
        """
        نمایش تصویری فرآیند تصمیم‌گیری مدل برای یک مشاهده خاص
        
        پارامترها:
        -----------
        observation : np.ndarray
            مشاهده ورودی به مدل
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار نمایش داده می‌شود)
            
        خروجی:
        -------
        matplotlib.figure.Figure
            شیء نمودار
        """
        if self.model is None:
            raise ValueError("مدل تنظیم نشده است. ابتدا با استفاده از set_model یک مدل تنظیم کنید.")
        
        observation = np.array(observation)
        if len(observation.shape) == 1:
            observation = observation.reshape(1, -1)
        
        # پیش‌بینی اقدام با مدل
        if hasattr(self.model, 'predict'):
            action, _ = self.model.predict(observation, deterministic=True)
            if len(action.shape) == 1:
                action = action.reshape(-1, 1)
        else:
            raise ValueError("مدل باید متد predict داشته باشد.")
        
        # محاسبه اهمیت ویژگی‌ها برای این مشاهده خاص
        importances = self.feature_importance(observation, action, method='permutation', n_samples=10)
        
        n_features = observation.shape[1]
        feature_names = self.feature_names
        if len(feature_names) != n_features:
            feature_names = [f"feature_{i}" for i in range(n_features)]
        
        # ایجاد نمودار
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # نمودار 1: مقادیر ویژگی‌ها
        y_pos = np.arange(n_features)
        ax1.barh(y_pos, observation[0], align='center')
        ax1.set_yticks(y_pos)
        ax1.set_yticklabels(feature_names)
        ax1.invert_yaxis()
        ax1.set_xlabel('مقدار ویژگی')
        ax1.set_title('مقادیر ویژگی‌ها در مشاهده')
        
        # نمودار 2: اهمیت ویژگی‌ها
        sorted_importances = sorted(importances.items(), key=lambda x: x[1], reverse=True)
        features = [item[0] for item in sorted_importances]
        values = [item[1] for item in sorted_importances]
        
        y_pos = np.arange(len(features))
        ax2.barh(y_pos, values, align='center')
        ax2.set_yticks(y_pos)
        ax2.set_yticklabels(features)
        ax2.invert_yaxis()
        ax2.set_xlabel('اهمیت نسبی')
        ax2.set_title(f'اهمیت ویژگی‌ها در تصمیم {action}')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            plt.close()
            return None
        else:
            return fig
    
    def compare_decisions(self, observations, save_path=None):
        """
        مقایسه تصمیمات مدل برای چندین مشاهده
        
        پارامترها:
        -----------
        observations : np.ndarray
            مشاهدات ورودی به مدل
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار نمایش داده می‌شود)
            
        خروجی:
        -------
        matplotlib.figure.Figure
            شیء نمودار
        """
        if self.model is None:
            raise ValueError("مدل تنظیم نشده است. ابتدا با استفاده از set_model یک مدل تنظیم کنید.")
        
        observations = np.array(observations)
        if len(observations.shape) == 1:
            observations = observations.reshape(1, -1)
        
        # پیش‌بینی اقدامات با مدل
        actions = []
        for obs in observations:
            if hasattr(self.model, 'predict'):
                action, _ = self.model.predict(obs.reshape(1, -1), deterministic=True)
                if len(action.shape) == 1:
                    action = action.reshape(-1, 1)
                actions.append(action[0])
            else:
                raise ValueError("مدل باید متد predict داشته باشد.")
        
        # محاسبه اهمیت ویژگی‌ها برای هر مشاهده
        all_importances = []
        for i, obs in enumerate(observations):
            obs_reshaped = obs.reshape(1, -1)
            action_reshaped = np.array([actions[i]]).reshape(1, -1)
            importances = self.feature_importance(obs_reshaped, action_reshaped, method='permutation', n_samples=5)
            all_importances.append(importances)
        
        # ایجاد نمودار
        n_obs = min(5, len(observations))  # حداکثر 5 مشاهده برای نمایش
        fig, axes = plt.subplots(n_obs, 1, figsize=(10, 4 * n_obs))
        if n_obs == 1:
            axes = [axes]
        
        for i in range(n_obs):
            sorted_importances = sorted(all_importances[i].items(), key=lambda x: x[1], reverse=True)
            features = [item[0] for item in sorted_importances]
            values = [item[1] for item in sorted_importances]
            
            y_pos = np.arange(len(features))
            axes[i].barh(y_pos, values, align='center')
            axes[i].set_yticks(y_pos)
            axes[i].set_yticklabels(features)
            axes[i].invert_yaxis()
            axes[i].set_xlabel('اهمیت نسبی')
            axes[i].set_title(f'مشاهده {i+1}: اقدام {actions[i]}')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            plt.close()
            return None
        else:
            return fig
    
    def generate_explanation_report(self, observations, save_path=None):
        """
        ایجاد گزارش کامل توضیحات برای مجموعه‌ای از مشاهدات
        
        پارامترها:
        -----------
        observations : np.ndarray
            مشاهدات ورودی به مدل
        save_path : str, optional
            مسیر ذخیره گزارش (اگر None باشد، گزارش به صورت دیکشنری برگردانده می‌شود)
            
        خروجی:
        -------
        Dict[str, Any] or None
            گزارش توضیحات یا None اگر گزارش ذخیره شده باشد
        """
        if self.model is None:
            raise ValueError("مدل تنظیم نشده است. ابتدا با استفاده از set_model یک مدل تنظیم کنید.")
        
        observations = np.array(observations)
        if len(observations.shape) == 1:
            observations = observations.reshape(1, -1)
        
        # محاسبه اهمیت کلی ویژگی‌ها
        overall_importances = self.feature_importance(observations, method='permutation', n_samples=10)
        
        # پیش‌بینی اقدامات برای هر مشاهده
        actions = []
        explanations = []
        for i, obs in enumerate(observations[:min(10, len(observations))]):  # حداکثر 10 مشاهده
            if hasattr(self.model, 'predict'):
                obs_reshaped = obs.reshape(1, -1)
                action, _ = self.model.predict(obs_reshaped, deterministic=True)
                if len(action.shape) == 1:
                    action = action.reshape(-1, 1)
                actions.append(action[0])
                
                # توضیح تصمیم
                explanation = self.decision_explanation(obs_reshaped, action)
                explanations.append(explanation)
            else:
                raise ValueError("مدل باید متد predict داشته باشد.")
        
        # ایجاد گزارش
        report = {
            'overall_feature_importance': overall_importances,
            'top_features': sorted(overall_importances.items(), key=lambda x: x[1], reverse=True)[:5],
            'sample_decisions': [
                {
                    'observation_idx': i,
                    'action': actions[i].tolist() if hasattr(actions[i], 'tolist') else actions[i],
                    'explanation': {
                        'action': explanations[i]['action'].tolist() if hasattr(explanations[i]['action'], 'tolist') else explanations[i]['action'],
                        'top_features': explanations[i]['top_features'],
                        'explanation': explanations[i]['explanation']
                    }
                }
                for i in range(len(actions))
            ]
        }
        
        if save_path:
            # ذخیره گزارش به صورت JSON
            import json
            
            # تبدیل مقادیر numpy به لیست برای سازگاری با JSON
            class NumpyEncoder(json.JSONEncoder):
                def default(self, obj):
                    if isinstance(obj, np.ndarray):
                        return obj.tolist()
                    if isinstance(obj, np.integer):
                        return int(obj)
                    if isinstance(obj, np.floating):
                        return float(obj)
                    return super(NumpyEncoder, self).default(obj)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, cls=NumpyEncoder)
            
            # ذخیره نمودار اهمیت ویژگی‌ها
            self.explanation_data['feature_importance'] = overall_importances
            self.visualize_feature_importance(save_path=save_path.replace('.json', '_importance.png'))
            
            return None
        else:
            return report
    
    def analyze_market_regime(self, observations, actions=None, market_data=None, n_regimes=3, save_path=None):
        """
        تحلیل تصمیمات مدل در شرایط مختلف بازار
        
        پارامترها:
        -----------
        observations : np.ndarray
            مشاهدات ورودی به مدل
        actions : np.ndarray, optional
            اقدامات انجام شده توسط مدل (اگر None باشد، از مدل پیش‌بینی می‌شود)
        market_data : pd.DataFrame, optional
            داده‌های بازار شامل قیمت، حجم و غیره
        n_regimes : int, default=3
            تعداد رژیم‌های بازار برای شناسایی
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار نمایش داده می‌شود)
            
        خروجی:
        -------
        Dict[str, Any]
            تحلیل تصمیمات در رژیم‌های مختلف بازار
        """
        if self.model is None:
            raise ValueError("مدل تنظیم نشده است. ابتدا با استفاده از set_model یک مدل تنظیم کنید.")
        
        observations = np.array(observations)
        if len(observations.shape) == 1:
            observations = observations.reshape(1, -1)
        
        # اگر market_data ارائه نشده باشد، از observations استفاده می‌کنیم
        if market_data is None:
            # فرض می‌کنیم که 4 ستون اول مربوط به OHLC هستند
            if observations.shape[1] >= 4:
                market_data = pd.DataFrame({
                    'open': observations[:, 0],
                    'high': observations[:, 1],
                    'low': observations[:, 2],
                    'close': observations[:, 3]
                })
            else:
                market_data = pd.DataFrame({
                    'feature': observations[:, 0]  # از اولین ویژگی استفاده می‌کنیم
                })
        
        # محاسبه بازده برای شناسایی رژیم‌های بازار
        if 'close' in market_data.columns:
            market_data['returns'] = market_data['close'].pct_change().fillna(0)
        else:
            market_data['returns'] = market_data.iloc[:, 0].pct_change().fillna(0)
        
        # محاسبه volatility با استفاده از انحراف معیار متحرک
        market_data['volatility'] = market_data['returns'].rolling(window=20).std().fillna(0)
        
        # شناسایی رژیم‌های بازار با استفاده از خوشه‌بندی K-means
        from sklearn.cluster import KMeans
        
        # استفاده از بازده و volatility برای خوشه‌بندی
        features = np.column_stack([
            market_data['returns'].values,
            market_data['volatility'].values
        ])
        
        # حذف ردیف‌های با مقادیر NaN
        valid_indices = ~np.isnan(features).any(axis=1)
        valid_features = features[valid_indices]
        
        if len(valid_features) < n_regimes:
            # اگر داده‌ها کافی نباشند، تعداد رژیم‌ها را کاهش می‌دهیم
            n_regimes = max(2, len(valid_features) // 10)
        
        # خوشه‌بندی K-means
        kmeans = KMeans(n_clusters=n_regimes, random_state=42)
        clusters = kmeans.fit_predict(valid_features)
        
        # اضافه کردن برچسب‌های خوشه به داده‌ها
        market_regimes = np.zeros(len(market_data))
        market_regimes[valid_indices] = clusters
        market_data['regime'] = market_regimes
        
        # تعیین نام رژیم‌ها بر اساس ویژگی‌های آنها
        regime_names = {}
        for i in range(n_regimes):
            regime_data = market_data[market_data['regime'] == i]
            avg_return = regime_data['returns'].mean()
            avg_volatility = regime_data['volatility'].mean()
            
            if avg_return > 0.001:
                if avg_volatility > 0.01:
                    name = "روند صعودی پرنوسان"
                else:
                    name = "روند صعودی کم‌نوسان"
            elif avg_return < -0.001:
                if avg_volatility > 0.01:
                    name = "روند نزولی پرنوسان"
                else:
                    name = "روند نزولی کم‌نوسان"
            else:
                if avg_volatility > 0.01:
                    name = "نوسانی بدون روند"
                else:
                    name = "بدون روند و کم‌نوسان"
            
            regime_names[i] = name
        
        # اگر actions ارائه نشده باشد، از مدل پیش‌بینی می‌کنیم
        if actions is None:
            actions = []
            for obs in observations:
                if hasattr(self.model, 'predict'):
                    action, _ = self.model.predict(obs.reshape(1, -1), deterministic=True)
                    if len(action.shape) == 1:
                        action = action.reshape(-1, 1)
                    actions.append(action[0])
                else:
                    raise ValueError("مدل باید متد predict داشته باشد یا actions باید مشخص شود.")
            actions = np.array(actions)
        
        # تحلیل اهمیت ویژگی‌ها در هر رژیم بازار
        regime_importances = {}
        for i in range(n_regimes):
            regime_indices = np.where(market_data['regime'] == i)[0]
            if len(regime_indices) > 5:  # حداقل 5 نمونه برای تحلیل
                regime_obs = observations[regime_indices]
                regime_acts = actions[regime_indices] if len(actions) > 0 else None
                
                try:
                    importances = self.feature_importance(regime_obs, regime_acts, method='permutation', n_samples=5)
                    regime_importances[regime_names[i]] = importances
                except Exception as e:
                    regime_importances[regime_names[i]] = {"error": str(e)}
        
        # تحلیل تصمیمات در هر رژیم بازار
        regime_decisions = {}
        for i in range(n_regimes):
            regime_indices = np.where(market_data['regime'] == i)[0]
            if len(regime_indices) > 0:
                regime_acts = actions[regime_indices] if len(actions) > 0 else []
                
                # محاسبه درصد اقدامات مختلف
                if len(regime_acts) > 0:
                    unique_actions, action_counts = np.unique(regime_acts, return_counts=True)
                    action_percentages = action_counts / len(regime_acts)
                    
                    regime_decisions[regime_names[i]] = {
                        'count': len(regime_indices),
                        'actions': {str(act): float(pct) for act, pct in zip(unique_actions, action_percentages)}
                    }
                else:
                    regime_decisions[regime_names[i]] = {
                        'count': len(regime_indices),
                        'actions': {}
                    }
        
        # ایجاد نمودار
        if save_path or market_data is not None:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [2, 1]})
            
            # نمودار 1: قیمت و رژیم‌های بازار
            if 'close' in market_data.columns:
                ax1.plot(market_data['close'], label='قیمت بسته شدن')
                
                # رنگ‌آمیزی پس‌زمینه بر اساس رژیم بازار
                for i in range(n_regimes):
                    regime_indices = np.where(market_data['regime'] == i)[0]
                    if len(regime_indices) > 0:
                        start_idx = regime_indices[0]
                        current_regime = i
                        
                        for j in range(1, len(regime_indices)):
                            if regime_indices[j] != regime_indices[j-1] + 1:
                                end_idx = regime_indices[j-1]
                                ax1.axvspan(start_idx, end_idx, alpha=0.2, color=f'C{current_regime}')
                                start_idx = regime_indices[j]
                        
                        # آخرین بخش
                        ax1.axvspan(start_idx, regime_indices[-1], alpha=0.2, color=f'C{current_regime}')
                
                ax1.set_ylabel('قیمت')
                ax1.set_title('تحلیل رژیم‌های بازار')
                ax1.legend()
            
            # نمودار 2: اقدامات مدل
            if len(actions) > 0:
                ax2.scatter(range(len(actions)), actions, c=market_data['regime'], cmap='viridis', alpha=0.7)
                ax2.set_xlabel('زمان')
                ax2.set_ylabel('اقدام مدل')
                ax2.set_title('اقدامات مدل در رژیم‌های مختلف بازار')
                
                # افزودن راهنما
                from matplotlib.lines import Line2D
                legend_elements = [Line2D([0], [0], marker='o', color='w', markerfacecolor=f'C{i}', 
                                        markersize=10, label=regime_names[i]) for i in range(n_regimes)]
                ax2.legend(handles=legend_elements, loc='best')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path)
                plt.close()
            else:
                plt.show()
        
        # ایجاد گزارش
        report = {
            'regime_names': regime_names,
            'regime_importances': regime_importances,
            'regime_decisions': regime_decisions
        }
        
        return report
    
    def analyze_decision_risk(self, observations, actions=None, returns=None, save_path=None):
        """
        تحلیل ریسک تصمیمات مدل
        
        پارامترها:
        -----------
        observations : np.ndarray
            مشاهدات ورودی به مدل
        actions : np.ndarray, optional
            اقدامات انجام شده توسط مدل (اگر None باشد، از مدل پیش‌بینی می‌شود)
        returns : np.ndarray, optional
            بازده‌های واقعی متناظر با هر اقدام
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار نمایش داده می‌شود)
            
        خروجی:
        -------
        Dict[str, Any]
            تحلیل ریسک تصمیمات
        """
        if self.model is None:
            raise ValueError("مدل تنظیم نشده است. ابتدا با استفاده از set_model یک مدل تنظیم کنید.")
        
        observations = np.array(observations)
        if len(observations.shape) == 1:
            observations = observations.reshape(1, -1)
        
        # اگر actions ارائه نشده باشد، از مدل پیش‌بینی می‌کنیم
        if actions is None:
            actions = []
            for obs in observations:
                if hasattr(self.model, 'predict'):
                    action, _ = self.model.predict(obs.reshape(1, -1), deterministic=True)
                    if len(action.shape) == 1:
                        action = action.reshape(-1, 1)
                    actions.append(action[0])
                else:
                    raise ValueError("مدل باید متد predict داشته باشد یا actions باید مشخص شود.")
            actions = np.array(actions)
        elif isinstance(actions, list):
            actions = np.array(actions)
        
        # اگر returns ارائه نشده باشد، مقادیر تصادفی تولید می‌کنیم
        if returns is None:
            # فرض می‌کنیم که اقدامات مثبت منجر به بازده مثبت و اقدامات منفی منجر به بازده منفی می‌شوند
            # اما با کمی نویز
            returns = np.array([a * (1 + 0.5 * np.random.randn()) for a in actions])
        elif isinstance(returns, list):
            returns = np.array(returns)
        
        # محاسبه معیارهای ریسک
        risk_metrics = {}
        
        # محاسبه Value at Risk (VaR) در سطح اطمینان 95%
        var_95 = np.percentile(returns, 5)
        risk_metrics['var_95'] = float(var_95)
        
        # محاسبه Expected Shortfall (ES) یا Conditional VaR
        es_95 = returns[returns <= var_95].mean() if any(returns <= var_95) else var_95
        risk_metrics['es_95'] = float(es_95)
        
        # محاسبه Sharpe Ratio (با فرض نرخ بدون ریسک صفر)
        mean_return = returns.mean()
        std_return = returns.std()
        sharpe_ratio = mean_return / std_return if std_return > 0 else 0
        risk_metrics['sharpe_ratio'] = float(sharpe_ratio)
        
        # محاسبه Maximum Drawdown
        cumulative_returns = np.cumsum(returns)
        max_drawdown = 0
        peak = cumulative_returns[0]
        
        for value in cumulative_returns:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak if peak > 0 else 0
            max_drawdown = max(max_drawdown, drawdown)
        
        risk_metrics['max_drawdown'] = float(max_drawdown)
        
        # تحلیل ریسک بر اساس اقدامات
        action_risk = {}
        unique_actions = np.unique(actions)
        
        for act in unique_actions:
            act_indices = np.where(actions == act)[0]
            act_returns = returns[act_indices]
            
            if len(act_returns) > 0:
                act_var_95 = np.percentile(act_returns, 5)
                act_es_95 = act_returns[act_returns <= act_var_95].mean() if any(act_returns <= act_var_95) else act_var_95
                act_sharpe = act_returns.mean() / act_returns.std() if act_returns.std() > 0 else 0
                
                action_risk[str(act)] = {
                    'count': int(len(act_returns)),
                    'mean_return': float(act_returns.mean()),
                    'std_return': float(act_returns.std()),
                    'var_95': float(act_var_95),
                    'es_95': float(act_es_95),
                    'sharpe_ratio': float(act_sharpe)
                }
        
        # ایجاد نمودار
        if save_path is not None:
            fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 15))
            
            # نمودار 1: توزیع بازده‌ها
            ax1.hist(returns, bins=30, alpha=0.7)
            ax1.axvline(var_95, color='r', linestyle='--', label=f'VaR 95%: {var_95:.4f}')
            ax1.axvline(es_95, color='g', linestyle='--', label=f'ES 95%: {es_95:.4f}')
            ax1.set_xlabel('بازده')
            ax1.set_ylabel('فراوانی')
            ax1.set_title('توزیع بازده‌ها و معیارهای ریسک')
            ax1.legend()
            
            # نمودار 2: بازده تجمعی
            ax2.plot(cumulative_returns)
            ax2.set_xlabel('زمان')
            ax2.set_ylabel('بازده تجمعی')
            ax2.set_title(f'بازده تجمعی (Maximum Drawdown: {max_drawdown:.4f})')
            
            # نمودار 3: ریسک بر اساس اقدامات
            action_labels = [str(act) for act in unique_actions]
            var_values = [action_risk[str(act)]['var_95'] for act in unique_actions]
            sharpe_values = [action_risk[str(act)]['sharpe_ratio'] for act in unique_actions]
            
            x = np.arange(len(action_labels))
            width = 0.35
            
            ax3.bar(x - width/2, var_values, width, label='VaR 95%')
            ax3.bar(x + width/2, sharpe_values, width, label='Sharpe Ratio')
            
            ax3.set_xlabel('اقدام')
            ax3.set_ylabel('مقدار')
            ax3.set_title('معیارهای ریسک بر اساس اقدامات')
            ax3.set_xticks(x)
            ax3.set_xticklabels(action_labels)
            ax3.legend()
            
            plt.tight_layout()
            plt.savefig(save_path)
            plt.close()
        
        # ایجاد گزارش
        report = {
            'overall_risk_metrics': risk_metrics,
            'action_risk_analysis': action_risk
        }
        
        return report
    
    def identify_critical_decision_points(self, observations, actions=None, returns=None, market_data=None, n_critical=5, save_path=None):
        """
        شناسایی نقاط تصمیم‌گیری بحرانی
        
        پارامترها:
        -----------
        observations : np.ndarray
            مشاهدات ورودی به مدل
        actions : np.ndarray, optional
            اقدامات انجام شده توسط مدل (اگر None باشد، از مدل پیش‌بینی می‌شود)
        returns : np.ndarray, optional
            بازده‌های واقعی متناظر با هر اقدام
        market_data : pd.DataFrame, optional
            داده‌های بازار شامل قیمت، حجم و غیره
        n_critical : int, default=5
            تعداد نقاط بحرانی برای شناسایی
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار نمایش داده می‌شود)
            
        خروجی:
        -------
        Dict[str, Any]
            اطلاعات نقاط تصمیم‌گیری بحرانی
        """
        if self.model is None:
            raise ValueError("مدل تنظیم نشده است. ابتدا با استفاده از set_model یک مدل تنظیم کنید.")
        
        observations = np.array(observations)
        if len(observations.shape) == 1:
            observations = observations.reshape(1, -1)
        
        # اگر actions ارائه نشده باشد، از مدل پیش‌بینی می‌کنیم
        if actions is None:
            actions = []
            for obs in observations:
                if hasattr(self.model, 'predict'):
                    action, _ = self.model.predict(obs.reshape(1, -1), deterministic=True)
                    if len(action.shape) == 1:
                        action = action.reshape(-1, 1)
                    actions.append(action[0])
                else:
                    raise ValueError("مدل باید متد predict داشته باشد یا actions باید مشخص شود.")
            actions = np.array(actions)
        elif isinstance(actions, list):
            actions = np.array(actions)
        
        # اگر returns ارائه نشده باشد، مقادیر تصادفی تولید می‌کنیم
        if returns is None:
            # فرض می‌کنیم که اقدامات مثبت منجر به بازده مثبت و اقدامات منفی منجر به بازده منفی می‌شوند
            # اما با کمی نویز
            returns = np.array([a * (1 + 0.5 * np.random.randn()) for a in actions])
        elif isinstance(returns, list):
            returns = np.array(returns)
        
        # محاسبه بازده تجمعی
        cumulative_returns = np.cumsum(returns)
        
        # شناسایی نقاط بحرانی بر اساس معیارهای مختلف
        critical_points = []
        
        # معیار 1: بزرگترین بازده‌های مثبت
        top_positive_indices = np.argsort(returns)[-n_critical:]
        for idx in top_positive_indices:
            critical_points.append({
                'index': int(idx),
                'type': 'بزرگترین بازده مثبت',
                'return': float(returns[idx]),
                'action': actions[idx].tolist() if hasattr(actions[idx], 'tolist') else float(actions[idx]),
                'cumulative_return': float(cumulative_returns[idx])
            })
        
        # معیار 2: بزرگترین بازده‌های منفی
        top_negative_indices = np.argsort(returns)[:n_critical]
        for idx in top_negative_indices:
            critical_points.append({
                'index': int(idx),
                'type': 'بزرگترین بازده منفی',
                'return': float(returns[idx]),
                'action': actions[idx].tolist() if hasattr(actions[idx], 'tolist') else float(actions[idx]),
                'cumulative_return': float(cumulative_returns[idx])
            })
        
        # معیار 3: نقاط تغییر روند در بازده تجمعی
        trend_changes = []
        for i in range(2, len(cumulative_returns) - 2):
            # بررسی تغییر از روند صعودی به نزولی یا برعکس
            prev_trend = cumulative_returns[i-1] - cumulative_returns[i-2]
            next_trend = cumulative_returns[i+1] - cumulative_returns[i]
            
            if (prev_trend > 0 and next_trend < 0) or (prev_trend < 0 and next_trend > 0):
                trend_changes.append((i, abs(next_trend - prev_trend)))
        
        # انتخاب مهم‌ترین تغییرات روند
        trend_changes.sort(key=lambda x: x[1], reverse=True)
        for idx, _ in trend_changes[:n_critical]:
            critical_points.append({
                'index': int(idx),
                'type': 'تغییر روند',
                'return': float(returns[idx]),
                'action': actions[idx].tolist() if hasattr(actions[idx], 'tolist') else float(actions[idx]),
                'cumulative_return': float(cumulative_returns[idx])
            })
        
        # مرتب‌سازی نقاط بحرانی بر اساس زمان
        critical_points.sort(key=lambda x: x['index'])
        
        # تحلیل ویژگی‌های مهم در نقاط بحرانی
        critical_importances = {}
        for point in critical_points:
            idx = point['index']
            if 0 <= idx < len(observations):
                obs = observations[idx].reshape(1, -1)
                act = np.array([point['action']]).reshape(1, -1) if not isinstance(point['action'], list) else np.array(point['action']).reshape(1, -1)
                
                try:
                    importances = self.feature_importance(obs, act, method='permutation', n_samples=10)
                    # یافتن سه ویژگی مهم
                    top_features = sorted(importances.items(), key=lambda x: x[1], reverse=True)[:3]
                    critical_importances[idx] = top_features
                except Exception as e:
                    critical_importances[idx] = [("error", str(e))]
        
        # ایجاد نمودار
        if save_path is not None:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [2, 1]})
            
            # نمودار 1: بازده تجمعی و نقاط بحرانی
            ax1.plot(cumulative_returns, label='بازده تجمعی')
            
            # نمایش نقاط بحرانی
            critical_indices = [point['index'] for point in critical_points]
            critical_values = [cumulative_returns[idx] for idx in critical_indices]
            
            ax1.scatter(critical_indices, critical_values, c='red', s=100, zorder=5, label='نقاط بحرانی')
            
            # افزودن برچسب به نقاط بحرانی
            for i, (idx, val) in enumerate(zip(critical_indices, critical_values)):
                ax1.annotate(f"{i+1}", (idx, val), xytext=(5, 5), textcoords='offset points')
            
            ax1.set_xlabel('زمان')
            ax1.set_ylabel('بازده تجمعی')
            ax1.set_title('نقاط تصمیم‌گیری بحرانی')
            ax1.legend()
            
            # نمودار 2: اقدامات مدل
            ax2.plot(actions, label='اقدامات مدل')
            ax2.scatter(critical_indices, [actions[idx] for idx in critical_indices], c='red', s=100, zorder=5)
            
            ax2.set_xlabel('زمان')
            ax2.set_ylabel('اقدام')
            ax2.set_title('اقدامات مدل و نقاط بحرانی')
            ax2.legend()
            
            plt.tight_layout()
            plt.savefig(save_path)
            plt.close()
        
        # ایجاد گزارش
        report = {
            'critical_points': critical_points,
            'critical_importances': {str(k): v for k, v in critical_importances.items()}
        }
        
        return report

    def create_decision_heatmap(self, observations, actions, timestamps, feature_idx=[0, 1], save_path=None):
        """
        تولید نقشه حرارتی تصمیمات مدل بر اساس دو ویژگی و زمان
        observations: np.ndarray
        actions: np.ndarray
        timestamps: pd.Series یا np.ndarray از تاریخ/زمان
        feature_idx: لیست اندیس دو ویژگی برای محورهای نقشه حرارتی
        save_path: مسیر ذخیره تصویر (اختیاری)
        خروجی: dict شامل داده‌های نقشه حرارتی، همبستگی ویژگی‌ها و الگوهای زمانی
        """
        import matplotlib.pyplot as plt
        import seaborn as sns
        x = observations[:, feature_idx[0]]
        y = observations[:, feature_idx[1]]
        z = actions
        # ساخت دیتافریم برای تحلیل
        df = pd.DataFrame({
            'x': x,
            'y': y,
            'action': z,
            'time': pd.to_datetime(timestamps)
        })
        # نقشه حرارتی دوبعدی
        heatmap_data = pd.pivot_table(df, values='action', index='y', columns='x', aggfunc='mean')
        # همبستگی ویژگی‌ها با اقدام
        feature_correlations = df[['x', 'y', 'action']].corr()['action'].to_dict()
        # الگوهای زمانی (میانگین اقدام در هر بازه زمانی)
        df['hour'] = df['time'].dt.hour
        time_patterns = df.groupby('hour')['action'].mean().to_dict()
        # رسم نقشه حرارتی
        plt.figure(figsize=(8, 6))
        sns.heatmap(heatmap_data, cmap='coolwarm', cbar_kws={'label': 'Action'})
        plt.title('Decision Heatmap')
        plt.xlabel(self.feature_names[feature_idx[0]] if self.feature_names else f'feature_{feature_idx[0]}')
        plt.ylabel(self.feature_names[feature_idx[1]] if self.feature_names else f'feature_{feature_idx[1]}')
        if save_path:
            plt.savefig(save_path, bbox_inches='tight')
            plt.close()
            fig = None
        else:
            fig = plt.gcf()
        return {
            'heatmap_data': heatmap_data.values.tolist(),
            'feature_correlations': feature_correlations,
            'time_patterns': time_patterns,
            'fig': fig
        }

    def counterfactual_analysis(self, observation, target_action, feature_ranges, n_samples=20, save_path=None):
        """
        تحلیل کانتر-فکتوال برای یک مشاهده و اقدام هدف
        observation: np.ndarray
        target_action: مقدار اقدام هدف
        feature_ranges: dict اندیس ویژگی به (min, max)
        n_samples: تعداد نمونه تصادفی
        save_path: مسیر ذخیره تصویر (اختیاری)
        خروجی: dict شامل لیست کانترفکتوال‌ها، اقدام اولیه، تغییرات ویژگی و کمترین تغییر
        """
        import matplotlib.pyplot as plt
        obs = np.array(observation).copy()
        if len(obs.shape) == 1:
            obs = obs.reshape(1, -1)
        original_action, _ = self.model.predict(obs)
        original_action = float(original_action[0])
        counterfactuals = []
        feature_changes = []
        min_dist = float('inf')
        minimal_cf = None
        for _ in range(n_samples):
            cf_obs = obs.copy()
            changed = {}
            for idx, (fmin, fmax) in feature_ranges.items():
                val = np.random.uniform(fmin, fmax)
                cf_obs[0, idx] = val
                changed[idx] = val
            cf_action, _ = self.model.predict(cf_obs)
            cf_action = float(cf_action[0])
            dist = abs(cf_action - target_action)
            feature_change = np.linalg.norm(cf_obs - obs)
            counterfactuals.append({'features': cf_obs[0].tolist(), 'action': cf_action, 'dist': dist})
            feature_changes.append(feature_change)
            if dist < min_dist:
                min_dist = dist
                minimal_cf = {'features': cf_obs[0].tolist(), 'action': cf_action, 'dist': dist, 'feature_change': feature_change}
        # رسم پراکندگی اقدام‌ها نسبت به اقدام هدف
        plt.figure(figsize=(7, 4))
        plt.scatter([cf['action'] for cf in counterfactuals], feature_changes, c='b', label='Counterfactuals')
        plt.axvline(target_action, color='r', linestyle='--', label='Target Action')
        plt.xlabel('Action')
        plt.ylabel('Feature Change (L2)')
        plt.title('Counterfactual Analysis')
        plt.legend()
        if save_path:
            plt.savefig(save_path, bbox_inches='tight')
            plt.close()
            fig = None
        else:
            fig = plt.gcf()
        return {
            'counterfactuals': counterfactuals,
            'original_action': original_action,
            'feature_changes': feature_changes,
            'minimal_counterfactual': minimal_cf,
            'fig': fig
        }

    def sensitivity_analysis(self, observation, feature_ranges, n_samples=10, save_path=None):
        """
        تحلیل حساسیت چندبعدی برای یک مشاهده و محدوده ویژگی‌ها
        observation: np.ndarray
        feature_ranges: dict اندیس ویژگی به (min, max)
        n_samples: تعداد نمونه تصادفی برای هر ویژگی
        save_path: مسیر ذخیره تصویر (اختیاری)
        خروجی: dict شامل امتیاز حساسیت، اثرات متقابل و مرزهای ویژگی
        """
        import matplotlib.pyplot as plt
        obs = np.array(observation).copy()
        if len(obs.shape) == 1:
            obs = obs.reshape(1, -1)
        n_features = obs.shape[1]
        sensitivity_scores = {}
        interaction_effects = {}
        feature_boundaries = {}
        # تحلیل حساسیت تک‌ویژگی
        for idx, (fmin, fmax) in feature_ranges.items():
            actions = []
            values = np.linspace(fmin, fmax, n_samples)
            for v in values:
                test_obs = obs.copy()
                test_obs[0, idx] = v
                act, _ = self.model.predict(test_obs)
                actions.append(float(act[0]))
            score = np.std(actions)
            sensitivity_scores[self.feature_names[idx] if self.feature_names else f'feature_{idx}'] = score
            feature_boundaries[self.feature_names[idx] if self.feature_names else f'feature_{idx}'] = (fmin, fmax)
        # تحلیل اثر متقابل دو ویژگی
        idxs = list(feature_ranges.keys())
        if len(idxs) >= 2:
            for i in range(len(idxs)):
                for j in range(i+1, len(idxs)):
                    acts = []
                    for v1 in np.linspace(feature_ranges[idxs[i]][0], feature_ranges[idxs[i]][1], n_samples):
                        for v2 in np.linspace(feature_ranges[idxs[j]][0], feature_ranges[idxs[j]][1], n_samples):
                            test_obs = obs.copy()
                            test_obs[0, idxs[i]] = v1
                            test_obs[0, idxs[j]] = v2
                            act, _ = self.model.predict(test_obs)
                            acts.append(float(act[0]))
                    interaction_effects[f'{self.feature_names[idxs[i]]}-{self.feature_names[idxs[j]]}'] = np.std(acts)
        # رسم نمودار حساسیت
        plt.figure(figsize=(7, 4))
        plt.bar(list(sensitivity_scores.keys()), list(sensitivity_scores.values()), color='orange')
        plt.title('Feature Sensitivity Scores')
        plt.ylabel('Std of Action')
        if save_path:
            plt.savefig(save_path, bbox_inches='tight')
            plt.close()
            fig = None
        else:
            fig = plt.gcf()
        return {
            'sensitivity_scores': sensitivity_scores,
            'interaction_effects': interaction_effects,
            'feature_boundaries': feature_boundaries,
            'fig': fig
        }

    def detect_bias(self, observations, actions, group_indices, save_path=None):
        """
        شناسایی و توضیح سوگیری مدل نسبت به گروه‌های مختلف داده
        observations: np.ndarray
        actions: np.ndarray
        group_indices: dict نام گروه به لیست اندیس نمونه‌ها
        save_path: مسیر ذخیره تصویر (اختیاری)
        خروجی: dict شامل متریک‌های سوگیری، مقایسه گروه‌ها و سوگیری ویژگی
        """
        import matplotlib.pyplot as plt
        bias_metrics = {}
        group_comparisons = {}
        feature_bias = {}
        # مقایسه میانگین و واریانس اقدام در گروه‌ها
        for group, idxs in group_indices.items():
            group_actions = np.array(actions)[idxs]
            bias_metrics[group] = {
                'mean_action': float(np.mean(group_actions)),
                'std_action': float(np.std(group_actions)),
                'count': int(len(group_actions))
            }
        # مقایسه جفتی گروه‌ها
        groups = list(group_indices.keys())
        for i in range(len(groups)):
            for j in range(i+1, len(groups)):
                g1, g2 = groups[i], groups[j]
                mean_diff = abs(bias_metrics[g1]['mean_action'] - bias_metrics[g2]['mean_action'])
                std_diff = abs(bias_metrics[g1]['std_action'] - bias_metrics[g2]['std_action'])
                group_comparisons[f'{g1}_vs_{g2}'] = {
                    'mean_diff': mean_diff,
                    'std_diff': std_diff
                }
        # بررسی سوگیری ویژگی (مثلاً میانگین مقدار ویژگی‌ها در هر گروه)
        for group, idxs in group_indices.items():
            group_obs = np.array(observations)[idxs]
            feature_bias[group] = {self.feature_names[i] if self.feature_names else f'feature_{i}': float(np.mean(group_obs[:, i])) for i in range(group_obs.shape[1])}
        # رسم نمودار مقایسه اقدام گروه‌ها
        plt.figure(figsize=(7, 4))
        means = [bias_metrics[g]['mean_action'] for g in groups]
        stds = [bias_metrics[g]['std_action'] for g in groups]
        plt.bar(groups, means, yerr=stds, color='purple', alpha=0.7)
        plt.ylabel('Mean Action')
        plt.title('Group Action Comparison')
        if save_path:
            plt.savefig(save_path, bbox_inches='tight')
            plt.close()
            fig = None
        else:
            fig = plt.gcf()
        return {
            'bias_metrics': bias_metrics,
            'group_comparisons': group_comparisons,
            'feature_bias': feature_bias,
            'fig': fig
        }

    def advanced_pnl_attribution(self, observations, actions, returns, save_path=None):
        """
        تحلیل پیشرفته سود و زیان و نسبت دادن آن به ویژگی‌ها و شناسایی معاملات افراطی
        observations: np.ndarray
        actions: np.ndarray
        returns: np.ndarray
        save_path: مسیر ذخیره تصویر (اختیاری)
        خروجی: dict شامل breakdown سود/زیان، نسبت‌دهی به ویژگی‌ها و معاملات افراطی
        """
        import matplotlib.pyplot as plt
        pnl_breakdown = {
            'total_pnl': float(np.sum(returns)),
            'mean_pnl': float(np.mean(returns)),
            'std_pnl': float(np.std(returns)),
            'max_pnl': float(np.max(returns)),
            'min_pnl': float(np.min(returns)),
        }
        # نسبت‌دهی سود/زیان به ویژگی‌ها (همبستگی ساده)
        feature_attribution = {}
        for i in range(observations.shape[1]):
            corr = np.corrcoef(observations[:, i], returns)[0, 1]
            feature_attribution[self.feature_names[i] if self.feature_names else f'feature_{i}'] = float(corr)
        # شناسایی معاملات افراطی (بزرگترین سود/زیان)
        extreme_idx = np.argsort(np.abs(returns))[-5:]
        extreme_trades = [{'idx': int(i), 'action': float(actions[i]), 'return': float(returns[i])} for i in extreme_idx]
        # نمودار توزیع سود/زیان
        plt.figure(figsize=(7, 4))
        plt.hist(returns, bins=30, color='green', alpha=0.7)
        plt.title('P&L Distribution')
        plt.xlabel('Return')
        plt.ylabel('Frequency')
        if save_path:
            plt.savefig(save_path, bbox_inches='tight')
            plt.close()
            fig = None
        else:
            fig = plt.gcf()
        return {
            'pnl_breakdown': pnl_breakdown,
            'feature_attribution': feature_attribution,
            'extreme_trades': extreme_trades,
            'fig': fig
        }

    def stress_scenario_analysis(self, observations, actions, returns, scenarios, save_path=None):
        """
        تحلیل سناریوهای استرس با شوک به ویژگی‌ها و بررسی اثر بر اقدامات و بازده
        observations: np.ndarray
        actions: np.ndarray
        returns: np.ndarray
        scenarios: لیست دیکشنری {'name', 'feature_idx', 'shock'}
        save_path: مسیر ذخیره تصویر (اختیاری)
        خروجی: dict شامل نتایج سناریوها و تاثیر کلی
        """
        import matplotlib.pyplot as plt
        scenario_results = {}
        overall_impact = {}
        for scenario in scenarios:
            obs_shocked = observations.copy()
            obs_shocked[:, scenario['feature_idx']] += scenario['shock']
            shocked_actions, _ = self.model.predict(obs_shocked)
            shocked_returns = returns.copy()  # فرض: بازده ثابت، یا می‌توان با مدل جدید محاسبه کرد
            # تغییر میانگین اقدام و بازده
            action_diff = float(np.mean(shocked_actions) - np.mean(actions))
            return_diff = float(np.mean(shocked_returns) - np.mean(returns))
            scenario_results[scenario['name']] = {
                'mean_action': float(np.mean(shocked_actions)),
                'action_diff': action_diff,
                'mean_return': float(np.mean(shocked_returns)),
                'return_diff': return_diff
            }
            overall_impact[scenario['name']] = return_diff
        # نمودار مقایسه سناریوها
        plt.figure(figsize=(7, 4))
        plt.bar(list(overall_impact.keys()), list(overall_impact.values()), color='red', alpha=0.7)
        plt.title('Stress Scenario Impact on Mean Return')
        plt.ylabel('Δ Mean Return')
        if save_path:
            plt.savefig(save_path, bbox_inches='tight')
            plt.close()
            fig = None
        else:
            fig = plt.gcf()
        return {
            'scenario_results': scenario_results,
            'overall_impact': overall_impact,
            'fig': fig
        }

    def decision_memory_analysis(self, observations, actions, window_size=10, save_path=None):
        """
        تحلیل حافظه تصمیمات مدل با کشف الگوهای تکرارشونده و محاسبه memory score
        observations: np.ndarray
        actions: np.ndarray
        window_size: اندازه پنجره برای کشف الگو
        save_path: مسیر ذخیره تصویر (اختیاری)
        خروجی: dict شامل الگوهای حافظه، توالی‌های تکرارشونده و memory score
        """
        import matplotlib.pyplot as plt
        from collections import Counter
        # ساخت دنباله‌های کوتاه از اقدامات
        seqs = [tuple(actions[i:i+window_size]) for i in range(len(actions)-window_size+1)]
        counter = Counter(seqs)
        recurring_sequences = [seq for seq, count in counter.items() if count > 1]
        memory_score = float(len(recurring_sequences) / max(1, len(seqs)))
        # الگوهای حافظه: فراوانی توالی‌های پرتکرار
        memory_patterns = dict(counter.most_common(5))
        # نمودار فراوانی توالی‌ها
        plt.figure(figsize=(7, 4))
        plt.bar(range(len(memory_patterns)), list(memory_patterns.values()), tick_label=[str(k) for k in memory_patterns.keys()])
        plt.title('Top Decision Memory Patterns')
        plt.ylabel('Frequency')
        plt.xticks(rotation=45)
        plt.tight_layout()
        if save_path:
            plt.savefig(save_path, bbox_inches='tight')
            plt.close()
            fig = None
        else:
            fig = plt.gcf()
        return {
            'memory_patterns': memory_patterns,
            'recurring_sequences': recurring_sequences,
            'memory_score': memory_score,
            'fig': fig
        }

    def behavioral_anomaly_detection(self, observations, actions, threshold=3.0, save_path=None):
        """
        شناسایی و توضیح نقاط پرت و رفتار غیرعادی مدل با استفاده از آستانه و تحلیل ویژگی‌ها
        observations: np.ndarray
        actions: np.ndarray
        threshold: آستانه تشخیص انحراف (بر حسب انحراف معیار)
        save_path: مسیر ذخیره تصویر (اختیاری)
        خروجی: dict شامل لیست نقاط پرت، امتیاز انحراف و توضیح
        """
        import matplotlib.pyplot as plt
        actions = np.array(actions)
        mean = np.mean(actions)
        std = np.std(actions)
        anomaly_scores = (actions - mean) / (std + 1e-8)
        anomalies = np.where(np.abs(anomaly_scores) > threshold)[0].tolist()
        explanation = f"تعداد نقاط پرت: {len(anomalies)} (آستانه: {threshold}σ)"
        # نمودار نقاط پرت
        plt.figure(figsize=(8, 4))
        plt.plot(actions, label='Actions')
        plt.scatter(anomalies, actions[anomalies], color='red', label='Anomaly', zorder=5)
        plt.axhline(mean + threshold*std, color='orange', linestyle='--', label='+Threshold')
        plt.axhline(mean - threshold*std, color='orange', linestyle='--', label='-Threshold')
        plt.title('Behavioral Anomaly Detection')
        plt.legend()
        if save_path:
            plt.savefig(save_path, bbox_inches='tight')
            plt.close()
            fig = None
        else:
            fig = plt.gcf()
        return {
            'anomalies': anomalies,
            'anomaly_scores': anomaly_scores.tolist(),
            'explanation': explanation,
            'fig': fig
        }

    def generate_nlg_explanation(self, observation, action=None, top_n=3):
        """
        تولید توضیح زبانی هوشمند (NLG) برای تصمیم مدل بر اساس مشاهده و اقدام
        
        پارامترها:
        -----------
        observation : np.ndarray
            مشاهده ورودی به مدل
        action : np.ndarray, optional
            اقدام انجام شده توسط مدل (اگر None باشد، از مدل پیش‌بینی می‌شود)
        top_n : int, default=3
            تعداد ویژگی مهم برای ذکر در توضیح
        
        خروجی:
        -------
        str
            توضیح زبانی قابل فهم برای انسان
        """
        # اطمینان از اینکه action آرایه با ابعاد مناسب است
        if action is not None and (np.isscalar(action) or (isinstance(action, np.ndarray) and action.ndim == 0)):
            action = np.array([[action]])
        elif action is not None and isinstance(action, np.ndarray) and action.ndim == 1:
            action = action.reshape(-1, 1)
        explanation = self.decision_explanation(observation, action)
        action_val = explanation['action']
        top_features = explanation['top_features'][:top_n]
        features_str = '، '.join([f"{name} ({importance:.2f})" for name, importance in top_features])
        nlg_text = f"مدل تصمیم گرفت اقدام {action_val} را انجام دهد، زیرا ویژگی‌های {features_str} بیشترین تاثیر را داشتند."
        return nlg_text

    def visualize_decision_flow(self, observation, action=None, save_path=None):
        """
        رسم نمودار جریان تصمیم مدل برای یک مشاهده و اقدام
        
        پارامترها:
        -----------
        observation : np.ndarray
            مشاهده ورودی به مدل
        action : np.ndarray, optional
            اقدام انجام شده توسط مدل (اگر None باشد، از مدل پیش‌بینی می‌شود)
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار نمایش داده می‌شود)
        
        خروجی:
        -------
        matplotlib.figure.Figure
            شیء نمودار
        """
        import matplotlib.pyplot as plt
        from matplotlib.patches import FancyBboxPatch
        
        explanation = self.decision_explanation(observation, action)
        action_val = explanation['action']
        top_features = explanation['top_features']
        
        steps = [
            "دریافت ورودی",
            "محاسبه اهمیت ویژگی‌ها",
            f"انتخاب اقدام: {action_val}",
            "تولید توضیح"
        ]
        
        fig, ax = plt.subplots(figsize=(7, 3))
        ax.axis('off')
        y = 0.5
        for i, step in enumerate(steps):
            box = FancyBboxPatch((i*2, y), 1.8, 0.5, boxstyle="round,pad=0.2", edgecolor='navy', facecolor='#e0eaff')
            ax.add_patch(box)
            ax.text(i*2+0.9, y+0.25, step, ha='center', va='center', fontsize=12)
            if i < len(steps)-1:
                ax.annotate('', xy=(i*2+1.8, y+0.25), xytext=(i*2+2, y+0.25), arrowprops=dict(arrowstyle="->", lw=2, color='navy'))
        ax.set_xlim(-0.5, len(steps)*2-0.5)
        ax.set_ylim(0, 1.5)
        plt.tight_layout()
        if save_path:
            plt.savefig(save_path)
            plt.close()
            return None
        else:
            return fig

    def interactive_decision_space(self, observations, actions=None, feature_idx=[0, 1], save_path=None):
        """
        نمایش تعاملی فضای تصمیم مدل با استفاده از دو ویژگی اصلی و رنگ‌بندی بر اساس اقدام
        
        پارامترها:
        -----------
        observations : np.ndarray
            مشاهدات ورودی به مدل
        actions : np.ndarray, optional
            اقدامات مدل (اگر None باشد، با مدل پیش‌بینی می‌شود)
        feature_idx : list, default=[0,1]
            اندیس دو ویژگی برای نمایش روی محورهای x و y
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار به صورت تعاملی نمایش داده می‌شود)
        
        خروجی:
        -------
        plotly.graph_objects.Figure
            شیء نمودار تعاملی
        """
        import numpy as np
        import plotly.express as px
        observations = np.array(observations)
        if actions is None:
            if hasattr(self.model, 'predict'):
                actions, _ = self.model.predict(observations, deterministic=True)
            else:
                raise ValueError("مدل باید متد predict داشته باشد یا actions باید مشخص شود.")
        x = observations[:, feature_idx[0]]
        y = observations[:, feature_idx[1]]
        fig = px.scatter(x=x, y=y, color=actions.astype(str), labels={'color': 'اقدام'},
                         title='فضای تصمیم مدل (نمایش تعاملی)')
        if save_path:
            fig.write_html(save_path)
            return None
        else:
            fig.show()
            return fig

    def expert_comparison(self, observations, expert_actions, model_actions=None, save_path=None):
        """
        مقایسه تصمیمات مدل با تصمیمات معامله‌گر خبره
        
        پارامترها:
        -----------
        observations : np.ndarray
            مشاهدات ورودی به مدل
        expert_actions : np.ndarray
            اقدامات معامله‌گر خبره
        model_actions : np.ndarray, optional
            اقدامات مدل (اگر None باشد، با مدل پیش‌بینی می‌شود)
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار نمایش داده می‌شود)
        
        خروجی:
        -------
        dict
            شامل درصد تطابق و نمودار مقایسه‌ای
        """
        import numpy as np
        import matplotlib.pyplot as plt
        observations = np.array(observations)
        expert_actions = np.array(expert_actions)
        if model_actions is None:
            if hasattr(self.model, 'predict'):
                model_actions, _ = self.model.predict(observations, deterministic=True)
            else:
                raise ValueError("مدل باید متد predict داشته باشد یا model_actions باید مشخص شود.")
        model_actions = np.array(model_actions)
        match = (model_actions == expert_actions)
        match_percent = 100 * np.mean(match)
        # نمودار مقایسه‌ای
        fig, ax = plt.subplots(figsize=(8, 3))
        ax.plot(model_actions, label='مدل')
        ax.plot(expert_actions, label='معامله‌گر خبره', linestyle='--')
        ax.set_title(f'درصد تطابق: {match_percent:.1f}%')
        ax.legend()
        plt.tight_layout()
        if save_path:
            plt.savefig(save_path)
            plt.close()
        else:
            plt.show()
        return {'match_percent': match_percent, 'fig': fig}

    def news_impact_analysis(self, observations, actions=None, news_events=None, window=5, save_path=None):
        """
        تحلیل اثر اخبار بر تصمیمات مدل
        
        پارامترها:
        -----------
        observations : np.ndarray
            مشاهدات ورودی به مدل
        actions : np.ndarray, optional
            اقدامات مدل (اگر None باشد، با مدل پیش‌بینی می‌شود)
        news_events : np.ndarray or list
            آرایه باینری یا عددی (0/1 یا True/False) که وقوع خبر را در هر بازه نشان می‌دهد
        window : int, default=5
            تعداد بازه قبل و بعد از خبر برای تحلیل
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار نمایش داده می‌شود)
        
        خروجی:
        -------
        dict
            شامل آمار تصمیمات قبل/بعد/حین اخبار و نمودار مقایسه‌ای
        """
        import numpy as np
        import matplotlib.pyplot as plt
        observations = np.array(observations)
        if actions is None:
            if hasattr(self.model, 'predict'):
                actions, _ = self.model.predict(observations, deterministic=True)
            else:
                raise ValueError("مدل باید متد predict داشته باشد یا actions باید مشخص شود.")
        actions = np.array(actions)
        news_events = np.array(news_events).astype(bool)
        n = len(actions)
        before, during, after = [], [], []
        for i in range(n):
            if news_events[i]:
                # حین خبر
                during.append(actions[i])
                # قبل خبر
                if i-window >= 0:
                    before.extend(actions[i-window:i])
                # بعد خبر
                if i+1+window <= n:
                    after.extend(actions[i+1:i+1+window])
        stats = {
            'before': np.mean(before) if before else None,
            'during': np.mean(during) if during else None,
            'after': np.mean(after) if after else None,
            'count_news': int(np.sum(news_events)),
            'count_total': n
        }
        # نمودار
        fig, ax = plt.subplots(figsize=(6, 4))
        labels = ['قبل خبر', 'حین خبر', 'بعد خبر']
        values = [stats['before'], stats['during'], stats['after']]
        ax.bar(labels, values, color=['#aaa', '#f99', '#9f9'])
        ax.set_ylabel('میانگین اقدام مدل')
        ax.set_title('تحلیل اثر اخبار بر تصمیمات مدل')
        plt.tight_layout()
        if save_path:
            plt.savefig(save_path)
            plt.close()
        else:
            plt.show()
        return {'stats': stats, 'fig': fig}

    def market_impact_analysis(self, observations, actions=None, market_data=None, save_path=None):
        """
        تحلیل تاثیر معاملات مدل بر بازار (market impact)
        
        پارامترها:
        -----------
        observations : np.ndarray
            مشاهدات ورودی به مدل
        actions : np.ndarray, optional
            اقدامات مدل (اگر None باشد، با مدل پیش‌بینی می‌شود)
        market_data : pd.DataFrame
            داده‌های بازار شامل ستون‌های قیمت (close) و حجم (volume)
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار نمایش داده می‌شود)
        
        خروجی:
        -------
        dict
            شامل همبستگی اقدام و تغییر قیمت/حجم و نمودار
        """
        import numpy as np
        import matplotlib.pyplot as plt
        import pandas as pd
        observations = np.array(observations)
        if actions is None:
            if hasattr(self.model, 'predict'):
                actions, _ = self.model.predict(observations, deterministic=True)
            else:
                raise ValueError("مدل باید متد predict داشته باشد یا actions باید مشخص شود.")
        actions = np.array(actions)
        if market_data is None or 'close' not in market_data.columns or 'volume' not in market_data.columns:
            raise ValueError("market_data باید شامل ستون‌های 'close' و 'volume' باشد.")
        price_change = market_data['close'].pct_change().fillna(0).values
        volume = market_data['volume'].values
        corr_price = np.corrcoef(actions, price_change)[0, 1]
        corr_volume = np.corrcoef(actions, volume)[0, 1]
        # نمودار
        fig, ax = plt.subplots(1, 2, figsize=(10, 4))
        ax[0].scatter(actions, price_change, alpha=0.5)
        ax[0].set_xlabel('اقدام مدل')
        ax[0].set_ylabel('تغییر قیمت')
        ax[0].set_title(f'همبستگی اقدام-قیمت: {corr_price:.2f}')
        ax[1].scatter(actions, volume, alpha=0.5, color='orange')
        ax[1].set_xlabel('اقدام مدل')
        ax[1].set_ylabel('حجم')
        ax[1].set_title(f'همبستگی اقدام-حجم: {corr_volume:.2f}')
        plt.tight_layout()
        if save_path:
            plt.savefig(save_path)
            plt.close()
        else:
            plt.show()
        return {'corr_price': corr_price, 'corr_volume': corr_volume, 'fig': fig}

    def decision_lag_analysis(self, observations, actions=None, timestamps=None, save_path=None):
        """
        تحلیل تاخیر تصمیم مدل (decision lag)
        
        پارامترها:
        -----------
        observations : np.ndarray
            مشاهدات ورودی به مدل
        actions : np.ndarray, optional
            اقدامات مدل (اگر None باشد، با مدل پیش‌بینی می‌شود)
        timestamps : np.ndarray or list
            آرایه زمانی (datetime یا عددی) متناظر با هر مشاهده
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار نمایش داده می‌شود)
        
        خروجی:
        -------
        dict
            شامل میانگین و توزیع تاخیر و نمودار
        """
        import numpy as np
        import matplotlib.pyplot as plt
        import pandas as pd
        observations = np.array(observations)
        if actions is None:
            if hasattr(self.model, 'predict'):
                actions, _ = self.model.predict(observations, deterministic=True)
            else:
                raise ValueError("مدل باید متد predict داشته باشد یا actions باید مشخص شود.")
        if timestamps is None:
            raise ValueError("timestamps باید ارائه شود.")
        timestamps = pd.to_datetime(timestamps)
        # فرض: تاخیر تصمیم به صورت فاصله زمانی بین تغییر اقدام مدل و زمان ثبت شده است
        action_change_idx = np.where(np.diff(actions) != 0)[0] + 1
        lags = np.diff(timestamps[action_change_idx]) / np.timedelta64(1, 's')
        mean_lag = np.mean(lags) if len(lags) > 0 else None
        # نمودار
        fig, ax = plt.subplots(figsize=(6, 4))
        ax.hist(lags, bins=20, color='#69c', alpha=0.7)
        ax.set_xlabel('تاخیر تصمیم (ثانیه)')
        ax.set_ylabel('تعداد')
        ax.set_title('توزیع تاخیر تصمیم مدل')
        plt.tight_layout()
        if save_path:
            plt.savefig(save_path)
            plt.close()
        else:
            plt.show()
        return {'mean_lag': mean_lag, 'lags': lags, 'fig': fig}

    def explanation_quality_assessment(self, observations, actions=None, explanations=None, save_path=None):
        """
        ارزیابی کیفیت توضیحات مدل (explanation quality)
        
        پارامترها:
        -----------
        observations : np.ndarray
            مشاهدات ورودی به مدل
        actions : np.ndarray, optional
            اقدامات مدل (اگر None باشد، با مدل پیش‌بینی می‌شود)
        explanations : list of str, optional
            لیست توضیحات متنی (اگر None باشد، با generate_nlg_explanation تولید می‌شود)
        save_path : str, optional
            مسیر ذخیره نمودار (اگر None باشد، نمودار نمایش داده می‌شود)
        
        خروجی:
        -------
        dict
            شامل معیارهای کیفیت و نمودار
        """
        import numpy as np
        import matplotlib.pyplot as plt
        from collections import Counter
        observations = np.array(observations)
        if actions is None:
            if hasattr(self.model, 'predict'):
                actions, _ = self.model.predict(observations, deterministic=True)
            else:
                raise ValueError("مدل باید متد predict داشته باشد یا actions باید مشخص شود.")
        if explanations is None:
            explanations = [self.generate_nlg_explanation(obs, act) for obs, act in zip(observations, actions)]
        # معیار سادگی: میانگین طول جملات
        lengths = [len(str(e).split()) for e in explanations]
        simplicity = np.mean(lengths)
        # معیار پوشش: نسبت توضیحات غیرتکراری به کل
        unique_expl = len(set(explanations)) / len(explanations)
        # معیار یکنواختی: توزیع فراوانی توضیحات
        counts = Counter(explanations)
        uniformity = np.std(list(counts.values())) / np.mean(list(counts.values())) if len(counts) > 1 else 0
        # نمودار
        fig, ax = plt.subplots(figsize=(7, 4))
        ax.bar(['سادگی', 'پوشش', 'یکنواختی'], [simplicity, unique_expl, 1-uniformity])
        ax.set_ylabel('امتیاز')
        ax.set_title('ارزیابی کیفیت توضیحات مدل')
        plt.tight_layout()
        if save_path:
            plt.savefig(save_path)
            plt.close()
        else:
            plt.show()
        return {'simplicity': simplicity, 'coverage': unique_expl, 'uniformity': 1-uniformity, 'fig': fig}