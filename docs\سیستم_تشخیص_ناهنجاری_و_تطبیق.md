# مستندات سیستم تشخیص ناهنجاری و تطبیق (AnomalyDetectionSystem)

## 📋 معرفی کلی

سیستم **AnomalyDetectionSystem** یکی از ماژول‌های پیشرفته پروژه است که شرایط غیرعادی بازار را تشخیص داده و استراتژی‌ها را متناسب با آن تطبیق می‌دهد. این سیستم از تکنیک‌های یادگیری ماشین مانند Isolation Forest و DBSCAN برای تشخیص ناهنجاری‌ها استفاده می‌کند.

## 🎯 مسئولیت‌های اصلی

### 1. تشخیص ناهنجاری‌ها
- تشخیص شرایط غیرعادی بازار
- طبقه‌بندی انواع ناهنجاری‌ها
- محاسبه شدت ناهنجاری

### 2. استخراج ویژگی‌های بازار
- تحلیل ویژگی‌های قیمت
- بررسی الگوهای حجم
- محاسبه متریک‌های نوسان

### 3. تطبیق استراتژی
- تعیین اقدامات مناسب
- اجرای قوانین تطبیق
- ذخیره تاریخچه تطبیق

## 🔧 کلاس‌های اصلی

### 1. AnomalyEvent
```python
@dataclass
class AnomalyEvent:
    timestamp: datetime
    anomaly_type: str
    severity: float  # 0-1
    affected_symbols: List[str]
    market_features: Dict[str, float]
    description: str
    adaptation_actions: List[str]
```

### 2. MarketFeatureExtractor
```python
class MarketFeatureExtractor:
    def __init__(self):
        self.feature_history = []
        self.scaler = StandardScaler()
        self.is_fitted = False
    
    def extract_features(self, market_data: Dict) -> Dict[str, float]:
        """استخراج ویژگی‌های بازار"""
```

### 3. AnomalyDetector
```python
class AnomalyDetector:
    def __init__(self, contamination: float = 0.1):
        self.contamination = contamination
        self.isolation_forest = IsolationForest(contamination=contamination)
        self.dbscan = DBSCAN(eps=0.5, min_samples=5)
```

### 4. AdaptationEngine
```python
class AdaptationEngine:
    def __init__(self, db_path: str = "anomaly_adaptation.db"):
        self.db_path = db_path
        self.adaptation_rules = {}
        self.adaptation_history = []
```

## 📊 متدهای اصلی

### 1. extract_features()
```python
def extract_features(self, market_data: Dict) -> Dict[str, float]:
    """
    استخراج ویژگی‌های بازار
    
    پارامترها:
    - market_data: داده‌های بازار
    
    خروجی:
    - Dict[str, float]: ویژگی‌های استخراج شده
    """
```

### 2. detect_anomalies()
```python
def detect_anomalies(self, current_features: Dict[str, float]) -> Tuple[bool, float, str]:
    """
    تشخیص ناهنجاری در ویژگی‌های فعلی
    
    پارامترها:
    - current_features: ویژگی‌های فعلی
    
    خروجی:
    - Tuple[bool, float, str]: (وجود ناهنجاری، احتمال، نوع)
    """
```

### 3. process_anomaly()
```python
def process_anomaly(self, anomaly_event: AnomalyEvent) -> List[str]:
    """
    پردازش ناهنجاری و تعیین اقدامات تطبیقی
    
    پارامترها:
    - anomaly_event: رویداد ناهنجاری
    
    خروجی:
    - List[str]: اقدامات تطبیقی
    """
```

## 🎨 انواع ناهنجاری‌ها

### 1. ناهنجاری نوسان بالا (High Volatility)
```python
if features.get('price_volatility', 0) > 0.05:
    return "high_volatility"
```

### 2. ناهنجاری حجم (Volume Spike)
```python
if features.get('volume_spike_ratio', 1) > 5:
    return "volume_spike"
```

### 3. ناهنجاری همبستگی (Correlation Breakdown)
```python
if features.get('correlation_breakdown', 0) > 0.5:
    return "correlation_breakdown"
```

### 4. ناهنجاری اقتصادی (Economic Shock)
```python
if features.get('economic_surprise_index', 0) > 2:
    return "economic_shock"
```

## 🔧 قوانین تطبیق پیش‌فرض

### 1. قانون نوسان بالا
```python
AdaptationRule(
    rule_id="high_volatility_rule",
    anomaly_type="high_volatility",
    adaptation_actions=[
        "reduce_position_size",
        "increase_stop_loss",
        "reduce_leverage",
        "switch_to_defensive_strategy"
    ]
)
```

### 2. قانون جهش حجم
```python
AdaptationRule(
    rule_id="volume_spike_rule",
    anomaly_type="volume_spike",
    adaptation_actions=[
        "pause_trading",
        "wait_for_volume_normalization",
        "reduce_order_size"
    ]
)
```

## 📈 نمونه کد استفاده

```python
from utils.anomaly_detection_system import AnomalyDetectionSystem
import numpy as np

# ایجاد سیستم
system = AnomalyDetectionSystem("anomaly_system.db")

# داده‌های تاریخی برای آموزش
historical_data = [
    {'price_data': {'prices': [100, 101, 102]}, 'volume_data': {'volumes': [1000, 1100, 1200]}},
    # ... سایر داده‌ها
]

# راه‌اندازی سیستم
system.initialize(historical_data)

# تحلیل داده‌های فعلی
current_data = {
    'price_data': {'prices': [100, 105, 110]},
    'volume_data': {'volumes': [1000, 5000, 6000]},
    'volatility_data': {'volatilities': [0.02, 0.08, 0.12]}
}

# تشخیص ناهنجاری
anomaly_event = system.analyze_market_data(current_data, ['EURUSD', 'GBPUSD'])

if anomaly_event:
    print(f"ناهنجاری تشخیص داده شد: {anomaly_event.anomaly_type}")
    print(f"شدت: {anomaly_event.severity:.2f}")
    print(f"اقدامات پیشنهادی: {anomaly_event.adaptation_actions}")
else:
    print("شرایط عادی بازار")

# وضعیت سیستم
status = system.get_system_status()
print("وضعیت سیستم:", status)
```

## 🔧 تنظیمات و پیکربندی

### پارامترهای مهم:
- `contamination`: نسبت ناهنجاری در داده‌ها (0.1)
- `eps`: شعاع برای DBSCAN (0.5)
- `min_samples`: حداقل نمونه‌ها برای DBSCAN (5)
- `anomaly_threshold`: آستانه تشخیص ناهنجاری (0.7)

### ویژگی‌های استخراج شده:
1. **ویژگی‌های قیمت**: نوسان، چولگی، کشیدگی، قدرت ترند
2. **ویژگی‌های حجم**: نوسان حجم، ترند حجم، نسبت جهش
3. **ویژگی‌های نوسان**: رژیم نوسان، خوشه‌بندی، پایداری
4. **ویژگی‌های همبستگی**: میانگین همبستگی، ناپایداری

## 🚨 مدیریت خطا

```python
try:
    anomaly_event = system.analyze_market_data(current_data, symbols)
    if anomaly_event:
        # پردازش ناهنجاری
        actions = system.adaptation_engine.process_anomaly(anomaly_event)
except Exception as e:
    print(f"خطا در تشخیص ناهنجاری: {e}")
    # اقدامات پیش‌فرض
    actions = ["monitor_situation"]
```

## 📊 متریک‌های عملکرد

### 1. دقت تشخیص
- نرخ تشخیص درست
- نرخ هشدار کاذب
- حساسیت و ویژگی

### 2. کارایی تطبیق
- موفقیت قوانین تطبیق
- زمان واکنش
- تأثیر بر عملکرد

## 🔗 اتصال به سیستم اصلی

### فایل‌های مرتبط:
- `utils/anomaly_detection_system.py`: پیاده‌سازی اصلی
- `tests/test_complete_advanced_system.py`: تست‌های پیشرفته
- `utils/data_cleaning.py`: تمیزسازی داده‌ها
- `utils/explainable_ai.py`: تشخیص ناهنجاری رفتاری

### وضعیت عملیاتی:
⚠️ **نیمه‌فعال** - این ماژول در تست‌های پیشرفته استفاده می‌شود اما هنوز به طور کامل در سیستم اصلی ادغام نشده

## 🎯 بهترین شیوه‌های استفاده

### 1. آموزش مدل
```python
# داده‌های کافی برای آموزش
if len(historical_data) >= 100:
    system.initialize(historical_data)
else:
    print("داده‌های تاریخی کافی نیست")
```

### 2. تنظیم آستانه‌ها
```python
# تنظیم بر اساس حساسیت مطلوب
detector = AnomalyDetector(contamination=0.05)  # حساسیت بالا
detector = AnomalyDetector(contamination=0.15)  # حساسیت کم
```

### 3. مدیریت قوانین تطبیق
```python
# اضافه کردن قانون جدید
new_rule = AdaptationRule(
    rule_id="custom_rule",
    anomaly_type="custom_anomaly",
    adaptation_actions=["custom_action"]
)
engine.adaptation_rules["custom_rule"] = new_rule
```

## 🔮 نمودار معماری

```mermaid
graph TD
    A[AnomalyDetectionSystem] --> B[MarketFeatureExtractor]
    A --> C[AnomalyDetector]
    A --> D[AdaptationEngine]
    
    B --> E[Price Features]
    B --> F[Volume Features]
    B --> G[Volatility Features]
    B --> H[Correlation Features]
    
    C --> I[Isolation Forest]
    C --> J[DBSCAN]
    C --> K[Anomaly Classification]
    
    D --> L[Adaptation Rules]
    D --> M[Action Selection]
    D --> N[History Tracking]
    
    K --> O[High Volatility]
    K --> P[Volume Spike]
    K --> Q[Correlation Breakdown]
    K --> R[Economic Shock]
```

## 📚 الگوریتم‌های استفاده شده

### 1. Isolation Forest
- تشخیص ناهنجاری بر اساس ایزولاسیون
- مناسب برای داده‌های چندبعدی
- عملکرد سریع

### 2. DBSCAN
- خوشه‌بندی مبتنی بر تراکم
- تشخیص نقاط پرت
- عدم نیاز به تعداد خوشه‌ها

### 3. Statistical Methods
- Z-Score برای تشخیص نقاط پرت
- IQR برای تشخیص ناهنجاری‌ها
- Rolling Statistics

---

**نکته**: این سیستم برای تشخیص و مدیریت شرایط غیرعادی بازار طراحی شده و می‌تواند به بهبود مقاومت سیستم معاملاتی کمک کند. 