import logging
from utils.logger import get_logger

def test_logger_info_level(caplog):
    logger = get_logger("test_logger")
    with caplog.at_level(logging.INFO):
        logger.info("test info message")
    assert "test info message" in caplog.text

def test_logger_error_level(caplog):
    logger = get_logger("test_logger")
    with caplog.at_level(logging.ERROR):
        logger.error("test error message")
    assert "test error message" in caplog.text
