"""
🤗 Test HuggingFace Sentiment Models Integration
تست ادغام مدل‌های احساسات HuggingFace

این تست شامل:
1. تست FinBERT Model
2. تست Financial RoBERTa Model
3. تست FinGPT Model (اختیاری)
4. تست Ensemble Model
5. تست HuggingFace Manager
"""

import os
import sys
import time
import json
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_finbert_model():
    """تست مدل FinBERT"""
    print("🤗 Testing Enhanced FinBERT Model...")
    
    try:
        from ai_models.sentiment_models import EnhancedFinBERTModel
        
        # Initialize FinBERT
        finbert = EnhancedFinBERTModel()
        print(f"✅ FinBERT initialized: {finbert.model_info['name']}")
        
        # Load model
        print("🔄 Loading FinBERT model (this may take a few minutes for first download)...")
        load_success = finbert.load()
        
        if load_success:
            print("✅ FinBERT loaded successfully")
            
            # Test sentiment analysis
            test_texts = [
                "The market is showing strong bullish signals today",
                "Economic uncertainty is causing market volatility",
                "The company reported excellent quarterly earnings"
            ]
            
            for text in test_texts:
                result = finbert.analyze_sentiment(text)
                print(f"  📊 Text: {text[:50]}...")
                print(f"      Sentiment: {result.sentiment} (confidence: {result.confidence:.3f})")
                print(f"      Processing time: {result.processing_time:.3f}s")
            
            return True
        else:
            print("❌ Failed to load FinBERT model")
            return False
            
    except Exception as e:
        print(f"❌ FinBERT test failed: {e}")
        return False

def test_roberta_model():
    """تست مدل Financial RoBERTa"""
    print("\n🤖 Testing Financial RoBERTa Model...")
    
    try:
        from ai_models.sentiment_models import FinancialRoBERTaModel
        
        # Initialize RoBERTa
        roberta = FinancialRoBERTaModel()
        print(f"✅ RoBERTa initialized: {roberta.model_info['name']}")
        
        # Load model
        print("🔄 Loading Financial RoBERTa model...")
        load_success = roberta.load()
        
        if load_success:
            print("✅ Financial RoBERTa loaded successfully")
            
            # Test sentiment analysis
            test_texts = [
                "Stock prices are rising due to positive earnings reports",
                "The Federal Reserve's decision may impact market stability"
            ]
            
            for text in test_texts:
                result = roberta.analyze_sentiment(text)
                print(f"  📊 Text: {text[:50]}...")
                print(f"      Sentiment: {result.sentiment} (confidence: {result.confidence:.3f})")
                print(f"      Probabilities: {result.probabilities}")
            
            return True
        else:
            print("❌ Failed to load Financial RoBERTa model")
            return False
            
    except Exception as e:
        print(f"❌ Financial RoBERTa test failed: {e}")
        return False

def test_fingpt_model():
    """تست مدل FinGPT (اختیاری)"""
    print("\n🧠 Testing FinGPT Model (optional)...")
    
    try:
        from ai_models.sentiment_models import FinGPTSentimentModel
        
        # Initialize FinGPT
        fingpt = FinGPTSentimentModel()
        print(f"✅ FinGPT initialized: {fingpt.model_info['name']}")
        
        # Load model (might fail if not available)
        print("🔄 Loading FinGPT model (might not be available)...")
        load_success = fingpt.load()
        
        if load_success:
            print("✅ FinGPT loaded successfully")
            
            # Test sentiment analysis
            test_text = "The cryptocurrency market is experiencing significant growth"
            result = fingpt.analyze_sentiment(test_text)
            print(f"  📊 Text: {test_text}")
            print(f"      Sentiment: {result.sentiment} (confidence: {result.confidence:.3f})")
            
            return True
        else:
            print("⚠️ FinGPT model not available (this is expected)")
            return True  # Not a failure, just not available
            
    except Exception as e:
        print(f"⚠️ FinGPT test skipped: {e}")
        return True  # Not a critical failure

def test_ensemble_model():
    """تست مدل Ensemble"""
    print("\n🎯 Testing Ensemble Sentiment Model...")
    
    try:
        from ai_models.sentiment_models import EnsembleSentimentModel
        
        # Initialize ensemble
        ensemble = EnsembleSentimentModel()
        print("✅ Ensemble model initialized")
        
        # Load all models
        print("🔄 Loading ensemble models...")
        load_results = ensemble.load_all_models()
        
        successful_models = sum(1 for success in load_results.values() if success)
        print(f"📊 Ensemble status: {successful_models}/3 models loaded")
        
        if successful_models >= 1:
            print("✅ Ensemble ready (at least one model loaded)")
            
            # Test ensemble analysis
            test_text = "The financial markets are showing positive momentum with strong investor confidence"
            
            # Simple ensemble analysis
            result = ensemble.analyze_sentiment(test_text)
            print(f"  🎯 Ensemble Result:")
            print(f"      Text: {test_text}")
            print(f"      Sentiment: {result.sentiment} (confidence: {result.confidence:.3f})")
            print(f"      Probabilities: {result.probabilities}")
            
            # Detailed analysis
            detailed = ensemble.get_detailed_analysis(test_text)
            print(f"  📊 Individual Model Results:")
            for model_name, model_result in detailed["individual_results"].items():
                if "error" not in model_result:
                    print(f"      {model_name}: {model_result['sentiment']} ({model_result['confidence']:.3f})")
                else:
                    print(f"      {model_name}: Error - {model_result['error']}")
            
            return True
        else:
            print("❌ No models loaded in ensemble")
            return False
            
    except Exception as e:
        print(f"❌ Ensemble test failed: {e}")
        return False

def test_huggingface_manager():
    """تست مدیر HuggingFace"""
    print("\n🤗 Testing HuggingFace Sentiment Manager...")
    
    try:
        from ai_models.sentiment_models import HuggingFaceSentimentManager
        
        # Initialize manager
        manager = HuggingFaceSentimentManager()
        print("✅ HuggingFace Manager initialized")
        
        # Initialize all models
        print("🔄 Initializing all sentiment models...")
        summary = manager.initialize_all_models()
        
        print(f"📊 Initialization Summary:")
        print(f"    Models loaded: {summary['models_loaded']}/{summary['total_models']}")
        print(f"    Ensemble available: {summary['ensemble_available']}")
        print(f"    Cache directory: {summary['cache_directory']}")
        
        if summary["initialization_successful"]:
            print("✅ Manager initialization successful")
            
            # Test best model analysis
            test_text = "The stock market rally continues with strong corporate earnings"
            result = manager.analyze_with_best_model(test_text)
            
            print(f"  🎯 Best Model Analysis:")
            print(f"      Text: {test_text}")
            print(f"      Model: {result.model_name}")
            print(f"      Sentiment: {result.sentiment} (confidence: {result.confidence:.3f})")
            
            # Get model status
            status = manager.get_model_status()
            print(f"  📊 Model Status:")
            print(f"      Ensemble available: {status['ensemble_model']['available']}")
            for model_name, model_status in status['individual_models'].items():
                print(f"      {model_name}: {'✅' if model_status['available'] else '❌'}")
            
            return True
        else:
            print("❌ Manager initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ HuggingFace Manager test failed: {e}")
        return False

def test_performance_comparison():
    """تست مقایسه عملکرد"""
    print("\n⚡ Testing Performance Comparison...")
    
    try:
        from ai_models.sentiment_models import HuggingFaceSentimentManager
        
        manager = HuggingFaceSentimentManager()
        summary = manager.initialize_all_models()
        
        if not summary["initialization_successful"]:
            print("⚠️ Skipping performance test - no models loaded")
            return True
        
        # Test texts for performance comparison
        test_texts = [
            "The market is bullish today with strong gains",
            "Economic uncertainty is affecting investor sentiment",
            "Company earnings exceeded expectations significantly",
            "The Federal Reserve's policy decision impacts markets",
            "Cryptocurrency adoption is growing rapidly"
        ]
        
        print(f"🔄 Testing performance on {len(test_texts)} texts...")
        
        total_time = 0
        results = []
        
        for text in test_texts:
            start_time = time.time()
            result = manager.analyze_with_best_model(text)
            processing_time = time.time() - start_time
            total_time += processing_time
            
            results.append({
                "text": text[:50] + "...",
                "sentiment": result.sentiment,
                "confidence": result.confidence,
                "time": processing_time
            })
        
        # Performance summary
        avg_time = total_time / len(test_texts)
        print(f"📊 Performance Results:")
        print(f"    Total time: {total_time:.3f}s")
        print(f"    Average time per text: {avg_time:.3f}s")
        print(f"    Texts per second: {1/avg_time:.1f}")
        
        # Show some results
        print(f"  🎯 Sample Results:")
        for result in results[:3]:
            print(f"    {result['text']}: {result['sentiment']} ({result['confidence']:.3f}) - {result['time']:.3f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def main():
    """اجرای تمام تست‌ها"""
    print("🤗 HUGGINGFACE SENTIMENT MODELS TESTS")
    print("=" * 70)
    
    tests = [
        ("Enhanced FinBERT Model", test_finbert_model),
        ("Financial RoBERTa Model", test_roberta_model),
        ("FinGPT Model (Optional)", test_fingpt_model),
        ("Ensemble Sentiment Model", test_ensemble_model),
        ("HuggingFace Manager", test_huggingface_manager),
        ("Performance Comparison", test_performance_comparison)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 HUGGINGFACE SENTIMENT MODELS TEST RESULTS")
    print("=" * 70)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    success_rate = (passed / total) * 100
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Success Rate: {success_rate:.1f}% ({passed}/{total})")
    
    if success_rate >= 80:
        print("🎉 HuggingFace Sentiment Models integration is working excellently!")
        print("🚀 Ready for production sentiment analysis!")
    elif success_rate >= 60:
        print("⚠️ HuggingFace integration mostly working, minor issues remain")
    else:
        print("❌ HuggingFace integration needs significant improvements")
    
    # Save results
    results_file = f"huggingface_sentiment_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            "test_results": results,
            "success_rate": success_rate,
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total,
                "passed_tests": passed,
                "failed_tests": total - passed
            }
        }, f, indent=2, ensure_ascii=False)
    
    print(f"📄 Detailed results saved to: {results_file}")
    
    return success_rate

if __name__ == "__main__":
    success_rate = main()
    exit(0 if success_rate >= 60 else 1)  # Lower threshold due to optional models
