#!/usr/bin/env python3
"""
🔧 Google Drive Setup for Google Colab
💾 تنظیم Google Drive برای ذخیره‌سازی دائمی
"""

import os
import sys
from pathlib import Path

def mount_google_drive():
    """🔧 Mount Google Drive in Google Colab"""
    try:
        from google.colab import drive
        print("🔧 Mounting Google Drive...")
        drive.mount('/content/drive')
        print("✅ Google Drive mounted successfully!")
        return True
    except ImportError:
        print("⚠️ Not running in Google Colab - Google Drive mount not available")
        return False
    except Exception as e:
        print(f"❌ Failed to mount Google Drive: {e}")
        return False

def setup_project_directories():
    """📁 Setup project directories in Google Drive"""
    base_path = "/content/drive/MyDrive/project2"
    
    directories = [
        f"{base_path}/cache",
        f"{base_path}/cache/models",
        f"{base_path}/cache/tokenizers",
        f"{base_path}/cache/analysis",
        f"{base_path}/cache/brain_results",
        f"{base_path}/cache/performance",
        f"{base_path}/cache/genius_indicators",
        f"{base_path}/cache/optuna_studies",
        f"{base_path}/models",
        f"{base_path}/checkpoints",
        f"{base_path}/logs",
        f"{base_path}/data_new",
        f"{base_path}/exports"
    ]
    
    created_dirs = []
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            created_dirs.append(directory)
        except Exception as e:
            print(f"⚠️ Failed to create {directory}: {e}")
    
    print(f"✅ Created {len(created_dirs)} directories in Google Drive")
    return created_dirs

def check_google_drive_space():
    """📊 Check available space in Google Drive"""
    try:
        import shutil
        drive_path = "/content/drive/MyDrive"
        
        if os.path.exists(drive_path):
            total, used, free = shutil.disk_usage(drive_path)
            
            # Convert to GB
            total_gb = total // (1024**3)
            used_gb = used // (1024**3)
            free_gb = free // (1024**3)
            
            print(f"📊 Google Drive Space:")
            print(f"   💾 Total: {total_gb} GB")
            print(f"   📁 Used: {used_gb} GB")
            print(f"   🆓 Free: {free_gb} GB")
            
            if free_gb < 1:
                print("⚠️ WARNING: Less than 1GB free space!")
                return False
            elif free_gb < 5:
                print("⚠️ WARNING: Less than 5GB free space - consider cleaning up")
                
            return True
        else:
            print("❌ Google Drive not accessible")
            return False
            
    except Exception as e:
        print(f"⚠️ Failed to check Google Drive space: {e}")
        return False

def install_required_packages():
    """📦 Install required packages for Google Drive integration"""
    packages = [
        "google-colab",
        "google-auth",
        "google-auth-oauthlib",
        "google-auth-httplib2"
    ]
    
    for package in packages:
        try:
            import subprocess
            result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {package} installed/updated")
            else:
                print(f"⚠️ {package} installation failed: {result.stderr}")
        except Exception as e:
            print(f"⚠️ Failed to install {package}: {e}")

def setup_google_drive_cache():
    """🔧 Complete Google Drive cache setup"""
    print("🔧 Setting up Google Drive cache system...")
    print("=" * 50)
    
    # Step 1: Install packages
    print("📦 Installing required packages...")
    install_required_packages()
    
    # Step 2: Mount Google Drive
    print("\n🔧 Mounting Google Drive...")
    if not mount_google_drive():
        print("❌ Google Drive setup failed - cache will use local storage")
        return False
    
    # Step 3: Check space
    print("\n📊 Checking Google Drive space...")
    if not check_google_drive_space():
        print("⚠️ Google Drive space issues detected")
    
    # Step 4: Create directories
    print("\n📁 Creating project directories...")
    created_dirs = setup_project_directories()
    
    # Step 5: Test write access
    print("\n🧪 Testing write access...")
    test_file = "/content/drive/MyDrive/project2/cache/test_write.txt"
    try:
        with open(test_file, 'w') as f:
            f.write("Google Drive cache test")
        os.remove(test_file)
        print("✅ Write access confirmed")
    except Exception as e:
        print(f"❌ Write access failed: {e}")
        return False
    
    print("\n🎉 Google Drive cache setup completed successfully!")
    print("💾 Your models and cache will now persist across Colab sessions!")
    print("🔄 No more losing progress when Colab resets!")
    
    return True

def clear_google_drive_cache():
    """🗑️ Clear Google Drive cache (use with caution)"""
    import shutil
    
    cache_path = "/content/drive/MyDrive/project2/cache"
    
    if os.path.exists(cache_path):
        try:
            shutil.rmtree(cache_path)
            print(f"🗑️ Cleared cache: {cache_path}")
            setup_project_directories()
            print("✅ Cache cleared and recreated")
        except Exception as e:
            print(f"❌ Failed to clear cache: {e}")
    else:
        print("⚠️ Cache directory not found")

def show_cache_status():
    """📊 Show current cache status"""
    cache_base = "/content/drive/MyDrive/project2/cache"
    
    if not os.path.exists(cache_base):
        print("❌ Google Drive cache not set up")
        return
    
    print("📊 Google Drive Cache Status:")
    print("=" * 40)
    
    cache_types = [
        ("models", "🤖 Model Cache"),
        ("tokenizers", "🔤 Tokenizer Cache"),
        ("analysis", "📊 Analysis Cache"),
        ("brain_results", "🧠 Brain Results"),
        ("performance", "📈 Performance Data"),
        ("genius_indicators", "⚡ Genius Indicators"),
        ("optuna_studies", "🎯 Optuna Studies")
    ]
    
    for cache_type, description in cache_types:
        cache_path = os.path.join(cache_base, cache_type)
        if os.path.exists(cache_path):
            file_count = len([f for f in os.listdir(cache_path) if os.path.isfile(os.path.join(cache_path, f))])
            print(f"   {description}: {file_count} files")
        else:
            print(f"   {description}: Not found")

if __name__ == "__main__":
    # Run setup when script is executed directly
    setup_google_drive_cache()
