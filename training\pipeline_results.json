{"pipeline_execution_date": "2025-07-15T03:38:45.975945", "pipeline_config": {"pipeline_steps": ["sentiment_training", "timeseries_training", "rl_training", "model_comparison"], "parallel_training": false, "save_intermediate_results": true, "final_report_path": "training/pipeline_results.json", "models_to_train": {"sentiment": true, "timeseries": true, "rl": true}, "data_paths": {"sentiment": "data/", "timeseries": "data/", "rl": "data/"}, "training_config": {"max_retries": 3, "retry_delay": 60, "timeout": 3600}, "evaluation_config": {"run_evaluation": true, "save_evaluation_results": true, "evaluation_metrics": ["accuracy", "f1_score", "execution_time", "memory_usage", "interpretability"]}, "output_config": {"save_models": true, "save_metadata": true, "save_logs": true, "create_summary": true}, "performance_config": {"use_gpu": false, "num_workers": 1, "batch_size_override": null, "memory_limit": "4GB"}}, "training_results": {"sentiment": {"success": false, "training_time": null, "metrics": {}, "model_path": null}, "timeseries": {"success": false, "training_time": null, "metrics": {}, "model_path": null}, "rl": {"success": false, "training_time": null, "metrics": {}, "models_trained": [], "model_paths": []}}, "comparison_results": {"success": false, "comparison_time": null, "models_compared": 0, "best_models": {}, "recommendations": {}}, "summary": {"total_models_trained": 3, "successful_trainings": 0, "failed_trainings": 3, "best_models": {}, "total_training_time": 0, "recommendations": []}}