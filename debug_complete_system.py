#!/usr/bin/env python3
"""
🔧 دیباگ کامل سیستم Multi-Brain
"""

import pandas as pd
import numpy as np
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_imports():
    """دیباگ import ها"""
    print("🔧 DEBUGGING IMPORTS")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import (
            MultiBrainSystem, 
            PyCaretBrain,
            OPTUNA_AVAILABLE,
            AUTOGLUON_AVAILABLE,
            RAY_AVAILABLE,
            PYCARET_AVAILABLE,
            MLFLOW_AVAILABLE
        )
        
        print("✅ All imports successful")
        print(f"🎯 Optuna: {OPTUNA_AVAILABLE}")
        print(f"🤖 AutoGluon: {AUTOGLUON_AVAILABLE}")
        print(f"🚀 Ray: {RAY_AVAILABLE}")
        print(f"🎯 PyCaret: {PYCARET_AVAILABLE}")
        print(f"🎯 MLflow: {MLFLOW_AVAILABLE}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_multibrain_system():
    """دیباگ MultiBrainSystem"""
    print("\n🧠 DEBUGGING MULTIBRAIN SYSTEM")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import MultiBrainSystem
        
        # Initialize system
        multi_brain = MultiBrainSystem()
        print("✅ MultiBrainSystem initialized")
        
        # Check available brains
        brains = {
            'optuna_brain': multi_brain.optuna_brain,
            'autogluon_brain': multi_brain.autogluon_brain,
            'ray_brain': multi_brain.ray_brain,
            'pycaret_brain': multi_brain.pycaret_brain
        }
        
        print("\n🧠 Brain Status:")
        for name, brain in brains.items():
            status = "✅ Available" if brain is not None else "❌ Not Available"
            print(f"   {name}: {status}")
        
        # Check for old H2O brain
        has_h2o = hasattr(multi_brain, 'h2o_brain')
        print(f"   h2o_brain (should be removed): {'❌ Still exists' if has_h2o else '✅ Removed'}")
        
        return True
        
    except Exception as e:
        print(f"❌ MultiBrainSystem debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_analysis():
    """دیباگ تحلیل Multi-Brain"""
    print("\n🔍 DEBUGGING ANALYSIS")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import MultiBrainSystem
        
        # Create test data
        data = pd.DataFrame({
            'close': np.random.uniform(1.1000, 1.1100, 100),
            'volume': np.random.randint(1000, 10000, 100),
            'rsi': np.random.uniform(20, 80, 100),
            'macd': np.random.uniform(-0.01, 0.01, 100)
        })
        
        print(f"📊 Test data: {len(data)} rows, {len(data.columns)} columns")
        
        # Initialize system
        multi_brain = MultiBrainSystem()
        
        # Test analysis
        print("🔍 Running analysis...")
        analysis = multi_brain.analyze_training_situation(data, "LSTM", "EURUSD")
        
        print("✅ Analysis completed")
        print(f"   Result type: {type(analysis)}")
        print(f"   Keys: {list(analysis.keys())}")
        
        # Check required keys
        required_keys = ['hyperparameter_suggestions', 'config_suggestions', 'action', 'confidence']
        missing_keys = [key for key in required_keys if key not in analysis]
        
        if missing_keys:
            print(f"❌ Missing keys: {missing_keys}")
            return False
        else:
            print("✅ All required keys present")
            return True
            
    except Exception as e:
        print(f"❌ Analysis debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_h2o_references():
    """بررسی عدم وجود مراجع H2O"""
    print("\n🗑️ CHECKING H2O REFERENCES")
    print("=" * 50)
    
    try:
        with open('fixed_ultimate_main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for H2O references
        h2o_patterns = [
            'H2OBrain',
            'h2o_brain',
            'H2O_AVAILABLE',
            'import h2o',
            'h2o.init',
            'H2OAutoML',
            'h2o.estimators'
        ]
        
        found_patterns = []
        for pattern in h2o_patterns:
            if pattern in content:
                found_patterns.append(pattern)
        
        if found_patterns:
            print(f"⚠️ Found H2O references: {found_patterns}")
            return False
        else:
            print("✅ No H2O references found")
            return True
            
    except Exception as e:
        print(f"❌ H2O reference check failed: {e}")
        return False

def debug_pycaret_integration():
    """دیباگ ادغام PyCaret"""
    print("\n🎯 DEBUGGING PYCARET INTEGRATION")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import PyCaretBrain, PYCARET_AVAILABLE
        
        print(f"🎯 PyCaret available: {PYCARET_AVAILABLE}")
        
        # Test PyCaretBrain
        pycaret_brain = PyCaretBrain()
        print("✅ PyCaretBrain initialized")
        
        # Test data analysis
        data = pd.DataFrame({
            'close': np.random.uniform(1.1000, 1.1100, 50),
            'volume': np.random.randint(1000, 10000, 50),
            'rsi': np.random.uniform(20, 80, 50)
        })
        
        result = pycaret_brain.analyze_data_patterns(data)
        print("✅ PyCaret analysis completed")
        print(f"   Result keys: {list(result.keys())}")
        
        # Check expected keys
        expected_keys = ['data_quality', 'feature_importance', 'anomaly_detection', 'trend_analysis']
        missing_keys = [key for key in expected_keys if key not in result]
        
        if missing_keys:
            print(f"⚠️ Missing expected keys: {missing_keys}")
        else:
            print("✅ All expected keys present")
        
        return True
        
    except Exception as e:
        print(f"❌ PyCaret integration debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 COMPLETE SYSTEM DEBUG")
    print("=" * 60)
    
    # Run all debug tests
    tests = [
        ("Import Test", debug_imports),
        ("MultiBrain System Test", debug_multibrain_system),
        ("Analysis Test", debug_analysis),
        ("H2O References Check", debug_h2o_references),
        ("PyCaret Integration Test", debug_pycaret_integration)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🔧 {test_name}")
        print(f"{'='*60}")
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 DEBUG SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ System is fully debugged and ready")
        print("🚀 Ready for ultimate_market_domination_training()")
    else:
        print(f"\n⚠️ {total - passed} tests failed")
        print("🔧 Please check the failed tests above")
