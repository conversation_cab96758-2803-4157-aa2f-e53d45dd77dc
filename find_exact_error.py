#!/usr/bin/env python3
"""
🔍 پیدا کردن دقیق محل خطای hyperparameter_suggestions
"""

import pandas as pd
import numpy as np
import sys
import os
import traceback

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_step_by_step():
    """تست گام به گام برای پیدا کردن محل دقیق خطا"""
    print("🔍 STEP-BY-STEP ERROR DETECTION")
    print("=" * 50)
    
    try:
        # Step 1: Import
        print("Step 1: Importing...")
        from fixed_ultimate_main import MultiBrainSystem, ultimate_market_domination_training
        print("✅ Import successful")
        
        # Step 2: Create test data
        print("\nStep 2: Creating test data...")
        data = pd.DataFrame({
            'close': np.random.uniform(1.1000, 1.1100, 50),
            'volume': np.random.randint(1000, 10000, 50),
            'rsi': np.random.uniform(20, 80, 50),
            'macd': np.random.uniform(-0.01, 0.01, 50),
            'sma_20': np.random.uniform(1.1000, 1.1100, 50)
        })
        print("✅ Test data created")
        
        # Step 3: Initialize MultiBrainSystem
        print("\nStep 3: Initializing MultiBrainSystem...")
        multi_brain = MultiBrainSystem()
        print("✅ MultiBrainSystem initialized")
        
        # Step 4: Test analyze_training_situation
        print("\nStep 4: Testing analyze_training_situation...")
        analysis = multi_brain.analyze_training_situation(data, "LSTM", "EURUSD")
        print("✅ analyze_training_situation completed")
        print(f"   Keys: {list(analysis.keys())}")
        
        # Step 5: Check specific keys
        print("\nStep 5: Checking specific keys...")
        required_keys = ['hyperparameter_suggestions', 'config_suggestions', 'action', 'confidence']
        for key in required_keys:
            if key in analysis:
                print(f"   ✅ {key}: {type(analysis[key])}")
            else:
                print(f"   ❌ {key}: MISSING")
        
        # Step 6: Test ultimate_market_domination_training
        print("\nStep 6: Testing ultimate_market_domination_training...")
        try:
            result = ultimate_market_domination_training()
            print("✅ ultimate_market_domination_training completed")
            return True
        except Exception as e:
            print(f"❌ ultimate_market_domination_training failed: {e}")
            print("🔍 Full traceback:")
            traceback.print_exc()
            
            # Check if it's the hyperparameter_suggestions error
            error_str = str(e)
            if 'hyperparameter_suggestions' in error_str:
                print("\n🎯 FOUND THE ERROR!")
                print(f"Error message: {error_str}")
                
                # Try to find the exact line
                tb = traceback.format_exc()
                lines = tb.split('\n')
                for i, line in enumerate(lines):
                    if 'hyperparameter_suggestions' in line:
                        print(f"🔍 Error line: {line.strip()}")
                        if i > 0:
                            print(f"🔍 Previous line: {lines[i-1].strip()}")
                        if i < len(lines) - 1:
                            print(f"🔍 Next line: {lines[i+1].strip()}")
            
            return False
            
    except Exception as e:
        print(f"❌ Test failed at early stage: {e}")
        traceback.print_exc()
        return False

def test_individual_functions():
    """تست توابع فردی"""
    print("\n🔍 TESTING INDIVIDUAL FUNCTIONS")
    print("=" * 50)
    
    try:
        from fixed_ultimate_main import (
            load_and_enhance_data, 
            train_transfer_lstm, 
            train_transfer_gru,
            train_pretrained_dqn,
            train_pretrained_ppo
        )
        
        # Test load_and_enhance_data
        print("Testing load_and_enhance_data...")
        try:
            enhanced_data = load_and_enhance_data()
            print("✅ load_and_enhance_data successful")
        except Exception as e:
            print(f"❌ load_and_enhance_data failed: {e}")
            if 'hyperparameter_suggestions' in str(e):
                print("🎯 FOUND ERROR IN load_and_enhance_data!")
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Individual function test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 EXACT ERROR FINDER")
    print("=" * 60)
    
    # Test 1: Step by step
    test1 = test_step_by_step()
    
    # Test 2: Individual functions
    test2 = test_individual_functions()
    
    print("\n" + "=" * 60)
    print("📋 ERROR DETECTION SUMMARY")
    print("=" * 60)
    print(f"🔍 Step-by-step test: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"🔍 Individual functions test: {'✅ PASSED' if test2 else '❌ FAILED'}")
    
    if not test1 or not test2:
        print("\n🎯 Error found! Check the output above for details.")
    else:
        print("\n🤔 No error found in tests. The error might be in a different context.")
