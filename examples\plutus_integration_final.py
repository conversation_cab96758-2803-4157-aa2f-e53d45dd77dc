"""
مثال نهایی برای استفاده مدل‌های Plutus در پروژه
Final Example for Using Plutus Models in Project
"""

import pandas as pd
import numpy as np
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
import logging

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.plutus_integration import PlutusFinancialForecaster, PlutusConfig
from tests.test_plutus_models_comprehensive import PlutusModelTester

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PlutusIntegratedTradingSystem:
    """
    سیستم معاملاتی یکپارچه با مدل‌های Plutus
    Integrated trading system with Plutus models
    """
    
    def __init__(self):
        self.model_tester = PlutusModelTester()
        self.project_root = Path(__file__).parent.parent
        self.data_dir = self.project_root / "data"
        
        # Trading parameters
        self.min_confidence = 0.65  # Minimum confidence for trading
        self.position_size = 0.02   # 2% position size
        self.stop_loss = 0.01      # 1% stop loss
        self.take_profit = 0.02    # 2% take profit
        
        # Model weights (based on our test results)
        self.model_weights = {
            "chronos": 0.6,   # Higher weight due to better performance
            "fingpt": 0.4     # Lower weight but still valuable
        }
        
        logger.info("Plutus Integrated Trading System initialized")
    
    def get_real_time_signal(self, symbol: str, timeframe: str = "H1") -> dict:
        """
        دریافت سیگنال زمان واقعی
        Get real-time signal
        """
        try:
            logger.info(f"Getting real-time signal for {symbol} {timeframe}")
            
            # Load latest data
            price_data = self.model_tester.load_real_project_data(symbol, timeframe)
            
            if price_data.empty:
                return {"error": f"No data available for {symbol}"}
            
            # Get predictions from both models
            chronos_pred = self.model_tester.test_chronos_model_offline(price_data, symbol)
            fingpt_pred = self.model_tester.test_fingpt_model_offline(price_data, symbol)
            
            # Process predictions
            signals = {}
            
            # Chronos signal
            if not chronos_pred.get("error"):
                chronos_signal = {
                    "trend": chronos_pred["signals"]["trend"],
                    "confidence": chronos_pred["signals"]["confidence"],
                    "strength": chronos_pred["signals"]["strength"]
                }
                signals["chronos"] = chronos_signal
            
            # FinGPT signal
            if not fingpt_pred.get("error"):
                fingpt_trend = "bullish" if fingpt_pred["prediction"] == "up" else \
                              "bearish" if fingpt_pred["prediction"] == "down" else "neutral"
                
                fingpt_signal = {
                    "trend": fingpt_trend,
                    "confidence": fingpt_pred["confidence"],
                    "reasoning": fingpt_pred["reasoning"],
                    "technical_indicators": fingpt_pred["technical_indicators"]
                }
                signals["fingpt"] = fingpt_signal
            
            # Generate combined signal
            combined_signal = self.generate_combined_signal(signals)
            
            # Add market context
            market_context = self.get_market_context(price_data)
            
            return {
                "symbol": symbol,
                "timeframe": timeframe,
                "timestamp": datetime.now().isoformat(),
                "individual_signals": signals,
                "combined_signal": combined_signal,
                "market_context": market_context,
                "recommendation": self.generate_recommendation(combined_signal, market_context)
            }
            
        except Exception as e:
            logger.error(f"Error getting real-time signal: {str(e)}")
            return {"error": str(e)}
    
    def generate_combined_signal(self, signals: dict) -> dict:
        """
        تولید سیگنال ترکیبی
        Generate combined signal
        """
        try:
            if not signals:
                return {"trend": "neutral", "confidence": 0, "agreement": False}
            
            # Extract individual signals
            chronos = signals.get("chronos", {})
            fingpt = signals.get("fingpt", {})
            
            # Calculate weighted confidence
            weighted_confidence = 0
            total_weight = 0
            
            if chronos:
                weighted_confidence += chronos["confidence"] * self.model_weights["chronos"]
                total_weight += self.model_weights["chronos"]
            
            if fingpt:
                weighted_confidence += fingpt["confidence"] * self.model_weights["fingpt"]
                total_weight += self.model_weights["fingpt"]
            
            if total_weight > 0:
                weighted_confidence /= total_weight
            
            # Determine combined trend
            trends = []
            if chronos and chronos["trend"] != "neutral":
                trends.append(chronos["trend"])
            if fingpt and fingpt["trend"] != "neutral":
                trends.append(fingpt["trend"])
            
            # Check agreement
            agreement = len(set(trends)) <= 1 if trends else False
            
            if agreement and trends:
                combined_trend = trends[0]
                # Boost confidence if models agree
                weighted_confidence = min(0.95, weighted_confidence * 1.2)
            elif trends:
                # Models disagree - reduce confidence
                combined_trend = "neutral"
                weighted_confidence *= 0.5
            else:
                combined_trend = "neutral"
                weighted_confidence = 0
            
            return {
                "trend": combined_trend,
                "confidence": weighted_confidence,
                "agreement": agreement,
                "individual_trends": trends
            }
            
        except Exception as e:
            logger.error(f"Error generating combined signal: {str(e)}")
            return {"trend": "neutral", "confidence": 0, "agreement": False}
    
    def get_market_context(self, price_data: pd.DataFrame) -> dict:
        """
        دریافت زمینه بازار
        Get market context
        """
        try:
            if price_data.empty:
                return {}
            
            close_prices = price_data['close'].values
            
            # Volatility analysis
            returns = np.diff(close_prices) / close_prices[:-1]
            volatility = np.std(returns) * np.sqrt(24)  # 24-hour volatility
            
            # Trend analysis
            short_ma = np.mean(close_prices[-24:])  # 24-period MA
            long_ma = np.mean(close_prices[-120:]) if len(close_prices) >= 120 else np.mean(close_prices)  # 120-period MA
            
            # Support/Resistance levels
            recent_high = np.max(close_prices[-120:]) if len(close_prices) >= 120 else np.max(close_prices)
            recent_low = np.min(close_prices[-120:]) if len(close_prices) >= 120 else np.min(close_prices)
            current_price = close_prices[-1]
            
            # Price position in range
            price_position = (current_price - recent_low) / (recent_high - recent_low) if recent_high > recent_low else 0.5
            
            # Market regime
            if volatility > 0.02:
                market_regime = "high_volatility"
            elif volatility < 0.005:
                market_regime = "low_volatility"
            else:
                market_regime = "normal"
            
            return {
                "volatility": float(volatility),
                "short_ma": float(short_ma),
                "long_ma": float(long_ma),
                "trend_direction": "bullish" if short_ma > long_ma else "bearish",
                "recent_high": float(recent_high),
                "recent_low": float(recent_low),
                "current_price": float(current_price),
                "price_position": float(price_position),
                "market_regime": market_regime
            }
            
        except Exception as e:
            logger.error(f"Error getting market context: {str(e)}")
            return {}
    
    def generate_recommendation(self, combined_signal: dict, market_context: dict) -> dict:
        """
        تولید توصیه معاملاتی
        Generate trading recommendation
        """
        try:
            trend = combined_signal.get("trend", "neutral")
            confidence = combined_signal.get("confidence", 0)
            agreement = combined_signal.get("agreement", False)
            
            # Base recommendation
            if trend == "neutral" or confidence < self.min_confidence:
                action = "HOLD"
                reason = "Low confidence or neutral signal"
            elif not agreement:
                action = "HOLD"
                reason = "Models disagree - wait for confirmation"
            else:
                action = "BUY" if trend == "bullish" else "SELL"
                reason = f"Strong {trend} signal with {confidence:.1%} confidence"
            
            # Adjust based on market context
            if market_context:
                volatility = market_context.get("volatility", 0)
                price_position = market_context.get("price_position", 0.5)
                market_regime = market_context.get("market_regime", "normal")
                
                # Risk adjustments
                if market_regime == "high_volatility":
                    if action in ["BUY", "SELL"]:
                        reason += " (Reduced position size due to high volatility)"
                    
                # Position adjustments
                if action == "BUY" and price_position > 0.8:
                    action = "HOLD"
                    reason = "Price near recent high - wait for pullback"
                elif action == "SELL" and price_position < 0.2:
                    action = "HOLD"
                    reason = "Price near recent low - wait for bounce"
            
            # Calculate position sizing
            if action in ["BUY", "SELL"]:
                # Adjust position size based on confidence and volatility
                base_size = self.position_size
                confidence_multiplier = confidence / 0.8  # Scale confidence
                volatility_multiplier = 1 / (1 + (market_context.get("volatility", 0.01) * 10))
                
                adjusted_size = base_size * confidence_multiplier * volatility_multiplier
                adjusted_size = max(0.005, min(0.05, adjusted_size))  # Clamp between 0.5% and 5%
            else:
                adjusted_size = 0
            
            return {
                "action": action,
                "reason": reason,
                "position_size": adjusted_size,
                "stop_loss": self.stop_loss,
                "take_profit": self.take_profit,
                "risk_level": "HIGH" if market_context.get("volatility", 0) > 0.02 else \
                             "LOW" if market_context.get("volatility", 0) < 0.005 else "MEDIUM"
            }
            
        except Exception as e:
            logger.error(f"Error generating recommendation: {str(e)}")
            return {"action": "HOLD", "reason": "Error in analysis"}
    
    def run_live_analysis(self, symbols: list = None, timeframe: str = "H1") -> dict:
        """
        اجرای تحلیل زنده
        Run live analysis
        """
        if symbols is None:
            symbols = ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD"]
        
        logger.info(f"Running live analysis for {len(symbols)} symbols")
        
        results = {
            "analysis_time": datetime.now().isoformat(),
            "timeframe": timeframe,
            "signals": {}
        }
        
        for symbol in symbols:
            try:
                signal = self.get_real_time_signal(symbol, timeframe)
                results["signals"][symbol] = signal
                
                # Log recommendation
                if not signal.get("error"):
                    recommendation = signal.get("recommendation", {})
                    action = recommendation.get("action", "HOLD")
                    reason = recommendation.get("reason", "No reason")
                    
                    logger.info(f"{symbol}: {action} - {reason}")
                
            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {str(e)}")
                results["signals"][symbol] = {"error": str(e)}
        
        return results
    
    def save_analysis_results(self, results: dict, filename: str = None):
        """
        ذخیره نتایج تحلیل
        Save analysis results
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"plutus_analysis_{timestamp}.json"
        
        filepath = self.project_root / "logs" / filename
        
        # Create logs directory if it doesn't exist
        filepath.parent.mkdir(exist_ok=True)
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Analysis results saved to: {filepath}")
        return filepath

def main():
    """
    مثال اجرای سیستم
    Example system execution
    """
    print("Plutus Integrated Trading System - Live Analysis")
    print("=" * 60)
    
    # Initialize system
    system = PlutusIntegratedTradingSystem()
    
    # Run live analysis
    results = system.run_live_analysis()
    
    # Save results
    system.save_analysis_results(results)
    
    # Print summary
    print("\nANALYSIS SUMMARY:")
    print("-" * 30)
    
    actions = {"BUY": 0, "SELL": 0, "HOLD": 0}
    
    for symbol, signal in results["signals"].items():
        if signal.get("error"):
            print(f"{symbol}: ERROR - {signal['error']}")
            continue
        
        recommendation = signal.get("recommendation", {})
        action = recommendation.get("action", "HOLD")
        reason = recommendation.get("reason", "No reason")
        
        actions[action] += 1
        
        print(f"{symbol}: {action}")
        print(f"  Reason: {reason}")
        
        # Show model agreement
        combined_signal = signal.get("combined_signal", {})
        if combined_signal.get("agreement"):
            print(f"  Models Agree: {combined_signal.get('trend', 'N/A')} ({combined_signal.get('confidence', 0):.1%})")
        else:
            print(f"  Models Disagree: {combined_signal.get('confidence', 0):.1%} confidence")
        
        print()
    
    print("ACTION SUMMARY:")
    print(f"  BUY signals: {actions['BUY']}")
    print(f"  SELL signals: {actions['SELL']}")
    print(f"  HOLD signals: {actions['HOLD']}")
    
    print("\nAnalysis completed successfully!")

if __name__ == "__main__":
    main() 