#!/usr/bin/env python3
"""
🧪 تست کامل رفع مشکلات
"""

import sys
import os
sys.path.append('.')

def test_pandas_arrowdtype():
    """تست رفع pandas.ArrowDtype"""
    try:
        import pandas as pd
        if hasattr(pd, 'ArrowDtype'):
            print("✅ pandas.ArrowDtype: موجود است")
            return True
        else:
            print("❌ pandas.ArrowDtype: موجود نیست")
            return False
    except Exception as e:
        print(f"❌ pandas.ArrowDtype test failed: {e}")
        return False

def test_sklearn_fbeta_score():
    """تست رفع sklearn.metrics.fbeta_score"""
    try:
        import sklearn.metrics
        if hasattr(sklearn.metrics, 'fbeta_score'):
            print("✅ sklearn.metrics.fbeta_score: موجود است")
            # تست عملکرد
            import numpy as np
            y_true = np.array([1, 1, 0, 0])
            y_pred = np.array([1, 0, 1, 0])
            score = sklearn.metrics.fbeta_score(y_true, y_pred, beta=1, average='binary')
            print(f"✅ fbeta_score test: {score:.3f}")
            return True
        else:
            print("❌ sklearn.metrics.fbeta_score: موجود نیست")
            return False
    except Exception as e:
        print(f"❌ sklearn.metrics.fbeta_score test failed: {e}")
        return False

def test_internal_brain_monitor():
    """تست Internal Brain Monitor"""
    try:
        from fixed_ultimate_main import INTERNAL_BRAIN
        
        # تست تشخیص خطا
        error_context = {
            'test': True,
            'function': 'test_function'
        }
        
        error_info = INTERNAL_BRAIN.detect_error(
            "TEST_ERROR",
            "This is a test error",
            error_context
        )
        
        if error_info:
            print("✅ Internal Brain Monitor: کار می‌کند")
            return True
        else:
            print("❌ Internal Brain Monitor: کار نمی‌کند")
            return False
            
    except Exception as e:
        print(f"❌ Internal Brain Monitor test failed: {e}")
        return False

def test_genius_indicators():
    """تست Genius Indicators"""
    try:
        from fixed_ultimate_main import GeniusIndicatorCreator
        import pandas as pd
        import numpy as np
        
        # ایجاد داده تست
        test_data = pd.DataFrame({
            'close': np.random.randn(100).cumsum() + 100,
            'open': np.random.randn(100).cumsum() + 100,
            'high': np.random.randn(100).cumsum() + 102,
            'low': np.random.randn(100).cumsum() + 98,
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        # اضافه کردن indicators پایه
        test_data['rsi'] = np.random.rand(100) * 100
        test_data['macd'] = np.random.randn(100) * 0.1
        test_data['bb_percent'] = np.random.rand(100)
        test_data['volatility_20'] = np.random.rand(100) * 0.1
        
        genius_creator = GeniusIndicatorCreator()
        
        # پاک کردن cache قدیمی
        genius_creator.indicator_cache = {}
        
        enhanced_data = genius_creator.create_genius_indicators(test_data)
        
        genius_columns = [col for col in enhanced_data.columns if col.startswith('genius_')]
        
        if len(genius_columns) > 0:
            print(f"✅ Genius Indicators: {len(genius_columns)} indicators created")
            return True
        else:
            print("❌ Genius Indicators: 0 indicators created")
            return False
            
    except Exception as e:
        print(f"❌ Genius Indicators test failed: {e}")
        return False

def test_checkpoint_system():
    """تست Checkpoint System"""
    try:
        from fixed_ultimate_main import save_model_checkpoint
        
        # تست با mock model
        class MockModel:
            def state_dict(self):
                return {'test': 'data'}
        
        mock_model = MockModel()
        progress_info = {
            'epoch': 10,
            'loss': 0.5,
            'test': True
        }
        
        result = save_model_checkpoint(mock_model, "TEST_MODEL", progress_info)
        
        if result:
            print("✅ Checkpoint System: کار می‌کند")
            return True
        else:
            print("❌ Checkpoint System: کار نمی‌کند")
            return False
            
    except Exception as e:
        print(f"❌ Checkpoint System test failed: {e}")
        return False

def main():
    """اجرای تمام تست‌ها"""
    print("🧪 شروع تست کامل رفع مشکلات")
    print("=" * 50)
    
    tests = [
        ("pandas.ArrowDtype", test_pandas_arrowdtype),
        ("sklearn.fbeta_score", test_sklearn_fbeta_score),
        ("Internal Brain Monitor", test_internal_brain_monitor),
        ("Genius Indicators", test_genius_indicators),
        ("Checkpoint System", test_checkpoint_system)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 نتایج نهایی:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 نتیجه کلی: {passed}/{total} تست موفق ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 تمام مشکلات رفع شده!")
    else:
        print("⚠️ برخی مشکلات هنوز باقی مانده")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
