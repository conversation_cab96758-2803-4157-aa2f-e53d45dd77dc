"""
⚠️ Exception Classes - کلاس‌های استثناء
استثناءات سفارشی برای سیستم معاملاتی
"""

class TradingSystemError(Exception):
    """استثناء کلی سیستم معاملاتی"""
    pass

class ConfigurationError(TradingSystemError):
    """خطا در تنظیمات"""
    pass

class ModelError(TradingSystemError):
    """خطا در مدل‌های AI"""
    pass

class DataError(TradingSystemError):
    """خطا در داده‌ها"""
    pass

class ConnectionError(TradingSystemError):
    """خطا در اتصال"""
    pass

class ValidationError(TradingSystemError):
    """خطا در اعتبارسنجی"""
    pass

class ResourceError(TradingSystemError):
    """خطا در مصرف منابع سیستم"""
    pass

class ModelLoadError(TradingSystemError):
    """خطا در بارگذاری مدل"""
    pass

class NetworkError(TradingSystemError):
    """خطا در ارتباط شبکه"""
    pass

# Export all exception classes
__all__ = [
    'TradingSystemError',
    'ConfigurationError',
    'ModelError',
    'DataError',
    'ConnectionError',
    'ValidationError',
    'ResourceError',
    'ModelLoadError',
    'NetworkError'
] 