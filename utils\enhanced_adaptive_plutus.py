"""
سیستم تطبیقی پیشرفته Plutus با تشخیص رژیم بازار
Enhanced Adaptive Plutus System with Market Regime Detection
"""

import os
import sys
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.adaptive_plutus_system import (
    AdaptivePlutusSystem, 
    PerformanceDatabase, 
    AdaptiveLearningEngine,
    AdaptiveWeights,
    ModelPerformanceMetrics
)
from utils.market_regime_detector import MarketRegimeDetector, RegimeDetectionResult

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedAdaptivePlutusSystem(AdaptivePlutusSystem):
    """سیستم تطبیقی پیشرفته با تشخیص رژیم بازار"""
    
    def __init__(self, db_path: str = "enhanced_adaptive_plutus.db"):
        super().__init__(db_path)
        
        # اضافه کردن تشخیص رژیم
        self.regime_detector = MarketRegimeDetector()
        self.regime_based_weights = {}
        self.regime_performance_history = {}
        
        logger.info("Enhanced Adaptive Plutus System initialized with regime detection")
    
    def initialize_regime_detection(self, historical_data: Dict[str, pd.DataFrame]):
        """راه‌اندازی سیستم تشخیص رژیم"""
        try:
            logger.info("Initializing regime detection system...")
            
            # آموزش مدل تشخیص رژیم
            success = self.regime_detector.train_regime_model(historical_data)
            
            if success:
                logger.info("Regime detection model trained successfully")
                
                # تست تشخیص رژیم برای هر نماد
                for symbol, data in historical_data.items():
                    if len(data) >= self.regime_detector.min_data_points:
                        regime_result = self.regime_detector.detect_regime(data)
                        logger.info(f"{symbol} current regime: {regime_result.current_regime} "
                                  f"(confidence: {regime_result.confidence:.1%})")
                
                return True
            else:
                logger.warning("Failed to train regime detection model")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing regime detection: {str(e)}")
            return False
    
    def get_regime_aware_signal(self, symbol: str, timeframe: str = "H1") -> Dict[str, Any]:
        """دریافت سیگنال با در نظر گیری رژیم بازار"""
        try:
            # دریافت سیگنال معمولی
            base_signal = super().get_adaptive_signal(symbol, timeframe)
            
            if base_signal.get("error"):
                return base_signal
            
            # بارگذاری داده‌های بازار برای تشخیص رژیم
            from tests.test_plutus_models_comprehensive import PlutusModelTester
            model_tester = PlutusModelTester()
            price_data = model_tester.load_real_project_data(symbol, timeframe)
            
            if price_data.empty:
                logger.warning(f"No price data available for regime detection: {symbol}")
                return base_signal
            
            # تشخیص رژیم فعلی
            regime_result = self.regime_detector.detect_regime(price_data)
            
            # دریافت وزن‌های بهینه برای رژیم
            regime_weights = self.regime_detector.get_regime_optimal_weights(regime_result.current_regime)
            
            # ترکیب وزن‌های تطبیقی با وزن‌های رژیم
            combined_weights = self._combine_adaptive_and_regime_weights(
                base_signal.get("combined_signal", {}),
                regime_weights,
                regime_result.confidence
            )
            
            # به‌روزرسانی سیگنال
            enhanced_signal = base_signal.copy()
            if "combined_signal" in enhanced_signal:
                enhanced_signal["combined_signal"].update(combined_weights)
            
            # اضافه کردن اطلاعات رژیم
            enhanced_signal["regime_info"] = {
                "current_regime": regime_result.current_regime,
                "regime_confidence": regime_result.confidence,
                "regime_probabilities": regime_result.regime_probabilities,
                "market_features": regime_result.market_features
            }
            
            # تنظیم توصیه بر اساس رژیم
            enhanced_recommendation = self._adjust_recommendation_for_regime(
                enhanced_signal.get("recommendation", {}),
                regime_result
            )
            enhanced_signal["recommendation"] = enhanced_recommendation
            
            return enhanced_signal
            
        except Exception as e:
            logger.error(f"Error getting regime-aware signal: {str(e)}")
            return base_signal if 'base_signal' in locals() else {"error": str(e)}
    
    def _combine_adaptive_and_regime_weights(self, adaptive_signal: Dict[str, Any], 
                                           regime_weights: Dict[str, float],
                                           regime_confidence: float) -> Dict[str, Any]:
        """ترکیب وزن‌های تطبیقی با وزن‌های رژیم"""
        try:
            # وزن‌های فعلی تطبیقی
            current_chronos = adaptive_signal.get("adaptive_weights", {}).get("chronos_weight", 0.6)
            current_fingpt = adaptive_signal.get("adaptive_weights", {}).get("fingpt_weight", 0.4)
            current_threshold = adaptive_signal.get("adaptive_weights", {}).get("combined_threshold", 0.65)
            
            # وزن‌های پیشنهادی رژیم
            regime_chronos = regime_weights.get("chronos_weight", 0.6)
            regime_fingpt = regime_weights.get("fingpt_weight", 0.4)
            regime_threshold = regime_weights.get("confidence_threshold", 0.65)
            
            # ترکیب بر اساس اعتماد به رژیم
            regime_influence = min(regime_confidence, 0.8)  # حداکثر 80% تأثیر
            adaptive_influence = 1 - regime_influence
            
            # وزن‌های نهایی
            final_chronos = (current_chronos * adaptive_influence + 
                           regime_chronos * regime_influence)
            final_fingpt = (current_fingpt * adaptive_influence + 
                          regime_fingpt * regime_influence)
            final_threshold = (current_threshold * adaptive_influence + 
                             regime_threshold * regime_influence)
            
            # تنظیم ضریب اعتماد بر اساس رژیم
            confidence_multiplier = adaptive_signal.get("confidence", 1.0)
            
            # در رژیم‌های پرریسک، اعتماد کمتر
            if regime_confidence > 0.7:
                regime_name = self._get_regime_name_from_weights(regime_weights)
                if regime_name in ["bear_market", "high_volatility"]:
                    confidence_multiplier *= 0.9
                elif regime_name in ["bull_market", "low_volatility"]:
                    confidence_multiplier *= 1.1
            
            return {
                "chronos_weight": final_chronos,
                "fingpt_weight": final_fingpt,
                "combined_threshold": final_threshold,
                "confidence": confidence_multiplier,
                "regime_influence": regime_influence,
                "adaptive_influence": adaptive_influence
            }
            
        except Exception as e:
            logger.error(f"Error combining weights: {str(e)}")
            return adaptive_signal
    
    def _get_regime_name_from_weights(self, regime_weights: Dict[str, float]) -> str:
        """تشخیص نام رژیم از روی وزن‌ها"""
        # این یک روش ساده است - در عمل باید از regime_detector استفاده کنیم
        chronos_weight = regime_weights.get("chronos_weight", 0.5)
        confidence_threshold = regime_weights.get("confidence_threshold", 0.65)
        
        if chronos_weight > 0.65 and confidence_threshold < 0.7:
            return "bull_market"
        elif chronos_weight < 0.45 and confidence_threshold > 0.75:
            return "bear_market"
        elif confidence_threshold > 0.8:
            return "high_volatility"
        else:
            return "sideways_market"
    
    def _adjust_recommendation_for_regime(self, base_recommendation: Dict[str, Any],
                                        regime_result: RegimeDetectionResult) -> Dict[str, Any]:
        """تنظیم توصیه بر اساس رژیم بازار"""
        try:
            recommendation = base_recommendation.copy()
            regime_name = regime_result.current_regime
            regime_confidence = regime_result.confidence
            
            # تنظیم اکشن بر اساس رژیم
            current_action = recommendation.get("action", "HOLD")
            
            if regime_name == "high_volatility" and regime_confidence > 0.7:
                # در رژیم پر نوسان، محافظه‌کاری بیشتر
                if current_action in ["BUY", "SELL"]:
                    recommendation["action"] = "HOLD"
                    recommendation["reason"] = f"High volatility regime detected - holding position for safety"
                    recommendation["regime_adjustment"] = "Conservative due to high volatility"
                
            elif regime_name == "bear_market" and regime_confidence > 0.6:
                # در بازار نزولی، محدودیت خرید
                if current_action == "BUY":
                    recommendation["action"] = "HOLD"
                    recommendation["reason"] = f"Bear market regime - avoiding long positions"
                    recommendation["regime_adjustment"] = "Avoiding longs in bear market"
                
            elif regime_name == "bull_market" and regime_confidence > 0.6:
                # در بازار صعودی، تقویت سیگنال‌های خرید
                if current_action == "BUY":
                    recommendation["confidence_boost"] = 1.1
                    recommendation["regime_adjustment"] = "Boosted confidence in bull market"
                elif current_action == "HOLD" and "bullish" in recommendation.get("reason", "").lower():
                    recommendation["action"] = "BUY"
                    recommendation["reason"] = f"Bull market regime supports bullish signal"
                    recommendation["regime_adjustment"] = "Upgraded to BUY in bull market"
            
            elif regime_name == "low_volatility" and regime_confidence > 0.6:
                # در بازار کم نوسان، افزایش اندازه موقعیت
                if current_action in ["BUY", "SELL"]:
                    recommendation["position_size_multiplier"] = 1.2
                    recommendation["regime_adjustment"] = "Increased position size in low volatility"
            
            # اضافه کردن اطلاعات رژیم به توصیه
            recommendation["regime_context"] = {
                "regime": regime_name,
                "confidence": regime_confidence,
                "risk_level": self.regime_detector.regimes[regime_name].risk_level,
                "description": self.regime_detector.regimes[regime_name].description
            }
            
            return recommendation
            
        except Exception as e:
            logger.error(f"Error adjusting recommendation for regime: {str(e)}")
            return base_recommendation
    
    def analyze_regime_performance(self, symbol: str, days: int = 30) -> Dict[str, Any]:
        """تحلیل عملکرد در رژیم‌های مختلف"""
        try:
            # دریافت عملکرد کلی
            base_analysis = self.learning_engine.analyze_model_performance(symbol, "adaptive_combined", days)
            
            if base_analysis.get("error"):
                return base_analysis
            
            # دریافت تاریخچه رژیم‌ها
            regime_history = self.regime_detector.regime_history
            
            if len(regime_history) < 5:
                return {**base_analysis, "regime_analysis": "Insufficient regime history"}
            
            # تحلیل عملکرد بر اساس رژیم
            regime_performance = {}
            
            for regime_name in self.regime_detector.regimes.keys():
                regime_metrics = []
                
                # پیدا کردن متریک‌های مربوط به این رژیم
                for regime_record in regime_history:
                    if regime_record.current_regime == regime_name:
                        # پیدا کردن عملکرد در همان زمان
                        matching_metrics = self.db.get_recent_performance(symbol, "adaptive_combined", 1)
                        
                        for metric in matching_metrics:
                            time_diff = abs((metric.timestamp - regime_record.timestamp).total_seconds())
                            if time_diff < 3600:  # در صورتی که کمتر از 1 ساعت فاصله باشد
                                regime_metrics.append(metric)
                
                if regime_metrics:
                    regime_performance[regime_name] = {
                        "count": len(regime_metrics),
                        "avg_accuracy": np.mean([m.accuracy for m in regime_metrics]),
                        "avg_profit": np.mean([m.profit_loss for m in regime_metrics]),
                        "win_rate": len([m for m in regime_metrics if m.profit_loss > 0]) / len(regime_metrics)
                    }
                else:
                    regime_performance[regime_name] = {
                        "count": 0,
                        "avg_accuracy": 0,
                        "avg_profit": 0,
                        "win_rate": 0
                    }
            
            # تحلیل ثبات رژیم
            stability_analysis = self.regime_detector.analyze_regime_stability()
            
            return {
                **base_analysis,
                "regime_performance": regime_performance,
                "regime_stability": stability_analysis,
                "best_regime": max(regime_performance.keys(), 
                                 key=lambda x: regime_performance[x]["avg_profit"]) if regime_performance else None,
                "worst_regime": min(regime_performance.keys(), 
                                  key=lambda x: regime_performance[x]["avg_profit"]) if regime_performance else None
            }
            
        except Exception as e:
            logger.error(f"Error analyzing regime performance: {str(e)}")
            return {"error": str(e)}
    
    def generate_enhanced_learning_report(self, symbol: str, days: int = 30) -> str:
        """تولید گزارش یادگیری پیشرفته با تحلیل رژیم"""
        try:
            # گزارش پایه
            base_report = super().generate_learning_report(symbol, days)
            
            # تحلیل رژیم
            regime_analysis = self.analyze_regime_performance(symbol, days)
            
            # اضافه کردن بخش رژیم به گزارش
            enhanced_report = base_report + "\n\n"
            enhanced_report += "MARKET REGIME ANALYSIS:\n"
            enhanced_report += "-" * 25 + "\n"
            
            if "regime_performance" in regime_analysis:
                for regime, performance in regime_analysis["regime_performance"].items():
                    if performance["count"] > 0:
                        enhanced_report += f"{regime.replace('_', ' ').title()}:\n"
                        enhanced_report += f"  Trades: {performance['count']}\n"
                        enhanced_report += f"  Accuracy: {performance['avg_accuracy']:.1%}\n"
                        enhanced_report += f"  Avg Profit: {performance['avg_profit']:.2f}\n"
                        enhanced_report += f"  Win Rate: {performance['win_rate']:.1%}\n\n"
            
            if "regime_stability" in regime_analysis:
                stability = regime_analysis["regime_stability"]
                enhanced_report += f"Regime Stability: {stability.get('stability', 'N/A')}\n"
                enhanced_report += f"Dominant Regime: {stability.get('dominant_regime', 'N/A')}\n"
                enhanced_report += f"Regime Changes: {stability.get('regime_changes', 'N/A')}\n\n"
            
            if regime_analysis.get("best_regime"):
                enhanced_report += f"Best Performing Regime: {regime_analysis['best_regime']}\n"
            if regime_analysis.get("worst_regime"):
                enhanced_report += f"Worst Performing Regime: {regime_analysis['worst_regime']}\n"
            
            enhanced_report += "\nRECOMMENDATIONS:\n"
            enhanced_report += "-" * 15 + "\n"
            
            # توصیه‌های بر اساس رژیم
            if "regime_stability" in regime_analysis:
                stability = regime_analysis["regime_stability"].get("stability", "unknown")
                if stability == "low":
                    enhanced_report += "• Market regime is unstable - use conservative settings\n"
                elif stability == "high":
                    enhanced_report += "• Market regime is stable - can use more aggressive settings\n"
            
            if "best_regime" in regime_analysis and regime_analysis["best_regime"]:
                best_regime = regime_analysis["best_regime"]
                enhanced_report += f"• Focus optimization on {best_regime.replace('_', ' ')} conditions\n"
            
            return enhanced_report
            
        except Exception as e:
            logger.error(f"Error generating enhanced report: {str(e)}")
            return super().generate_learning_report(symbol, days)

def main():
    """مثال استفاده از سیستم پیشرفته"""
    print("🚀 Enhanced Adaptive Plutus System Demo")
    print("=" * 60)
    
    # ایجاد سیستم پیشرفته
    system = EnhancedAdaptivePlutusSystem("enhanced_demo.db")
    
    # شبیه‌سازی داده‌های تاریخی
    from tests.test_plutus_models_comprehensive import PlutusModelTester
    model_tester = PlutusModelTester()
    
    historical_data = {}
    symbols = ["EURUSD", "GBPUSD"]
    
    for symbol in symbols:
        data = model_tester.load_real_project_data(symbol, "H1")
        if not data.empty:
            historical_data[symbol] = data
            print(f"✅ Loaded {len(data)} records for {symbol}")
    
    if historical_data:
        # راه‌اندازی تشخیص رژیم
        print("\n🎭 Initializing regime detection...")
        success = system.initialize_regime_detection(historical_data)
        
        if success:
            print("✅ Regime detection initialized successfully")
            
            # تست سیگنال‌های آگاه از رژیم
            print("\n📡 Testing regime-aware signals...")
            
            for symbol in symbols:
                signal = system.get_regime_aware_signal(symbol, "H1")
                
                if not signal.get("error"):
                    print(f"\n{symbol}:")
                    
                    # اطلاعات رژیم
                    regime_info = signal.get("regime_info", {})
                    print(f"  Current Regime: {regime_info.get('current_regime', 'Unknown')}")
                    print(f"  Regime Confidence: {regime_info.get('regime_confidence', 0):.1%}")
                    
                    # سیگنال ترکیبی
                    combined = signal.get("combined_signal", {})
                    print(f"  Signal Confidence: {combined.get('confidence', 0):.1%}")
                    print(f"  Regime Influence: {combined.get('regime_influence', 0):.1%}")
                    
                    # توصیه
                    recommendation = signal.get("recommendation", {})
                    print(f"  Action: {recommendation.get('action', 'HOLD')}")
                    if "regime_adjustment" in recommendation:
                        print(f"  Regime Adjustment: {recommendation['regime_adjustment']}")
                
                else:
                    print(f"{symbol}: Error - {signal['error']}")
            
            # تولید گزارش پیشرفته
            print("\n📋 Generating enhanced learning report...")
            for symbol in symbols:
                report = system.generate_enhanced_learning_report(symbol, 7)
                print(f"\n{symbol} Enhanced Report:")
                print("-" * 30)
                # نمایش بخشی از گزارش
                report_lines = report.split('\n')
                for line in report_lines[:20]:
                    if line.strip():
                        print(line)
                print("... (truncated)")
        
        else:
            print("❌ Failed to initialize regime detection")
    
    else:
        print("❌ No historical data available for testing")

if __name__ == "__main__":
    main() 