{"timestamp": "2025-07-17T11:47:38.445575", "available_modules": {"memory_manager": {"success": true, "stats": "MemoryStats(timestamp=datetime.datetime(2025, 7, 17, 11, 47, 36, 326813), total_memory=8105.42578125, used_memory=6582.30859375, available_memory=1523.1171875, memory_percent=81.2, swap_memory=2693.05078125, process_memory=1855.0625, memory_level=<MemoryLevel.HIGH: 'high'>, gc_count=0, active_objects=0)"}, "enhanced_replay": {"success": false, "error": "__init__() got an unexpected keyword argument 'buffer_size'"}, "genetic_evolution": {"success": true}, "continual_learning": {"success": false, "error": "__init__() missing 1 required positional argument: 'model'"}, "backtesting": {"success": false, "error": "Not available"}}, "brain_decisions": [{"action": "train", "model": "FinBERT", "reasoning": "Selected FinBERT based on priority 1", "confidence": 0.7098349363082015, "expected_performance": 0.7192143013705687}, {"action": "train", "model": "EnhancedDQNAgent", "reasoning": "Selected EnhancedDQNAgent based on priority 1", "confidence": 0.8593532869194084, "expected_performance": 0.7054900438412461}, {"action": "train", "model": "DQN_Agent", "reasoning": "Selected DQN_Agent based on priority 1", "confidence": 0.8287775002605341, "expected_performance": 0.7073860064445949}], "resource_predictions": {"FinBERT": {"memory_mb": 2457.6, "training_time_min": 49.50000000000001, "confidence": 0.7823343778043568}, "LSTM_TimeSeries": {"memory_mb": 1228.8, "training_time_min": 33.0, "confidence": 0.7426717961340303}, "DQN_Agent": {"memory_mb": 1440.0, "training_time_min": 66.0, "confidence": 0.8009479698517685}, "PPO_Agent": {"memory_mb": 1320.0, "training_time_min": 60.50000000000001, "confidence": 0.829309040658907}, "EnhancedDQNAgent": {"memory_mb": 1800.0, "training_time_min": 88.0, "confidence": 0.7625555492095508}}, "integration": {"success": true}, "advanced_features": {"genetic_optimization": {"success": true, "optimized_params": {"learning_rate": 0.001, "batch_size": 64, "hidden_size": 128}}, "continual_learning": {"success": true, "ewc_lambda": 0.4, "replay_buffer_size": 10000}, "enhanced_replay": {"success": false, "error": "__init__() got an unexpected keyword argument 'buffer_size'"}}}