"""
🛠️ Core Utilities - ابزارهای کلی هسته
ابزارهای کاربردی برای سیستم معاملاتی
"""

import os
import sys
import psutil
import threading
import time
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import json
import requests
from collections import OrderedDict

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logger = logging.getLogger(__name__)

class CacheManager:
    """مدیر کش سیستم"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        self.max_size = max_size
        self.ttl = ttl  # Time to live in seconds
        self.cache = OrderedDict()
        self.timestamps = {}
        self.hits = 0
        self.misses = 0
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get(self, key: str) -> Any:
        """دریافت از کش"""
        # Check if key exists and is not expired
        if key in self.cache:
            if self._is_expired(key):
                self.delete(key)
                self.misses += 1
                return None
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            self.hits += 1
            return self.cache[key]
        
        self.misses += 1
        return None
    
    def set(self, key: str, value: Any) -> None:
        """ذخیره در کش"""
        # Remove expired items
        self._cleanup_expired()
        
        # If at max capacity, remove oldest item
        if len(self.cache) >= self.max_size and key not in self.cache:
            oldest_key = next(iter(self.cache))
            self.delete(oldest_key)
        
        # Add/update item
        self.cache[key] = value
        self.timestamps[key] = datetime.now()
        
        # Move to end
        self.cache.move_to_end(key)
    
    def delete(self, key: str) -> bool:
        """حذف از کش"""
        if key in self.cache:
            del self.cache[key]
            del self.timestamps[key]
            return True
        return False
    
    def clear(self) -> None:
        """پاک کردن کش"""
        self.cache.clear()
        self.timestamps.clear()
        self.hits = 0
        self.misses = 0
    
    def _is_expired(self, key: str) -> bool:
        """بررسی انقضای کلید"""
        if key not in self.timestamps:
            return True
        
        age = (datetime.now() - self.timestamps[key]).total_seconds()
        return age > self.ttl
    
    def _cleanup_expired(self) -> None:
        """پاکسازی موارد منقضی شده"""
        expired_keys = [
            key for key in self.timestamps
            if self._is_expired(key)
        ]
        
        for key in expired_keys:
            self.delete(key)
    
    def get_stats(self) -> Dict[str, Any]:
        """دریافت آمار کش"""
        total_requests = self.hits + self.misses
        hit_rate = self.hits / total_requests if total_requests > 0 else 0.0
        
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': hit_rate,
            'ttl': self.ttl
        }

class PerformanceMonitor:
    """مانیتور عملکرد سیستم"""
    
    def __init__(self):
        self.monitoring = False
        self.metrics = {}
        self.monitor_thread = None
        self.update_interval = 5  # seconds
    
    def start(self):
        """شروع مانیتورینگ"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info("(OK) Performance monitoring started")
    
    def stop(self):
        """توقف مانیتورینگ"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("✅ Performance monitoring stopped")
    
    def _monitor_loop(self):
        """حلقه مانیتورینگ"""
        while self.monitoring:
            try:
                self.metrics = {
                    'timestamp': datetime.now().isoformat(),
                    'cpu_percent': psutil.cpu_percent(),
                    'memory_percent': psutil.virtual_memory().percent,
                    'memory_used_gb': psutil.virtual_memory().used / (1024**3),
                    'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                    'disk_usage_percent': psutil.disk_usage('/').percent,
                    'process_count': len(psutil.pids())
                }
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Error in performance monitoring: {e}")
                time.sleep(self.update_interval)
    
    def get_metrics(self) -> Dict[str, Any]:
        """دریافت معیارهای عملکرد"""
        return self.metrics.copy()

class MemoryManager:
    """مدیر حافظه"""
    
    def __init__(self):
        self.memory_threshold = 80  # percent
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def check_memory_usage(self) -> Dict[str, Any]:
        """بررسی استفاده از حافظه"""
        memory = psutil.virtual_memory()
        
        return {
            'percent': memory.percent,
            'used_gb': memory.used / (1024**3),
            'total_gb': memory.total / (1024**3),
            'available_gb': memory.available / (1024**3),
            'warning': memory.percent > self.memory_threshold
        }
    
    def cleanup_memory(self):
        """پاکسازی حافظه"""
        try:
            import gc
            gc.collect()
            self.logger.info("✅ Memory cleanup completed")
        except Exception as e:
            self.logger.error(f"Memory cleanup failed: {e}")

class ProxyManager:
    """مدیر پروکسی"""
    
    def __init__(self):
        self.proxy_url = None
        self.proxy_enabled = False
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def set_proxy(self, proxy_url: str) -> bool:
        """تنظیم پروکسی"""
        try:
            self.proxy_url = proxy_url
            self.proxy_enabled = True
            
            # Set environment variables
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url
            
            self.logger.info(f"✅ Proxy set: {proxy_url}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to set proxy: {e}")
            return False
    
    def disable_proxy(self):
        """غیرفعال کردن پروکسی"""
        self.proxy_enabled = False
        self.proxy_url = None
        
        # Clear environment variables
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        
        self.logger.info("✅ Proxy disabled")
    
    def test_proxy(self) -> bool:
        """تست پروکسی"""
        if not self.proxy_enabled:
            return False
        
        try:
            proxies = {
                'http': self.proxy_url,
                'https': self.proxy_url
            }
            
            response = requests.get(
                'https://httpbin.org/ip',
                proxies=proxies,
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            self.logger.error(f"Proxy test failed: {e}")
            return False

def safe_file_operation(file_path: str, operation: str = 'read', 
                        data: Any = None, encoding: str = 'utf-8') -> Any:
    """
    عملیات امن فایل با مدیریت خطا
    
    Args:
        file_path: مسیر فایل
        operation: نوع عملیات ('read', 'write', 'append', 'exists', 'delete')
        data: داده برای نوشتن
        encoding: کدگذاری فایل
    
    Returns:
        Any: نتیجه عملیات
    """
    try:
        if operation == 'read':
            if not os.path.exists(file_path):
                logger.warning(f"File not found: {file_path}")
                return None
            
            with open(file_path, 'r', encoding=encoding) as file:
                return file.read()
        
        elif operation == 'write':
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding=encoding) as file:
                file.write(str(data))
            return True
        
        elif operation == 'append':
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'a', encoding=encoding) as file:
                file.write(str(data))
            return True
        
        elif operation == 'exists':
            return os.path.exists(file_path)
        
        elif operation == 'delete':
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        
        else:
            logger.error(f"Unknown operation: {operation}")
            return None
            
    except Exception as e:
        logger.error(f"File operation failed ({operation}): {e}")
        return None

def ensure_directory(directory_path: str) -> bool:
    """
    اطمینان از وجود دایرکتوری
    
    Args:
        directory_path: مسیر دایرکتوری
    
    Returns:
        bool: آیا دایرکتوری موجود است یا ایجاد شد
    """
    try:
        os.makedirs(directory_path, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Failed to create directory {directory_path}: {e}")
        return False

def get_file_hash(file_path: str, algorithm: str = 'sha256') -> Optional[str]:
    """
    محاسبه hash فایل
    
    Args:
        file_path: مسیر فایل
        algorithm: الگوریتم hash
    
    Returns:
        str: hash فایل
    """
    try:
        import hashlib
        
        if not os.path.exists(file_path):
            return None
        
        hash_obj = hashlib.new(algorithm)
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
    except Exception as e:
        logger.error(f"Failed to calculate hash for {file_path}: {e}")
        return None

def compress_data(data: Any, compression_type: str = 'gzip') -> Optional[bytes]:
    """
    فشرده‌سازی داده
    
    Args:
        data: داده ورودی
        compression_type: نوع فشرده‌سازی
    
    Returns:
        bytes: داده فشرده شده
    """
    try:
        import gzip
        import pickle
        
        serialized = pickle.dumps(data)
        
        if compression_type == 'gzip':
            return gzip.compress(serialized)
        else:
            return serialized
            
    except Exception as e:
        logger.error(f"Failed to compress data: {e}")
        return None

def decompress_data(compressed_data: bytes, compression_type: str = 'gzip') -> Any:
    """
    رفع فشردگی داده
    
    Args:
        compressed_data: داده فشرده شده
        compression_type: نوع فشرده‌سازی
    
    Returns:
        Any: داده اصلی
    """
    try:
        import gzip
        import pickle
        
        if compression_type == 'gzip':
            serialized = gzip.decompress(compressed_data)
        else:
            serialized = compressed_data
            
        return pickle.loads(serialized)
        
    except Exception as e:
        logger.error(f"Failed to decompress data: {e}")
        return None

def parse_config_value(value: str) -> Any:
    """
    تبدیل رشته config به مقدار مناسب
    
    Args:
        value: مقدار رشته‌ای
    
    Returns:
        Any: مقدار تبدیل شده
    """
    try:
        # Try boolean
        if value.lower() in ['true', 'false']:
            return value.lower() == 'true'
        
        # Try integer
        try:
            return int(value)
        except ValueError:
            pass
        
        # Try float
        try:
            return float(value)
        except ValueError:
            pass
        
        # Try JSON
        try:
            import json
            return json.loads(value)
        except:
            pass
        
        # Return as string
        return value
        
    except Exception as e:
        logger.error(f"Failed to parse config value {value}: {e}")
        return value

def generate_unique_id(prefix: str = "", length: int = 8) -> str:
    """
    تولید شناسه منحصر به فرد
    
    Args:
        prefix: پیشوند
        length: طول شناسه
    
    Returns:
        str: شناسه منحصر به فرد
    """
    try:
        import uuid
        import string
        import random
        
        # Generate random string
        chars = string.ascii_letters + string.digits
        random_str = ''.join(random.choice(chars) for _ in range(length))
        
        # Add timestamp
        timestamp = str(int(time.time()))[-4:]
        
        return f"{prefix}{timestamp}{random_str}" if prefix else f"{timestamp}{random_str}"
        
    except Exception as e:
        logger.error(f"Failed to generate unique ID: {e}")
        return f"id_{int(time.time())}"

# Global instances
cache_manager = CacheManager()
performance_monitor = PerformanceMonitor()
memory_manager = MemoryManager()
proxy_manager = ProxyManager()

def setup_system_monitoring():
    """تنظیم مانیتورینگ سیستم"""
    performance_monitor.start()
    logger.info("(OK) System monitoring setup completed")

def cleanup_resources():
    """پاکسازی منابع"""
    performance_monitor.stop()
    memory_manager.cleanup_memory()
    cache_manager.clear()
    logger.info("✅ Resources cleaned up")

def get_system_info() -> Dict[str, Any]:
    """دریافت اطلاعات سیستم"""
    try:
        return {
            'platform': sys.platform,
            'python_version': sys.version,
            'cpu_count': psutil.cpu_count(),
            'memory_total_gb': psutil.virtual_memory().total / (1024**3),
            'disk_total_gb': psutil.disk_usage('/').total / (1024**3),
            'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat(),
            'current_time': datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get system info: {e}")
        return {'error': str(e)}

def get_resource_usage() -> Dict[str, Any]:
    """
    دریافت اطلاعات مصرف منابع سیستم
    
    Returns:
        Dict[str, Any]: اطلاعات مصرف منابع
    """
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # Memory usage
        memory = psutil.virtual_memory()
        
        # Disk usage
        disk = psutil.disk_usage('/')
        
        # Network stats
        try:
            network = psutil.net_io_counters()
            network_info = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
        except Exception:
            network_info = {
                'bytes_sent': 0,
                'bytes_recv': 0,
                'packets_sent': 0,
                'packets_recv': 0
            }
        
        return {
            'cpu': {
                'percent': cpu_percent,
                'count': cpu_count,
                'load_avg': cpu_percent / 100.0
            },
            'memory': {
                'percent': memory.percent,
                'used_gb': memory.used / (1024**3),
                'total_gb': memory.total / (1024**3),
                'available_gb': memory.available / (1024**3)
            },
            'disk': {
                'percent': disk.percent,
                'used_gb': disk.used / (1024**3),
                'total_gb': disk.total / (1024**3),
                'free_gb': disk.free / (1024**3)
            },
            'network': network_info,
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting resource usage: {e}")
        return {
            'cpu': {'percent': 0, 'count': 1, 'load_avg': 0},
            'memory': {'percent': 0, 'used_gb': 0, 'total_gb': 0, 'available_gb': 0},
            'disk': {'percent': 0, 'used_gb': 0, 'total_gb': 0, 'free_gb': 0},
            'network': {'bytes_sent': 0, 'bytes_recv': 0, 'packets_sent': 0, 'packets_recv': 0},
            'timestamp': datetime.now().isoformat()
        }

def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """
    Decorator برای retry کردن توابع در صورت خطا
    
    Args:
        max_retries: حداکثر تعداد تلاش
        delay: تاخیر بین تلاش‌ها (ثانیه)
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. Retrying in {delay}s...")
                        time.sleep(delay)
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
            
            raise last_exception
        
        return wrapper
    return decorator


def timeout_after(timeout_seconds: float):
    """
    Decorator برای timeout کردن توابع
    
    Args:
        timeout_seconds: مدت زمان timeout (ثانیه)
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            import signal
            
            def timeout_handler(signum, frame):
                raise TimeoutError(f"Function {func.__name__} timed out after {timeout_seconds}s")
            
            # Set timeout
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(int(timeout_seconds))
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # Reset alarm
                signal.alarm(0)
                signal.signal(signal.SIGALRM, old_handler)
        
        return wrapper
    return decorator


def validate_data_format(data: Any, expected_format: str) -> bool:
    """
    اعتبارسنجی فرمت داده
    
    Args:
        data: داده ورودی
        expected_format: فرمت مورد انتظار
    
    Returns:
        bool: آیا داده معتبر است
    """
    try:
        if expected_format == "dataframe":
            import pandas as pd
            return isinstance(data, pd.DataFrame)
        elif expected_format == "dict":
            return isinstance(data, dict)
        elif expected_format == "list":
            return isinstance(data, list)
        elif expected_format == "numeric":
            return isinstance(data, (int, float))
        else:
            return True
    except Exception:
        return False


def safe_divide(a: float, b: float, default: float = 0.0) -> float:
    """
    تقسیم امن با جلوگیری از تقسیم بر صفر
    
    Args:
        a: عدد اول
        b: عدد دوم
        default: مقدار پیش‌فرض در صورت تقسیم بر صفر
    
    Returns:
        float: نتیجه تقسیم یا مقدار پیش‌فرض
    """
    try:
        if b == 0:
            return default
        return a / b
    except Exception:
        return default


def format_currency(amount: float, currency: str = "USD") -> str:
    """
    فرمت کردن مبلغ ارزی
    
    Args:
        amount: مبلغ
        currency: نوع ارز
    
    Returns:
        str: مبلغ فرمت شده
    """
    try:
        return f"{amount:,.2f} {currency}"
    except Exception:
        return f"{amount} {currency}"


def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """
    محاسبه درصد تغییر
    
    Args:
        old_value: مقدار قبلی
        new_value: مقدار جدید
    
    Returns:
        float: درصد تغییر
    """
    try:
        if old_value == 0:
            return 0.0
        return ((new_value - old_value) / old_value) * 100
    except Exception:
        return 0.0


def cache_result(ttl: int = 300):
    """
    Decorator برای cache کردن نتایج توابع
    
    Args:
        ttl: مدت زمان cache (ثانیه)
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Create cache key from function name and arguments
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            result = cache_manager.get(cache_key)
            if result is not None:
                return result
            
            # Calculate and cache result
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result)
            
            return result
        
        return wrapper
    return decorator

def monitor_performance(func):
    """
    Decorator برای مانیتور کردن عملکرد توابع
    """
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            performance_monitor.metrics[f"{func.__name__}_execution_time"] = execution_time
            performance_monitor.metrics[f"{func.__name__}_last_call"] = datetime.now().isoformat()
            
            logger.debug(f"Function {func.__name__} executed in {execution_time:.4f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            performance_monitor.metrics[f"{func.__name__}_error_time"] = execution_time
            performance_monitor.metrics[f"{func.__name__}_last_error"] = str(e)
            
            logger.error(f"Function {func.__name__} failed after {execution_time:.4f}s: {e}")
            raise
    
    return wrapper

# Export main classes and functions
__all__ = [
    'CacheManager',
    'PerformanceMonitor',
    'MemoryManager',
    'ProxyManager',
    'cache_manager',
    'performance_monitor',
    'memory_manager',
    'proxy_manager',
    'setup_system_monitoring',
    'cleanup_resources',
    'get_system_info',
    'get_resource_usage',
    'safe_file_operation',
    'ensure_directory',
    'get_file_hash',
    'compress_data',
    'decompress_data',
    'parse_config_value',
    'generate_unique_id',
    'retry_on_failure',
    'timeout_after',
    'cache_result',
    'monitor_performance',
    'validate_data_format',
    'safe_divide',
    'format_currency',
    'calculate_percentage_change'
] 