{"session_info": {"start_time": "2025-07-17T13:18:43.533065", "end_time": "2025-07-17T13:20:11.592932", "duration_seconds": 88.052857, "config": {"use_memory_optimization": true, "use_enhanced_replay": true, "use_genetic_optimization": true, "use_continual_learning": true, "use_advanced_backtesting": true, "memory_optimization_level": "aggressive", "replay_buffer_size": 10000, "prioritized_replay": true, "genetic_population_size": 20, "genetic_generations": 10, "ewc_lambda": 0.4, "use_experience_replay": true, "auto_backtest_after_training": true, "backtest_validation_split": 0.2}}, "training_results": {"completed": [{"success": true, "performance": 0.982, "metrics": {"rmse": 0.018000000000000002, "mae": 0.0126, "mape": 1.4400000000000002, "directional_accuracy": 0.85}, "epochs_completed": 50, "best_epoch": 42, "advanced_metrics": {"memory_efficiency": 0.95, "training_stability": 0.9700000000000001, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.9348407242068436, "sharpe_ratio": 1.7128168817352392, "max_drawdown": 0.07144494487711314, "win_rate": 0.674433509582982}, "model_saved": true, "model_name": "LSTM_TimeSeries", "category": "timeseries", "training_duration": 11.542978, "start_time": "2025-07-17T13:18:49.765362", "end_time": "2025-07-17T13:19:01.308340", "preprocessing_results": {"memory_optimized": true, "genetic_optimization": {"learning_rate": 0.002044682884139436, "batch_size": 64, "hidden_size": 64}, "continual_learning_ready": true}, "advanced_features_applied": {"memory_optimization": true, "genetic_optimization": true, "enhanced_replay": false, "continual_learning": true}}, {"success": true, "performance": 0.8999999999999999, "metrics": {"avg_reward": 0.18, "success_rate": 0.72, "sharpe_ratio": 2.0, "max_drawdown": 0.07}, "episodes_completed": 1000, "best_episode": 850, "advanced_metrics": {"memory_efficiency": 0.95, "training_stability": 0.9700000000000001, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.8909087694452092, "sharpe_ratio": 1.5104775517234597, "max_drawdown": 0.082005497945376, "win_rate": 0.719677296656658}, "model_saved": true, "model_name": "DQN_Agent", "category": "reinforcement_learning", "training_duration": 11.600687, "start_time": "2025-07-17T13:19:06.357824", "end_time": "2025-07-17T13:19:17.958511", "preprocessing_results": {"memory_optimized": true, "genetic_optimization": {"learning_rate": 0.009110152817286879, "batch_size": 64, "hidden_size": 128}, "continual_learning_ready": true}, "advanced_features_applied": {"memory_optimization": true, "genetic_optimization": true, "enhanced_replay": false, "continual_learning": true}}, {"success": true, "performance": 0.982, "metrics": {"rmse": 0.018000000000000002, "mae": 0.0126, "mape": 1.4400000000000002, "directional_accuracy": 0.85}, "epochs_completed": 50, "best_epoch": 42, "advanced_metrics": {"memory_efficiency": 0.95, "training_stability": 0.9700000000000001, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.8891255982082457, "sharpe_ratio": 1.5879354938582058, "max_drawdown": 0.06274803273272571, "win_rate": 0.7836304438263286}, "model_saved": true, "model_name": "GRU_TimeSeries", "category": "timeseries", "training_duration": 11.427064, "start_time": "2025-07-17T13:19:23.011528", "end_time": "2025-07-17T13:19:34.438592", "preprocessing_results": {"memory_optimized": true, "genetic_optimization": {"learning_rate": 0.008571689467705323, "batch_size": 128, "hidden_size": 128}, "continual_learning_ready": true}, "advanced_features_applied": {"memory_optimization": true, "genetic_optimization": true, "enhanced_replay": false, "continual_learning": true}}, {"success": true, "performance": 0.87, "metrics": {"accuracy": 0.87, "f1_score": 0.85, "precision": 0.88, "recall": 0.83}, "epochs_completed": 10, "best_epoch": 8, "advanced_metrics": {"memory_efficiency": 0.95, "training_stability": 0.92, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.9451835452013577, "sharpe_ratio": 1.9631078137937648, "max_drawdown": 0.10030195490778135, "win_rate": 0.6867451577932618}, "model_saved": true, "model_name": "FinBERT", "category": "sentiment", "training_duration": 11.410503, "start_time": "2025-07-17T13:19:39.490922", "end_time": "2025-07-17T13:19:50.901425", "preprocessing_results": {"memory_optimized": true, "continual_learning_ready": true}, "advanced_features_applied": {"memory_optimization": true, "genetic_optimization": false, "enhanced_replay": false, "continual_learning": true}}, {"success": true, "performance": 0.87, "metrics": {"accuracy": 0.87, "f1_score": 0.85, "precision": 0.88, "recall": 0.83}, "epochs_completed": 10, "best_epoch": 8, "advanced_metrics": {"memory_efficiency": 0.95, "training_stability": 0.92, "convergence_speed": 0.78, "generalization_score": 0.88}, "backtest_results": {"backtest_score": 0.9414367432569547, "sharpe_ratio": 1.8750116154277698, "max_drawdown": 0.14748343573201222, "win_rate": 0.6038837992302318}, "model_saved": true, "model_name": "CryptoBERT", "category": "sentiment", "training_duration": 11.401606, "start_time": "2025-07-17T13:19:55.920768", "end_time": "2025-07-17T13:20:07.322374", "preprocessing_results": {"memory_optimized": true, "continual_learning_ready": true}, "advanced_features_applied": {"memory_optimization": true, "genetic_optimization": false, "enhanced_replay": false, "continual_learning": true}}], "failed": [{"success": false, "error": "Prerequisites not met", "details": {"trainer_available": true, "data_available": true, "dependencies_installed": true, "memory_sufficient": false, "ready_to_train": false}, "model_name": "PPO_Agent"}], "success_rate": 83.33333333333334}, "brain_analytics": {"decisions_made": 6, "performance_memory": {"PPO_Agent": [{"success": false, "performance": 0.0, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 13:18:46.751532"}], "LSTM_TimeSeries": [{"success": true, "performance": 0.982, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 13:19:03.336095"}], "DQN_Agent": [{"success": true, "performance": 0.8999999999999999, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 13:19:19.985774"}], "GRU_TimeSeries": [{"success": true, "performance": 0.982, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 13:19:36.474506"}], "FinBERT": [{"success": true, "performance": 0.87, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 13:19:52.906482"}], "CryptoBERT": [{"success": true, "performance": 0.87, "training_time": 0.0, "advanced_features_used": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}, "timestamp": "2025-07-17 13:20:09.335241"}]}, "decision_history": [{"decision": {"action": "train", "model": "RealModelInfo(name='PPO_Agent', category='reinforcement_learning', priority=1, trainer_module='training.train_rl', trainer_class='PearlRLTrainer', config_class='RLTrainingConfig', data_requirements=['trading_environment', 'price_data'], estimated_time_hours=2.5, memory_gb=2.2)", "reasoning": "انتخاب PPO_Agent با امتیاز 1.160", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": false, "timestamp": "2025-07-17T13:18:46.751532"}, {"decision": {"action": "train", "model": "RealModelInfo(name='LSTM_TimeSeries', category='timeseries', priority=1, trainer_module='training.train_timeseries', trainer_class='PearlTimeSeriesTrainer', config_class='TimeSeriesTrainingConfig', data_requirements=['price_data', 'technical_indicators'], estimated_time_hours=1.0, memory_gb=2.0)", "reasoning": "انتخاب LSTM_TimeSeries با امتیاز 1.100", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": true, "timestamp": "2025-07-17T13:19:03.336095"}, {"decision": {"action": "train", "model": "RealModelInfo(name='DQN_Agent', category='reinforcement_learning', priority=1, trainer_module='training.train_rl', trainer_class='PearlRLTrainer', config_class='RLTrainingConfig', data_requirements=['trading_environment', 'price_data'], estimated_time_hours=3.0, memory_gb=2.5)", "reasoning": "انتخاب DQN_Agent با امتیاز 1.133", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": true, "timestamp": "2025-07-17T13:19:19.985774"}, {"decision": {"action": "train", "model": "RealModelInfo(name='GRU_TimeSeries', category='timeseries', priority=1, trainer_module='training.train_timeseries', trainer_class='PearlTimeSeriesTrainer', config_class='TimeSeriesTrainingConfig', data_requirements=['price_data', 'technical_indicators'], estimated_time_hours=0.8, memory_gb=1.8)", "reasoning": "انتخاب GRU_TimeSeries با امتیاز 1.100", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": true, "timestamp": "2025-07-17T13:19:36.474506"}, {"decision": {"action": "train", "model": "RealModelInfo(name='FinBERT', category='sentiment', priority=2, trainer_module='training.train_sentiment', trainer_class='PearlSentimentTrainer', config_class='SentimentTrainingConfig', data_requirements=['financial_news', 'sentiment_labels'], estimated_time_hours=2.0, memory_gb=3.0)", "reasoning": "انتخاب FinBERT با امتیاز 1.020", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": true, "timestamp": "2025-07-17T13:19:52.906482"}, {"decision": {"action": "train", "model": "RealModelInfo(name='CryptoBERT', category='sentiment', priority=2, trainer_module='training.train_sentiment', trainer_class='PearlSentimentTrainer', config_class='SentimentTrainingConfig', data_requirements=['crypto_news', 'sentiment_labels'], estimated_time_hours=1.5, memory_gb=2.8)", "reasoning": "انتخاب CryptoBERT با امتیاز 1.020", "confidence": 0.95, "advanced_features": {"memory_optimization": true, "enhanced_replay": true, "genetic_optimization": true, "continual_learning": true, "advanced_backtesting": true}}, "outcome_success": true, "timestamp": "2025-07-17T13:20:09.335241"}]}, "advanced_features_summary": {"memory_optimization_available": true, "enhanced_replay_available": true, "genetic_evolution_available": true, "continual_learning_available": true, "backtesting_available": true}}