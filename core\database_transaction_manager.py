#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗄️ Advanced Database Transaction Management System
سیستم پیشرفته مدیریت تراکنش‌های دیتابیس با SQLAlchemy
"""

import os
import sys
import time
import logging
import asyncio
from typing import Dict, List, Optional, Any, Callable, AsyncGenerator, TypeVar, Generic
from datetime import datetime, timedelta
from contextlib import asynccontextmanager, contextmanager
from dataclasses import dataclass, field
from enum import Enum
from functools import wraps
import threading
import json
from collections import defaultdict

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from sqlalchemy import create_engine, text, pool, event
    from sqlalchemy.orm import sessionmaker, Session, declarative_base
    from sqlalchemy.exc import SQLAlchemyError, IntegrityError, OperationalError
    from sqlalchemy.engine import Engine
    from sqlalchemy.pool import QueuePool
    from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, BigInteger
    from sqlalchemy.dialects.sqlite import insert
    from sqlalchemy.sql import func
    import sqlalchemy
except ImportError as e:
    logging.error(f"SQLAlchemy not installed: {e}")
    # Fallback to basic implementation
    sqlalchemy = None

# Configure logging
logger = logging.getLogger(__name__)

class TransactionStatus(str, Enum):
    """وضعیت تراکنش"""
    PENDING = "pending"
    COMMITTED = "committed"
    ROLLED_BACK = "rolled_back"
    FAILED = "failed"
    TIMEOUT = "timeout"

class TransactionType(str, Enum):
    """نوع تراکنش"""
    READ = "read"
    WRITE = "write"
    BULK_INSERT = "bulk_insert"
    BULK_UPDATE = "bulk_update"
    BULK_DELETE = "bulk_delete"
    DDL = "ddl"
    MIGRATION = "migration"

class IsolationLevel(str, Enum):
    """سطح جداسازی تراکنش"""
    READ_UNCOMMITTED = "READ UNCOMMITTED"
    READ_COMMITTED = "READ COMMITTED"
    REPEATABLE_READ = "REPEATABLE READ"
    SERIALIZABLE = "SERIALIZABLE"

@dataclass
class TransactionMetrics:
    """متریک‌های تراکنش پیشرفته"""
    transaction_id: str
    transaction_type: TransactionType
    isolation_level: IsolationLevel
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    status: TransactionStatus = TransactionStatus.PENDING
    affected_rows: int = 0
    error_message: Optional[str] = None
    retry_count: int = 0
    connection_id: Optional[str] = None
    thread_id: Optional[int] = None
    query_count: int = 0
    rollback_reason: Optional[str] = None

# SQLAlchemy Base
Base = declarative_base()

# Database Models
class TradingSignal(Base):
    """مدل سیگنال معاملاتی"""
    __tablename__ = 'trading_signals'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    signal_type = Column(String(20), nullable=False)
    price = Column(Float, nullable=False)
    confidence = Column(Float, default=0.0)
    source = Column(String(50), nullable=False)
    signal_metadata = Column(Text)
    processed = Column(Boolean, default=False, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class TradingPosition(Base):
    """مدل پوزیشن معاملاتی"""
    __tablename__ = 'trading_positions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    position_type = Column(String(10), nullable=False)
    entry_price = Column(Float, nullable=False)
    exit_price = Column(Float)
    quantity = Column(Float, nullable=False)
    status = Column(String(20), default='open', index=True)
    profit_loss = Column(Float, default=0.0)
    entry_time = Column(DateTime, default=datetime.utcnow)
    exit_time = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class TransactionLog(Base):
    """لاگ تراکنش‌ها"""
    __tablename__ = 'transaction_logs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    transaction_id = Column(String(50), nullable=False, index=True)
    transaction_type = Column(String(20), nullable=False)
    status = Column(String(20), nullable=False)
    duration = Column(Float)
    affected_rows = Column(Integer, default=0)
    error_message = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)

class DatabaseConnectionPool:
    """مجموعه اتصالات پیشرفته"""
    
    def __init__(self, database_url: str, **kwargs):
        self.database_url = database_url
        self.engine = None
        self.session_factory = None
        self._lock = threading.Lock()
        self._is_initialized = False
        
        # Configuration
        self.pool_size = kwargs.get('pool_size', 20)
        self.max_overflow = kwargs.get('max_overflow', 30)
        self.pool_timeout = kwargs.get('pool_timeout', 30)
        self.pool_recycle = kwargs.get('pool_recycle', 3600)
        
        # Statistics
        self.connection_stats = defaultdict(int)
        
    def initialize(self) -> bool:
        """راه‌اندازی connection pool"""
        try:
            with self._lock:
                if self._is_initialized:
                    return True
                
                # Create engine with connection pooling
                self.engine = create_engine(
                    self.database_url,
                    poolclass=QueuePool,
                    pool_size=self.pool_size,
                    max_overflow=self.max_overflow,
                    pool_timeout=self.pool_timeout,
                    pool_recycle=self.pool_recycle,
                    pool_pre_ping=True,
                    echo=False,
                    future=True
                )
                
                # Event listeners for statistics
                event.listen(self.engine, 'connect', self._on_connect)
                event.listen(self.engine, 'close', self._on_disconnect)
                
                # Create session factory
                self.session_factory = sessionmaker(bind=self.engine)
                
                # Create tables
                Base.metadata.create_all(self.engine)
                
                self._is_initialized = True
                logger.info("✅ Advanced database connection pool initialized")
                return True
                
        except Exception as e:
            logger.error(f"❌ Error initializing connection pool: {e}")
            return False
    
    def _on_connect(self, dbapi_connection, connection_record):
        """رویداد اتصال"""
        self.connection_stats['total_connections'] += 1
        logger.debug("🔗 New database connection established")
        
    def _on_disconnect(self, dbapi_connection, connection_record):
        """رویداد قطع اتصال"""
        self.connection_stats['total_disconnections'] += 1
        logger.debug("🔌 Database connection closed")
    
    def get_session(self) -> Session:
        """دریافت session"""
        if not self._is_initialized:
            if not self.initialize():
                raise RuntimeError("Connection pool not initialized")
        
        return self.session_factory()
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """آمار اتصالات"""
        if not self.engine:
            return {}
        
        pool = self.engine.pool
        return {
            'pool_size': pool.size(),
            'checked_in_connections': pool.checkedin(),
            'checked_out_connections': pool.checkedout(),
            'overflow_connections': pool.overflow(),
            'total_connections': self.connection_stats['total_connections'],
            'total_disconnections': self.connection_stats['total_disconnections']
        }
    
    def close(self):
        """بستن connection pool"""
        with self._lock:
            if self.engine:
                self.engine.dispose()
                self.engine = None
            self._is_initialized = False
        logger.info("✅ Connection pool closed")

class TransactionManager:
    """مدیر پیشرفته تراکنش‌ها"""
    
    def __init__(self, connection_pool: DatabaseConnectionPool):
        self.connection_pool = connection_pool
        self.active_transactions: Dict[str, TransactionMetrics] = {}
        self.transaction_history: List[TransactionMetrics] = []
        self._lock = threading.Lock()
        self._transaction_counter = 0
        
    def generate_transaction_id(self) -> str:
        """تولید شناسه تراکنش"""
        with self._lock:
            self._transaction_counter += 1
            return f"txn_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self._transaction_counter:06d}"
    
    @contextmanager
    def transaction(self, 
                   transaction_type: TransactionType = TransactionType.WRITE,
                   isolation_level: IsolationLevel = IsolationLevel.READ_COMMITTED,
                   timeout: int = 30,
                   retry_count: int = 3):
        """Context manager برای مدیریت تراکنش"""
        transaction_id = self.generate_transaction_id()
        session = None
        
        # Create transaction metrics
        metrics = TransactionMetrics(
            transaction_id=transaction_id,
            transaction_type=transaction_type,
            isolation_level=isolation_level,
            start_time=datetime.now(),
            thread_id=threading.current_thread().ident
        )
        
        with self._lock:
            self.active_transactions[transaction_id] = metrics
        
        try:
            session = self.connection_pool.get_session()
            
            # Set isolation level
            if isolation_level != IsolationLevel.READ_COMMITTED:
                session.execute(text(f"SET TRANSACTION ISOLATION LEVEL {isolation_level.value}"))
            
            # Start transaction
            session.begin()
            
            logger.debug(f"🔄 Transaction started: {transaction_id} ({transaction_type.value})")
            
            yield session, metrics
            
            # Commit transaction
            session.commit()
            metrics.status = TransactionStatus.COMMITTED
            metrics.end_time = datetime.now()
            metrics.duration = (metrics.end_time - metrics.start_time).total_seconds()
            
            logger.debug(f"✅ Transaction committed: {transaction_id} ({metrics.duration:.3f}s)")
            
            # Log transaction
            self._log_transaction(metrics)
            
        except Exception as e:
            if session:
                session.rollback()
            
            metrics.status = TransactionStatus.ROLLED_BACK
            metrics.end_time = datetime.now()
            metrics.duration = (metrics.end_time - metrics.start_time).total_seconds()
            metrics.error_message = str(e)
            metrics.rollback_reason = type(e).__name__
            
            logger.error(f"❌ Transaction rolled back: {transaction_id} - {e}")
            
            # Log failed transaction
            self._log_transaction(metrics)
            
            # Retry logic
            if retry_count > 0 and self._should_retry(e):
                logger.info(f"🔄 Retrying transaction: {transaction_id}")
                time.sleep(min(2 ** (3 - retry_count), 5))  # Exponential backoff
                metrics.retry_count += 1
                
                with self.transaction(transaction_type, isolation_level, timeout, retry_count - 1) as (retry_session, retry_metrics):
                    yield retry_session, retry_metrics
            else:
                raise
        
        finally:
            if session:
                session.close()
            
            # Move to history
            with self._lock:
                if transaction_id in self.active_transactions:
                    del self.active_transactions[transaction_id]
                self.transaction_history.append(metrics)
                
                # Keep only last 10000 transactions
                if len(self.transaction_history) > 10000:
                    self.transaction_history = self.transaction_history[-10000:]
    
    def _should_retry(self, error: Exception) -> bool:
        """تعیین اینکه آیا تراکنش قابل تکرار است"""
        retry_errors = [
            OperationalError,
            "database is locked",
            "connection lost",
            "timeout"
        ]
        
        error_str = str(error).lower()
        for retry_error in retry_errors:
            if isinstance(retry_error, str):
                if retry_error in error_str:
                    return True
            else:
                if isinstance(error, retry_error):
                    return True
        
        return False
    
    def _log_transaction(self, metrics: TransactionMetrics):
        """ثبت تراکنش در لاگ"""
        try:
            with self.connection_pool.get_session() as session:
                log_entry = TransactionLog(
                    transaction_id=metrics.transaction_id,
                    transaction_type=metrics.transaction_type.value,
                    status=metrics.status.value,
                    duration=metrics.duration,
                    affected_rows=metrics.affected_rows,
                    error_message=metrics.error_message
                )
                session.add(log_entry)
                session.commit()
        except Exception as e:
            logger.error(f"❌ Error logging transaction: {e}")
    
    def get_transaction_statistics(self) -> Dict[str, Any]:
        """آمار تراکنش‌ها"""
        with self._lock:
            total_transactions = len(self.transaction_history)
            if total_transactions == 0:
                return {
                    "total_transactions": 0,
                    "active_transactions": len(self.active_transactions)
                }
            
            # Calculate statistics
            committed = sum(1 for t in self.transaction_history if t.status == TransactionStatus.COMMITTED)
            rolled_back = sum(1 for t in self.transaction_history if t.status == TransactionStatus.ROLLED_BACK)
            failed = sum(1 for t in self.transaction_history if t.status == TransactionStatus.FAILED)
            
            durations = [t.duration for t in self.transaction_history if t.duration is not None]
            avg_duration = sum(durations) / len(durations) if durations else 0
            max_duration = max(durations) if durations else 0
            min_duration = min(durations) if durations else 0
            
            # Transaction types distribution
            type_distribution = defaultdict(int)
            for t in self.transaction_history:
                type_distribution[t.transaction_type.value] += 1
            
            return {
                "total_transactions": total_transactions,
                "committed": committed,
                "rolled_back": rolled_back,
                "failed": failed,
                "success_rate": (committed / total_transactions) * 100,
                "average_duration": avg_duration,
                "max_duration": max_duration,
                "min_duration": min_duration,
                "active_transactions": len(self.active_transactions),
                "type_distribution": dict(type_distribution),
                "recent_errors": [
                    {
                        "transaction_id": t.transaction_id,
                        "error": t.error_message,
                        "timestamp": t.start_time.isoformat()
                    }
                    for t in self.transaction_history[-10:]
                    if t.error_message
                ]
            }

class DatabaseTransactionManager:
    """مدیر کلی سیستم دیتابیس"""
    
    def __init__(self, database_url: str = "sqlite:///trading_system.db", **kwargs):
        self.database_url = database_url
        self.connection_pool = DatabaseConnectionPool(database_url, **kwargs)
        self.transaction_manager = TransactionManager(self.connection_pool)
        self._is_initialized = False
        
    def initialize(self) -> bool:
        """راه‌اندازی سیستم دیتابیس"""
        try:
            if not self.connection_pool.initialize():
                return False
            
            # Test connection
            with self.connection_pool.get_session() as session:
                session.execute(text("SELECT 1"))
                session.commit()
            
            self._is_initialized = True
            logger.info("✅ Database transaction manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error initializing database transaction manager: {e}")
            return False
    
    def get_session(self) -> Session:
        """دریافت session"""
        if not self._is_initialized:
            if not self.initialize():
                raise RuntimeError("Database not initialized")
        
        return self.connection_pool.get_session()
    
    def transaction(self, **kwargs):
        """Context manager برای تراکنش"""
        return self.transaction_manager.transaction(**kwargs)
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """اجرای کوئری"""
        with self.transaction(transaction_type=TransactionType.READ) as (session, metrics):
            result = session.execute(text(query), params or {})
            metrics.query_count += 1
            
            if result.returns_rows:
                # Convert SQLAlchemy Row objects to dictionaries
                rows = result.fetchall()
                return [dict(row._mapping) for row in rows]
            else:
                metrics.affected_rows = result.rowcount
                return []
    
    def bulk_insert(self, model_class, data: List[Dict[str, Any]]) -> int:
        """درج انبوه"""
        with self.transaction(transaction_type=TransactionType.BULK_INSERT) as (session, metrics):
            if sqlalchemy and hasattr(session, 'bulk_insert_mappings'):
                session.bulk_insert_mappings(model_class, data)
            else:
                # Fallback to individual inserts
                for item in data:
                    session.add(model_class(**item))
            
            session.flush()
            metrics.affected_rows = len(data)
            return len(data)
    
    def get_system_health(self) -> Dict[str, Any]:
        """بررسی سلامت سیستم"""
        return {
            "database_initialized": self._is_initialized,
            "connection_pool_stats": self.connection_pool.get_connection_stats(),
            "transaction_stats": self.transaction_manager.get_transaction_statistics(),
            "timestamp": datetime.now().isoformat()
        }
    
    def close(self):
        """بستن سیستم دیتابیس"""
        self.connection_pool.close()
        self._is_initialized = False
        logger.info("✅ Database transaction manager closed")

# Global instance
_db_manager: Optional[DatabaseTransactionManager] = None
_db_lock = threading.Lock()

def get_database_manager(database_url: str = "sqlite:///trading_system.db", **kwargs) -> DatabaseTransactionManager:
    """دریافت instance سراسری database manager"""
    global _db_manager
    
    with _db_lock:
        if _db_manager is None:
            _db_manager = DatabaseTransactionManager(database_url, **kwargs)
            _db_manager.initialize()
        return _db_manager

def close_database_manager():
    """بستن database manager سراسری"""
    global _db_manager
    
    with _db_lock:
        if _db_manager:
            _db_manager.close()
            _db_manager = None

# Convenience functions
def transaction(**kwargs):
    """تراکنش ساده"""
    return get_database_manager().transaction(**kwargs)

def execute_query(query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """اجرای کوئری ساده"""
    return get_database_manager().execute_query(query, params)

def bulk_insert(model_class, data: List[Dict[str, Any]]) -> int:
    """درج انبوه ساده"""
    return get_database_manager().bulk_insert(model_class, data)

if __name__ == "__main__":
    # Test the system
    print("🧪 Testing Database Transaction Manager...")
    
    db_manager = get_database_manager()
    
    # Test transaction
    with transaction() as (session, metrics):
        # Insert test data
        signal = TradingSignal(
            symbol="EURUSD",
            signal_type="BUY",
            price=1.0950,
            confidence=0.85,
            source="test"
        )
        session.add(signal)
        session.flush()
        
        print(f"✅ Test transaction successful: {metrics.transaction_id}")
    
    # Test statistics
    stats = db_manager.get_system_health()
    print(f"📊 System Health: {json.dumps(stats, indent=2, default=str)}")
    
    close_database_manager()
    print("🎉 Database Transaction Manager test completed!") 